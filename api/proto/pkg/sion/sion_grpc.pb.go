// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: sion/sion.proto

package sion

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Sion_FileDetect_FullMethodName = "/sion.Sion/FileDetect"
)

// SionClient is the client API for Sion service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ClaimAV文件检测服务
type SionClient interface {
	// 文件检测
	FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error)
}

type sionClient struct {
	cc grpc.ClientConnInterface
}

func NewSionClient(cc grpc.ClientConnInterface) SionClient {
	return &sionClient{cc}
}

func (c *sionClient) FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Sion_ServiceDesc.Streams[0], Sion_FileDetect_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[FileDetectReq, FileDetectResp]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Sion_FileDetectClient = grpc.BidiStreamingClient[FileDetectReq, FileDetectResp]

// SionServer is the server API for Sion service.
// All implementations must embed UnimplementedSionServer
// for forward compatibility.
//
// ClaimAV文件检测服务
type SionServer interface {
	// 文件检测
	FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error
	mustEmbedUnimplementedSionServer()
}

// UnimplementedSionServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSionServer struct{}

func (UnimplementedSionServer) FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error {
	return status.Errorf(codes.Unimplemented, "method FileDetect not implemented")
}
func (UnimplementedSionServer) mustEmbedUnimplementedSionServer() {}
func (UnimplementedSionServer) testEmbeddedByValue()              {}

// UnsafeSionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SionServer will
// result in compilation errors.
type UnsafeSionServer interface {
	mustEmbedUnimplementedSionServer()
}

func RegisterSionServer(s grpc.ServiceRegistrar, srv SionServer) {
	// If the following call pancis, it indicates UnimplementedSionServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Sion_ServiceDesc, srv)
}

func _Sion_FileDetect_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(SionServer).FileDetect(&grpc.GenericServerStream[FileDetectReq, FileDetectResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Sion_FileDetectServer = grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]

// Sion_ServiceDesc is the grpc.ServiceDesc for Sion service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Sion_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sion.Sion",
	HandlerType: (*SionServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "FileDetect",
			Handler:       _Sion_FileDetect_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "sion/sion.proto",
}
