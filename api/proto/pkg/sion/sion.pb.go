// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: sion/sion.proto

package sion

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FileDetectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`                    // 仅首个包携带
	Sha256    string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                        // 文件sha256
	ChunkData []byte `protobuf:"bytes,3,opt,name=chunk_data,json=chunkData,proto3" json:"chunk_data,omitempty"` // 数据块
	Eof       bool   `protobuf:"varint,4,opt,name=eof,proto3" json:"eof,omitempty"`                             // 是否结束标志
}

func (x *FileDetectReq) Reset() {
	*x = FileDetectReq{}
	mi := &file_sion_sion_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectReq) ProtoMessage() {}

func (x *FileDetectReq) ProtoReflect() protoreflect.Message {
	mi := &file_sion_sion_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectReq.ProtoReflect.Descriptor instead.
func (*FileDetectReq) Descriptor() ([]byte, []int) {
	return file_sion_sion_proto_rawDescGZIP(), []int{0}
}

func (x *FileDetectReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FileDetectReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileDetectReq) GetChunkData() []byte {
	if x != nil {
		return x.ChunkData
	}
	return nil
}

func (x *FileDetectReq) GetEof() bool {
	if x != nil {
		return x.Eof
	}
	return false
}

type FileDetectResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`                    // 文件名
	VirusName string `protobuf:"bytes,2,opt,name=virus_name,json=virusName,proto3" json:"virus_name,omitempty"` // 病毒名称
	Version   string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                      // 引擎版本
}

func (x *FileDetectResp) Reset() {
	*x = FileDetectResp{}
	mi := &file_sion_sion_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectResp) ProtoMessage() {}

func (x *FileDetectResp) ProtoReflect() protoreflect.Message {
	mi := &file_sion_sion_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectResp.ProtoReflect.Descriptor instead.
func (*FileDetectResp) Descriptor() ([]byte, []int) {
	return file_sion_sion_proto_rawDescGZIP(), []int{1}
}

func (x *FileDetectResp) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FileDetectResp) GetVirusName() string {
	if x != nil {
		return x.VirusName
	}
	return ""
}

func (x *FileDetectResp) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

var File_sion_sion_proto protoreflect.FileDescriptor

var file_sion_sion_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x73, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x74, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6f, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6f, 0x66, 0x22, 0x65, 0x0a,
	0x0e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76,
	0x69, 0x72, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x32, 0x45, 0x0a, 0x04, 0x53, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0a,
	0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x12, 0x13, 0x2e, 0x73, 0x69, 0x6f,
	0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a,
	0x14, 0x2e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01, 0x42, 0x29, 0x5a, 0x27, 0x67,
	0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31,
	0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x73, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_sion_sion_proto_rawDescOnce sync.Once
	file_sion_sion_proto_rawDescData = file_sion_sion_proto_rawDesc
)

func file_sion_sion_proto_rawDescGZIP() []byte {
	file_sion_sion_proto_rawDescOnce.Do(func() {
		file_sion_sion_proto_rawDescData = protoimpl.X.CompressGZIP(file_sion_sion_proto_rawDescData)
	})
	return file_sion_sion_proto_rawDescData
}

var file_sion_sion_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_sion_sion_proto_goTypes = []any{
	(*FileDetectReq)(nil),  // 0: sion.FileDetectReq
	(*FileDetectResp)(nil), // 1: sion.FileDetectResp
}
var file_sion_sion_proto_depIdxs = []int32{
	0, // 0: sion.Sion.FileDetect:input_type -> sion.FileDetectReq
	1, // 1: sion.Sion.FileDetect:output_type -> sion.FileDetectResp
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_sion_sion_proto_init() }
func file_sion_sion_proto_init() {
	if File_sion_sion_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sion_sion_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sion_sion_proto_goTypes,
		DependencyIndexes: file_sion_sion_proto_depIdxs,
		MessageInfos:      file_sion_sion_proto_msgTypes,
	}.Build()
	File_sion_sion_proto = out.File
	file_sion_sion_proto_rawDesc = nil
	file_sion_sion_proto_goTypes = nil
	file_sion_sion_proto_depIdxs = nil
}
