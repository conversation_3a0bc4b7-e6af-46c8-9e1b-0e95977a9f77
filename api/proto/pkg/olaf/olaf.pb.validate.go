// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: olaf/olaf.proto

package olaf

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FileDetectReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileDetectReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileDetectReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileDetectReqMultiError, or
// nil if none found.
func (m *FileDetectReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileDetectReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filename

	// no validation rules for Sha256

	// no validation rules for ChunkData

	// no validation rules for Eof

	// no validation rules for Type

	if len(errors) > 0 {
		return FileDetectReqMultiError(errors)
	}

	return nil
}

// FileDetectReqMultiError is an error wrapping multiple validation errors
// returned by FileDetectReq.ValidateAll() if the designated constraints
// aren't met.
type FileDetectReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileDetectReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileDetectReqMultiError) AllErrors() []error { return m }

// FileDetectReqValidationError is the validation error returned by
// FileDetectReq.Validate if the designated constraints aren't met.
type FileDetectReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileDetectReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileDetectReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileDetectReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileDetectReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileDetectReqValidationError) ErrorName() string { return "FileDetectReqValidationError" }

// Error satisfies the builtin error interface
func (e FileDetectReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileDetectReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileDetectReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileDetectReqValidationError{}

// Validate checks the field values on FileDetectResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileDetectResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileDetectResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileDetectRespMultiError,
// or nil if none found.
func (m *FileDetectResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileDetectResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	if len(errors) > 0 {
		return FileDetectRespMultiError(errors)
	}

	return nil
}

// FileDetectRespMultiError is an error wrapping multiple validation errors
// returned by FileDetectResp.ValidateAll() if the designated constraints
// aren't met.
type FileDetectRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileDetectRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileDetectRespMultiError) AllErrors() []error { return m }

// FileDetectRespValidationError is the validation error returned by
// FileDetectResp.Validate if the designated constraints aren't met.
type FileDetectRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileDetectRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileDetectRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileDetectRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileDetectRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileDetectRespValidationError) ErrorName() string { return "FileDetectRespValidationError" }

// Error satisfies the builtin error interface
func (e FileDetectRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileDetectResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileDetectRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileDetectRespValidationError{}
