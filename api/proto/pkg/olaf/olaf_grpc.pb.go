// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: olaf/olaf.proto

package olaf

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	<PERSON>_FileDetect_FullMethodName = "/olaf.Olaf/FileDetect"
)

// OlafClient is the client API for Olaf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// <PERSON>（ai检测服务）
type OlafClient interface {
	// 文件检测
	FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error)
}

type olafClient struct {
	cc grpc.ClientConnInterface
}

func NewOlafClient(cc grpc.ClientConnInterface) OlafClient {
	return &olafClient{cc}
}

func (c *olafClient) FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Olaf_ServiceDesc.Streams[0], Olaf_FileDetect_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[FileDetectReq, FileDetectResp]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Olaf_FileDetectClient = grpc.BidiStreamingClient[FileDetectReq, FileDetectResp]

// OlafServer is the server API for Olaf service.
// All implementations must embed UnimplementedOlafServer
// for forward compatibility.
//
// Olaf（ai检测服务）
type OlafServer interface {
	// 文件检测
	FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error
	mustEmbedUnimplementedOlafServer()
}

// UnimplementedOlafServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedOlafServer struct{}

func (UnimplementedOlafServer) FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error {
	return status.Errorf(codes.Unimplemented, "method FileDetect not implemented")
}
func (UnimplementedOlafServer) mustEmbedUnimplementedOlafServer() {}
func (UnimplementedOlafServer) testEmbeddedByValue()              {}

// UnsafeOlafServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OlafServer will
// result in compilation errors.
type UnsafeOlafServer interface {
	mustEmbedUnimplementedOlafServer()
}

func RegisterOlafServer(s grpc.ServiceRegistrar, srv OlafServer) {
	// If the following call pancis, it indicates UnimplementedOlafServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Olaf_ServiceDesc, srv)
}

func _Olaf_FileDetect_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(OlafServer).FileDetect(&grpc.GenericServerStream[FileDetectReq, FileDetectResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Olaf_FileDetectServer = grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]

// Olaf_ServiceDesc is the grpc.ServiceDesc for Olaf service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Olaf_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "olaf.Olaf",
	HandlerType: (*OlafServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "FileDetect",
			Handler:       _Olaf_FileDetect_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "olaf/olaf.proto",
}
