// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: olaf/olaf.proto

package olaf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DetectType int32

const (
	DetectType_UNKNOWN DetectType = 0
	DetectType_AI      DetectType = 1 // ai检测
	DetectType_AIPE    DetectType = 2 // aipe检测
)

// Enum value maps for DetectType.
var (
	DetectType_name = map[int32]string{
		0: "UNKNOWN",
		1: "AI",
		2: "AIPE",
	}
	DetectType_value = map[string]int32{
		"UNKNOWN": 0,
		"AI":      1,
		"AIPE":    2,
	}
)

func (x DetectType) Enum() *DetectType {
	p := new(DetectType)
	*p = x
	return p
}

func (x DetectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectType) Descriptor() protoreflect.EnumDescriptor {
	return file_olaf_olaf_proto_enumTypes[0].Descriptor()
}

func (DetectType) Type() protoreflect.EnumType {
	return &file_olaf_olaf_proto_enumTypes[0]
}

func (x DetectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectType.Descriptor instead.
func (DetectType) EnumDescriptor() ([]byte, []int) {
	return file_olaf_olaf_proto_rawDescGZIP(), []int{0}
}

type FileDetectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string     `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`                    // 仅首个包携带
	Sha256    string     `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                        // 文件sha256
	ChunkData []byte     `protobuf:"bytes,3,opt,name=chunk_data,json=chunkData,proto3" json:"chunk_data,omitempty"` // 数据块
	Eof       bool       `protobuf:"varint,4,opt,name=eof,proto3" json:"eof,omitempty"`                             // 是否结束标志
	Type      DetectType `protobuf:"varint,5,opt,name=type,proto3,enum=olaf.DetectType" json:"type,omitempty"`      // 检测类型
}

func (x *FileDetectReq) Reset() {
	*x = FileDetectReq{}
	mi := &file_olaf_olaf_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectReq) ProtoMessage() {}

func (x *FileDetectReq) ProtoReflect() protoreflect.Message {
	mi := &file_olaf_olaf_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectReq.ProtoReflect.Descriptor instead.
func (*FileDetectReq) Descriptor() ([]byte, []int) {
	return file_olaf_olaf_proto_rawDescGZIP(), []int{0}
}

func (x *FileDetectReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FileDetectReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileDetectReq) GetChunkData() []byte {
	if x != nil {
		return x.ChunkData
	}
	return nil
}

func (x *FileDetectReq) GetEof() bool {
	if x != nil {
		return x.Eof
	}
	return false
}

func (x *FileDetectReq) GetType() DetectType {
	if x != nil {
		return x.Type
	}
	return DetectType_UNKNOWN
}

type FileDetectResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score int32 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"` // 检测分值
}

func (x *FileDetectResp) Reset() {
	*x = FileDetectResp{}
	mi := &file_olaf_olaf_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectResp) ProtoMessage() {}

func (x *FileDetectResp) ProtoReflect() protoreflect.Message {
	mi := &file_olaf_olaf_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectResp.ProtoReflect.Descriptor instead.
func (*FileDetectResp) Descriptor() ([]byte, []int) {
	return file_olaf_olaf_proto_rawDescGZIP(), []int{1}
}

func (x *FileDetectResp) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

var File_olaf_olaf_proto protoreflect.FileDescriptor

var file_olaf_olaf_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x6f, 0x6c, 0x61, 0x66, 0x2f, 0x6f, 0x6c, 0x61, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x6f, 0x6c, 0x61, 0x66, 0x22, 0x9a, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x6f, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6f, 0x66, 0x12, 0x24,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6f,
	0x6c, 0x61, 0x66, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x26, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x2a, 0x2b, 0x0a, 0x0a,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x41, 0x49, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x41, 0x49, 0x50, 0x45, 0x10, 0x02, 0x32, 0x45, 0x0a, 0x04, 0x4f, 0x6c, 0x61,
	0x66, 0x12, 0x3d, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x12,
	0x13, 0x2e, 0x6f, 0x6c, 0x61, 0x66, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6f, 0x6c, 0x61, 0x66, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x28, 0x01, 0x30, 0x01,
	0x42, 0x29, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6f, 0x6c, 0x61, 0x66, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_olaf_olaf_proto_rawDescOnce sync.Once
	file_olaf_olaf_proto_rawDescData = file_olaf_olaf_proto_rawDesc
)

func file_olaf_olaf_proto_rawDescGZIP() []byte {
	file_olaf_olaf_proto_rawDescOnce.Do(func() {
		file_olaf_olaf_proto_rawDescData = protoimpl.X.CompressGZIP(file_olaf_olaf_proto_rawDescData)
	})
	return file_olaf_olaf_proto_rawDescData
}

var file_olaf_olaf_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_olaf_olaf_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_olaf_olaf_proto_goTypes = []any{
	(DetectType)(0),        // 0: olaf.DetectType
	(*FileDetectReq)(nil),  // 1: olaf.FileDetectReq
	(*FileDetectResp)(nil), // 2: olaf.FileDetectResp
}
var file_olaf_olaf_proto_depIdxs = []int32{
	0, // 0: olaf.FileDetectReq.type:type_name -> olaf.DetectType
	1, // 1: olaf.Olaf.FileDetect:input_type -> olaf.FileDetectReq
	2, // 2: olaf.Olaf.FileDetect:output_type -> olaf.FileDetectResp
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_olaf_olaf_proto_init() }
func file_olaf_olaf_proto_init() {
	if File_olaf_olaf_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_olaf_olaf_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_olaf_olaf_proto_goTypes,
		DependencyIndexes: file_olaf_olaf_proto_depIdxs,
		EnumInfos:         file_olaf_olaf_proto_enumTypes,
		MessageInfos:      file_olaf_olaf_proto_msgTypes,
	}.Build()
	File_olaf_olaf_proto = out.File
	file_olaf_olaf_proto_rawDesc = nil
	file_olaf_olaf_proto_goTypes = nil
	file_olaf_olaf_proto_depIdxs = nil
}
