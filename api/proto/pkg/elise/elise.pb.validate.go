// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: elise/elise.proto

package elise

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetFileReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFileReqMultiError, or
// nil if none found.
func (m *GetFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	if len(errors) > 0 {
		return GetFileReqMultiError(errors)
	}

	return nil
}

// GetFileReqMultiError is an error wrapping multiple validation errors
// returned by GetFileReq.ValidateAll() if the designated constraints aren't met.
type GetFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileReqMultiError) AllErrors() []error { return m }

// GetFileReqValidationError is the validation error returned by
// GetFileReq.Validate if the designated constraints aren't met.
type GetFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileReqValidationError) ErrorName() string { return "GetFileReqValidationError" }

// Error satisfies the builtin error interface
func (e GetFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileReqValidationError{}

// Validate checks the field values on GetFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetFileRespMultiError, or
// nil if none found.
func (m *GetFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	// no validation rules for Path

	// no validation rules for StoragePath

	// no validation rules for StorageUrl

	if len(errors) > 0 {
		return GetFileRespMultiError(errors)
	}

	return nil
}

// GetFileRespMultiError is an error wrapping multiple validation errors
// returned by GetFileResp.ValidateAll() if the designated constraints aren't met.
type GetFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileRespMultiError) AllErrors() []error { return m }

// GetFileRespValidationError is the validation error returned by
// GetFileResp.Validate if the designated constraints aren't met.
type GetFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileRespValidationError) ErrorName() string { return "GetFileRespValidationError" }

// Error satisfies the builtin error interface
func (e GetFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileRespValidationError{}

// Validate checks the field values on UploadFileReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadFileReqMultiError, or
// nil if none found.
func (m *UploadFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Name

	// no validation rules for Size

	// no validation rules for Path

	// no validation rules for Data

	// no validation rules for Offset

	if len(errors) > 0 {
		return UploadFileReqMultiError(errors)
	}

	return nil
}

// UploadFileReqMultiError is an error wrapping multiple validation errors
// returned by UploadFileReq.ValidateAll() if the designated constraints
// aren't met.
type UploadFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileReqMultiError) AllErrors() []error { return m }

// UploadFileReqValidationError is the validation error returned by
// UploadFileReq.Validate if the designated constraints aren't met.
type UploadFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileReqValidationError) ErrorName() string { return "UploadFileReqValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileReqValidationError{}

// Validate checks the field values on UploadFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadFileRespMultiError,
// or nil if none found.
func (m *UploadFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UploadFileRespMultiError(errors)
	}

	return nil
}

// UploadFileRespMultiError is an error wrapping multiple validation errors
// returned by UploadFileResp.ValidateAll() if the designated constraints
// aren't met.
type UploadFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileRespMultiError) AllErrors() []error { return m }

// UploadFileRespValidationError is the validation error returned by
// UploadFileResp.Validate if the designated constraints aren't met.
type UploadFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileRespValidationError) ErrorName() string { return "UploadFileRespValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileRespValidationError{}

// Validate checks the field values on DownloadFileReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DownloadFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadFileReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadFileReqMultiError, or nil if none found.
func (m *DownloadFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Size

	// no validation rules for Path

	// no validation rules for Offset

	if len(errors) > 0 {
		return DownloadFileReqMultiError(errors)
	}

	return nil
}

// DownloadFileReqMultiError is an error wrapping multiple validation errors
// returned by DownloadFileReq.ValidateAll() if the designated constraints
// aren't met.
type DownloadFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadFileReqMultiError) AllErrors() []error { return m }

// DownloadFileReqValidationError is the validation error returned by
// DownloadFileReq.Validate if the designated constraints aren't met.
type DownloadFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadFileReqValidationError) ErrorName() string { return "DownloadFileReqValidationError" }

// Error satisfies the builtin error interface
func (e DownloadFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadFileReqValidationError{}

// Validate checks the field values on DownloadFileResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DownloadFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadFileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadFileRespMultiError, or nil if none found.
func (m *DownloadFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return DownloadFileRespMultiError(errors)
	}

	return nil
}

// DownloadFileRespMultiError is an error wrapping multiple validation errors
// returned by DownloadFileResp.ValidateAll() if the designated constraints
// aren't met.
type DownloadFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadFileRespMultiError) AllErrors() []error { return m }

// DownloadFileRespValidationError is the validation error returned by
// DownloadFileResp.Validate if the designated constraints aren't met.
type DownloadFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadFileRespValidationError) ErrorName() string { return "DownloadFileRespValidationError" }

// Error satisfies the builtin error interface
func (e DownloadFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadFileRespValidationError{}

// Validate checks the field values on DeleteFileReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFileReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteFileReqMultiError, or
// nil if none found.
func (m *DeleteFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	if len(errors) > 0 {
		return DeleteFileReqMultiError(errors)
	}

	return nil
}

// DeleteFileReqMultiError is an error wrapping multiple validation errors
// returned by DeleteFileReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFileReqMultiError) AllErrors() []error { return m }

// DeleteFileReqValidationError is the validation error returned by
// DeleteFileReq.Validate if the designated constraints aren't met.
type DeleteFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFileReqValidationError) ErrorName() string { return "DeleteFileReqValidationError" }

// Error satisfies the builtin error interface
func (e DeleteFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFileReqValidationError{}

// Validate checks the field values on DeleteFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeleteFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFileResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeleteFileRespMultiError,
// or nil if none found.
func (m *DeleteFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteFileRespMultiError(errors)
	}

	return nil
}

// DeleteFileRespMultiError is an error wrapping multiple validation errors
// returned by DeleteFileResp.ValidateAll() if the designated constraints
// aren't met.
type DeleteFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFileRespMultiError) AllErrors() []error { return m }

// DeleteFileRespValidationError is the validation error returned by
// DeleteFileResp.Validate if the designated constraints aren't met.
type DeleteFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFileRespValidationError) ErrorName() string { return "DeleteFileRespValidationError" }

// Error satisfies the builtin error interface
func (e DeleteFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFileRespValidationError{}
