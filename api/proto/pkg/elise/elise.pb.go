// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: elise/elise.proto

package elise

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256 string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"` // 文件md5
}

func (x *GetFileReq) Reset() {
	*x = GetFileReq{}
	mi := &file_elise_elise_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileReq) ProtoMessage() {}

func (x *GetFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileReq.ProtoReflect.Descriptor instead.
func (*GetFileReq) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{0}
}

func (x *GetFileReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

type GetFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256      string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`                              // 文件md5
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                  // 文件名
	Size        int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`                                 // 文件大小
	CreateTime  int64  `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`   // 文件创建时间
	UpdateTime  int64  `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`   // 文件更新时间
	Path        string `protobuf:"bytes,6,opt,name=path,proto3" json:"path,omitempty"`                                  // 文件路径
	StoragePath string `protobuf:"bytes,7,opt,name=storage_path,json=storagePath,proto3" json:"storage_path,omitempty"` // 文件存储路径
	StorageUrl  string `protobuf:"bytes,8,opt,name=storage_url,json=storageUrl,proto3" json:"storage_url,omitempty"`    // 文件存储URL
}

func (x *GetFileResp) Reset() {
	*x = GetFileResp{}
	mi := &file_elise_elise_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileResp) ProtoMessage() {}

func (x *GetFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileResp.ProtoReflect.Descriptor instead.
func (*GetFileResp) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{1}
}

func (x *GetFileResp) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *GetFileResp) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetFileResp) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetFileResp) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *GetFileResp) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *GetFileResp) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *GetFileResp) GetStoragePath() string {
	if x != nil {
		return x.StoragePath
	}
	return ""
}

func (x *GetFileResp) GetStorageUrl() string {
	if x != nil {
		return x.StorageUrl
	}
	return ""
}

type UploadFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256 string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`  // 文件sha256
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`      // 文件名
	Size   int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`     // 文件大小
	Path   string `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`      // 文件路径
	Data   []byte `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`      // 文件数据
	Offset int64  `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"` // 文件偏移
}

func (x *UploadFileReq) Reset() {
	*x = UploadFileReq{}
	mi := &file_elise_elise_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileReq) ProtoMessage() {}

func (x *UploadFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileReq.ProtoReflect.Descriptor instead.
func (*UploadFileReq) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{2}
}

func (x *UploadFileReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *UploadFileReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UploadFileReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UploadFileReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *UploadFileReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *UploadFileReq) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type UploadFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UploadFileResp) Reset() {
	*x = UploadFileResp{}
	mi := &file_elise_elise_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileResp) ProtoMessage() {}

func (x *UploadFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileResp.ProtoReflect.Descriptor instead.
func (*UploadFileResp) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{3}
}

type DownloadFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256 string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`  // 文件sha256
	Size   int64  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`     // 文件大小
	Path   string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`      // 文件路径
	Offset int64  `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"` // 文件偏移
}

func (x *DownloadFileReq) Reset() {
	*x = DownloadFileReq{}
	mi := &file_elise_elise_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileReq) ProtoMessage() {}

func (x *DownloadFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileReq.ProtoReflect.Descriptor instead.
func (*DownloadFileReq) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{4}
}

func (x *DownloadFileReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *DownloadFileReq) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *DownloadFileReq) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DownloadFileReq) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

type DownloadFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件数据
}

func (x *DownloadFileResp) Reset() {
	*x = DownloadFileResp{}
	mi := &file_elise_elise_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DownloadFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadFileResp) ProtoMessage() {}

func (x *DownloadFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadFileResp.ProtoReflect.Descriptor instead.
func (*DownloadFileResp) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{5}
}

func (x *DownloadFileResp) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256 string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"` // 文件sha256
}

func (x *DeleteFileReq) Reset() {
	*x = DeleteFileReq{}
	mi := &file_elise_elise_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileReq) ProtoMessage() {}

func (x *DeleteFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileReq.ProtoReflect.Descriptor instead.
func (*DeleteFileReq) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteFileReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

type DeleteFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteFileResp) Reset() {
	*x = DeleteFileResp{}
	mi := &file_elise_elise_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFileResp) ProtoMessage() {}

func (x *DeleteFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_elise_elise_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFileResp.ProtoReflect.Descriptor instead.
func (*DeleteFileResp) Descriptor() ([]byte, []int) {
	return file_elise_elise_proto_rawDescGZIP(), []int{7}
}

var File_elise_elise_proto protoreflect.FileDescriptor

var file_elise_elise_proto_rawDesc = []byte{
	0x0a, 0x11, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2f, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x22, 0x24, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x22, 0xe7, 0x01, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x22, 0x8f, 0x01, 0x0a, 0x0d, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x10, 0x0a, 0x0e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x69,
	0x0a, 0x0f, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x22, 0x26, 0x0a, 0x10, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x27, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22, 0x10, 0x0a, 0x0e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x32, 0xf2, 0x01, 0x0a,
	0x05, 0x45, 0x6c, 0x69, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x11, 0x2e, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x2e, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x65,
	0x6c, 0x69, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x41, 0x0a, 0x0c, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x16, 0x2e, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x65, 0x6c,
	0x69, 0x73, 0x65, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x30, 0x01, 0x12, 0x39, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0x14, 0x2e, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x65, 0x6c, 0x69,
	0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x65, 0x6c, 0x69, 0x73, 0x65, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_elise_elise_proto_rawDescOnce sync.Once
	file_elise_elise_proto_rawDescData = file_elise_elise_proto_rawDesc
)

func file_elise_elise_proto_rawDescGZIP() []byte {
	file_elise_elise_proto_rawDescOnce.Do(func() {
		file_elise_elise_proto_rawDescData = protoimpl.X.CompressGZIP(file_elise_elise_proto_rawDescData)
	})
	return file_elise_elise_proto_rawDescData
}

var file_elise_elise_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_elise_elise_proto_goTypes = []any{
	(*GetFileReq)(nil),       // 0: elise.GetFileReq
	(*GetFileResp)(nil),      // 1: elise.GetFileResp
	(*UploadFileReq)(nil),    // 2: elise.UploadFileReq
	(*UploadFileResp)(nil),   // 3: elise.UploadFileResp
	(*DownloadFileReq)(nil),  // 4: elise.DownloadFileReq
	(*DownloadFileResp)(nil), // 5: elise.DownloadFileResp
	(*DeleteFileReq)(nil),    // 6: elise.DeleteFileReq
	(*DeleteFileResp)(nil),   // 7: elise.DeleteFileResp
}
var file_elise_elise_proto_depIdxs = []int32{
	0, // 0: elise.Elise.GetFile:input_type -> elise.GetFileReq
	2, // 1: elise.Elise.UploadFile:input_type -> elise.UploadFileReq
	4, // 2: elise.Elise.DownloadFile:input_type -> elise.DownloadFileReq
	6, // 3: elise.Elise.DeleteFile:input_type -> elise.DeleteFileReq
	1, // 4: elise.Elise.GetFile:output_type -> elise.GetFileResp
	3, // 5: elise.Elise.UploadFile:output_type -> elise.UploadFileResp
	5, // 6: elise.Elise.DownloadFile:output_type -> elise.DownloadFileResp
	7, // 7: elise.Elise.DeleteFile:output_type -> elise.DeleteFileResp
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_elise_elise_proto_init() }
func file_elise_elise_proto_init() {
	if File_elise_elise_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_elise_elise_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_elise_elise_proto_goTypes,
		DependencyIndexes: file_elise_elise_proto_depIdxs,
		MessageInfos:      file_elise_elise_proto_msgTypes,
	}.Build()
	File_elise_elise_proto = out.File
	file_elise_elise_proto_rawDesc = nil
	file_elise_elise_proto_goTypes = nil
	file_elise_elise_proto_depIdxs = nil
}
