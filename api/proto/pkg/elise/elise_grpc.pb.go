// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: elise/elise.proto

package elise

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Elise_GetFile_FullMethodName      = "/elise.Elise/GetFile"
	Elise_UploadFile_FullMethodName   = "/elise.Elise/UploadFile"
	Elise_DownloadFile_FullMethodName = "/elise.Elise/DownloadFile"
	Elise_DeleteFile_FullMethodName   = "/elise.<PERSON>/DeleteFile"
)

// EliseClient is the client API for Elise service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 文件上传服务
type EliseClient interface {
	// 获取文件元数据信息
	GetFile(ctx context.Context, in *GetFileReq, opts ...grpc.CallOption) (*GetFileResp, error)
	// 上传文件
	UploadFile(ctx context.Context, in *UploadFileReq, opts ...grpc.CallOption) (*UploadFileResp, error)
	// 下载文件
	DownloadFile(ctx context.Context, in *DownloadFileReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DownloadFileResp], error)
	// 删除文件
	DeleteFile(ctx context.Context, in *DeleteFileReq, opts ...grpc.CallOption) (*DeleteFileResp, error)
}

type eliseClient struct {
	cc grpc.ClientConnInterface
}

func NewEliseClient(cc grpc.ClientConnInterface) EliseClient {
	return &eliseClient{cc}
}

func (c *eliseClient) GetFile(ctx context.Context, in *GetFileReq, opts ...grpc.CallOption) (*GetFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileResp)
	err := c.cc.Invoke(ctx, Elise_GetFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eliseClient) UploadFile(ctx context.Context, in *UploadFileReq, opts ...grpc.CallOption) (*UploadFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadFileResp)
	err := c.cc.Invoke(ctx, Elise_UploadFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eliseClient) DownloadFile(ctx context.Context, in *DownloadFileReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[DownloadFileResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Elise_ServiceDesc.Streams[0], Elise_DownloadFile_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[DownloadFileReq, DownloadFileResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Elise_DownloadFileClient = grpc.ServerStreamingClient[DownloadFileResp]

func (c *eliseClient) DeleteFile(ctx context.Context, in *DeleteFileReq, opts ...grpc.CallOption) (*DeleteFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteFileResp)
	err := c.cc.Invoke(ctx, Elise_DeleteFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EliseServer is the server API for Elise service.
// All implementations must embed UnimplementedEliseServer
// for forward compatibility.
//
// 文件上传服务
type EliseServer interface {
	// 获取文件元数据信息
	GetFile(context.Context, *GetFileReq) (*GetFileResp, error)
	// 上传文件
	UploadFile(context.Context, *UploadFileReq) (*UploadFileResp, error)
	// 下载文件
	DownloadFile(*DownloadFileReq, grpc.ServerStreamingServer[DownloadFileResp]) error
	// 删除文件
	DeleteFile(context.Context, *DeleteFileReq) (*DeleteFileResp, error)
	mustEmbedUnimplementedEliseServer()
}

// UnimplementedEliseServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEliseServer struct{}

func (UnimplementedEliseServer) GetFile(context.Context, *GetFileReq) (*GetFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFile not implemented")
}
func (UnimplementedEliseServer) UploadFile(context.Context, *UploadFileReq) (*UploadFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedEliseServer) DownloadFile(*DownloadFileReq, grpc.ServerStreamingServer[DownloadFileResp]) error {
	return status.Errorf(codes.Unimplemented, "method DownloadFile not implemented")
}
func (UnimplementedEliseServer) DeleteFile(context.Context, *DeleteFileReq) (*DeleteFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFile not implemented")
}
func (UnimplementedEliseServer) mustEmbedUnimplementedEliseServer() {}
func (UnimplementedEliseServer) testEmbeddedByValue()               {}

// UnsafeEliseServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EliseServer will
// result in compilation errors.
type UnsafeEliseServer interface {
	mustEmbedUnimplementedEliseServer()
}

func RegisterEliseServer(s grpc.ServiceRegistrar, srv EliseServer) {
	// If the following call pancis, it indicates UnimplementedEliseServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Elise_ServiceDesc, srv)
}

func _Elise_GetFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EliseServer).GetFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Elise_GetFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EliseServer).GetFile(ctx, req.(*GetFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Elise_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EliseServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Elise_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EliseServer).UploadFile(ctx, req.(*UploadFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Elise_DownloadFile_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(DownloadFileReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(EliseServer).DownloadFile(m, &grpc.GenericServerStream[DownloadFileReq, DownloadFileResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Elise_DownloadFileServer = grpc.ServerStreamingServer[DownloadFileResp]

func _Elise_DeleteFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EliseServer).DeleteFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Elise_DeleteFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EliseServer).DeleteFile(ctx, req.(*DeleteFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Elise_ServiceDesc is the grpc.ServiceDesc for Elise service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Elise_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "elise.Elise",
	HandlerType: (*EliseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetFile",
			Handler:    _Elise_GetFile_Handler,
		},
		{
			MethodName: "UploadFile",
			Handler:    _Elise_UploadFile_Handler,
		},
		{
			MethodName: "DeleteFile",
			Handler:    _Elise_DeleteFile_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "DownloadFile",
			Handler:       _Elise_DownloadFile_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "elise/elise.proto",
}
