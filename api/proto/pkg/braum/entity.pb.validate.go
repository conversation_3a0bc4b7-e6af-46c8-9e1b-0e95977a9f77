// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: braum/entity.proto

package braum

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OnOffInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OnOffInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnOffInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OnOffInfoMultiError, or nil
// if none found.
func (m *OnOffInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OnOffInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for GroupId

	// no validation rules for Ip

	// no validation rules for Online

	// no validation rules for OsType

	if len(errors) > 0 {
		return OnOffInfoMultiError(errors)
	}

	return nil
}

// OnOffInfoMultiError is an error wrapping multiple validation errors returned
// by OnOffInfo.ValidateAll() if the designated constraints aren't met.
type OnOffInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnOffInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnOffInfoMultiError) AllErrors() []error { return m }

// OnOffInfoValidationError is the validation error returned by
// OnOffInfo.Validate if the designated constraints aren't met.
type OnOffInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnOffInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnOffInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnOffInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnOffInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnOffInfoValidationError) ErrorName() string { return "OnOffInfoValidationError" }

// Error satisfies the builtin error interface
func (e OnOffInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnOffInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnOffInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnOffInfoValidationError{}

// Validate checks the field values on EntityInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityInfoMultiError, or
// nil if none found.
func (m *EntityInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for MachineName

	// no validation rules for OsType

	// no validation rules for Ip

	// no validation rules for Os

	// no validation rules for KernelVersion

	// no validation rules for Online

	// no validation rules for GroupId

	// no validation rules for Tag

	// no validation rules for LastOnlineAt

	// no validation rules for LastOfflineAt

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	// no validation rules for DeletedAt

	// no validation rules for GroupName

	// no validation rules for Arch

	// no validation rules for OsInfoShort

	// no validation rules for MacAddress

	if len(errors) > 0 {
		return EntityInfoMultiError(errors)
	}

	return nil
}

// EntityInfoMultiError is an error wrapping multiple validation errors
// returned by EntityInfo.ValidateAll() if the designated constraints aren't met.
type EntityInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityInfoMultiError) AllErrors() []error { return m }

// EntityInfoValidationError is the validation error returned by
// EntityInfo.Validate if the designated constraints aren't met.
type EntityInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityInfoValidationError) ErrorName() string { return "EntityInfoValidationError" }

// Error satisfies the builtin error interface
func (e EntityInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityInfoValidationError{}

// Validate checks the field values on EntityList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityListMultiError, or
// nil if none found.
func (m *EntityList) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityListValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityListValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EntityListMultiError(errors)
	}

	return nil
}

// EntityListMultiError is an error wrapping multiple validation errors
// returned by EntityList.ValidateAll() if the designated constraints aren't met.
type EntityListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityListMultiError) AllErrors() []error { return m }

// EntityListValidationError is the validation error returned by
// EntityList.Validate if the designated constraints aren't met.
type EntityListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityListValidationError) ErrorName() string { return "EntityListValidationError" }

// Error satisfies the builtin error interface
func (e EntityListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityListValidationError{}

// Validate checks the field values on EntitybaseInput with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntitybaseInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntitybaseInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntitybaseInputMultiError, or nil if none found.
func (m *EntitybaseInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntitybaseInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Ip

	// no validation rules for OsType

	// no validation rules for Tag

	// no validation rules for GroupId

	// no validation rules for MachineName

	// no validation rules for Os

	// no validation rules for KernelInfo

	// no validation rules for OsInfoLong

	// no validation rules for OsInfoShort

	// no validation rules for ProxyIpPort

	for idx, item := range m.GetVersionSlice() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntitybaseInputValidationError{
						field:  fmt.Sprintf("VersionSlice[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntitybaseInputValidationError{
						field:  fmt.Sprintf("VersionSlice[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntitybaseInputValidationError{
					field:  fmt.Sprintf("VersionSlice[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Arch

	// no validation rules for OsInfoDisplay

	// no validation rules for Mac

	// no validation rules for Gateway

	// no validation rules for LatestUser

	// no validation rules for CurrUser

	// no validation rules for DomainUser

	if len(errors) > 0 {
		return EntitybaseInputMultiError(errors)
	}

	return nil
}

// EntitybaseInputMultiError is an error wrapping multiple validation errors
// returned by EntitybaseInput.ValidateAll() if the designated constraints
// aren't met.
type EntitybaseInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntitybaseInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntitybaseInputMultiError) AllErrors() []error { return m }

// EntitybaseInputValidationError is the validation error returned by
// EntitybaseInput.Validate if the designated constraints aren't met.
type EntitybaseInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntitybaseInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntitybaseInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntitybaseInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntitybaseInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntitybaseInputValidationError) ErrorName() string { return "EntitybaseInputValidationError" }

// Error satisfies the builtin error interface
func (e EntitybaseInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntitybaseInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntitybaseInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntitybaseInputValidationError{}

// Validate checks the field values on EntityStatusStatistic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityStatusStatistic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityStatusStatistic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityStatusStatisticMultiError, or nil if none found.
func (m *EntityStatusStatistic) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityStatusStatistic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for HealthyCount

	// no validation rules for RiskCount

	// no validation rules for AttackedCount

	// no validation rules for OnlineCount

	// no validation rules for OfflineCount

	// no validation rules for UninstalledCount

	// no validation rules for WindowsCount

	// no validation rules for LinuxCount

	if len(errors) > 0 {
		return EntityStatusStatisticMultiError(errors)
	}

	return nil
}

// EntityStatusStatisticMultiError is an error wrapping multiple validation
// errors returned by EntityStatusStatistic.ValidateAll() if the designated
// constraints aren't met.
type EntityStatusStatisticMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityStatusStatisticMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityStatusStatisticMultiError) AllErrors() []error { return m }

// EntityStatusStatisticValidationError is the validation error returned by
// EntityStatusStatistic.Validate if the designated constraints aren't met.
type EntityStatusStatisticValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityStatusStatisticValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityStatusStatisticValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityStatusStatisticValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityStatusStatisticValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityStatusStatisticValidationError) ErrorName() string {
	return "EntityStatusStatisticValidationError"
}

// Error satisfies the builtin error interface
func (e EntityStatusStatisticValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityStatusStatistic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityStatusStatisticValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityStatusStatisticValidationError{}

// Validate checks the field values on AddTagInput with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddTagInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddTagInput with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddTagInputMultiError, or
// nil if none found.
func (m *AddTagInput) ValidateAll() error {
	return m.validate(true)
}

func (m *AddTagInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Tag

	if len(errors) > 0 {
		return AddTagInputMultiError(errors)
	}

	return nil
}

// AddTagInputMultiError is an error wrapping multiple validation errors
// returned by AddTagInput.ValidateAll() if the designated constraints aren't met.
type AddTagInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddTagInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddTagInputMultiError) AllErrors() []error { return m }

// AddTagInputValidationError is the validation error returned by
// AddTagInput.Validate if the designated constraints aren't met.
type AddTagInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddTagInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddTagInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddTagInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddTagInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddTagInputValidationError) ErrorName() string { return "AddTagInputValidationError" }

// Error satisfies the builtin error interface
func (e AddTagInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddTagInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddTagInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddTagInputValidationError{}

// Validate checks the field values on EntityMoveToGroupInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityMoveToGroupInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityMoveToGroupInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityMoveToGroupInputMultiError, or nil if none found.
func (m *EntityMoveToGroupInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityMoveToGroupInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityMoveToGroupInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityMoveToGroupInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityMoveToGroupInputValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EntityMoveToGroupInputMultiError(errors)
	}

	return nil
}

// EntityMoveToGroupInputMultiError is an error wrapping multiple validation
// errors returned by EntityMoveToGroupInput.ValidateAll() if the designated
// constraints aren't met.
type EntityMoveToGroupInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityMoveToGroupInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityMoveToGroupInputMultiError) AllErrors() []error { return m }

// EntityMoveToGroupInputValidationError is the validation error returned by
// EntityMoveToGroupInput.Validate if the designated constraints aren't met.
type EntityMoveToGroupInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityMoveToGroupInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityMoveToGroupInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityMoveToGroupInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityMoveToGroupInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityMoveToGroupInputValidationError) ErrorName() string {
	return "EntityMoveToGroupInputValidationError"
}

// Error satisfies the builtin error interface
func (e EntityMoveToGroupInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityMoveToGroupInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityMoveToGroupInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityMoveToGroupInputValidationError{}

// Validate checks the field values on MultiEntityMoveToGroupInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MultiEntityMoveToGroupInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MultiEntityMoveToGroupInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MultiEntityMoveToGroupInputMultiError, or nil if none found.
func (m *MultiEntityMoveToGroupInput) ValidateAll() error {
	return m.validate(true)
}

func (m *MultiEntityMoveToGroupInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	if len(errors) > 0 {
		return MultiEntityMoveToGroupInputMultiError(errors)
	}

	return nil
}

// MultiEntityMoveToGroupInputMultiError is an error wrapping multiple
// validation errors returned by MultiEntityMoveToGroupInput.ValidateAll() if
// the designated constraints aren't met.
type MultiEntityMoveToGroupInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MultiEntityMoveToGroupInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MultiEntityMoveToGroupInputMultiError) AllErrors() []error { return m }

// MultiEntityMoveToGroupInputValidationError is the validation error returned
// by MultiEntityMoveToGroupInput.Validate if the designated constraints
// aren't met.
type MultiEntityMoveToGroupInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MultiEntityMoveToGroupInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MultiEntityMoveToGroupInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MultiEntityMoveToGroupInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MultiEntityMoveToGroupInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MultiEntityMoveToGroupInputValidationError) ErrorName() string {
	return "MultiEntityMoveToGroupInputValidationError"
}

// Error satisfies the builtin error interface
func (e MultiEntityMoveToGroupInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMultiEntityMoveToGroupInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MultiEntityMoveToGroupInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MultiEntityMoveToGroupInputValidationError{}

// Validate checks the field values on QueryMachineIDFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryMachineIDFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryMachineIDFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryMachineIDFilterMultiError, or nil if none found.
func (m *QueryMachineIDFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryMachineIDFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	// no validation rules for OsType

	if len(errors) > 0 {
		return QueryMachineIDFilterMultiError(errors)
	}

	return nil
}

// QueryMachineIDFilterMultiError is an error wrapping multiple validation
// errors returned by QueryMachineIDFilter.ValidateAll() if the designated
// constraints aren't met.
type QueryMachineIDFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryMachineIDFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryMachineIDFilterMultiError) AllErrors() []error { return m }

// QueryMachineIDFilterValidationError is the validation error returned by
// QueryMachineIDFilter.Validate if the designated constraints aren't met.
type QueryMachineIDFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryMachineIDFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryMachineIDFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryMachineIDFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryMachineIDFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryMachineIDFilterValidationError) ErrorName() string {
	return "QueryMachineIDFilterValidationError"
}

// Error satisfies the builtin error interface
func (e QueryMachineIDFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryMachineIDFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryMachineIDFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryMachineIDFilterValidationError{}

// Validate checks the field values on QueryMachineIDInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryMachineIDInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryMachineIDInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryMachineIDInputMultiError, or nil if none found.
func (m *QueryMachineIDInput) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryMachineIDInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryMachineIDInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryMachineIDInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryMachineIDInputValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryMachineIDInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryMachineIDInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryMachineIDInputValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QueryMachineIDInputMultiError(errors)
	}

	return nil
}

// QueryMachineIDInputMultiError is an error wrapping multiple validation
// errors returned by QueryMachineIDInput.ValidateAll() if the designated
// constraints aren't met.
type QueryMachineIDInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryMachineIDInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryMachineIDInputMultiError) AllErrors() []error { return m }

// QueryMachineIDInputValidationError is the validation error returned by
// QueryMachineIDInput.Validate if the designated constraints aren't met.
type QueryMachineIDInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryMachineIDInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryMachineIDInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryMachineIDInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryMachineIDInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryMachineIDInputValidationError) ErrorName() string {
	return "QueryMachineIDInputValidationError"
}

// Error satisfies the builtin error interface
func (e QueryMachineIDInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryMachineIDInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryMachineIDInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryMachineIDInputValidationError{}

// Validate checks the field values on MachineIDList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MachineIDList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MachineIDList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MachineIDListMultiError, or
// nil if none found.
func (m *MachineIDList) ValidateAll() error {
	return m.validate(true)
}

func (m *MachineIDList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MachineIDListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MachineIDListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MachineIDListValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MachineIDListMultiError(errors)
	}

	return nil
}

// MachineIDListMultiError is an error wrapping multiple validation errors
// returned by MachineIDList.ValidateAll() if the designated constraints
// aren't met.
type MachineIDListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MachineIDListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MachineIDListMultiError) AllErrors() []error { return m }

// MachineIDListValidationError is the validation error returned by
// MachineIDList.Validate if the designated constraints aren't met.
type MachineIDListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MachineIDListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MachineIDListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MachineIDListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MachineIDListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MachineIDListValidationError) ErrorName() string { return "MachineIDListValidationError" }

// Error satisfies the builtin error interface
func (e MachineIDListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMachineIDList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MachineIDListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MachineIDListValidationError{}

// Validate checks the field values on RiskStatusInput with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskStatusInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskStatusInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskStatusInputMultiError, or nil if none found.
func (m *RiskStatusInput) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskStatusInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Attack

	// no validation rules for Risk

	if len(errors) > 0 {
		return RiskStatusInputMultiError(errors)
	}

	return nil
}

// RiskStatusInputMultiError is an error wrapping multiple validation errors
// returned by RiskStatusInput.ValidateAll() if the designated constraints
// aren't met.
type RiskStatusInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskStatusInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskStatusInputMultiError) AllErrors() []error { return m }

// RiskStatusInputValidationError is the validation error returned by
// RiskStatusInput.Validate if the designated constraints aren't met.
type RiskStatusInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskStatusInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskStatusInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskStatusInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskStatusInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskStatusInputValidationError) ErrorName() string { return "RiskStatusInputValidationError" }

// Error satisfies the builtin error interface
func (e RiskStatusInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskStatusInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskStatusInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskStatusInputValidationError{}

// Validate checks the field values on EntityVersionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityVersionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityVersionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityVersionRequestMultiError, or nil if none found.
func (m *EntityVersionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityVersionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for VerKind

	if len(errors) > 0 {
		return EntityVersionRequestMultiError(errors)
	}

	return nil
}

// EntityVersionRequestMultiError is an error wrapping multiple validation
// errors returned by EntityVersionRequest.ValidateAll() if the designated
// constraints aren't met.
type EntityVersionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityVersionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityVersionRequestMultiError) AllErrors() []error { return m }

// EntityVersionRequestValidationError is the validation error returned by
// EntityVersionRequest.Validate if the designated constraints aren't met.
type EntityVersionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityVersionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityVersionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityVersionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityVersionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityVersionRequestValidationError) ErrorName() string {
	return "EntityVersionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EntityVersionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityVersionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityVersionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityVersionRequestValidationError{}

// Validate checks the field values on EntityVersion with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityVersion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityVersion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityVersionMultiError, or
// nil if none found.
func (m *EntityVersion) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityVersion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for VerKind

	// no validation rules for VerType

	// no validation rules for Version

	if len(errors) > 0 {
		return EntityVersionMultiError(errors)
	}

	return nil
}

// EntityVersionMultiError is an error wrapping multiple validation errors
// returned by EntityVersion.ValidateAll() if the designated constraints
// aren't met.
type EntityVersionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityVersionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityVersionMultiError) AllErrors() []error { return m }

// EntityVersionValidationError is the validation error returned by
// EntityVersion.Validate if the designated constraints aren't met.
type EntityVersionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityVersionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityVersionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityVersionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityVersionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityVersionValidationError) ErrorName() string { return "EntityVersionValidationError" }

// Error satisfies the builtin error interface
func (e EntityVersionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityVersion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityVersionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityVersionValidationError{}

// Validate checks the field values on EntityDetailInput with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityDetailInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityDetailInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityDetailInputMultiError, or nil if none found.
func (m *EntityDetailInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityDetailInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IncludeGroupName

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityDetailInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityDetailInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityDetailInputValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EntityDetailInputMultiError(errors)
	}

	return nil
}

// EntityDetailInputMultiError is an error wrapping multiple validation errors
// returned by EntityDetailInput.ValidateAll() if the designated constraints
// aren't met.
type EntityDetailInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityDetailInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityDetailInputMultiError) AllErrors() []error { return m }

// EntityDetailInputValidationError is the validation error returned by
// EntityDetailInput.Validate if the designated constraints aren't met.
type EntityDetailInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityDetailInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityDetailInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityDetailInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityDetailInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityDetailInputValidationError) ErrorName() string {
	return "EntityDetailInputValidationError"
}

// Error satisfies the builtin error interface
func (e EntityDetailInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityDetailInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityDetailInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityDetailInputValidationError{}

// Validate checks the field values on EntityDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityDetailResponseMultiError, or nil if none found.
func (m *EntityDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEntityInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityDetailResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityDetailResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntityInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityDetailResponseValidationError{
				field:  "EntityInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVersions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityDetailResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityDetailResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityDetailResponseValidationError{
					field:  fmt.Sprintf("Versions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EntityDetailResponseMultiError(errors)
	}

	return nil
}

// EntityDetailResponseMultiError is an error wrapping multiple validation
// errors returned by EntityDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type EntityDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityDetailResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityDetailResponseMultiError) AllErrors() []error { return m }

// EntityDetailResponseValidationError is the validation error returned by
// EntityDetailResponse.Validate if the designated constraints aren't met.
type EntityDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityDetailResponseValidationError) ErrorName() string {
	return "EntityDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EntityDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityDetailResponseValidationError{}

// Validate checks the field values on EntityDetailList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityDetailList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityDetailList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityDetailListMultiError, or nil if none found.
func (m *EntityDetailList) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityDetailList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityDetailListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityDetailListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityDetailListValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityDetailListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityDetailListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityDetailListValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EntityDetailListMultiError(errors)
	}

	return nil
}

// EntityDetailListMultiError is an error wrapping multiple validation errors
// returned by EntityDetailList.ValidateAll() if the designated constraints
// aren't met.
type EntityDetailListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityDetailListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityDetailListMultiError) AllErrors() []error { return m }

// EntityDetailListValidationError is the validation error returned by
// EntityDetailList.Validate if the designated constraints aren't met.
type EntityDetailListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityDetailListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityDetailListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityDetailListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityDetailListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityDetailListValidationError) ErrorName() string { return "EntityDetailListValidationError" }

// Error satisfies the builtin error interface
func (e EntityDetailListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityDetailList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityDetailListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityDetailListValidationError{}

// Validate checks the field values on EntityImportInput with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityImportInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityImportInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityImportInputMultiError, or nil if none found.
func (m *EntityImportInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityImportInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImportMode

	// no validation rules for BucketName

	// no validation rules for ObjectName

	if len(errors) > 0 {
		return EntityImportInputMultiError(errors)
	}

	return nil
}

// EntityImportInputMultiError is an error wrapping multiple validation errors
// returned by EntityImportInput.ValidateAll() if the designated constraints
// aren't met.
type EntityImportInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityImportInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityImportInputMultiError) AllErrors() []error { return m }

// EntityImportInputValidationError is the validation error returned by
// EntityImportInput.Validate if the designated constraints aren't met.
type EntityImportInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityImportInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityImportInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityImportInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityImportInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityImportInputValidationError) ErrorName() string {
	return "EntityImportInputValidationError"
}

// Error satisfies the builtin error interface
func (e EntityImportInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityImportInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityImportInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityImportInputValidationError{}

// Validate checks the field values on EntityImportResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityImportResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityImportResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityImportResultMultiError, or nil if none found.
func (m *EntityImportResult) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityImportResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Success

	// no validation rules for Failure

	// no validation rules for LogId

	if len(errors) > 0 {
		return EntityImportResultMultiError(errors)
	}

	return nil
}

// EntityImportResultMultiError is an error wrapping multiple validation errors
// returned by EntityImportResult.ValidateAll() if the designated constraints
// aren't met.
type EntityImportResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityImportResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityImportResultMultiError) AllErrors() []error { return m }

// EntityImportResultValidationError is the validation error returned by
// EntityImportResult.Validate if the designated constraints aren't met.
type EntityImportResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityImportResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityImportResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityImportResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityImportResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityImportResultValidationError) ErrorName() string {
	return "EntityImportResultValidationError"
}

// Error satisfies the builtin error interface
func (e EntityImportResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityImportResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityImportResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityImportResultValidationError{}

// Validate checks the field values on EntityExtInfoInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityExtInfoInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityExtInfoInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityExtInfoInputMultiError, or nil if none found.
func (m *EntityExtInfoInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityExtInfoInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if all {
		switch v := interface{}(m.GetExtInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityExtInfoInputValidationError{
					field:  "ExtInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityExtInfoInputValidationError{
					field:  "ExtInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityExtInfoInputValidationError{
				field:  "ExtInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EntityExtInfoInputMultiError(errors)
	}

	return nil
}

// EntityExtInfoInputMultiError is an error wrapping multiple validation errors
// returned by EntityExtInfoInput.ValidateAll() if the designated constraints
// aren't met.
type EntityExtInfoInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityExtInfoInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityExtInfoInputMultiError) AllErrors() []error { return m }

// EntityExtInfoInputValidationError is the validation error returned by
// EntityExtInfoInput.Validate if the designated constraints aren't met.
type EntityExtInfoInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityExtInfoInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityExtInfoInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityExtInfoInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityExtInfoInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityExtInfoInputValidationError) ErrorName() string {
	return "EntityExtInfoInputValidationError"
}

// Error satisfies the builtin error interface
func (e EntityExtInfoInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityExtInfoInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityExtInfoInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityExtInfoInputValidationError{}

// Validate checks the field values on EntityImportLog with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityImportLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityImportLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityImportLogMultiError, or nil if none found.
func (m *EntityImportLog) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityImportLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Status

	// no validation rules for Success

	// no validation rules for Failure

	// no validation rules for AdminId

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return EntityImportLogMultiError(errors)
	}

	return nil
}

// EntityImportLogMultiError is an error wrapping multiple validation errors
// returned by EntityImportLog.ValidateAll() if the designated constraints
// aren't met.
type EntityImportLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityImportLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityImportLogMultiError) AllErrors() []error { return m }

// EntityImportLogValidationError is the validation error returned by
// EntityImportLog.Validate if the designated constraints aren't met.
type EntityImportLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityImportLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityImportLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityImportLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityImportLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityImportLogValidationError) ErrorName() string { return "EntityImportLogValidationError" }

// Error satisfies the builtin error interface
func (e EntityImportLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityImportLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityImportLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityImportLogValidationError{}

// Validate checks the field values on EntityImportLogList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityImportLogList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityImportLogList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityImportLogListMultiError, or nil if none found.
func (m *EntityImportLogList) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityImportLogList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityImportLogListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityImportLogListValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityImportLogListValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EntityImportLogListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EntityImportLogListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EntityImportLogListValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EntityImportLogListMultiError(errors)
	}

	return nil
}

// EntityImportLogListMultiError is an error wrapping multiple validation
// errors returned by EntityImportLogList.ValidateAll() if the designated
// constraints aren't met.
type EntityImportLogListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityImportLogListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityImportLogListMultiError) AllErrors() []error { return m }

// EntityImportLogListValidationError is the validation error returned by
// EntityImportLogList.Validate if the designated constraints aren't met.
type EntityImportLogListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityImportLogListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityImportLogListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityImportLogListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityImportLogListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityImportLogListValidationError) ErrorName() string {
	return "EntityImportLogListValidationError"
}

// Error satisfies the builtin error interface
func (e EntityImportLogListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityImportLogList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityImportLogListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityImportLogListValidationError{}

// Validate checks the field values on AOSObjectInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AOSObjectInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AOSObjectInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AOSObjectInfoMultiError, or
// nil if none found.
func (m *AOSObjectInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AOSObjectInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BucketName

	// no validation rules for ObjectName

	// no validation rules for Url

	if len(errors) > 0 {
		return AOSObjectInfoMultiError(errors)
	}

	return nil
}

// AOSObjectInfoMultiError is an error wrapping multiple validation errors
// returned by AOSObjectInfo.ValidateAll() if the designated constraints
// aren't met.
type AOSObjectInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AOSObjectInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AOSObjectInfoMultiError) AllErrors() []error { return m }

// AOSObjectInfoValidationError is the validation error returned by
// AOSObjectInfo.Validate if the designated constraints aren't met.
type AOSObjectInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AOSObjectInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AOSObjectInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AOSObjectInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AOSObjectInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AOSObjectInfoValidationError) ErrorName() string { return "AOSObjectInfoValidationError" }

// Error satisfies the builtin error interface
func (e AOSObjectInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAOSObjectInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AOSObjectInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AOSObjectInfoValidationError{}

// Validate checks the field values on SimpleEntityInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SimpleEntityInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SimpleEntityInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SimpleEntityInfoMultiError, or nil if none found.
func (m *SimpleEntityInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SimpleEntityInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for Os

	// no validation rules for OsType

	// no validation rules for Ip

	// no validation rules for MachineName

	// no validation rules for Arch

	// no validation rules for Gateway

	// no validation rules for CurrUser

	// no validation rules for LatestUser

	// no validation rules for DomainUser

	// no validation rules for HostId

	// no validation rules for AgentVersion

	// no validation rules for FirstInstallAt

	// no validation rules for AgentUpgradeAt

	// no validation rules for LatestOnlineAt

	// no validation rules for LatestOfflineAt

	// no validation rules for UserName

	if len(errors) > 0 {
		return SimpleEntityInfoMultiError(errors)
	}

	return nil
}

// SimpleEntityInfoMultiError is an error wrapping multiple validation errors
// returned by SimpleEntityInfo.ValidateAll() if the designated constraints
// aren't met.
type SimpleEntityInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SimpleEntityInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SimpleEntityInfoMultiError) AllErrors() []error { return m }

// SimpleEntityInfoValidationError is the validation error returned by
// SimpleEntityInfo.Validate if the designated constraints aren't met.
type SimpleEntityInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SimpleEntityInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SimpleEntityInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SimpleEntityInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SimpleEntityInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SimpleEntityInfoValidationError) ErrorName() string { return "SimpleEntityInfoValidationError" }

// Error satisfies the builtin error interface
func (e SimpleEntityInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSimpleEntityInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SimpleEntityInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SimpleEntityInfoValidationError{}

// Validate checks the field values on UpdateEntityVulCountInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEntityVulCountInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEntityVulCountInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEntityVulCountInputMultiError, or nil if none found.
func (m *UpdateEntityVulCountInput) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEntityVulCountInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for KernelVulCount

	// no validation rules for AppVulCount

	// no validation rules for WebVulCount

	if len(errors) > 0 {
		return UpdateEntityVulCountInputMultiError(errors)
	}

	return nil
}

// UpdateEntityVulCountInputMultiError is an error wrapping multiple validation
// errors returned by UpdateEntityVulCountInput.ValidateAll() if the
// designated constraints aren't met.
type UpdateEntityVulCountInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEntityVulCountInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEntityVulCountInputMultiError) AllErrors() []error { return m }

// UpdateEntityVulCountInputValidationError is the validation error returned by
// UpdateEntityVulCountInput.Validate if the designated constraints aren't met.
type UpdateEntityVulCountInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEntityVulCountInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEntityVulCountInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEntityVulCountInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEntityVulCountInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEntityVulCountInputValidationError) ErrorName() string {
	return "UpdateEntityVulCountInputValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEntityVulCountInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEntityVulCountInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEntityVulCountInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEntityVulCountInputValidationError{}

// Validate checks the field values on EntityComMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityComMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityComMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityComMessageMultiError, or nil if none found.
func (m *EntityComMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityComMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Type

	// no validation rules for Method

	// no validation rules for Data

	if len(errors) > 0 {
		return EntityComMessageMultiError(errors)
	}

	return nil
}

// EntityComMessageMultiError is an error wrapping multiple validation errors
// returned by EntityComMessage.ValidateAll() if the designated constraints
// aren't met.
type EntityComMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityComMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityComMessageMultiError) AllErrors() []error { return m }

// EntityComMessageValidationError is the validation error returned by
// EntityComMessage.Validate if the designated constraints aren't met.
type EntityComMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityComMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityComMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityComMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityComMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityComMessageValidationError) ErrorName() string { return "EntityComMessageValidationError" }

// Error satisfies the builtin error interface
func (e EntityComMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityComMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityComMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityComMessageValidationError{}

// Validate checks the field values on ModifyMachineUserNameReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyMachineUserNameReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyMachineUserNameReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyMachineUserNameReqMultiError, or nil if none found.
func (m *ModifyMachineUserNameReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyMachineUserNameReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for UserName

	if len(errors) > 0 {
		return ModifyMachineUserNameReqMultiError(errors)
	}

	return nil
}

// ModifyMachineUserNameReqMultiError is an error wrapping multiple validation
// errors returned by ModifyMachineUserNameReq.ValidateAll() if the designated
// constraints aren't met.
type ModifyMachineUserNameReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyMachineUserNameReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyMachineUserNameReqMultiError) AllErrors() []error { return m }

// ModifyMachineUserNameReqValidationError is the validation error returned by
// ModifyMachineUserNameReq.Validate if the designated constraints aren't met.
type ModifyMachineUserNameReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyMachineUserNameReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyMachineUserNameReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyMachineUserNameReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyMachineUserNameReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyMachineUserNameReqValidationError) ErrorName() string {
	return "ModifyMachineUserNameReqValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyMachineUserNameReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyMachineUserNameReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyMachineUserNameReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyMachineUserNameReqValidationError{}

// Validate checks the field values on RemoveEntityInput with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RemoveEntityInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveEntityInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveEntityInputMultiError, or nil if none found.
func (m *RemoveEntityInput) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveEntityInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveEntityInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveEntityInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveEntityInputValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemoveEntityInputMultiError(errors)
	}

	return nil
}

// RemoveEntityInputMultiError is an error wrapping multiple validation errors
// returned by RemoveEntityInput.ValidateAll() if the designated constraints
// aren't met.
type RemoveEntityInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveEntityInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveEntityInputMultiError) AllErrors() []error { return m }

// RemoveEntityInputValidationError is the validation error returned by
// RemoveEntityInput.Validate if the designated constraints aren't met.
type RemoveEntityInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveEntityInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveEntityInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveEntityInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveEntityInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveEntityInputValidationError) ErrorName() string {
	return "RemoveEntityInputValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveEntityInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveEntityInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveEntityInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveEntityInputValidationError{}

// Validate checks the field values on EntityCountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityCountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityCountResponseMultiError, or nil if none found.
func (m *EntityCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return EntityCountResponseMultiError(errors)
	}

	return nil
}

// EntityCountResponseMultiError is an error wrapping multiple validation
// errors returned by EntityCountResponse.ValidateAll() if the designated
// constraints aren't met.
type EntityCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityCountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityCountResponseMultiError) AllErrors() []error { return m }

// EntityCountResponseValidationError is the validation error returned by
// EntityCountResponse.Validate if the designated constraints aren't met.
type EntityCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityCountResponseValidationError) ErrorName() string {
	return "EntityCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EntityCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityCountResponseValidationError{}

// Validate checks the field values on CommonResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonResponseMultiError,
// or nil if none found.
func (m *CommonResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for ErrorMessage

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return CommonResponseMultiError(errors)
	}

	return nil
}

// CommonResponseMultiError is an error wrapping multiple validation errors
// returned by CommonResponse.ValidateAll() if the designated constraints
// aren't met.
type CommonResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonResponseMultiError) AllErrors() []error { return m }

// CommonResponseValidationError is the validation error returned by
// CommonResponse.Validate if the designated constraints aren't met.
type CommonResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonResponseValidationError) ErrorName() string { return "CommonResponseValidationError" }

// Error satisfies the builtin error interface
func (e CommonResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonResponseValidationError{}

// Validate checks the field values on UpsertEntityInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpsertEntityInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpsertEntityInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpsertEntityInfoRequestMultiError, or nil if none found.
func (m *UpsertEntityInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpsertEntityInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEntities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpsertEntityInfoRequestValidationError{
						field:  fmt.Sprintf("Entities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpsertEntityInfoRequestValidationError{
						field:  fmt.Sprintf("Entities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpsertEntityInfoRequestValidationError{
					field:  fmt.Sprintf("Entities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpsertEntityInfoRequestMultiError(errors)
	}

	return nil
}

// UpsertEntityInfoRequestMultiError is an error wrapping multiple validation
// errors returned by UpsertEntityInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpsertEntityInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpsertEntityInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpsertEntityInfoRequestMultiError) AllErrors() []error { return m }

// UpsertEntityInfoRequestValidationError is the validation error returned by
// UpsertEntityInfoRequest.Validate if the designated constraints aren't met.
type UpsertEntityInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpsertEntityInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpsertEntityInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpsertEntityInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpsertEntityInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpsertEntityInfoRequestValidationError) ErrorName() string {
	return "UpsertEntityInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpsertEntityInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpsertEntityInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpsertEntityInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpsertEntityInfoRequestValidationError{}

// Validate checks the field values on EntityInfoRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EntityInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityInfoRequestMultiError, or nil if none found.
func (m *EntityInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if len(errors) > 0 {
		return EntityInfoRequestMultiError(errors)
	}

	return nil
}

// EntityInfoRequestMultiError is an error wrapping multiple validation errors
// returned by EntityInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type EntityInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityInfoRequestMultiError) AllErrors() []error { return m }

// EntityInfoRequestValidationError is the validation error returned by
// EntityInfoRequest.Validate if the designated constraints aren't met.
type EntityInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityInfoRequestValidationError) ErrorName() string {
	return "EntityInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EntityInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityInfoRequestValidationError{}

// Validate checks the field values on EntityInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EntityInfoResponseMultiError, or nil if none found.
func (m *EntityInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEntityInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInfoResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInfoResponseValidationError{
					field:  "EntityInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEntityInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInfoResponseValidationError{
				field:  "EntityInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EntityInfoResponseMultiError(errors)
	}

	return nil
}

// EntityInfoResponseMultiError is an error wrapping multiple validation errors
// returned by EntityInfoResponse.ValidateAll() if the designated constraints
// aren't met.
type EntityInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityInfoResponseMultiError) AllErrors() []error { return m }

// EntityInfoResponseValidationError is the validation error returned by
// EntityInfoResponse.Validate if the designated constraints aren't met.
type EntityInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityInfoResponseValidationError) ErrorName() string {
	return "EntityInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EntityInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityInfoResponseValidationError{}

// Validate checks the field values on BatchQueryEntityInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchQueryEntityInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchQueryEntityInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchQueryEntityInfoRequestMultiError, or nil if none found.
func (m *BatchQueryEntityInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchQueryEntityInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchQueryEntityInfoRequestMultiError(errors)
	}

	return nil
}

// BatchQueryEntityInfoRequestMultiError is an error wrapping multiple
// validation errors returned by BatchQueryEntityInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type BatchQueryEntityInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchQueryEntityInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchQueryEntityInfoRequestMultiError) AllErrors() []error { return m }

// BatchQueryEntityInfoRequestValidationError is the validation error returned
// by BatchQueryEntityInfoRequest.Validate if the designated constraints
// aren't met.
type BatchQueryEntityInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchQueryEntityInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchQueryEntityInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchQueryEntityInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchQueryEntityInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchQueryEntityInfoRequestValidationError) ErrorName() string {
	return "BatchQueryEntityInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchQueryEntityInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchQueryEntityInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchQueryEntityInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchQueryEntityInfoRequestValidationError{}

// Validate checks the field values on BatchQueryEntityInfoByIPRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchQueryEntityInfoByIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchQueryEntityInfoByIPRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchQueryEntityInfoByIPRequestMultiError, or nil if none found.
func (m *BatchQueryEntityInfoByIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchQueryEntityInfoByIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchQueryEntityInfoByIPRequestMultiError(errors)
	}

	return nil
}

// BatchQueryEntityInfoByIPRequestMultiError is an error wrapping multiple
// validation errors returned by BatchQueryEntityInfoByIPRequest.ValidateAll()
// if the designated constraints aren't met.
type BatchQueryEntityInfoByIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchQueryEntityInfoByIPRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchQueryEntityInfoByIPRequestMultiError) AllErrors() []error { return m }

// BatchQueryEntityInfoByIPRequestValidationError is the validation error
// returned by BatchQueryEntityInfoByIPRequest.Validate if the designated
// constraints aren't met.
type BatchQueryEntityInfoByIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchQueryEntityInfoByIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchQueryEntityInfoByIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchQueryEntityInfoByIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchQueryEntityInfoByIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchQueryEntityInfoByIPRequestValidationError) ErrorName() string {
	return "BatchQueryEntityInfoByIPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchQueryEntityInfoByIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchQueryEntityInfoByIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchQueryEntityInfoByIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchQueryEntityInfoByIPRequestValidationError{}

// Validate checks the field values on BatchQueryEntityInfoByMachineNameRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BatchQueryEntityInfoByMachineNameRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BatchQueryEntityInfoByMachineNameRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// BatchQueryEntityInfoByMachineNameRequestMultiError, or nil if none found.
func (m *BatchQueryEntityInfoByMachineNameRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchQueryEntityInfoByMachineNameRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchQueryEntityInfoByMachineNameRequestMultiError(errors)
	}

	return nil
}

// BatchQueryEntityInfoByMachineNameRequestMultiError is an error wrapping
// multiple validation errors returned by
// BatchQueryEntityInfoByMachineNameRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchQueryEntityInfoByMachineNameRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchQueryEntityInfoByMachineNameRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchQueryEntityInfoByMachineNameRequestMultiError) AllErrors() []error { return m }

// BatchQueryEntityInfoByMachineNameRequestValidationError is the validation
// error returned by BatchQueryEntityInfoByMachineNameRequest.Validate if the
// designated constraints aren't met.
type BatchQueryEntityInfoByMachineNameRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) ErrorName() string {
	return "BatchQueryEntityInfoByMachineNameRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchQueryEntityInfoByMachineNameRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchQueryEntityInfoByMachineNameRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchQueryEntityInfoByMachineNameRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchQueryEntityInfoByMachineNameRequestValidationError{}

// Validate checks the field values on EntityInput with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityInput with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityInputMultiError, or
// nil if none found.
func (m *EntityInput) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInputValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInputValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderBy

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EntityInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EntityInputValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EntityInputValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EntityInputMultiError(errors)
	}

	return nil
}

// EntityInputMultiError is an error wrapping multiple validation errors
// returned by EntityInput.ValidateAll() if the designated constraints aren't met.
type EntityInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityInputMultiError) AllErrors() []error { return m }

// EntityInputValidationError is the validation error returned by
// EntityInput.Validate if the designated constraints aren't met.
type EntityInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityInputValidationError) ErrorName() string { return "EntityInputValidationError" }

// Error satisfies the builtin error interface
func (e EntityInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityInputValidationError{}

// Validate checks the field values on RemoveMultiEntityRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveMultiEntityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveMultiEntityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveMultiEntityRequestMultiError, or nil if none found.
func (m *RemoveMultiEntityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveMultiEntityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RemoveMultiEntityRequestMultiError(errors)
	}

	return nil
}

// RemoveMultiEntityRequestMultiError is an error wrapping multiple validation
// errors returned by RemoveMultiEntityRequest.ValidateAll() if the designated
// constraints aren't met.
type RemoveMultiEntityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveMultiEntityRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveMultiEntityRequestMultiError) AllErrors() []error { return m }

// RemoveMultiEntityRequestValidationError is the validation error returned by
// RemoveMultiEntityRequest.Validate if the designated constraints aren't met.
type RemoveMultiEntityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveMultiEntityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveMultiEntityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveMultiEntityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveMultiEntityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveMultiEntityRequestValidationError) ErrorName() string {
	return "RemoveMultiEntityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveMultiEntityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveMultiEntityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveMultiEntityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveMultiEntityRequestValidationError{}

// Validate checks the field values on QueryEntityMacListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryEntityMacListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryEntityMacListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryEntityMacListRequestMultiError, or nil if none found.
func (m *QueryEntityMacListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryEntityMacListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchData

	if len(errors) > 0 {
		return QueryEntityMacListRequestMultiError(errors)
	}

	return nil
}

// QueryEntityMacListRequestMultiError is an error wrapping multiple validation
// errors returned by QueryEntityMacListRequest.ValidateAll() if the
// designated constraints aren't met.
type QueryEntityMacListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryEntityMacListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryEntityMacListRequestMultiError) AllErrors() []error { return m }

// QueryEntityMacListRequestValidationError is the validation error returned by
// QueryEntityMacListRequest.Validate if the designated constraints aren't met.
type QueryEntityMacListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryEntityMacListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryEntityMacListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryEntityMacListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryEntityMacListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryEntityMacListRequestValidationError) ErrorName() string {
	return "QueryEntityMacListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryEntityMacListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryEntityMacListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryEntityMacListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryEntityMacListRequestValidationError{}

// Validate checks the field values on QueryVersionListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryVersionListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryVersionListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryVersionListRequestMultiError, or nil if none found.
func (m *QueryVersionListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryVersionListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VersionKind

	if len(errors) > 0 {
		return QueryVersionListRequestMultiError(errors)
	}

	return nil
}

// QueryVersionListRequestMultiError is an error wrapping multiple validation
// errors returned by QueryVersionListRequest.ValidateAll() if the designated
// constraints aren't met.
type QueryVersionListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryVersionListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryVersionListRequestMultiError) AllErrors() []error { return m }

// QueryVersionListRequestValidationError is the validation error returned by
// QueryVersionListRequest.Validate if the designated constraints aren't met.
type QueryVersionListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryVersionListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryVersionListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryVersionListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryVersionListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryVersionListRequestValidationError) ErrorName() string {
	return "QueryVersionListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryVersionListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryVersionListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryVersionListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryVersionListRequestValidationError{}

// Validate checks the field values on QueryVersionListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryVersionListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryVersionListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryVersionListResponseMultiError, or nil if none found.
func (m *QueryVersionListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryVersionListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return QueryVersionListResponseMultiError(errors)
	}

	return nil
}

// QueryVersionListResponseMultiError is an error wrapping multiple validation
// errors returned by QueryVersionListResponse.ValidateAll() if the designated
// constraints aren't met.
type QueryVersionListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryVersionListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryVersionListResponseMultiError) AllErrors() []error { return m }

// QueryVersionListResponseValidationError is the validation error returned by
// QueryVersionListResponse.Validate if the designated constraints aren't met.
type QueryVersionListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryVersionListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryVersionListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryVersionListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryVersionListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryVersionListResponseValidationError) ErrorName() string {
	return "QueryVersionListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryVersionListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryVersionListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryVersionListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryVersionListResponseValidationError{}

// Validate checks the field values on QueryEntityVersionListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryEntityVersionListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryEntityVersionListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryEntityVersionListResponseMultiError, or nil if none found.
func (m *QueryEntityVersionListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryEntityVersionListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVersions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryEntityVersionListResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryEntityVersionListResponseValidationError{
						field:  fmt.Sprintf("Versions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryEntityVersionListResponseValidationError{
					field:  fmt.Sprintf("Versions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryEntityVersionListResponseMultiError(errors)
	}

	return nil
}

// QueryEntityVersionListResponseMultiError is an error wrapping multiple
// validation errors returned by QueryEntityVersionListResponse.ValidateAll()
// if the designated constraints aren't met.
type QueryEntityVersionListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryEntityVersionListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryEntityVersionListResponseMultiError) AllErrors() []error { return m }

// QueryEntityVersionListResponseValidationError is the validation error
// returned by QueryEntityVersionListResponse.Validate if the designated
// constraints aren't met.
type QueryEntityVersionListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryEntityVersionListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryEntityVersionListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryEntityVersionListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryEntityVersionListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryEntityVersionListResponseValidationError) ErrorName() string {
	return "QueryEntityVersionListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryEntityVersionListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryEntityVersionListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryEntityVersionListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryEntityVersionListResponseValidationError{}

// Validate checks the field values on EntityImportTemplateURLRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EntityImportTemplateURLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityImportTemplateURLRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// EntityImportTemplateURLRequestMultiError, or nil if none found.
func (m *EntityImportTemplateURLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityImportTemplateURLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filename

	if len(errors) > 0 {
		return EntityImportTemplateURLRequestMultiError(errors)
	}

	return nil
}

// EntityImportTemplateURLRequestMultiError is an error wrapping multiple
// validation errors returned by EntityImportTemplateURLRequest.ValidateAll()
// if the designated constraints aren't met.
type EntityImportTemplateURLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityImportTemplateURLRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityImportTemplateURLRequestMultiError) AllErrors() []error { return m }

// EntityImportTemplateURLRequestValidationError is the validation error
// returned by EntityImportTemplateURLRequest.Validate if the designated
// constraints aren't met.
type EntityImportTemplateURLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityImportTemplateURLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityImportTemplateURLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityImportTemplateURLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityImportTemplateURLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityImportTemplateURLRequestValidationError) ErrorName() string {
	return "EntityImportTemplateURLRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EntityImportTemplateURLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityImportTemplateURLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityImportTemplateURLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityImportTemplateURLRequestValidationError{}

// Validate checks the field values on GetEntityLogURLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEntityLogURLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEntityLogURLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEntityLogURLRequestMultiError, or nil if none found.
func (m *GetEntityLogURLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEntityLogURLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetEntityLogURLRequestMultiError(errors)
	}

	return nil
}

// GetEntityLogURLRequestMultiError is an error wrapping multiple validation
// errors returned by GetEntityLogURLRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEntityLogURLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEntityLogURLRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEntityLogURLRequestMultiError) AllErrors() []error { return m }

// GetEntityLogURLRequestValidationError is the validation error returned by
// GetEntityLogURLRequest.Validate if the designated constraints aren't met.
type GetEntityLogURLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEntityLogURLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEntityLogURLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEntityLogURLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEntityLogURLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEntityLogURLRequestValidationError) ErrorName() string {
	return "GetEntityLogURLRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEntityLogURLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEntityLogURLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEntityLogURLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEntityLogURLRequestValidationError{}

// Validate checks the field values on NoAdaptedAgentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoAdaptedAgentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoAdaptedAgentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoAdaptedAgentInfoMultiError, or nil if none found.
func (m *NoAdaptedAgentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NoAdaptedAgentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for MachineName

	// no validation rules for OsInfo

	// no validation rules for KernelInfo

	if len(errors) > 0 {
		return NoAdaptedAgentInfoMultiError(errors)
	}

	return nil
}

// NoAdaptedAgentInfoMultiError is an error wrapping multiple validation errors
// returned by NoAdaptedAgentInfo.ValidateAll() if the designated constraints
// aren't met.
type NoAdaptedAgentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoAdaptedAgentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoAdaptedAgentInfoMultiError) AllErrors() []error { return m }

// NoAdaptedAgentInfoValidationError is the validation error returned by
// NoAdaptedAgentInfo.Validate if the designated constraints aren't met.
type NoAdaptedAgentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoAdaptedAgentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoAdaptedAgentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoAdaptedAgentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoAdaptedAgentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoAdaptedAgentInfoValidationError) ErrorName() string {
	return "NoAdaptedAgentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NoAdaptedAgentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoAdaptedAgentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoAdaptedAgentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoAdaptedAgentInfoValidationError{}

// Validate checks the field values on GetGroupRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGroupRequestMultiError, or nil if none found.
func (m *GetGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetGroupRequestMultiError(errors)
	}

	return nil
}

// GetGroupRequestMultiError is an error wrapping multiple validation errors
// returned by GetGroupRequest.ValidateAll() if the designated constraints
// aren't met.
type GetGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroupRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroupRequestMultiError) AllErrors() []error { return m }

// GetGroupRequestValidationError is the validation error returned by
// GetGroupRequest.Validate if the designated constraints aren't met.
type GetGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroupRequestValidationError) ErrorName() string { return "GetGroupRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroupRequestValidationError{}

// Validate checks the field values on LogCollectionType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionTypeMultiError, or nil if none found.
func (m *LogCollectionType) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogCollectionTypeMultiError(errors)
	}

	return nil
}

// LogCollectionTypeMultiError is an error wrapping multiple validation errors
// returned by LogCollectionType.ValidateAll() if the designated constraints
// aren't met.
type LogCollectionTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionTypeMultiError) AllErrors() []error { return m }

// LogCollectionTypeValidationError is the validation error returned by
// LogCollectionType.Validate if the designated constraints aren't met.
type LogCollectionTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionTypeValidationError) ErrorName() string {
	return "LogCollectionTypeValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionTypeValidationError{}

// Validate checks the field values on LogCollectionStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionStatusMultiError, or nil if none found.
func (m *LogCollectionStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogCollectionStatusMultiError(errors)
	}

	return nil
}

// LogCollectionStatusMultiError is an error wrapping multiple validation
// errors returned by LogCollectionStatus.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionStatusMultiError) AllErrors() []error { return m }

// LogCollectionStatusValidationError is the validation error returned by
// LogCollectionStatus.Validate if the designated constraints aren't met.
type LogCollectionStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionStatusValidationError) ErrorName() string {
	return "LogCollectionStatusValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionStatusValidationError{}

// Validate checks the field values on LogCollectionFailure with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionFailure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionFailure with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionFailureMultiError, or nil if none found.
func (m *LogCollectionFailure) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionFailure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogCollectionFailureMultiError(errors)
	}

	return nil
}

// LogCollectionFailureMultiError is an error wrapping multiple validation
// errors returned by LogCollectionFailure.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionFailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionFailureMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionFailureMultiError) AllErrors() []error { return m }

// LogCollectionFailureValidationError is the validation error returned by
// LogCollectionFailure.Validate if the designated constraints aren't met.
type LogCollectionFailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionFailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionFailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionFailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionFailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionFailureValidationError) ErrorName() string {
	return "LogCollectionFailureValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionFailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionFailure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionFailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionFailureValidationError{}

// Validate checks the field values on LogCollectionTask with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionTask with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionTaskMultiError, or nil if none found.
func (m *LogCollectionTask) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for HostId

	// no validation rules for UserId

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for Failure

	// no validation rules for Filename

	// no validation rules for ObjectName

	// no validation rules for ObjectSize

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionTaskValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionTaskValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionTaskValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionTaskValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionTaskValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionTaskValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogCollectionTaskMultiError(errors)
	}

	return nil
}

// LogCollectionTaskMultiError is an error wrapping multiple validation errors
// returned by LogCollectionTask.ValidateAll() if the designated constraints
// aren't met.
type LogCollectionTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionTaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionTaskMultiError) AllErrors() []error { return m }

// LogCollectionTaskValidationError is the validation error returned by
// LogCollectionTask.Validate if the designated constraints aren't met.
type LogCollectionTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionTaskValidationError) ErrorName() string {
	return "LogCollectionTaskValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionTaskValidationError{}

// Validate checks the field values on LogCollectionCreateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionCreateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionCreateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionCreateReqMultiError, or nil if none found.
func (m *LogCollectionCreateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionCreateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionCreateReqValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionCreateReqValidationError{
					field:  "Task",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionCreateReqValidationError{
				field:  "Task",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UploadUrl

	if len(errors) > 0 {
		return LogCollectionCreateReqMultiError(errors)
	}

	return nil
}

// LogCollectionCreateReqMultiError is an error wrapping multiple validation
// errors returned by LogCollectionCreateReq.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionCreateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionCreateReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionCreateReqMultiError) AllErrors() []error { return m }

// LogCollectionCreateReqValidationError is the validation error returned by
// LogCollectionCreateReq.Validate if the designated constraints aren't met.
type LogCollectionCreateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionCreateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionCreateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionCreateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionCreateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionCreateReqValidationError) ErrorName() string {
	return "LogCollectionCreateReqValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionCreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionCreateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionCreateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionCreateReqValidationError{}

// Validate checks the field values on LogCollectionUploadedReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionUploadedReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionUploadedReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionUploadedReqMultiError, or nil if none found.
func (m *LogCollectionUploadedReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionUploadedReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Succeeded

	// no validation rules for Filename

	// no validation rules for ObjectName

	// no validation rules for ObjectSize

	if len(errors) > 0 {
		return LogCollectionUploadedReqMultiError(errors)
	}

	return nil
}

// LogCollectionUploadedReqMultiError is an error wrapping multiple validation
// errors returned by LogCollectionUploadedReq.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionUploadedReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionUploadedReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionUploadedReqMultiError) AllErrors() []error { return m }

// LogCollectionUploadedReqValidationError is the validation error returned by
// LogCollectionUploadedReq.Validate if the designated constraints aren't met.
type LogCollectionUploadedReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionUploadedReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionUploadedReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionUploadedReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionUploadedReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionUploadedReqValidationError) ErrorName() string {
	return "LogCollectionUploadedReqValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionUploadedReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionUploadedReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionUploadedReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionUploadedReqValidationError{}

// Validate checks the field values on LogCollectionListFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionListFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionListFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionListFilterMultiError, or nil if none found.
func (m *LogCollectionListFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionListFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchData

	for idx, item := range m.GetDateRange() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LogCollectionListFilterValidationError{
						field:  fmt.Sprintf("DateRange[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LogCollectionListFilterValidationError{
						field:  fmt.Sprintf("DateRange[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LogCollectionListFilterValidationError{
					field:  fmt.Sprintf("DateRange[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return LogCollectionListFilterMultiError(errors)
	}

	return nil
}

// LogCollectionListFilterMultiError is an error wrapping multiple validation
// errors returned by LogCollectionListFilter.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionListFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionListFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionListFilterMultiError) AllErrors() []error { return m }

// LogCollectionListFilterValidationError is the validation error returned by
// LogCollectionListFilter.Validate if the designated constraints aren't met.
type LogCollectionListFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionListFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionListFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionListFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionListFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionListFilterValidationError) ErrorName() string {
	return "LogCollectionListFilterValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionListFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionListFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionListFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionListFilterValidationError{}

// Validate checks the field values on LogCollectionListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionListReqMultiError, or nil if none found.
func (m *LogCollectionListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionListReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionListReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogCollectionListReqMultiError(errors)
	}

	return nil
}

// LogCollectionListReqMultiError is an error wrapping multiple validation
// errors returned by LogCollectionListReq.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionListReqMultiError) AllErrors() []error { return m }

// LogCollectionListReqValidationError is the validation error returned by
// LogCollectionListReq.Validate if the designated constraints aren't met.
type LogCollectionListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionListReqValidationError) ErrorName() string {
	return "LogCollectionListReqValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionListReqValidationError{}

// Validate checks the field values on LogCollectionListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogCollectionListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogCollectionListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogCollectionListRespMultiError, or nil if none found.
func (m *LogCollectionListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LogCollectionListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LogCollectionListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LogCollectionListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LogCollectionListRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogCollectionListRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogCollectionListRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogCollectionListRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogCollectionListRespMultiError(errors)
	}

	return nil
}

// LogCollectionListRespMultiError is an error wrapping multiple validation
// errors returned by LogCollectionListResp.ValidateAll() if the designated
// constraints aren't met.
type LogCollectionListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogCollectionListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogCollectionListRespMultiError) AllErrors() []error { return m }

// LogCollectionListRespValidationError is the validation error returned by
// LogCollectionListResp.Validate if the designated constraints aren't met.
type LogCollectionListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogCollectionListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogCollectionListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogCollectionListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogCollectionListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogCollectionListRespValidationError) ErrorName() string {
	return "LogCollectionListRespValidationError"
}

// Error satisfies the builtin error interface
func (e LogCollectionListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogCollectionListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogCollectionListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogCollectionListRespValidationError{}
