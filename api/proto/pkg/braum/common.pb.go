// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: braum/common.proto

package braum

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PageInput 分页输入参数
type PageInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *PageInput) Reset() {
	*x = PageInput{}
	mi := &file_braum_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInput) ProtoMessage() {}

func (x *PageInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInput.ProtoReflect.Descriptor instead.
func (*PageInput) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{0}
}

func (x *PageInput) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInput) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// PageOutput 分页输出参数
type PageOutput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page     int32 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total    int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PageOutput) Reset() {
	*x = PageOutput{}
	mi := &file_braum_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageOutput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageOutput) ProtoMessage() {}

func (x *PageOutput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageOutput.ProtoReflect.Descriptor instead.
func (*PageOutput) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{1}
}

func (x *PageOutput) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageOutput) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageOutput) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// Response 通用响应
type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data    []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_braum_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{2}
}

func (x *Response) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Response) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Response) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// EntityFilter 实体过滤条件
type EntityFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OsType        int32    `protobuf:"varint,1,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`                       // 0: 忽略，1: linux, 2: windows
	Online        int32    `protobuf:"varint,2,opt,name=online,proto3" json:"online,omitempty"`                                     // 0: 忽略，1: 在线，2：离线
	SecureStatus  int32    `protobuf:"varint,3,opt,name=secure_status,json=secureStatus,proto3" json:"secure_status,omitempty"`     // 0: 忽略，1: 受攻击, 2：存在风险，3：受攻击&&存在风险, 4: 健康
	GroupIds      []int64  `protobuf:"varint,4,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`          // 组ID集合 (全部: 空，未分组; 0)
	OfflineBegin  int64    `protobuf:"varint,5,opt,name=offline_begin,json=offlineBegin,proto3" json:"offline_begin,omitempty"`     // 起始离线时间
	OfflineEnd    int64    `protobuf:"varint,6,opt,name=offline_end,json=offlineEnd,proto3" json:"offline_end,omitempty"`           // 截止离线时间
	Tags          []string `protobuf:"bytes,7,rep,name=tags,proto3" json:"tags,omitempty"`                                          // 查询指定的标签
	SearchData    string   `protobuf:"bytes,8,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`            // 模糊搜索(主机IP 或 名称)
	Tid           string   `protobuf:"bytes,9,opt,name=tid,proto3" json:"tid,omitempty"`                                            // 终端ID (主机ID或IP)
	Unscoped      bool     `protobuf:"varint,10,opt,name=unscoped,proto3" json:"unscoped,omitempty"`                                // 用 Unscoped
	OrUninstalled bool     `protobuf:"varint,11,opt,name=or_uninstalled,json=orUninstalled,proto3" json:"or_uninstalled,omitempty"` // 在线状态含已卸载
}

func (x *EntityFilter) Reset() {
	*x = EntityFilter{}
	mi := &file_braum_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityFilter) ProtoMessage() {}

func (x *EntityFilter) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityFilter.ProtoReflect.Descriptor instead.
func (*EntityFilter) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{3}
}

func (x *EntityFilter) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *EntityFilter) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *EntityFilter) GetSecureStatus() int32 {
	if x != nil {
		return x.SecureStatus
	}
	return 0
}

func (x *EntityFilter) GetGroupIds() []int64 {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *EntityFilter) GetOfflineBegin() int64 {
	if x != nil {
		return x.OfflineBegin
	}
	return 0
}

func (x *EntityFilter) GetOfflineEnd() int64 {
	if x != nil {
		return x.OfflineEnd
	}
	return 0
}

func (x *EntityFilter) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *EntityFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *EntityFilter) GetTid() string {
	if x != nil {
		return x.Tid
	}
	return ""
}

func (x *EntityFilter) GetUnscoped() bool {
	if x != nil {
		return x.Unscoped
	}
	return false
}

func (x *EntityFilter) GetOrUninstalled() bool {
	if x != nil {
		return x.OrUninstalled
	}
	return false
}

// VersionInfo 版本信息
type VersionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind    int32  `protobuf:"varint,1,opt,name=kind,proto3" json:"kind,omitempty"`      // 版本类别 1: agent, 2: 驱动, 3: 行为库
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"` // 版本号
}

func (x *VersionInfo) Reset() {
	*x = VersionInfo{}
	mi := &file_braum_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VersionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VersionInfo) ProtoMessage() {}

func (x *VersionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VersionInfo.ProtoReflect.Descriptor instead.
func (*VersionInfo) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{4}
}

func (x *VersionInfo) GetKind() int32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

func (x *VersionInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// EntityExtInfo 实体扩展信息
type EntityExtInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetLevel        string `protobuf:"bytes,1,opt,name=asset_level,json=assetLevel,proto3" json:"asset_level,omitempty"`                      // 资产等级
	ResponsiblePerson string `protobuf:"bytes,2,opt,name=responsible_person,json=responsiblePerson,proto3" json:"responsible_person,omitempty"` // 负责人
	Department        string `protobuf:"bytes,3,opt,name=department,proto3" json:"department,omitempty"`                                        // 部门
	Location          string `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`                                            // 位置
	AssetNumber       string `protobuf:"bytes,5,opt,name=asset_number,json=assetNumber,proto3" json:"asset_number,omitempty"`                   // 资产编号
	Note              string `protobuf:"bytes,6,opt,name=note,proto3" json:"note,omitempty"`                                                    // 备注
}

func (x *EntityExtInfo) Reset() {
	*x = EntityExtInfo{}
	mi := &file_braum_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityExtInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityExtInfo) ProtoMessage() {}

func (x *EntityExtInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityExtInfo.ProtoReflect.Descriptor instead.
func (*EntityExtInfo) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{5}
}

func (x *EntityExtInfo) GetAssetLevel() string {
	if x != nil {
		return x.AssetLevel
	}
	return ""
}

func (x *EntityExtInfo) GetResponsiblePerson() string {
	if x != nil {
		return x.ResponsiblePerson
	}
	return ""
}

func (x *EntityExtInfo) GetDepartment() string {
	if x != nil {
		return x.Department
	}
	return ""
}

func (x *EntityExtInfo) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *EntityExtInfo) GetAssetNumber() string {
	if x != nil {
		return x.AssetNumber
	}
	return ""
}

func (x *EntityExtInfo) GetNote() string {
	if x != nil {
		return x.Note
	}
	return ""
}

// ParseFeatureLibPKGInput 解析特征库包输入
type ParseFeatureLibPKGInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BucketName string `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	ObjectName string `protobuf:"bytes,2,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`
	Kind       int32  `protobuf:"varint,3,opt,name=kind,proto3" json:"kind,omitempty"`
}

func (x *ParseFeatureLibPKGInput) Reset() {
	*x = ParseFeatureLibPKGInput{}
	mi := &file_braum_common_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseFeatureLibPKGInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseFeatureLibPKGInput) ProtoMessage() {}

func (x *ParseFeatureLibPKGInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseFeatureLibPKGInput.ProtoReflect.Descriptor instead.
func (*ParseFeatureLibPKGInput) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{6}
}

func (x *ParseFeatureLibPKGInput) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *ParseFeatureLibPKGInput) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *ParseFeatureLibPKGInput) GetKind() int32 {
	if x != nil {
		return x.Kind
	}
	return 0
}

// ParseFeatureLibPKGResponse 解析特征库包响应
type ParseFeatureLibPKGResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId   int64  `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
	Version     string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Size        int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Md5         string `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *ParseFeatureLibPKGResponse) Reset() {
	*x = ParseFeatureLibPKGResponse{}
	mi := &file_braum_common_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParseFeatureLibPKGResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParseFeatureLibPKGResponse) ProtoMessage() {}

func (x *ParseFeatureLibPKGResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParseFeatureLibPKGResponse.ProtoReflect.Descriptor instead.
func (*ParseFeatureLibPKGResponse) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{7}
}

func (x *ParseFeatureLibPKGResponse) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

func (x *ParseFeatureLibPKGResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ParseFeatureLibPKGResponse) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ParseFeatureLibPKGResponse) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ParseFeatureLibPKGResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// RevokeLibPackageRequest 撤回库包请求
type RevokeLibPackageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageId int64 `protobuf:"varint,1,opt,name=package_id,json=packageId,proto3" json:"package_id,omitempty"`
}

func (x *RevokeLibPackageRequest) Reset() {
	*x = RevokeLibPackageRequest{}
	mi := &file_braum_common_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeLibPackageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeLibPackageRequest) ProtoMessage() {}

func (x *RevokeLibPackageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeLibPackageRequest.ProtoReflect.Descriptor instead.
func (*RevokeLibPackageRequest) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{8}
}

func (x *RevokeLibPackageRequest) GetPackageId() int64 {
	if x != nil {
		return x.PackageId
	}
	return 0
}

// LatestLibResponse 最新库响应
type LatestLibResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	Url     string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5     string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
}

func (x *LatestLibResponse) Reset() {
	*x = LatestLibResponse{}
	mi := &file_braum_common_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LatestLibResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LatestLibResponse) ProtoMessage() {}

func (x *LatestLibResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LatestLibResponse.ProtoReflect.Descriptor instead.
func (*LatestLibResponse) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{9}
}

func (x *LatestLibResponse) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LatestLibResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *LatestLibResponse) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

// LibUploadRecord 库包上传记录
type LibUploadRecord struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Version     string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	Size        int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Md5         string `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	Status      int32  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	CreatedAt   int64  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	CreatedBy   string `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
}

func (x *LibUploadRecord) Reset() {
	*x = LibUploadRecord{}
	mi := &file_braum_common_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LibUploadRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LibUploadRecord) ProtoMessage() {}

func (x *LibUploadRecord) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LibUploadRecord.ProtoReflect.Descriptor instead.
func (*LibUploadRecord) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{10}
}

func (x *LibUploadRecord) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LibUploadRecord) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *LibUploadRecord) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *LibUploadRecord) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *LibUploadRecord) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LibUploadRecord) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *LibUploadRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *LibUploadRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

// QueryLibUploadRecordResponse 查询库包上传记录响应
type QueryLibUploadRecordResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  *PageOutput        `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	Items []*LibUploadRecord `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *QueryLibUploadRecordResponse) Reset() {
	*x = QueryLibUploadRecordResponse{}
	mi := &file_braum_common_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryLibUploadRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryLibUploadRecordResponse) ProtoMessage() {}

func (x *QueryLibUploadRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryLibUploadRecordResponse.ProtoReflect.Descriptor instead.
func (*QueryLibUploadRecordResponse) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{11}
}

func (x *QueryLibUploadRecordResponse) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *QueryLibUploadRecordResponse) GetItems() []*LibUploadRecord {
	if x != nil {
		return x.Items
	}
	return nil
}

type Group struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ParentId    int64     `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	Name        string    `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	GroupKvPath []*PathKV `protobuf:"bytes,4,rep,name=group_kv_path,json=groupKvPath,proto3" json:"group_kv_path,omitempty"`
	Sequence    int32     `protobuf:"varint,5,opt,name=sequence,proto3" json:"sequence,omitempty"`
}

func (x *Group) Reset() {
	*x = Group{}
	mi := &file_braum_common_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Group) ProtoMessage() {}

func (x *Group) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Group.ProtoReflect.Descriptor instead.
func (*Group) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{12}
}

func (x *Group) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Group) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *Group) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Group) GetGroupKvPath() []*PathKV {
	if x != nil {
		return x.GroupKvPath
	}
	return nil
}

func (x *Group) GetSequence() int32 {
	if x != nil {
		return x.Sequence
	}
	return 0
}

type PathKV struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *PathKV) Reset() {
	*x = PathKV{}
	mi := &file_braum_common_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PathKV) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PathKV) ProtoMessage() {}

func (x *PathKV) ProtoReflect() protoreflect.Message {
	mi := &file_braum_common_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PathKV.ProtoReflect.Descriptor instead.
func (*PathKV) Descriptor() ([]byte, []int) {
	return file_braum_common_proto_rawDescGZIP(), []int{13}
}

func (x *PathKV) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PathKV) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_braum_common_proto protoreflect.FileDescriptor

var file_braum_common_proto_rawDesc = []byte{
	0x0a, 0x12, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x22, 0x3c, 0x0a, 0x09, 0x50,
	0x61, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x53, 0x0a, 0x0a, 0x50, 0x61, 0x67,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x4c,
	0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd1, 0x02, 0x0a,
	0x0c, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x65, 0x67, 0x69,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x42, 0x65, 0x67, 0x69, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x45, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x74,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x69, 0x64, 0x12, 0x1a, 0x0a,
	0x08, 0x75, 0x6e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x75, 0x6e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x72, 0x5f,
	0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0d, 0x6f, 0x72, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64,
	0x22, 0x3b, 0x0a, 0x0b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xd2, 0x01,
	0x0a, 0x0d, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x2d, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x70, 0x61, 0x72, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x74, 0x65, 0x22, 0x6f, 0x0a, 0x17, 0x50, 0x61, 0x72, 0x73, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x4c, 0x69, 0x62, 0x50, 0x4b, 0x47, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6b,
	0x69, 0x6e, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x1a, 0x50, 0x61, 0x72, 0x73, 0x65, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x62, 0x50, 0x4b, 0x47, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49,
	0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64,
	0x35, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x38, 0x0a, 0x17, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x4c, 0x69, 0x62,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x51, 0x0a,
	0x11, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4c, 0x69, 0x62, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35,
	0x22, 0xd9, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x22, 0x73, 0x0a, 0x1c,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x69, 0x62, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0x97, 0x01, 0x0a, 0x05, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x0d,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6b, 0x76, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x74, 0x68,
	0x4b, 0x56, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4b, 0x76, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x22, 0x2c, 0x0a, 0x06, 0x50,
	0x61, 0x74, 0x68, 0x4b, 0x56, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x12, 0x5a, 0x10, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_braum_common_proto_rawDescOnce sync.Once
	file_braum_common_proto_rawDescData = file_braum_common_proto_rawDesc
)

func file_braum_common_proto_rawDescGZIP() []byte {
	file_braum_common_proto_rawDescOnce.Do(func() {
		file_braum_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_braum_common_proto_rawDescData)
	})
	return file_braum_common_proto_rawDescData
}

var file_braum_common_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_braum_common_proto_goTypes = []any{
	(*PageInput)(nil),                    // 0: braum.PageInput
	(*PageOutput)(nil),                   // 1: braum.PageOutput
	(*Response)(nil),                     // 2: braum.Response
	(*EntityFilter)(nil),                 // 3: braum.EntityFilter
	(*VersionInfo)(nil),                  // 4: braum.VersionInfo
	(*EntityExtInfo)(nil),                // 5: braum.EntityExtInfo
	(*ParseFeatureLibPKGInput)(nil),      // 6: braum.ParseFeatureLibPKGInput
	(*ParseFeatureLibPKGResponse)(nil),   // 7: braum.ParseFeatureLibPKGResponse
	(*RevokeLibPackageRequest)(nil),      // 8: braum.RevokeLibPackageRequest
	(*LatestLibResponse)(nil),            // 9: braum.LatestLibResponse
	(*LibUploadRecord)(nil),              // 10: braum.LibUploadRecord
	(*QueryLibUploadRecordResponse)(nil), // 11: braum.QueryLibUploadRecordResponse
	(*Group)(nil),                        // 12: braum.Group
	(*PathKV)(nil),                       // 13: braum.PathKV
}
var file_braum_common_proto_depIdxs = []int32{
	1,  // 0: braum.QueryLibUploadRecordResponse.page:type_name -> braum.PageOutput
	10, // 1: braum.QueryLibUploadRecordResponse.items:type_name -> braum.LibUploadRecord
	13, // 2: braum.Group.group_kv_path:type_name -> braum.PathKV
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_braum_common_proto_init() }
func file_braum_common_proto_init() {
	if File_braum_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_braum_common_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_braum_common_proto_goTypes,
		DependencyIndexes: file_braum_common_proto_depIdxs,
		MessageInfos:      file_braum_common_proto_msgTypes,
	}.Build()
	File_braum_common_proto = out.File
	file_braum_common_proto_rawDesc = nil
	file_braum_common_proto_goTypes = nil
	file_braum_common_proto_depIdxs = nil
}
