// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: braum/common.proto

package braum

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PageInput with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageInput with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageInputMultiError, or nil
// if none found.
func (m *PageInput) ValidateAll() error {
	return m.validate(true)
}

func (m *PageInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	if len(errors) > 0 {
		return PageInputMultiError(errors)
	}

	return nil
}

// PageInputMultiError is an error wrapping multiple validation errors returned
// by PageInput.ValidateAll() if the designated constraints aren't met.
type PageInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageInputMultiError) AllErrors() []error { return m }

// PageInputValidationError is the validation error returned by
// PageInput.Validate if the designated constraints aren't met.
type PageInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageInputValidationError) ErrorName() string { return "PageInputValidationError" }

// Error satisfies the builtin error interface
func (e PageInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageInputValidationError{}

// Validate checks the field values on PageOutput with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageOutput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageOutput with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageOutputMultiError, or
// nil if none found.
func (m *PageOutput) ValidateAll() error {
	return m.validate(true)
}

func (m *PageOutput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Page

	// no validation rules for PageSize

	// no validation rules for Total

	if len(errors) > 0 {
		return PageOutputMultiError(errors)
	}

	return nil
}

// PageOutputMultiError is an error wrapping multiple validation errors
// returned by PageOutput.ValidateAll() if the designated constraints aren't met.
type PageOutputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageOutputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageOutputMultiError) AllErrors() []error { return m }

// PageOutputValidationError is the validation error returned by
// PageOutput.Validate if the designated constraints aren't met.
type PageOutputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageOutputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageOutputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageOutputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageOutputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageOutputValidationError) ErrorName() string { return "PageOutputValidationError" }

// Error satisfies the builtin error interface
func (e PageOutputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageOutput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageOutputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageOutputValidationError{}

// Validate checks the field values on Response with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Response with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResponseMultiError, or nil
// if none found.
func (m *Response) ValidateAll() error {
	return m.validate(true)
}

func (m *Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Message

	// no validation rules for Data

	if len(errors) > 0 {
		return ResponseMultiError(errors)
	}

	return nil
}

// ResponseMultiError is an error wrapping multiple validation errors returned
// by Response.ValidateAll() if the designated constraints aren't met.
type ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResponseMultiError) AllErrors() []error { return m }

// ResponseValidationError is the validation error returned by
// Response.Validate if the designated constraints aren't met.
type ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResponseValidationError) ErrorName() string { return "ResponseValidationError" }

// Error satisfies the builtin error interface
func (e ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResponseValidationError{}

// Validate checks the field values on EntityFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityFilterMultiError, or
// nil if none found.
func (m *EntityFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OsType

	// no validation rules for Online

	// no validation rules for SecureStatus

	// no validation rules for OfflineBegin

	// no validation rules for OfflineEnd

	// no validation rules for SearchData

	// no validation rules for Tid

	// no validation rules for Unscoped

	// no validation rules for OrUninstalled

	if len(errors) > 0 {
		return EntityFilterMultiError(errors)
	}

	return nil
}

// EntityFilterMultiError is an error wrapping multiple validation errors
// returned by EntityFilter.ValidateAll() if the designated constraints aren't met.
type EntityFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityFilterMultiError) AllErrors() []error { return m }

// EntityFilterValidationError is the validation error returned by
// EntityFilter.Validate if the designated constraints aren't met.
type EntityFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityFilterValidationError) ErrorName() string { return "EntityFilterValidationError" }

// Error satisfies the builtin error interface
func (e EntityFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityFilterValidationError{}

// Validate checks the field values on VersionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VersionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VersionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VersionInfoMultiError, or
// nil if none found.
func (m *VersionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VersionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Version

	if len(errors) > 0 {
		return VersionInfoMultiError(errors)
	}

	return nil
}

// VersionInfoMultiError is an error wrapping multiple validation errors
// returned by VersionInfo.ValidateAll() if the designated constraints aren't met.
type VersionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VersionInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VersionInfoMultiError) AllErrors() []error { return m }

// VersionInfoValidationError is the validation error returned by
// VersionInfo.Validate if the designated constraints aren't met.
type VersionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VersionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VersionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VersionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VersionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VersionInfoValidationError) ErrorName() string { return "VersionInfoValidationError" }

// Error satisfies the builtin error interface
func (e VersionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVersionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VersionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VersionInfoValidationError{}

// Validate checks the field values on EntityExtInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EntityExtInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EntityExtInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EntityExtInfoMultiError, or
// nil if none found.
func (m *EntityExtInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EntityExtInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetLevel

	// no validation rules for ResponsiblePerson

	// no validation rules for Department

	// no validation rules for Location

	// no validation rules for AssetNumber

	// no validation rules for Note

	if len(errors) > 0 {
		return EntityExtInfoMultiError(errors)
	}

	return nil
}

// EntityExtInfoMultiError is an error wrapping multiple validation errors
// returned by EntityExtInfo.ValidateAll() if the designated constraints
// aren't met.
type EntityExtInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EntityExtInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EntityExtInfoMultiError) AllErrors() []error { return m }

// EntityExtInfoValidationError is the validation error returned by
// EntityExtInfo.Validate if the designated constraints aren't met.
type EntityExtInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EntityExtInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EntityExtInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EntityExtInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EntityExtInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EntityExtInfoValidationError) ErrorName() string { return "EntityExtInfoValidationError" }

// Error satisfies the builtin error interface
func (e EntityExtInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEntityExtInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EntityExtInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EntityExtInfoValidationError{}

// Validate checks the field values on ParseFeatureLibPKGInput with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParseFeatureLibPKGInput) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParseFeatureLibPKGInput with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParseFeatureLibPKGInputMultiError, or nil if none found.
func (m *ParseFeatureLibPKGInput) ValidateAll() error {
	return m.validate(true)
}

func (m *ParseFeatureLibPKGInput) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BucketName

	// no validation rules for ObjectName

	// no validation rules for Kind

	if len(errors) > 0 {
		return ParseFeatureLibPKGInputMultiError(errors)
	}

	return nil
}

// ParseFeatureLibPKGInputMultiError is an error wrapping multiple validation
// errors returned by ParseFeatureLibPKGInput.ValidateAll() if the designated
// constraints aren't met.
type ParseFeatureLibPKGInputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParseFeatureLibPKGInputMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParseFeatureLibPKGInputMultiError) AllErrors() []error { return m }

// ParseFeatureLibPKGInputValidationError is the validation error returned by
// ParseFeatureLibPKGInput.Validate if the designated constraints aren't met.
type ParseFeatureLibPKGInputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParseFeatureLibPKGInputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParseFeatureLibPKGInputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParseFeatureLibPKGInputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParseFeatureLibPKGInputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParseFeatureLibPKGInputValidationError) ErrorName() string {
	return "ParseFeatureLibPKGInputValidationError"
}

// Error satisfies the builtin error interface
func (e ParseFeatureLibPKGInputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParseFeatureLibPKGInput.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParseFeatureLibPKGInputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParseFeatureLibPKGInputValidationError{}

// Validate checks the field values on ParseFeatureLibPKGResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ParseFeatureLibPKGResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ParseFeatureLibPKGResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ParseFeatureLibPKGResponseMultiError, or nil if none found.
func (m *ParseFeatureLibPKGResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ParseFeatureLibPKGResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PackageId

	// no validation rules for Version

	// no validation rules for Size

	// no validation rules for Md5

	// no validation rules for Description

	if len(errors) > 0 {
		return ParseFeatureLibPKGResponseMultiError(errors)
	}

	return nil
}

// ParseFeatureLibPKGResponseMultiError is an error wrapping multiple
// validation errors returned by ParseFeatureLibPKGResponse.ValidateAll() if
// the designated constraints aren't met.
type ParseFeatureLibPKGResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ParseFeatureLibPKGResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ParseFeatureLibPKGResponseMultiError) AllErrors() []error { return m }

// ParseFeatureLibPKGResponseValidationError is the validation error returned
// by ParseFeatureLibPKGResponse.Validate if the designated constraints aren't met.
type ParseFeatureLibPKGResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ParseFeatureLibPKGResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ParseFeatureLibPKGResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ParseFeatureLibPKGResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ParseFeatureLibPKGResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ParseFeatureLibPKGResponseValidationError) ErrorName() string {
	return "ParseFeatureLibPKGResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ParseFeatureLibPKGResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sParseFeatureLibPKGResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ParseFeatureLibPKGResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ParseFeatureLibPKGResponseValidationError{}

// Validate checks the field values on RevokeLibPackageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RevokeLibPackageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RevokeLibPackageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RevokeLibPackageRequestMultiError, or nil if none found.
func (m *RevokeLibPackageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RevokeLibPackageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PackageId

	if len(errors) > 0 {
		return RevokeLibPackageRequestMultiError(errors)
	}

	return nil
}

// RevokeLibPackageRequestMultiError is an error wrapping multiple validation
// errors returned by RevokeLibPackageRequest.ValidateAll() if the designated
// constraints aren't met.
type RevokeLibPackageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RevokeLibPackageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RevokeLibPackageRequestMultiError) AllErrors() []error { return m }

// RevokeLibPackageRequestValidationError is the validation error returned by
// RevokeLibPackageRequest.Validate if the designated constraints aren't met.
type RevokeLibPackageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RevokeLibPackageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RevokeLibPackageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RevokeLibPackageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RevokeLibPackageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RevokeLibPackageRequestValidationError) ErrorName() string {
	return "RevokeLibPackageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RevokeLibPackageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRevokeLibPackageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RevokeLibPackageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RevokeLibPackageRequestValidationError{}

// Validate checks the field values on LatestLibResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LatestLibResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LatestLibResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LatestLibResponseMultiError, or nil if none found.
func (m *LatestLibResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LatestLibResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for Url

	// no validation rules for Md5

	if len(errors) > 0 {
		return LatestLibResponseMultiError(errors)
	}

	return nil
}

// LatestLibResponseMultiError is an error wrapping multiple validation errors
// returned by LatestLibResponse.ValidateAll() if the designated constraints
// aren't met.
type LatestLibResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LatestLibResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LatestLibResponseMultiError) AllErrors() []error { return m }

// LatestLibResponseValidationError is the validation error returned by
// LatestLibResponse.Validate if the designated constraints aren't met.
type LatestLibResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LatestLibResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LatestLibResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LatestLibResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LatestLibResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LatestLibResponseValidationError) ErrorName() string {
	return "LatestLibResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LatestLibResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLatestLibResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LatestLibResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LatestLibResponseValidationError{}

// Validate checks the field values on LibUploadRecord with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LibUploadRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LibUploadRecord with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LibUploadRecordMultiError, or nil if none found.
func (m *LibUploadRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *LibUploadRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Version

	// no validation rules for Size

	// no validation rules for Md5

	// no validation rules for Description

	// no validation rules for Status

	// no validation rules for CreatedAt

	// no validation rules for CreatedBy

	if len(errors) > 0 {
		return LibUploadRecordMultiError(errors)
	}

	return nil
}

// LibUploadRecordMultiError is an error wrapping multiple validation errors
// returned by LibUploadRecord.ValidateAll() if the designated constraints
// aren't met.
type LibUploadRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LibUploadRecordMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LibUploadRecordMultiError) AllErrors() []error { return m }

// LibUploadRecordValidationError is the validation error returned by
// LibUploadRecord.Validate if the designated constraints aren't met.
type LibUploadRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LibUploadRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LibUploadRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LibUploadRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LibUploadRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LibUploadRecordValidationError) ErrorName() string { return "LibUploadRecordValidationError" }

// Error satisfies the builtin error interface
func (e LibUploadRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLibUploadRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LibUploadRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LibUploadRecordValidationError{}

// Validate checks the field values on QueryLibUploadRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryLibUploadRecordResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryLibUploadRecordResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryLibUploadRecordResponseMultiError, or nil if none found.
func (m *QueryLibUploadRecordResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryLibUploadRecordResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryLibUploadRecordResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryLibUploadRecordResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryLibUploadRecordResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryLibUploadRecordResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryLibUploadRecordResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryLibUploadRecordResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryLibUploadRecordResponseMultiError(errors)
	}

	return nil
}

// QueryLibUploadRecordResponseMultiError is an error wrapping multiple
// validation errors returned by QueryLibUploadRecordResponse.ValidateAll() if
// the designated constraints aren't met.
type QueryLibUploadRecordResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryLibUploadRecordResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryLibUploadRecordResponseMultiError) AllErrors() []error { return m }

// QueryLibUploadRecordResponseValidationError is the validation error returned
// by QueryLibUploadRecordResponse.Validate if the designated constraints
// aren't met.
type QueryLibUploadRecordResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryLibUploadRecordResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryLibUploadRecordResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryLibUploadRecordResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryLibUploadRecordResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryLibUploadRecordResponseValidationError) ErrorName() string {
	return "QueryLibUploadRecordResponseValidationError"
}

// Error satisfies the builtin error interface
func (e QueryLibUploadRecordResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryLibUploadRecordResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryLibUploadRecordResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryLibUploadRecordResponseValidationError{}

// Validate checks the field values on Group with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Group) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Group with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GroupMultiError, or nil if none found.
func (m *Group) ValidateAll() error {
	return m.validate(true)
}

func (m *Group) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ParentId

	// no validation rules for Name

	for idx, item := range m.GetGroupKvPath() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GroupValidationError{
						field:  fmt.Sprintf("GroupKvPath[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GroupValidationError{
						field:  fmt.Sprintf("GroupKvPath[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GroupValidationError{
					field:  fmt.Sprintf("GroupKvPath[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Sequence

	if len(errors) > 0 {
		return GroupMultiError(errors)
	}

	return nil
}

// GroupMultiError is an error wrapping multiple validation errors returned by
// Group.ValidateAll() if the designated constraints aren't met.
type GroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroupMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroupMultiError) AllErrors() []error { return m }

// GroupValidationError is the validation error returned by Group.Validate if
// the designated constraints aren't met.
type GroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroupValidationError) ErrorName() string { return "GroupValidationError" }

// Error satisfies the builtin error interface
func (e GroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroupValidationError{}

// Validate checks the field values on PathKV with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PathKV) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PathKV with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PathKVMultiError, or nil if none found.
func (m *PathKV) ValidateAll() error {
	return m.validate(true)
}

func (m *PathKV) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return PathKVMultiError(errors)
	}

	return nil
}

// PathKVMultiError is an error wrapping multiple validation errors returned by
// PathKV.ValidateAll() if the designated constraints aren't met.
type PathKVMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PathKVMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PathKVMultiError) AllErrors() []error { return m }

// PathKVValidationError is the validation error returned by PathKV.Validate if
// the designated constraints aren't met.
type PathKVValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PathKVValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PathKVValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PathKVValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PathKVValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PathKVValidationError) ErrorName() string { return "PathKVValidationError" }

// Error satisfies the builtin error interface
func (e PathKVValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPathKV.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PathKVValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PathKVValidationError{}
