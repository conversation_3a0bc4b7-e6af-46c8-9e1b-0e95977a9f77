// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: braum/entity.proto

package braum

import (
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LogCollectionType_Enum int32

const (
	LogCollectionType_Unknown      LogCollectionType_Enum = 0
	LogCollectionType_SysDump      LogCollectionType_Enum = 1 // 系统 dump 日志
	LogCollectionType_AgentDump    LogCollectionType_Enum = 2 // Agent dump 日志
	LogCollectionType_SysEvent     LogCollectionType_Enum = 3 // 系统事件日志
	LogCollectionType_AgentRunning LogCollectionType_Enum = 4 // 系统运行日志
)

// Enum value maps for LogCollectionType_Enum.
var (
	LogCollectionType_Enum_name = map[int32]string{
		0: "Unknown",
		1: "SysDump",
		2: "AgentDump",
		3: "SysEvent",
		4: "AgentRunning",
	}
	LogCollectionType_Enum_value = map[string]int32{
		"Unknown":      0,
		"SysDump":      1,
		"AgentDump":    2,
		"SysEvent":     3,
		"AgentRunning": 4,
	}
)

func (x LogCollectionType_Enum) Enum() *LogCollectionType_Enum {
	p := new(LogCollectionType_Enum)
	*p = x
	return p
}

func (x LogCollectionType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogCollectionType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_braum_entity_proto_enumTypes[0].Descriptor()
}

func (LogCollectionType_Enum) Type() protoreflect.EnumType {
	return &file_braum_entity_proto_enumTypes[0]
}

func (x LogCollectionType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogCollectionType_Enum.Descriptor instead.
func (LogCollectionType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{46, 0}
}

type LogCollectionStatus_Enum int32

const (
	LogCollectionStatus_Unknown    LogCollectionStatus_Enum = 0
	LogCollectionStatus_Collecting LogCollectionStatus_Enum = 1 // 收集中
	LogCollectionStatus_Collected  LogCollectionStatus_Enum = 2 // 收集成功
	LogCollectionStatus_Failed     LogCollectionStatus_Enum = 3 // 收集失败
)

// Enum value maps for LogCollectionStatus_Enum.
var (
	LogCollectionStatus_Enum_name = map[int32]string{
		0: "Unknown",
		1: "Collecting",
		2: "Collected",
		3: "Failed",
	}
	LogCollectionStatus_Enum_value = map[string]int32{
		"Unknown":    0,
		"Collecting": 1,
		"Collected":  2,
		"Failed":     3,
	}
)

func (x LogCollectionStatus_Enum) Enum() *LogCollectionStatus_Enum {
	p := new(LogCollectionStatus_Enum)
	*p = x
	return p
}

func (x LogCollectionStatus_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogCollectionStatus_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_braum_entity_proto_enumTypes[1].Descriptor()
}

func (LogCollectionStatus_Enum) Type() protoreflect.EnumType {
	return &file_braum_entity_proto_enumTypes[1]
}

func (x LogCollectionStatus_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogCollectionStatus_Enum.Descriptor instead.
func (LogCollectionStatus_Enum) EnumDescriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{47, 0}
}

type LogCollectionFailure_Enum int32

const (
	LogCollectionFailure_Unknown      LogCollectionFailure_Enum = 0
	LogCollectionFailure_Timeout      LogCollectionFailure_Enum = 1 // 任务超时
	LogCollectionFailure_FileNotExist LogCollectionFailure_Enum = 2 // 文件不存在
	LogCollectionFailure_UploadFailed LogCollectionFailure_Enum = 3 // 上传失败
	LogCollectionFailure_HostOffline  LogCollectionFailure_Enum = 4 // 主机离线
)

// Enum value maps for LogCollectionFailure_Enum.
var (
	LogCollectionFailure_Enum_name = map[int32]string{
		0: "Unknown",
		1: "Timeout",
		2: "FileNotExist",
		3: "UploadFailed",
		4: "HostOffline",
	}
	LogCollectionFailure_Enum_value = map[string]int32{
		"Unknown":      0,
		"Timeout":      1,
		"FileNotExist": 2,
		"UploadFailed": 3,
		"HostOffline":  4,
	}
)

func (x LogCollectionFailure_Enum) Enum() *LogCollectionFailure_Enum {
	p := new(LogCollectionFailure_Enum)
	*p = x
	return p
}

func (x LogCollectionFailure_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogCollectionFailure_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_braum_entity_proto_enumTypes[2].Descriptor()
}

func (LogCollectionFailure_Enum) Type() protoreflect.EnumType {
	return &file_braum_entity_proto_enumTypes[2]
}

func (x LogCollectionFailure_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogCollectionFailure_Enum.Descriptor instead.
func (LogCollectionFailure_Enum) EnumDescriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{48, 0}
}

// OnOffInfo 主机上下线信息
type OnOffInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	GroupId   uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Ip        string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Online    int32  `protobuf:"varint,4,opt,name=online,proto3" json:"online,omitempty"`
	OsType    int32  `protobuf:"varint,5,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
}

func (x *OnOffInfo) Reset() {
	*x = OnOffInfo{}
	mi := &file_braum_entity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OnOffInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnOffInfo) ProtoMessage() {}

func (x *OnOffInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnOffInfo.ProtoReflect.Descriptor instead.
func (*OnOffInfo) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{0}
}

func (x *OnOffInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *OnOffInfo) GetGroupId() uint32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *OnOffInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *OnOffInfo) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *OnOffInfo) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

// EntityInfo 主机基本信息
type EntityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId     string `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	MachineName   string `protobuf:"bytes,3,opt,name=machine_name,json=machineName,proto3" json:"machine_name,omitempty"`
	OsType        int32  `protobuf:"varint,4,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	Ip            string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	Os            string `protobuf:"bytes,6,opt,name=os,proto3" json:"os,omitempty"`
	KernelVersion string `protobuf:"bytes,7,opt,name=kernel_version,json=kernelVersion,proto3" json:"kernel_version,omitempty"`
	Online        int32  `protobuf:"varint,8,opt,name=online,proto3" json:"online,omitempty"`
	GroupId       int64  `protobuf:"varint,9,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Tag           string `protobuf:"bytes,10,opt,name=tag,proto3" json:"tag,omitempty"`
	LastOnlineAt  int64  `protobuf:"varint,11,opt,name=last_online_at,json=lastOnlineAt,proto3" json:"last_online_at,omitempty"`
	LastOfflineAt int64  `protobuf:"varint,12,opt,name=last_offline_at,json=lastOfflineAt,proto3" json:"last_offline_at,omitempty"`
	CreatedAt     int64  `protobuf:"varint,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64  `protobuf:"varint,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt     int64  `protobuf:"varint,15,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	GroupName     string `protobuf:"bytes,16,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Arch          string `protobuf:"bytes,17,opt,name=arch,proto3" json:"arch,omitempty"`
	OsInfoShort   string `protobuf:"bytes,18,opt,name=os_info_short,json=osInfoShort,proto3" json:"os_info_short,omitempty"`
	MacAddress    string `protobuf:"bytes,19,opt,name=mac_address,json=macAddress,proto3" json:"mac_address,omitempty"`
}

func (x *EntityInfo) Reset() {
	*x = EntityInfo{}
	mi := &file_braum_entity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInfo) ProtoMessage() {}

func (x *EntityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInfo.ProtoReflect.Descriptor instead.
func (*EntityInfo) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{1}
}

func (x *EntityInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EntityInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntityInfo) GetMachineName() string {
	if x != nil {
		return x.MachineName
	}
	return ""
}

func (x *EntityInfo) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *EntityInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *EntityInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *EntityInfo) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

func (x *EntityInfo) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *EntityInfo) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *EntityInfo) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *EntityInfo) GetLastOnlineAt() int64 {
	if x != nil {
		return x.LastOnlineAt
	}
	return 0
}

func (x *EntityInfo) GetLastOfflineAt() int64 {
	if x != nil {
		return x.LastOfflineAt
	}
	return 0
}

func (x *EntityInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *EntityInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *EntityInfo) GetDeletedAt() int64 {
	if x != nil {
		return x.DeletedAt
	}
	return 0
}

func (x *EntityInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *EntityInfo) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *EntityInfo) GetOsInfoShort() string {
	if x != nil {
		return x.OsInfoShort
	}
	return ""
}

func (x *EntityInfo) GetMacAddress() string {
	if x != nil {
		return x.MacAddress
	}
	return ""
}

// EntityList 主机列表
type EntityList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  *PageOutput   `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	Items []*EntityInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *EntityList) Reset() {
	*x = EntityList{}
	mi := &file_braum_entity_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityList) ProtoMessage() {}

func (x *EntityList) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityList.ProtoReflect.Descriptor instead.
func (*EntityList) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{2}
}

func (x *EntityList) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *EntityList) GetItems() []*EntityInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

// EntitybaseInput 用于写入
type EntitybaseInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId     string         `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Ip            string         `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	OsType        int32          `protobuf:"varint,3,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	Tag           string         `protobuf:"bytes,4,opt,name=tag,proto3" json:"tag,omitempty"`
	GroupId       int64          `protobuf:"varint,5,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MachineName   string         `protobuf:"bytes,6,opt,name=machine_name,json=machineName,proto3" json:"machine_name,omitempty"`
	Os            string         `protobuf:"bytes,7,opt,name=os,proto3" json:"os,omitempty"`
	KernelInfo    string         `protobuf:"bytes,8,opt,name=kernel_info,json=kernelInfo,proto3" json:"kernel_info,omitempty"`
	OsInfoLong    string         `protobuf:"bytes,9,opt,name=os_info_long,json=osInfoLong,proto3" json:"os_info_long,omitempty"`
	OsInfoShort   string         `protobuf:"bytes,10,opt,name=os_info_short,json=osInfoShort,proto3" json:"os_info_short,omitempty"`
	ProxyIpPort   string         `protobuf:"bytes,11,opt,name=proxy_ip_port,json=proxyIpPort,proto3" json:"proxy_ip_port,omitempty"`
	VersionSlice  []*VersionInfo `protobuf:"bytes,12,rep,name=version_slice,json=versionSlice,proto3" json:"version_slice,omitempty"`
	Arch          string         `protobuf:"bytes,13,opt,name=arch,proto3" json:"arch,omitempty"`
	OsInfoDisplay string         `protobuf:"bytes,14,opt,name=os_info_display,json=osInfoDisplay,proto3" json:"os_info_display,omitempty"`
	Mac           string         `protobuf:"bytes,15,opt,name=mac,proto3" json:"mac,omitempty"`
	Gateway       string         `protobuf:"bytes,16,opt,name=gateway,proto3" json:"gateway,omitempty"`
	LatestUser    string         `protobuf:"bytes,17,opt,name=latest_user,json=latestUser,proto3" json:"latest_user,omitempty"`
	CurrUser      string         `protobuf:"bytes,18,opt,name=curr_user,json=currUser,proto3" json:"curr_user,omitempty"`
	DomainUser    string         `protobuf:"bytes,19,opt,name=domain_user,json=domainUser,proto3" json:"domain_user,omitempty"`
	CurrFlag      []bool         `protobuf:"varint,20,rep,packed,name=curr_flag,json=currFlag,proto3" json:"curr_flag,omitempty"`
}

func (x *EntitybaseInput) Reset() {
	*x = EntitybaseInput{}
	mi := &file_braum_entity_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntitybaseInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntitybaseInput) ProtoMessage() {}

func (x *EntitybaseInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntitybaseInput.ProtoReflect.Descriptor instead.
func (*EntitybaseInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{3}
}

func (x *EntitybaseInput) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntitybaseInput) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *EntitybaseInput) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *EntitybaseInput) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *EntitybaseInput) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *EntitybaseInput) GetMachineName() string {
	if x != nil {
		return x.MachineName
	}
	return ""
}

func (x *EntitybaseInput) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *EntitybaseInput) GetKernelInfo() string {
	if x != nil {
		return x.KernelInfo
	}
	return ""
}

func (x *EntitybaseInput) GetOsInfoLong() string {
	if x != nil {
		return x.OsInfoLong
	}
	return ""
}

func (x *EntitybaseInput) GetOsInfoShort() string {
	if x != nil {
		return x.OsInfoShort
	}
	return ""
}

func (x *EntitybaseInput) GetProxyIpPort() string {
	if x != nil {
		return x.ProxyIpPort
	}
	return ""
}

func (x *EntitybaseInput) GetVersionSlice() []*VersionInfo {
	if x != nil {
		return x.VersionSlice
	}
	return nil
}

func (x *EntitybaseInput) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *EntitybaseInput) GetOsInfoDisplay() string {
	if x != nil {
		return x.OsInfoDisplay
	}
	return ""
}

func (x *EntitybaseInput) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *EntitybaseInput) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *EntitybaseInput) GetLatestUser() string {
	if x != nil {
		return x.LatestUser
	}
	return ""
}

func (x *EntitybaseInput) GetCurrUser() string {
	if x != nil {
		return x.CurrUser
	}
	return ""
}

func (x *EntitybaseInput) GetDomainUser() string {
	if x != nil {
		return x.DomainUser
	}
	return ""
}

func (x *EntitybaseInput) GetCurrFlag() []bool {
	if x != nil {
		return x.CurrFlag
	}
	return nil
}

// EntityStatusStatistic 资产统计信息
type EntityStatusStatistic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total            int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	HealthyCount     int32 `protobuf:"varint,2,opt,name=healthy_count,json=healthyCount,proto3" json:"healthy_count,omitempty"`
	RiskCount        int32 `protobuf:"varint,3,opt,name=risk_count,json=riskCount,proto3" json:"risk_count,omitempty"`
	AttackedCount    int32 `protobuf:"varint,4,opt,name=attacked_count,json=attackedCount,proto3" json:"attacked_count,omitempty"`
	OnlineCount      int32 `protobuf:"varint,5,opt,name=online_count,json=onlineCount,proto3" json:"online_count,omitempty"`
	OfflineCount     int32 `protobuf:"varint,6,opt,name=offline_count,json=offlineCount,proto3" json:"offline_count,omitempty"`
	UninstalledCount int32 `protobuf:"varint,7,opt,name=uninstalled_count,json=uninstalledCount,proto3" json:"uninstalled_count,omitempty"`
	WindowsCount     int32 `protobuf:"varint,8,opt,name=windows_count,json=windowsCount,proto3" json:"windows_count,omitempty"`
	LinuxCount       int32 `protobuf:"varint,9,opt,name=linux_count,json=linuxCount,proto3" json:"linux_count,omitempty"`
}

func (x *EntityStatusStatistic) Reset() {
	*x = EntityStatusStatistic{}
	mi := &file_braum_entity_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityStatusStatistic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityStatusStatistic) ProtoMessage() {}

func (x *EntityStatusStatistic) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityStatusStatistic.ProtoReflect.Descriptor instead.
func (*EntityStatusStatistic) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{4}
}

func (x *EntityStatusStatistic) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *EntityStatusStatistic) GetHealthyCount() int32 {
	if x != nil {
		return x.HealthyCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetRiskCount() int32 {
	if x != nil {
		return x.RiskCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetAttackedCount() int32 {
	if x != nil {
		return x.AttackedCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetOnlineCount() int32 {
	if x != nil {
		return x.OnlineCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetOfflineCount() int32 {
	if x != nil {
		return x.OfflineCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetUninstalledCount() int32 {
	if x != nil {
		return x.UninstalledCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetWindowsCount() int32 {
	if x != nil {
		return x.WindowsCount
	}
	return 0
}

func (x *EntityStatusStatistic) GetLinuxCount() int32 {
	if x != nil {
		return x.LinuxCount
	}
	return 0
}

// AddTagInput 添加标签输入
type AddTagInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Tag       string `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *AddTagInput) Reset() {
	*x = AddTagInput{}
	mi := &file_braum_entity_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTagInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTagInput) ProtoMessage() {}

func (x *AddTagInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTagInput.ProtoReflect.Descriptor instead.
func (*AddTagInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{5}
}

func (x *AddTagInput) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AddTagInput) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

// EntityMoveToGroupInput 按条件移动主机到指定分组
type EntityMoveToGroupInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId int64         `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	Filter  *EntityFilter `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	Exclude []string      `protobuf:"bytes,3,rep,name=exclude,proto3" json:"exclude,omitempty"`
}

func (x *EntityMoveToGroupInput) Reset() {
	*x = EntityMoveToGroupInput{}
	mi := &file_braum_entity_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityMoveToGroupInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityMoveToGroupInput) ProtoMessage() {}

func (x *EntityMoveToGroupInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityMoveToGroupInput.ProtoReflect.Descriptor instead.
func (*EntityMoveToGroupInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{6}
}

func (x *EntityMoveToGroupInput) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *EntityMoveToGroupInput) GetFilter() *EntityFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *EntityMoveToGroupInput) GetExclude() []string {
	if x != nil {
		return x.Exclude
	}
	return nil
}

// MultiEntityMoveToGroupInput 将多个主机移动到指定分组
type MultiEntityMoveToGroupInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId    int64    `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MachineIds []string `protobuf:"bytes,2,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
}

func (x *MultiEntityMoveToGroupInput) Reset() {
	*x = MultiEntityMoveToGroupInput{}
	mi := &file_braum_entity_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MultiEntityMoveToGroupInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiEntityMoveToGroupInput) ProtoMessage() {}

func (x *MultiEntityMoveToGroupInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiEntityMoveToGroupInput.ProtoReflect.Descriptor instead.
func (*MultiEntityMoveToGroupInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{7}
}

func (x *MultiEntityMoveToGroupInput) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MultiEntityMoveToGroupInput) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

// QueryMachineIDFilter 查询主机ID过滤条件
type QueryMachineIDFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId int64 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	OsType  int32 `protobuf:"varint,2,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
}

func (x *QueryMachineIDFilter) Reset() {
	*x = QueryMachineIDFilter{}
	mi := &file_braum_entity_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryMachineIDFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryMachineIDFilter) ProtoMessage() {}

func (x *QueryMachineIDFilter) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryMachineIDFilter.ProtoReflect.Descriptor instead.
func (*QueryMachineIDFilter) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{8}
}

func (x *QueryMachineIDFilter) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *QueryMachineIDFilter) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

// QueryMachineIDInput 查询主机ID输入
type QueryMachineIDInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *QueryMachineIDFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageInput            `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *QueryMachineIDInput) Reset() {
	*x = QueryMachineIDInput{}
	mi := &file_braum_entity_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryMachineIDInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryMachineIDInput) ProtoMessage() {}

func (x *QueryMachineIDInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryMachineIDInput.ProtoReflect.Descriptor instead.
func (*QueryMachineIDInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{9}
}

func (x *QueryMachineIDInput) GetFilter() *QueryMachineIDFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *QueryMachineIDInput) GetPage() *PageInput {
	if x != nil {
		return x.Page
	}
	return nil
}

// MachineIDList 主机ID列表
type MachineIDList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       *PageOutput `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	MachineIds []string    `protobuf:"bytes,2,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
}

func (x *MachineIDList) Reset() {
	*x = MachineIDList{}
	mi := &file_braum_entity_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MachineIDList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MachineIDList) ProtoMessage() {}

func (x *MachineIDList) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MachineIDList.ProtoReflect.Descriptor instead.
func (*MachineIDList) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{10}
}

func (x *MachineIDList) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *MachineIDList) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

// RiskStatusInput 用于写入攻击、风险状态
type RiskStatusInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Attack    int32  `protobuf:"varint,2,opt,name=attack,proto3" json:"attack,omitempty"`
	Risk      int32  `protobuf:"varint,3,opt,name=risk,proto3" json:"risk,omitempty"`
}

func (x *RiskStatusInput) Reset() {
	*x = RiskStatusInput{}
	mi := &file_braum_entity_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskStatusInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskStatusInput) ProtoMessage() {}

func (x *RiskStatusInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskStatusInput.ProtoReflect.Descriptor instead.
func (*RiskStatusInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{11}
}

func (x *RiskStatusInput) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *RiskStatusInput) GetAttack() int32 {
	if x != nil {
		return x.Attack
	}
	return 0
}

func (x *RiskStatusInput) GetRisk() int32 {
	if x != nil {
		return x.Risk
	}
	return 0
}

// EntityVersionRequest 主机版本请求
type EntityVersionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	VerKind   int32  `protobuf:"varint,2,opt,name=ver_kind,json=verKind,proto3" json:"ver_kind,omitempty"`
}

func (x *EntityVersionRequest) Reset() {
	*x = EntityVersionRequest{}
	mi := &file_braum_entity_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityVersionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityVersionRequest) ProtoMessage() {}

func (x *EntityVersionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityVersionRequest.ProtoReflect.Descriptor instead.
func (*EntityVersionRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{12}
}

func (x *EntityVersionRequest) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntityVersionRequest) GetVerKind() int32 {
	if x != nil {
		return x.VerKind
	}
	return 0
}

// EntityVersion 主机版本信息
type EntityVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	VerKind   int32  `protobuf:"varint,2,opt,name=ver_kind,json=verKind,proto3" json:"ver_kind,omitempty"`
	VerType   int32  `protobuf:"varint,3,opt,name=ver_type,json=verType,proto3" json:"ver_type,omitempty"`
	Version   string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *EntityVersion) Reset() {
	*x = EntityVersion{}
	mi := &file_braum_entity_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityVersion) ProtoMessage() {}

func (x *EntityVersion) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityVersion.ProtoReflect.Descriptor instead.
func (*EntityVersion) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{13}
}

func (x *EntityVersion) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntityVersion) GetVerKind() int32 {
	if x != nil {
		return x.VerKind
	}
	return 0
}

func (x *EntityVersion) GetVerType() int32 {
	if x != nil {
		return x.VerType
	}
	return 0
}

func (x *EntityVersion) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// EntityDetailInput 主机详情输入
type EntityDetailInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IncludeGroupName bool       `protobuf:"varint,1,opt,name=include_group_name,json=includeGroupName,proto3" json:"include_group_name,omitempty"`
	Page             *PageInput `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *EntityDetailInput) Reset() {
	*x = EntityDetailInput{}
	mi := &file_braum_entity_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityDetailInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityDetailInput) ProtoMessage() {}

func (x *EntityDetailInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityDetailInput.ProtoReflect.Descriptor instead.
func (*EntityDetailInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{14}
}

func (x *EntityDetailInput) GetIncludeGroupName() bool {
	if x != nil {
		return x.IncludeGroupName
	}
	return false
}

func (x *EntityDetailInput) GetPage() *PageInput {
	if x != nil {
		return x.Page
	}
	return nil
}

// EntityDetail 主机详情
type EntityDetailResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityInfo *EntityInfo      `protobuf:"bytes,1,opt,name=entity_info,json=entityInfo,proto3" json:"entity_info,omitempty"`
	Versions   []*EntityVersion `protobuf:"bytes,2,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *EntityDetailResponse) Reset() {
	*x = EntityDetailResponse{}
	mi := &file_braum_entity_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityDetailResponse) ProtoMessage() {}

func (x *EntityDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityDetailResponse.ProtoReflect.Descriptor instead.
func (*EntityDetailResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{15}
}

func (x *EntityDetailResponse) GetEntityInfo() *EntityInfo {
	if x != nil {
		return x.EntityInfo
	}
	return nil
}

func (x *EntityDetailResponse) GetVersions() []*EntityVersion {
	if x != nil {
		return x.Versions
	}
	return nil
}

// EntityDetailList 主机详情列表
type EntityDetailList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  *PageOutput             `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	Items []*EntityDetailResponse `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *EntityDetailList) Reset() {
	*x = EntityDetailList{}
	mi := &file_braum_entity_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityDetailList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityDetailList) ProtoMessage() {}

func (x *EntityDetailList) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityDetailList.ProtoReflect.Descriptor instead.
func (*EntityDetailList) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{16}
}

func (x *EntityDetailList) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *EntityDetailList) GetItems() []*EntityDetailResponse {
	if x != nil {
		return x.Items
	}
	return nil
}

// EntityImportInput 主机导入输入
type EntityImportInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImportMode int32  `protobuf:"varint,1,opt,name=import_mode,json=importMode,proto3" json:"import_mode,omitempty"`
	BucketName string `protobuf:"bytes,2,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	ObjectName string `protobuf:"bytes,3,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`
}

func (x *EntityImportInput) Reset() {
	*x = EntityImportInput{}
	mi := &file_braum_entity_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityImportInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityImportInput) ProtoMessage() {}

func (x *EntityImportInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityImportInput.ProtoReflect.Descriptor instead.
func (*EntityImportInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{17}
}

func (x *EntityImportInput) GetImportMode() int32 {
	if x != nil {
		return x.ImportMode
	}
	return 0
}

func (x *EntityImportInput) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *EntityImportInput) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

// EntityImportResult 主机导入结果
type EntityImportResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Success int32 `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Failure int32 `protobuf:"varint,3,opt,name=failure,proto3" json:"failure,omitempty"`
	LogId   int64 `protobuf:"varint,4,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
}

func (x *EntityImportResult) Reset() {
	*x = EntityImportResult{}
	mi := &file_braum_entity_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityImportResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityImportResult) ProtoMessage() {}

func (x *EntityImportResult) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityImportResult.ProtoReflect.Descriptor instead.
func (*EntityImportResult) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{18}
}

func (x *EntityImportResult) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *EntityImportResult) GetSuccess() int32 {
	if x != nil {
		return x.Success
	}
	return 0
}

func (x *EntityImportResult) GetFailure() int32 {
	if x != nil {
		return x.Failure
	}
	return 0
}

func (x *EntityImportResult) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

// EntityExtInfoInput 主机扩展信息输入
type EntityExtInfoInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string         `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	ExtInfo   *EntityExtInfo `protobuf:"bytes,2,opt,name=ext_info,json=extInfo,proto3" json:"ext_info,omitempty"`
}

func (x *EntityExtInfoInput) Reset() {
	*x = EntityExtInfoInput{}
	mi := &file_braum_entity_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityExtInfoInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityExtInfoInput) ProtoMessage() {}

func (x *EntityExtInfoInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityExtInfoInput.ProtoReflect.Descriptor instead.
func (*EntityExtInfoInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{19}
}

func (x *EntityExtInfoInput) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntityExtInfoInput) GetExtInfo() *EntityExtInfo {
	if x != nil {
		return x.ExtInfo
	}
	return nil
}

// EntityImportLog 主机导入日志
type EntityImportLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status    int32 `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Success   int32 `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	Failure   int32 `protobuf:"varint,4,opt,name=failure,proto3" json:"failure,omitempty"`
	AdminId   int64 `protobuf:"varint,5,opt,name=admin_id,json=adminId,proto3" json:"admin_id,omitempty"`
	CreatedAt int64 `protobuf:"varint,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *EntityImportLog) Reset() {
	*x = EntityImportLog{}
	mi := &file_braum_entity_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityImportLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityImportLog) ProtoMessage() {}

func (x *EntityImportLog) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityImportLog.ProtoReflect.Descriptor instead.
func (*EntityImportLog) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{20}
}

func (x *EntityImportLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EntityImportLog) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *EntityImportLog) GetSuccess() int32 {
	if x != nil {
		return x.Success
	}
	return 0
}

func (x *EntityImportLog) GetFailure() int32 {
	if x != nil {
		return x.Failure
	}
	return 0
}

func (x *EntityImportLog) GetAdminId() int64 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *EntityImportLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// EntityImportLogList 主机导入日志列表
type EntityImportLogList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page  *PageOutput        `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`
	Items []*EntityImportLog `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *EntityImportLogList) Reset() {
	*x = EntityImportLogList{}
	mi := &file_braum_entity_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityImportLogList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityImportLogList) ProtoMessage() {}

func (x *EntityImportLogList) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityImportLogList.ProtoReflect.Descriptor instead.
func (*EntityImportLogList) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{21}
}

func (x *EntityImportLogList) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *EntityImportLogList) GetItems() []*EntityImportLog {
	if x != nil {
		return x.Items
	}
	return nil
}

// AOSObjectInfo AOS对象信息
type AOSObjectInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BucketName string `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	ObjectName string `protobuf:"bytes,2,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`
	Url        string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *AOSObjectInfo) Reset() {
	*x = AOSObjectInfo{}
	mi := &file_braum_entity_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AOSObjectInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AOSObjectInfo) ProtoMessage() {}

func (x *AOSObjectInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AOSObjectInfo.ProtoReflect.Descriptor instead.
func (*AOSObjectInfo) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{22}
}

func (x *AOSObjectInfo) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *AOSObjectInfo) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *AOSObjectInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// SimpleEntityInfo 简单主机信息
type SimpleEntityInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac             string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`
	Os              string `protobuf:"bytes,2,opt,name=os,proto3" json:"os,omitempty"`
	OsType          int32  `protobuf:"varint,3,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`
	Ip              string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip,omitempty"`
	MachineName     string `protobuf:"bytes,5,opt,name=machine_name,json=machineName,proto3" json:"machine_name,omitempty"`
	Arch            string `protobuf:"bytes,6,opt,name=arch,proto3" json:"arch,omitempty"`
	Gateway         string `protobuf:"bytes,7,opt,name=gateway,proto3" json:"gateway,omitempty"`
	CurrUser        string `protobuf:"bytes,8,opt,name=curr_user,json=currUser,proto3" json:"curr_user,omitempty"`
	LatestUser      string `protobuf:"bytes,9,opt,name=latest_user,json=latestUser,proto3" json:"latest_user,omitempty"`
	DomainUser      string `protobuf:"bytes,10,opt,name=domain_user,json=domainUser,proto3" json:"domain_user,omitempty"`
	HostId          string `protobuf:"bytes,11,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
	AgentVersion    string `protobuf:"bytes,12,opt,name=agent_version,json=agentVersion,proto3" json:"agent_version,omitempty"`
	FirstInstallAt  int64  `protobuf:"varint,13,opt,name=first_install_at,json=firstInstallAt,proto3" json:"first_install_at,omitempty"`
	AgentUpgradeAt  int64  `protobuf:"varint,14,opt,name=agent_upgrade_at,json=agentUpgradeAt,proto3" json:"agent_upgrade_at,omitempty"`
	LatestOnlineAt  int64  `protobuf:"varint,15,opt,name=latest_online_at,json=latestOnlineAt,proto3" json:"latest_online_at,omitempty"`
	LatestOfflineAt int64  `protobuf:"varint,16,opt,name=latest_offline_at,json=latestOfflineAt,proto3" json:"latest_offline_at,omitempty"`
	UserName        string `protobuf:"bytes,17,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *SimpleEntityInfo) Reset() {
	*x = SimpleEntityInfo{}
	mi := &file_braum_entity_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SimpleEntityInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SimpleEntityInfo) ProtoMessage() {}

func (x *SimpleEntityInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SimpleEntityInfo.ProtoReflect.Descriptor instead.
func (*SimpleEntityInfo) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{23}
}

func (x *SimpleEntityInfo) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *SimpleEntityInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *SimpleEntityInfo) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *SimpleEntityInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *SimpleEntityInfo) GetMachineName() string {
	if x != nil {
		return x.MachineName
	}
	return ""
}

func (x *SimpleEntityInfo) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *SimpleEntityInfo) GetGateway() string {
	if x != nil {
		return x.Gateway
	}
	return ""
}

func (x *SimpleEntityInfo) GetCurrUser() string {
	if x != nil {
		return x.CurrUser
	}
	return ""
}

func (x *SimpleEntityInfo) GetLatestUser() string {
	if x != nil {
		return x.LatestUser
	}
	return ""
}

func (x *SimpleEntityInfo) GetDomainUser() string {
	if x != nil {
		return x.DomainUser
	}
	return ""
}

func (x *SimpleEntityInfo) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *SimpleEntityInfo) GetAgentVersion() string {
	if x != nil {
		return x.AgentVersion
	}
	return ""
}

func (x *SimpleEntityInfo) GetFirstInstallAt() int64 {
	if x != nil {
		return x.FirstInstallAt
	}
	return 0
}

func (x *SimpleEntityInfo) GetAgentUpgradeAt() int64 {
	if x != nil {
		return x.AgentUpgradeAt
	}
	return 0
}

func (x *SimpleEntityInfo) GetLatestOnlineAt() int64 {
	if x != nil {
		return x.LatestOnlineAt
	}
	return 0
}

func (x *SimpleEntityInfo) GetLatestOfflineAt() int64 {
	if x != nil {
		return x.LatestOfflineAt
	}
	return 0
}

func (x *SimpleEntityInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

// UpdateEntityVulCountInput
type UpdateEntityVulCountInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac            string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`
	KernelVulCount int64  `protobuf:"varint,2,opt,name=kernel_vul_count,json=kernelVulCount,proto3" json:"kernel_vul_count,omitempty"`
	AppVulCount    int64  `protobuf:"varint,3,opt,name=app_vul_count,json=appVulCount,proto3" json:"app_vul_count,omitempty"`
	WebVulCount    int64  `protobuf:"varint,4,opt,name=web_vul_count,json=webVulCount,proto3" json:"web_vul_count,omitempty"`
}

func (x *UpdateEntityVulCountInput) Reset() {
	*x = UpdateEntityVulCountInput{}
	mi := &file_braum_entity_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateEntityVulCountInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEntityVulCountInput) ProtoMessage() {}

func (x *UpdateEntityVulCountInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEntityVulCountInput.ProtoReflect.Descriptor instead.
func (*UpdateEntityVulCountInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{24}
}

func (x *UpdateEntityVulCountInput) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *UpdateEntityVulCountInput) GetKernelVulCount() int64 {
	if x != nil {
		return x.KernelVulCount
	}
	return 0
}

func (x *UpdateEntityVulCountInput) GetAppVulCount() int64 {
	if x != nil {
		return x.AppVulCount
	}
	return 0
}

func (x *UpdateEntityVulCountInput) GetWebVulCount() int64 {
	if x != nil {
		return x.WebVulCount
	}
	return 0
}

// EntityComMessage
type EntityComMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Type      string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Method    string `protobuf:"bytes,3,opt,name=method,proto3" json:"method,omitempty"`
	Data      []byte `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *EntityComMessage) Reset() {
	*x = EntityComMessage{}
	mi := &file_braum_entity_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityComMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityComMessage) ProtoMessage() {}

func (x *EntityComMessage) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityComMessage.ProtoReflect.Descriptor instead.
func (*EntityComMessage) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{25}
}

func (x *EntityComMessage) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *EntityComMessage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *EntityComMessage) GetMethod() string {
	if x != nil {
		return x.Method
	}
	return ""
}

func (x *EntityComMessage) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// ModifyMachineUserNameReq
type ModifyMachineUserNameReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac      string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`
	UserName string `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
}

func (x *ModifyMachineUserNameReq) Reset() {
	*x = ModifyMachineUserNameReq{}
	mi := &file_braum_entity_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModifyMachineUserNameReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMachineUserNameReq) ProtoMessage() {}

func (x *ModifyMachineUserNameReq) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMachineUserNameReq.ProtoReflect.Descriptor instead.
func (*ModifyMachineUserNameReq) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{26}
}

func (x *ModifyMachineUserNameReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ModifyMachineUserNameReq) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

// RemoveEntityInput
type RemoveEntityInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *EntityFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Exclude []string      `protobuf:"bytes,2,rep,name=exclude,proto3" json:"exclude,omitempty"`
}

func (x *RemoveEntityInput) Reset() {
	*x = RemoveEntityInput{}
	mi := &file_braum_entity_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveEntityInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveEntityInput) ProtoMessage() {}

func (x *RemoveEntityInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveEntityInput.ProtoReflect.Descriptor instead.
func (*RemoveEntityInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{27}
}

func (x *RemoveEntityInput) GetFilter() *EntityFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *RemoveEntityInput) GetExclude() []string {
	if x != nil {
		return x.Exclude
	}
	return nil
}

// EntityCountResponse
type EntityCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *EntityCountResponse) Reset() {
	*x = EntityCountResponse{}
	mi := &file_braum_entity_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityCountResponse) ProtoMessage() {}

func (x *EntityCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityCountResponse.ProtoReflect.Descriptor instead.
func (*EntityCountResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{28}
}

func (x *EntityCountResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// CommonResponse 通用响应消息
type CommonResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success      bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMessage string `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	ErrorCode    int32  `protobuf:"varint,3,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_braum_entity_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{29}
}

func (x *CommonResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CommonResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *CommonResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

// UpsertEntityInfoRequest 主机信息写入请求
type UpsertEntityInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entities []*EntitybaseInput `protobuf:"bytes,1,rep,name=entities,proto3" json:"entities,omitempty"`
}

func (x *UpsertEntityInfoRequest) Reset() {
	*x = UpsertEntityInfoRequest{}
	mi := &file_braum_entity_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpsertEntityInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpsertEntityInfoRequest) ProtoMessage() {}

func (x *UpsertEntityInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpsertEntityInfoRequest.ProtoReflect.Descriptor instead.
func (*UpsertEntityInfoRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{30}
}

func (x *UpsertEntityInfoRequest) GetEntities() []*EntitybaseInput {
	if x != nil {
		return x.Entities
	}
	return nil
}

// EntityInfoRequest 主机信息请求
type EntityInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
}

func (x *EntityInfoRequest) Reset() {
	*x = EntityInfoRequest{}
	mi := &file_braum_entity_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInfoRequest) ProtoMessage() {}

func (x *EntityInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInfoRequest.ProtoReflect.Descriptor instead.
func (*EntityInfoRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{31}
}

func (x *EntityInfoRequest) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

// EntityInfoResponse 主机信息响应
type EntityInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EntityInfo *EntityInfo `protobuf:"bytes,1,opt,name=entity_info,json=entityInfo,proto3" json:"entity_info,omitempty"`
}

func (x *EntityInfoResponse) Reset() {
	*x = EntityInfoResponse{}
	mi := &file_braum_entity_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInfoResponse) ProtoMessage() {}

func (x *EntityInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInfoResponse.ProtoReflect.Descriptor instead.
func (*EntityInfoResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{32}
}

func (x *EntityInfoResponse) GetEntityInfo() *EntityInfo {
	if x != nil {
		return x.EntityInfo
	}
	return nil
}

// BatchQueryEntityInfoRequest 批量查询主机信息请求
type BatchQueryEntityInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIds []string `protobuf:"bytes,1,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
}

func (x *BatchQueryEntityInfoRequest) Reset() {
	*x = BatchQueryEntityInfoRequest{}
	mi := &file_braum_entity_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchQueryEntityInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryEntityInfoRequest) ProtoMessage() {}

func (x *BatchQueryEntityInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryEntityInfoRequest.ProtoReflect.Descriptor instead.
func (*BatchQueryEntityInfoRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{33}
}

func (x *BatchQueryEntityInfoRequest) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

// BatchQueryEntityInfoByIPRequest 批量通过IP查询主机信息请求
type BatchQueryEntityInfoByIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ips []string `protobuf:"bytes,1,rep,name=ips,proto3" json:"ips,omitempty"`
}

func (x *BatchQueryEntityInfoByIPRequest) Reset() {
	*x = BatchQueryEntityInfoByIPRequest{}
	mi := &file_braum_entity_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchQueryEntityInfoByIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryEntityInfoByIPRequest) ProtoMessage() {}

func (x *BatchQueryEntityInfoByIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryEntityInfoByIPRequest.ProtoReflect.Descriptor instead.
func (*BatchQueryEntityInfoByIPRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{34}
}

func (x *BatchQueryEntityInfoByIPRequest) GetIps() []string {
	if x != nil {
		return x.Ips
	}
	return nil
}

// BatchQueryEntityInfoByMachineNameRequest 批量通过主机名查询主机信息请求
type BatchQueryEntityInfoByMachineNameRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineNames []string `protobuf:"bytes,1,rep,name=machine_names,json=machineNames,proto3" json:"machine_names,omitempty"`
}

func (x *BatchQueryEntityInfoByMachineNameRequest) Reset() {
	*x = BatchQueryEntityInfoByMachineNameRequest{}
	mi := &file_braum_entity_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchQueryEntityInfoByMachineNameRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchQueryEntityInfoByMachineNameRequest) ProtoMessage() {}

func (x *BatchQueryEntityInfoByMachineNameRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchQueryEntityInfoByMachineNameRequest.ProtoReflect.Descriptor instead.
func (*BatchQueryEntityInfoByMachineNameRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{35}
}

func (x *BatchQueryEntityInfoByMachineNameRequest) GetMachineNames() []string {
	if x != nil {
		return x.MachineNames
	}
	return nil
}

// EntityInput 主机输入
type EntityInput struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *EntityFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string        `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Page    *PageInput    `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *EntityInput) Reset() {
	*x = EntityInput{}
	mi := &file_braum_entity_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityInput) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityInput) ProtoMessage() {}

func (x *EntityInput) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityInput.ProtoReflect.Descriptor instead.
func (*EntityInput) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{36}
}

func (x *EntityInput) GetFilter() *EntityFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *EntityInput) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *EntityInput) GetPage() *PageInput {
	if x != nil {
		return x.Page
	}
	return nil
}

// RemoveMultiEntityRequest 删除多个主机请求
type RemoveMultiEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIds []string `protobuf:"bytes,1,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
}

func (x *RemoveMultiEntityRequest) Reset() {
	*x = RemoveMultiEntityRequest{}
	mi := &file_braum_entity_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveMultiEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveMultiEntityRequest) ProtoMessage() {}

func (x *RemoveMultiEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveMultiEntityRequest.ProtoReflect.Descriptor instead.
func (*RemoveMultiEntityRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{37}
}

func (x *RemoveMultiEntityRequest) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

// QueryEntityMacListRequest 查询主机Mac列表请求
type QueryEntityMacListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchData string `protobuf:"bytes,1,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`
}

func (x *QueryEntityMacListRequest) Reset() {
	*x = QueryEntityMacListRequest{}
	mi := &file_braum_entity_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryEntityMacListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEntityMacListRequest) ProtoMessage() {}

func (x *QueryEntityMacListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEntityMacListRequest.ProtoReflect.Descriptor instead.
func (*QueryEntityMacListRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{38}
}

func (x *QueryEntityMacListRequest) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

// QueryVersionListRequest 查询版本列表请求
type QueryVersionListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VersionKind int32 `protobuf:"varint,1,opt,name=version_kind,json=versionKind,proto3" json:"version_kind,omitempty"`
}

func (x *QueryVersionListRequest) Reset() {
	*x = QueryVersionListRequest{}
	mi := &file_braum_entity_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryVersionListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryVersionListRequest) ProtoMessage() {}

func (x *QueryVersionListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryVersionListRequest.ProtoReflect.Descriptor instead.
func (*QueryVersionListRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{39}
}

func (x *QueryVersionListRequest) GetVersionKind() int32 {
	if x != nil {
		return x.VersionKind
	}
	return 0
}

// QueryVersionListResponse 查询版本列表响应
type QueryVersionListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Versions []string `protobuf:"bytes,1,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *QueryVersionListResponse) Reset() {
	*x = QueryVersionListResponse{}
	mi := &file_braum_entity_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryVersionListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryVersionListResponse) ProtoMessage() {}

func (x *QueryVersionListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryVersionListResponse.ProtoReflect.Descriptor instead.
func (*QueryVersionListResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{40}
}

func (x *QueryVersionListResponse) GetVersions() []string {
	if x != nil {
		return x.Versions
	}
	return nil
}

// QueryEntityVersionListResponse 查询主机版本列表响应
type QueryEntityVersionListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Versions []*EntityVersion `protobuf:"bytes,1,rep,name=versions,proto3" json:"versions,omitempty"`
}

func (x *QueryEntityVersionListResponse) Reset() {
	*x = QueryEntityVersionListResponse{}
	mi := &file_braum_entity_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryEntityVersionListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryEntityVersionListResponse) ProtoMessage() {}

func (x *QueryEntityVersionListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryEntityVersionListResponse.ProtoReflect.Descriptor instead.
func (*QueryEntityVersionListResponse) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{41}
}

func (x *QueryEntityVersionListResponse) GetVersions() []*EntityVersion {
	if x != nil {
		return x.Versions
	}
	return nil
}

// EntityImportTemplateURLRequest 主机导入模板URL请求
type EntityImportTemplateURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
}

func (x *EntityImportTemplateURLRequest) Reset() {
	*x = EntityImportTemplateURLRequest{}
	mi := &file_braum_entity_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EntityImportTemplateURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntityImportTemplateURLRequest) ProtoMessage() {}

func (x *EntityImportTemplateURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntityImportTemplateURLRequest.ProtoReflect.Descriptor instead.
func (*EntityImportTemplateURLRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{42}
}

func (x *EntityImportTemplateURLRequest) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

// GetEntityLogURLRequest 获取主机日志URL请求
type GetEntityLogURLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetEntityLogURLRequest) Reset() {
	*x = GetEntityLogURLRequest{}
	mi := &file_braum_entity_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEntityLogURLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityLogURLRequest) ProtoMessage() {}

func (x *GetEntityLogURLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityLogURLRequest.ProtoReflect.Descriptor instead.
func (*GetEntityLogURLRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{43}
}

func (x *GetEntityLogURLRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// NoAdaptedAgentInfo 未适配的主机信息
type NoAdaptedAgentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	MachineName string `protobuf:"bytes,2,opt,name=machine_name,json=machineName,proto3" json:"machine_name,omitempty"`
	OsInfo      string `protobuf:"bytes,3,opt,name=os_info,json=osInfo,proto3" json:"os_info,omitempty"`
	KernelInfo  string `protobuf:"bytes,4,opt,name=kernel_info,json=kernelInfo,proto3" json:"kernel_info,omitempty"`
}

func (x *NoAdaptedAgentInfo) Reset() {
	*x = NoAdaptedAgentInfo{}
	mi := &file_braum_entity_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoAdaptedAgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoAdaptedAgentInfo) ProtoMessage() {}

func (x *NoAdaptedAgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoAdaptedAgentInfo.ProtoReflect.Descriptor instead.
func (*NoAdaptedAgentInfo) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{44}
}

func (x *NoAdaptedAgentInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *NoAdaptedAgentInfo) GetMachineName() string {
	if x != nil {
		return x.MachineName
	}
	return ""
}

func (x *NoAdaptedAgentInfo) GetOsInfo() string {
	if x != nil {
		return x.OsInfo
	}
	return ""
}

func (x *NoAdaptedAgentInfo) GetKernelInfo() string {
	if x != nil {
		return x.KernelInfo
	}
	return ""
}

type GetGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetGroupRequest) Reset() {
	*x = GetGroupRequest{}
	mi := &file_braum_entity_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupRequest) ProtoMessage() {}

func (x *GetGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupRequest.ProtoReflect.Descriptor instead.
func (*GetGroupRequest) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{45}
}

func (x *GetGroupRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// LogCollectionType 同 agent.CollectLogsType
type LogCollectionType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogCollectionType) Reset() {
	*x = LogCollectionType{}
	mi := &file_braum_entity_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionType) ProtoMessage() {}

func (x *LogCollectionType) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionType.ProtoReflect.Descriptor instead.
func (*LogCollectionType) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{46}
}

type LogCollectionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogCollectionStatus) Reset() {
	*x = LogCollectionStatus{}
	mi := &file_braum_entity_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionStatus) ProtoMessage() {}

func (x *LogCollectionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionStatus.ProtoReflect.Descriptor instead.
func (*LogCollectionStatus) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{47}
}

type LogCollectionFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogCollectionFailure) Reset() {
	*x = LogCollectionFailure{}
	mi := &file_braum_entity_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionFailure) ProtoMessage() {}

func (x *LogCollectionFailure) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionFailure.ProtoReflect.Descriptor instead.
func (*LogCollectionFailure) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{48}
}

type LogCollectionTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int64                     `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                          // 收集任务 ID
	HostId     string                    `protobuf:"bytes,2,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`                           // 主机 ID
	UserId     int64                     `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                          // 用户 ID
	Type       LogCollectionType_Enum    `protobuf:"varint,4,opt,name=type,proto3,enum=braum.LogCollectionType_Enum" json:"type,omitempty"`          // 日志类型
	Status     LogCollectionStatus_Enum  `protobuf:"varint,5,opt,name=status,proto3,enum=braum.LogCollectionStatus_Enum" json:"status,omitempty"`    // 日志收集状态
	Failure    LogCollectionFailure_Enum `protobuf:"varint,6,opt,name=failure,proto3,enum=braum.LogCollectionFailure_Enum" json:"failure,omitempty"` // 日志收集失败原因
	Filename   string                    `protobuf:"bytes,7,opt,name=filename,proto3" json:"filename,omitempty"`                                     // 原始文件名
	ObjectName string                    `protobuf:"bytes,8,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`               // 存储对象名
	ObjectSize int64                     `protobuf:"varint,9,opt,name=object_size,json=objectSize,proto3" json:"object_size,omitempty"`              // 存储对象大小
	UpdatedAt  *timestamppb.Timestamp    `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                 // 更新时间
	CreatedAt  *timestamppb.Timestamp    `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                 // 创建时间
}

func (x *LogCollectionTask) Reset() {
	*x = LogCollectionTask{}
	mi := &file_braum_entity_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionTask) ProtoMessage() {}

func (x *LogCollectionTask) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionTask.ProtoReflect.Descriptor instead.
func (*LogCollectionTask) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{49}
}

func (x *LogCollectionTask) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *LogCollectionTask) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *LogCollectionTask) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LogCollectionTask) GetType() LogCollectionType_Enum {
	if x != nil {
		return x.Type
	}
	return LogCollectionType_Unknown
}

func (x *LogCollectionTask) GetStatus() LogCollectionStatus_Enum {
	if x != nil {
		return x.Status
	}
	return LogCollectionStatus_Unknown
}

func (x *LogCollectionTask) GetFailure() LogCollectionFailure_Enum {
	if x != nil {
		return x.Failure
	}
	return LogCollectionFailure_Unknown
}

func (x *LogCollectionTask) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *LogCollectionTask) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *LogCollectionTask) GetObjectSize() int64 {
	if x != nil {
		return x.ObjectSize
	}
	return 0
}

func (x *LogCollectionTask) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LogCollectionTask) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type LogCollectionCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Task      *LogCollectionTask `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`                            // 任务详细内容
	UploadUrl string             `protobuf:"bytes,2,opt,name=upload_url,json=uploadUrl,proto3" json:"upload_url,omitempty"` // Agent 上传日志 URL
}

func (x *LogCollectionCreateReq) Reset() {
	*x = LogCollectionCreateReq{}
	mi := &file_braum_entity_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionCreateReq) ProtoMessage() {}

func (x *LogCollectionCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionCreateReq.ProtoReflect.Descriptor instead.
func (*LogCollectionCreateReq) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{50}
}

func (x *LogCollectionCreateReq) GetTask() *LogCollectionTask {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *LogCollectionCreateReq) GetUploadUrl() string {
	if x != nil {
		return x.UploadUrl
	}
	return ""
}

type LogCollectionUploadedReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Succeeded  bool   `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	Filename   string `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`                        // 原始文件名
	ObjectName string `protobuf:"bytes,4,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`  // 存储对象名
	ObjectSize int64  `protobuf:"varint,5,opt,name=object_size,json=objectSize,proto3" json:"object_size,omitempty"` // 存储对象大小
}

func (x *LogCollectionUploadedReq) Reset() {
	*x = LogCollectionUploadedReq{}
	mi := &file_braum_entity_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionUploadedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionUploadedReq) ProtoMessage() {}

func (x *LogCollectionUploadedReq) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionUploadedReq.ProtoReflect.Descriptor instead.
func (*LogCollectionUploadedReq) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{51}
}

func (x *LogCollectionUploadedReq) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *LogCollectionUploadedReq) GetSucceeded() bool {
	if x != nil {
		return x.Succeeded
	}
	return false
}

func (x *LogCollectionUploadedReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *LogCollectionUploadedReq) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *LogCollectionUploadedReq) GetObjectSize() int64 {
	if x != nil {
		return x.ObjectSize
	}
	return 0
}

type LogCollectionListFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskIds    []int64                    `protobuf:"varint,1,rep,packed,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"`
	SearchData string                     `protobuf:"bytes,2,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`                   // 模糊搜索主机名/IP
	HostIds    []string                   `protobuf:"bytes,3,rep,name=host_ids,json=hostIds,proto3" json:"host_ids,omitempty"`                            // 过滤主机 ID
	Types      []LogCollectionType_Enum   `protobuf:"varint,4,rep,packed,name=types,proto3,enum=braum.LogCollectionType_Enum" json:"types,omitempty"`     // 过滤日志类型
	Status     []LogCollectionStatus_Enum `protobuf:"varint,5,rep,packed,name=status,proto3,enum=braum.LogCollectionStatus_Enum" json:"status,omitempty"` // 过滤日志收集状态
	DateRange  []*timestamppb.Timestamp   `protobuf:"bytes,6,rep,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`                      // 任务创建时间范围
}

func (x *LogCollectionListFilter) Reset() {
	*x = LogCollectionListFilter{}
	mi := &file_braum_entity_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionListFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionListFilter) ProtoMessage() {}

func (x *LogCollectionListFilter) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionListFilter.ProtoReflect.Descriptor instead.
func (*LogCollectionListFilter) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{52}
}

func (x *LogCollectionListFilter) GetTaskIds() []int64 {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

func (x *LogCollectionListFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *LogCollectionListFilter) GetHostIds() []string {
	if x != nil {
		return x.HostIds
	}
	return nil
}

func (x *LogCollectionListFilter) GetTypes() []LogCollectionType_Enum {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *LogCollectionListFilter) GetStatus() []LogCollectionStatus_Enum {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *LogCollectionListFilter) GetDateRange() []*timestamppb.Timestamp {
	if x != nil {
		return x.DateRange
	}
	return nil
}

type LogCollectionListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *LogCollectionListFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageInput               `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *LogCollectionListReq) Reset() {
	*x = LogCollectionListReq{}
	mi := &file_braum_entity_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionListReq) ProtoMessage() {}

func (x *LogCollectionListReq) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionListReq.ProtoReflect.Descriptor instead.
func (*LogCollectionListReq) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{53}
}

func (x *LogCollectionListReq) GetFilter() *LogCollectionListFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *LogCollectionListReq) GetPage() *PageInput {
	if x != nil {
		return x.Page
	}
	return nil
}

type LogCollectionListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*LogCollectionTask `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Page  *PageOutput          `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *LogCollectionListResp) Reset() {
	*x = LogCollectionListResp{}
	mi := &file_braum_entity_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogCollectionListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogCollectionListResp) ProtoMessage() {}

func (x *LogCollectionListResp) ProtoReflect() protoreflect.Message {
	mi := &file_braum_entity_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogCollectionListResp.ProtoReflect.Descriptor instead.
func (*LogCollectionListResp) Descriptor() ([]byte, []int) {
	return file_braum_entity_proto_rawDescGZIP(), []int{54}
}

func (x *LogCollectionListResp) GetItems() []*LogCollectionTask {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *LogCollectionListResp) GetPage() *PageOutput {
	if x != nil {
		return x.Page
	}
	return nil
}

var File_braum_entity_proto protoreflect.FileDescriptor

var file_braum_entity_proto_rawDesc = []byte{
	0x0a, 0x12, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2f, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x1a, 0x1a, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0b, 0x6d, 0x71, 0x2f, 0x6d, 0x71,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x86, 0x01, 0x0a, 0x09, 0x4f, 0x6e, 0x4f, 0x66, 0x66,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x16,
	0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22,
	0xa6, 0x04, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c,
	0x61, 0x73, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6c, 0x61, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e,
	0x65, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61,
	0x72, 0x63, 0x68, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73,
	0x68, 0x6f, 0x72, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61,
	0x63, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x5c, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62,
	0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0xe1, 0x04, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x74, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x6f, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c,
	0x6f, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x70, 0x72, 0x6f,
	0x78, 0x79, 0x5f, 0x69, 0x70, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x37, 0x0a,
	0x0d, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x6f, 0x73,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x61, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x14, 0x20, 0x03, 0x28, 0x08,
	0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x22, 0xd3, 0x02, 0x0a, 0x15, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x79, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25,
	0x0a, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a,
	0x11, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x77, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x3e, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x54, 0x61, 0x67, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67,
	0x22, 0x7a, 0x0a, 0x16, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x76, 0x65, 0x54, 0x6f,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x22, 0x59, 0x0a, 0x1b,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x76, 0x65, 0x54,
	0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x22, 0x4a, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x70, 0x0a, 0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x49, 0x44, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x33, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49,
	0x44, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x57, 0x0a, 0x0d, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x22, 0x5c,
	0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x69, 0x73, 0x6b,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x22, 0x50, 0x0a, 0x14,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x7e,
	0x0a, 0x0d, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x76, 0x65, 0x72, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x67,
	0x0a, 0x11, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x10, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x24, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x7c, 0x0a, 0x14, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x32, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x6c, 0x0a, 0x10, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x12, 0x31, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x22, 0x76, 0x0a, 0x11, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x77, 0x0a, 0x12, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x15, 0x0a,
	0x06, 0x6c, 0x6f, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c,
	0x6f, 0x67, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x12, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x78,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x08, 0x65, 0x78, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa7, 0x01, 0x0a, 0x0f, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0x6a, 0x0a, 0x13, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x67, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x63, 0x0a, 0x0d, 0x41, 0x4f, 0x53, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x92, 0x04, 0x0a, 0x10, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61,
	0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x18, 0x0a, 0x07,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x75, 0x72, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x55, 0x73, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x23,
	0x0a, 0x0d, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66,
	0x69, 0x72, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61,
	0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x41, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x74, 0x65, 0x73,
	0x74, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x41,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6c, 0x61,
	0x74, 0x65, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x19, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x75, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x28, 0x0a, 0x10, 0x6b, 0x65,
	0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x76, 0x75, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x75, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x75, 0x6c, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x56, 0x75, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x77, 0x65, 0x62, 0x5f,
	0x76, 0x75, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x77, 0x65, 0x62, 0x56, 0x75, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x71, 0x0a, 0x10,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6d, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x49, 0x0a, 0x18, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a,
	0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5a, 0x0a, 0x11, 0x52, 0x65,
	0x6d, 0x6f, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x2b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x22, 0x2b, 0x0a, 0x13, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x6e, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12,
	0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x22, 0x4d, 0x0a, 0x17, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32,
	0x0a, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x69,
	0x65, 0x73, 0x22, 0x32, 0x0a, 0x11, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0x48, 0x0a, 0x12, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x0b,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x3e, 0x0a, 0x1b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73,
	0x22, 0x33, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x03, 0x69, 0x70, 0x73, 0x22, 0x4f, 0x0a, 0x28, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x7b, 0x0a, 0x0b, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x2b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x24, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0x3b, 0x0a, 0x18, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73,
	0x22, 0x3c, 0x0a, 0x19, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d,
	0x61, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x22, 0x3c,
	0x0a, 0x17, 0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4b, 0x69, 0x6e, 0x64, 0x22, 0x36, 0x0a, 0x18,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x52, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x08, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x08,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x3c, 0x0a, 0x1e, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x28, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x90, 0x01, 0x0a, 0x12, 0x4e, 0x6f, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x64, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x21, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x64, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4f, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x53, 0x79, 0x73, 0x44, 0x75, 0x6d, 0x70, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x75, 0x6d, 0x70, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x53, 0x79, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x04, 0x22, 0x55, 0x0a, 0x13,
	0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x3e, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x10, 0x03, 0x22, 0x6d, 0x0a, 0x14, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x22, 0x55, 0x0a, 0x04, 0x45,
	0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x0b, 0x0a, 0x07, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x45, 0x78, 0x69, 0x73, 0x74, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10,
	0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x48, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65,
	0x10, 0x04, 0x22, 0xda, 0x03, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1d, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c,
	0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x3a, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x20, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x65, 0x0a, 0x16, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x61, 0x73,
	0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e,
	0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0xaf, 0x01, 0x0a, 0x18, 0x4c, 0x6f, 0x67, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x73, 0x75, 0x63, 0x63, 0x65, 0x65, 0x64, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x99, 0x02, 0x0a, 0x17, 0x4c, 0x6f, 0x67,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x73, 0x12, 0x33, 0x0a, 0x05, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x05, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x75,
	0x6d, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x22, 0x74, 0x0a, 0x14, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x36, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x62,
	0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x24, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x6e, 0x0a, 0x15, 0x4c, 0x6f,
	0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x32, 0x87, 0x14, 0x0a, 0x0d, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x43, 0x0a, 0x11,
	0x52, 0x65, 0x73, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x3b, 0x0a, 0x0f, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4f, 0x6e, 0x4f, 0x66, 0x66,
	0x4c, 0x69, 0x6e, 0x65, 0x12, 0x10, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4f, 0x6e, 0x4f,
	0x66, 0x66, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4a,
	0x0a, 0x10, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x55, 0x70, 0x73, 0x65, 0x72,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4b, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x55, 0x6e, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x62,
	0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x41, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x19, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x45, 0x0a, 0x0c, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x4a, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x17, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4d,
	0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x55, 0x0a,
	0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x49, 0x50, 0x12, 0x26, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x67, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x11, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a,
	0x13, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x12, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x11, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x41, 0x0a, 0x0b, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x1a, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x45,
	0x0a, 0x12, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x13, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0x1a, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x48, 0x0a, 0x14, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x18, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x54, 0x0a,
	0x16, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x76, 0x65,
	0x54, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x22, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e,
	0x4d, 0x75, 0x6c, 0x74, 0x69, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x76, 0x65, 0x54,
	0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x19, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x6f, 0x76,
	0x65, 0x54, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x42, 0x79, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x1d, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d,
	0x6f, 0x76, 0x65, 0x54, 0x6f, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x34, 0x0a, 0x06, 0x41, 0x64, 0x64, 0x54, 0x61,
	0x67, 0x12, 0x12, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x67,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x4c, 0x0a,
	0x12, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x63, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x18, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x1a, 0x14, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x49, 0x44, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x10, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x2e,
	0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x59,
	0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d,
	0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x17, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x55, 0x52, 0x4c, 0x12, 0x25, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x41, 0x4f, 0x53, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x51, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x19, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x45, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x48,
	0x0a, 0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x10, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0x1a, 0x2e, 0x62,
	0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x46, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x55, 0x52, 0x4c, 0x12, 0x1d, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67,
	0x55, 0x52, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x62, 0x72, 0x61,
	0x75, 0x6d, 0x2e, 0x41, 0x4f, 0x53, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x41, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x44, 0x42, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x12, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4e, 0x6f, 0x41,
	0x64, 0x61, 0x70, 0x74, 0x65, 0x64, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x62, 0x72,
	0x61, 0x75, 0x6d, 0x2e, 0x4e, 0x6f, 0x41, 0x64, 0x61, 0x70, 0x74, 0x65, 0x64, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x48,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x45, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x50, 0x0a, 0x15, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1f, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x08, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x16, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x47,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x0c,
	0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x46, 0x0a, 0x15,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x2e, 0x6d, 0x71, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x32, 0x83, 0x02, 0x0a, 0x05, 0x42, 0x72, 0x61, 0x75, 0x6d, 0x12, 0x50,
	0x0a, 0x17, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x2e, 0x62, 0x72, 0x61, 0x75,
	0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x54, 0x0a, 0x19, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x12, 0x1f, 0x2e,
	0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x52, 0x0a, 0x15, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x1b, 0x2e, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x62,
	0x72, 0x61, 0x75, 0x6d, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x42, 0x12, 0x5a, 0x10, 0x76, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_braum_entity_proto_rawDescOnce sync.Once
	file_braum_entity_proto_rawDescData = file_braum_entity_proto_rawDesc
)

func file_braum_entity_proto_rawDescGZIP() []byte {
	file_braum_entity_proto_rawDescOnce.Do(func() {
		file_braum_entity_proto_rawDescData = protoimpl.X.CompressGZIP(file_braum_entity_proto_rawDescData)
	})
	return file_braum_entity_proto_rawDescData
}

var file_braum_entity_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_braum_entity_proto_msgTypes = make([]protoimpl.MessageInfo, 55)
var file_braum_entity_proto_goTypes = []any{
	(LogCollectionType_Enum)(0),                      // 0: braum.LogCollectionType.Enum
	(LogCollectionStatus_Enum)(0),                    // 1: braum.LogCollectionStatus.Enum
	(LogCollectionFailure_Enum)(0),                   // 2: braum.LogCollectionFailure.Enum
	(*OnOffInfo)(nil),                                // 3: braum.OnOffInfo
	(*EntityInfo)(nil),                               // 4: braum.EntityInfo
	(*EntityList)(nil),                               // 5: braum.EntityList
	(*EntitybaseInput)(nil),                          // 6: braum.EntitybaseInput
	(*EntityStatusStatistic)(nil),                    // 7: braum.EntityStatusStatistic
	(*AddTagInput)(nil),                              // 8: braum.AddTagInput
	(*EntityMoveToGroupInput)(nil),                   // 9: braum.EntityMoveToGroupInput
	(*MultiEntityMoveToGroupInput)(nil),              // 10: braum.MultiEntityMoveToGroupInput
	(*QueryMachineIDFilter)(nil),                     // 11: braum.QueryMachineIDFilter
	(*QueryMachineIDInput)(nil),                      // 12: braum.QueryMachineIDInput
	(*MachineIDList)(nil),                            // 13: braum.MachineIDList
	(*RiskStatusInput)(nil),                          // 14: braum.RiskStatusInput
	(*EntityVersionRequest)(nil),                     // 15: braum.EntityVersionRequest
	(*EntityVersion)(nil),                            // 16: braum.EntityVersion
	(*EntityDetailInput)(nil),                        // 17: braum.EntityDetailInput
	(*EntityDetailResponse)(nil),                     // 18: braum.EntityDetailResponse
	(*EntityDetailList)(nil),                         // 19: braum.EntityDetailList
	(*EntityImportInput)(nil),                        // 20: braum.EntityImportInput
	(*EntityImportResult)(nil),                       // 21: braum.EntityImportResult
	(*EntityExtInfoInput)(nil),                       // 22: braum.EntityExtInfoInput
	(*EntityImportLog)(nil),                          // 23: braum.EntityImportLog
	(*EntityImportLogList)(nil),                      // 24: braum.EntityImportLogList
	(*AOSObjectInfo)(nil),                            // 25: braum.AOSObjectInfo
	(*SimpleEntityInfo)(nil),                         // 26: braum.SimpleEntityInfo
	(*UpdateEntityVulCountInput)(nil),                // 27: braum.UpdateEntityVulCountInput
	(*EntityComMessage)(nil),                         // 28: braum.EntityComMessage
	(*ModifyMachineUserNameReq)(nil),                 // 29: braum.ModifyMachineUserNameReq
	(*RemoveEntityInput)(nil),                        // 30: braum.RemoveEntityInput
	(*EntityCountResponse)(nil),                      // 31: braum.EntityCountResponse
	(*CommonResponse)(nil),                           // 32: braum.CommonResponse
	(*UpsertEntityInfoRequest)(nil),                  // 33: braum.UpsertEntityInfoRequest
	(*EntityInfoRequest)(nil),                        // 34: braum.EntityInfoRequest
	(*EntityInfoResponse)(nil),                       // 35: braum.EntityInfoResponse
	(*BatchQueryEntityInfoRequest)(nil),              // 36: braum.BatchQueryEntityInfoRequest
	(*BatchQueryEntityInfoByIPRequest)(nil),          // 37: braum.BatchQueryEntityInfoByIPRequest
	(*BatchQueryEntityInfoByMachineNameRequest)(nil), // 38: braum.BatchQueryEntityInfoByMachineNameRequest
	(*EntityInput)(nil),                              // 39: braum.EntityInput
	(*RemoveMultiEntityRequest)(nil),                 // 40: braum.RemoveMultiEntityRequest
	(*QueryEntityMacListRequest)(nil),                // 41: braum.QueryEntityMacListRequest
	(*QueryVersionListRequest)(nil),                  // 42: braum.QueryVersionListRequest
	(*QueryVersionListResponse)(nil),                 // 43: braum.QueryVersionListResponse
	(*QueryEntityVersionListResponse)(nil),           // 44: braum.QueryEntityVersionListResponse
	(*EntityImportTemplateURLRequest)(nil),           // 45: braum.EntityImportTemplateURLRequest
	(*GetEntityLogURLRequest)(nil),                   // 46: braum.GetEntityLogURLRequest
	(*NoAdaptedAgentInfo)(nil),                       // 47: braum.NoAdaptedAgentInfo
	(*GetGroupRequest)(nil),                          // 48: braum.GetGroupRequest
	(*LogCollectionType)(nil),                        // 49: braum.LogCollectionType
	(*LogCollectionStatus)(nil),                      // 50: braum.LogCollectionStatus
	(*LogCollectionFailure)(nil),                     // 51: braum.LogCollectionFailure
	(*LogCollectionTask)(nil),                        // 52: braum.LogCollectionTask
	(*LogCollectionCreateReq)(nil),                   // 53: braum.LogCollectionCreateReq
	(*LogCollectionUploadedReq)(nil),                 // 54: braum.LogCollectionUploadedReq
	(*LogCollectionListFilter)(nil),                  // 55: braum.LogCollectionListFilter
	(*LogCollectionListReq)(nil),                     // 56: braum.LogCollectionListReq
	(*LogCollectionListResp)(nil),                    // 57: braum.LogCollectionListResp
	(*PageOutput)(nil),                               // 58: braum.PageOutput
	(*VersionInfo)(nil),                              // 59: braum.VersionInfo
	(*EntityFilter)(nil),                             // 60: braum.EntityFilter
	(*PageInput)(nil),                                // 61: braum.PageInput
	(*EntityExtInfo)(nil),                            // 62: braum.EntityExtInfo
	(*timestamppb.Timestamp)(nil),                    // 63: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                            // 64: google.protobuf.Empty
	(*mq.AgentPacket)(nil),                           // 65: mq.AgentPacket
	(*Group)(nil),                                    // 66: braum.Group
	(*agent.LicenseEnableResponse)(nil),              // 67: agent.LicenseEnableResponse
}
var file_braum_entity_proto_depIdxs = []int32{
	58, // 0: braum.EntityList.page:type_name -> braum.PageOutput
	4,  // 1: braum.EntityList.items:type_name -> braum.EntityInfo
	59, // 2: braum.EntitybaseInput.version_slice:type_name -> braum.VersionInfo
	60, // 3: braum.EntityMoveToGroupInput.filter:type_name -> braum.EntityFilter
	11, // 4: braum.QueryMachineIDInput.filter:type_name -> braum.QueryMachineIDFilter
	61, // 5: braum.QueryMachineIDInput.page:type_name -> braum.PageInput
	58, // 6: braum.MachineIDList.page:type_name -> braum.PageOutput
	61, // 7: braum.EntityDetailInput.page:type_name -> braum.PageInput
	4,  // 8: braum.EntityDetailResponse.entity_info:type_name -> braum.EntityInfo
	16, // 9: braum.EntityDetailResponse.versions:type_name -> braum.EntityVersion
	58, // 10: braum.EntityDetailList.page:type_name -> braum.PageOutput
	18, // 11: braum.EntityDetailList.items:type_name -> braum.EntityDetailResponse
	62, // 12: braum.EntityExtInfoInput.ext_info:type_name -> braum.EntityExtInfo
	58, // 13: braum.EntityImportLogList.page:type_name -> braum.PageOutput
	23, // 14: braum.EntityImportLogList.items:type_name -> braum.EntityImportLog
	60, // 15: braum.RemoveEntityInput.filter:type_name -> braum.EntityFilter
	6,  // 16: braum.UpsertEntityInfoRequest.entities:type_name -> braum.EntitybaseInput
	4,  // 17: braum.EntityInfoResponse.entity_info:type_name -> braum.EntityInfo
	60, // 18: braum.EntityInput.filter:type_name -> braum.EntityFilter
	61, // 19: braum.EntityInput.page:type_name -> braum.PageInput
	16, // 20: braum.QueryEntityVersionListResponse.versions:type_name -> braum.EntityVersion
	0,  // 21: braum.LogCollectionTask.type:type_name -> braum.LogCollectionType.Enum
	1,  // 22: braum.LogCollectionTask.status:type_name -> braum.LogCollectionStatus.Enum
	2,  // 23: braum.LogCollectionTask.failure:type_name -> braum.LogCollectionFailure.Enum
	63, // 24: braum.LogCollectionTask.updated_at:type_name -> google.protobuf.Timestamp
	63, // 25: braum.LogCollectionTask.created_at:type_name -> google.protobuf.Timestamp
	52, // 26: braum.LogCollectionCreateReq.task:type_name -> braum.LogCollectionTask
	0,  // 27: braum.LogCollectionListFilter.types:type_name -> braum.LogCollectionType.Enum
	1,  // 28: braum.LogCollectionListFilter.status:type_name -> braum.LogCollectionStatus.Enum
	63, // 29: braum.LogCollectionListFilter.date_range:type_name -> google.protobuf.Timestamp
	55, // 30: braum.LogCollectionListReq.filter:type_name -> braum.LogCollectionListFilter
	61, // 31: braum.LogCollectionListReq.page:type_name -> braum.PageInput
	52, // 32: braum.LogCollectionListResp.items:type_name -> braum.LogCollectionTask
	58, // 33: braum.LogCollectionListResp.page:type_name -> braum.PageOutput
	64, // 34: braum.EntityService.ResetOnlineStatus:input_type -> google.protobuf.Empty
	3,  // 35: braum.EntityService.EntityOnOffLine:input_type -> braum.OnOffInfo
	33, // 36: braum.EntityService.UpsertEntityInfo:input_type -> braum.UpsertEntityInfoRequest
	34, // 37: braum.EntityService.GetUnscopeEntityInfo:input_type -> braum.EntityInfoRequest
	34, // 38: braum.EntityService.EntityInfo:input_type -> braum.EntityInfoRequest
	15, // 39: braum.EntityService.GetVersion:input_type -> braum.EntityVersionRequest
	34, // 40: braum.EntityService.EntityDetail:input_type -> braum.EntityInfoRequest
	17, // 41: braum.EntityService.QueryEntityDetailList:input_type -> braum.EntityDetailInput
	36, // 42: braum.EntityService.BatchQueryEntityInfo:input_type -> braum.BatchQueryEntityInfoRequest
	37, // 43: braum.EntityService.BatchQueryEntityInfoByIP:input_type -> braum.BatchQueryEntityInfoByIPRequest
	38, // 44: braum.EntityService.BatchQueryEntityInfoByMachineName:input_type -> braum.BatchQueryEntityInfoByMachineNameRequest
	39, // 45: braum.EntityService.QueryEntityInfoList:input_type -> braum.EntityInput
	64, // 46: braum.EntityService.EntityCount:input_type -> google.protobuf.Empty
	60, // 47: braum.EntityService.OfflineEntityCount:input_type -> braum.EntityFilter
	40, // 48: braum.EntityService.RemoveMultiEntity:input_type -> braum.RemoveMultiEntityRequest
	30, // 49: braum.EntityService.RemoveEntityByFilter:input_type -> braum.RemoveEntityInput
	10, // 50: braum.EntityService.MultiEntityMoveToGroup:input_type -> braum.MultiEntityMoveToGroupInput
	9,  // 51: braum.EntityService.EntityMoveToGroupByFilter:input_type -> braum.EntityMoveToGroupInput
	8,  // 52: braum.EntityService.AddTag:input_type -> braum.AddTagInput
	41, // 53: braum.EntityService.QueryEntityMacList:input_type -> braum.QueryEntityMacListRequest
	12, // 54: braum.EntityService.QueryEntityMachineIDList:input_type -> braum.QueryMachineIDInput
	42, // 55: braum.EntityService.QueryVersionList:input_type -> braum.QueryVersionListRequest
	34, // 56: braum.EntityService.QueryEntityVersionList:input_type -> braum.EntityInfoRequest
	45, // 57: braum.EntityService.EntityImportTemplateURL:input_type -> braum.EntityImportTemplateURLRequest
	20, // 58: braum.EntityService.BatchImportEntityGroupInfo:input_type -> braum.EntityImportInput
	22, // 59: braum.EntityService.ModifyEntityExtInfo:input_type -> braum.EntityExtInfoInput
	61, // 60: braum.EntityService.QueryEntityImportLogList:input_type -> braum.PageInput
	46, // 61: braum.EntityService.GetEntityLogURL:input_type -> braum.GetEntityLogURLRequest
	64, // 62: braum.EntityService.RestoreDBNotify:input_type -> google.protobuf.Empty
	47, // 63: braum.EntityService.ReportNoAdaptedAgent:input_type -> braum.NoAdaptedAgentInfo
	34, // 64: braum.EntityService.GetSimpleEntityInfo:input_type -> braum.EntityInfoRequest
	29, // 65: braum.EntityService.ModifyMachineUserName:input_type -> braum.ModifyMachineUserNameReq
	48, // 66: braum.EntityService.GetGroup:input_type -> braum.GetGroupRequest
	65, // 67: braum.EntityService.ApplyAccessPermission:input_type -> mq.AgentPacket
	53, // 68: braum.Braum.LogCollectionTaskCreate:input_type -> braum.LogCollectionCreateReq
	54, // 69: braum.Braum.LogCollectionFileUploaded:input_type -> braum.LogCollectionUploadedReq
	56, // 70: braum.Braum.LogCollectionTaskList:input_type -> braum.LogCollectionListReq
	64, // 71: braum.EntityService.ResetOnlineStatus:output_type -> google.protobuf.Empty
	64, // 72: braum.EntityService.EntityOnOffLine:output_type -> google.protobuf.Empty
	64, // 73: braum.EntityService.UpsertEntityInfo:output_type -> google.protobuf.Empty
	35, // 74: braum.EntityService.GetUnscopeEntityInfo:output_type -> braum.EntityInfoResponse
	35, // 75: braum.EntityService.EntityInfo:output_type -> braum.EntityInfoResponse
	16, // 76: braum.EntityService.GetVersion:output_type -> braum.EntityVersion
	18, // 77: braum.EntityService.EntityDetail:output_type -> braum.EntityDetailResponse
	19, // 78: braum.EntityService.QueryEntityDetailList:output_type -> braum.EntityDetailList
	5,  // 79: braum.EntityService.BatchQueryEntityInfo:output_type -> braum.EntityList
	5,  // 80: braum.EntityService.BatchQueryEntityInfoByIP:output_type -> braum.EntityList
	5,  // 81: braum.EntityService.BatchQueryEntityInfoByMachineName:output_type -> braum.EntityList
	5,  // 82: braum.EntityService.QueryEntityInfoList:output_type -> braum.EntityList
	31, // 83: braum.EntityService.EntityCount:output_type -> braum.EntityCountResponse
	31, // 84: braum.EntityService.OfflineEntityCount:output_type -> braum.EntityCountResponse
	64, // 85: braum.EntityService.RemoveMultiEntity:output_type -> google.protobuf.Empty
	64, // 86: braum.EntityService.RemoveEntityByFilter:output_type -> google.protobuf.Empty
	64, // 87: braum.EntityService.MultiEntityMoveToGroup:output_type -> google.protobuf.Empty
	64, // 88: braum.EntityService.EntityMoveToGroupByFilter:output_type -> google.protobuf.Empty
	64, // 89: braum.EntityService.AddTag:output_type -> google.protobuf.Empty
	13, // 90: braum.EntityService.QueryEntityMacList:output_type -> braum.MachineIDList
	13, // 91: braum.EntityService.QueryEntityMachineIDList:output_type -> braum.MachineIDList
	43, // 92: braum.EntityService.QueryVersionList:output_type -> braum.QueryVersionListResponse
	44, // 93: braum.EntityService.QueryEntityVersionList:output_type -> braum.QueryEntityVersionListResponse
	25, // 94: braum.EntityService.EntityImportTemplateURL:output_type -> braum.AOSObjectInfo
	21, // 95: braum.EntityService.BatchImportEntityGroupInfo:output_type -> braum.EntityImportResult
	64, // 96: braum.EntityService.ModifyEntityExtInfo:output_type -> google.protobuf.Empty
	24, // 97: braum.EntityService.QueryEntityImportLogList:output_type -> braum.EntityImportLogList
	25, // 98: braum.EntityService.GetEntityLogURL:output_type -> braum.AOSObjectInfo
	64, // 99: braum.EntityService.RestoreDBNotify:output_type -> google.protobuf.Empty
	64, // 100: braum.EntityService.ReportNoAdaptedAgent:output_type -> google.protobuf.Empty
	26, // 101: braum.EntityService.GetSimpleEntityInfo:output_type -> braum.SimpleEntityInfo
	64, // 102: braum.EntityService.ModifyMachineUserName:output_type -> google.protobuf.Empty
	66, // 103: braum.EntityService.GetGroup:output_type -> braum.Group
	67, // 104: braum.EntityService.ApplyAccessPermission:output_type -> agent.LicenseEnableResponse
	64, // 105: braum.Braum.LogCollectionTaskCreate:output_type -> google.protobuf.Empty
	64, // 106: braum.Braum.LogCollectionFileUploaded:output_type -> google.protobuf.Empty
	57, // 107: braum.Braum.LogCollectionTaskList:output_type -> braum.LogCollectionListResp
	71, // [71:108] is the sub-list for method output_type
	34, // [34:71] is the sub-list for method input_type
	34, // [34:34] is the sub-list for extension type_name
	34, // [34:34] is the sub-list for extension extendee
	0,  // [0:34] is the sub-list for field type_name
}

func init() { file_braum_entity_proto_init() }
func file_braum_entity_proto_init() {
	if File_braum_entity_proto != nil {
		return
	}
	file_braum_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_braum_entity_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   55,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_braum_entity_proto_goTypes,
		DependencyIndexes: file_braum_entity_proto_depIdxs,
		EnumInfos:         file_braum_entity_proto_enumTypes,
		MessageInfos:      file_braum_entity_proto_msgTypes,
	}.Build()
	File_braum_entity_proto = out.File
	file_braum_entity_proto_rawDesc = nil
	file_braum_entity_proto_goTypes = nil
	file_braum_entity_proto_depIdxs = nil
}
