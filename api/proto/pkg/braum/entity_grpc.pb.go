// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: braum/entity.proto

package braum

import (
	context "context"
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EntityService_ResetOnlineStatus_FullMethodName                 = "/braum.EntityService/ResetOnlineStatus"
	EntityService_EntityOnOffLine_FullMethodName                   = "/braum.EntityService/EntityOnOffLine"
	EntityService_UpsertEntityInfo_FullMethodName                  = "/braum.EntityService/UpsertEntityInfo"
	EntityService_GetUnscopeEntityInfo_FullMethodName              = "/braum.EntityService/GetUnscopeEntityInfo"
	EntityService_EntityInfo_FullMethodName                        = "/braum.EntityService/EntityInfo"
	EntityService_GetVersion_FullMethodName                        = "/braum.EntityService/GetVersion"
	EntityService_EntityDetail_FullMethodName                      = "/braum.EntityService/EntityDetail"
	EntityService_QueryEntityDetailList_FullMethodName             = "/braum.EntityService/QueryEntityDetailList"
	EntityService_BatchQueryEntityInfo_FullMethodName              = "/braum.EntityService/BatchQueryEntityInfo"
	EntityService_BatchQueryEntityInfoByIP_FullMethodName          = "/braum.EntityService/BatchQueryEntityInfoByIP"
	EntityService_BatchQueryEntityInfoByMachineName_FullMethodName = "/braum.EntityService/BatchQueryEntityInfoByMachineName"
	EntityService_QueryEntityInfoList_FullMethodName               = "/braum.EntityService/QueryEntityInfoList"
	EntityService_EntityCount_FullMethodName                       = "/braum.EntityService/EntityCount"
	EntityService_OfflineEntityCount_FullMethodName                = "/braum.EntityService/OfflineEntityCount"
	EntityService_RemoveMultiEntity_FullMethodName                 = "/braum.EntityService/RemoveMultiEntity"
	EntityService_RemoveEntityByFilter_FullMethodName              = "/braum.EntityService/RemoveEntityByFilter"
	EntityService_MultiEntityMoveToGroup_FullMethodName            = "/braum.EntityService/MultiEntityMoveToGroup"
	EntityService_EntityMoveToGroupByFilter_FullMethodName         = "/braum.EntityService/EntityMoveToGroupByFilter"
	EntityService_AddTag_FullMethodName                            = "/braum.EntityService/AddTag"
	EntityService_QueryEntityMacList_FullMethodName                = "/braum.EntityService/QueryEntityMacList"
	EntityService_QueryEntityMachineIDList_FullMethodName          = "/braum.EntityService/QueryEntityMachineIDList"
	EntityService_QueryVersionList_FullMethodName                  = "/braum.EntityService/QueryVersionList"
	EntityService_QueryEntityVersionList_FullMethodName            = "/braum.EntityService/QueryEntityVersionList"
	EntityService_EntityImportTemplateURL_FullMethodName           = "/braum.EntityService/EntityImportTemplateURL"
	EntityService_BatchImportEntityGroupInfo_FullMethodName        = "/braum.EntityService/BatchImportEntityGroupInfo"
	EntityService_ModifyEntityExtInfo_FullMethodName               = "/braum.EntityService/ModifyEntityExtInfo"
	EntityService_QueryEntityImportLogList_FullMethodName          = "/braum.EntityService/QueryEntityImportLogList"
	EntityService_GetEntityLogURL_FullMethodName                   = "/braum.EntityService/GetEntityLogURL"
	EntityService_RestoreDBNotify_FullMethodName                   = "/braum.EntityService/RestoreDBNotify"
	EntityService_ReportNoAdaptedAgent_FullMethodName              = "/braum.EntityService/ReportNoAdaptedAgent"
	EntityService_GetSimpleEntityInfo_FullMethodName               = "/braum.EntityService/GetSimpleEntityInfo"
	EntityService_ModifyMachineUserName_FullMethodName             = "/braum.EntityService/ModifyMachineUserName"
	EntityService_GetGroup_FullMethodName                          = "/braum.EntityService/GetGroup"
	EntityService_ApplyAccessPermission_FullMethodName             = "/braum.EntityService/ApplyAccessPermission"
)

// EntityServiceClient is the client API for EntityService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// EntityService 主机服务
type EntityServiceClient interface {
	// ResetOnlineStatus 重置在线状态
	ResetOnlineStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// EntityOnOffLine 主机上下线
	EntityOnOffLine(ctx context.Context, in *OnOffInfo, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpsertEntityInfo 主机信息写入接口
	UpsertEntityInfo(ctx context.Context, in *UpsertEntityInfoRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetUnscopeEntityInfo 获取单一主机信息(包含删除的主机)
	GetUnscopeEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error)
	// EntityInfo 获取单一主机信息
	EntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error)
	// GetVersion 获取主机相应信息
	GetVersion(ctx context.Context, in *EntityVersionRequest, opts ...grpc.CallOption) (*EntityVersion, error)
	// EntityDetail 获取单一主机详细信息(不含asset部分)
	EntityDetail(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityDetailResponse, error)
	// QueryEntityDetailList 获取主机详细信息(不含asset部分)
	QueryEntityDetailList(ctx context.Context, in *EntityDetailInput, opts ...grpc.CallOption) (*EntityDetailList, error)
	// BatchQueryEntityInfo 批量查询主机信息列表
	BatchQueryEntityInfo(ctx context.Context, in *BatchQueryEntityInfoRequest, opts ...grpc.CallOption) (*EntityList, error)
	// BatchQueryEntityInfoByIP 批量通过IP查询主机信息列表
	BatchQueryEntityInfoByIP(ctx context.Context, in *BatchQueryEntityInfoByIPRequest, opts ...grpc.CallOption) (*EntityList, error)
	// BatchQueryEntityInfoByMachineName 批量通过主机名查询主机信息列表
	BatchQueryEntityInfoByMachineName(ctx context.Context, in *BatchQueryEntityInfoByMachineNameRequest, opts ...grpc.CallOption) (*EntityList, error)
	// QueryEntityInfoList 获取主机信息列表
	QueryEntityInfoList(ctx context.Context, in *EntityInput, opts ...grpc.CallOption) (*EntityList, error)
	// EntityCount 主机总数
	EntityCount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EntityCountResponse, error)
	// OfflineEntityCount 获取离线主机数
	OfflineEntityCount(ctx context.Context, in *EntityFilter, opts ...grpc.CallOption) (*EntityCountResponse, error)
	// RemoveMultiEntity 删除多个主机
	RemoveMultiEntity(ctx context.Context, in *RemoveMultiEntityRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// RemoveEntityByFilter 按条件删除主机
	RemoveEntityByFilter(ctx context.Context, in *RemoveEntityInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// MultiEntityMoveToGroup 将多个主机移动到指定的分组
	MultiEntityMoveToGroup(ctx context.Context, in *MultiEntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// EntityMoveToGroupByFilter 按条件将主机移动到指定的分组
	EntityMoveToGroupByFilter(ctx context.Context, in *EntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// AddTag 添加Tag
	AddTag(ctx context.Context, in *AddTagInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// QueryEntityMacList 根据IP/Hostname返回最多1000个元素的macList
	QueryEntityMacList(ctx context.Context, in *QueryEntityMacListRequest, opts ...grpc.CallOption) (*MachineIDList, error)
	// QueryEntityMachineIDList 根据groupID和系统类型（win/lin/全部），分页获取machineID列
	QueryEntityMachineIDList(ctx context.Context, in *QueryMachineIDInput, opts ...grpc.CallOption) (*MachineIDList, error)
	// QueryVersionList 查询版本列表
	QueryVersionList(ctx context.Context, in *QueryVersionListRequest, opts ...grpc.CallOption) (*QueryVersionListResponse, error)
	// QueryEntityVersionList 获取主机版本信息(agent、driver...)
	QueryEntityVersionList(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*QueryEntityVersionListResponse, error)
	// EntityImportTemplateURL 获取主机导入模板URL
	EntityImportTemplateURL(ctx context.Context, in *EntityImportTemplateURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error)
	// BatchImportEntityGroupInfo 批量导入主机分组信息
	BatchImportEntityGroupInfo(ctx context.Context, in *EntityImportInput, opts ...grpc.CallOption) (*EntityImportResult, error)
	// ModifyEntityExtInfo 修改主机扩展信息(资产编号、资产等级...)
	ModifyEntityExtInfo(ctx context.Context, in *EntityExtInfoInput, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// QueryEntityImportLogList 查询entity导入日志信息
	QueryEntityImportLogList(ctx context.Context, in *PageInput, opts ...grpc.CallOption) (*EntityImportLogList, error)
	// GetEntityLogURL 获取主机导入日志文件URL
	GetEntityLogURL(ctx context.Context, in *GetEntityLogURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error)
	// RestoreDBNotify 数据库还原通知
	RestoreDBNotify(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ReportNoAdaptedAgent 接收上报未适配的主机信息
	ReportNoAdaptedAgent(ctx context.Context, in *NoAdaptedAgentInfo, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetSimpleEntityInfo 获取简单主机信息
	GetSimpleEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*SimpleEntityInfo, error)
	// ModifyMachineUserName 修改主机用户名
	ModifyMachineUserName(ctx context.Context, in *ModifyMachineUserNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// GetGroup 获取主机组信息
	GetGroup(ctx context.Context, in *GetGroupRequest, opts ...grpc.CallOption) (*Group, error)
	ApplyAccessPermission(ctx context.Context, in *mq.AgentPacket, opts ...grpc.CallOption) (*agent.LicenseEnableResponse, error)
}

type entityServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEntityServiceClient(cc grpc.ClientConnInterface) EntityServiceClient {
	return &entityServiceClient{cc}
}

func (c *entityServiceClient) ResetOnlineStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_ResetOnlineStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityOnOffLine(ctx context.Context, in *OnOffInfo, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_EntityOnOffLine_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) UpsertEntityInfo(ctx context.Context, in *UpsertEntityInfoRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_UpsertEntityInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetUnscopeEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityInfoResponse)
	err := c.cc.Invoke(ctx, EntityService_GetUnscopeEntityInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityInfoResponse)
	err := c.cc.Invoke(ctx, EntityService_EntityInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetVersion(ctx context.Context, in *EntityVersionRequest, opts ...grpc.CallOption) (*EntityVersion, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityVersion)
	err := c.cc.Invoke(ctx, EntityService_GetVersion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityDetail(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityDetailResponse)
	err := c.cc.Invoke(ctx, EntityService_EntityDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityDetailList(ctx context.Context, in *EntityDetailInput, opts ...grpc.CallOption) (*EntityDetailList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityDetailList)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityDetailList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) BatchQueryEntityInfo(ctx context.Context, in *BatchQueryEntityInfoRequest, opts ...grpc.CallOption) (*EntityList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityList)
	err := c.cc.Invoke(ctx, EntityService_BatchQueryEntityInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) BatchQueryEntityInfoByIP(ctx context.Context, in *BatchQueryEntityInfoByIPRequest, opts ...grpc.CallOption) (*EntityList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityList)
	err := c.cc.Invoke(ctx, EntityService_BatchQueryEntityInfoByIP_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) BatchQueryEntityInfoByMachineName(ctx context.Context, in *BatchQueryEntityInfoByMachineNameRequest, opts ...grpc.CallOption) (*EntityList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityList)
	err := c.cc.Invoke(ctx, EntityService_BatchQueryEntityInfoByMachineName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityInfoList(ctx context.Context, in *EntityInput, opts ...grpc.CallOption) (*EntityList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityList)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityInfoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityCount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EntityCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityCountResponse)
	err := c.cc.Invoke(ctx, EntityService_EntityCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) OfflineEntityCount(ctx context.Context, in *EntityFilter, opts ...grpc.CallOption) (*EntityCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityCountResponse)
	err := c.cc.Invoke(ctx, EntityService_OfflineEntityCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) RemoveMultiEntity(ctx context.Context, in *RemoveMultiEntityRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_RemoveMultiEntity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) RemoveEntityByFilter(ctx context.Context, in *RemoveEntityInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_RemoveEntityByFilter_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) MultiEntityMoveToGroup(ctx context.Context, in *MultiEntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_MultiEntityMoveToGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityMoveToGroupByFilter(ctx context.Context, in *EntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_EntityMoveToGroupByFilter_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) AddTag(ctx context.Context, in *AddTagInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_AddTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityMacList(ctx context.Context, in *QueryEntityMacListRequest, opts ...grpc.CallOption) (*MachineIDList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MachineIDList)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityMacList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityMachineIDList(ctx context.Context, in *QueryMachineIDInput, opts ...grpc.CallOption) (*MachineIDList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MachineIDList)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityMachineIDList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryVersionList(ctx context.Context, in *QueryVersionListRequest, opts ...grpc.CallOption) (*QueryVersionListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryVersionListResponse)
	err := c.cc.Invoke(ctx, EntityService_QueryVersionList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityVersionList(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*QueryEntityVersionListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryEntityVersionListResponse)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityVersionList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) EntityImportTemplateURL(ctx context.Context, in *EntityImportTemplateURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AOSObjectInfo)
	err := c.cc.Invoke(ctx, EntityService_EntityImportTemplateURL_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) BatchImportEntityGroupInfo(ctx context.Context, in *EntityImportInput, opts ...grpc.CallOption) (*EntityImportResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityImportResult)
	err := c.cc.Invoke(ctx, EntityService_BatchImportEntityGroupInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ModifyEntityExtInfo(ctx context.Context, in *EntityExtInfoInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_ModifyEntityExtInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) QueryEntityImportLogList(ctx context.Context, in *PageInput, opts ...grpc.CallOption) (*EntityImportLogList, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EntityImportLogList)
	err := c.cc.Invoke(ctx, EntityService_QueryEntityImportLogList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetEntityLogURL(ctx context.Context, in *GetEntityLogURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AOSObjectInfo)
	err := c.cc.Invoke(ctx, EntityService_GetEntityLogURL_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) RestoreDBNotify(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_RestoreDBNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ReportNoAdaptedAgent(ctx context.Context, in *NoAdaptedAgentInfo, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_ReportNoAdaptedAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetSimpleEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*SimpleEntityInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SimpleEntityInfo)
	err := c.cc.Invoke(ctx, EntityService_GetSimpleEntityInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ModifyMachineUserName(ctx context.Context, in *ModifyMachineUserNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EntityService_ModifyMachineUserName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) GetGroup(ctx context.Context, in *GetGroupRequest, opts ...grpc.CallOption) (*Group, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Group)
	err := c.cc.Invoke(ctx, EntityService_GetGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *entityServiceClient) ApplyAccessPermission(ctx context.Context, in *mq.AgentPacket, opts ...grpc.CallOption) (*agent.LicenseEnableResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(agent.LicenseEnableResponse)
	err := c.cc.Invoke(ctx, EntityService_ApplyAccessPermission_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EntityServiceServer is the server API for EntityService service.
// All implementations must embed UnimplementedEntityServiceServer
// for forward compatibility.
//
// EntityService 主机服务
type EntityServiceServer interface {
	// ResetOnlineStatus 重置在线状态
	ResetOnlineStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// EntityOnOffLine 主机上下线
	EntityOnOffLine(context.Context, *OnOffInfo) (*emptypb.Empty, error)
	// UpsertEntityInfo 主机信息写入接口
	UpsertEntityInfo(context.Context, *UpsertEntityInfoRequest) (*emptypb.Empty, error)
	// GetUnscopeEntityInfo 获取单一主机信息(包含删除的主机)
	GetUnscopeEntityInfo(context.Context, *EntityInfoRequest) (*EntityInfoResponse, error)
	// EntityInfo 获取单一主机信息
	EntityInfo(context.Context, *EntityInfoRequest) (*EntityInfoResponse, error)
	// GetVersion 获取主机相应信息
	GetVersion(context.Context, *EntityVersionRequest) (*EntityVersion, error)
	// EntityDetail 获取单一主机详细信息(不含asset部分)
	EntityDetail(context.Context, *EntityInfoRequest) (*EntityDetailResponse, error)
	// QueryEntityDetailList 获取主机详细信息(不含asset部分)
	QueryEntityDetailList(context.Context, *EntityDetailInput) (*EntityDetailList, error)
	// BatchQueryEntityInfo 批量查询主机信息列表
	BatchQueryEntityInfo(context.Context, *BatchQueryEntityInfoRequest) (*EntityList, error)
	// BatchQueryEntityInfoByIP 批量通过IP查询主机信息列表
	BatchQueryEntityInfoByIP(context.Context, *BatchQueryEntityInfoByIPRequest) (*EntityList, error)
	// BatchQueryEntityInfoByMachineName 批量通过主机名查询主机信息列表
	BatchQueryEntityInfoByMachineName(context.Context, *BatchQueryEntityInfoByMachineNameRequest) (*EntityList, error)
	// QueryEntityInfoList 获取主机信息列表
	QueryEntityInfoList(context.Context, *EntityInput) (*EntityList, error)
	// EntityCount 主机总数
	EntityCount(context.Context, *emptypb.Empty) (*EntityCountResponse, error)
	// OfflineEntityCount 获取离线主机数
	OfflineEntityCount(context.Context, *EntityFilter) (*EntityCountResponse, error)
	// RemoveMultiEntity 删除多个主机
	RemoveMultiEntity(context.Context, *RemoveMultiEntityRequest) (*emptypb.Empty, error)
	// RemoveEntityByFilter 按条件删除主机
	RemoveEntityByFilter(context.Context, *RemoveEntityInput) (*emptypb.Empty, error)
	// MultiEntityMoveToGroup 将多个主机移动到指定的分组
	MultiEntityMoveToGroup(context.Context, *MultiEntityMoveToGroupInput) (*emptypb.Empty, error)
	// EntityMoveToGroupByFilter 按条件将主机移动到指定的分组
	EntityMoveToGroupByFilter(context.Context, *EntityMoveToGroupInput) (*emptypb.Empty, error)
	// AddTag 添加Tag
	AddTag(context.Context, *AddTagInput) (*emptypb.Empty, error)
	// QueryEntityMacList 根据IP/Hostname返回最多1000个元素的macList
	QueryEntityMacList(context.Context, *QueryEntityMacListRequest) (*MachineIDList, error)
	// QueryEntityMachineIDList 根据groupID和系统类型（win/lin/全部），分页获取machineID列
	QueryEntityMachineIDList(context.Context, *QueryMachineIDInput) (*MachineIDList, error)
	// QueryVersionList 查询版本列表
	QueryVersionList(context.Context, *QueryVersionListRequest) (*QueryVersionListResponse, error)
	// QueryEntityVersionList 获取主机版本信息(agent、driver...)
	QueryEntityVersionList(context.Context, *EntityInfoRequest) (*QueryEntityVersionListResponse, error)
	// EntityImportTemplateURL 获取主机导入模板URL
	EntityImportTemplateURL(context.Context, *EntityImportTemplateURLRequest) (*AOSObjectInfo, error)
	// BatchImportEntityGroupInfo 批量导入主机分组信息
	BatchImportEntityGroupInfo(context.Context, *EntityImportInput) (*EntityImportResult, error)
	// ModifyEntityExtInfo 修改主机扩展信息(资产编号、资产等级...)
	ModifyEntityExtInfo(context.Context, *EntityExtInfoInput) (*emptypb.Empty, error)
	// QueryEntityImportLogList 查询entity导入日志信息
	QueryEntityImportLogList(context.Context, *PageInput) (*EntityImportLogList, error)
	// GetEntityLogURL 获取主机导入日志文件URL
	GetEntityLogURL(context.Context, *GetEntityLogURLRequest) (*AOSObjectInfo, error)
	// RestoreDBNotify 数据库还原通知
	RestoreDBNotify(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// ReportNoAdaptedAgent 接收上报未适配的主机信息
	ReportNoAdaptedAgent(context.Context, *NoAdaptedAgentInfo) (*emptypb.Empty, error)
	// GetSimpleEntityInfo 获取简单主机信息
	GetSimpleEntityInfo(context.Context, *EntityInfoRequest) (*SimpleEntityInfo, error)
	// ModifyMachineUserName 修改主机用户名
	ModifyMachineUserName(context.Context, *ModifyMachineUserNameReq) (*emptypb.Empty, error)
	// GetGroup 获取主机组信息
	GetGroup(context.Context, *GetGroupRequest) (*Group, error)
	ApplyAccessPermission(context.Context, *mq.AgentPacket) (*agent.LicenseEnableResponse, error)
	mustEmbedUnimplementedEntityServiceServer()
}

// UnimplementedEntityServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEntityServiceServer struct{}

func (UnimplementedEntityServiceServer) ResetOnlineStatus(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetOnlineStatus not implemented")
}
func (UnimplementedEntityServiceServer) EntityOnOffLine(context.Context, *OnOffInfo) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityOnOffLine not implemented")
}
func (UnimplementedEntityServiceServer) UpsertEntityInfo(context.Context, *UpsertEntityInfoRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertEntityInfo not implemented")
}
func (UnimplementedEntityServiceServer) GetUnscopeEntityInfo(context.Context, *EntityInfoRequest) (*EntityInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnscopeEntityInfo not implemented")
}
func (UnimplementedEntityServiceServer) EntityInfo(context.Context, *EntityInfoRequest) (*EntityInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityInfo not implemented")
}
func (UnimplementedEntityServiceServer) GetVersion(context.Context, *EntityVersionRequest) (*EntityVersion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVersion not implemented")
}
func (UnimplementedEntityServiceServer) EntityDetail(context.Context, *EntityInfoRequest) (*EntityDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityDetail not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityDetailList(context.Context, *EntityDetailInput) (*EntityDetailList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityDetailList not implemented")
}
func (UnimplementedEntityServiceServer) BatchQueryEntityInfo(context.Context, *BatchQueryEntityInfoRequest) (*EntityList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQueryEntityInfo not implemented")
}
func (UnimplementedEntityServiceServer) BatchQueryEntityInfoByIP(context.Context, *BatchQueryEntityInfoByIPRequest) (*EntityList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQueryEntityInfoByIP not implemented")
}
func (UnimplementedEntityServiceServer) BatchQueryEntityInfoByMachineName(context.Context, *BatchQueryEntityInfoByMachineNameRequest) (*EntityList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchQueryEntityInfoByMachineName not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityInfoList(context.Context, *EntityInput) (*EntityList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityInfoList not implemented")
}
func (UnimplementedEntityServiceServer) EntityCount(context.Context, *emptypb.Empty) (*EntityCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityCount not implemented")
}
func (UnimplementedEntityServiceServer) OfflineEntityCount(context.Context, *EntityFilter) (*EntityCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OfflineEntityCount not implemented")
}
func (UnimplementedEntityServiceServer) RemoveMultiEntity(context.Context, *RemoveMultiEntityRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveMultiEntity not implemented")
}
func (UnimplementedEntityServiceServer) RemoveEntityByFilter(context.Context, *RemoveEntityInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveEntityByFilter not implemented")
}
func (UnimplementedEntityServiceServer) MultiEntityMoveToGroup(context.Context, *MultiEntityMoveToGroupInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MultiEntityMoveToGroup not implemented")
}
func (UnimplementedEntityServiceServer) EntityMoveToGroupByFilter(context.Context, *EntityMoveToGroupInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityMoveToGroupByFilter not implemented")
}
func (UnimplementedEntityServiceServer) AddTag(context.Context, *AddTagInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTag not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityMacList(context.Context, *QueryEntityMacListRequest) (*MachineIDList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityMacList not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityMachineIDList(context.Context, *QueryMachineIDInput) (*MachineIDList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityMachineIDList not implemented")
}
func (UnimplementedEntityServiceServer) QueryVersionList(context.Context, *QueryVersionListRequest) (*QueryVersionListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryVersionList not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityVersionList(context.Context, *EntityInfoRequest) (*QueryEntityVersionListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityVersionList not implemented")
}
func (UnimplementedEntityServiceServer) EntityImportTemplateURL(context.Context, *EntityImportTemplateURLRequest) (*AOSObjectInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EntityImportTemplateURL not implemented")
}
func (UnimplementedEntityServiceServer) BatchImportEntityGroupInfo(context.Context, *EntityImportInput) (*EntityImportResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchImportEntityGroupInfo not implemented")
}
func (UnimplementedEntityServiceServer) ModifyEntityExtInfo(context.Context, *EntityExtInfoInput) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyEntityExtInfo not implemented")
}
func (UnimplementedEntityServiceServer) QueryEntityImportLogList(context.Context, *PageInput) (*EntityImportLogList, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryEntityImportLogList not implemented")
}
func (UnimplementedEntityServiceServer) GetEntityLogURL(context.Context, *GetEntityLogURLRequest) (*AOSObjectInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityLogURL not implemented")
}
func (UnimplementedEntityServiceServer) RestoreDBNotify(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestoreDBNotify not implemented")
}
func (UnimplementedEntityServiceServer) ReportNoAdaptedAgent(context.Context, *NoAdaptedAgentInfo) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportNoAdaptedAgent not implemented")
}
func (UnimplementedEntityServiceServer) GetSimpleEntityInfo(context.Context, *EntityInfoRequest) (*SimpleEntityInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSimpleEntityInfo not implemented")
}
func (UnimplementedEntityServiceServer) ModifyMachineUserName(context.Context, *ModifyMachineUserNameReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyMachineUserName not implemented")
}
func (UnimplementedEntityServiceServer) GetGroup(context.Context, *GetGroupRequest) (*Group, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroup not implemented")
}
func (UnimplementedEntityServiceServer) ApplyAccessPermission(context.Context, *mq.AgentPacket) (*agent.LicenseEnableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyAccessPermission not implemented")
}
func (UnimplementedEntityServiceServer) mustEmbedUnimplementedEntityServiceServer() {}
func (UnimplementedEntityServiceServer) testEmbeddedByValue()                       {}

// UnsafeEntityServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EntityServiceServer will
// result in compilation errors.
type UnsafeEntityServiceServer interface {
	mustEmbedUnimplementedEntityServiceServer()
}

func RegisterEntityServiceServer(s grpc.ServiceRegistrar, srv EntityServiceServer) {
	// If the following call pancis, it indicates UnimplementedEntityServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EntityService_ServiceDesc, srv)
}

func _EntityService_ResetOnlineStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ResetOnlineStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_ResetOnlineStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ResetOnlineStatus(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityOnOffLine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnOffInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityOnOffLine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityOnOffLine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityOnOffLine(ctx, req.(*OnOffInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_UpsertEntityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertEntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).UpsertEntityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_UpsertEntityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).UpsertEntityInfo(ctx, req.(*UpsertEntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetUnscopeEntityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetUnscopeEntityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_GetUnscopeEntityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetUnscopeEntityInfo(ctx, req.(*EntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityInfo(ctx, req.(*EntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityVersionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_GetVersion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetVersion(ctx, req.(*EntityVersionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityDetail(ctx, req.(*EntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityDetailInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityDetailList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityDetailList(ctx, req.(*EntityDetailInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_BatchQueryEntityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQueryEntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).BatchQueryEntityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_BatchQueryEntityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).BatchQueryEntityInfo(ctx, req.(*BatchQueryEntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_BatchQueryEntityInfoByIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQueryEntityInfoByIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).BatchQueryEntityInfoByIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_BatchQueryEntityInfoByIP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).BatchQueryEntityInfoByIP(ctx, req.(*BatchQueryEntityInfoByIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_BatchQueryEntityInfoByMachineName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchQueryEntityInfoByMachineNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).BatchQueryEntityInfoByMachineName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_BatchQueryEntityInfoByMachineName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).BatchQueryEntityInfoByMachineName(ctx, req.(*BatchQueryEntityInfoByMachineNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityInfoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityInfoList(ctx, req.(*EntityInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityCount(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_OfflineEntityCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityFilter)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).OfflineEntityCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_OfflineEntityCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).OfflineEntityCount(ctx, req.(*EntityFilter))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_RemoveMultiEntity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveMultiEntityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).RemoveMultiEntity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_RemoveMultiEntity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).RemoveMultiEntity(ctx, req.(*RemoveMultiEntityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_RemoveEntityByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveEntityInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).RemoveEntityByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_RemoveEntityByFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).RemoveEntityByFilter(ctx, req.(*RemoveEntityInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_MultiEntityMoveToGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MultiEntityMoveToGroupInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).MultiEntityMoveToGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_MultiEntityMoveToGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).MultiEntityMoveToGroup(ctx, req.(*MultiEntityMoveToGroupInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityMoveToGroupByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityMoveToGroupInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityMoveToGroupByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityMoveToGroupByFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityMoveToGroupByFilter(ctx, req.(*EntityMoveToGroupInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_AddTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTagInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).AddTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_AddTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).AddTag(ctx, req.(*AddTagInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityMacList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryEntityMacListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityMacList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityMacList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityMacList(ctx, req.(*QueryEntityMacListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityMachineIDList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryMachineIDInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityMachineIDList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityMachineIDList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityMachineIDList(ctx, req.(*QueryMachineIDInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryVersionListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryVersionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryVersionList(ctx, req.(*QueryVersionListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityVersionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityVersionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityVersionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityVersionList(ctx, req.(*EntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_EntityImportTemplateURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityImportTemplateURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).EntityImportTemplateURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_EntityImportTemplateURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).EntityImportTemplateURL(ctx, req.(*EntityImportTemplateURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_BatchImportEntityGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityImportInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).BatchImportEntityGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_BatchImportEntityGroupInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).BatchImportEntityGroupInfo(ctx, req.(*EntityImportInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ModifyEntityExtInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityExtInfoInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ModifyEntityExtInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_ModifyEntityExtInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ModifyEntityExtInfo(ctx, req.(*EntityExtInfoInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_QueryEntityImportLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageInput)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).QueryEntityImportLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_QueryEntityImportLogList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).QueryEntityImportLogList(ctx, req.(*PageInput))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetEntityLogURL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEntityLogURLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetEntityLogURL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_GetEntityLogURL_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetEntityLogURL(ctx, req.(*GetEntityLogURLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_RestoreDBNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).RestoreDBNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_RestoreDBNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).RestoreDBNotify(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ReportNoAdaptedAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoAdaptedAgentInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ReportNoAdaptedAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_ReportNoAdaptedAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ReportNoAdaptedAgent(ctx, req.(*NoAdaptedAgentInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetSimpleEntityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EntityInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetSimpleEntityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_GetSimpleEntityInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetSimpleEntityInfo(ctx, req.(*EntityInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ModifyMachineUserName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyMachineUserNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ModifyMachineUserName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_ModifyMachineUserName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ModifyMachineUserName(ctx, req.(*ModifyMachineUserNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_GetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).GetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_GetGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).GetGroup(ctx, req.(*GetGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EntityService_ApplyAccessPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mq.AgentPacket)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EntityServiceServer).ApplyAccessPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EntityService_ApplyAccessPermission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EntityServiceServer).ApplyAccessPermission(ctx, req.(*mq.AgentPacket))
	}
	return interceptor(ctx, in, info, handler)
}

// EntityService_ServiceDesc is the grpc.ServiceDesc for EntityService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EntityService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "braum.EntityService",
	HandlerType: (*EntityServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ResetOnlineStatus",
			Handler:    _EntityService_ResetOnlineStatus_Handler,
		},
		{
			MethodName: "EntityOnOffLine",
			Handler:    _EntityService_EntityOnOffLine_Handler,
		},
		{
			MethodName: "UpsertEntityInfo",
			Handler:    _EntityService_UpsertEntityInfo_Handler,
		},
		{
			MethodName: "GetUnscopeEntityInfo",
			Handler:    _EntityService_GetUnscopeEntityInfo_Handler,
		},
		{
			MethodName: "EntityInfo",
			Handler:    _EntityService_EntityInfo_Handler,
		},
		{
			MethodName: "GetVersion",
			Handler:    _EntityService_GetVersion_Handler,
		},
		{
			MethodName: "EntityDetail",
			Handler:    _EntityService_EntityDetail_Handler,
		},
		{
			MethodName: "QueryEntityDetailList",
			Handler:    _EntityService_QueryEntityDetailList_Handler,
		},
		{
			MethodName: "BatchQueryEntityInfo",
			Handler:    _EntityService_BatchQueryEntityInfo_Handler,
		},
		{
			MethodName: "BatchQueryEntityInfoByIP",
			Handler:    _EntityService_BatchQueryEntityInfoByIP_Handler,
		},
		{
			MethodName: "BatchQueryEntityInfoByMachineName",
			Handler:    _EntityService_BatchQueryEntityInfoByMachineName_Handler,
		},
		{
			MethodName: "QueryEntityInfoList",
			Handler:    _EntityService_QueryEntityInfoList_Handler,
		},
		{
			MethodName: "EntityCount",
			Handler:    _EntityService_EntityCount_Handler,
		},
		{
			MethodName: "OfflineEntityCount",
			Handler:    _EntityService_OfflineEntityCount_Handler,
		},
		{
			MethodName: "RemoveMultiEntity",
			Handler:    _EntityService_RemoveMultiEntity_Handler,
		},
		{
			MethodName: "RemoveEntityByFilter",
			Handler:    _EntityService_RemoveEntityByFilter_Handler,
		},
		{
			MethodName: "MultiEntityMoveToGroup",
			Handler:    _EntityService_MultiEntityMoveToGroup_Handler,
		},
		{
			MethodName: "EntityMoveToGroupByFilter",
			Handler:    _EntityService_EntityMoveToGroupByFilter_Handler,
		},
		{
			MethodName: "AddTag",
			Handler:    _EntityService_AddTag_Handler,
		},
		{
			MethodName: "QueryEntityMacList",
			Handler:    _EntityService_QueryEntityMacList_Handler,
		},
		{
			MethodName: "QueryEntityMachineIDList",
			Handler:    _EntityService_QueryEntityMachineIDList_Handler,
		},
		{
			MethodName: "QueryVersionList",
			Handler:    _EntityService_QueryVersionList_Handler,
		},
		{
			MethodName: "QueryEntityVersionList",
			Handler:    _EntityService_QueryEntityVersionList_Handler,
		},
		{
			MethodName: "EntityImportTemplateURL",
			Handler:    _EntityService_EntityImportTemplateURL_Handler,
		},
		{
			MethodName: "BatchImportEntityGroupInfo",
			Handler:    _EntityService_BatchImportEntityGroupInfo_Handler,
		},
		{
			MethodName: "ModifyEntityExtInfo",
			Handler:    _EntityService_ModifyEntityExtInfo_Handler,
		},
		{
			MethodName: "QueryEntityImportLogList",
			Handler:    _EntityService_QueryEntityImportLogList_Handler,
		},
		{
			MethodName: "GetEntityLogURL",
			Handler:    _EntityService_GetEntityLogURL_Handler,
		},
		{
			MethodName: "RestoreDBNotify",
			Handler:    _EntityService_RestoreDBNotify_Handler,
		},
		{
			MethodName: "ReportNoAdaptedAgent",
			Handler:    _EntityService_ReportNoAdaptedAgent_Handler,
		},
		{
			MethodName: "GetSimpleEntityInfo",
			Handler:    _EntityService_GetSimpleEntityInfo_Handler,
		},
		{
			MethodName: "ModifyMachineUserName",
			Handler:    _EntityService_ModifyMachineUserName_Handler,
		},
		{
			MethodName: "GetGroup",
			Handler:    _EntityService_GetGroup_Handler,
		},
		{
			MethodName: "ApplyAccessPermission",
			Handler:    _EntityService_ApplyAccessPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "braum/entity.proto",
}

const (
	Braum_LogCollectionTaskCreate_FullMethodName   = "/braum.Braum/LogCollectionTaskCreate"
	Braum_LogCollectionFileUploaded_FullMethodName = "/braum.Braum/LogCollectionFileUploaded"
	Braum_LogCollectionTaskList_FullMethodName     = "/braum.Braum/LogCollectionTaskList"
)

// BraumClient is the client API for Braum service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Braum 服务
type BraumClient interface {
	// LogCollectionTaskCreate 创建日志收集任务
	LogCollectionTaskCreate(ctx context.Context, in *LogCollectionCreateReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// LogCollectionFileUploaded 通知文件上传结果
	LogCollectionFileUploaded(ctx context.Context, in *LogCollectionUploadedReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// LogCollectionTaskList 查询日志收集任务列表
	LogCollectionTaskList(ctx context.Context, in *LogCollectionListReq, opts ...grpc.CallOption) (*LogCollectionListResp, error)
}

type braumClient struct {
	cc grpc.ClientConnInterface
}

func NewBraumClient(cc grpc.ClientConnInterface) BraumClient {
	return &braumClient{cc}
}

func (c *braumClient) LogCollectionTaskCreate(ctx context.Context, in *LogCollectionCreateReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Braum_LogCollectionTaskCreate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *braumClient) LogCollectionFileUploaded(ctx context.Context, in *LogCollectionUploadedReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Braum_LogCollectionFileUploaded_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *braumClient) LogCollectionTaskList(ctx context.Context, in *LogCollectionListReq, opts ...grpc.CallOption) (*LogCollectionListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogCollectionListResp)
	err := c.cc.Invoke(ctx, Braum_LogCollectionTaskList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BraumServer is the server API for Braum service.
// All implementations must embed UnimplementedBraumServer
// for forward compatibility.
//
// Braum 服务
type BraumServer interface {
	// LogCollectionTaskCreate 创建日志收集任务
	LogCollectionTaskCreate(context.Context, *LogCollectionCreateReq) (*emptypb.Empty, error)
	// LogCollectionFileUploaded 通知文件上传结果
	LogCollectionFileUploaded(context.Context, *LogCollectionUploadedReq) (*emptypb.Empty, error)
	// LogCollectionTaskList 查询日志收集任务列表
	LogCollectionTaskList(context.Context, *LogCollectionListReq) (*LogCollectionListResp, error)
	mustEmbedUnimplementedBraumServer()
}

// UnimplementedBraumServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBraumServer struct{}

func (UnimplementedBraumServer) LogCollectionTaskCreate(context.Context, *LogCollectionCreateReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogCollectionTaskCreate not implemented")
}
func (UnimplementedBraumServer) LogCollectionFileUploaded(context.Context, *LogCollectionUploadedReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogCollectionFileUploaded not implemented")
}
func (UnimplementedBraumServer) LogCollectionTaskList(context.Context, *LogCollectionListReq) (*LogCollectionListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LogCollectionTaskList not implemented")
}
func (UnimplementedBraumServer) mustEmbedUnimplementedBraumServer() {}
func (UnimplementedBraumServer) testEmbeddedByValue()               {}

// UnsafeBraumServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BraumServer will
// result in compilation errors.
type UnsafeBraumServer interface {
	mustEmbedUnimplementedBraumServer()
}

func RegisterBraumServer(s grpc.ServiceRegistrar, srv BraumServer) {
	// If the following call pancis, it indicates UnimplementedBraumServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Braum_ServiceDesc, srv)
}

func _Braum_LogCollectionTaskCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogCollectionCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BraumServer).LogCollectionTaskCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Braum_LogCollectionTaskCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BraumServer).LogCollectionTaskCreate(ctx, req.(*LogCollectionCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Braum_LogCollectionFileUploaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogCollectionUploadedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BraumServer).LogCollectionFileUploaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Braum_LogCollectionFileUploaded_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BraumServer).LogCollectionFileUploaded(ctx, req.(*LogCollectionUploadedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Braum_LogCollectionTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogCollectionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BraumServer).LogCollectionTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Braum_LogCollectionTaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BraumServer).LogCollectionTaskList(ctx, req.(*LogCollectionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Braum_ServiceDesc is the grpc.ServiceDesc for Braum service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Braum_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "braum.Braum",
	HandlerType: (*BraumServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LogCollectionTaskCreate",
			Handler:    _Braum_LogCollectionTaskCreate_Handler,
		},
		{
			MethodName: "LogCollectionFileUploaded",
			Handler:    _Braum_LogCollectionFileUploaded_Handler,
		},
		{
			MethodName: "LogCollectionTaskList",
			Handler:    _Braum_LogCollectionTaskList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "braum/entity.proto",
}
