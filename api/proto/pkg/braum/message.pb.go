// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: braum/message.proto

package braum

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MsgHostDeleted struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIds []string `protobuf:"bytes,1,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
}

func (x *MsgHostDeleted) Reset() {
	*x = MsgHostDeleted{}
	mi := &file_braum_message_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgHostDeleted) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgHostDeleted) ProtoMessage() {}

func (x *MsgHostDeleted) ProtoReflect() protoreflect.Message {
	mi := &file_braum_message_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgHostDeleted.ProtoReflect.Descriptor instead.
func (*MsgHostDeleted) Descriptor() ([]byte, []int) {
	return file_braum_message_proto_rawDescGZIP(), []int{0}
}

func (x *MsgHostDeleted) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

type MsgGroupAdded struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId  int64 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	ParentId int64 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
}

func (x *MsgGroupAdded) Reset() {
	*x = MsgGroupAdded{}
	mi := &file_braum_message_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgGroupAdded) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgGroupAdded) ProtoMessage() {}

func (x *MsgGroupAdded) ProtoReflect() protoreflect.Message {
	mi := &file_braum_message_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgGroupAdded.ProtoReflect.Descriptor instead.
func (*MsgGroupAdded) Descriptor() ([]byte, []int) {
	return file_braum_message_proto_rawDescGZIP(), []int{1}
}

func (x *MsgGroupAdded) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MsgGroupAdded) GetParentId() int64 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

type MsgGroupUpdated struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId     int64 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	OldParentId int64 `protobuf:"varint,2,opt,name=old_parent_id,json=oldParentId,proto3" json:"old_parent_id,omitempty"`
	NewParentId int64 `protobuf:"varint,3,opt,name=new_parent_id,json=newParentId,proto3" json:"new_parent_id,omitempty"`
}

func (x *MsgGroupUpdated) Reset() {
	*x = MsgGroupUpdated{}
	mi := &file_braum_message_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgGroupUpdated) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgGroupUpdated) ProtoMessage() {}

func (x *MsgGroupUpdated) ProtoReflect() protoreflect.Message {
	mi := &file_braum_message_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgGroupUpdated.ProtoReflect.Descriptor instead.
func (*MsgGroupUpdated) Descriptor() ([]byte, []int) {
	return file_braum_message_proto_rawDescGZIP(), []int{2}
}

func (x *MsgGroupUpdated) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MsgGroupUpdated) GetOldParentId() int64 {
	if x != nil {
		return x.OldParentId
	}
	return 0
}

func (x *MsgGroupUpdated) GetNewParentId() int64 {
	if x != nil {
		return x.NewParentId
	}
	return 0
}

type MsgGroupDeleted struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId []int64 `protobuf:"varint,1,rep,packed,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
}

func (x *MsgGroupDeleted) Reset() {
	*x = MsgGroupDeleted{}
	mi := &file_braum_message_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgGroupDeleted) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgGroupDeleted) ProtoMessage() {}

func (x *MsgGroupDeleted) ProtoReflect() protoreflect.Message {
	mi := &file_braum_message_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgGroupDeleted.ProtoReflect.Descriptor instead.
func (*MsgGroupDeleted) Descriptor() ([]byte, []int) {
	return file_braum_message_proto_rawDescGZIP(), []int{3}
}

func (x *MsgGroupDeleted) GetGroupId() []int64 {
	if x != nil {
		return x.GroupId
	}
	return nil
}

var File_braum_message_proto protoreflect.FileDescriptor

var file_braum_message_proto_rawDesc = []byte{
	0x0a, 0x13, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x2f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x62, 0x72, 0x61, 0x75, 0x6d, 0x22, 0x31, 0x0a, 0x0e,
	0x4d, 0x73, 0x67, 0x48, 0x6f, 0x73, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x22,
	0x47, 0x0a, 0x0d, 0x4d, 0x73, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64, 0x65, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x74, 0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f,
	0x6c, 0x64, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65,
	0x77, 0x5f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x2c,
	0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x42, 0x12, 0x5a, 0x10,
	0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x62, 0x72, 0x61, 0x75, 0x6d,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_braum_message_proto_rawDescOnce sync.Once
	file_braum_message_proto_rawDescData = file_braum_message_proto_rawDesc
)

func file_braum_message_proto_rawDescGZIP() []byte {
	file_braum_message_proto_rawDescOnce.Do(func() {
		file_braum_message_proto_rawDescData = protoimpl.X.CompressGZIP(file_braum_message_proto_rawDescData)
	})
	return file_braum_message_proto_rawDescData
}

var file_braum_message_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_braum_message_proto_goTypes = []any{
	(*MsgHostDeleted)(nil),  // 0: braum.MsgHostDeleted
	(*MsgGroupAdded)(nil),   // 1: braum.MsgGroupAdded
	(*MsgGroupUpdated)(nil), // 2: braum.MsgGroupUpdated
	(*MsgGroupDeleted)(nil), // 3: braum.MsgGroupDeleted
}
var file_braum_message_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_braum_message_proto_init() }
func file_braum_message_proto_init() {
	if File_braum_message_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_braum_message_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_braum_message_proto_goTypes,
		DependencyIndexes: file_braum_message_proto_depIdxs,
		MessageInfos:      file_braum_message_proto_msgTypes,
	}.Build()
	File_braum_message_proto = out.File
	file_braum_message_proto_rawDesc = nil
	file_braum_message_proto_goTypes = nil
	file_braum_message_proto_depIdxs = nil
}
