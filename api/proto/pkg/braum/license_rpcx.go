package braum

import (
	"context"

	"git.anxin.com/v01-cluster/vapi/pkg/agent"
	"git.anxin.com/v01-cluster/vapi/pkg/mq"
	"google.golang.org/grpc"
)

func (c *RpcxClient) ApplyAccessPermission(ctx context.Context, in *mq.AgentPacket, opts ...grpc.CallOption) (*agent.LicenseEnableResponse, error) {
	resp := &agent.LicenseEnableResponse{}
	if err := c.call(ctx, "ApplyAccessPermission", in, resp); err != nil {
		return nil, err
	}

	return resp, nil
}
