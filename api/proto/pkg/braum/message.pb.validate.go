// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: braum/message.proto

package braum

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MsgHostDeleted with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgHostDeleted) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgHostDeleted with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgHostDeletedMultiError,
// or nil if none found.
func (m *MsgHostDeleted) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgHostDeleted) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MsgHostDeletedMultiError(errors)
	}

	return nil
}

// MsgHostDeletedMultiError is an error wrapping multiple validation errors
// returned by MsgHostDeleted.ValidateAll() if the designated constraints
// aren't met.
type MsgHostDeletedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgHostDeletedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgHostDeletedMultiError) AllErrors() []error { return m }

// MsgHostDeletedValidationError is the validation error returned by
// MsgHostDeleted.Validate if the designated constraints aren't met.
type MsgHostDeletedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgHostDeletedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgHostDeletedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgHostDeletedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgHostDeletedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgHostDeletedValidationError) ErrorName() string { return "MsgHostDeletedValidationError" }

// Error satisfies the builtin error interface
func (e MsgHostDeletedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgHostDeleted.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgHostDeletedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgHostDeletedValidationError{}

// Validate checks the field values on MsgGroupAdded with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgGroupAdded) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgGroupAdded with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgGroupAddedMultiError, or
// nil if none found.
func (m *MsgGroupAdded) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgGroupAdded) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	// no validation rules for ParentId

	if len(errors) > 0 {
		return MsgGroupAddedMultiError(errors)
	}

	return nil
}

// MsgGroupAddedMultiError is an error wrapping multiple validation errors
// returned by MsgGroupAdded.ValidateAll() if the designated constraints
// aren't met.
type MsgGroupAddedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgGroupAddedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgGroupAddedMultiError) AllErrors() []error { return m }

// MsgGroupAddedValidationError is the validation error returned by
// MsgGroupAdded.Validate if the designated constraints aren't met.
type MsgGroupAddedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgGroupAddedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgGroupAddedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgGroupAddedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgGroupAddedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgGroupAddedValidationError) ErrorName() string { return "MsgGroupAddedValidationError" }

// Error satisfies the builtin error interface
func (e MsgGroupAddedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgGroupAdded.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgGroupAddedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgGroupAddedValidationError{}

// Validate checks the field values on MsgGroupUpdated with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MsgGroupUpdated) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgGroupUpdated with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MsgGroupUpdatedMultiError, or nil if none found.
func (m *MsgGroupUpdated) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgGroupUpdated) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	// no validation rules for OldParentId

	// no validation rules for NewParentId

	if len(errors) > 0 {
		return MsgGroupUpdatedMultiError(errors)
	}

	return nil
}

// MsgGroupUpdatedMultiError is an error wrapping multiple validation errors
// returned by MsgGroupUpdated.ValidateAll() if the designated constraints
// aren't met.
type MsgGroupUpdatedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgGroupUpdatedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgGroupUpdatedMultiError) AllErrors() []error { return m }

// MsgGroupUpdatedValidationError is the validation error returned by
// MsgGroupUpdated.Validate if the designated constraints aren't met.
type MsgGroupUpdatedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgGroupUpdatedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgGroupUpdatedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgGroupUpdatedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgGroupUpdatedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgGroupUpdatedValidationError) ErrorName() string { return "MsgGroupUpdatedValidationError" }

// Error satisfies the builtin error interface
func (e MsgGroupUpdatedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgGroupUpdated.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgGroupUpdatedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgGroupUpdatedValidationError{}

// Validate checks the field values on MsgGroupDeleted with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MsgGroupDeleted) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgGroupDeleted with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MsgGroupDeletedMultiError, or nil if none found.
func (m *MsgGroupDeleted) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgGroupDeleted) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MsgGroupDeletedMultiError(errors)
	}

	return nil
}

// MsgGroupDeletedMultiError is an error wrapping multiple validation errors
// returned by MsgGroupDeleted.ValidateAll() if the designated constraints
// aren't met.
type MsgGroupDeletedMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgGroupDeletedMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgGroupDeletedMultiError) AllErrors() []error { return m }

// MsgGroupDeletedValidationError is the validation error returned by
// MsgGroupDeleted.Validate if the designated constraints aren't met.
type MsgGroupDeletedValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgGroupDeletedValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgGroupDeletedValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgGroupDeletedValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgGroupDeletedValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgGroupDeletedValidationError) ErrorName() string { return "MsgGroupDeletedValidationError" }

// Error satisfies the builtin error interface
func (e MsgGroupDeletedValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgGroupDeleted.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgGroupDeletedValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgGroupDeletedValidationError{}
