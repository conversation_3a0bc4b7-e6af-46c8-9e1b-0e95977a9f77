package braum

import (
	context "context"

	"git.anxin.com/pkg/axgolib/res"
	"git.anxin.com/pkg/golib/base"
	"git.anxin.com/pkg/golib/errs"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

var _ EntityServiceClient = &RpcxClient{}

type RpcxClient struct {
	client *base.RpcxClient
}

func NewRpcxClient(client *base.RpcxClient) (*RpcxClient, error) {
	return &RpcxClient{
		client: client,
	}, nil
}

// ResetOnlineStatus 重置在线状态
func (c *RpcxClient) ResetOnlineStatus(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "ResetOnlineStatus", in, nil)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityOnOffLine 主机上下线
func (c *RpcxClient) EntityOnOffLine(ctx context.Context, in *OnOffInfo, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "EntityOnOffLine", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UpsertEntityInfo 主机信息写入接口
func (c *RpcxClient) UpsertEntityInfo(ctx context.Context, in *UpsertEntityInfoRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "UpsertEntityInfo", in.GetEntities(), nil)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetUnscopeEntityInfo 获取单一主机信息(包含删除的主机)
func (c *RpcxClient) GetUnscopeEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error) {
	resp := &EntityInfoResponse{}
	err := c.call(ctx, "GetUnscopeEntityInfo", in.GetMachineId(), resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityInfo 获取单一主机信息
func (c *RpcxClient) EntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityInfoResponse, error) {
	entityInfo := &EntityInfo{}
	err := c.call(ctx, "EntityInfo", in.GetMachineId(), entityInfo)
	if err != nil {
		return nil, err
	}
	return &EntityInfoResponse{
		EntityInfo: entityInfo,
	}, nil
}

// GetVersion 获取主机相应信息
func (c *RpcxClient) GetVersion(ctx context.Context, in *EntityVersionRequest, opts ...grpc.CallOption) (*EntityVersion, error) {
	resp := &EntityVersion{}
	err := c.call(ctx, "GetVersion", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityDetail 获取单一主机详细信息(不含asset部分)
func (c *RpcxClient) EntityDetail(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*EntityDetailResponse, error) {
	resp := &EntityDetailResponse{}
	err := c.call(ctx, "EntityDetail", in.GetMachineId(), resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryEntityDetailList 获取主机详细信息(不含asset部分)
func (c *RpcxClient) QueryEntityDetailList(ctx context.Context, in *EntityDetailInput, opts ...grpc.CallOption) (*EntityDetailList, error) {
	resp := &EntityDetailList{}
	err := c.call(ctx, "QueryEntityDetailList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// BatchQueryEntityInfo 批量查询主机信息列表
func (c *RpcxClient) BatchQueryEntityInfo(ctx context.Context, in *BatchQueryEntityInfoRequest, opts ...grpc.CallOption) (*EntityList, error) {
	entityInfoList := make([]*EntityInfo, 0)
	err := c.call(ctx, "BatchQueryEntityInfo", in.GetMachineIds(), &entityInfoList)
	if err != nil {
		return nil, err
	}
	return &EntityList{
		Items: entityInfoList,
	}, nil
}

// BatchQueryEntityInfoByIP 批量通过IP查询主机信息列表
func (c *RpcxClient) BatchQueryEntityInfoByIP(ctx context.Context, in *BatchQueryEntityInfoByIPRequest, opts ...grpc.CallOption) (*EntityList, error) {
	entityInfoList := make([]*EntityInfo, 0)
	err := c.call(ctx, "BatchQueryEntityInfoByIP", in.GetIps(), &entityInfoList)
	if err != nil {
		return nil, err
	}
	return &EntityList{
		Items: entityInfoList,
	}, nil
}

// BatchQueryEntityInfoByMachineName 批量通过主机名查询主机信息列表
func (c *RpcxClient) BatchQueryEntityInfoByMachineName(ctx context.Context, in *BatchQueryEntityInfoByMachineNameRequest, opts ...grpc.CallOption) (*EntityList, error) {
	entityInfoList := make([]*EntityInfo, 0)
	err := c.call(ctx, "BatchQueryEntityInfoByMachineName", in.GetMachineNames(), &entityInfoList)
	if err != nil {
		return nil, err
	}
	return &EntityList{
		Items: entityInfoList,
	}, nil
}

// QueryEntityInfoList 获取主机信息列表
func (c *RpcxClient) QueryEntityInfoList(ctx context.Context, in *EntityInput, opts ...grpc.CallOption) (*EntityList, error) {
	resp := &EntityList{}
	err := c.call(ctx, "QueryEntityInfoList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityCount 主机总数
func (c *RpcxClient) EntityCount(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*EntityCountResponse, error) {
	resp := &EntityCountResponse{}
	err := c.call(ctx, "EntityCount", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// OfflineEntityCount 获取离线主机数
func (c *RpcxClient) OfflineEntityCount(ctx context.Context, in *EntityFilter, opts ...grpc.CallOption) (*EntityCountResponse, error) {
	resp := &EntityCountResponse{}
	err := c.call(ctx, "OfflineEntityCount", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RemoveMultiEntity 删除多个主机
func (c *RpcxClient) RemoveMultiEntity(ctx context.Context, in *RemoveMultiEntityRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "RemoveMultiEntity", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RemoveEntityByFilter 按条件删除主机
func (c *RpcxClient) RemoveEntityByFilter(ctx context.Context, in *RemoveEntityInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "RemoveEntityByFilter", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// MultiEntityMoveToGroup 将多个主机移动到指定的分组
func (c *RpcxClient) MultiEntityMoveToGroup(ctx context.Context, in *MultiEntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "MultiEntityMoveToGroup", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityMoveToGroupByFilter 按条件将主机移动到指定的分组
func (c *RpcxClient) EntityMoveToGroupByFilter(ctx context.Context, in *EntityMoveToGroupInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "EntityMoveToGroupByFilter", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// AddTag 添加Tag
func (c *RpcxClient) AddTag(ctx context.Context, in *AddTagInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "AddTag", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryEntityMacList 根据IP/Hostname返回最多1000个元素的macList
func (c *RpcxClient) QueryEntityMacList(ctx context.Context, in *QueryEntityMacListRequest, opts ...grpc.CallOption) (*MachineIDList, error) {
	resp := &MachineIDList{}
	err := c.call(ctx, "QueryEntityMacList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryEntityMachineIDList 根据groupID和系统类型（win/lin/全部），分页获取machineID列
func (c *RpcxClient) QueryEntityMachineIDList(ctx context.Context, in *QueryMachineIDInput, opts ...grpc.CallOption) (*MachineIDList, error) {
	resp := &MachineIDList{}
	err := c.call(ctx, "QueryEntityMachineIDList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryVersionList 查询版本列表
func (c *RpcxClient) QueryVersionList(ctx context.Context, in *QueryVersionListRequest, opts ...grpc.CallOption) (*QueryVersionListResponse, error) {
	resp := &QueryVersionListResponse{}
	err := c.call(ctx, "QueryVersionList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryEntityVersionList 获取主机版本信息(agent、driver...)
func (c *RpcxClient) QueryEntityVersionList(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*QueryEntityVersionListResponse, error) {
	resp := &QueryEntityVersionListResponse{}
	err := c.call(ctx, "QueryEntityVersionList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// EntityImportTemplateURL 获取主机导入模板URL
func (c *RpcxClient) EntityImportTemplateURL(ctx context.Context, in *EntityImportTemplateURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error) {
	resp := &AOSObjectInfo{}
	err := c.call(ctx, "EntityImportTemplateURL", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// BatchImportEntityGroupInfo 批量导入主机分组信息
func (c *RpcxClient) BatchImportEntityGroupInfo(ctx context.Context, in *EntityImportInput, opts ...grpc.CallOption) (*EntityImportResult, error) {
	resp := &EntityImportResult{}
	err := c.call(ctx, "BatchImportEntityGroupInfo", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ModifyEntityExtInfo 修改主机扩展信息(资产编号、资产等级...)
func (c *RpcxClient) ModifyEntityExtInfo(ctx context.Context, in *EntityExtInfoInput, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "ModifyEntityExtInfo", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// QueryEntityImportLogList 查询entity导入日志信息
func (c *RpcxClient) QueryEntityImportLogList(ctx context.Context, in *PageInput, opts ...grpc.CallOption) (*EntityImportLogList, error) {
	resp := &EntityImportLogList{}
	err := c.call(ctx, "QueryEntityImportLogList", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetEntityLogURL 获取主机导入日志文件URL
func (c *RpcxClient) GetEntityLogURL(ctx context.Context, in *GetEntityLogURLRequest, opts ...grpc.CallOption) (*AOSObjectInfo, error) {
	resp := &AOSObjectInfo{}
	err := c.call(ctx, "GetEntityLogURL", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// RestoreDBNotify 数据库还原通知
func (c *RpcxClient) RestoreDBNotify(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "RestoreDBNotify", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ReportNoAdaptedAgent 接收上报未适配的主机信息
func (c *RpcxClient) ReportNoAdaptedAgent(ctx context.Context, in *NoAdaptedAgentInfo, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "ReportNoAdaptedAgent", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetSimpleEntityInfo 获取简单主机信息
func (c *RpcxClient) GetSimpleEntityInfo(ctx context.Context, in *EntityInfoRequest, opts ...grpc.CallOption) (*SimpleEntityInfo, error) {
	resp := &SimpleEntityInfo{}
	err := c.call(ctx, "GetSimpleEntityInfo", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// ModifyMachineUserName 修改主机用户名
func (c *RpcxClient) ModifyMachineUserName(ctx context.Context, in *ModifyMachineUserNameReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	resp := &emptypb.Empty{}
	err := c.call(ctx, "ModifyMachineUserName", in, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// GetGroup 获取主机组信息
func (c *RpcxClient) GetGroup(ctx context.Context, in *GetGroupRequest, opts ...grpc.CallOption) (*Group, error) {
	resp := &Group{}
	err := c.call(ctx, "GetGroup", in.Id, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *RpcxClient) call(ctx context.Context, method string, args any, resData any) error {
	var reply res.Response
	if err := c.client.P2PCall(ctx, method, args, &reply); err != nil {
		return err
	}
	if reply.IsError() {
		return errs.New(reply.Code, reply.Message)
	}
	if resData != nil {
		reply.ReadData(resData)
	}
	return nil
}
