// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: veigar/veigar.proto

package veigar

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SearchFilesReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchFilesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchFilesReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchFilesReqMultiError,
// or nil if none found.
func (m *SearchFilesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchFilesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnlyDb

	if len(errors) > 0 {
		return SearchFilesReqMultiError(errors)
	}

	return nil
}

// SearchFilesReqMultiError is an error wrapping multiple validation errors
// returned by SearchFilesReq.ValidateAll() if the designated constraints
// aren't met.
type SearchFilesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchFilesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchFilesReqMultiError) AllErrors() []error { return m }

// SearchFilesReqValidationError is the validation error returned by
// SearchFilesReq.Validate if the designated constraints aren't met.
type SearchFilesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchFilesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchFilesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchFilesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchFilesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchFilesReqValidationError) ErrorName() string { return "SearchFilesReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchFilesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchFilesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchFilesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchFilesReqValidationError{}

// Validate checks the field values on FileIntel with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileIntel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileIntel with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileIntelMultiError, or nil
// if none found.
func (m *FileIntel) ValidateAll() error {
	return m.validate(true)
}

func (m *FileIntel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for State

	// no validation rules for Source

	// no validation rules for Vendor

	// no validation rules for Confidence

	// no validation rules for Severity

	// no validation rules for ExtraInfo

	if len(errors) > 0 {
		return FileIntelMultiError(errors)
	}

	return nil
}

// FileIntelMultiError is an error wrapping multiple validation errors returned
// by FileIntel.ValidateAll() if the designated constraints aren't met.
type FileIntelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileIntelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileIntelMultiError) AllErrors() []error { return m }

// FileIntelValidationError is the validation error returned by
// FileIntel.Validate if the designated constraints aren't met.
type FileIntelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileIntelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileIntelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileIntelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileIntelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileIntelValidationError) ErrorName() string { return "FileIntelValidationError" }

// Error satisfies the builtin error interface
func (e FileIntelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileIntel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileIntelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileIntelValidationError{}

// Validate checks the field values on SearchFilesResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchFilesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchFilesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchFilesRespMultiError, or nil if none found.
func (m *SearchFilesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchFilesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIntel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchFilesRespValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchFilesRespValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchFilesRespValidationError{
					field:  fmt.Sprintf("Intel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchFilesRespMultiError(errors)
	}

	return nil
}

// SearchFilesRespMultiError is an error wrapping multiple validation errors
// returned by SearchFilesResp.ValidateAll() if the designated constraints
// aren't met.
type SearchFilesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchFilesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchFilesRespMultiError) AllErrors() []error { return m }

// SearchFilesRespValidationError is the validation error returned by
// SearchFilesResp.Validate if the designated constraints aren't met.
type SearchFilesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchFilesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchFilesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchFilesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchFilesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchFilesRespValidationError) ErrorName() string { return "SearchFilesRespValidationError" }

// Error satisfies the builtin error interface
func (e SearchFilesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchFilesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchFilesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchFilesRespValidationError{}

// Validate checks the field values on FilesIntelExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilesIntelExistsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilesIntelExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilesIntelExistsRespMultiError, or nil if none found.
func (m *FilesIntelExistsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FilesIntelExistsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return FilesIntelExistsRespMultiError(errors)
	}

	return nil
}

// FilesIntelExistsRespMultiError is an error wrapping multiple validation
// errors returned by FilesIntelExistsResp.ValidateAll() if the designated
// constraints aren't met.
type FilesIntelExistsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilesIntelExistsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilesIntelExistsRespMultiError) AllErrors() []error { return m }

// FilesIntelExistsRespValidationError is the validation error returned by
// FilesIntelExistsResp.Validate if the designated constraints aren't met.
type FilesIntelExistsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilesIntelExistsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilesIntelExistsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilesIntelExistsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilesIntelExistsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilesIntelExistsRespValidationError) ErrorName() string {
	return "FilesIntelExistsRespValidationError"
}

// Error satisfies the builtin error interface
func (e FilesIntelExistsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilesIntelExistsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilesIntelExistsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilesIntelExistsRespValidationError{}

// Validate checks the field values on AddFilesReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddFilesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddFilesReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddFilesReqMultiError, or
// nil if none found.
func (m *AddFilesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddFilesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIntel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddFilesReqValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddFilesReqValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddFilesReqValidationError{
					field:  fmt.Sprintf("Intel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddFilesReqMultiError(errors)
	}

	return nil
}

// AddFilesReqMultiError is an error wrapping multiple validation errors
// returned by AddFilesReq.ValidateAll() if the designated constraints aren't met.
type AddFilesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddFilesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddFilesReqMultiError) AllErrors() []error { return m }

// AddFilesReqValidationError is the validation error returned by
// AddFilesReq.Validate if the designated constraints aren't met.
type AddFilesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddFilesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddFilesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddFilesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddFilesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddFilesReqValidationError) ErrorName() string { return "AddFilesReqValidationError" }

// Error satisfies the builtin error interface
func (e AddFilesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddFilesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddFilesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddFilesReqValidationError{}

// Validate checks the field values on AddFilesResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddFilesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddFilesResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddFilesRespMultiError, or
// nil if none found.
func (m *AddFilesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddFilesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessfulNum

	// no validation rules for FailedNum

	if len(errors) > 0 {
		return AddFilesRespMultiError(errors)
	}

	return nil
}

// AddFilesRespMultiError is an error wrapping multiple validation errors
// returned by AddFilesResp.ValidateAll() if the designated constraints aren't met.
type AddFilesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddFilesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddFilesRespMultiError) AllErrors() []error { return m }

// AddFilesRespValidationError is the validation error returned by
// AddFilesResp.Validate if the designated constraints aren't met.
type AddFilesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddFilesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddFilesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddFilesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddFilesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddFilesRespValidationError) ErrorName() string { return "AddFilesRespValidationError" }

// Error satisfies the builtin error interface
func (e AddFilesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddFilesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddFilesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddFilesRespValidationError{}

// Validate checks the field values on SearchHostsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchHostsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchHostsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchHostsReqMultiError,
// or nil if none found.
func (m *SearchHostsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchHostsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnlyDb

	if len(errors) > 0 {
		return SearchHostsReqMultiError(errors)
	}

	return nil
}

// SearchHostsReqMultiError is an error wrapping multiple validation errors
// returned by SearchHostsReq.ValidateAll() if the designated constraints
// aren't met.
type SearchHostsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchHostsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchHostsReqMultiError) AllErrors() []error { return m }

// SearchHostsReqValidationError is the validation error returned by
// SearchHostsReq.Validate if the designated constraints aren't met.
type SearchHostsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchHostsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchHostsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchHostsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchHostsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchHostsReqValidationError) ErrorName() string { return "SearchHostsReqValidationError" }

// Error satisfies the builtin error interface
func (e SearchHostsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchHostsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchHostsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchHostsReqValidationError{}

// Validate checks the field values on HostIntel with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostIntel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostIntel with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostIntelMultiError, or nil
// if none found.
func (m *HostIntel) ValidateAll() error {
	return m.validate(true)
}

func (m *HostIntel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Host

	// no validation rules for State

	// no validation rules for Source

	// no validation rules for Vendor

	// no validation rules for Confidence

	// no validation rules for Severity

	// no validation rules for ExtraInfo

	if len(errors) > 0 {
		return HostIntelMultiError(errors)
	}

	return nil
}

// HostIntelMultiError is an error wrapping multiple validation errors returned
// by HostIntel.ValidateAll() if the designated constraints aren't met.
type HostIntelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostIntelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostIntelMultiError) AllErrors() []error { return m }

// HostIntelValidationError is the validation error returned by
// HostIntel.Validate if the designated constraints aren't met.
type HostIntelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostIntelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostIntelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostIntelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostIntelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostIntelValidationError) ErrorName() string { return "HostIntelValidationError" }

// Error satisfies the builtin error interface
func (e HostIntelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostIntel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostIntelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostIntelValidationError{}

// Validate checks the field values on SearchHostsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchHostsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchHostsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchHostsRespMultiError, or nil if none found.
func (m *SearchHostsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchHostsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIntel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchHostsRespValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchHostsRespValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchHostsRespValidationError{
					field:  fmt.Sprintf("Intel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchHostsRespMultiError(errors)
	}

	return nil
}

// SearchHostsRespMultiError is an error wrapping multiple validation errors
// returned by SearchHostsResp.ValidateAll() if the designated constraints
// aren't met.
type SearchHostsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchHostsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchHostsRespMultiError) AllErrors() []error { return m }

// SearchHostsRespValidationError is the validation error returned by
// SearchHostsResp.Validate if the designated constraints aren't met.
type SearchHostsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchHostsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchHostsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchHostsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchHostsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchHostsRespValidationError) ErrorName() string { return "SearchHostsRespValidationError" }

// Error satisfies the builtin error interface
func (e SearchHostsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchHostsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchHostsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchHostsRespValidationError{}

// Validate checks the field values on HostsIntelExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HostsIntelExistsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostsIntelExistsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostsIntelExistsRespMultiError, or nil if none found.
func (m *HostsIntelExistsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *HostsIntelExistsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HostsIntelExistsRespMultiError(errors)
	}

	return nil
}

// HostsIntelExistsRespMultiError is an error wrapping multiple validation
// errors returned by HostsIntelExistsResp.ValidateAll() if the designated
// constraints aren't met.
type HostsIntelExistsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostsIntelExistsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostsIntelExistsRespMultiError) AllErrors() []error { return m }

// HostsIntelExistsRespValidationError is the validation error returned by
// HostsIntelExistsResp.Validate if the designated constraints aren't met.
type HostsIntelExistsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostsIntelExistsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostsIntelExistsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostsIntelExistsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostsIntelExistsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostsIntelExistsRespValidationError) ErrorName() string {
	return "HostsIntelExistsRespValidationError"
}

// Error satisfies the builtin error interface
func (e HostsIntelExistsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostsIntelExistsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostsIntelExistsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostsIntelExistsRespValidationError{}

// Validate checks the field values on AddHostsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddHostsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddHostsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddHostsReqMultiError, or
// nil if none found.
func (m *AddHostsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddHostsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIntel() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AddHostsReqValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AddHostsReqValidationError{
						field:  fmt.Sprintf("Intel[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AddHostsReqValidationError{
					field:  fmt.Sprintf("Intel[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AddHostsReqMultiError(errors)
	}

	return nil
}

// AddHostsReqMultiError is an error wrapping multiple validation errors
// returned by AddHostsReq.ValidateAll() if the designated constraints aren't met.
type AddHostsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddHostsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddHostsReqMultiError) AllErrors() []error { return m }

// AddHostsReqValidationError is the validation error returned by
// AddHostsReq.Validate if the designated constraints aren't met.
type AddHostsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddHostsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddHostsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddHostsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddHostsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddHostsReqValidationError) ErrorName() string { return "AddHostsReqValidationError" }

// Error satisfies the builtin error interface
func (e AddHostsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddHostsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddHostsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddHostsReqValidationError{}

// Validate checks the field values on AddHostsResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddHostsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddHostsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddHostsRespMultiError, or
// nil if none found.
func (m *AddHostsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddHostsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessfulNum

	// no validation rules for FailedNum

	if len(errors) > 0 {
		return AddHostsRespMultiError(errors)
	}

	return nil
}

// AddHostsRespMultiError is an error wrapping multiple validation errors
// returned by AddHostsResp.ValidateAll() if the designated constraints aren't met.
type AddHostsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddHostsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddHostsRespMultiError) AllErrors() []error { return m }

// AddHostsRespValidationError is the validation error returned by
// AddHostsResp.Validate if the designated constraints aren't met.
type AddHostsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddHostsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddHostsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddHostsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddHostsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddHostsRespValidationError) ErrorName() string { return "AddHostsRespValidationError" }

// Error satisfies the builtin error interface
func (e AddHostsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddHostsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddHostsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddHostsRespValidationError{}
