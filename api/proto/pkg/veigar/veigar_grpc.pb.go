// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: veigar/veigar.proto

package veigar

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Veigar_SearchFilesIntel_FullMethodName   = "/veigar.Veigar/SearchFilesIntel"
	Veigar_FilesIntelExistsBF_FullMethodName = "/veigar.Veigar/FilesIntelExistsBF"
	Veigar_AddFilesIntel_FullMethodName      = "/veigar.Veigar/AddFilesIntel"
	Veigar_SearchHostsIntel_FullMethodName   = "/veigar.Veigar/SearchHostsIntel"
	Veigar_HostsIntelExistsBF_FullMethodName = "/veigar.Veigar/HostsIntelExistsBF"
	Veigar_AddHostsIntel_FullMethodName      = "/veigar.Veigar/AddHostsIntel"
)

// VeigarClient is the client API for Veigar service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VeigarClient interface {
	// 基于md5批量搜索文件情报
	SearchFilesIntel(ctx context.Context, in *SearchFilesReq, opts ...grpc.CallOption) (*SearchFilesResp, error)
	// 快速判断文件情报是否存在，接口将返回存在和不存在的md5列表，不会返回具体情报内容
	// 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
	FilesIntelExistsBF(ctx context.Context, in *SearchFilesReq, opts ...grpc.CallOption) (*FilesIntelExistsResp, error)
	// 批量添加文件情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
	AddFilesIntel(ctx context.Context, in *AddFilesReq, opts ...grpc.CallOption) (*AddFilesResp, error)
	// 基于ip或域名批量查询外联情报
	SearchHostsIntel(ctx context.Context, in *SearchHostsReq, opts ...grpc.CallOption) (*SearchHostsResp, error)
	// 快速判断外联情报是否存在，接口将返回存在和不存在的host列表，不会返回具体情报内容
	// 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
	HostsIntelExistsBF(ctx context.Context, in *SearchHostsReq, opts ...grpc.CallOption) (*HostsIntelExistsResp, error)
	// 批量添加外联情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
	AddHostsIntel(ctx context.Context, in *AddHostsReq, opts ...grpc.CallOption) (*AddHostsResp, error)
}

type veigarClient struct {
	cc grpc.ClientConnInterface
}

func NewVeigarClient(cc grpc.ClientConnInterface) VeigarClient {
	return &veigarClient{cc}
}

func (c *veigarClient) SearchFilesIntel(ctx context.Context, in *SearchFilesReq, opts ...grpc.CallOption) (*SearchFilesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchFilesResp)
	err := c.cc.Invoke(ctx, Veigar_SearchFilesIntel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *veigarClient) FilesIntelExistsBF(ctx context.Context, in *SearchFilesReq, opts ...grpc.CallOption) (*FilesIntelExistsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilesIntelExistsResp)
	err := c.cc.Invoke(ctx, Veigar_FilesIntelExistsBF_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *veigarClient) AddFilesIntel(ctx context.Context, in *AddFilesReq, opts ...grpc.CallOption) (*AddFilesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddFilesResp)
	err := c.cc.Invoke(ctx, Veigar_AddFilesIntel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *veigarClient) SearchHostsIntel(ctx context.Context, in *SearchHostsReq, opts ...grpc.CallOption) (*SearchHostsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchHostsResp)
	err := c.cc.Invoke(ctx, Veigar_SearchHostsIntel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *veigarClient) HostsIntelExistsBF(ctx context.Context, in *SearchHostsReq, opts ...grpc.CallOption) (*HostsIntelExistsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HostsIntelExistsResp)
	err := c.cc.Invoke(ctx, Veigar_HostsIntelExistsBF_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *veigarClient) AddHostsIntel(ctx context.Context, in *AddHostsReq, opts ...grpc.CallOption) (*AddHostsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddHostsResp)
	err := c.cc.Invoke(ctx, Veigar_AddHostsIntel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VeigarServer is the server API for Veigar service.
// All implementations must embed UnimplementedVeigarServer
// for forward compatibility.
type VeigarServer interface {
	// 基于md5批量搜索文件情报
	SearchFilesIntel(context.Context, *SearchFilesReq) (*SearchFilesResp, error)
	// 快速判断文件情报是否存在，接口将返回存在和不存在的md5列表，不会返回具体情报内容
	// 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
	FilesIntelExistsBF(context.Context, *SearchFilesReq) (*FilesIntelExistsResp, error)
	// 批量添加文件情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
	AddFilesIntel(context.Context, *AddFilesReq) (*AddFilesResp, error)
	// 基于ip或域名批量查询外联情报
	SearchHostsIntel(context.Context, *SearchHostsReq) (*SearchHostsResp, error)
	// 快速判断外联情报是否存在，接口将返回存在和不存在的host列表，不会返回具体情报内容
	// 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
	HostsIntelExistsBF(context.Context, *SearchHostsReq) (*HostsIntelExistsResp, error)
	// 批量添加外联情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
	AddHostsIntel(context.Context, *AddHostsReq) (*AddHostsResp, error)
	mustEmbedUnimplementedVeigarServer()
}

// UnimplementedVeigarServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVeigarServer struct{}

func (UnimplementedVeigarServer) SearchFilesIntel(context.Context, *SearchFilesReq) (*SearchFilesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchFilesIntel not implemented")
}
func (UnimplementedVeigarServer) FilesIntelExistsBF(context.Context, *SearchFilesReq) (*FilesIntelExistsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilesIntelExistsBF not implemented")
}
func (UnimplementedVeigarServer) AddFilesIntel(context.Context, *AddFilesReq) (*AddFilesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFilesIntel not implemented")
}
func (UnimplementedVeigarServer) SearchHostsIntel(context.Context, *SearchHostsReq) (*SearchHostsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchHostsIntel not implemented")
}
func (UnimplementedVeigarServer) HostsIntelExistsBF(context.Context, *SearchHostsReq) (*HostsIntelExistsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HostsIntelExistsBF not implemented")
}
func (UnimplementedVeigarServer) AddHostsIntel(context.Context, *AddHostsReq) (*AddHostsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHostsIntel not implemented")
}
func (UnimplementedVeigarServer) mustEmbedUnimplementedVeigarServer() {}
func (UnimplementedVeigarServer) testEmbeddedByValue()                {}

// UnsafeVeigarServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VeigarServer will
// result in compilation errors.
type UnsafeVeigarServer interface {
	mustEmbedUnimplementedVeigarServer()
}

func RegisterVeigarServer(s grpc.ServiceRegistrar, srv VeigarServer) {
	// If the following call pancis, it indicates UnimplementedVeigarServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Veigar_ServiceDesc, srv)
}

func _Veigar_SearchFilesIntel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchFilesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).SearchFilesIntel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_SearchFilesIntel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).SearchFilesIntel(ctx, req.(*SearchFilesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Veigar_FilesIntelExistsBF_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchFilesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).FilesIntelExistsBF(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_FilesIntelExistsBF_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).FilesIntelExistsBF(ctx, req.(*SearchFilesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Veigar_AddFilesIntel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFilesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).AddFilesIntel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_AddFilesIntel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).AddFilesIntel(ctx, req.(*AddFilesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Veigar_SearchHostsIntel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchHostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).SearchHostsIntel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_SearchHostsIntel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).SearchHostsIntel(ctx, req.(*SearchHostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Veigar_HostsIntelExistsBF_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchHostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).HostsIntelExistsBF(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_HostsIntelExistsBF_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).HostsIntelExistsBF(ctx, req.(*SearchHostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Veigar_AddHostsIntel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddHostsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VeigarServer).AddHostsIntel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Veigar_AddHostsIntel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VeigarServer).AddHostsIntel(ctx, req.(*AddHostsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Veigar_ServiceDesc is the grpc.ServiceDesc for Veigar service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Veigar_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "veigar.Veigar",
	HandlerType: (*VeigarServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchFilesIntel",
			Handler:    _Veigar_SearchFilesIntel_Handler,
		},
		{
			MethodName: "FilesIntelExistsBF",
			Handler:    _Veigar_FilesIntelExistsBF_Handler,
		},
		{
			MethodName: "AddFilesIntel",
			Handler:    _Veigar_AddFilesIntel_Handler,
		},
		{
			MethodName: "SearchHostsIntel",
			Handler:    _Veigar_SearchHostsIntel_Handler,
		},
		{
			MethodName: "HostsIntelExistsBF",
			Handler:    _Veigar_HostsIntelExistsBF_Handler,
		},
		{
			MethodName: "AddHostsIntel",
			Handler:    _Veigar_AddHostsIntel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "veigar/veigar.proto",
}
