// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: veigar/veigar.proto

package veigar

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 情报中心异常状态码
type Codes int32

const (
	Codes_CODE_UNKNOWN                Codes = 0
	Codes_ERROR_BLOOM_FILTER_BUILDING Codes = 10001 // 布隆过滤器正在构建中，无法对外提供服务，client侧需妥善处理
)

// Enum value maps for Codes.
var (
	Codes_name = map[int32]string{
		0:     "CODE_UNKNOWN",
		10001: "ERROR_BLOOM_FILTER_BUILDING",
	}
	Codes_value = map[string]int32{
		"CODE_UNKNOWN":                0,
		"ERROR_BLOOM_FILTER_BUILDING": 10001,
	}
)

func (x Codes) Enum() *Codes {
	p := new(Codes)
	*p = x
	return p
}

func (x Codes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Codes) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[0].Descriptor()
}

func (Codes) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[0]
}

func (x Codes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Codes.Descriptor instead.
func (Codes) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{0}
}

type FileIntelState int32

const (
	FileIntelState_FILE_UNKNOWN FileIntelState = 0 // 文件情报库不存在该文件情报，结果未知
	FileIntelState_FILE_WHITE   FileIntelState = 1 // 文件为白
	FileIntelState_FILE_BLACK   FileIntelState = 2 // 文件为黑
	FileIntelState_FILE_GRAY    FileIntelState = 3 // 文件为灰
)

// Enum value maps for FileIntelState.
var (
	FileIntelState_name = map[int32]string{
		0: "FILE_UNKNOWN",
		1: "FILE_WHITE",
		2: "FILE_BLACK",
		3: "FILE_GRAY",
	}
	FileIntelState_value = map[string]int32{
		"FILE_UNKNOWN": 0,
		"FILE_WHITE":   1,
		"FILE_BLACK":   2,
		"FILE_GRAY":    3,
	}
)

func (x FileIntelState) Enum() *FileIntelState {
	p := new(FileIntelState)
	*p = x
	return p
}

func (x FileIntelState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileIntelState) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[1].Descriptor()
}

func (FileIntelState) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[1]
}

func (x FileIntelState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileIntelState.Descriptor instead.
func (FileIntelState) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{1}
}

type FileIntelSource int32

const (
	FileIntelSource_FILE_SOURCE_UNKNOWN  FileIntelSource = 0 // 默认值安全
	FileIntelSource_FILE_SOURCE_LIB      FileIntelSource = 1 // 情报库
	FileIntelSource_FILE_SOURCE_WFY      FileIntelSource = 2 // 网防云
	FileIntelSource_FILE_SOURCE_CUSTOMER FileIntelSource = 3 // 用户自定义
	FileIntelSource_FILE_SOURCE_DETECT   FileIntelSource = 4 // 检测结果写入
)

// Enum value maps for FileIntelSource.
var (
	FileIntelSource_name = map[int32]string{
		0: "FILE_SOURCE_UNKNOWN",
		1: "FILE_SOURCE_LIB",
		2: "FILE_SOURCE_WFY",
		3: "FILE_SOURCE_CUSTOMER",
		4: "FILE_SOURCE_DETECT",
	}
	FileIntelSource_value = map[string]int32{
		"FILE_SOURCE_UNKNOWN":  0,
		"FILE_SOURCE_LIB":      1,
		"FILE_SOURCE_WFY":      2,
		"FILE_SOURCE_CUSTOMER": 3,
		"FILE_SOURCE_DETECT":   4,
	}
)

func (x FileIntelSource) Enum() *FileIntelSource {
	p := new(FileIntelSource)
	*p = x
	return p
}

func (x FileIntelSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileIntelSource) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[2].Descriptor()
}

func (FileIntelSource) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[2]
}

func (x FileIntelSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileIntelSource.Descriptor instead.
func (FileIntelSource) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{2}
}

type Severity int32

const (
	Severity_UNKNOWN Severity = 0
	Severity_LOW     Severity = 1
	Severity_MIDDLE  Severity = 2
	Severity_HIGH    Severity = 3
)

// Enum value maps for Severity.
var (
	Severity_name = map[int32]string{
		0: "UNKNOWN",
		1: "LOW",
		2: "MIDDLE",
		3: "HIGH",
	}
	Severity_value = map[string]int32{
		"UNKNOWN": 0,
		"LOW":     1,
		"MIDDLE":  2,
		"HIGH":    3,
	}
)

func (x Severity) Enum() *Severity {
	p := new(Severity)
	*p = x
	return p
}

func (x Severity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Severity) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[3].Descriptor()
}

func (Severity) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[3]
}

func (x Severity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Severity.Descriptor instead.
func (Severity) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{3}
}

type HostIntelState int32

const (
	HostIntelState_HOST_UNKNOWN HostIntelState = 0 // 文件情报库不存在该文件情报，结果未知
	HostIntelState_HOST_WHITE   HostIntelState = 1 // 文件为白
	HostIntelState_HOST_BLACK   HostIntelState = 2 // 文件为黑
)

// Enum value maps for HostIntelState.
var (
	HostIntelState_name = map[int32]string{
		0: "HOST_UNKNOWN",
		1: "HOST_WHITE",
		2: "HOST_BLACK",
	}
	HostIntelState_value = map[string]int32{
		"HOST_UNKNOWN": 0,
		"HOST_WHITE":   1,
		"HOST_BLACK":   2,
	}
)

func (x HostIntelState) Enum() *HostIntelState {
	p := new(HostIntelState)
	*p = x
	return p
}

func (x HostIntelState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostIntelState) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[4].Descriptor()
}

func (HostIntelState) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[4]
}

func (x HostIntelState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostIntelState.Descriptor instead.
func (HostIntelState) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{4}
}

type HostIntelSource int32

const (
	HostIntelSource_HOST_SOURCE_UNKNOWN  HostIntelSource = 0 // 默认值安全
	HostIntelSource_HOST_SOURCE_LIB      HostIntelSource = 1 // 情报库
	HostIntelSource_HOST_SOURCE_WFY      HostIntelSource = 2 // 网防云
	HostIntelSource_HOST_SOURCE_CUSTOMER HostIntelSource = 3 // 用户自定义
)

// Enum value maps for HostIntelSource.
var (
	HostIntelSource_name = map[int32]string{
		0: "HOST_SOURCE_UNKNOWN",
		1: "HOST_SOURCE_LIB",
		2: "HOST_SOURCE_WFY",
		3: "HOST_SOURCE_CUSTOMER",
	}
	HostIntelSource_value = map[string]int32{
		"HOST_SOURCE_UNKNOWN":  0,
		"HOST_SOURCE_LIB":      1,
		"HOST_SOURCE_WFY":      2,
		"HOST_SOURCE_CUSTOMER": 3,
	}
)

func (x HostIntelSource) Enum() *HostIntelSource {
	p := new(HostIntelSource)
	*p = x
	return p
}

func (x HostIntelSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HostIntelSource) Descriptor() protoreflect.EnumDescriptor {
	return file_veigar_veigar_proto_enumTypes[5].Descriptor()
}

func (HostIntelSource) Type() protoreflect.EnumType {
	return &file_veigar_veigar_proto_enumTypes[5]
}

func (x HostIntelSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HostIntelSource.Descriptor instead.
func (HostIntelSource) EnumDescriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{5}
}

type SearchFilesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5S   []string `protobuf:"bytes,1,rep,name=md5s,proto3" json:"md5s,omitempty"`                    // 文件md5
	OnlyDb bool     `protobuf:"varint,2,opt,name=only_db,json=onlyDb,proto3" json:"only_db,omitempty"` // 仅在数据库中查询，文件检测服务有此需求，一般不要指定为true
}

func (x *SearchFilesReq) Reset() {
	*x = SearchFilesReq{}
	mi := &file_veigar_veigar_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchFilesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchFilesReq) ProtoMessage() {}

func (x *SearchFilesReq) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchFilesReq.ProtoReflect.Descriptor instead.
func (*SearchFilesReq) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{0}
}

func (x *SearchFilesReq) GetMd5S() []string {
	if x != nil {
		return x.Md5S
	}
	return nil
}

func (x *SearchFilesReq) GetOnlyDb() bool {
	if x != nil {
		return x.OnlyDb
	}
	return false
}

type FileIntel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5             string          `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`                                                // 文件md5
	State           FileIntelState  `protobuf:"varint,2,opt,name=state,proto3,enum=veigar.FileIntelState" json:"state,omitempty"`                // 文件判断结果
	Source          FileIntelSource `protobuf:"varint,3,opt,name=source,proto3,enum=veigar.FileIntelSource" json:"source,omitempty"`             // 情报来源
	Vendor          string          `protobuf:"bytes,4,opt,name=vendor,proto3" json:"vendor,omitempty"`                                          // 情报厂商
	Confidence      int32           `protobuf:"varint,5,opt,name=confidence,proto3" json:"confidence,omitempty"`                                 // 置信度
	Severity        Severity        `protobuf:"varint,6,opt,name=severity,proto3,enum=veigar.Severity" json:"severity,omitempty"`                // 严重程度
	MalwareFamilies []string        `protobuf:"bytes,7,rep,name=malware_families,json=malwareFamilies,proto3" json:"malware_families,omitempty"` // 病毒家族
	ThreatTypes     []int32         `protobuf:"varint,8,rep,packed,name=threat_types,json=threatTypes,proto3" json:"threat_types,omitempty"`     // 风险类型
	AptOrg          []string        `protobuf:"bytes,9,rep,name=apt_org,json=aptOrg,proto3" json:"apt_org,omitempty"`                            // APT组织
	Tags            []string        `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty"`                                             // 情报标签
	ExtraInfo       string          `protobuf:"bytes,11,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`                  // 不同来源可设置额外信息
}

func (x *FileIntel) Reset() {
	*x = FileIntel{}
	mi := &file_veigar_veigar_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileIntel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileIntel) ProtoMessage() {}

func (x *FileIntel) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileIntel.ProtoReflect.Descriptor instead.
func (*FileIntel) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{1}
}

func (x *FileIntel) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileIntel) GetState() FileIntelState {
	if x != nil {
		return x.State
	}
	return FileIntelState_FILE_UNKNOWN
}

func (x *FileIntel) GetSource() FileIntelSource {
	if x != nil {
		return x.Source
	}
	return FileIntelSource_FILE_SOURCE_UNKNOWN
}

func (x *FileIntel) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *FileIntel) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *FileIntel) GetSeverity() Severity {
	if x != nil {
		return x.Severity
	}
	return Severity_UNKNOWN
}

func (x *FileIntel) GetMalwareFamilies() []string {
	if x != nil {
		return x.MalwareFamilies
	}
	return nil
}

func (x *FileIntel) GetThreatTypes() []int32 {
	if x != nil {
		return x.ThreatTypes
	}
	return nil
}

func (x *FileIntel) GetAptOrg() []string {
	if x != nil {
		return x.AptOrg
	}
	return nil
}

func (x *FileIntel) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *FileIntel) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

type SearchFilesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intel []*FileIntel `protobuf:"bytes,1,rep,name=intel,proto3" json:"intel,omitempty"`
}

func (x *SearchFilesResp) Reset() {
	*x = SearchFilesResp{}
	mi := &file_veigar_veigar_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchFilesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchFilesResp) ProtoMessage() {}

func (x *SearchFilesResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchFilesResp.ProtoReflect.Descriptor instead.
func (*SearchFilesResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{2}
}

func (x *SearchFilesResp) GetIntel() []*FileIntel {
	if x != nil {
		return x.Intel
	}
	return nil
}

type FilesIntelExistsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unknown []string `protobuf:"bytes,1,rep,name=unknown,proto3" json:"unknown,omitempty"`
	Exists  []string `protobuf:"bytes,2,rep,name=exists,proto3" json:"exists,omitempty"`
}

func (x *FilesIntelExistsResp) Reset() {
	*x = FilesIntelExistsResp{}
	mi := &file_veigar_veigar_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilesIntelExistsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilesIntelExistsResp) ProtoMessage() {}

func (x *FilesIntelExistsResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilesIntelExistsResp.ProtoReflect.Descriptor instead.
func (*FilesIntelExistsResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{3}
}

func (x *FilesIntelExistsResp) GetUnknown() []string {
	if x != nil {
		return x.Unknown
	}
	return nil
}

func (x *FilesIntelExistsResp) GetExists() []string {
	if x != nil {
		return x.Exists
	}
	return nil
}

type AddFilesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intel []*FileIntel `protobuf:"bytes,1,rep,name=intel,proto3" json:"intel,omitempty"`
}

func (x *AddFilesReq) Reset() {
	*x = AddFilesReq{}
	mi := &file_veigar_veigar_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFilesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFilesReq) ProtoMessage() {}

func (x *AddFilesReq) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFilesReq.ProtoReflect.Descriptor instead.
func (*AddFilesReq) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{4}
}

func (x *AddFilesReq) GetIntel() []*FileIntel {
	if x != nil {
		return x.Intel
	}
	return nil
}

type AddFilesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessfulNum int64 `protobuf:"varint,1,opt,name=successful_num,json=successfulNum,proto3" json:"successful_num,omitempty"`
	FailedNum     int64 `protobuf:"varint,2,opt,name=failed_num,json=failedNum,proto3" json:"failed_num,omitempty"`
}

func (x *AddFilesResp) Reset() {
	*x = AddFilesResp{}
	mi := &file_veigar_veigar_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFilesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFilesResp) ProtoMessage() {}

func (x *AddFilesResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFilesResp.ProtoReflect.Descriptor instead.
func (*AddFilesResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{5}
}

func (x *AddFilesResp) GetSuccessfulNum() int64 {
	if x != nil {
		return x.SuccessfulNum
	}
	return 0
}

func (x *AddFilesResp) GetFailedNum() int64 {
	if x != nil {
		return x.FailedNum
	}
	return 0
}

type SearchHostsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hosts  []string `protobuf:"bytes,1,rep,name=hosts,proto3" json:"hosts,omitempty"`                  // ip或域名列表
	OnlyDb bool     `protobuf:"varint,2,opt,name=only_db,json=onlyDb,proto3" json:"only_db,omitempty"` // 仅在数据库中查询，文件检测服务有此需求，一般不要指定为true
}

func (x *SearchHostsReq) Reset() {
	*x = SearchHostsReq{}
	mi := &file_veigar_veigar_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchHostsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHostsReq) ProtoMessage() {}

func (x *SearchHostsReq) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHostsReq.ProtoReflect.Descriptor instead.
func (*SearchHostsReq) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{6}
}

func (x *SearchHostsReq) GetHosts() []string {
	if x != nil {
		return x.Hosts
	}
	return nil
}

func (x *SearchHostsReq) GetOnlyDb() bool {
	if x != nil {
		return x.OnlyDb
	}
	return false
}

type HostIntel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Host            string          `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`                                  // ip或域名
	State           HostIntelState  `protobuf:"varint,2,opt,name=state,proto3,enum=veigar.HostIntelState" json:"state,omitempty"`    // 文件判断结果
	Source          HostIntelSource `protobuf:"varint,3,opt,name=source,proto3,enum=veigar.HostIntelSource" json:"source,omitempty"` // 情报来源
	Ports           []int32         `protobuf:"varint,4,rep,packed,name=ports,proto3" json:"ports,omitempty"`
	Vendor          string          `protobuf:"bytes,5,opt,name=vendor,proto3" json:"vendor,omitempty"`                                          // 情报厂商
	Confidence      int32           `protobuf:"varint,6,opt,name=confidence,proto3" json:"confidence,omitempty"`                                 // 置信度
	Severity        Severity        `protobuf:"varint,7,opt,name=severity,proto3,enum=veigar.Severity" json:"severity,omitempty"`                // 严重程度
	MalwareFamilies []string        `protobuf:"bytes,8,rep,name=malware_families,json=malwareFamilies,proto3" json:"malware_families,omitempty"` // 病毒家族
	ThreatTypes     []int32         `protobuf:"varint,9,rep,packed,name=threat_types,json=threatTypes,proto3" json:"threat_types,omitempty"`     // 风险类型
	AptOrg          []string        `protobuf:"bytes,10,rep,name=apt_org,json=aptOrg,proto3" json:"apt_org,omitempty"`                           // APT组织
	Tags            []string        `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`                                             // 情报标签
	ExtraInfo       string          `protobuf:"bytes,12,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`                  // 不同来源可设置额外信息
}

func (x *HostIntel) Reset() {
	*x = HostIntel{}
	mi := &file_veigar_veigar_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostIntel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostIntel) ProtoMessage() {}

func (x *HostIntel) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostIntel.ProtoReflect.Descriptor instead.
func (*HostIntel) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{7}
}

func (x *HostIntel) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HostIntel) GetState() HostIntelState {
	if x != nil {
		return x.State
	}
	return HostIntelState_HOST_UNKNOWN
}

func (x *HostIntel) GetSource() HostIntelSource {
	if x != nil {
		return x.Source
	}
	return HostIntelSource_HOST_SOURCE_UNKNOWN
}

func (x *HostIntel) GetPorts() []int32 {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *HostIntel) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *HostIntel) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *HostIntel) GetSeverity() Severity {
	if x != nil {
		return x.Severity
	}
	return Severity_UNKNOWN
}

func (x *HostIntel) GetMalwareFamilies() []string {
	if x != nil {
		return x.MalwareFamilies
	}
	return nil
}

func (x *HostIntel) GetThreatTypes() []int32 {
	if x != nil {
		return x.ThreatTypes
	}
	return nil
}

func (x *HostIntel) GetAptOrg() []string {
	if x != nil {
		return x.AptOrg
	}
	return nil
}

func (x *HostIntel) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *HostIntel) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

type SearchHostsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intel []*HostIntel `protobuf:"bytes,1,rep,name=intel,proto3" json:"intel,omitempty"`
}

func (x *SearchHostsResp) Reset() {
	*x = SearchHostsResp{}
	mi := &file_veigar_veigar_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchHostsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHostsResp) ProtoMessage() {}

func (x *SearchHostsResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHostsResp.ProtoReflect.Descriptor instead.
func (*SearchHostsResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{8}
}

func (x *SearchHostsResp) GetIntel() []*HostIntel {
	if x != nil {
		return x.Intel
	}
	return nil
}

type HostsIntelExistsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unknown []string `protobuf:"bytes,1,rep,name=unknown,proto3" json:"unknown,omitempty"`
	Exists  []string `protobuf:"bytes,2,rep,name=exists,proto3" json:"exists,omitempty"`
}

func (x *HostsIntelExistsResp) Reset() {
	*x = HostsIntelExistsResp{}
	mi := &file_veigar_veigar_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostsIntelExistsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostsIntelExistsResp) ProtoMessage() {}

func (x *HostsIntelExistsResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostsIntelExistsResp.ProtoReflect.Descriptor instead.
func (*HostsIntelExistsResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{9}
}

func (x *HostsIntelExistsResp) GetUnknown() []string {
	if x != nil {
		return x.Unknown
	}
	return nil
}

func (x *HostsIntelExistsResp) GetExists() []string {
	if x != nil {
		return x.Exists
	}
	return nil
}

type AddHostsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Intel []*HostIntel `protobuf:"bytes,1,rep,name=intel,proto3" json:"intel,omitempty"`
}

func (x *AddHostsReq) Reset() {
	*x = AddHostsReq{}
	mi := &file_veigar_veigar_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddHostsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddHostsReq) ProtoMessage() {}

func (x *AddHostsReq) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddHostsReq.ProtoReflect.Descriptor instead.
func (*AddHostsReq) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{10}
}

func (x *AddHostsReq) GetIntel() []*HostIntel {
	if x != nil {
		return x.Intel
	}
	return nil
}

type AddHostsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessfulNum int64 `protobuf:"varint,1,opt,name=successful_num,json=successfulNum,proto3" json:"successful_num,omitempty"`
	FailedNum     int64 `protobuf:"varint,2,opt,name=failed_num,json=failedNum,proto3" json:"failed_num,omitempty"`
}

func (x *AddHostsResp) Reset() {
	*x = AddHostsResp{}
	mi := &file_veigar_veigar_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddHostsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddHostsResp) ProtoMessage() {}

func (x *AddHostsResp) ProtoReflect() protoreflect.Message {
	mi := &file_veigar_veigar_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddHostsResp.ProtoReflect.Descriptor instead.
func (*AddHostsResp) Descriptor() ([]byte, []int) {
	return file_veigar_veigar_proto_rawDescGZIP(), []int{11}
}

func (x *AddHostsResp) GetSuccessfulNum() int64 {
	if x != nil {
		return x.SuccessfulNum
	}
	return 0
}

func (x *AddHostsResp) GetFailedNum() int64 {
	if x != nil {
		return x.FailedNum
	}
	return 0
}

var File_veigar_veigar_proto protoreflect.FileDescriptor

var file_veigar_veigar_proto_rawDesc = []byte{
	0x0a, 0x13, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2f, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x22, 0x3d, 0x0a,
	0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x12, 0x0a, 0x04, 0x6d, 0x64, 0x35, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6d,
	0x64, 0x35, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x64, 0x62, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x79, 0x44, 0x62, 0x22, 0xfc, 0x02, 0x0a,
	0x09, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64,
	0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x2c, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x76, 0x65,
	0x69, 0x67, 0x61, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x76, 0x65, 0x69,
	0x67, 0x61, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x53,
	0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x29, 0x0a, 0x10, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x66, 0x61, 0x6d,
	0x69, 0x6c, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x6c,
	0x77, 0x61, 0x72, 0x65, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x0b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x61, 0x70, 0x74, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x70, 0x74, 0x4f, 0x72, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x3a, 0x0a, 0x0f, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27,
	0x0a, 0x05, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6c,
	0x52, 0x05, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x22, 0x48, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x49, 0x6e, 0x74, 0x65, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69,
	0x73, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x73, 0x22, 0x36, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x27, 0x0a, 0x05, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74,
	0x65, 0x6c, 0x52, 0x05, 0x69, 0x6e, 0x74, 0x65, 0x6c, 0x22, 0x54, 0x0a, 0x0c, 0x41, 0x64, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x22,
	0x3f, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x6e, 0x6c, 0x79, 0x5f,
	0x64, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x79, 0x44, 0x62,
	0x22, 0x94, 0x03, 0x0a, 0x09, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49,
	0x6e, 0x74, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x2c, 0x0a, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x10, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x76, 0x65, 0x72,
	0x69, 0x74, 0x79, 0x52, 0x08, 0x73, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a,
	0x10, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65,
	0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0b,
	0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x61,
	0x70, 0x74, 0x5f, 0x6f, 0x72, 0x67, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70,
	0x74, 0x4f, 0x72, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x3a, 0x0a, 0x0f, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x76, 0x65, 0x69, 0x67,
	0x61, 0x72, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x52, 0x05, 0x69, 0x6e,
	0x74, 0x65, 0x6c, 0x22, 0x48, 0x0a, 0x14, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x6e, 0x74, 0x65,
	0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x75, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x69, 0x73, 0x74, 0x73, 0x22, 0x36, 0x0a,
	0x0b, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x05,
	0x69, 0x6e, 0x74, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x76, 0x65,
	0x69, 0x67, 0x61, 0x72, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x52, 0x05,
	0x69, 0x6e, 0x74, 0x65, 0x6c, 0x22, 0x54, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x66, 0x75, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a,
	0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x2a, 0x3b, 0x0a, 0x05, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x20, 0x0a, 0x1b, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f,
	0x42, 0x4c, 0x4f, 0x4f, 0x4d, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x42, 0x55, 0x49,
	0x4c, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x91, 0x4e, 0x2a, 0x51, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x47, 0x52, 0x41, 0x59, 0x10, 0x03, 0x2a, 0x86, 0x01, 0x0a, 0x0f,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4c, 0x49, 0x42, 0x10, 0x01, 0x12, 0x13, 0x0a,
	0x0f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x57, 0x46, 0x59,
	0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43,
	0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x45,
	0x43, 0x54, 0x10, 0x04, 0x2a, 0x36, 0x0a, 0x08, 0x53, 0x65, 0x76, 0x65, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x07, 0x0a,
	0x03, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45,
	0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x03, 0x2a, 0x42, 0x0a, 0x0e,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10,
	0x0a, 0x0c, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00,
	0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x01,
	0x12, 0x0e, 0x0a, 0x0a, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x02,
	0x2a, 0x6e, 0x0a, 0x0f, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f,
	0x48, 0x4f, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x4c, 0x49, 0x42, 0x10,
	0x01, 0x12, 0x13, 0x0a, 0x0f, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45,
	0x5f, 0x57, 0x46, 0x59, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x03,
	0x32, 0xae, 0x03, 0x0a, 0x06, 0x56, 0x65, 0x69, 0x67, 0x61, 0x72, 0x12, 0x45, 0x0a, 0x10, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x12,
	0x16, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x4c, 0x0a, 0x12, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x6c,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x46, 0x12, 0x16, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61,
	0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1c, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49,
	0x6e, 0x74, 0x65, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x74, 0x65,
	0x6c, 0x12, 0x13, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e,
	0x41, 0x64, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x45,
	0x0a, 0x10, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x6e, 0x74,
	0x65, 0x6c, 0x12, 0x16, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x76, 0x65, 0x69,
	0x67, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x12, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x49, 0x6e,
	0x74, 0x65, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x42, 0x46, 0x12, 0x16, 0x2e, 0x76, 0x65,
	0x69, 0x67, 0x61, 0x72, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x48, 0x6f, 0x73, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x48, 0x6f, 0x73,
	0x74, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x6c, 0x45, 0x78, 0x69, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x3c, 0x0a, 0x0d, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x49,
	0x6e, 0x74, 0x65, 0x6c, 0x12, 0x13, 0x2e, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x2e, 0x41, 0x64,
	0x64, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x76, 0x65, 0x69, 0x67,
	0x61, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x73, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x76, 0x65, 0x69, 0x67, 0x61, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_veigar_veigar_proto_rawDescOnce sync.Once
	file_veigar_veigar_proto_rawDescData = file_veigar_veigar_proto_rawDesc
)

func file_veigar_veigar_proto_rawDescGZIP() []byte {
	file_veigar_veigar_proto_rawDescOnce.Do(func() {
		file_veigar_veigar_proto_rawDescData = protoimpl.X.CompressGZIP(file_veigar_veigar_proto_rawDescData)
	})
	return file_veigar_veigar_proto_rawDescData
}

var file_veigar_veigar_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_veigar_veigar_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_veigar_veigar_proto_goTypes = []any{
	(Codes)(0),                   // 0: veigar.Codes
	(FileIntelState)(0),          // 1: veigar.FileIntelState
	(FileIntelSource)(0),         // 2: veigar.FileIntelSource
	(Severity)(0),                // 3: veigar.Severity
	(HostIntelState)(0),          // 4: veigar.HostIntelState
	(HostIntelSource)(0),         // 5: veigar.HostIntelSource
	(*SearchFilesReq)(nil),       // 6: veigar.SearchFilesReq
	(*FileIntel)(nil),            // 7: veigar.FileIntel
	(*SearchFilesResp)(nil),      // 8: veigar.SearchFilesResp
	(*FilesIntelExistsResp)(nil), // 9: veigar.FilesIntelExistsResp
	(*AddFilesReq)(nil),          // 10: veigar.AddFilesReq
	(*AddFilesResp)(nil),         // 11: veigar.AddFilesResp
	(*SearchHostsReq)(nil),       // 12: veigar.SearchHostsReq
	(*HostIntel)(nil),            // 13: veigar.HostIntel
	(*SearchHostsResp)(nil),      // 14: veigar.SearchHostsResp
	(*HostsIntelExistsResp)(nil), // 15: veigar.HostsIntelExistsResp
	(*AddHostsReq)(nil),          // 16: veigar.AddHostsReq
	(*AddHostsResp)(nil),         // 17: veigar.AddHostsResp
}
var file_veigar_veigar_proto_depIdxs = []int32{
	1,  // 0: veigar.FileIntel.state:type_name -> veigar.FileIntelState
	2,  // 1: veigar.FileIntel.source:type_name -> veigar.FileIntelSource
	3,  // 2: veigar.FileIntel.severity:type_name -> veigar.Severity
	7,  // 3: veigar.SearchFilesResp.intel:type_name -> veigar.FileIntel
	7,  // 4: veigar.AddFilesReq.intel:type_name -> veigar.FileIntel
	4,  // 5: veigar.HostIntel.state:type_name -> veigar.HostIntelState
	5,  // 6: veigar.HostIntel.source:type_name -> veigar.HostIntelSource
	3,  // 7: veigar.HostIntel.severity:type_name -> veigar.Severity
	13, // 8: veigar.SearchHostsResp.intel:type_name -> veigar.HostIntel
	13, // 9: veigar.AddHostsReq.intel:type_name -> veigar.HostIntel
	6,  // 10: veigar.Veigar.SearchFilesIntel:input_type -> veigar.SearchFilesReq
	6,  // 11: veigar.Veigar.FilesIntelExistsBF:input_type -> veigar.SearchFilesReq
	10, // 12: veigar.Veigar.AddFilesIntel:input_type -> veigar.AddFilesReq
	12, // 13: veigar.Veigar.SearchHostsIntel:input_type -> veigar.SearchHostsReq
	12, // 14: veigar.Veigar.HostsIntelExistsBF:input_type -> veigar.SearchHostsReq
	16, // 15: veigar.Veigar.AddHostsIntel:input_type -> veigar.AddHostsReq
	8,  // 16: veigar.Veigar.SearchFilesIntel:output_type -> veigar.SearchFilesResp
	9,  // 17: veigar.Veigar.FilesIntelExistsBF:output_type -> veigar.FilesIntelExistsResp
	11, // 18: veigar.Veigar.AddFilesIntel:output_type -> veigar.AddFilesResp
	14, // 19: veigar.Veigar.SearchHostsIntel:output_type -> veigar.SearchHostsResp
	15, // 20: veigar.Veigar.HostsIntelExistsBF:output_type -> veigar.HostsIntelExistsResp
	17, // 21: veigar.Veigar.AddHostsIntel:output_type -> veigar.AddHostsResp
	16, // [16:22] is the sub-list for method output_type
	10, // [10:16] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_veigar_veigar_proto_init() }
func file_veigar_veigar_proto_init() {
	if File_veigar_veigar_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_veigar_veigar_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_veigar_veigar_proto_goTypes,
		DependencyIndexes: file_veigar_veigar_proto_depIdxs,
		EnumInfos:         file_veigar_veigar_proto_enumTypes,
		MessageInfos:      file_veigar_veigar_proto_msgTypes,
	}.Build()
	File_veigar_veigar_proto = out.File
	file_veigar_veigar_proto_rawDesc = nil
	file_veigar_veigar_proto_goTypes = nil
	file_veigar_veigar_proto_depIdxs = nil
}
