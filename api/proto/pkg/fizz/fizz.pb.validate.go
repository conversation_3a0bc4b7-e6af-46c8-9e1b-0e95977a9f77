// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: fizz/fizz.proto

package fizz

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = agent.Command(0)
)

// Validate checks the field values on AddUnicastReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddUnicastReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddUnicastReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddUnicastReqMultiError, or
// nil if none found.
func (m *AddUnicastReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddUnicastReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddUnicastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddUnicastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddUnicastReqValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowDelete

	if len(errors) > 0 {
		return AddUnicastReqMultiError(errors)
	}

	return nil
}

// AddUnicastReqMultiError is an error wrapping multiple validation errors
// returned by AddUnicastReq.ValidateAll() if the designated constraints
// aren't met.
type AddUnicastReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddUnicastReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddUnicastReqMultiError) AllErrors() []error { return m }

// AddUnicastReqValidationError is the validation error returned by
// AddUnicastReq.Validate if the designated constraints aren't met.
type AddUnicastReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddUnicastReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddUnicastReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddUnicastReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddUnicastReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddUnicastReqValidationError) ErrorName() string { return "AddUnicastReqValidationError" }

// Error satisfies the builtin error interface
func (e AddUnicastReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddUnicastReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddUnicastReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddUnicastReqValidationError{}

// Validate checks the field values on AddUnicastResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddUnicastResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddUnicastResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddUnicastRespMultiError,
// or nil if none found.
func (m *AddUnicastResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddUnicastResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddUnicastRespMultiError(errors)
	}

	return nil
}

// AddUnicastRespMultiError is an error wrapping multiple validation errors
// returned by AddUnicastResp.ValidateAll() if the designated constraints
// aren't met.
type AddUnicastRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddUnicastRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddUnicastRespMultiError) AllErrors() []error { return m }

// AddUnicastRespValidationError is the validation error returned by
// AddUnicastResp.Validate if the designated constraints aren't met.
type AddUnicastRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddUnicastRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddUnicastRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddUnicastRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddUnicastRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddUnicastRespValidationError) ErrorName() string { return "AddUnicastRespValidationError" }

// Error satisfies the builtin error interface
func (e AddUnicastRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddUnicastResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddUnicastRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddUnicastRespValidationError{}

// Validate checks the field values on AddMulticastReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddMulticastReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMulticastReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddMulticastReqMultiError, or nil if none found.
func (m *AddMulticastReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMulticastReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Os

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddMulticastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddMulticastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddMulticastReqValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowDelete

	if len(errors) > 0 {
		return AddMulticastReqMultiError(errors)
	}

	return nil
}

// AddMulticastReqMultiError is an error wrapping multiple validation errors
// returned by AddMulticastReq.ValidateAll() if the designated constraints
// aren't met.
type AddMulticastReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMulticastReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMulticastReqMultiError) AllErrors() []error { return m }

// AddMulticastReqValidationError is the validation error returned by
// AddMulticastReq.Validate if the designated constraints aren't met.
type AddMulticastReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMulticastReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMulticastReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMulticastReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMulticastReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMulticastReqValidationError) ErrorName() string { return "AddMulticastReqValidationError" }

// Error satisfies the builtin error interface
func (e AddMulticastReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMulticastReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMulticastReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMulticastReqValidationError{}

// Validate checks the field values on AddMulticastResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddMulticastResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMulticastResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddMulticastRespMultiError, or nil if none found.
func (m *AddMulticastResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMulticastResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddMulticastRespMultiError(errors)
	}

	return nil
}

// AddMulticastRespMultiError is an error wrapping multiple validation errors
// returned by AddMulticastResp.ValidateAll() if the designated constraints
// aren't met.
type AddMulticastRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMulticastRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMulticastRespMultiError) AllErrors() []error { return m }

// AddMulticastRespValidationError is the validation error returned by
// AddMulticastResp.Validate if the designated constraints aren't met.
type AddMulticastRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMulticastRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMulticastRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMulticastRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMulticastRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMulticastRespValidationError) ErrorName() string { return "AddMulticastRespValidationError" }

// Error satisfies the builtin error interface
func (e AddMulticastRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMulticastResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMulticastRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMulticastRespValidationError{}

// Validate checks the field values on AddBroadcastReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddBroadcastReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBroadcastReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBroadcastReqMultiError, or nil if none found.
func (m *AddBroadcastReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBroadcastReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Os

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBroadcastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBroadcastReqValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBroadcastReqValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowDelete

	if len(errors) > 0 {
		return AddBroadcastReqMultiError(errors)
	}

	return nil
}

// AddBroadcastReqMultiError is an error wrapping multiple validation errors
// returned by AddBroadcastReq.ValidateAll() if the designated constraints
// aren't met.
type AddBroadcastReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBroadcastReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBroadcastReqMultiError) AllErrors() []error { return m }

// AddBroadcastReqValidationError is the validation error returned by
// AddBroadcastReq.Validate if the designated constraints aren't met.
type AddBroadcastReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBroadcastReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBroadcastReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBroadcastReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBroadcastReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBroadcastReqValidationError) ErrorName() string { return "AddBroadcastReqValidationError" }

// Error satisfies the builtin error interface
func (e AddBroadcastReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBroadcastReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBroadcastReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBroadcastReqValidationError{}

// Validate checks the field values on AddBroadcastResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddBroadcastResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBroadcastResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBroadcastRespMultiError, or nil if none found.
func (m *AddBroadcastResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBroadcastResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddBroadcastRespMultiError(errors)
	}

	return nil
}

// AddBroadcastRespMultiError is an error wrapping multiple validation errors
// returned by AddBroadcastResp.ValidateAll() if the designated constraints
// aren't met.
type AddBroadcastRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBroadcastRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBroadcastRespMultiError) AllErrors() []error { return m }

// AddBroadcastRespValidationError is the validation error returned by
// AddBroadcastResp.Validate if the designated constraints aren't met.
type AddBroadcastRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBroadcastRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBroadcastRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBroadcastRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBroadcastRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBroadcastRespValidationError) ErrorName() string { return "AddBroadcastRespValidationError" }

// Error satisfies the builtin error interface
func (e AddBroadcastRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBroadcastResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBroadcastRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBroadcastRespValidationError{}

// Validate checks the field values on PushMsgReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushMsgReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushMsgReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushMsgReqMultiError, or
// nil if none found.
func (m *PushMsgReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PushMsgReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FailedToCache

	// no validation rules for Random

	// no validation rules for Async

	// no validation rules for CmdId

	// no validation rules for Data

	if len(errors) > 0 {
		return PushMsgReqMultiError(errors)
	}

	return nil
}

// PushMsgReqMultiError is an error wrapping multiple validation errors
// returned by PushMsgReq.ValidateAll() if the designated constraints aren't met.
type PushMsgReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushMsgReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushMsgReqMultiError) AllErrors() []error { return m }

// PushMsgReqValidationError is the validation error returned by
// PushMsgReq.Validate if the designated constraints aren't met.
type PushMsgReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushMsgReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushMsgReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushMsgReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushMsgReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushMsgReqValidationError) ErrorName() string { return "PushMsgReqValidationError" }

// Error satisfies the builtin error interface
func (e PushMsgReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushMsgReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushMsgReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushMsgReqValidationError{}

// Validate checks the field values on PushMsgResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushMsgResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushMsgResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushMsgRespMultiError, or
// nil if none found.
func (m *PushMsgResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PushMsgResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PushMsgRespMultiError(errors)
	}

	return nil
}

// PushMsgRespMultiError is an error wrapping multiple validation errors
// returned by PushMsgResp.ValidateAll() if the designated constraints aren't met.
type PushMsgRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushMsgRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushMsgRespMultiError) AllErrors() []error { return m }

// PushMsgRespValidationError is the validation error returned by
// PushMsgResp.Validate if the designated constraints aren't met.
type PushMsgRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushMsgRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushMsgRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushMsgRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushMsgRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushMsgRespValidationError) ErrorName() string { return "PushMsgRespValidationError" }

// Error satisfies the builtin error interface
func (e PushMsgRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushMsgResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushMsgRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushMsgRespValidationError{}

// Validate checks the field values on DebugReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DebugReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebugReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DebugReqMultiError, or nil
// if none found.
func (m *DebugReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DebugReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Cond.(type) {
	case *DebugReq_MachineId:
		if v == nil {
			err := DebugReqValidationError{
				field:  "Cond",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for MachineId
	case *DebugReq_GroupId:
		if v == nil {
			err := DebugReqValidationError{
				field:  "Cond",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for GroupId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DebugReqMultiError(errors)
	}

	return nil
}

// DebugReqMultiError is an error wrapping multiple validation errors returned
// by DebugReq.ValidateAll() if the designated constraints aren't met.
type DebugReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebugReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebugReqMultiError) AllErrors() []error { return m }

// DebugReqValidationError is the validation error returned by
// DebugReq.Validate if the designated constraints aren't met.
type DebugReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebugReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebugReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebugReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebugReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebugReqValidationError) ErrorName() string { return "DebugReqValidationError" }

// Error satisfies the builtin error interface
func (e DebugReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebugReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebugReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebugReqValidationError{}

// Validate checks the field values on DebugResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DebugResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebugResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DebugRespMultiError, or nil
// if none found.
func (m *DebugResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DebugResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DebugRespMultiError(errors)
	}

	return nil
}

// DebugRespMultiError is an error wrapping multiple validation errors returned
// by DebugResp.ValidateAll() if the designated constraints aren't met.
type DebugRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebugRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebugRespMultiError) AllErrors() []error { return m }

// DebugRespValidationError is the validation error returned by
// DebugResp.Validate if the designated constraints aren't met.
type DebugRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebugRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebugRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebugRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebugRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebugRespValidationError) ErrorName() string { return "DebugRespValidationError" }

// Error satisfies the builtin error interface
func (e DebugRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebugResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebugRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebugRespValidationError{}

// Validate checks the field values on MsgContent with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MsgContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MsgContent with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MsgContentMultiError, or
// nil if none found.
func (m *MsgContent) ValidateAll() error {
	return m.validate(true)
}

func (m *MsgContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	// no validation rules for Category

	// no validation rules for Content

	// no validation rules for Size

	if len(errors) > 0 {
		return MsgContentMultiError(errors)
	}

	return nil
}

// MsgContentMultiError is an error wrapping multiple validation errors
// returned by MsgContent.ValidateAll() if the designated constraints aren't met.
type MsgContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MsgContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MsgContentMultiError) AllErrors() []error { return m }

// MsgContentValidationError is the validation error returned by
// MsgContent.Validate if the designated constraints aren't met.
type MsgContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MsgContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MsgContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MsgContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MsgContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MsgContentValidationError) ErrorName() string { return "MsgContentValidationError" }

// Error satisfies the builtin error interface
func (e MsgContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMsgContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MsgContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MsgContentValidationError{}

// Validate checks the field values on UnicastSubject with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UnicastSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnicastSubject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UnicastSubjectMultiError,
// or nil if none found.
func (m *UnicastSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *UnicastSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for MaxSeq

	// no validation rules for ContentId

	// no validation rules for Uuid

	// no validation rules for Type

	if len(errors) > 0 {
		return UnicastSubjectMultiError(errors)
	}

	return nil
}

// UnicastSubjectMultiError is an error wrapping multiple validation errors
// returned by UnicastSubject.ValidateAll() if the designated constraints
// aren't met.
type UnicastSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnicastSubjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnicastSubjectMultiError) AllErrors() []error { return m }

// UnicastSubjectValidationError is the validation error returned by
// UnicastSubject.Validate if the designated constraints aren't met.
type UnicastSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnicastSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnicastSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnicastSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnicastSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnicastSubjectValidationError) ErrorName() string { return "UnicastSubjectValidationError" }

// Error satisfies the builtin error interface
func (e UnicastSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnicastSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnicastSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnicastSubjectValidationError{}

// Validate checks the field values on MulticastSubject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MulticastSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MulticastSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MulticastSubjectMultiError, or nil if none found.
func (m *MulticastSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *MulticastSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	// no validation rules for MaxSeq

	// no validation rules for ContentId

	// no validation rules for Uuid

	// no validation rules for Type

	// no validation rules for Os

	if len(errors) > 0 {
		return MulticastSubjectMultiError(errors)
	}

	return nil
}

// MulticastSubjectMultiError is an error wrapping multiple validation errors
// returned by MulticastSubject.ValidateAll() if the designated constraints
// aren't met.
type MulticastSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MulticastSubjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MulticastSubjectMultiError) AllErrors() []error { return m }

// MulticastSubjectValidationError is the validation error returned by
// MulticastSubject.Validate if the designated constraints aren't met.
type MulticastSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MulticastSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MulticastSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MulticastSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MulticastSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MulticastSubjectValidationError) ErrorName() string { return "MulticastSubjectValidationError" }

// Error satisfies the builtin error interface
func (e MulticastSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMulticastSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MulticastSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MulticastSubjectValidationError{}

// Validate checks the field values on BroadcastSubject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BroadcastSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BroadcastSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BroadcastSubjectMultiError, or nil if none found.
func (m *BroadcastSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *BroadcastSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxSeq

	// no validation rules for ContentId

	// no validation rules for Uuid

	// no validation rules for Type

	// no validation rules for Os

	if len(errors) > 0 {
		return BroadcastSubjectMultiError(errors)
	}

	return nil
}

// BroadcastSubjectMultiError is an error wrapping multiple validation errors
// returned by BroadcastSubject.ValidateAll() if the designated constraints
// aren't met.
type BroadcastSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BroadcastSubjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BroadcastSubjectMultiError) AllErrors() []error { return m }

// BroadcastSubjectValidationError is the validation error returned by
// BroadcastSubject.Validate if the designated constraints aren't met.
type BroadcastSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BroadcastSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BroadcastSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BroadcastSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BroadcastSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BroadcastSubjectValidationError) ErrorName() string { return "BroadcastSubjectValidationError" }

// Error satisfies the builtin error interface
func (e BroadcastSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBroadcastSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BroadcastSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BroadcastSubjectValidationError{}

// Validate checks the field values on MachineProfileSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MachineProfileSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MachineProfileSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MachineProfileSubjectMultiError, or nil if none found.
func (m *MachineProfileSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *MachineProfileSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if len(errors) > 0 {
		return MachineProfileSubjectMultiError(errors)
	}

	return nil
}

// MachineProfileSubjectMultiError is an error wrapping multiple validation
// errors returned by MachineProfileSubject.ValidateAll() if the designated
// constraints aren't met.
type MachineProfileSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MachineProfileSubjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MachineProfileSubjectMultiError) AllErrors() []error { return m }

// MachineProfileSubjectValidationError is the validation error returned by
// MachineProfileSubject.Validate if the designated constraints aren't met.
type MachineProfileSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MachineProfileSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MachineProfileSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MachineProfileSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MachineProfileSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MachineProfileSubjectValidationError) ErrorName() string {
	return "MachineProfileSubjectValidationError"
}

// Error satisfies the builtin error interface
func (e MachineProfileSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMachineProfileSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MachineProfileSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MachineProfileSubjectValidationError{}

// Validate checks the field values on ContentProfileSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ContentProfileSubject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContentProfileSubject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ContentProfileSubjectMultiError, or nil if none found.
func (m *ContentProfileSubject) ValidateAll() error {
	return m.validate(true)
}

func (m *ContentProfileSubject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ContentId

	if len(errors) > 0 {
		return ContentProfileSubjectMultiError(errors)
	}

	return nil
}

// ContentProfileSubjectMultiError is an error wrapping multiple validation
// errors returned by ContentProfileSubject.ValidateAll() if the designated
// constraints aren't met.
type ContentProfileSubjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentProfileSubjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentProfileSubjectMultiError) AllErrors() []error { return m }

// ContentProfileSubjectValidationError is the validation error returned by
// ContentProfileSubject.Validate if the designated constraints aren't met.
type ContentProfileSubjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentProfileSubjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentProfileSubjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentProfileSubjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentProfileSubjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentProfileSubjectValidationError) ErrorName() string {
	return "ContentProfileSubjectValidationError"
}

// Error satisfies the builtin error interface
func (e ContentProfileSubjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContentProfileSubject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentProfileSubjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentProfileSubjectValidationError{}

// Validate checks the field values on MachineProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MachineProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MachineProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MachineProfileMultiError,
// or nil if none found.
func (m *MachineProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *MachineProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for MaxSeq

	for idx, item := range m.GetContents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MachineProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MachineProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MachineProfileValidationError{
					field:  fmt.Sprintf("Contents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MachineProfileMultiError(errors)
	}

	return nil
}

// MachineProfileMultiError is an error wrapping multiple validation errors
// returned by MachineProfile.ValidateAll() if the designated constraints
// aren't met.
type MachineProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MachineProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MachineProfileMultiError) AllErrors() []error { return m }

// MachineProfileValidationError is the validation error returned by
// MachineProfile.Validate if the designated constraints aren't met.
type MachineProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MachineProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MachineProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MachineProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MachineProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MachineProfileValidationError) ErrorName() string { return "MachineProfileValidationError" }

// Error satisfies the builtin error interface
func (e MachineProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMachineProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MachineProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MachineProfileValidationError{}

// Validate checks the field values on MulticastProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MulticastProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MulticastProfile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MulticastProfileMultiError, or nil if none found.
func (m *MulticastProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *MulticastProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupId

	// no validation rules for MaxSeq

	for idx, item := range m.GetContents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MulticastProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MulticastProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MulticastProfileValidationError{
					field:  fmt.Sprintf("Contents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MulticastProfileMultiError(errors)
	}

	return nil
}

// MulticastProfileMultiError is an error wrapping multiple validation errors
// returned by MulticastProfile.ValidateAll() if the designated constraints
// aren't met.
type MulticastProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MulticastProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MulticastProfileMultiError) AllErrors() []error { return m }

// MulticastProfileValidationError is the validation error returned by
// MulticastProfile.Validate if the designated constraints aren't met.
type MulticastProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MulticastProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MulticastProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MulticastProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MulticastProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MulticastProfileValidationError) ErrorName() string { return "MulticastProfileValidationError" }

// Error satisfies the builtin error interface
func (e MulticastProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMulticastProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MulticastProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MulticastProfileValidationError{}

// Validate checks the field values on BroadcastProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BroadcastProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BroadcastProfile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BroadcastProfileMultiError, or nil if none found.
func (m *BroadcastProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *BroadcastProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxSeq

	for idx, item := range m.GetContents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BroadcastProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BroadcastProfileValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BroadcastProfileValidationError{
					field:  fmt.Sprintf("Contents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BroadcastProfileMultiError(errors)
	}

	return nil
}

// BroadcastProfileMultiError is an error wrapping multiple validation errors
// returned by BroadcastProfile.ValidateAll() if the designated constraints
// aren't met.
type BroadcastProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BroadcastProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BroadcastProfileMultiError) AllErrors() []error { return m }

// BroadcastProfileValidationError is the validation error returned by
// BroadcastProfile.Validate if the designated constraints aren't met.
type BroadcastProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BroadcastProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BroadcastProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BroadcastProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BroadcastProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BroadcastProfileValidationError) ErrorName() string { return "BroadcastProfileValidationError" }

// Error satisfies the builtin error interface
func (e BroadcastProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBroadcastProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BroadcastProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BroadcastProfileValidationError{}

// Validate checks the field values on ContentProfile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ContentProfile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ContentProfile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ContentProfileMultiError,
// or nil if none found.
func (m *ContentProfile) ValidateAll() error {
	return m.validate(true)
}

func (m *ContentProfile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Category

	// no validation rules for MsgType

	// no validation rules for Content

	// no validation rules for Size

	// no validation rules for Os

	if len(errors) > 0 {
		return ContentProfileMultiError(errors)
	}

	return nil
}

// ContentProfileMultiError is an error wrapping multiple validation errors
// returned by ContentProfile.ValidateAll() if the designated constraints
// aren't met.
type ContentProfileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentProfileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentProfileMultiError) AllErrors() []error { return m }

// ContentProfileValidationError is the validation error returned by
// ContentProfile.Validate if the designated constraints aren't met.
type ContentProfileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentProfileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentProfileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentProfileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentProfileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentProfileValidationError) ErrorName() string { return "ContentProfileValidationError" }

// Error satisfies the builtin error interface
func (e ContentProfileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContentProfile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentProfileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentProfileValidationError{}

// Validate checks the field values on MachineProfile_Content with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MachineProfile_Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MachineProfile_Content with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MachineProfile_ContentMultiError, or nil if none found.
func (m *MachineProfile_Content) ValidateAll() error {
	return m.validate(true)
}

func (m *MachineProfile_Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Seq

	// no validation rules for Uuid

	// no validation rules for Type

	if len(errors) > 0 {
		return MachineProfile_ContentMultiError(errors)
	}

	return nil
}

// MachineProfile_ContentMultiError is an error wrapping multiple validation
// errors returned by MachineProfile_Content.ValidateAll() if the designated
// constraints aren't met.
type MachineProfile_ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MachineProfile_ContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MachineProfile_ContentMultiError) AllErrors() []error { return m }

// MachineProfile_ContentValidationError is the validation error returned by
// MachineProfile_Content.Validate if the designated constraints aren't met.
type MachineProfile_ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MachineProfile_ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MachineProfile_ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MachineProfile_ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MachineProfile_ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MachineProfile_ContentValidationError) ErrorName() string {
	return "MachineProfile_ContentValidationError"
}

// Error satisfies the builtin error interface
func (e MachineProfile_ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMachineProfile_Content.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MachineProfile_ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MachineProfile_ContentValidationError{}

// Validate checks the field values on MulticastProfile_Content with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MulticastProfile_Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MulticastProfile_Content with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MulticastProfile_ContentMultiError, or nil if none found.
func (m *MulticastProfile_Content) ValidateAll() error {
	return m.validate(true)
}

func (m *MulticastProfile_Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Seq

	// no validation rules for Uuid

	// no validation rules for Type

	// no validation rules for Os

	if len(errors) > 0 {
		return MulticastProfile_ContentMultiError(errors)
	}

	return nil
}

// MulticastProfile_ContentMultiError is an error wrapping multiple validation
// errors returned by MulticastProfile_Content.ValidateAll() if the designated
// constraints aren't met.
type MulticastProfile_ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MulticastProfile_ContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MulticastProfile_ContentMultiError) AllErrors() []error { return m }

// MulticastProfile_ContentValidationError is the validation error returned by
// MulticastProfile_Content.Validate if the designated constraints aren't met.
type MulticastProfile_ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MulticastProfile_ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MulticastProfile_ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MulticastProfile_ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MulticastProfile_ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MulticastProfile_ContentValidationError) ErrorName() string {
	return "MulticastProfile_ContentValidationError"
}

// Error satisfies the builtin error interface
func (e MulticastProfile_ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMulticastProfile_Content.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MulticastProfile_ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MulticastProfile_ContentValidationError{}

// Validate checks the field values on BroadcastProfile_Content with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BroadcastProfile_Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BroadcastProfile_Content with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BroadcastProfile_ContentMultiError, or nil if none found.
func (m *BroadcastProfile_Content) ValidateAll() error {
	return m.validate(true)
}

func (m *BroadcastProfile_Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Seq

	// no validation rules for Uuid

	// no validation rules for Type

	// no validation rules for Os

	if len(errors) > 0 {
		return BroadcastProfile_ContentMultiError(errors)
	}

	return nil
}

// BroadcastProfile_ContentMultiError is an error wrapping multiple validation
// errors returned by BroadcastProfile_Content.ValidateAll() if the designated
// constraints aren't met.
type BroadcastProfile_ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BroadcastProfile_ContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BroadcastProfile_ContentMultiError) AllErrors() []error { return m }

// BroadcastProfile_ContentValidationError is the validation error returned by
// BroadcastProfile_Content.Validate if the designated constraints aren't met.
type BroadcastProfile_ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BroadcastProfile_ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BroadcastProfile_ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BroadcastProfile_ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BroadcastProfile_ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BroadcastProfile_ContentValidationError) ErrorName() string {
	return "BroadcastProfile_ContentValidationError"
}

// Error satisfies the builtin error interface
func (e BroadcastProfile_ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBroadcastProfile_Content.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BroadcastProfile_ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BroadcastProfile_ContentValidationError{}
