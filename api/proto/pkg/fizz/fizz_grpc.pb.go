// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: fizz/fizz.proto

package fizz

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Fizz_AddUnicast_FullMethodName   = "/fizz.Fizz/AddUnicast"
	Fizz_AddMulticast_FullMethodName = "/fizz.Fizz/AddMulticast"
	Fizz_AddBroadcast_FullMethodName = "/fizz.Fizz/AddBroadcast"
	Fizz_PushMsg_FullMethodName      = "/fizz.Fizz/PushMsg"
	Fizz_Debug_FullMethodName        = "/fizz.Fizz/Debug"
)

// FizzClient is the client API for Fizz service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 消息中心服务
type FizzClient interface {
	// 添加单播消息
	AddUnicast(ctx context.Context, in *AddUnicastReq, opts ...grpc.CallOption) (*AddUnicastResp, error)
	// 添加组播消息
	AddMulticast(ctx context.Context, in *AddMulticastReq, opts ...grpc.CallOption) (*AddMulticastResp, error)
	// 添加广播消息
	AddBroadcast(ctx context.Context, in *AddBroadcastReq, opts ...grpc.CallOption) (*AddBroadcastResp, error)
	// 消息推送
	PushMsg(ctx context.Context, in *PushMsgReq, opts ...grpc.CallOption) (*PushMsgResp, error)
	// 调试接口
	Debug(ctx context.Context, in *DebugReq, opts ...grpc.CallOption) (*DebugResp, error)
}

type fizzClient struct {
	cc grpc.ClientConnInterface
}

func NewFizzClient(cc grpc.ClientConnInterface) FizzClient {
	return &fizzClient{cc}
}

func (c *fizzClient) AddUnicast(ctx context.Context, in *AddUnicastReq, opts ...grpc.CallOption) (*AddUnicastResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddUnicastResp)
	err := c.cc.Invoke(ctx, Fizz_AddUnicast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fizzClient) AddMulticast(ctx context.Context, in *AddMulticastReq, opts ...grpc.CallOption) (*AddMulticastResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddMulticastResp)
	err := c.cc.Invoke(ctx, Fizz_AddMulticast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fizzClient) AddBroadcast(ctx context.Context, in *AddBroadcastReq, opts ...grpc.CallOption) (*AddBroadcastResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBroadcastResp)
	err := c.cc.Invoke(ctx, Fizz_AddBroadcast_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fizzClient) PushMsg(ctx context.Context, in *PushMsgReq, opts ...grpc.CallOption) (*PushMsgResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushMsgResp)
	err := c.cc.Invoke(ctx, Fizz_PushMsg_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fizzClient) Debug(ctx context.Context, in *DebugReq, opts ...grpc.CallOption) (*DebugResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DebugResp)
	err := c.cc.Invoke(ctx, Fizz_Debug_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FizzServer is the server API for Fizz service.
// All implementations must embed UnimplementedFizzServer
// for forward compatibility.
//
// 消息中心服务
type FizzServer interface {
	// 添加单播消息
	AddUnicast(context.Context, *AddUnicastReq) (*AddUnicastResp, error)
	// 添加组播消息
	AddMulticast(context.Context, *AddMulticastReq) (*AddMulticastResp, error)
	// 添加广播消息
	AddBroadcast(context.Context, *AddBroadcastReq) (*AddBroadcastResp, error)
	// 消息推送
	PushMsg(context.Context, *PushMsgReq) (*PushMsgResp, error)
	// 调试接口
	Debug(context.Context, *DebugReq) (*DebugResp, error)
	mustEmbedUnimplementedFizzServer()
}

// UnimplementedFizzServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFizzServer struct{}

func (UnimplementedFizzServer) AddUnicast(context.Context, *AddUnicastReq) (*AddUnicastResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUnicast not implemented")
}
func (UnimplementedFizzServer) AddMulticast(context.Context, *AddMulticastReq) (*AddMulticastResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddMulticast not implemented")
}
func (UnimplementedFizzServer) AddBroadcast(context.Context, *AddBroadcastReq) (*AddBroadcastResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBroadcast not implemented")
}
func (UnimplementedFizzServer) PushMsg(context.Context, *PushMsgReq) (*PushMsgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMsg not implemented")
}
func (UnimplementedFizzServer) Debug(context.Context, *DebugReq) (*DebugResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Debug not implemented")
}
func (UnimplementedFizzServer) mustEmbedUnimplementedFizzServer() {}
func (UnimplementedFizzServer) testEmbeddedByValue()              {}

// UnsafeFizzServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FizzServer will
// result in compilation errors.
type UnsafeFizzServer interface {
	mustEmbedUnimplementedFizzServer()
}

func RegisterFizzServer(s grpc.ServiceRegistrar, srv FizzServer) {
	// If the following call pancis, it indicates UnimplementedFizzServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Fizz_ServiceDesc, srv)
}

func _Fizz_AddUnicast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUnicastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FizzServer).AddUnicast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fizz_AddUnicast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FizzServer).AddUnicast(ctx, req.(*AddUnicastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fizz_AddMulticast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMulticastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FizzServer).AddMulticast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fizz_AddMulticast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FizzServer).AddMulticast(ctx, req.(*AddMulticastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fizz_AddBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBroadcastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FizzServer).AddBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fizz_AddBroadcast_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FizzServer).AddBroadcast(ctx, req.(*AddBroadcastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fizz_PushMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FizzServer).PushMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fizz_PushMsg_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FizzServer).PushMsg(ctx, req.(*PushMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Fizz_Debug_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebugReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FizzServer).Debug(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Fizz_Debug_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FizzServer).Debug(ctx, req.(*DebugReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Fizz_ServiceDesc is the grpc.ServiceDesc for Fizz service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Fizz_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "fizz.Fizz",
	HandlerType: (*FizzServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddUnicast",
			Handler:    _Fizz_AddUnicast_Handler,
		},
		{
			MethodName: "AddMulticast",
			Handler:    _Fizz_AddMulticast_Handler,
		},
		{
			MethodName: "AddBroadcast",
			Handler:    _Fizz_AddBroadcast_Handler,
		},
		{
			MethodName: "PushMsg",
			Handler:    _Fizz_PushMsg_Handler,
		},
		{
			MethodName: "Debug",
			Handler:    _Fizz_Debug_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "fizz/fizz.proto",
}
