// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: fizz/fizz.proto

package fizz

import (
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 操作系统类型
type OsType int32

const (
	OsType_OS_TYPE_UNKNOWN OsType = 0 // 类型未知
	OsType_OS_TYPE_WINDOWS OsType = 1
	OsType_OS_TYPE_LINUX   OsType = 2
)

// Enum value maps for OsType.
var (
	OsType_name = map[int32]string{
		0: "OS_TYPE_UNKNOWN",
		1: "OS_TYPE_WINDOWS",
		2: "OS_TYPE_LINUX",
	}
	OsType_value = map[string]int32{
		"OS_TYPE_UNKNOWN": 0,
		"OS_TYPE_WINDOWS": 1,
		"OS_TYPE_LINUX":   2,
	}
)

func (x OsType) Enum() *OsType {
	p := new(OsType)
	*p = x
	return p
}

func (x OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_fizz_fizz_proto_enumTypes[0].Descriptor()
}

func (OsType) Type() protoreflect.EnumType {
	return &file_fizz_fizz_proto_enumTypes[0]
}

func (x OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OsType.Descriptor instead.
func (OsType) EnumDescriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{0}
}

// 消息类型枚举
type MsgContentType int32

const (
	MsgContentType_MS_TYPE_UNKNOWN   MsgContentType = 0 // 未知
	MsgContentType_MS_TYPE_UNICAST   MsgContentType = 1 // 单播消息
	MsgContentType_MS_TYPE_MULTICAST MsgContentType = 2 // 组播消息
	MsgContentType_MS_TYPE_BROADCAST MsgContentType = 3 // 广播消息
)

// Enum value maps for MsgContentType.
var (
	MsgContentType_name = map[int32]string{
		0: "MS_TYPE_UNKNOWN",
		1: "MS_TYPE_UNICAST",
		2: "MS_TYPE_MULTICAST",
		3: "MS_TYPE_BROADCAST",
	}
	MsgContentType_value = map[string]int32{
		"MS_TYPE_UNKNOWN":   0,
		"MS_TYPE_UNICAST":   1,
		"MS_TYPE_MULTICAST": 2,
		"MS_TYPE_BROADCAST": 3,
	}
)

func (x MsgContentType) Enum() *MsgContentType {
	p := new(MsgContentType)
	*p = x
	return p
}

func (x MsgContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_fizz_fizz_proto_enumTypes[1].Descriptor()
}

func (MsgContentType) Type() protoreflect.EnumType {
	return &file_fizz_fizz_proto_enumTypes[1]
}

func (x MsgContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgContentType.Descriptor instead.
func (MsgContentType) EnumDescriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{1}
}

// 消息内容存储类型枚举
type MsgStoreType int32

const (
	MsgStoreType_MS_STORE_TYPE_UNKNOWN MsgStoreType = 0 // 未知
	MsgStoreType_MS_STORE_TYPE_DB      MsgStoreType = 1 // 数据库
	MsgStoreType_MS_STORE_TYPE_KV      MsgStoreType = 2 // kv存储
)

// Enum value maps for MsgStoreType.
var (
	MsgStoreType_name = map[int32]string{
		0: "MS_STORE_TYPE_UNKNOWN",
		1: "MS_STORE_TYPE_DB",
		2: "MS_STORE_TYPE_KV",
	}
	MsgStoreType_value = map[string]int32{
		"MS_STORE_TYPE_UNKNOWN": 0,
		"MS_STORE_TYPE_DB":      1,
		"MS_STORE_TYPE_KV":      2,
	}
)

func (x MsgStoreType) Enum() *MsgStoreType {
	p := new(MsgStoreType)
	*p = x
	return p
}

func (x MsgStoreType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgStoreType) Descriptor() protoreflect.EnumDescriptor {
	return file_fizz_fizz_proto_enumTypes[2].Descriptor()
}

func (MsgStoreType) Type() protoreflect.EnumType {
	return &file_fizz_fizz_proto_enumTypes[2]
}

func (x MsgStoreType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgStoreType.Descriptor instead.
func (MsgStoreType) EnumDescriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{2}
}

// 消息内容分类类型枚举
type MsgCategoryType int32

const (
	MsgCategoryType_MSG_CATEGORY_TYPE_UNKNOWN MsgCategoryType = 0
	// 主机配置变更通知消息使用 10xx 格式
	MsgCategoryType_MSG_CATEGORY_TYPE_AGENT_GROUP_ID_CHANGE MsgCategoryType = 1001 // unicast, 主机, 移动分组
	// 策略消息使用 20xx 格式
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_RISK_SWITCH        MsgCategoryType = 2001 // multicast, 策略, 线索上报开关
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_GLOBAL_SWITCH      MsgCategoryType = 2002 // multicast, 策略, 全局开关
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_FILE_SCAN          MsgCategoryType = 2003 // multicast, 策略, 文件扫描配置
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_ABNORMAL_LOGIN     MsgCategoryType = 2004 // multicast, 策略, 异常登录配置
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_UNINSTALL_PASSWORD MsgCategoryType = 2005 // broadcast, 策略, agent 卸载密码
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_WHITE_LIST         MsgCategoryType = 2006 // multicast, 策略, 白名单更新/删除
	MsgCategoryType_MSG_CATEGORY_TYPE_POLICY_CLOUD_WHITE_LIST   MsgCategoryType = 2007 // broadcast, 策略, 云白名单更新/删除
	// 线索、文件处置、取证消息使用 30xx格式
	MsgCategoryType_MSG_CATEGORY_TYPE_HANDLE_FILES    MsgCategoryType = 3001 // unicast, 文件处置, 包含隔离、删除、恢复操作
	MsgCategoryType_MSG_CATEGORY_TYPE_OBTAIN_EVIDENCE MsgCategoryType = 3002 // unicast, 取证
)

// Enum value maps for MsgCategoryType.
var (
	MsgCategoryType_name = map[int32]string{
		0:    "MSG_CATEGORY_TYPE_UNKNOWN",
		1001: "MSG_CATEGORY_TYPE_AGENT_GROUP_ID_CHANGE",
		2001: "MSG_CATEGORY_TYPE_POLICY_RISK_SWITCH",
		2002: "MSG_CATEGORY_TYPE_POLICY_GLOBAL_SWITCH",
		2003: "MSG_CATEGORY_TYPE_POLICY_FILE_SCAN",
		2004: "MSG_CATEGORY_TYPE_POLICY_ABNORMAL_LOGIN",
		2005: "MSG_CATEGORY_TYPE_POLICY_UNINSTALL_PASSWORD",
		2006: "MSG_CATEGORY_TYPE_POLICY_WHITE_LIST",
		2007: "MSG_CATEGORY_TYPE_POLICY_CLOUD_WHITE_LIST",
		3001: "MSG_CATEGORY_TYPE_HANDLE_FILES",
		3002: "MSG_CATEGORY_TYPE_OBTAIN_EVIDENCE",
	}
	MsgCategoryType_value = map[string]int32{
		"MSG_CATEGORY_TYPE_UNKNOWN":                   0,
		"MSG_CATEGORY_TYPE_AGENT_GROUP_ID_CHANGE":     1001,
		"MSG_CATEGORY_TYPE_POLICY_RISK_SWITCH":        2001,
		"MSG_CATEGORY_TYPE_POLICY_GLOBAL_SWITCH":      2002,
		"MSG_CATEGORY_TYPE_POLICY_FILE_SCAN":          2003,
		"MSG_CATEGORY_TYPE_POLICY_ABNORMAL_LOGIN":     2004,
		"MSG_CATEGORY_TYPE_POLICY_UNINSTALL_PASSWORD": 2005,
		"MSG_CATEGORY_TYPE_POLICY_WHITE_LIST":         2006,
		"MSG_CATEGORY_TYPE_POLICY_CLOUD_WHITE_LIST":   2007,
		"MSG_CATEGORY_TYPE_HANDLE_FILES":              3001,
		"MSG_CATEGORY_TYPE_OBTAIN_EVIDENCE":           3002,
	}
)

func (x MsgCategoryType) Enum() *MsgCategoryType {
	p := new(MsgCategoryType)
	*p = x
	return p
}

func (x MsgCategoryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MsgCategoryType) Descriptor() protoreflect.EnumDescriptor {
	return file_fizz_fizz_proto_enumTypes[3].Descriptor()
}

func (MsgCategoryType) Type() protoreflect.EnumType {
	return &file_fizz_fizz_proto_enumTypes[3]
}

func (x MsgCategoryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MsgCategoryType.Descriptor instead.
func (MsgCategoryType) EnumDescriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{3}
}

type AddUnicastReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string      `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`        // 设备唯一标识
	Content     *MsgContent `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`                             // 消息内容
	AllowDelete bool        `protobuf:"varint,3,opt,name=allow_delete,json=allowDelete,proto3" json:"allow_delete,omitempty"` // 消息是否允许删除
}

func (x *AddUnicastReq) Reset() {
	*x = AddUnicastReq{}
	mi := &file_fizz_fizz_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUnicastReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUnicastReq) ProtoMessage() {}

func (x *AddUnicastReq) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUnicastReq.ProtoReflect.Descriptor instead.
func (*AddUnicastReq) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{0}
}

func (x *AddUnicastReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AddUnicastReq) GetContent() *MsgContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *AddUnicastReq) GetAllowDelete() bool {
	if x != nil {
		return x.AllowDelete
	}
	return false
}

type AddUnicastResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddUnicastResp) Reset() {
	*x = AddUnicastResp{}
	mi := &file_fizz_fizz_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUnicastResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUnicastResp) ProtoMessage() {}

func (x *AddUnicastResp) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUnicastResp.ProtoReflect.Descriptor instead.
func (*AddUnicastResp) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{1}
}

type AddMulticastReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupIds    []int64     `protobuf:"varint,1,rep,packed,name=group_ids,json=groupIds,proto3" json:"group_ids,omitempty"`   // 分组id
	Os          OsType      `protobuf:"varint,2,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"`                     // 操作系统类型
	Content     *MsgContent `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`                             // 消息内容
	AllowDelete bool        `protobuf:"varint,4,opt,name=allow_delete,json=allowDelete,proto3" json:"allow_delete,omitempty"` // 消息是否允许删除
}

func (x *AddMulticastReq) Reset() {
	*x = AddMulticastReq{}
	mi := &file_fizz_fizz_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMulticastReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMulticastReq) ProtoMessage() {}

func (x *AddMulticastReq) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMulticastReq.ProtoReflect.Descriptor instead.
func (*AddMulticastReq) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{2}
}

func (x *AddMulticastReq) GetGroupIds() []int64 {
	if x != nil {
		return x.GroupIds
	}
	return nil
}

func (x *AddMulticastReq) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

func (x *AddMulticastReq) GetContent() *MsgContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *AddMulticastReq) GetAllowDelete() bool {
	if x != nil {
		return x.AllowDelete
	}
	return false
}

type AddMulticastResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddMulticastResp) Reset() {
	*x = AddMulticastResp{}
	mi := &file_fizz_fizz_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddMulticastResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMulticastResp) ProtoMessage() {}

func (x *AddMulticastResp) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMulticastResp.ProtoReflect.Descriptor instead.
func (*AddMulticastResp) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{3}
}

type AddBroadcastReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os          OsType      `protobuf:"varint,1,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"`                     // 操作系统类型
	Content     *MsgContent `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`                             // 消息内容
	AllowDelete bool        `protobuf:"varint,3,opt,name=allow_delete,json=allowDelete,proto3" json:"allow_delete,omitempty"` // 消息是否允许删除
}

func (x *AddBroadcastReq) Reset() {
	*x = AddBroadcastReq{}
	mi := &file_fizz_fizz_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBroadcastReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBroadcastReq) ProtoMessage() {}

func (x *AddBroadcastReq) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBroadcastReq.ProtoReflect.Descriptor instead.
func (*AddBroadcastReq) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{4}
}

func (x *AddBroadcastReq) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

func (x *AddBroadcastReq) GetContent() *MsgContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *AddBroadcastReq) GetAllowDelete() bool {
	if x != nil {
		return x.AllowDelete
	}
	return false
}

type AddBroadcastResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddBroadcastResp) Reset() {
	*x = AddBroadcastResp{}
	mi := &file_fizz_fizz_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBroadcastResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBroadcastResp) ProtoMessage() {}

func (x *AddBroadcastResp) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBroadcastResp.ProtoReflect.Descriptor instead.
func (*AddBroadcastResp) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{5}
}

type PushMsgReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIds    []string      `protobuf:"bytes,1,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`             // 设备列表，注意：如果为空代表广播消息
	FailedToCache bool          `protobuf:"varint,2,opt,name=failed_to_cache,json=failedToCache,proto3" json:"failed_to_cache,omitempty"` // 推送失败是否缓存
	Random        bool          `protobuf:"varint,3,opt,name=random,proto3" json:"random,omitempty"`                                      // 随机选取一个在线主机下发
	Async         bool          `protobuf:"varint,4,opt,name=async,proto3" json:"async,omitempty"`                                        // 是否开启异步推送（默认同步推送）
	CmdId         agent.Command `protobuf:"varint,5,opt,name=cmd_id,json=cmdId,proto3,enum=agent.Command" json:"cmd_id,omitempty"`        // 消息类型
	Data          []byte        `protobuf:"bytes,6,opt,name=data,proto3" json:"data,omitempty"`                                           // 需要推送的内容
}

func (x *PushMsgReq) Reset() {
	*x = PushMsgReq{}
	mi := &file_fizz_fizz_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushMsgReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMsgReq) ProtoMessage() {}

func (x *PushMsgReq) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMsgReq.ProtoReflect.Descriptor instead.
func (*PushMsgReq) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{6}
}

func (x *PushMsgReq) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

func (x *PushMsgReq) GetFailedToCache() bool {
	if x != nil {
		return x.FailedToCache
	}
	return false
}

func (x *PushMsgReq) GetRandom() bool {
	if x != nil {
		return x.Random
	}
	return false
}

func (x *PushMsgReq) GetAsync() bool {
	if x != nil {
		return x.Async
	}
	return false
}

func (x *PushMsgReq) GetCmdId() agent.Command {
	if x != nil {
		return x.CmdId
	}
	return agent.Command(0)
}

func (x *PushMsgReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushMsgResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PushMsgResp) Reset() {
	*x = PushMsgResp{}
	mi := &file_fizz_fizz_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushMsgResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMsgResp) ProtoMessage() {}

func (x *PushMsgResp) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMsgResp.ProtoReflect.Descriptor instead.
func (*PushMsgResp) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{7}
}

type DebugReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Cond:
	//
	//	*DebugReq_MachineId
	//	*DebugReq_GroupId
	Cond isDebugReq_Cond `protobuf_oneof:"cond"`
}

func (x *DebugReq) Reset() {
	*x = DebugReq{}
	mi := &file_fizz_fizz_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebugReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugReq) ProtoMessage() {}

func (x *DebugReq) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugReq.ProtoReflect.Descriptor instead.
func (*DebugReq) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{8}
}

func (m *DebugReq) GetCond() isDebugReq_Cond {
	if m != nil {
		return m.Cond
	}
	return nil
}

func (x *DebugReq) GetMachineId() string {
	if x, ok := x.GetCond().(*DebugReq_MachineId); ok {
		return x.MachineId
	}
	return ""
}

func (x *DebugReq) GetGroupId() int64 {
	if x, ok := x.GetCond().(*DebugReq_GroupId); ok {
		return x.GroupId
	}
	return 0
}

type isDebugReq_Cond interface {
	isDebugReq_Cond()
}

type DebugReq_MachineId struct {
	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3,oneof"` // 设备唯一标识
}

type DebugReq_GroupId struct {
	GroupId int64 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3,oneof"` // 分组id
}

func (*DebugReq_MachineId) isDebugReq_Cond() {}

func (*DebugReq_GroupId) isDebugReq_Cond() {}

type DebugResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DebugResp) Reset() {
	*x = DebugResp{}
	mi := &file_fizz_fizz_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DebugResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugResp) ProtoMessage() {}

func (x *DebugResp) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DebugResp.ProtoReflect.Descriptor instead.
func (*DebugResp) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{9}
}

// 消息内容
type MsgContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid     string          `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`                                    // 消息唯一标识（通过uuid进行去重操作）
	Category MsgCategoryType `protobuf:"varint,2,opt,name=category,proto3,enum=fizz.MsgCategoryType" json:"category,omitempty"` // 消息分类
	Content  []byte          `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`                              // 消息内容
	Size     int64           `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`                                   // 消息大小
}

func (x *MsgContent) Reset() {
	*x = MsgContent{}
	mi := &file_fizz_fizz_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MsgContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgContent) ProtoMessage() {}

func (x *MsgContent) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgContent.ProtoReflect.Descriptor instead.
func (*MsgContent) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{10}
}

func (x *MsgContent) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MsgContent) GetCategory() MsgCategoryType {
	if x != nil {
		return x.Category
	}
	return MsgCategoryType_MSG_CATEGORY_TYPE_UNKNOWN
}

func (x *MsgContent) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *MsgContent) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

// 消息队列数据结构
type UnicastSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string       `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	MaxSeq    int64        `protobuf:"varint,2,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"`
	ContentId int64        `protobuf:"varint,3,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	Uuid      string       `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`                         // 消息唯一标识
	Type      MsgStoreType `protobuf:"varint,5,opt,name=type,proto3,enum=fizz.MsgStoreType" json:"type,omitempty"` // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
}

func (x *UnicastSubject) Reset() {
	*x = UnicastSubject{}
	mi := &file_fizz_fizz_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnicastSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnicastSubject) ProtoMessage() {}

func (x *UnicastSubject) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnicastSubject.ProtoReflect.Descriptor instead.
func (*UnicastSubject) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{11}
}

func (x *UnicastSubject) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *UnicastSubject) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *UnicastSubject) GetContentId() int64 {
	if x != nil {
		return x.ContentId
	}
	return 0
}

func (x *UnicastSubject) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *UnicastSubject) GetType() MsgStoreType {
	if x != nil {
		return x.Type
	}
	return MsgStoreType_MS_STORE_TYPE_UNKNOWN
}

type MulticastSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId   int64        `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	MaxSeq    int64        `protobuf:"varint,2,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"`
	ContentId int64        `protobuf:"varint,3,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	Uuid      string       `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`                         // 消息唯一标识
	Type      MsgStoreType `protobuf:"varint,5,opt,name=type,proto3,enum=fizz.MsgStoreType" json:"type,omitempty"` // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
	Os        OsType       `protobuf:"varint,6,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"`           // 操作系统类型
}

func (x *MulticastSubject) Reset() {
	*x = MulticastSubject{}
	mi := &file_fizz_fizz_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MulticastSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MulticastSubject) ProtoMessage() {}

func (x *MulticastSubject) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MulticastSubject.ProtoReflect.Descriptor instead.
func (*MulticastSubject) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{12}
}

func (x *MulticastSubject) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MulticastSubject) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *MulticastSubject) GetContentId() int64 {
	if x != nil {
		return x.ContentId
	}
	return 0
}

func (x *MulticastSubject) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MulticastSubject) GetType() MsgStoreType {
	if x != nil {
		return x.Type
	}
	return MsgStoreType_MS_STORE_TYPE_UNKNOWN
}

func (x *MulticastSubject) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

type BroadcastSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxSeq    int64        `protobuf:"varint,1,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"`
	ContentId int64        `protobuf:"varint,2,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
	Uuid      string       `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`                         // 消息唯一标识
	Type      MsgStoreType `protobuf:"varint,4,opt,name=type,proto3,enum=fizz.MsgStoreType" json:"type,omitempty"` // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
	Os        OsType       `protobuf:"varint,5,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"`           // 操作系统类型
}

func (x *BroadcastSubject) Reset() {
	*x = BroadcastSubject{}
	mi := &file_fizz_fizz_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastSubject) ProtoMessage() {}

func (x *BroadcastSubject) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastSubject.ProtoReflect.Descriptor instead.
func (*BroadcastSubject) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{13}
}

func (x *BroadcastSubject) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *BroadcastSubject) GetContentId() int64 {
	if x != nil {
		return x.ContentId
	}
	return 0
}

func (x *BroadcastSubject) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *BroadcastSubject) GetType() MsgStoreType {
	if x != nil {
		return x.Type
	}
	return MsgStoreType_MS_STORE_TYPE_UNKNOWN
}

func (x *BroadcastSubject) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

type MachineProfileSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
}

func (x *MachineProfileSubject) Reset() {
	*x = MachineProfileSubject{}
	mi := &file_fizz_fizz_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MachineProfileSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MachineProfileSubject) ProtoMessage() {}

func (x *MachineProfileSubject) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MachineProfileSubject.ProtoReflect.Descriptor instead.
func (*MachineProfileSubject) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{14}
}

func (x *MachineProfileSubject) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

type ContentProfileSubject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ContentId int64 `protobuf:"varint,1,opt,name=content_id,json=contentId,proto3" json:"content_id,omitempty"`
}

func (x *ContentProfileSubject) Reset() {
	*x = ContentProfileSubject{}
	mi := &file_fizz_fizz_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContentProfileSubject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentProfileSubject) ProtoMessage() {}

func (x *ContentProfileSubject) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentProfileSubject.ProtoReflect.Descriptor instead.
func (*ContentProfileSubject) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{15}
}

func (x *ContentProfileSubject) GetContentId() int64 {
	if x != nil {
		return x.ContentId
	}
	return 0
}

// 设备缓存数据结构（key: machine_id, value: profile）
type MachineProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string                    `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"` // 设备唯一标识
	MaxSeq    int64                     `protobuf:"varint,2,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"`         // 单播消息最大序号
	Contents  []*MachineProfile_Content `protobuf:"bytes,3,rep,name=contents,proto3" json:"contents,omitempty"`                    // 单播消息内容列表（倒序）
}

func (x *MachineProfile) Reset() {
	*x = MachineProfile{}
	mi := &file_fizz_fizz_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MachineProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MachineProfile) ProtoMessage() {}

func (x *MachineProfile) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MachineProfile.ProtoReflect.Descriptor instead.
func (*MachineProfile) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{16}
}

func (x *MachineProfile) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *MachineProfile) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *MachineProfile) GetContents() []*MachineProfile_Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

// 组播消息缓存数据结构（key: group_id, value: profile）
type MulticastProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupId  int64                       `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"` // 分组Id
	MaxSeq   int64                       `protobuf:"varint,2,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"`    // 组播消息最大序号
	Contents []*MulticastProfile_Content `protobuf:"bytes,3,rep,name=contents,proto3" json:"contents,omitempty"`               // 组播消息内容列表（倒序）
}

func (x *MulticastProfile) Reset() {
	*x = MulticastProfile{}
	mi := &file_fizz_fizz_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MulticastProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MulticastProfile) ProtoMessage() {}

func (x *MulticastProfile) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MulticastProfile.ProtoReflect.Descriptor instead.
func (*MulticastProfile) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{17}
}

func (x *MulticastProfile) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MulticastProfile) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *MulticastProfile) GetContents() []*MulticastProfile_Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

// 广播消息缓存数据结构（key: broadcast, value: profile）
type BroadcastProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxSeq   int64                       `protobuf:"varint,1,opt,name=max_seq,json=maxSeq,proto3" json:"max_seq,omitempty"` // 广播消息最大序号
	Contents []*BroadcastProfile_Content `protobuf:"bytes,2,rep,name=contents,proto3" json:"contents,omitempty"`            // 广播消息内容列表（倒序）
}

func (x *BroadcastProfile) Reset() {
	*x = BroadcastProfile{}
	mi := &file_fizz_fizz_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastProfile) ProtoMessage() {}

func (x *BroadcastProfile) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastProfile.ProtoReflect.Descriptor instead.
func (*BroadcastProfile) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{18}
}

func (x *BroadcastProfile) GetMaxSeq() int64 {
	if x != nil {
		return x.MaxSeq
	}
	return 0
}

func (x *BroadcastProfile) GetContents() []*BroadcastProfile_Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

// 消息内容缓存数据结构（key: machine_id, value: profile）
type ContentProfile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Category int32  `protobuf:"varint,1,opt,name=category,proto3" json:"category,omitempty"`              // 消息分类（枚举类型存入DB再取出时转换存在问题）
	MsgType  int32  `protobuf:"varint,2,opt,name=msg_type,json=msgType,proto3" json:"msg_type,omitempty"` // 消息存储类型（枚举类型存入DB再取出时转换存在问题）
	Content  []byte `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`                 // 消息内容
	Size     int64  `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`                      // 消息大小
	Os       OsType `protobuf:"varint,5,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"`         // 消息对应的操作系统类型
}

func (x *ContentProfile) Reset() {
	*x = ContentProfile{}
	mi := &file_fizz_fizz_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContentProfile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContentProfile) ProtoMessage() {}

func (x *ContentProfile) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContentProfile.ProtoReflect.Descriptor instead.
func (*ContentProfile) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{19}
}

func (x *ContentProfile) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *ContentProfile) GetMsgType() int32 {
	if x != nil {
		return x.MsgType
	}
	return 0
}

func (x *ContentProfile) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *ContentProfile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ContentProfile) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

type MachineProfile_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`     // 消息id（消息中心数据库主键）
	Seq  int64  `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`   // 设备对应的序号
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`  // 消息唯一id（接口调用时调用方传的消息唯一标识）
	Type int32  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"` // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
}

func (x *MachineProfile_Content) Reset() {
	*x = MachineProfile_Content{}
	mi := &file_fizz_fizz_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MachineProfile_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MachineProfile_Content) ProtoMessage() {}

func (x *MachineProfile_Content) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MachineProfile_Content.ProtoReflect.Descriptor instead.
func (*MachineProfile_Content) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{16, 0}
}

func (x *MachineProfile_Content) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MachineProfile_Content) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *MachineProfile_Content) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MachineProfile_Content) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

type MulticastProfile_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  // 组播消息id
	Seq  int64  `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`                // 组播消息对应的序号
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`               // 消息唯一id（接口调用时调用方传的消息唯一标识）
	Type int32  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`              // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
	Os   OsType `protobuf:"varint,5,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"` // 操作系统类型
}

func (x *MulticastProfile_Content) Reset() {
	*x = MulticastProfile_Content{}
	mi := &file_fizz_fizz_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MulticastProfile_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MulticastProfile_Content) ProtoMessage() {}

func (x *MulticastProfile_Content) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MulticastProfile_Content.ProtoReflect.Descriptor instead.
func (*MulticastProfile_Content) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{17, 0}
}

func (x *MulticastProfile_Content) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MulticastProfile_Content) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *MulticastProfile_Content) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *MulticastProfile_Content) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *MulticastProfile_Content) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

type BroadcastProfile_Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  // 广播消息id
	Seq  int64  `protobuf:"varint,2,opt,name=seq,proto3" json:"seq,omitempty"`                // 广播消息对应的序号
	Uuid string `protobuf:"bytes,3,opt,name=uuid,proto3" json:"uuid,omitempty"`               // 消息唯一id（接口调用时调用方传的消息唯一标识）
	Type int32  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`              // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
	Os   OsType `protobuf:"varint,5,opt,name=os,proto3,enum=fizz.OsType" json:"os,omitempty"` // 操作系统类型
}

func (x *BroadcastProfile_Content) Reset() {
	*x = BroadcastProfile_Content{}
	mi := &file_fizz_fizz_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastProfile_Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastProfile_Content) ProtoMessage() {}

func (x *BroadcastProfile_Content) ProtoReflect() protoreflect.Message {
	mi := &file_fizz_fizz_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastProfile_Content.ProtoReflect.Descriptor instead.
func (*BroadcastProfile_Content) Descriptor() ([]byte, []int) {
	return file_fizz_fizz_proto_rawDescGZIP(), []int{18, 0}
}

func (x *BroadcastProfile_Content) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BroadcastProfile_Content) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *BroadcastProfile_Content) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *BroadcastProfile_Content) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BroadcastProfile_Content) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

var File_fizz_fizz_proto protoreflect.FileDescriptor

var file_fizz_fizz_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x66, 0x69, 0x7a, 0x7a, 0x2f, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x66, 0x69, 0x7a, 0x7a, 0x1a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x0d,
	0x41, 0x64, 0x64, 0x55, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x41,
	0x64, 0x64, 0x55, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x9b, 0x01,
	0x0a, 0x0f, 0x41, 0x64, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x12, 0x1b, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1c,
	0x0a, 0x02, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66, 0x69, 0x7a,
	0x7a, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x2a, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x6c, 0x6c, 0x6f,
	0x77, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22, 0x12, 0x0a, 0x10, 0x41,
	0x64, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x7e, 0x0a, 0x0f, 0x41, 0x64, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c,
	0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x2a, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x22,
	0x12, 0x0a, 0x10, 0x41, 0x64, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x22, 0xbe, 0x01, 0x0a, 0x0a, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x54, 0x6f, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x61, 0x6e,
	0x64, 0x6f, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x05, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x12, 0x25, 0x0a, 0x06, 0x63, 0x6d, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x05, 0x63, 0x6d, 0x64, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x0d, 0x0a, 0x0b, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x50, 0x0a, 0x08, 0x44, 0x65, 0x62, 0x75, 0x67, 0x52, 0x65, 0x71, 0x12,
	0x1f, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x48, 0x00, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x42, 0x06, 0x0a,
	0x04, 0x63, 0x6f, 0x6e, 0x64, 0x22, 0x0b, 0x0a, 0x09, 0x44, 0x65, 0x62, 0x75, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x81, 0x01, 0x0a, 0x0a, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x31, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d,
	0x73, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0xa3, 0x01, 0x0a, 0x0e, 0x55, 0x6e, 0x69, 0x63, 0x61,
	0x73, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f,
	0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x53, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x6f,
	0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xbf, 0x01, 0x0a,
	0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d,
	0x61, 0x78, 0x53, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73,
	0x67, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66,
	0x69, 0x7a, 0x7a, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x22, 0xa4,
	0x01, 0x0a, 0x10, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x53, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x53, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12,
	0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x73, 0x67, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x02, 0x6f, 0x73, 0x22, 0x36, 0x0a, 0x15, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0x36, 0x0a,
	0x15, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xd7, 0x01, 0x0a, 0x0e, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x73,
	0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x53, 0x65, 0x71,
	0x12, 0x38, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x53, 0x0a, 0x07, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0xf5, 0x01, 0x0a, 0x10, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x50, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x6d, 0x61, 0x78, 0x53, 0x65, 0x71, 0x12, 0x3a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x69, 0x7a,
	0x7a, 0x2e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x73, 0x1a, 0x71, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65,
	0x71, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4f, 0x73, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x22, 0xda, 0x01, 0x0a, 0x10, 0x42, 0x72, 0x6f, 0x61,
	0x64, 0x63, 0x61, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6d, 0x61, 0x78, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6d,
	0x61, 0x78, 0x53, 0x65, 0x71, 0x12, 0x3a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x42,
	0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x2e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x73, 0x1a, 0x71, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x02, 0x6f, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x73, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6d, 0x73, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e,
	0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f, 0x73, 0x2a, 0x45, 0x0a, 0x06, 0x4f, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x01, 0x12, 0x11,
	0x0a, 0x0d, 0x4f, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x55, 0x58, 0x10,
	0x02, 0x2a, 0x68, 0x0a, 0x0e, 0x4d, 0x73, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x53, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x49, 0x43, 0x41, 0x53, 0x54, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x43, 0x41,
	0x53, 0x54, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x42, 0x52, 0x4f, 0x41, 0x44, 0x43, 0x41, 0x53, 0x54, 0x10, 0x03, 0x2a, 0x55, 0x0a, 0x0c, 0x4d,
	0x73, 0x67, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x4d,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x42, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x4d, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4b, 0x56,
	0x10, 0x02, 0x2a, 0xe6, 0x03, 0x0a, 0x0f, 0x4d, 0x73, 0x67, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x27, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54,
	0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x5f, 0x49, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45,
	0x10, 0xe9, 0x07, 0x12, 0x29, 0x0a, 0x24, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x10, 0xd1, 0x0f, 0x12, 0x2b,
	0x0a, 0x26, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x47, 0x4c, 0x4f, 0x42, 0x41,
	0x4c, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x10, 0xd2, 0x0f, 0x12, 0x27, 0x0a, 0x22, 0x4d,
	0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x41,
	0x4e, 0x10, 0xd3, 0x0f, 0x12, 0x2c, 0x0a, 0x27, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x5f, 0x41, 0x42, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10,
	0xd4, 0x0f, 0x12, 0x30, 0x0a, 0x2b, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x55,
	0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52,
	0x44, 0x10, 0xd5, 0x0f, 0x12, 0x28, 0x0a, 0x23, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xd6, 0x0f, 0x12, 0x2e,
	0x0a, 0x29, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x43, 0x4c, 0x4f, 0x55, 0x44,
	0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x10, 0xd7, 0x0f, 0x12, 0x23,
	0x0a, 0x1e, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53,
	0x10, 0xb9, 0x17, 0x12, 0x26, 0x0a, 0x21, 0x4d, 0x53, 0x47, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x42, 0x54, 0x41, 0x49, 0x4e, 0x5f,
	0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x10, 0xba, 0x17, 0x32, 0xa1, 0x02, 0x0a, 0x04,
	0x46, 0x69, 0x7a, 0x7a, 0x12, 0x39, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x55, 0x6e, 0x69, 0x63, 0x61,
	0x73, 0x74, 0x12, 0x13, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x6e, 0x69,
	0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41,
	0x64, 0x64, 0x55, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x3f, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x12,
	0x15, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63,
	0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41, 0x64,
	0x64, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x3f, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74,
	0x12, 0x15, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64,
	0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x41,
	0x64, 0x64, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x30, 0x0a, 0x07, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x12, 0x10, 0x2e, 0x66,
	0x69, 0x7a, 0x7a, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x11,
	0x2e, 0x66, 0x69, 0x7a, 0x7a, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4d, 0x73, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x2a, 0x0a, 0x05, 0x44, 0x65, 0x62, 0x75, 0x67, 0x12, 0x0e, 0x2e, 0x66,
	0x69, 0x7a, 0x7a, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x0f, 0x2e, 0x66,
	0x69, 0x7a, 0x7a, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42,
	0x29, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x66, 0x69, 0x7a, 0x7a, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_fizz_fizz_proto_rawDescOnce sync.Once
	file_fizz_fizz_proto_rawDescData = file_fizz_fizz_proto_rawDesc
)

func file_fizz_fizz_proto_rawDescGZIP() []byte {
	file_fizz_fizz_proto_rawDescOnce.Do(func() {
		file_fizz_fizz_proto_rawDescData = protoimpl.X.CompressGZIP(file_fizz_fizz_proto_rawDescData)
	})
	return file_fizz_fizz_proto_rawDescData
}

var file_fizz_fizz_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_fizz_fizz_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_fizz_fizz_proto_goTypes = []any{
	(OsType)(0),                      // 0: fizz.OsType
	(MsgContentType)(0),              // 1: fizz.MsgContentType
	(MsgStoreType)(0),                // 2: fizz.MsgStoreType
	(MsgCategoryType)(0),             // 3: fizz.MsgCategoryType
	(*AddUnicastReq)(nil),            // 4: fizz.AddUnicastReq
	(*AddUnicastResp)(nil),           // 5: fizz.AddUnicastResp
	(*AddMulticastReq)(nil),          // 6: fizz.AddMulticastReq
	(*AddMulticastResp)(nil),         // 7: fizz.AddMulticastResp
	(*AddBroadcastReq)(nil),          // 8: fizz.AddBroadcastReq
	(*AddBroadcastResp)(nil),         // 9: fizz.AddBroadcastResp
	(*PushMsgReq)(nil),               // 10: fizz.PushMsgReq
	(*PushMsgResp)(nil),              // 11: fizz.PushMsgResp
	(*DebugReq)(nil),                 // 12: fizz.DebugReq
	(*DebugResp)(nil),                // 13: fizz.DebugResp
	(*MsgContent)(nil),               // 14: fizz.MsgContent
	(*UnicastSubject)(nil),           // 15: fizz.UnicastSubject
	(*MulticastSubject)(nil),         // 16: fizz.MulticastSubject
	(*BroadcastSubject)(nil),         // 17: fizz.BroadcastSubject
	(*MachineProfileSubject)(nil),    // 18: fizz.MachineProfileSubject
	(*ContentProfileSubject)(nil),    // 19: fizz.ContentProfileSubject
	(*MachineProfile)(nil),           // 20: fizz.MachineProfile
	(*MulticastProfile)(nil),         // 21: fizz.MulticastProfile
	(*BroadcastProfile)(nil),         // 22: fizz.BroadcastProfile
	(*ContentProfile)(nil),           // 23: fizz.ContentProfile
	(*MachineProfile_Content)(nil),   // 24: fizz.MachineProfile.Content
	(*MulticastProfile_Content)(nil), // 25: fizz.MulticastProfile.Content
	(*BroadcastProfile_Content)(nil), // 26: fizz.BroadcastProfile.Content
	(agent.Command)(0),               // 27: agent.Command
}
var file_fizz_fizz_proto_depIdxs = []int32{
	14, // 0: fizz.AddUnicastReq.content:type_name -> fizz.MsgContent
	0,  // 1: fizz.AddMulticastReq.os:type_name -> fizz.OsType
	14, // 2: fizz.AddMulticastReq.content:type_name -> fizz.MsgContent
	0,  // 3: fizz.AddBroadcastReq.os:type_name -> fizz.OsType
	14, // 4: fizz.AddBroadcastReq.content:type_name -> fizz.MsgContent
	27, // 5: fizz.PushMsgReq.cmd_id:type_name -> agent.Command
	3,  // 6: fizz.MsgContent.category:type_name -> fizz.MsgCategoryType
	2,  // 7: fizz.UnicastSubject.type:type_name -> fizz.MsgStoreType
	2,  // 8: fizz.MulticastSubject.type:type_name -> fizz.MsgStoreType
	0,  // 9: fizz.MulticastSubject.os:type_name -> fizz.OsType
	2,  // 10: fizz.BroadcastSubject.type:type_name -> fizz.MsgStoreType
	0,  // 11: fizz.BroadcastSubject.os:type_name -> fizz.OsType
	24, // 12: fizz.MachineProfile.contents:type_name -> fizz.MachineProfile.Content
	25, // 13: fizz.MulticastProfile.contents:type_name -> fizz.MulticastProfile.Content
	26, // 14: fizz.BroadcastProfile.contents:type_name -> fizz.BroadcastProfile.Content
	0,  // 15: fizz.ContentProfile.os:type_name -> fizz.OsType
	0,  // 16: fizz.MulticastProfile.Content.os:type_name -> fizz.OsType
	0,  // 17: fizz.BroadcastProfile.Content.os:type_name -> fizz.OsType
	4,  // 18: fizz.Fizz.AddUnicast:input_type -> fizz.AddUnicastReq
	6,  // 19: fizz.Fizz.AddMulticast:input_type -> fizz.AddMulticastReq
	8,  // 20: fizz.Fizz.AddBroadcast:input_type -> fizz.AddBroadcastReq
	10, // 21: fizz.Fizz.PushMsg:input_type -> fizz.PushMsgReq
	12, // 22: fizz.Fizz.Debug:input_type -> fizz.DebugReq
	5,  // 23: fizz.Fizz.AddUnicast:output_type -> fizz.AddUnicastResp
	7,  // 24: fizz.Fizz.AddMulticast:output_type -> fizz.AddMulticastResp
	9,  // 25: fizz.Fizz.AddBroadcast:output_type -> fizz.AddBroadcastResp
	11, // 26: fizz.Fizz.PushMsg:output_type -> fizz.PushMsgResp
	13, // 27: fizz.Fizz.Debug:output_type -> fizz.DebugResp
	23, // [23:28] is the sub-list for method output_type
	18, // [18:23] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_fizz_fizz_proto_init() }
func file_fizz_fizz_proto_init() {
	if File_fizz_fizz_proto != nil {
		return
	}
	file_fizz_fizz_proto_msgTypes[8].OneofWrappers = []any{
		(*DebugReq_MachineId)(nil),
		(*DebugReq_GroupId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_fizz_fizz_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_fizz_fizz_proto_goTypes,
		DependencyIndexes: file_fizz_fizz_proto_depIdxs,
		EnumInfos:         file_fizz_fizz_proto_enumTypes,
		MessageInfos:      file_fizz_fizz_proto_msgTypes,
	}.Build()
	File_fizz_fizz_proto = out.File
	file_fizz_fizz_proto_rawDesc = nil
	file_fizz_fizz_proto_goTypes = nil
	file_fizz_fizz_proto_depIdxs = nil
}
