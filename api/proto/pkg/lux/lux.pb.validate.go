// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: lux/lux.proto

package lux

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = agent.Command(0)
)

// Validate checks the field values on PushReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PushReqMultiError, or nil if none found.
func (m *PushReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PushReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for CmdId

	// no validation rules for Data

	if len(errors) > 0 {
		return PushReqMultiError(errors)
	}

	return nil
}

// PushReqMultiError is an error wrapping multiple validation errors returned
// by PushReq.ValidateAll() if the designated constraints aren't met.
type PushReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushReqMultiError) AllErrors() []error { return m }

// PushReqValidationError is the validation error returned by PushReq.Validate
// if the designated constraints aren't met.
type PushReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushReqValidationError) ErrorName() string { return "PushReqValidationError" }

// Error satisfies the builtin error interface
func (e PushReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushReqValidationError{}

// Validate checks the field values on PushResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PushResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PushRespMultiError, or nil
// if none found.
func (m *PushResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PushResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PushRespMultiError(errors)
	}

	return nil
}

// PushRespMultiError is an error wrapping multiple validation errors returned
// by PushResp.ValidateAll() if the designated constraints aren't met.
type PushRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushRespMultiError) AllErrors() []error { return m }

// PushRespValidationError is the validation error returned by
// PushResp.Validate if the designated constraints aren't met.
type PushRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushRespValidationError) ErrorName() string { return "PushRespValidationError" }

// Error satisfies the builtin error interface
func (e PushRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushRespValidationError{}

// Validate checks the field values on ProbeReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProbeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProbeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProbeReqMultiError, or nil
// if none found.
func (m *ProbeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ProbeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if len(errors) > 0 {
		return ProbeReqMultiError(errors)
	}

	return nil
}

// ProbeReqMultiError is an error wrapping multiple validation errors returned
// by ProbeReq.ValidateAll() if the designated constraints aren't met.
type ProbeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProbeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProbeReqMultiError) AllErrors() []error { return m }

// ProbeReqValidationError is the validation error returned by
// ProbeReq.Validate if the designated constraints aren't met.
type ProbeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProbeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProbeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProbeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProbeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProbeReqValidationError) ErrorName() string { return "ProbeReqValidationError" }

// Error satisfies the builtin error interface
func (e ProbeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProbeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProbeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProbeReqValidationError{}

// Validate checks the field values on ProbeResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProbeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProbeResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProbeRespMultiError, or nil
// if none found.
func (m *ProbeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ProbeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Existed

	if len(errors) > 0 {
		return ProbeRespMultiError(errors)
	}

	return nil
}

// ProbeRespMultiError is an error wrapping multiple validation errors returned
// by ProbeResp.ValidateAll() if the designated constraints aren't met.
type ProbeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProbeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProbeRespMultiError) AllErrors() []error { return m }

// ProbeRespValidationError is the validation error returned by
// ProbeResp.Validate if the designated constraints aren't met.
type ProbeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProbeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProbeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProbeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProbeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProbeRespValidationError) ErrorName() string { return "ProbeRespValidationError" }

// Error satisfies the builtin error interface
func (e ProbeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProbeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProbeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProbeRespValidationError{}

// Validate checks the field values on MonitorReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MonitorReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonitorReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MonitorReqMultiError, or
// nil if none found.
func (m *MonitorReq) ValidateAll() error {
	return m.validate(true)
}

func (m *MonitorReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return MonitorReqMultiError(errors)
	}

	return nil
}

// MonitorReqMultiError is an error wrapping multiple validation errors
// returned by MonitorReq.ValidateAll() if the designated constraints aren't met.
type MonitorReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonitorReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonitorReqMultiError) AllErrors() []error { return m }

// MonitorReqValidationError is the validation error returned by
// MonitorReq.Validate if the designated constraints aren't met.
type MonitorReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonitorReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonitorReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonitorReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonitorReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonitorReqValidationError) ErrorName() string { return "MonitorReqValidationError" }

// Error satisfies the builtin error interface
func (e MonitorReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonitorReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonitorReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonitorReqValidationError{}

// Validate checks the field values on MonitorResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MonitorResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonitorResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MonitorRespMultiError, or
// nil if none found.
func (m *MonitorResp) ValidateAll() error {
	return m.validate(true)
}

func (m *MonitorResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConnCount

	if len(errors) > 0 {
		return MonitorRespMultiError(errors)
	}

	return nil
}

// MonitorRespMultiError is an error wrapping multiple validation errors
// returned by MonitorResp.ValidateAll() if the designated constraints aren't met.
type MonitorRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonitorRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonitorRespMultiError) AllErrors() []error { return m }

// MonitorRespValidationError is the validation error returned by
// MonitorResp.Validate if the designated constraints aren't met.
type MonitorRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonitorRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonitorRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonitorRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonitorRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonitorRespValidationError) ErrorName() string { return "MonitorRespValidationError" }

// Error satisfies the builtin error interface
func (e MonitorRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonitorResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonitorRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonitorRespValidationError{}

// Validate checks the field values on LogLevelReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogLevelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogLevelReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogLevelReqMultiError, or
// nil if none found.
func (m *LogLevelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *LogLevelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogLevel

	// no validation rules for ContinueTs

	if len(errors) > 0 {
		return LogLevelReqMultiError(errors)
	}

	return nil
}

// LogLevelReqMultiError is an error wrapping multiple validation errors
// returned by LogLevelReq.ValidateAll() if the designated constraints aren't met.
type LogLevelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogLevelReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogLevelReqMultiError) AllErrors() []error { return m }

// LogLevelReqValidationError is the validation error returned by
// LogLevelReq.Validate if the designated constraints aren't met.
type LogLevelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogLevelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogLevelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogLevelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogLevelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogLevelReqValidationError) ErrorName() string { return "LogLevelReqValidationError" }

// Error satisfies the builtin error interface
func (e LogLevelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogLevelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogLevelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogLevelReqValidationError{}

// Validate checks the field values on LogLevelResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogLevelResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogLevelResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogLevelRespMultiError, or
// nil if none found.
func (m *LogLevelResp) ValidateAll() error {
	return m.validate(true)
}

func (m *LogLevelResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return LogLevelRespMultiError(errors)
	}

	return nil
}

// LogLevelRespMultiError is an error wrapping multiple validation errors
// returned by LogLevelResp.ValidateAll() if the designated constraints aren't met.
type LogLevelRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogLevelRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogLevelRespMultiError) AllErrors() []error { return m }

// LogLevelRespValidationError is the validation error returned by
// LogLevelResp.Validate if the designated constraints aren't met.
type LogLevelRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogLevelRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogLevelRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogLevelRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogLevelRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogLevelRespValidationError) ErrorName() string { return "LogLevelRespValidationError" }

// Error satisfies the builtin error interface
func (e LogLevelRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogLevelResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogLevelRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogLevelRespValidationError{}
