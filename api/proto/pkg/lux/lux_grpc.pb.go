// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: lux/lux.proto

package lux

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Lux_Push_FullMethodName        = "/lux.Lux/Push"
	Lux_Probe_FullMethodName       = "/lux.Lux/Probe"
	Lux_Monitor_FullMethodName     = "/lux.Lux/Monitor"
	Lux_SetLogLevel_FullMethodName = "/lux.Lux/SetLogLevel"
)

// LuxClient is the client API for Lux service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Agent通信服务
type LuxClient interface {
	// 推送
	Push(ctx context.Context, in *PushReq, opts ...grpc.CallOption) (*PushResp, error)
	// 探测
	Probe(ctx context.Context, in *ProbeReq, opts ...grpc.CallOption) (*ProbeResp, error)
	// 监控
	Monitor(ctx context.Context, in *MonitorReq, opts ...grpc.CallOption) (*MonitorResp, error)
	// 调整日志级别
	SetLogLevel(ctx context.Context, in *LogLevelReq, opts ...grpc.CallOption) (*LogLevelResp, error)
}

type luxClient struct {
	cc grpc.ClientConnInterface
}

func NewLuxClient(cc grpc.ClientConnInterface) LuxClient {
	return &luxClient{cc}
}

func (c *luxClient) Push(ctx context.Context, in *PushReq, opts ...grpc.CallOption) (*PushResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushResp)
	err := c.cc.Invoke(ctx, Lux_Push_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luxClient) Probe(ctx context.Context, in *ProbeReq, opts ...grpc.CallOption) (*ProbeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProbeResp)
	err := c.cc.Invoke(ctx, Lux_Probe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luxClient) Monitor(ctx context.Context, in *MonitorReq, opts ...grpc.CallOption) (*MonitorResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MonitorResp)
	err := c.cc.Invoke(ctx, Lux_Monitor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *luxClient) SetLogLevel(ctx context.Context, in *LogLevelReq, opts ...grpc.CallOption) (*LogLevelResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogLevelResp)
	err := c.cc.Invoke(ctx, Lux_SetLogLevel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LuxServer is the server API for Lux service.
// All implementations must embed UnimplementedLuxServer
// for forward compatibility.
//
// Agent通信服务
type LuxServer interface {
	// 推送
	Push(context.Context, *PushReq) (*PushResp, error)
	// 探测
	Probe(context.Context, *ProbeReq) (*ProbeResp, error)
	// 监控
	Monitor(context.Context, *MonitorReq) (*MonitorResp, error)
	// 调整日志级别
	SetLogLevel(context.Context, *LogLevelReq) (*LogLevelResp, error)
	mustEmbedUnimplementedLuxServer()
}

// UnimplementedLuxServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedLuxServer struct{}

func (UnimplementedLuxServer) Push(context.Context, *PushReq) (*PushResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Push not implemented")
}
func (UnimplementedLuxServer) Probe(context.Context, *ProbeReq) (*ProbeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Probe not implemented")
}
func (UnimplementedLuxServer) Monitor(context.Context, *MonitorReq) (*MonitorResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Monitor not implemented")
}
func (UnimplementedLuxServer) SetLogLevel(context.Context, *LogLevelReq) (*LogLevelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetLogLevel not implemented")
}
func (UnimplementedLuxServer) mustEmbedUnimplementedLuxServer() {}
func (UnimplementedLuxServer) testEmbeddedByValue()             {}

// UnsafeLuxServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LuxServer will
// result in compilation errors.
type UnsafeLuxServer interface {
	mustEmbedUnimplementedLuxServer()
}

func RegisterLuxServer(s grpc.ServiceRegistrar, srv LuxServer) {
	// If the following call pancis, it indicates UnimplementedLuxServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Lux_ServiceDesc, srv)
}

func _Lux_Push_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuxServer).Push(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lux_Push_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuxServer).Push(ctx, req.(*PushReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lux_Probe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProbeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuxServer).Probe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lux_Probe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuxServer).Probe(ctx, req.(*ProbeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lux_Monitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MonitorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuxServer).Monitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lux_Monitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuxServer).Monitor(ctx, req.(*MonitorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lux_SetLogLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LuxServer).SetLogLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lux_SetLogLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LuxServer).SetLogLevel(ctx, req.(*LogLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Lux_ServiceDesc is the grpc.ServiceDesc for Lux service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lux_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "lux.Lux",
	HandlerType: (*LuxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Push",
			Handler:    _Lux_Push_Handler,
		},
		{
			MethodName: "Probe",
			Handler:    _Lux_Probe_Handler,
		},
		{
			MethodName: "Monitor",
			Handler:    _Lux_Monitor_Handler,
		},
		{
			MethodName: "SetLogLevel",
			Handler:    _Lux_SetLogLevel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "lux/lux.proto",
}
