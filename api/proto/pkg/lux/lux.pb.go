// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: lux/lux.proto

package lux

import (
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Status int32

const (
	Status_STATUS_UNKNOWN     Status = 0 // 状态未知
	Status_STATUS_SUCCESS     Status = 1 // 成功
	Status_STATUS_FAILED      Status = 2 // 失败（通用错误）
	Status_STATUS_REJECTION   Status = 3 // 拒绝（限流、熔断）
	Status_STATUS_UNSUPPORTED Status = 4 // 不支持
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNKNOWN",
		1: "STATUS_SUCCESS",
		2: "STATUS_FAILED",
		3: "STATUS_REJECTION",
		4: "STATUS_UNSUPPORTED",
	}
	Status_value = map[string]int32{
		"STATUS_UNKNOWN":     0,
		"STATUS_SUCCESS":     1,
		"STATUS_FAILED":      2,
		"STATUS_REJECTION":   3,
		"STATUS_UNSUPPORTED": 4,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_lux_lux_proto_enumTypes[0].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_lux_lux_proto_enumTypes[0]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{0}
}

type PushReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string        `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`         // 设备信息
	CmdId     agent.Command `protobuf:"varint,2,opt,name=cmd_id,json=cmdId,proto3,enum=agent.Command" json:"cmd_id,omitempty"` // 命令字
	Data      []byte        `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                                    // 数据
}

func (x *PushReq) Reset() {
	*x = PushReq{}
	mi := &file_lux_lux_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushReq) ProtoMessage() {}

func (x *PushReq) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushReq.ProtoReflect.Descriptor instead.
func (*PushReq) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{0}
}

func (x *PushReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *PushReq) GetCmdId() agent.Command {
	if x != nil {
		return x.CmdId
	}
	return agent.Command(0)
}

func (x *PushReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PushResp) Reset() {
	*x = PushResp{}
	mi := &file_lux_lux_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushResp) ProtoMessage() {}

func (x *PushResp) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushResp.ProtoReflect.Descriptor instead.
func (*PushResp) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{1}
}

type ProbeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"` // 设备信息
}

func (x *ProbeReq) Reset() {
	*x = ProbeReq{}
	mi := &file_lux_lux_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProbeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProbeReq) ProtoMessage() {}

func (x *ProbeReq) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProbeReq.ProtoReflect.Descriptor instead.
func (*ProbeReq) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{2}
}

func (x *ProbeReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

type ProbeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Existed bool `protobuf:"varint,1,opt,name=existed,proto3" json:"existed,omitempty"` // 连接是否已存在
}

func (x *ProbeResp) Reset() {
	*x = ProbeResp{}
	mi := &file_lux_lux_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProbeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProbeResp) ProtoMessage() {}

func (x *ProbeResp) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProbeResp.ProtoReflect.Descriptor instead.
func (*ProbeResp) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{3}
}

func (x *ProbeResp) GetExisted() bool {
	if x != nil {
		return x.Existed
	}
	return false
}

type MonitorReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *MonitorReq) Reset() {
	*x = MonitorReq{}
	mi := &file_lux_lux_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorReq) ProtoMessage() {}

func (x *MonitorReq) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorReq.ProtoReflect.Descriptor instead.
func (*MonitorReq) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{4}
}

type MonitorResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnCount int32 `protobuf:"varint,1,opt,name=conn_count,json=connCount,proto3" json:"conn_count,omitempty"` // 当前连接数
}

func (x *MonitorResp) Reset() {
	*x = MonitorResp{}
	mi := &file_lux_lux_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorResp) ProtoMessage() {}

func (x *MonitorResp) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorResp.ProtoReflect.Descriptor instead.
func (*MonitorResp) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{5}
}

func (x *MonitorResp) GetConnCount() int32 {
	if x != nil {
		return x.ConnCount
	}
	return 0
}

type LogLevelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogLevel   string `protobuf:"bytes,1,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"`        // 日志级别 1:debug 2:info 3:warn 4:error
	ContinueTs int32  `protobuf:"varint,2,opt,name=continue_ts,json=continueTs,proto3" json:"continue_ts,omitempty"` // 修改日志级别之后持续该日志级别的时间（秒），默认 600秒
}

func (x *LogLevelReq) Reset() {
	*x = LogLevelReq{}
	mi := &file_lux_lux_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogLevelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogLevelReq) ProtoMessage() {}

func (x *LogLevelReq) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogLevelReq.ProtoReflect.Descriptor instead.
func (*LogLevelReq) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{6}
}

func (x *LogLevelReq) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *LogLevelReq) GetContinueTs() int32 {
	if x != nil {
		return x.ContinueTs
	}
	return 0
}

type LogLevelResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *LogLevelResp) Reset() {
	*x = LogLevelResp{}
	mi := &file_lux_lux_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogLevelResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogLevelResp) ProtoMessage() {}

func (x *LogLevelResp) ProtoReflect() protoreflect.Message {
	mi := &file_lux_lux_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogLevelResp.ProtoReflect.Descriptor instead.
func (*LogLevelResp) Descriptor() ([]byte, []int) {
	return file_lux_lux_proto_rawDescGZIP(), []int{7}
}

var File_lux_lux_proto protoreflect.FileDescriptor

var file_lux_lux_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6c, 0x75, 0x78, 0x2f, 0x6c, 0x75, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x6c, 0x75, 0x78, 0x1a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x63, 0x0a, 0x07, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x06, 0x63, 0x6d, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d, 0x6d,
	0x61, 0x6e, 0x64, 0x52, 0x05, 0x63, 0x6d, 0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x0a,
	0x0a, 0x08, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x29, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x22, 0x25, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x22, 0x0c, 0x0a, 0x0a,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x22, 0x2c, 0x0a, 0x0b, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4b, 0x0a, 0x0b, 0x4c, 0x6f, 0x67, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65,
	0x5f, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x74, 0x69,
	0x6e, 0x75, 0x65, 0x54, 0x73, 0x22, 0x0e, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x2a, 0x71, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03,
	0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x55, 0x50,
	0x50, 0x4f, 0x52, 0x54, 0x45, 0x44, 0x10, 0x04, 0x32, 0xbc, 0x01, 0x0a, 0x03, 0x4c, 0x75, 0x78,
	0x12, 0x25, 0x0a, 0x04, 0x50, 0x75, 0x73, 0x68, 0x12, 0x0c, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x50,
	0x75, 0x73, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x0d, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x28, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x12, 0x0d, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x0e, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x2e, 0x0a, 0x07, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x0f, 0x2e, 0x6c,
	0x75, 0x78, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x10, 0x2e,
	0x6c, 0x75, 0x78, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x34, 0x0a, 0x0b, 0x53, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x10, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6c, 0x75, 0x78, 0x2e, 0x4c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x28, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x2e, 0x61,
	0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6c, 0x75,
	0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_lux_lux_proto_rawDescOnce sync.Once
	file_lux_lux_proto_rawDescData = file_lux_lux_proto_rawDesc
)

func file_lux_lux_proto_rawDescGZIP() []byte {
	file_lux_lux_proto_rawDescOnce.Do(func() {
		file_lux_lux_proto_rawDescData = protoimpl.X.CompressGZIP(file_lux_lux_proto_rawDescData)
	})
	return file_lux_lux_proto_rawDescData
}

var file_lux_lux_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_lux_lux_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_lux_lux_proto_goTypes = []any{
	(Status)(0),          // 0: lux.Status
	(*PushReq)(nil),      // 1: lux.PushReq
	(*PushResp)(nil),     // 2: lux.PushResp
	(*ProbeReq)(nil),     // 3: lux.ProbeReq
	(*ProbeResp)(nil),    // 4: lux.ProbeResp
	(*MonitorReq)(nil),   // 5: lux.MonitorReq
	(*MonitorResp)(nil),  // 6: lux.MonitorResp
	(*LogLevelReq)(nil),  // 7: lux.LogLevelReq
	(*LogLevelResp)(nil), // 8: lux.LogLevelResp
	(agent.Command)(0),   // 9: agent.Command
}
var file_lux_lux_proto_depIdxs = []int32{
	9, // 0: lux.PushReq.cmd_id:type_name -> agent.Command
	1, // 1: lux.Lux.Push:input_type -> lux.PushReq
	3, // 2: lux.Lux.Probe:input_type -> lux.ProbeReq
	5, // 3: lux.Lux.Monitor:input_type -> lux.MonitorReq
	7, // 4: lux.Lux.SetLogLevel:input_type -> lux.LogLevelReq
	2, // 5: lux.Lux.Push:output_type -> lux.PushResp
	4, // 6: lux.Lux.Probe:output_type -> lux.ProbeResp
	6, // 7: lux.Lux.Monitor:output_type -> lux.MonitorResp
	8, // 8: lux.Lux.SetLogLevel:output_type -> lux.LogLevelResp
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_lux_lux_proto_init() }
func file_lux_lux_proto_init() {
	if File_lux_lux_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_lux_lux_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_lux_lux_proto_goTypes,
		DependencyIndexes: file_lux_lux_proto_depIdxs,
		EnumInfos:         file_lux_lux_proto_enumTypes,
		MessageInfos:      file_lux_lux_proto_msgTypes,
	}.Build()
	File_lux_lux_proto = out.File
	file_lux_lux_proto_rawDesc = nil
	file_lux_lux_proto_goTypes = nil
	file_lux_lux_proto_depIdxs = nil
}
