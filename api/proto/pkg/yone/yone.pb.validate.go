// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: yone/yone.proto

package yone

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = mq.DetectEngine(0)
)

// Validate checks the field values on FileReceiveReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileReceiveReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileReceiveReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileReceiveReqMultiError,
// or nil if none found.
func (m *FileReceiveReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileReceiveReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileReceiveReqValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileReceiveReqValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileReceiveReqValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FileReceiveReqMultiError(errors)
	}

	return nil
}

// FileReceiveReqMultiError is an error wrapping multiple validation errors
// returned by FileReceiveReq.ValidateAll() if the designated constraints
// aren't met.
type FileReceiveReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileReceiveReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileReceiveReqMultiError) AllErrors() []error { return m }

// FileReceiveReqValidationError is the validation error returned by
// FileReceiveReq.Validate if the designated constraints aren't met.
type FileReceiveReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileReceiveReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileReceiveReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileReceiveReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileReceiveReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileReceiveReqValidationError) ErrorName() string { return "FileReceiveReqValidationError" }

// Error satisfies the builtin error interface
func (e FileReceiveReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileReceiveReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileReceiveReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileReceiveReqValidationError{}

// Validate checks the field values on FileReceiveResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileReceiveResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileReceiveResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileReceiveRespMultiError, or nil if none found.
func (m *FileReceiveResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileReceiveResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessNum

	if len(errors) > 0 {
		return FileReceiveRespMultiError(errors)
	}

	return nil
}

// FileReceiveRespMultiError is an error wrapping multiple validation errors
// returned by FileReceiveResp.ValidateAll() if the designated constraints
// aren't met.
type FileReceiveRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileReceiveRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileReceiveRespMultiError) AllErrors() []error { return m }

// FileReceiveRespValidationError is the validation error returned by
// FileReceiveResp.Validate if the designated constraints aren't met.
type FileReceiveRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileReceiveRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileReceiveRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileReceiveRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileReceiveRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileReceiveRespValidationError) ErrorName() string { return "FileReceiveRespValidationError" }

// Error satisfies the builtin error interface
func (e FileReceiveRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileReceiveResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileReceiveRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileReceiveRespValidationError{}

// Validate checks the field values on VirusReceiveReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VirusReceiveReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusReceiveReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VirusReceiveReqMultiError, or nil if none found.
func (m *VirusReceiveReq) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusReceiveReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	if all {
		switch v := interface{}(m.GetVirusInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirusReceiveReqValidationError{
					field:  "VirusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirusReceiveReqValidationError{
					field:  "VirusInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVirusInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirusReceiveReqValidationError{
				field:  "VirusInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VirusReceiveReqMultiError(errors)
	}

	return nil
}

// VirusReceiveReqMultiError is an error wrapping multiple validation errors
// returned by VirusReceiveReq.ValidateAll() if the designated constraints
// aren't met.
type VirusReceiveReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusReceiveReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusReceiveReqMultiError) AllErrors() []error { return m }

// VirusReceiveReqValidationError is the validation error returned by
// VirusReceiveReq.Validate if the designated constraints aren't met.
type VirusReceiveReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusReceiveReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusReceiveReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusReceiveReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusReceiveReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusReceiveReqValidationError) ErrorName() string { return "VirusReceiveReqValidationError" }

// Error satisfies the builtin error interface
func (e VirusReceiveReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusReceiveReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusReceiveReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusReceiveReqValidationError{}

// Validate checks the field values on VirusReceiveResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VirusReceiveResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusReceiveResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VirusReceiveRespMultiError, or nil if none found.
func (m *VirusReceiveResp) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusReceiveResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessNum

	if len(errors) > 0 {
		return VirusReceiveRespMultiError(errors)
	}

	return nil
}

// VirusReceiveRespMultiError is an error wrapping multiple validation errors
// returned by VirusReceiveResp.ValidateAll() if the designated constraints
// aren't met.
type VirusReceiveRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusReceiveRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusReceiveRespMultiError) AllErrors() []error { return m }

// VirusReceiveRespValidationError is the validation error returned by
// VirusReceiveResp.Validate if the designated constraints aren't met.
type VirusReceiveRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusReceiveRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusReceiveRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusReceiveRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusReceiveRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusReceiveRespValidationError) ErrorName() string { return "VirusReceiveRespValidationError" }

// Error satisfies the builtin error interface
func (e VirusReceiveRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusReceiveResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusReceiveRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusReceiveRespValidationError{}

// Validate checks the field values on OutreachReceiveReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachReceiveReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachReceiveReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachReceiveReqMultiError, or nil if none found.
func (m *OutreachReceiveReq) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachReceiveReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOutreaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutreachReceiveReqValidationError{
						field:  fmt.Sprintf("Outreaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutreachReceiveReqValidationError{
						field:  fmt.Sprintf("Outreaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutreachReceiveReqValidationError{
					field:  fmt.Sprintf("Outreaches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutreachReceiveReqMultiError(errors)
	}

	return nil
}

// OutreachReceiveReqMultiError is an error wrapping multiple validation errors
// returned by OutreachReceiveReq.ValidateAll() if the designated constraints
// aren't met.
type OutreachReceiveReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachReceiveReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachReceiveReqMultiError) AllErrors() []error { return m }

// OutreachReceiveReqValidationError is the validation error returned by
// OutreachReceiveReq.Validate if the designated constraints aren't met.
type OutreachReceiveReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachReceiveReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachReceiveReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachReceiveReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachReceiveReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachReceiveReqValidationError) ErrorName() string {
	return "OutreachReceiveReqValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachReceiveReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachReceiveReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachReceiveReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachReceiveReqValidationError{}

// Validate checks the field values on OutreachReceiveResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachReceiveResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachReceiveResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachReceiveRespMultiError, or nil if none found.
func (m *OutreachReceiveResp) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachReceiveResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SuccessNum

	if len(errors) > 0 {
		return OutreachReceiveRespMultiError(errors)
	}

	return nil
}

// OutreachReceiveRespMultiError is an error wrapping multiple validation
// errors returned by OutreachReceiveResp.ValidateAll() if the designated
// constraints aren't met.
type OutreachReceiveRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachReceiveRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachReceiveRespMultiError) AllErrors() []error { return m }

// OutreachReceiveRespValidationError is the validation error returned by
// OutreachReceiveResp.Validate if the designated constraints aren't met.
type OutreachReceiveRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachReceiveRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachReceiveRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachReceiveRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachReceiveRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachReceiveRespValidationError) ErrorName() string {
	return "OutreachReceiveRespValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachReceiveRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachReceiveResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachReceiveRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachReceiveRespValidationError{}

// Validate checks the field values on ProcChainsNodesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcChainsNodesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainsNodesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcChainsNodesReqMultiError, or nil if none found.
func (m *ProcChainsNodesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainsNodesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OnlyLeaf

	if len(errors) > 0 {
		return ProcChainsNodesReqMultiError(errors)
	}

	return nil
}

// ProcChainsNodesReqMultiError is an error wrapping multiple validation errors
// returned by ProcChainsNodesReq.ValidateAll() if the designated constraints
// aren't met.
type ProcChainsNodesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainsNodesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainsNodesReqMultiError) AllErrors() []error { return m }

// ProcChainsNodesReqValidationError is the validation error returned by
// ProcChainsNodesReq.Validate if the designated constraints aren't met.
type ProcChainsNodesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainsNodesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainsNodesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainsNodesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainsNodesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainsNodesReqValidationError) ErrorName() string {
	return "ProcChainsNodesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ProcChainsNodesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainsNodesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainsNodesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainsNodesReqValidationError{}

// Validate checks the field values on ProcChainsNodesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcChainsNodesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainsNodesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcChainsNodesRespMultiError, or nil if none found.
func (m *ProcChainsNodesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainsNodesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProcNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcChainsNodesRespValidationError{
						field:  fmt.Sprintf("ProcNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcChainsNodesRespValidationError{
						field:  fmt.Sprintf("ProcNodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcChainsNodesRespValidationError{
					field:  fmt.Sprintf("ProcNodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcChainsNodesRespValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcChainsNodesRespValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcChainsNodesRespValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcChainsNodesRespMultiError(errors)
	}

	return nil
}

// ProcChainsNodesRespMultiError is an error wrapping multiple validation
// errors returned by ProcChainsNodesResp.ValidateAll() if the designated
// constraints aren't met.
type ProcChainsNodesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainsNodesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainsNodesRespMultiError) AllErrors() []error { return m }

// ProcChainsNodesRespValidationError is the validation error returned by
// ProcChainsNodesResp.Validate if the designated constraints aren't met.
type ProcChainsNodesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainsNodesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainsNodesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainsNodesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainsNodesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainsNodesRespValidationError) ErrorName() string {
	return "ProcChainsNodesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ProcChainsNodesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainsNodesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainsNodesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainsNodesRespValidationError{}

// Validate checks the field values on ProcChainsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcChainsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcChainsReqMultiError, or
// nil if none found.
func (m *ProcChainsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBeans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcChainsReqValidationError{
						field:  fmt.Sprintf("Beans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcChainsReqValidationError{
						field:  fmt.Sprintf("Beans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcChainsReqValidationError{
					field:  fmt.Sprintf("Beans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcChainsReqMultiError(errors)
	}

	return nil
}

// ProcChainsReqMultiError is an error wrapping multiple validation errors
// returned by ProcChainsReq.ValidateAll() if the designated constraints
// aren't met.
type ProcChainsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainsReqMultiError) AllErrors() []error { return m }

// ProcChainsReqValidationError is the validation error returned by
// ProcChainsReq.Validate if the designated constraints aren't met.
type ProcChainsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainsReqValidationError) ErrorName() string { return "ProcChainsReqValidationError" }

// Error satisfies the builtin error interface
func (e ProcChainsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainsReqValidationError{}

// Validate checks the field values on ProcChainsResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcChainsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcChainsRespMultiError,
// or nil if none found.
func (m *ProcChainsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChains() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcChainsRespValidationError{
						field:  fmt.Sprintf("Chains[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcChainsRespValidationError{
						field:  fmt.Sprintf("Chains[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcChainsRespValidationError{
					field:  fmt.Sprintf("Chains[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcChainsRespMultiError(errors)
	}

	return nil
}

// ProcChainsRespMultiError is an error wrapping multiple validation errors
// returned by ProcChainsResp.ValidateAll() if the designated constraints
// aren't met.
type ProcChainsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainsRespMultiError) AllErrors() []error { return m }

// ProcChainsRespValidationError is the validation error returned by
// ProcChainsResp.Validate if the designated constraints aren't met.
type ProcChainsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainsRespValidationError) ErrorName() string { return "ProcChainsRespValidationError" }

// Error satisfies the builtin error interface
func (e ProcChainsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainsRespValidationError{}

// Validate checks the field values on ProcChain with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcChain) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChain with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcChainMultiError, or nil
// if none found.
func (m *ProcChain) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChain) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Unique

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcChainValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcChainValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcChainValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcChainMultiError(errors)
	}

	return nil
}

// ProcChainMultiError is an error wrapping multiple validation errors returned
// by ProcChain.ValidateAll() if the designated constraints aren't met.
type ProcChainMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainMultiError) AllErrors() []error { return m }

// ProcChainValidationError is the validation error returned by
// ProcChain.Validate if the designated constraints aren't met.
type ProcChainValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainValidationError) ErrorName() string { return "ProcChainValidationError" }

// Error satisfies the builtin error interface
func (e ProcChainValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChain.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainValidationError{}

// Validate checks the field values on ProcNodeBean with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcNodeBean) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcNodeBean with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcNodeBeanMultiError, or
// nil if none found.
func (m *ProcNodeBean) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcNodeBean) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for Pid

	// no validation rules for StartTime

	if len(errors) > 0 {
		return ProcNodeBeanMultiError(errors)
	}

	return nil
}

// ProcNodeBeanMultiError is an error wrapping multiple validation errors
// returned by ProcNodeBean.ValidateAll() if the designated constraints aren't met.
type ProcNodeBeanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcNodeBeanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcNodeBeanMultiError) AllErrors() []error { return m }

// ProcNodeBeanValidationError is the validation error returned by
// ProcNodeBean.Validate if the designated constraints aren't met.
type ProcNodeBeanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcNodeBeanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcNodeBeanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcNodeBeanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcNodeBeanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcNodeBeanValidationError) ErrorName() string { return "ProcNodeBeanValidationError" }

// Error satisfies the builtin error interface
func (e ProcNodeBeanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcNodeBean.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcNodeBeanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcNodeBeanValidationError{}

// Validate checks the field values on ProcChainNode with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcChainNode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainNode with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcChainNodeMultiError, or
// nil if none found.
func (m *ProcChainNode) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainNode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Unique

	// no validation rules for Pid

	// no validation rules for StartTime

	// no validation rules for Ppid

	// no validation rules for ParentStartTime

	// no validation rules for Epid

	// no validation rules for ExecStartTime

	// no validation rules for Name

	// no validation rules for Command

	// no validation rules for Username

	// no validation rules for Euid

	// no validation rules for FileUnique

	if len(errors) > 0 {
		return ProcChainNodeMultiError(errors)
	}

	return nil
}

// ProcChainNodeMultiError is an error wrapping multiple validation errors
// returned by ProcChainNode.ValidateAll() if the designated constraints
// aren't met.
type ProcChainNodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainNodeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainNodeMultiError) AllErrors() []error { return m }

// ProcChainNodeValidationError is the validation error returned by
// ProcChainNode.Validate if the designated constraints aren't met.
type ProcChainNodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainNodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainNodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainNodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainNodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainNodeValidationError) ErrorName() string { return "ProcChainNodeValidationError" }

// Error satisfies the builtin error interface
func (e ProcChainNodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainNode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainNodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainNodeValidationError{}

// Validate checks the field values on ProcChainNodeWithFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcChainNodeWithFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainNodeWithFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcChainNodeWithFileMultiError, or nil if none found.
func (m *ProcChainNodeWithFile) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainNodeWithFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Unique

	// no validation rules for Pid

	// no validation rules for StartTime

	// no validation rules for Ppid

	// no validation rules for ParentStartTime

	// no validation rules for Epid

	// no validation rules for ExecStartTime

	// no validation rules for Name

	// no validation rules for Command

	// no validation rules for Username

	// no validation rules for Euid

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcChainNodeWithFileValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcChainNodeWithFileValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcChainNodeWithFileValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcChainNodeWithFileMultiError(errors)
	}

	return nil
}

// ProcChainNodeWithFileMultiError is an error wrapping multiple validation
// errors returned by ProcChainNodeWithFile.ValidateAll() if the designated
// constraints aren't met.
type ProcChainNodeWithFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainNodeWithFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainNodeWithFileMultiError) AllErrors() []error { return m }

// ProcChainNodeWithFileValidationError is the validation error returned by
// ProcChainNodeWithFile.Validate if the designated constraints aren't met.
type ProcChainNodeWithFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainNodeWithFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainNodeWithFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainNodeWithFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainNodeWithFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainNodeWithFileValidationError) ErrorName() string {
	return "ProcChainNodeWithFileValidationError"
}

// Error satisfies the builtin error interface
func (e ProcChainNodeWithFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainNodeWithFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainNodeWithFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainNodeWithFileValidationError{}

// Validate checks the field values on ProcChainFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcChainFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcChainFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcChainFileMultiError, or
// nil if none found.
func (m *ProcChainFile) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcChainFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Unique

	// no validation rules for Name

	// no validation rules for Path

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Size

	// no validation rules for Mtime

	// no validation rules for Ctime

	// no validation rules for Atime

	// no validation rules for Permission

	// no validation rules for CompanyName

	if len(errors) > 0 {
		return ProcChainFileMultiError(errors)
	}

	return nil
}

// ProcChainFileMultiError is an error wrapping multiple validation errors
// returned by ProcChainFile.ValidateAll() if the designated constraints
// aren't met.
type ProcChainFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcChainFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcChainFileMultiError) AllErrors() []error { return m }

// ProcChainFileValidationError is the validation error returned by
// ProcChainFile.Validate if the designated constraints aren't met.
type ProcChainFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcChainFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcChainFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcChainFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcChainFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcChainFileValidationError) ErrorName() string { return "ProcChainFileValidationError" }

// Error satisfies the builtin error interface
func (e ProcChainFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcChainFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcChainFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcChainFileValidationError{}

// Validate checks the field values on OutreachSearchReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachSearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachSearchReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachSearchReqMultiError, or nil if none found.
func (m *OutreachSearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachSearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SrcIp

	// no validation rules for SrcPort

	// no validation rules for DestIp

	// no validation rules for DestPort

	// no validation rules for Protocol

	// no validation rules for BeginTime

	// no validation rules for EndTime

	// no validation rules for Limit

	if len(errors) > 0 {
		return OutreachSearchReqMultiError(errors)
	}

	return nil
}

// OutreachSearchReqMultiError is an error wrapping multiple validation errors
// returned by OutreachSearchReq.ValidateAll() if the designated constraints
// aren't met.
type OutreachSearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachSearchReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachSearchReqMultiError) AllErrors() []error { return m }

// OutreachSearchReqValidationError is the validation error returned by
// OutreachSearchReq.Validate if the designated constraints aren't met.
type OutreachSearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachSearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachSearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachSearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachSearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachSearchReqValidationError) ErrorName() string {
	return "OutreachSearchReqValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachSearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachSearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachSearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachSearchReqValidationError{}

// Validate checks the field values on OutreachSearchResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachSearchResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachSearchResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachSearchRespMultiError, or nil if none found.
func (m *OutreachSearchResp) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachSearchResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOutreaches() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutreachSearchRespValidationError{
						field:  fmt.Sprintf("Outreaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutreachSearchRespValidationError{
						field:  fmt.Sprintf("Outreaches[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutreachSearchRespValidationError{
					field:  fmt.Sprintf("Outreaches[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutreachSearchRespMultiError(errors)
	}

	return nil
}

// OutreachSearchRespMultiError is an error wrapping multiple validation errors
// returned by OutreachSearchResp.ValidateAll() if the designated constraints
// aren't met.
type OutreachSearchRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachSearchRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachSearchRespMultiError) AllErrors() []error { return m }

// OutreachSearchRespValidationError is the validation error returned by
// OutreachSearchResp.Validate if the designated constraints aren't met.
type OutreachSearchRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachSearchRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachSearchRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachSearchRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachSearchRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachSearchRespValidationError) ErrorName() string {
	return "OutreachSearchRespValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachSearchRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachSearchResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachSearchRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachSearchRespValidationError{}

// Validate checks the field values on CreateFileTaskReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateFileTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileTaskReqMultiError, or nil if none found.
func (m *CreateFileTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateFileTaskReqMultiError(errors)
	}

	return nil
}

// CreateFileTaskReqMultiError is an error wrapping multiple validation errors
// returned by CreateFileTaskReq.ValidateAll() if the designated constraints
// aren't met.
type CreateFileTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileTaskReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileTaskReqMultiError) AllErrors() []error { return m }

// CreateFileTaskReqValidationError is the validation error returned by
// CreateFileTaskReq.Validate if the designated constraints aren't met.
type CreateFileTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileTaskReqValidationError) ErrorName() string {
	return "CreateFileTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFileTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileTaskReqValidationError{}

// Validate checks the field values on CreateFileTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateFileTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateFileTaskResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateFileTaskRespMultiError, or nil if none found.
func (m *CreateFileTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateFileTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateFileTaskRespMultiError(errors)
	}

	return nil
}

// CreateFileTaskRespMultiError is an error wrapping multiple validation errors
// returned by CreateFileTaskResp.ValidateAll() if the designated constraints
// aren't met.
type CreateFileTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateFileTaskRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateFileTaskRespMultiError) AllErrors() []error { return m }

// CreateFileTaskRespValidationError is the validation error returned by
// CreateFileTaskResp.Validate if the designated constraints aren't met.
type CreateFileTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateFileTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateFileTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateFileTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateFileTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateFileTaskRespValidationError) ErrorName() string {
	return "CreateFileTaskRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateFileTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateFileTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateFileTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateFileTaskRespValidationError{}

// Validate checks the field values on GetFileTasksStateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileTasksStateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileTasksStateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileTasksStateReqMultiError, or nil if none found.
func (m *GetFileTasksStateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileTasksStateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetFileTasksStateReqMultiError(errors)
	}

	return nil
}

// GetFileTasksStateReqMultiError is an error wrapping multiple validation
// errors returned by GetFileTasksStateReq.ValidateAll() if the designated
// constraints aren't met.
type GetFileTasksStateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileTasksStateReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileTasksStateReqMultiError) AllErrors() []error { return m }

// GetFileTasksStateReqValidationError is the validation error returned by
// GetFileTasksStateReq.Validate if the designated constraints aren't met.
type GetFileTasksStateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileTasksStateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileTasksStateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileTasksStateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileTasksStateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileTasksStateReqValidationError) ErrorName() string {
	return "GetFileTasksStateReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileTasksStateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileTasksStateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileTasksStateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileTasksStateReqValidationError{}

// Validate checks the field values on GetFileTasksStateResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileTasksStateResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileTasksStateResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileTasksStateRespMultiError, or nil if none found.
func (m *GetFileTasksStateResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileTasksStateResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFileTasksStateRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFileTasksStateRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFileTasksStateRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFileTasksStateRespMultiError(errors)
	}

	return nil
}

// GetFileTasksStateRespMultiError is an error wrapping multiple validation
// errors returned by GetFileTasksStateResp.ValidateAll() if the designated
// constraints aren't met.
type GetFileTasksStateRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileTasksStateRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileTasksStateRespMultiError) AllErrors() []error { return m }

// GetFileTasksStateRespValidationError is the validation error returned by
// GetFileTasksStateResp.Validate if the designated constraints aren't met.
type GetFileTasksStateRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileTasksStateRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileTasksStateRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileTasksStateRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileTasksStateRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileTasksStateRespValidationError) ErrorName() string {
	return "GetFileTasksStateRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileTasksStateRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileTasksStateResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileTasksStateRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileTasksStateRespValidationError{}

// Validate checks the field values on FileTaskState with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileTaskState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileTaskState with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileTaskStateMultiError, or
// nil if none found.
func (m *FileTaskState) ValidateAll() error {
	return m.validate(true)
}

func (m *FileTaskState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Status

	// no validation rules for Result

	if len(errors) > 0 {
		return FileTaskStateMultiError(errors)
	}

	return nil
}

// FileTaskStateMultiError is an error wrapping multiple validation errors
// returned by FileTaskState.ValidateAll() if the designated constraints
// aren't met.
type FileTaskStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileTaskStateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileTaskStateMultiError) AllErrors() []error { return m }

// FileTaskStateValidationError is the validation error returned by
// FileTaskState.Validate if the designated constraints aren't met.
type FileTaskStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileTaskStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileTaskStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileTaskStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileTaskStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileTaskStateValidationError) ErrorName() string { return "FileTaskStateValidationError" }

// Error satisfies the builtin error interface
func (e FileTaskStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileTaskState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileTaskStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileTaskStateValidationError{}

// Validate checks the field values on OutreachInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutreachInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutreachInfoMultiError, or
// nil if none found.
func (m *OutreachInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SrcIp

	// no validation rules for SrcPort

	// no validation rules for DestIp

	// no validation rules for DestPort

	// no validation rules for Protocol

	// no validation rules for Pid

	// no validation rules for StartTime

	// no validation rules for FoundTime

	// no validation rules for CreatedAt

	// no validation rules for HostId

	// no validation rules for Hostname

	// no validation rules for HostIp

	// no validation rules for HostOs

	// no validation rules for HostGroup

	if all {
		switch v := interface{}(m.GetChain()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachInfoValidationError{
					field:  "Chain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachInfoValidationError{
					field:  "Chain",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChain()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachInfoValidationError{
				field:  "Chain",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachInfoMultiError(errors)
	}

	return nil
}

// OutreachInfoMultiError is an error wrapping multiple validation errors
// returned by OutreachInfo.ValidateAll() if the designated constraints aren't met.
type OutreachInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachInfoMultiError) AllErrors() []error { return m }

// OutreachInfoValidationError is the validation error returned by
// OutreachInfo.Validate if the designated constraints aren't met.
type OutreachInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachInfoValidationError) ErrorName() string { return "OutreachInfoValidationError" }

// Error satisfies the builtin error interface
func (e OutreachInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachInfoValidationError{}

// Validate checks the field values on ArchiveListReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveListReqMultiError,
// or nil if none found.
func (m *ArchiveListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveListReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveListReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Orderby

	if len(errors) > 0 {
		return ArchiveListReqMultiError(errors)
	}

	return nil
}

// ArchiveListReqMultiError is an error wrapping multiple validation errors
// returned by ArchiveListReq.ValidateAll() if the designated constraints
// aren't met.
type ArchiveListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveListReqMultiError) AllErrors() []error { return m }

// ArchiveListReqValidationError is the validation error returned by
// ArchiveListReq.Validate if the designated constraints aren't met.
type ArchiveListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveListReqValidationError) ErrorName() string { return "ArchiveListReqValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveListReqValidationError{}

// Validate checks the field values on ArchiveListFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ArchiveListFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveListFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArchiveListFilterMultiError, or nil if none found.
func (m *ArchiveListFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveListFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchData

	// no validation rules for DetectResult

	// no validation rules for TerminalStart

	// no validation rules for TerminalEnd

	if len(errors) > 0 {
		return ArchiveListFilterMultiError(errors)
	}

	return nil
}

// ArchiveListFilterMultiError is an error wrapping multiple validation errors
// returned by ArchiveListFilter.ValidateAll() if the designated constraints
// aren't met.
type ArchiveListFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveListFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveListFilterMultiError) AllErrors() []error { return m }

// ArchiveListFilterValidationError is the validation error returned by
// ArchiveListFilter.Validate if the designated constraints aren't met.
type ArchiveListFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveListFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveListFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveListFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveListFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveListFilterValidationError) ErrorName() string {
	return "ArchiveListFilterValidationError"
}

// Error satisfies the builtin error interface
func (e ArchiveListFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveListFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveListFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveListFilterValidationError{}

// Validate checks the field values on Page with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Page) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Page with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PageMultiError, or nil if none found.
func (m *Page) ValidateAll() error {
	return m.validate(true)
}

func (m *Page) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageIndex

	// no validation rules for PageSize

	if len(errors) > 0 {
		return PageMultiError(errors)
	}

	return nil
}

// PageMultiError is an error wrapping multiple validation errors returned by
// Page.ValidateAll() if the designated constraints aren't met.
type PageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageMultiError) AllErrors() []error { return m }

// PageValidationError is the validation error returned by Page.Validate if the
// designated constraints aren't met.
type PageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageValidationError) ErrorName() string { return "PageValidationError" }

// Error satisfies the builtin error interface
func (e PageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageValidationError{}

// Validate checks the field values on ArchiveListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ArchiveListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArchiveListRespMultiError, or nil if none found.
func (m *ArchiveListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ArchiveListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ArchiveListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ArchiveListRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return ArchiveListRespMultiError(errors)
	}

	return nil
}

// ArchiveListRespMultiError is an error wrapping multiple validation errors
// returned by ArchiveListResp.ValidateAll() if the designated constraints
// aren't met.
type ArchiveListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveListRespMultiError) AllErrors() []error { return m }

// ArchiveListRespValidationError is the validation error returned by
// ArchiveListResp.Validate if the designated constraints aren't met.
type ArchiveListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveListRespValidationError) ErrorName() string { return "ArchiveListRespValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveListRespValidationError{}

// Validate checks the field values on ArchiveItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveItemMultiError, or
// nil if none found.
func (m *ArchiveItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for FileName

	// no validation rules for FileSize

	// no validation rules for FileCreatedAt

	// no validation rules for FileFoundAt

	// no validation rules for DetectResult

	// no validation rules for TerminalCount

	if len(errors) > 0 {
		return ArchiveItemMultiError(errors)
	}

	return nil
}

// ArchiveItemMultiError is an error wrapping multiple validation errors
// returned by ArchiveItem.ValidateAll() if the designated constraints aren't met.
type ArchiveItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveItemMultiError) AllErrors() []error { return m }

// ArchiveItemValidationError is the validation error returned by
// ArchiveItem.Validate if the designated constraints aren't met.
type ArchiveItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveItemValidationError) ErrorName() string { return "ArchiveItemValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveItemValidationError{}

// Validate checks the field values on ArchiveStatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveStatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveStatReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveStatReqMultiError,
// or nil if none found.
func (m *ArchiveStatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveStatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveStatReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveStatReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveStatReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ArchiveStatReqMultiError(errors)
	}

	return nil
}

// ArchiveStatReqMultiError is an error wrapping multiple validation errors
// returned by ArchiveStatReq.ValidateAll() if the designated constraints
// aren't met.
type ArchiveStatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveStatReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveStatReqMultiError) AllErrors() []error { return m }

// ArchiveStatReqValidationError is the validation error returned by
// ArchiveStatReq.Validate if the designated constraints aren't met.
type ArchiveStatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveStatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveStatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveStatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveStatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveStatReqValidationError) ErrorName() string { return "ArchiveStatReqValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveStatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveStatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveStatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveStatReqValidationError{}

// Validate checks the field values on ArchiveStat with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveStat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveStat with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveStatMultiError, or
// nil if none found.
func (m *ArchiveStat) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveStat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalCount

	// no validation rules for DailyCount

	// no validation rules for ServerCount

	if len(errors) > 0 {
		return ArchiveStatMultiError(errors)
	}

	return nil
}

// ArchiveStatMultiError is an error wrapping multiple validation errors
// returned by ArchiveStat.ValidateAll() if the designated constraints aren't met.
type ArchiveStatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveStatMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveStatMultiError) AllErrors() []error { return m }

// ArchiveStatValidationError is the validation error returned by
// ArchiveStat.Validate if the designated constraints aren't met.
type ArchiveStatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveStatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveStatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveStatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveStatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveStatValidationError) ErrorName() string { return "ArchiveStatValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveStatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveStat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveStatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveStatValidationError{}

// Validate checks the field values on ArchiveStatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ArchiveStatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveStatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArchiveStatRespMultiError, or nil if none found.
func (m *ArchiveStatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveStatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBlack()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Black",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Black",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBlack()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveStatRespValidationError{
				field:  "Black",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWhite()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "White",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "White",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWhite()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveStatRespValidationError{
				field:  "White",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGray()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Gray",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Gray",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGray()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveStatRespValidationError{
				field:  "Gray",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnknown()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Unknown",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArchiveStatRespValidationError{
					field:  "Unknown",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnknown()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArchiveStatRespValidationError{
				field:  "Unknown",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ArchiveStatRespMultiError(errors)
	}

	return nil
}

// ArchiveStatRespMultiError is an error wrapping multiple validation errors
// returned by ArchiveStatResp.ValidateAll() if the designated constraints
// aren't met.
type ArchiveStatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveStatRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveStatRespMultiError) AllErrors() []error { return m }

// ArchiveStatRespValidationError is the validation error returned by
// ArchiveStatResp.Validate if the designated constraints aren't met.
type ArchiveStatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveStatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveStatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveStatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveStatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveStatRespValidationError) ErrorName() string { return "ArchiveStatRespValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveStatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveStatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveStatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveStatRespValidationError{}

// Validate checks the field values on ArchiveInfoReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveInfoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveInfoReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveInfoReqMultiError,
// or nil if none found.
func (m *ArchiveInfoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveInfoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	if len(errors) > 0 {
		return ArchiveInfoReqMultiError(errors)
	}

	return nil
}

// ArchiveInfoReqMultiError is an error wrapping multiple validation errors
// returned by ArchiveInfoReq.ValidateAll() if the designated constraints
// aren't met.
type ArchiveInfoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveInfoReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveInfoReqMultiError) AllErrors() []error { return m }

// ArchiveInfoReqValidationError is the validation error returned by
// ArchiveInfoReq.Validate if the designated constraints aren't met.
type ArchiveInfoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveInfoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveInfoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveInfoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveInfoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveInfoReqValidationError) ErrorName() string { return "ArchiveInfoReqValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveInfoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveInfoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveInfoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveInfoReqValidationError{}

// Validate checks the field values on ArchiveInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ArchiveInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArchiveInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ArchiveInfoMultiError, or
// nil if none found.
func (m *ArchiveInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ArchiveInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for FileName

	// no validation rules for FileType

	// no validation rules for FileSize

	// no validation rules for FileVendor

	// no validation rules for FileVersion

	// no validation rules for EventAt

	// no validation rules for DetectResult

	// no validation rules for TerminalCount

	for idx, item := range m.GetSignInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ArchiveInfoValidationError{
						field:  fmt.Sprintf("SignInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ArchiveInfoValidationError{
						field:  fmt.Sprintf("SignInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ArchiveInfoValidationError{
					field:  fmt.Sprintf("SignInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ArchiveInfoMultiError(errors)
	}

	return nil
}

// ArchiveInfoMultiError is an error wrapping multiple validation errors
// returned by ArchiveInfo.ValidateAll() if the designated constraints aren't met.
type ArchiveInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArchiveInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArchiveInfoMultiError) AllErrors() []error { return m }

// ArchiveInfoValidationError is the validation error returned by
// ArchiveInfo.Validate if the designated constraints aren't met.
type ArchiveInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArchiveInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArchiveInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArchiveInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArchiveInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArchiveInfoValidationError) ErrorName() string { return "ArchiveInfoValidationError" }

// Error satisfies the builtin error interface
func (e ArchiveInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArchiveInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArchiveInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArchiveInfoValidationError{}

// Validate checks the field values on SignInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SignInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SignInfoMultiError, or nil
// if none found.
func (m *SignInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SignInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Serial

	// no validation rules for IssuerName

	// no validation rules for Customer

	// no validation rules for Thumbprint

	// no validation rules for Result

	// no validation rules for Description

	// no validation rules for NotAfter

	// no validation rules for NotBefore

	// no validation rules for SignAlgorithm

	// no validation rules for SignHashAlgorithm

	// no validation rules for Version

	// no validation rules for SignStatusInfo

	if len(errors) > 0 {
		return SignInfoMultiError(errors)
	}

	return nil
}

// SignInfoMultiError is an error wrapping multiple validation errors returned
// by SignInfo.ValidateAll() if the designated constraints aren't met.
type SignInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignInfoMultiError) AllErrors() []error { return m }

// SignInfoValidationError is the validation error returned by
// SignInfo.Validate if the designated constraints aren't met.
type SignInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignInfoValidationError) ErrorName() string { return "SignInfoValidationError" }

// Error satisfies the builtin error interface
func (e SignInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignInfoValidationError{}

// Validate checks the field values on FileHostListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileHostListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHostListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileHostListReqMultiError, or nil if none found.
func (m *FileHostListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHostListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileHostListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileHostListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileHostListReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileHostListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileHostListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileHostListReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileHostListReqMultiError(errors)
	}

	return nil
}

// FileHostListReqMultiError is an error wrapping multiple validation errors
// returned by FileHostListReq.ValidateAll() if the designated constraints
// aren't met.
type FileHostListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHostListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHostListReqMultiError) AllErrors() []error { return m }

// FileHostListReqValidationError is the validation error returned by
// FileHostListReq.Validate if the designated constraints aren't met.
type FileHostListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHostListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHostListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHostListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHostListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHostListReqValidationError) ErrorName() string { return "FileHostListReqValidationError" }

// Error satisfies the builtin error interface
func (e FileHostListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHostListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHostListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHostListReqValidationError{}

// Validate checks the field values on FileHostFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileHostFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHostFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileHostFilterMultiError,
// or nil if none found.
func (m *FileHostFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHostFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	if len(errors) > 0 {
		return FileHostFilterMultiError(errors)
	}

	return nil
}

// FileHostFilterMultiError is an error wrapping multiple validation errors
// returned by FileHostFilter.ValidateAll() if the designated constraints
// aren't met.
type FileHostFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHostFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHostFilterMultiError) AllErrors() []error { return m }

// FileHostFilterValidationError is the validation error returned by
// FileHostFilter.Validate if the designated constraints aren't met.
type FileHostFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHostFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHostFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHostFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHostFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHostFilterValidationError) ErrorName() string { return "FileHostFilterValidationError" }

// Error satisfies the builtin error interface
func (e FileHostFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHostFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHostFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHostFilterValidationError{}

// Validate checks the field values on FileHostListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileHostListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHostListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileHostListRespMultiError, or nil if none found.
func (m *FileHostListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHostListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileHostListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileHostListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileHostListRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return FileHostListRespMultiError(errors)
	}

	return nil
}

// FileHostListRespMultiError is an error wrapping multiple validation errors
// returned by FileHostListResp.ValidateAll() if the designated constraints
// aren't met.
type FileHostListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHostListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHostListRespMultiError) AllErrors() []error { return m }

// FileHostListRespValidationError is the validation error returned by
// FileHostListResp.Validate if the designated constraints aren't met.
type FileHostListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHostListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHostListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHostListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHostListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHostListRespValidationError) ErrorName() string { return "FileHostListRespValidationError" }

// Error satisfies the builtin error interface
func (e FileHostListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHostListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHostListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHostListRespValidationError{}

// Validate checks the field values on FileHostItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileHostItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHostItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileHostItemMultiError, or
// nil if none found.
func (m *FileHostItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHostItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for FileName

	// no validation rules for FileType

	// no validation rules for FileSize

	// no validation rules for FilePath

	// no validation rules for FileCreatedAt

	// no validation rules for FileFoundAt

	// no validation rules for Mac

	// no validation rules for Ip

	// no validation rules for Osver

	// no validation rules for Os

	// no validation rules for HostName

	// no validation rules for GroupId

	// no validation rules for GroupName

	// no validation rules for Online

	if len(errors) > 0 {
		return FileHostItemMultiError(errors)
	}

	return nil
}

// FileHostItemMultiError is an error wrapping multiple validation errors
// returned by FileHostItem.ValidateAll() if the designated constraints aren't met.
type FileHostItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHostItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHostItemMultiError) AllErrors() []error { return m }

// FileHostItemValidationError is the validation error returned by
// FileHostItem.Validate if the designated constraints aren't met.
type FileHostItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHostItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHostItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHostItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHostItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHostItemValidationError) ErrorName() string { return "FileHostItemValidationError" }

// Error satisfies the builtin error interface
func (e FileHostItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHostItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHostItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHostItemValidationError{}

// Validate checks the field values on HostViewReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostViewReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostViewReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostViewReqMultiError, or
// nil if none found.
func (m *HostViewReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HostViewReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HostViewReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HostViewReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HostViewReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HostViewReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HostViewReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HostViewReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HostViewReqMultiError(errors)
	}

	return nil
}

// HostViewReqMultiError is an error wrapping multiple validation errors
// returned by HostViewReq.ValidateAll() if the designated constraints aren't met.
type HostViewReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostViewReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostViewReqMultiError) AllErrors() []error { return m }

// HostViewReqValidationError is the validation error returned by
// HostViewReq.Validate if the designated constraints aren't met.
type HostViewReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostViewReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostViewReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostViewReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostViewReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostViewReqValidationError) ErrorName() string { return "HostViewReqValidationError" }

// Error satisfies the builtin error interface
func (e HostViewReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostViewReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostViewReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostViewReqValidationError{}

// Validate checks the field values on HostViewFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostViewFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostViewFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostViewFilterMultiError,
// or nil if none found.
func (m *HostViewFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *HostViewFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for FileCountStart

	// no validation rules for FileCountEnd

	if len(errors) > 0 {
		return HostViewFilterMultiError(errors)
	}

	return nil
}

// HostViewFilterMultiError is an error wrapping multiple validation errors
// returned by HostViewFilter.ValidateAll() if the designated constraints
// aren't met.
type HostViewFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostViewFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostViewFilterMultiError) AllErrors() []error { return m }

// HostViewFilterValidationError is the validation error returned by
// HostViewFilter.Validate if the designated constraints aren't met.
type HostViewFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostViewFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostViewFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostViewFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostViewFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostViewFilterValidationError) ErrorName() string { return "HostViewFilterValidationError" }

// Error satisfies the builtin error interface
func (e HostViewFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostViewFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostViewFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostViewFilterValidationError{}

// Validate checks the field values on HostViewResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostViewResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostViewResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostViewRespMultiError, or
// nil if none found.
func (m *HostViewResp) ValidateAll() error {
	return m.validate(true)
}

func (m *HostViewResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostViewRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostViewRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostViewRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return HostViewRespMultiError(errors)
	}

	return nil
}

// HostViewRespMultiError is an error wrapping multiple validation errors
// returned by HostViewResp.ValidateAll() if the designated constraints aren't met.
type HostViewRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostViewRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostViewRespMultiError) AllErrors() []error { return m }

// HostViewRespValidationError is the validation error returned by
// HostViewResp.Validate if the designated constraints aren't met.
type HostViewRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostViewRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostViewRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostViewRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostViewRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostViewRespValidationError) ErrorName() string { return "HostViewRespValidationError" }

// Error satisfies the builtin error interface
func (e HostViewRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostViewResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostViewRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostViewRespValidationError{}

// Validate checks the field values on HostViewItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostViewItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostViewItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostViewItemMultiError, or
// nil if none found.
func (m *HostViewItem) ValidateAll() error {
	return m.validate(true)
}

func (m *HostViewItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for Osver

	// no validation rules for Os

	// no validation rules for HostName

	// no validation rules for GroupId

	// no validation rules for GroupName

	// no validation rules for Mac

	// no validation rules for FileCount

	// no validation rules for Online

	if len(errors) > 0 {
		return HostViewItemMultiError(errors)
	}

	return nil
}

// HostViewItemMultiError is an error wrapping multiple validation errors
// returned by HostViewItem.ValidateAll() if the designated constraints aren't met.
type HostViewItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostViewItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostViewItemMultiError) AllErrors() []error { return m }

// HostViewItemValidationError is the validation error returned by
// HostViewItem.Validate if the designated constraints aren't met.
type HostViewItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostViewItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostViewItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostViewItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostViewItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostViewItemValidationError) ErrorName() string { return "HostViewItemValidationError" }

// Error satisfies the builtin error interface
func (e HostViewItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostViewItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostViewItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostViewItemValidationError{}

// Validate checks the field values on HostStatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostStatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostStatReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostStatReqMultiError, or
// nil if none found.
func (m *HostStatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HostStatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HostStatReqMultiError(errors)
	}

	return nil
}

// HostStatReqMultiError is an error wrapping multiple validation errors
// returned by HostStatReq.ValidateAll() if the designated constraints aren't met.
type HostStatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostStatReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostStatReqMultiError) AllErrors() []error { return m }

// HostStatReqValidationError is the validation error returned by
// HostStatReq.Validate if the designated constraints aren't met.
type HostStatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostStatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostStatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostStatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostStatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostStatReqValidationError) ErrorName() string { return "HostStatReqValidationError" }

// Error satisfies the builtin error interface
func (e HostStatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostStatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostStatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostStatReqValidationError{}

// Validate checks the field values on HostStatResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostStatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostStatResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostStatRespMultiError, or
// nil if none found.
func (m *HostStatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *HostStatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHosts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostStatRespValidationError{
						field:  fmt.Sprintf("Hosts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostStatRespValidationError{
						field:  fmt.Sprintf("Hosts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostStatRespValidationError{
					field:  fmt.Sprintf("Hosts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HostStatRespMultiError(errors)
	}

	return nil
}

// HostStatRespMultiError is an error wrapping multiple validation errors
// returned by HostStatResp.ValidateAll() if the designated constraints aren't met.
type HostStatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostStatRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostStatRespMultiError) AllErrors() []error { return m }

// HostStatRespValidationError is the validation error returned by
// HostStatResp.Validate if the designated constraints aren't met.
type HostStatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostStatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostStatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostStatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostStatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostStatRespValidationError) ErrorName() string { return "HostStatRespValidationError" }

// Error satisfies the builtin error interface
func (e HostStatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostStatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostStatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostStatRespValidationError{}

// Validate checks the field values on HostFileStat with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostFileStat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostFileStat with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostFileStatMultiError, or
// nil if none found.
func (m *HostFileStat) ValidateAll() error {
	return m.validate(true)
}

func (m *HostFileStat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for Count

	if len(errors) > 0 {
		return HostFileStatMultiError(errors)
	}

	return nil
}

// HostFileStatMultiError is an error wrapping multiple validation errors
// returned by HostFileStat.ValidateAll() if the designated constraints aren't met.
type HostFileStatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostFileStatMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostFileStatMultiError) AllErrors() []error { return m }

// HostFileStatValidationError is the validation error returned by
// HostFileStat.Validate if the designated constraints aren't met.
type HostFileStatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostFileStatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostFileStatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostFileStatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostFileStatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostFileStatValidationError) ErrorName() string { return "HostFileStatValidationError" }

// Error satisfies the builtin error interface
func (e HostFileStatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostFileStat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostFileStatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostFileStatValidationError{}

// Validate checks the field values on HostFileListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HostFileListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostFileListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostFileListReqMultiError, or nil if none found.
func (m *HostFileListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HostFileListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HostFileListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HostFileListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HostFileListReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HostFileListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HostFileListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HostFileListReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HostFileListReqMultiError(errors)
	}

	return nil
}

// HostFileListReqMultiError is an error wrapping multiple validation errors
// returned by HostFileListReq.ValidateAll() if the designated constraints
// aren't met.
type HostFileListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostFileListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostFileListReqMultiError) AllErrors() []error { return m }

// HostFileListReqValidationError is the validation error returned by
// HostFileListReq.Validate if the designated constraints aren't met.
type HostFileListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostFileListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostFileListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostFileListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostFileListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostFileListReqValidationError) ErrorName() string { return "HostFileListReqValidationError" }

// Error satisfies the builtin error interface
func (e HostFileListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostFileListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostFileListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostFileListReqValidationError{}

// Validate checks the field values on HostFileFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostFileFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostFileFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostFileFilterMultiError,
// or nil if none found.
func (m *HostFileFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *HostFileFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for FileName

	// no validation rules for Md5

	if len(errors) > 0 {
		return HostFileFilterMultiError(errors)
	}

	return nil
}

// HostFileFilterMultiError is an error wrapping multiple validation errors
// returned by HostFileFilter.ValidateAll() if the designated constraints
// aren't met.
type HostFileFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostFileFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostFileFilterMultiError) AllErrors() []error { return m }

// HostFileFilterValidationError is the validation error returned by
// HostFileFilter.Validate if the designated constraints aren't met.
type HostFileFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostFileFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostFileFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostFileFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostFileFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostFileFilterValidationError) ErrorName() string { return "HostFileFilterValidationError" }

// Error satisfies the builtin error interface
func (e HostFileFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostFileFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostFileFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostFileFilterValidationError{}

// Validate checks the field values on HostFileListResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HostFileListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostFileListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostFileListRespMultiError, or nil if none found.
func (m *HostFileListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *HostFileListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostFileListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostFileListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostFileListRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return HostFileListRespMultiError(errors)
	}

	return nil
}

// HostFileListRespMultiError is an error wrapping multiple validation errors
// returned by HostFileListResp.ValidateAll() if the designated constraints
// aren't met.
type HostFileListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostFileListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostFileListRespMultiError) AllErrors() []error { return m }

// HostFileListRespValidationError is the validation error returned by
// HostFileListResp.Validate if the designated constraints aren't met.
type HostFileListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostFileListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostFileListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostFileListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostFileListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostFileListRespValidationError) ErrorName() string { return "HostFileListRespValidationError" }

// Error satisfies the builtin error interface
func (e HostFileListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostFileListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostFileListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostFileListRespValidationError{}

// Validate checks the field values on FileItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileItemMultiError, or nil
// if none found.
func (m *FileItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FileItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for FileName

	// no validation rules for FileType

	// no validation rules for FileSize

	// no validation rules for FileCreatedAt

	// no validation rules for FileFoundAt

	if len(errors) > 0 {
		return FileItemMultiError(errors)
	}

	return nil
}

// FileItemMultiError is an error wrapping multiple validation errors returned
// by FileItem.ValidateAll() if the designated constraints aren't met.
type FileItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileItemMultiError) AllErrors() []error { return m }

// FileItemValidationError is the validation error returned by
// FileItem.Validate if the designated constraints aren't met.
type FileItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileItemValidationError) ErrorName() string { return "FileItemValidationError" }

// Error satisfies the builtin error interface
func (e FileItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileItemValidationError{}

// Validate checks the field values on FileSearchReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileSearchReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSearchReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileSearchReqMultiError, or
// nil if none found.
func (m *FileSearchReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSearchReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchData

	if len(errors) > 0 {
		return FileSearchReqMultiError(errors)
	}

	return nil
}

// FileSearchReqMultiError is an error wrapping multiple validation errors
// returned by FileSearchReq.ValidateAll() if the designated constraints
// aren't met.
type FileSearchReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSearchReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSearchReqMultiError) AllErrors() []error { return m }

// FileSearchReqValidationError is the validation error returned by
// FileSearchReq.Validate if the designated constraints aren't met.
type FileSearchReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSearchReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSearchReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSearchReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSearchReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSearchReqValidationError) ErrorName() string { return "FileSearchReqValidationError" }

// Error satisfies the builtin error interface
func (e FileSearchReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSearchReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSearchReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSearchReqValidationError{}

// Validate checks the field values on FileSearchResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileSearchResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSearchResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileSearchRespMultiError,
// or nil if none found.
func (m *FileSearchResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSearchResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	if len(errors) > 0 {
		return FileSearchRespMultiError(errors)
	}

	return nil
}

// FileSearchRespMultiError is an error wrapping multiple validation errors
// returned by FileSearchResp.ValidateAll() if the designated constraints
// aren't met.
type FileSearchRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSearchRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSearchRespMultiError) AllErrors() []error { return m }

// FileSearchRespValidationError is the validation error returned by
// FileSearchResp.Validate if the designated constraints aren't met.
type FileSearchRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSearchRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSearchRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSearchRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSearchRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSearchRespValidationError) ErrorName() string { return "FileSearchRespValidationError" }

// Error satisfies the builtin error interface
func (e FileSearchRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSearchResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSearchRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSearchRespValidationError{}

// Validate checks the field values on OutreachDailyStatReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachDailyStatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachDailyStatReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachDailyStatReqMultiError, or nil if none found.
func (m *OutreachDailyStatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachDailyStatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachDailyStatReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachDailyStatReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachDailyStatReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachDailyStatReqMultiError(errors)
	}

	return nil
}

// OutreachDailyStatReqMultiError is an error wrapping multiple validation
// errors returned by OutreachDailyStatReq.ValidateAll() if the designated
// constraints aren't met.
type OutreachDailyStatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachDailyStatReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachDailyStatReqMultiError) AllErrors() []error { return m }

// OutreachDailyStatReqValidationError is the validation error returned by
// OutreachDailyStatReq.Validate if the designated constraints aren't met.
type OutreachDailyStatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachDailyStatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachDailyStatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachDailyStatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachDailyStatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachDailyStatReqValidationError) ErrorName() string {
	return "OutreachDailyStatReqValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachDailyStatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachDailyStatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachDailyStatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachDailyStatReqValidationError{}

// Validate checks the field values on OutreachDailyStatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachDailyStatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachDailyStatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachDailyStatRespMultiError, or nil if none found.
func (m *OutreachDailyStatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachDailyStatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutreachDailyStatRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutreachDailyStatRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutreachDailyStatRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutreachDailyStatRespMultiError(errors)
	}

	return nil
}

// OutreachDailyStatRespMultiError is an error wrapping multiple validation
// errors returned by OutreachDailyStatResp.ValidateAll() if the designated
// constraints aren't met.
type OutreachDailyStatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachDailyStatRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachDailyStatRespMultiError) AllErrors() []error { return m }

// OutreachDailyStatRespValidationError is the validation error returned by
// OutreachDailyStatResp.Validate if the designated constraints aren't met.
type OutreachDailyStatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachDailyStatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachDailyStatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachDailyStatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachDailyStatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachDailyStatRespValidationError) ErrorName() string {
	return "OutreachDailyStatRespValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachDailyStatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachDailyStatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachDailyStatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachDailyStatRespValidationError{}

// Validate checks the field values on OutreachDailyStat with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachDailyStat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachDailyStat with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachDailyStatMultiError, or nil if none found.
func (m *OutreachDailyStat) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachDailyStat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	// no validation rules for Count

	if len(errors) > 0 {
		return OutreachDailyStatMultiError(errors)
	}

	return nil
}

// OutreachDailyStatMultiError is an error wrapping multiple validation errors
// returned by OutreachDailyStat.ValidateAll() if the designated constraints
// aren't met.
type OutreachDailyStatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachDailyStatMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachDailyStatMultiError) AllErrors() []error { return m }

// OutreachDailyStatValidationError is the validation error returned by
// OutreachDailyStat.Validate if the designated constraints aren't met.
type OutreachDailyStatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachDailyStatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachDailyStatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachDailyStatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachDailyStatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachDailyStatValidationError) ErrorName() string {
	return "OutreachDailyStatValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachDailyStatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachDailyStat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachDailyStatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachDailyStatValidationError{}

// Validate checks the field values on OutreachTotalReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachTotalReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachTotalReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachTotalReqMultiError, or nil if none found.
func (m *OutreachTotalReq) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachTotalReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachTotalReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachTotalReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachTotalReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachTotalReqMultiError(errors)
	}

	return nil
}

// OutreachTotalReqMultiError is an error wrapping multiple validation errors
// returned by OutreachTotalReq.ValidateAll() if the designated constraints
// aren't met.
type OutreachTotalReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachTotalReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachTotalReqMultiError) AllErrors() []error { return m }

// OutreachTotalReqValidationError is the validation error returned by
// OutreachTotalReq.Validate if the designated constraints aren't met.
type OutreachTotalReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachTotalReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachTotalReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachTotalReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachTotalReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachTotalReqValidationError) ErrorName() string { return "OutreachTotalReqValidationError" }

// Error satisfies the builtin error interface
func (e OutreachTotalReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachTotalReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachTotalReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachTotalReqValidationError{}

// Validate checks the field values on OutreachTotalResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachTotalResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachTotalResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachTotalRespMultiError, or nil if none found.
func (m *OutreachTotalResp) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachTotalResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	if len(errors) > 0 {
		return OutreachTotalRespMultiError(errors)
	}

	return nil
}

// OutreachTotalRespMultiError is an error wrapping multiple validation errors
// returned by OutreachTotalResp.ValidateAll() if the designated constraints
// aren't met.
type OutreachTotalRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachTotalRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachTotalRespMultiError) AllErrors() []error { return m }

// OutreachTotalRespValidationError is the validation error returned by
// OutreachTotalResp.Validate if the designated constraints aren't met.
type OutreachTotalRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachTotalRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachTotalRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachTotalRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachTotalRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachTotalRespValidationError) ErrorName() string {
	return "OutreachTotalRespValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachTotalRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachTotalResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachTotalRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachTotalRespValidationError{}

// Validate checks the field values on LogFilter with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogFilterMultiError, or nil
// if none found.
func (m *LogFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *LogFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Os

	// no validation rules for DateRange

	// no validation rules for SearchData

	if len(errors) > 0 {
		return LogFilterMultiError(errors)
	}

	return nil
}

// LogFilterMultiError is an error wrapping multiple validation errors returned
// by LogFilter.ValidateAll() if the designated constraints aren't met.
type LogFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogFilterMultiError) AllErrors() []error { return m }

// LogFilterValidationError is the validation error returned by
// LogFilter.Validate if the designated constraints aren't met.
type LogFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogFilterValidationError) ErrorName() string { return "LogFilterValidationError" }

// Error satisfies the builtin error interface
func (e LogFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogFilterValidationError{}

// Validate checks the field values on OutreachLogReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutreachLogReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachLogReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutreachLogReqMultiError,
// or nil if none found.
func (m *OutreachLogReq) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachLogReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachLogReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachLogReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachLogReqMultiError(errors)
	}

	return nil
}

// OutreachLogReqMultiError is an error wrapping multiple validation errors
// returned by OutreachLogReq.ValidateAll() if the designated constraints
// aren't met.
type OutreachLogReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachLogReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachLogReqMultiError) AllErrors() []error { return m }

// OutreachLogReqValidationError is the validation error returned by
// OutreachLogReq.Validate if the designated constraints aren't met.
type OutreachLogReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachLogReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachLogReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachLogReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachLogReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachLogReqValidationError) ErrorName() string { return "OutreachLogReqValidationError" }

// Error satisfies the builtin error interface
func (e OutreachLogReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachLogReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachLogReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachLogReqValidationError{}

// Validate checks the field values on OutreachLogResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachLogResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachLogResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachLogRespMultiError, or nil if none found.
func (m *OutreachLogResp) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachLogResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutreachLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutreachLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutreachLogRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return OutreachLogRespMultiError(errors)
	}

	return nil
}

// OutreachLogRespMultiError is an error wrapping multiple validation errors
// returned by OutreachLogResp.ValidateAll() if the designated constraints
// aren't met.
type OutreachLogRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachLogRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachLogRespMultiError) AllErrors() []error { return m }

// OutreachLogRespValidationError is the validation error returned by
// OutreachLogResp.Validate if the designated constraints aren't met.
type OutreachLogRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachLogRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachLogRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachLogRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachLogRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachLogRespValidationError) ErrorName() string { return "OutreachLogRespValidationError" }

// Error satisfies the builtin error interface
func (e OutreachLogRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachLogResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachLogRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachLogRespValidationError{}

// Validate checks the field values on OutreachLogItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachLogItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachLogItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachLogItemMultiError, or nil if none found.
func (m *OutreachLogItem) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachLogItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Ip

	// no validation rules for Hostname

	// no validation rules for Os

	// no validation rules for Pid

	// no validation rules for Pname

	// no validation rules for Ppid

	// no validation rules for Ppname

	// no validation rules for Epid

	// no validation rules for User

	// no validation rules for Euid

	// no validation rules for Cmd

	// no validation rules for StartAt

	// no validation rules for Path

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for Size

	// no validation rules for Ctime

	// no validation rules for Mtime

	// no validation rules for Atime

	// no validation rules for Attrs

	// no validation rules for Signs

	// no validation rules for Uuid

	// no validation rules for SourceIp

	// no validation rules for SourcePort

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	// no validation rules for Protocol

	// no validation rules for TriggerTime

	// no validation rules for CreateAt

	if len(errors) > 0 {
		return OutreachLogItemMultiError(errors)
	}

	return nil
}

// OutreachLogItemMultiError is an error wrapping multiple validation errors
// returned by OutreachLogItem.ValidateAll() if the designated constraints
// aren't met.
type OutreachLogItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachLogItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachLogItemMultiError) AllErrors() []error { return m }

// OutreachLogItemValidationError is the validation error returned by
// OutreachLogItem.Validate if the designated constraints aren't met.
type OutreachLogItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachLogItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachLogItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachLogItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachLogItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachLogItemValidationError) ErrorName() string { return "OutreachLogItemValidationError" }

// Error satisfies the builtin error interface
func (e OutreachLogItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachLogItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachLogItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachLogItemValidationError{}

// Validate checks the field values on FileCreateLogReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileCreateLogReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileCreateLogReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileCreateLogReqMultiError, or nil if none found.
func (m *FileCreateLogReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileCreateLogReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileCreateLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileCreateLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileCreateLogReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileCreateLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileCreateLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileCreateLogReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileCreateLogReqMultiError(errors)
	}

	return nil
}

// FileCreateLogReqMultiError is an error wrapping multiple validation errors
// returned by FileCreateLogReq.ValidateAll() if the designated constraints
// aren't met.
type FileCreateLogReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileCreateLogReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileCreateLogReqMultiError) AllErrors() []error { return m }

// FileCreateLogReqValidationError is the validation error returned by
// FileCreateLogReq.Validate if the designated constraints aren't met.
type FileCreateLogReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileCreateLogReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileCreateLogReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileCreateLogReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileCreateLogReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileCreateLogReqValidationError) ErrorName() string { return "FileCreateLogReqValidationError" }

// Error satisfies the builtin error interface
func (e FileCreateLogReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileCreateLogReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileCreateLogReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileCreateLogReqValidationError{}

// Validate checks the field values on FileCreateLogResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileCreateLogResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileCreateLogResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileCreateLogRespMultiError, or nil if none found.
func (m *FileCreateLogResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileCreateLogResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileCreateLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileCreateLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileCreateLogRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return FileCreateLogRespMultiError(errors)
	}

	return nil
}

// FileCreateLogRespMultiError is an error wrapping multiple validation errors
// returned by FileCreateLogResp.ValidateAll() if the designated constraints
// aren't met.
type FileCreateLogRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileCreateLogRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileCreateLogRespMultiError) AllErrors() []error { return m }

// FileCreateLogRespValidationError is the validation error returned by
// FileCreateLogResp.Validate if the designated constraints aren't met.
type FileCreateLogRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileCreateLogRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileCreateLogRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileCreateLogRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileCreateLogRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileCreateLogRespValidationError) ErrorName() string {
	return "FileCreateLogRespValidationError"
}

// Error satisfies the builtin error interface
func (e FileCreateLogRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileCreateLogResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileCreateLogRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileCreateLogRespValidationError{}

// Validate checks the field values on FileCreateLogItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileCreateLogItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileCreateLogItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileCreateLogItemMultiError, or nil if none found.
func (m *FileCreateLogItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FileCreateLogItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Ip

	// no validation rules for Hostname

	// no validation rules for Os

	// no validation rules for Pid

	// no validation rules for StartAt

	// no validation rules for Path

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for Size

	// no validation rules for Ctime

	// no validation rules for Mtime

	// no validation rules for Atime

	// no validation rules for Attrs

	// no validation rules for Signs

	// no validation rules for Permission

	// no validation rules for UniqueKey

	// no validation rules for TriggerTime

	// no validation rules for CreateAt

	if len(errors) > 0 {
		return FileCreateLogItemMultiError(errors)
	}

	return nil
}

// FileCreateLogItemMultiError is an error wrapping multiple validation errors
// returned by FileCreateLogItem.ValidateAll() if the designated constraints
// aren't met.
type FileCreateLogItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileCreateLogItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileCreateLogItemMultiError) AllErrors() []error { return m }

// FileCreateLogItemValidationError is the validation error returned by
// FileCreateLogItem.Validate if the designated constraints aren't met.
type FileCreateLogItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileCreateLogItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileCreateLogItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileCreateLogItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileCreateLogItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileCreateLogItemValidationError) ErrorName() string {
	return "FileCreateLogItemValidationError"
}

// Error satisfies the builtin error interface
func (e FileCreateLogItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileCreateLogItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileCreateLogItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileCreateLogItemValidationError{}

// Validate checks the field values on ProcessLogReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessLogReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessLogReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcessLogReqMultiError, or
// nil if none found.
func (m *ProcessLogReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessLogReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessLogReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessLogReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessLogReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessLogReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessLogReqMultiError(errors)
	}

	return nil
}

// ProcessLogReqMultiError is an error wrapping multiple validation errors
// returned by ProcessLogReq.ValidateAll() if the designated constraints
// aren't met.
type ProcessLogReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessLogReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessLogReqMultiError) AllErrors() []error { return m }

// ProcessLogReqValidationError is the validation error returned by
// ProcessLogReq.Validate if the designated constraints aren't met.
type ProcessLogReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessLogReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessLogReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessLogReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessLogReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessLogReqValidationError) ErrorName() string { return "ProcessLogReqValidationError" }

// Error satisfies the builtin error interface
func (e ProcessLogReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessLogReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessLogReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessLogReqValidationError{}

// Validate checks the field values on ProcessLogResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessLogResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessLogResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcessLogRespMultiError,
// or nil if none found.
func (m *ProcessLogResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessLogResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessLogRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessLogRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return ProcessLogRespMultiError(errors)
	}

	return nil
}

// ProcessLogRespMultiError is an error wrapping multiple validation errors
// returned by ProcessLogResp.ValidateAll() if the designated constraints
// aren't met.
type ProcessLogRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessLogRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessLogRespMultiError) AllErrors() []error { return m }

// ProcessLogRespValidationError is the validation error returned by
// ProcessLogResp.Validate if the designated constraints aren't met.
type ProcessLogRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessLogRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessLogRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessLogRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessLogRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessLogRespValidationError) ErrorName() string { return "ProcessLogRespValidationError" }

// Error satisfies the builtin error interface
func (e ProcessLogRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessLogResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessLogRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessLogRespValidationError{}

// Validate checks the field values on ProcessLogItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessLogItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessLogItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcessLogItemMultiError,
// or nil if none found.
func (m *ProcessLogItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessLogItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for Ip

	// no validation rules for Hostname

	// no validation rules for Os

	// no validation rules for Ppname

	// no validation rules for Ppid

	// no validation rules for Epid

	// no validation rules for Pname

	// no validation rules for Pid

	// no validation rules for User

	// no validation rules for Euid

	// no validation rules for Cmd

	// no validation rules for StartAt

	// no validation rules for Path

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for Size

	// no validation rules for Ctime

	// no validation rules for Mtime

	// no validation rules for Atime

	// no validation rules for Attrs

	// no validation rules for Signs

	// no validation rules for UniqueKey

	// no validation rules for TriggerTime

	// no validation rules for CreateAt

	if len(errors) > 0 {
		return ProcessLogItemMultiError(errors)
	}

	return nil
}

// ProcessLogItemMultiError is an error wrapping multiple validation errors
// returned by ProcessLogItem.ValidateAll() if the designated constraints
// aren't met.
type ProcessLogItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessLogItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessLogItemMultiError) AllErrors() []error { return m }

// ProcessLogItemValidationError is the validation error returned by
// ProcessLogItem.Validate if the designated constraints aren't met.
type ProcessLogItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessLogItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessLogItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessLogItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessLogItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessLogItemValidationError) ErrorName() string { return "ProcessLogItemValidationError" }

// Error satisfies the builtin error interface
func (e ProcessLogItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessLogItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessLogItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessLogItemValidationError{}

// Validate checks the field values on SearchSchemas with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchSchemas) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchSchemas with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchSchemasMultiError, or
// nil if none found.
func (m *SearchSchemas) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchSchemas) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetProcessLinux() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("ProcessLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("ProcessLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("ProcessLinux[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcessWindows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("ProcessWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("ProcessWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("ProcessWindows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOutreachLinux() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("OutreachLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("OutreachLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("OutreachLinux[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetOutreachWindows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("OutreachWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("OutreachWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("OutreachWindows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFilecreateLinux() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("FilecreateLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("FilecreateLinux[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("FilecreateLinux[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFilecreateWindows() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("FilecreateWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchSchemasValidationError{
						field:  fmt.Sprintf("FilecreateWindows[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchSchemasValidationError{
					field:  fmt.Sprintf("FilecreateWindows[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchSchemasMultiError(errors)
	}

	return nil
}

// SearchSchemasMultiError is an error wrapping multiple validation errors
// returned by SearchSchemas.ValidateAll() if the designated constraints
// aren't met.
type SearchSchemasMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchSchemasMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchSchemasMultiError) AllErrors() []error { return m }

// SearchSchemasValidationError is the validation error returned by
// SearchSchemas.Validate if the designated constraints aren't met.
type SearchSchemasValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchSchemasValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchSchemasValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchSchemasValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchSchemasValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchSchemasValidationError) ErrorName() string { return "SearchSchemasValidationError" }

// Error satisfies the builtin error interface
func (e SearchSchemasValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchSchemas.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchSchemasValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchSchemasValidationError{}

// Validate checks the field values on SearchField with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchField) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchField with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchFieldMultiError, or
// nil if none found.
func (m *SearchField) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchField) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Field

	// no validation rules for Label

	// no validation rules for Type

	// no validation rules for Placeholder

	if len(errors) > 0 {
		return SearchFieldMultiError(errors)
	}

	return nil
}

// SearchFieldMultiError is an error wrapping multiple validation errors
// returned by SearchField.ValidateAll() if the designated constraints aren't met.
type SearchFieldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchFieldMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchFieldMultiError) AllErrors() []error { return m }

// SearchFieldValidationError is the validation error returned by
// SearchField.Validate if the designated constraints aren't met.
type SearchFieldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchFieldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchFieldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchFieldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchFieldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchFieldValidationError) ErrorName() string { return "SearchFieldValidationError" }

// Error satisfies the builtin error interface
func (e SearchFieldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchField.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchFieldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchFieldValidationError{}

// Validate checks the field values on MacProgressStatReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MacProgressStatReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressStatReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressStatReqMultiError, or nil if none found.
func (m *MacProgressStatReq) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressStatReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if len(errors) > 0 {
		return MacProgressStatReqMultiError(errors)
	}

	return nil
}

// MacProgressStatReqMultiError is an error wrapping multiple validation errors
// returned by MacProgressStatReq.ValidateAll() if the designated constraints
// aren't met.
type MacProgressStatReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressStatReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressStatReqMultiError) AllErrors() []error { return m }

// MacProgressStatReqValidationError is the validation error returned by
// MacProgressStatReq.Validate if the designated constraints aren't met.
type MacProgressStatReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressStatReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressStatReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressStatReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressStatReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressStatReqValidationError) ErrorName() string {
	return "MacProgressStatReqValidationError"
}

// Error satisfies the builtin error interface
func (e MacProgressStatReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressStatReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressStatReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressStatReqValidationError{}

// Validate checks the field values on MacProgressStatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MacProgressStatResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressStatResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressStatRespMultiError, or nil if none found.
func (m *MacProgressStatResp) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressStatResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProgressingCount

	// no validation rules for TerminalTotal

	// no validation rules for ProgressingPercent

	// no validation rules for SumFoundFileCount

	if len(errors) > 0 {
		return MacProgressStatRespMultiError(errors)
	}

	return nil
}

// MacProgressStatRespMultiError is an error wrapping multiple validation
// errors returned by MacProgressStatResp.ValidateAll() if the designated
// constraints aren't met.
type MacProgressStatRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressStatRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressStatRespMultiError) AllErrors() []error { return m }

// MacProgressStatRespValidationError is the validation error returned by
// MacProgressStatResp.Validate if the designated constraints aren't met.
type MacProgressStatRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressStatRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressStatRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressStatRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressStatRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressStatRespValidationError) ErrorName() string {
	return "MacProgressStatRespValidationError"
}

// Error satisfies the builtin error interface
func (e MacProgressStatRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressStatResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressStatRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressStatRespValidationError{}

// Validate checks the field values on MacProgressListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MacProgressListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressListReqMultiError, or nil if none found.
func (m *MacProgressListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MacProgressListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MacProgressListReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MacProgressListReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MacProgressListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MacProgressListReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MacProgressListReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MacProgressListReqMultiError(errors)
	}

	return nil
}

// MacProgressListReqMultiError is an error wrapping multiple validation errors
// returned by MacProgressListReq.ValidateAll() if the designated constraints
// aren't met.
type MacProgressListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressListReqMultiError) AllErrors() []error { return m }

// MacProgressListReqValidationError is the validation error returned by
// MacProgressListReq.Validate if the designated constraints aren't met.
type MacProgressListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressListReqValidationError) ErrorName() string {
	return "MacProgressListReqValidationError"
}

// Error satisfies the builtin error interface
func (e MacProgressListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressListReqValidationError{}

// Validate checks the field values on MacProgressFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MacProgressFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressFilterMultiError, or nil if none found.
func (m *MacProgressFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Ip

	// no validation rules for HostName

	if len(errors) > 0 {
		return MacProgressFilterMultiError(errors)
	}

	return nil
}

// MacProgressFilterMultiError is an error wrapping multiple validation errors
// returned by MacProgressFilter.ValidateAll() if the designated constraints
// aren't met.
type MacProgressFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressFilterMultiError) AllErrors() []error { return m }

// MacProgressFilterValidationError is the validation error returned by
// MacProgressFilter.Validate if the designated constraints aren't met.
type MacProgressFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressFilterValidationError) ErrorName() string {
	return "MacProgressFilterValidationError"
}

// Error satisfies the builtin error interface
func (e MacProgressFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressFilterValidationError{}

// Validate checks the field values on MacProgressListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MacProgressListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressListRespMultiError, or nil if none found.
func (m *MacProgressListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MacProgressListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MacProgressListRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MacProgressListRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	// no validation rules for PageIndex

	if len(errors) > 0 {
		return MacProgressListRespMultiError(errors)
	}

	return nil
}

// MacProgressListRespMultiError is an error wrapping multiple validation
// errors returned by MacProgressListResp.ValidateAll() if the designated
// constraints aren't met.
type MacProgressListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressListRespMultiError) AllErrors() []error { return m }

// MacProgressListRespValidationError is the validation error returned by
// MacProgressListResp.Validate if the designated constraints aren't met.
type MacProgressListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressListRespValidationError) ErrorName() string {
	return "MacProgressListRespValidationError"
}

// Error satisfies the builtin error interface
func (e MacProgressListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressListRespValidationError{}

// Validate checks the field values on MacProgressItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MacProgressItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MacProgressItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MacProgressItemMultiError, or nil if none found.
func (m *MacProgressItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MacProgressItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mac

	// no validation rules for Ip

	// no validation rules for OsType

	// no validation rules for Os

	// no validation rules for HostName

	// no validation rules for GroupId

	// no validation rules for GroupName

	// no validation rules for HostOnline

	// no validation rules for Status

	// no validation rules for StatusText

	// no validation rules for Progress

	// no validation rules for MacAddr

	if len(errors) > 0 {
		return MacProgressItemMultiError(errors)
	}

	return nil
}

// MacProgressItemMultiError is an error wrapping multiple validation errors
// returned by MacProgressItem.ValidateAll() if the designated constraints
// aren't met.
type MacProgressItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MacProgressItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MacProgressItemMultiError) AllErrors() []error { return m }

// MacProgressItemValidationError is the validation error returned by
// MacProgressItem.Validate if the designated constraints aren't met.
type MacProgressItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MacProgressItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MacProgressItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MacProgressItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MacProgressItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MacProgressItemValidationError) ErrorName() string { return "MacProgressItemValidationError" }

// Error satisfies the builtin error interface
func (e MacProgressItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMacProgressItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MacProgressItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MacProgressItemValidationError{}
