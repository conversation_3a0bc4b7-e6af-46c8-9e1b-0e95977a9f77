// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: yone/yone.proto

package yone

import (
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	conan "git.anxin.com/v01-cluster/vapi/pkg/conan"
	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 日志接收服务异常状态码
type Codes int32

const (
	Codes_CODE_UNKNOWN                 Codes = 0
	Codes_ERROR_INVALID_PARAMETERS     Codes = 20001 // 参数异常
	Codes_ERROR_FILE_ARCHIVE_NOT_FOUND Codes = 20002 // 文件档案未找到
)

// Enum value maps for Codes.
var (
	Codes_name = map[int32]string{
		0:     "CODE_UNKNOWN",
		20001: "ERROR_INVALID_PARAMETERS",
		20002: "ERROR_FILE_ARCHIVE_NOT_FOUND",
	}
	Codes_value = map[string]int32{
		"CODE_UNKNOWN":                 0,
		"ERROR_INVALID_PARAMETERS":     20001,
		"ERROR_FILE_ARCHIVE_NOT_FOUND": 20002,
	}
)

func (x Codes) Enum() *Codes {
	p := new(Codes)
	*p = x
	return p
}

func (x Codes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Codes) Descriptor() protoreflect.EnumDescriptor {
	return file_yone_yone_proto_enumTypes[0].Descriptor()
}

func (Codes) Type() protoreflect.EnumType {
	return &file_yone_yone_proto_enumTypes[0]
}

func (x Codes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Codes.Descriptor instead.
func (Codes) EnumDescriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{0}
}

// 文件检测结果
type DetectResult int32

const (
	DetectResult_DETECT_RESULT_INVALID DetectResult = 0 // 结果异常
	DetectResult_DETECT_RESULT_WHITE   DetectResult = 1 // 文件为白
	DetectResult_DETECT_RESULT_BLACK   DetectResult = 2 // 文件为黑
	DetectResult_DETECT_RESULT_GRAY    DetectResult = 3 // 文件为灰
	DetectResult_DETECT_RESULT_UNKNOWN DetectResult = 4 // 结果未知
)

// Enum value maps for DetectResult.
var (
	DetectResult_name = map[int32]string{
		0: "DETECT_RESULT_INVALID",
		1: "DETECT_RESULT_WHITE",
		2: "DETECT_RESULT_BLACK",
		3: "DETECT_RESULT_GRAY",
		4: "DETECT_RESULT_UNKNOWN",
	}
	DetectResult_value = map[string]int32{
		"DETECT_RESULT_INVALID": 0,
		"DETECT_RESULT_WHITE":   1,
		"DETECT_RESULT_BLACK":   2,
		"DETECT_RESULT_GRAY":    3,
		"DETECT_RESULT_UNKNOWN": 4,
	}
)

func (x DetectResult) Enum() *DetectResult {
	p := new(DetectResult)
	*p = x
	return p
}

func (x DetectResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectResult) Descriptor() protoreflect.EnumDescriptor {
	return file_yone_yone_proto_enumTypes[1].Descriptor()
}

func (DetectResult) Type() protoreflect.EnumType {
	return &file_yone_yone_proto_enumTypes[1]
}

func (x DetectResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectResult.Descriptor instead.
func (DetectResult) EnumDescriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{1}
}

// 文件检测状态
type FileTaskStatus int32

const (
	FileTaskStatus_TASK_STATUS_NONE           FileTaskStatus = 0  // 无检测结果
	FileTaskStatus_TASK_STATUS_FETCH_WAIT     FileTaskStatus = 1  // 待提取文件
	FileTaskStatus_TASK_STATUS_FETCHING       FileTaskStatus = 2  // 文件提取中
	FileTaskStatus_TASK_STATUS_FETCH_SUCCESS  FileTaskStatus = 3  // 文件提取成功
	FileTaskStatus_TASK_STATUS_FETCH_FAILED   FileTaskStatus = 4  // 文件提取失败
	FileTaskStatus_TASK_STATUS_FETCH_TIMEOUT  FileTaskStatus = 5  // 文件提取超时
	FileTaskStatus_TASK_STATUS_DETECT_QUEUE   FileTaskStatus = 6  // 文件已进入检测队列
	FileTaskStatus_TASK_STATUS_DETECTING      FileTaskStatus = 7  // 文件检测中
	FileTaskStatus_TASK_STATUS_DETECT_SUCCESS FileTaskStatus = 8  // 文件检测成功
	FileTaskStatus_TASK_STATUS_DETECT_FAILED  FileTaskStatus = 9  // 文件检测失败
	FileTaskStatus_TASK_STATUS_DETECT_TIMEOUT FileTaskStatus = 10 // 文件检测超时
)

// Enum value maps for FileTaskStatus.
var (
	FileTaskStatus_name = map[int32]string{
		0:  "TASK_STATUS_NONE",
		1:  "TASK_STATUS_FETCH_WAIT",
		2:  "TASK_STATUS_FETCHING",
		3:  "TASK_STATUS_FETCH_SUCCESS",
		4:  "TASK_STATUS_FETCH_FAILED",
		5:  "TASK_STATUS_FETCH_TIMEOUT",
		6:  "TASK_STATUS_DETECT_QUEUE",
		7:  "TASK_STATUS_DETECTING",
		8:  "TASK_STATUS_DETECT_SUCCESS",
		9:  "TASK_STATUS_DETECT_FAILED",
		10: "TASK_STATUS_DETECT_TIMEOUT",
	}
	FileTaskStatus_value = map[string]int32{
		"TASK_STATUS_NONE":           0,
		"TASK_STATUS_FETCH_WAIT":     1,
		"TASK_STATUS_FETCHING":       2,
		"TASK_STATUS_FETCH_SUCCESS":  3,
		"TASK_STATUS_FETCH_FAILED":   4,
		"TASK_STATUS_FETCH_TIMEOUT":  5,
		"TASK_STATUS_DETECT_QUEUE":   6,
		"TASK_STATUS_DETECTING":      7,
		"TASK_STATUS_DETECT_SUCCESS": 8,
		"TASK_STATUS_DETECT_FAILED":  9,
		"TASK_STATUS_DETECT_TIMEOUT": 10,
	}
)

func (x FileTaskStatus) Enum() *FileTaskStatus {
	p := new(FileTaskStatus)
	*p = x
	return p
}

func (x FileTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_yone_yone_proto_enumTypes[2].Descriptor()
}

func (FileTaskStatus) Type() protoreflect.EnumType {
	return &file_yone_yone_proto_enumTypes[2]
}

func (x FileTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileTaskStatus.Descriptor instead.
func (FileTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{2}
}

type OsType int32

const (
	OsType_OS_TYPE_UNKNOWN OsType = 0 // 类型位置
	OsType_OS_TYPE_WINDOWS OsType = 1
	OsType_OS_TYPE_LINUX   OsType = 2
)

// Enum value maps for OsType.
var (
	OsType_name = map[int32]string{
		0: "OS_TYPE_UNKNOWN",
		1: "OS_TYPE_WINDOWS",
		2: "OS_TYPE_LINUX",
	}
	OsType_value = map[string]int32{
		"OS_TYPE_UNKNOWN": 0,
		"OS_TYPE_WINDOWS": 1,
		"OS_TYPE_LINUX":   2,
	}
)

func (x OsType) Enum() *OsType {
	p := new(OsType)
	*p = x
	return p
}

func (x OsType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OsType) Descriptor() protoreflect.EnumDescriptor {
	return file_yone_yone_proto_enumTypes[3].Descriptor()
}

func (OsType) Type() protoreflect.EnumType {
	return &file_yone_yone_proto_enumTypes[3]
}

func (x OsType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OsType.Descriptor instead.
func (OsType) EnumDescriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{3}
}

type ProgressType int32

const (
	ProgressType_PROGRESS_TYPE_UNKNOWN  ProgressType = 0
	ProgressType_PROGRESS_TYPE_THREATEN ProgressType = 1 // 文件威胁
	ProgressType_PROGRESS_TYPE_SNAPSHOT ProgressType = 2 // 文件快照
)

// Enum value maps for ProgressType.
var (
	ProgressType_name = map[int32]string{
		0: "PROGRESS_TYPE_UNKNOWN",
		1: "PROGRESS_TYPE_THREATEN",
		2: "PROGRESS_TYPE_SNAPSHOT",
	}
	ProgressType_value = map[string]int32{
		"PROGRESS_TYPE_UNKNOWN":  0,
		"PROGRESS_TYPE_THREATEN": 1,
		"PROGRESS_TYPE_SNAPSHOT": 2,
	}
)

func (x ProgressType) Enum() *ProgressType {
	p := new(ProgressType)
	*p = x
	return p
}

func (x ProgressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProgressType) Descriptor() protoreflect.EnumDescriptor {
	return file_yone_yone_proto_enumTypes[4].Descriptor()
}

func (ProgressType) Type() protoreflect.EnumType {
	return &file_yone_yone_proto_enumTypes[4]
}

func (x ProgressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProgressType.Descriptor instead.
func (ProgressType) EnumDescriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{4}
}

type FileReceiveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac   string                   `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`
	Files []*agent.VirusDetectItem `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"` // agent同步的文件信息
}

func (x *FileReceiveReq) Reset() {
	*x = FileReceiveReq{}
	mi := &file_yone_yone_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileReceiveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileReceiveReq) ProtoMessage() {}

func (x *FileReceiveReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileReceiveReq.ProtoReflect.Descriptor instead.
func (*FileReceiveReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{0}
}

func (x *FileReceiveReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *FileReceiveReq) GetFiles() []*agent.VirusDetectItem {
	if x != nil {
		return x.Files
	}
	return nil
}

type FileReceiveResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessNum int64 `protobuf:"varint,1,opt,name=success_num,json=successNum,proto3" json:"success_num,omitempty"`
}

func (x *FileReceiveResp) Reset() {
	*x = FileReceiveResp{}
	mi := &file_yone_yone_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileReceiveResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileReceiveResp) ProtoMessage() {}

func (x *FileReceiveResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileReceiveResp.ProtoReflect.Descriptor instead.
func (*FileReceiveResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{1}
}

func (x *FileReceiveResp) GetSuccessNum() int64 {
	if x != nil {
		return x.SuccessNum
	}
	return 0
}

type VirusReceiveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac       string                         `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`
	VirusInfo *agent.MemProtectVirusFileInfo `protobuf:"bytes,2,opt,name=virus_info,json=virusInfo,proto3" json:"virus_info,omitempty"`
}

func (x *VirusReceiveReq) Reset() {
	*x = VirusReceiveReq{}
	mi := &file_yone_yone_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusReceiveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusReceiveReq) ProtoMessage() {}

func (x *VirusReceiveReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusReceiveReq.ProtoReflect.Descriptor instead.
func (*VirusReceiveReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{2}
}

func (x *VirusReceiveReq) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *VirusReceiveReq) GetVirusInfo() *agent.MemProtectVirusFileInfo {
	if x != nil {
		return x.VirusInfo
	}
	return nil
}

type VirusReceiveResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessNum int64 `protobuf:"varint,1,opt,name=success_num,json=successNum,proto3" json:"success_num,omitempty"`
}

func (x *VirusReceiveResp) Reset() {
	*x = VirusReceiveResp{}
	mi := &file_yone_yone_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusReceiveResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusReceiveResp) ProtoMessage() {}

func (x *VirusReceiveResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusReceiveResp.ProtoReflect.Descriptor instead.
func (*VirusReceiveResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{3}
}

func (x *VirusReceiveResp) GetSuccessNum() int64 {
	if x != nil {
		return x.SuccessNum
	}
	return 0
}

type OutreachReceiveReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Outreaches []*agent.InternalOutreachInfoWithMac `protobuf:"bytes,1,rep,name=outreaches,proto3" json:"outreaches,omitempty"`
}

func (x *OutreachReceiveReq) Reset() {
	*x = OutreachReceiveReq{}
	mi := &file_yone_yone_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachReceiveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachReceiveReq) ProtoMessage() {}

func (x *OutreachReceiveReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachReceiveReq.ProtoReflect.Descriptor instead.
func (*OutreachReceiveReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{4}
}

func (x *OutreachReceiveReq) GetOutreaches() []*agent.InternalOutreachInfoWithMac {
	if x != nil {
		return x.Outreaches
	}
	return nil
}

type OutreachReceiveResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SuccessNum int64 `protobuf:"varint,1,opt,name=success_num,json=successNum,proto3" json:"success_num,omitempty"`
}

func (x *OutreachReceiveResp) Reset() {
	*x = OutreachReceiveResp{}
	mi := &file_yone_yone_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachReceiveResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachReceiveResp) ProtoMessage() {}

func (x *OutreachReceiveResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachReceiveResp.ProtoReflect.Descriptor instead.
func (*OutreachReceiveResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{5}
}

func (x *OutreachReceiveResp) GetSuccessNum() int64 {
	if x != nil {
		return x.SuccessNum
	}
	return 0
}

type ProcChainsNodesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uniques  []string `protobuf:"bytes,1,rep,name=uniques,proto3" json:"uniques,omitempty"`                    // 进程唯一标识列表，unique生成方式和历史逻辑想通: hex(sha256("mac:pid:startTime")
	OnlyLeaf bool     `protobuf:"varint,2,opt,name=only_leaf,json=onlyLeaf,proto3" json:"only_leaf,omitempty"` // 仅查询叶子节点，不会递归向上查询父节点
}

func (x *ProcChainsNodesReq) Reset() {
	*x = ProcChainsNodesReq{}
	mi := &file_yone_yone_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainsNodesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainsNodesReq) ProtoMessage() {}

func (x *ProcChainsNodesReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainsNodesReq.ProtoReflect.Descriptor instead.
func (*ProcChainsNodesReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{6}
}

func (x *ProcChainsNodesReq) GetUniques() []string {
	if x != nil {
		return x.Uniques
	}
	return nil
}

func (x *ProcChainsNodesReq) GetOnlyLeaf() bool {
	if x != nil {
		return x.OnlyLeaf
	}
	return false
}

type ProcChainsNodesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcNodes []*ProcChainNode `protobuf:"bytes,1,rep,name=proc_nodes,json=procNodes,proto3" json:"proc_nodes,omitempty"` // 进程节点列表，已基于进程unique去重
	Files     []*ProcChainFile `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`                          // 进程链中的文件信息，已基于文件unique去重
}

func (x *ProcChainsNodesResp) Reset() {
	*x = ProcChainsNodesResp{}
	mi := &file_yone_yone_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainsNodesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainsNodesResp) ProtoMessage() {}

func (x *ProcChainsNodesResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainsNodesResp.ProtoReflect.Descriptor instead.
func (*ProcChainsNodesResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{7}
}

func (x *ProcChainsNodesResp) GetProcNodes() []*ProcChainNode {
	if x != nil {
		return x.ProcNodes
	}
	return nil
}

func (x *ProcChainsNodesResp) GetFiles() []*ProcChainFile {
	if x != nil {
		return x.Files
	}
	return nil
}

type ProcChainsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Beans []*ProcNodeBean `protobuf:"bytes,1,rep,name=beans,proto3" json:"beans,omitempty"` // 叶子节点列表
}

func (x *ProcChainsReq) Reset() {
	*x = ProcChainsReq{}
	mi := &file_yone_yone_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainsReq) ProtoMessage() {}

func (x *ProcChainsReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainsReq.ProtoReflect.Descriptor instead.
func (*ProcChainsReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{8}
}

func (x *ProcChainsReq) GetBeans() []*ProcNodeBean {
	if x != nil {
		return x.Beans
	}
	return nil
}

type ProcChainsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chains []*ProcChain `protobuf:"bytes,2,rep,name=chains,proto3" json:"chains,omitempty"` // 进程链列表
}

func (x *ProcChainsResp) Reset() {
	*x = ProcChainsResp{}
	mi := &file_yone_yone_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainsResp) ProtoMessage() {}

func (x *ProcChainsResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainsResp.ProtoReflect.Descriptor instead.
func (*ProcChainsResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{9}
}

func (x *ProcChainsResp) GetChains() []*ProcChain {
	if x != nil {
		return x.Chains
	}
	return nil
}

type ProcChain struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 使用进程链第一个节点(叶子节点)的唯一标识作为整个继承链的唯一标识
	Unique string `protobuf:"bytes,1,opt,name=unique,proto3" json:"unique,omitempty"`
	// 进程链包含的节点列表，进行查询的叶子节点将出现在进程链的第一个，之后依次为父进程，父父进程节点，直至无父进程节点结束
	Nodes []*ProcChainNodeWithFile `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`
}

func (x *ProcChain) Reset() {
	*x = ProcChain{}
	mi := &file_yone_yone_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChain) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChain) ProtoMessage() {}

func (x *ProcChain) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChain.ProtoReflect.Descriptor instead.
func (*ProcChain) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{10}
}

func (x *ProcChain) GetUnique() string {
	if x != nil {
		return x.Unique
	}
	return ""
}

func (x *ProcChain) GetNodes() []*ProcChainNodeWithFile {
	if x != nil {
		return x.Nodes
	}
	return nil
}

type ProcNodeBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac       string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`                               // 主机唯一标识
	Pid       int64  `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`                              // 进程id
	StartTime int64  `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 进程启动时间，纳秒级时间戳
}

func (x *ProcNodeBean) Reset() {
	*x = ProcNodeBean{}
	mi := &file_yone_yone_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcNodeBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcNodeBean) ProtoMessage() {}

func (x *ProcNodeBean) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcNodeBean.ProtoReflect.Descriptor instead.
func (*ProcNodeBean) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{11}
}

func (x *ProcNodeBean) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *ProcNodeBean) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcNodeBean) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type ProcChainNode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unique          string `protobuf:"bytes,1,opt,name=unique,proto3" json:"unique,omitempty"`                                             // 进程唯一标识
	Pid             int64  `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`                                                  // 进程id
	StartTime       int64  `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                     // 进程启动时间
	Ppid            int64  `protobuf:"varint,4,opt,name=ppid,proto3" json:"ppid,omitempty"`                                                // 父进程id
	ParentStartTime int64  `protobuf:"varint,5,opt,name=parent_start_time,json=parentStartTime,proto3" json:"parent_start_time,omitempty"` // 父进程启动时间
	Epid            int64  `protobuf:"varint,6,opt,name=epid,proto3" json:"epid,omitempty"`                                                // exec 源进程 pid
	ExecStartTime   int64  `protobuf:"varint,7,opt,name=exec_start_time,json=execStartTime,proto3" json:"exec_start_time,omitempty"`       //  exec 源进程启动时间
	Name            string `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`                                                 // 进程名
	Command         string `protobuf:"bytes,9,opt,name=command,proto3" json:"command,omitempty"`                                           // 启动命令
	Username        string `protobuf:"bytes,10,opt,name=username,proto3" json:"username,omitempty"`                                        // 进程用户
	Euid            int64  `protobuf:"varint,11,opt,name=euid,proto3" json:"euid,omitempty"`                                               // 进程euid
	FileUnique      string `protobuf:"bytes,12,opt,name=file_unique,json=fileUnique,proto3" json:"file_unique,omitempty"`                  // 进程文件唯一标识
}

func (x *ProcChainNode) Reset() {
	*x = ProcChainNode{}
	mi := &file_yone_yone_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainNode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainNode) ProtoMessage() {}

func (x *ProcChainNode) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainNode.ProtoReflect.Descriptor instead.
func (*ProcChainNode) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{12}
}

func (x *ProcChainNode) GetUnique() string {
	if x != nil {
		return x.Unique
	}
	return ""
}

func (x *ProcChainNode) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcChainNode) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ProcChainNode) GetPpid() int64 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *ProcChainNode) GetParentStartTime() int64 {
	if x != nil {
		return x.ParentStartTime
	}
	return 0
}

func (x *ProcChainNode) GetEpid() int64 {
	if x != nil {
		return x.Epid
	}
	return 0
}

func (x *ProcChainNode) GetExecStartTime() int64 {
	if x != nil {
		return x.ExecStartTime
	}
	return 0
}

func (x *ProcChainNode) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProcChainNode) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *ProcChainNode) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ProcChainNode) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *ProcChainNode) GetFileUnique() string {
	if x != nil {
		return x.FileUnique
	}
	return ""
}

type ProcChainNodeWithFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unique          string         `protobuf:"bytes,1,opt,name=unique,proto3" json:"unique,omitempty"`                                             // 进程唯一标识
	Pid             int64          `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"`                                                  // 进程id
	StartTime       int64          `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                     // 进程启动时间
	Ppid            int64          `protobuf:"varint,4,opt,name=ppid,proto3" json:"ppid,omitempty"`                                                // 父进程id
	ParentStartTime int64          `protobuf:"varint,5,opt,name=parent_start_time,json=parentStartTime,proto3" json:"parent_start_time,omitempty"` // 父进程启动时间
	Epid            int64          `protobuf:"varint,6,opt,name=epid,proto3" json:"epid,omitempty"`                                                // exec 源进程 pid
	ExecStartTime   int64          `protobuf:"varint,7,opt,name=exec_start_time,json=execStartTime,proto3" json:"exec_start_time,omitempty"`       //  exec 源进程启动时间
	Name            string         `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`                                                 // 进程名
	Command         string         `protobuf:"bytes,9,opt,name=command,proto3" json:"command,omitempty"`                                           // 启动命令
	Username        string         `protobuf:"bytes,10,opt,name=username,proto3" json:"username,omitempty"`                                        // 进程用户
	Euid            int64          `protobuf:"varint,11,opt,name=euid,proto3" json:"euid,omitempty"`                                               // 进程euid
	FileInfo        *ProcChainFile `protobuf:"bytes,12,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`                        // 进程文件信息
}

func (x *ProcChainNodeWithFile) Reset() {
	*x = ProcChainNodeWithFile{}
	mi := &file_yone_yone_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainNodeWithFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainNodeWithFile) ProtoMessage() {}

func (x *ProcChainNodeWithFile) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainNodeWithFile.ProtoReflect.Descriptor instead.
func (*ProcChainNodeWithFile) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{13}
}

func (x *ProcChainNodeWithFile) GetUnique() string {
	if x != nil {
		return x.Unique
	}
	return ""
}

func (x *ProcChainNodeWithFile) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetPpid() int64 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetParentStartTime() int64 {
	if x != nil {
		return x.ParentStartTime
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetEpid() int64 {
	if x != nil {
		return x.Epid
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetExecStartTime() int64 {
	if x != nil {
		return x.ExecStartTime
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProcChainNodeWithFile) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *ProcChainNodeWithFile) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ProcChainNodeWithFile) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *ProcChainNodeWithFile) GetFileInfo() *ProcChainFile {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type ProcChainFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Unique      string `protobuf:"bytes,1,opt,name=unique,proto3" json:"unique,omitempty"`                               // 文件唯一标识，unique生成方式和历史逻辑想通: hex(sha256("mac:path:sha256")
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                   // 文件名
	Path        string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`                                   // 文件路径
	Md5         string `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`                                     // 文件md5
	Sha256      string `protobuf:"bytes,5,opt,name=sha256,proto3" json:"sha256,omitempty"`                               // 文件sha256
	Size        int64  `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`                                  // 文件大小
	Mtime       int64  `protobuf:"varint,7,opt,name=mtime,proto3" json:"mtime,omitempty"`                                // 更改时间
	Ctime       int64  `protobuf:"varint,8,opt,name=ctime,proto3" json:"ctime,omitempty"`                                // 创建时间
	Atime       int64  `protobuf:"varint,9,opt,name=atime,proto3" json:"atime,omitempty"`                                // 访问时间
	Permission  string `protobuf:"bytes,10,opt,name=permission,proto3" json:"permission,omitempty"`                      // 文件权限
	CompanyName string `protobuf:"bytes,11,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"` // 文件厂商名
}

func (x *ProcChainFile) Reset() {
	*x = ProcChainFile{}
	mi := &file_yone_yone_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcChainFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcChainFile) ProtoMessage() {}

func (x *ProcChainFile) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcChainFile.ProtoReflect.Descriptor instead.
func (*ProcChainFile) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{14}
}

func (x *ProcChainFile) GetUnique() string {
	if x != nil {
		return x.Unique
	}
	return ""
}

func (x *ProcChainFile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ProcChainFile) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ProcChainFile) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ProcChainFile) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *ProcChainFile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ProcChainFile) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *ProcChainFile) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *ProcChainFile) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *ProcChainFile) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *ProcChainFile) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

type OutreachSearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcIp     string `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`              // 源ip
	SrcPort   int32  `protobuf:"varint,2,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`       // 源端口
	DestIp    string `protobuf:"bytes,3,opt,name=dest_ip,json=destIp,proto3" json:"dest_ip,omitempty"`           // 目的ip
	DestPort  int32  `protobuf:"varint,4,opt,name=dest_port,json=destPort,proto3" json:"dest_port,omitempty"`    // 目的端口
	Protocol  string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`                     // 通信协议
	BeginTime int64  `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"` // 记录创建起始时间，纳秒级时间戳
	EndTime   int64  `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 记录创建终止时间，纳秒级时间戳
	Limit     int32  `protobuf:"varint,8,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *OutreachSearchReq) Reset() {
	*x = OutreachSearchReq{}
	mi := &file_yone_yone_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachSearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachSearchReq) ProtoMessage() {}

func (x *OutreachSearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachSearchReq.ProtoReflect.Descriptor instead.
func (*OutreachSearchReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{15}
}

func (x *OutreachSearchReq) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *OutreachSearchReq) GetSrcPort() int32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *OutreachSearchReq) GetDestIp() string {
	if x != nil {
		return x.DestIp
	}
	return ""
}

func (x *OutreachSearchReq) GetDestPort() int32 {
	if x != nil {
		return x.DestPort
	}
	return 0
}

func (x *OutreachSearchReq) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *OutreachSearchReq) GetBeginTime() int64 {
	if x != nil {
		return x.BeginTime
	}
	return 0
}

func (x *OutreachSearchReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *OutreachSearchReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type OutreachSearchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Outreaches []*OutreachInfo `protobuf:"bytes,1,rep,name=outreaches,proto3" json:"outreaches,omitempty"` // 外联信息列表
}

func (x *OutreachSearchResp) Reset() {
	*x = OutreachSearchResp{}
	mi := &file_yone_yone_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachSearchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachSearchResp) ProtoMessage() {}

func (x *OutreachSearchResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachSearchResp.ProtoReflect.Descriptor instead.
func (*OutreachSearchResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{16}
}

func (x *OutreachSearchResp) GetOutreaches() []*OutreachInfo {
	if x != nil {
		return x.Outreaches
	}
	return nil
}

type CreateFileTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5S    []string          `protobuf:"bytes,1,rep,name=md5s,proto3" json:"md5s,omitempty"`                                    // 需要创建检测任务的文件md5列表
	Engines []mq.DetectEngine `protobuf:"varint,2,rep,packed,name=engines,proto3,enum=mq.DetectEngine" json:"engines,omitempty"` // 需要哪些引擎进行检测
}

func (x *CreateFileTaskReq) Reset() {
	*x = CreateFileTaskReq{}
	mi := &file_yone_yone_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFileTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileTaskReq) ProtoMessage() {}

func (x *CreateFileTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileTaskReq.ProtoReflect.Descriptor instead.
func (*CreateFileTaskReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{17}
}

func (x *CreateFileTaskReq) GetMd5S() []string {
	if x != nil {
		return x.Md5S
	}
	return nil
}

func (x *CreateFileTaskReq) GetEngines() []mq.DetectEngine {
	if x != nil {
		return x.Engines
	}
	return nil
}

type CreateFileTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateFileTaskResp) Reset() {
	*x = CreateFileTaskResp{}
	mi := &file_yone_yone_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFileTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFileTaskResp) ProtoMessage() {}

func (x *CreateFileTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFileTaskResp.ProtoReflect.Descriptor instead.
func (*CreateFileTaskResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{18}
}

type GetFileTasksStateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5S []string `protobuf:"bytes,1,rep,name=md5s,proto3" json:"md5s,omitempty"` // 需要查询检测状态的文件md5列表
}

func (x *GetFileTasksStateReq) Reset() {
	*x = GetFileTasksStateReq{}
	mi := &file_yone_yone_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileTasksStateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileTasksStateReq) ProtoMessage() {}

func (x *GetFileTasksStateReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileTasksStateReq.ProtoReflect.Descriptor instead.
func (*GetFileTasksStateReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{19}
}

func (x *GetFileTasksStateReq) GetMd5S() []string {
	if x != nil {
		return x.Md5S
	}
	return nil
}

type GetFileTasksStateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*FileTaskState `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` // 各文件检测状态
}

func (x *GetFileTasksStateResp) Reset() {
	*x = GetFileTasksStateResp{}
	mi := &file_yone_yone_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileTasksStateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileTasksStateResp) ProtoMessage() {}

func (x *GetFileTasksStateResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileTasksStateResp.ProtoReflect.Descriptor instead.
func (*GetFileTasksStateResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{20}
}

func (x *GetFileTasksStateResp) GetItems() []*FileTaskState {
	if x != nil {
		return x.Items
	}
	return nil
}

type FileTaskState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5         string                `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256      string                `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Status      FileTaskStatus        `protobuf:"varint,3,opt,name=status,proto3,enum=yone.FileTaskStatus" json:"status,omitempty"`                                     // 任务检测状态
	Result      DetectResult          `protobuf:"varint,4,opt,name=result,proto3,enum=yone.DetectResult" json:"result,omitempty"`                                       // 任务检测结果
	Engines     []mq.DetectEngine     `protobuf:"varint,5,rep,packed,name=engines,proto3,enum=mq.DetectEngine" json:"engines,omitempty"`                                // 检测引擎列表
	EnginesClue []mq.ClueDetectEngine `protobuf:"varint,6,rep,packed,name=engines_clue,json=enginesClue,proto3,enum=mq.ClueDetectEngine" json:"engines_clue,omitempty"` // 基于线索的检测引擎列表
}

func (x *FileTaskState) Reset() {
	*x = FileTaskState{}
	mi := &file_yone_yone_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileTaskState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileTaskState) ProtoMessage() {}

func (x *FileTaskState) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileTaskState.ProtoReflect.Descriptor instead.
func (*FileTaskState) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{21}
}

func (x *FileTaskState) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileTaskState) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileTaskState) GetStatus() FileTaskStatus {
	if x != nil {
		return x.Status
	}
	return FileTaskStatus_TASK_STATUS_NONE
}

func (x *FileTaskState) GetResult() DetectResult {
	if x != nil {
		return x.Result
	}
	return DetectResult_DETECT_RESULT_INVALID
}

func (x *FileTaskState) GetEngines() []mq.DetectEngine {
	if x != nil {
		return x.Engines
	}
	return nil
}

func (x *FileTaskState) GetEnginesClue() []mq.ClueDetectEngine {
	if x != nil {
		return x.EnginesClue
	}
	return nil
}

type OutreachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcIp     string     `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`              // 源ip
	SrcPort   int32      `protobuf:"varint,2,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`       // 源端口
	DestIp    string     `protobuf:"bytes,3,opt,name=dest_ip,json=destIp,proto3" json:"dest_ip,omitempty"`           // 目的ip
	DestPort  int32      `protobuf:"varint,4,opt,name=dest_port,json=destPort,proto3" json:"dest_port,omitempty"`    // 目的端口
	Protocol  string     `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`                     // 通信协议
	Pid       int64      `protobuf:"varint,6,opt,name=pid,proto3" json:"pid,omitempty"`                              // 发起外联的进程pid
	StartTime int64      `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 发起外联的进程启动时间
	FoundTime int64      `protobuf:"varint,8,opt,name=found_time,json=foundTime,proto3" json:"found_time,omitempty"` // 客户端发现时间，纳秒级时间戳
	CreatedAt int64      `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"` // 记录创建时间，纳秒级时间戳
	HostId    string     `protobuf:"bytes,10,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`          // 主机唯一标识，即mac
	Hostname  string     `protobuf:"bytes,11,opt,name=hostname,proto3" json:"hostname,omitempty"`                    // 主机名
	HostIp    string     `protobuf:"bytes,12,opt,name=host_ip,json=hostIp,proto3" json:"host_ip,omitempty"`          // 主机ip
	HostOs    string     `protobuf:"bytes,13,opt,name=host_os,json=hostOs,proto3" json:"host_os,omitempty"`          // 主机操作系统
	HostGroup string     `protobuf:"bytes,14,opt,name=host_group,json=hostGroup,proto3" json:"host_group,omitempty"` // 主机分组名
	Chain     *ProcChain `protobuf:"bytes,15,opt,name=chain,proto3" json:"chain,omitempty"`                          // 进程链信息
}

func (x *OutreachInfo) Reset() {
	*x = OutreachInfo{}
	mi := &file_yone_yone_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachInfo) ProtoMessage() {}

func (x *OutreachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachInfo.ProtoReflect.Descriptor instead.
func (*OutreachInfo) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{22}
}

func (x *OutreachInfo) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *OutreachInfo) GetSrcPort() int32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *OutreachInfo) GetDestIp() string {
	if x != nil {
		return x.DestIp
	}
	return ""
}

func (x *OutreachInfo) GetDestPort() int32 {
	if x != nil {
		return x.DestPort
	}
	return 0
}

func (x *OutreachInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *OutreachInfo) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *OutreachInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *OutreachInfo) GetFoundTime() int64 {
	if x != nil {
		return x.FoundTime
	}
	return 0
}

func (x *OutreachInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *OutreachInfo) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *OutreachInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *OutreachInfo) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *OutreachInfo) GetHostOs() string {
	if x != nil {
		return x.HostOs
	}
	return ""
}

func (x *OutreachInfo) GetHostGroup() string {
	if x != nil {
		return x.HostGroup
	}
	return ""
}

func (x *OutreachInfo) GetChain() *ProcChain {
	if x != nil {
		return x.Chain
	}
	return nil
}

type ArchiveListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *ArchiveListFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page    *Page              `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
	Orderby string             `protobuf:"bytes,3,opt,name=orderby,proto3" json:"orderby,omitempty"`
}

func (x *ArchiveListReq) Reset() {
	*x = ArchiveListReq{}
	mi := &file_yone_yone_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveListReq) ProtoMessage() {}

func (x *ArchiveListReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveListReq.ProtoReflect.Descriptor instead.
func (*ArchiveListReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{23}
}

func (x *ArchiveListReq) GetFilter() *ArchiveListFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ArchiveListReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ArchiveListReq) GetOrderby() string {
	if x != nil {
		return x.Orderby
	}
	return ""
}

type ArchiveListFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchData    string       `protobuf:"bytes,1,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`                               // 文件md5，sha256或文件名
	DetectResult  DetectResult `protobuf:"varint,2,opt,name=detect_result,json=detectResult,proto3,enum=yone.DetectResult" json:"detect_result,omitempty"` // 检测结果
	TerminalStart int32        `protobuf:"varint,3,opt,name=terminal_start,json=terminalStart,proto3" json:"terminal_start,omitempty"`                     // 终端数最小值
	TerminalEnd   int32        `protobuf:"varint,4,opt,name=terminal_end,json=terminalEnd,proto3" json:"terminal_end,omitempty"`                           // 终端数最大值
}

func (x *ArchiveListFilter) Reset() {
	*x = ArchiveListFilter{}
	mi := &file_yone_yone_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveListFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveListFilter) ProtoMessage() {}

func (x *ArchiveListFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveListFilter.ProtoReflect.Descriptor instead.
func (*ArchiveListFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{24}
}

func (x *ArchiveListFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *ArchiveListFilter) GetDetectResult() DetectResult {
	if x != nil {
		return x.DetectResult
	}
	return DetectResult_DETECT_RESULT_INVALID
}

func (x *ArchiveListFilter) GetTerminalStart() int32 {
	if x != nil {
		return x.TerminalStart
	}
	return 0
}

func (x *ArchiveListFilter) GetTerminalEnd() int32 {
	if x != nil {
		return x.TerminalEnd
	}
	return 0
}

type Page struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int64 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize  int64 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *Page) Reset() {
	*x = Page{}
	mi := &file_yone_yone_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Page) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Page) ProtoMessage() {}

func (x *Page) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Page.ProtoReflect.Descriptor instead.
func (*Page) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{25}
}

func (x *Page) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *Page) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ArchiveListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*ArchiveItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64          `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *ArchiveListResp) Reset() {
	*x = ArchiveListResp{}
	mi := &file_yone_yone_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveListResp) ProtoMessage() {}

func (x *ArchiveListResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveListResp.ProtoReflect.Descriptor instead.
func (*ArchiveListResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{26}
}

func (x *ArchiveListResp) GetItems() []*ArchiveItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ArchiveListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ArchiveListResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type ArchiveItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5           string       `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256        string       `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Sha1          string       `protobuf:"bytes,3,opt,name=sha1,proto3" json:"sha1,omitempty"`
	FileName      string       `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileSize      string       `protobuf:"bytes,5,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileCreatedAt string       `protobuf:"bytes,6,opt,name=file_created_at,json=fileCreatedAt,proto3" json:"file_created_at,omitempty"`                    // 文件创建时间
	FileFoundAt   string       `protobuf:"bytes,7,opt,name=file_found_at,json=fileFoundAt,proto3" json:"file_found_at,omitempty"`                          // 文件鉴定时间
	DetectResult  DetectResult `protobuf:"varint,8,opt,name=detect_result,json=detectResult,proto3,enum=yone.DetectResult" json:"detect_result,omitempty"` // 文件检测结果
	TerminalCount int64        `protobuf:"varint,9,opt,name=terminal_count,json=terminalCount,proto3" json:"terminal_count,omitempty"`                     // 关联终端数量
}

func (x *ArchiveItem) Reset() {
	*x = ArchiveItem{}
	mi := &file_yone_yone_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveItem) ProtoMessage() {}

func (x *ArchiveItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveItem.ProtoReflect.Descriptor instead.
func (*ArchiveItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{27}
}

func (x *ArchiveItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ArchiveItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *ArchiveItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *ArchiveItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ArchiveItem) GetFileSize() string {
	if x != nil {
		return x.FileSize
	}
	return ""
}

func (x *ArchiveItem) GetFileCreatedAt() string {
	if x != nil {
		return x.FileCreatedAt
	}
	return ""
}

func (x *ArchiveItem) GetFileFoundAt() string {
	if x != nil {
		return x.FileFoundAt
	}
	return ""
}

func (x *ArchiveItem) GetDetectResult() DetectResult {
	if x != nil {
		return x.DetectResult
	}
	return DetectResult_DETECT_RESULT_INVALID
}

func (x *ArchiveItem) GetTerminalCount() int64 {
	if x != nil {
		return x.TerminalCount
	}
	return 0
}

type ArchiveStatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *conan.TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *ArchiveStatReq) Reset() {
	*x = ArchiveStatReq{}
	mi := &file_yone_yone_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveStatReq) ProtoMessage() {}

func (x *ArchiveStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveStatReq.ProtoReflect.Descriptor instead.
func (*ArchiveStatReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{28}
}

func (x *ArchiveStatReq) GetTimeRange() *conan.TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type ArchiveStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount  int64 `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`    // 数量总量
	DailyCount  int64 `protobuf:"varint,2,opt,name=daily_count,json=dailyCount,proto3" json:"daily_count,omitempty"`    // 今日上传量
	ServerCount int64 `protobuf:"varint,3,opt,name=server_count,json=serverCount,proto3" json:"server_count,omitempty"` // 终端发现数量
}

func (x *ArchiveStat) Reset() {
	*x = ArchiveStat{}
	mi := &file_yone_yone_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveStat) ProtoMessage() {}

func (x *ArchiveStat) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveStat.ProtoReflect.Descriptor instead.
func (*ArchiveStat) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{29}
}

func (x *ArchiveStat) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *ArchiveStat) GetDailyCount() int64 {
	if x != nil {
		return x.DailyCount
	}
	return 0
}

func (x *ArchiveStat) GetServerCount() int64 {
	if x != nil {
		return x.ServerCount
	}
	return 0
}

type ArchiveStatResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Black   *ArchiveStat `protobuf:"bytes,1,opt,name=black,proto3" json:"black,omitempty"`
	White   *ArchiveStat `protobuf:"bytes,2,opt,name=white,proto3" json:"white,omitempty"`
	Gray    *ArchiveStat `protobuf:"bytes,3,opt,name=gray,proto3" json:"gray,omitempty"`
	Unknown *ArchiveStat `protobuf:"bytes,4,opt,name=unknown,proto3" json:"unknown,omitempty"`
}

func (x *ArchiveStatResp) Reset() {
	*x = ArchiveStatResp{}
	mi := &file_yone_yone_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveStatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveStatResp) ProtoMessage() {}

func (x *ArchiveStatResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveStatResp.ProtoReflect.Descriptor instead.
func (*ArchiveStatResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{30}
}

func (x *ArchiveStatResp) GetBlack() *ArchiveStat {
	if x != nil {
		return x.Black
	}
	return nil
}

func (x *ArchiveStatResp) GetWhite() *ArchiveStat {
	if x != nil {
		return x.White
	}
	return nil
}

func (x *ArchiveStatResp) GetGray() *ArchiveStat {
	if x != nil {
		return x.Gray
	}
	return nil
}

func (x *ArchiveStatResp) GetUnknown() *ArchiveStat {
	if x != nil {
		return x.Unknown
	}
	return nil
}

type ArchiveInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5 string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
}

func (x *ArchiveInfoReq) Reset() {
	*x = ArchiveInfoReq{}
	mi := &file_yone_yone_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveInfoReq) ProtoMessage() {}

func (x *ArchiveInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveInfoReq.ProtoReflect.Descriptor instead.
func (*ArchiveInfoReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{31}
}

func (x *ArchiveInfoReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type ArchiveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5           string       `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256        string       `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Sha1          string       `protobuf:"bytes,3,opt,name=sha1,proto3" json:"sha1,omitempty"`
	FileName      string       `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType      string       `protobuf:"bytes,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize      string       `protobuf:"bytes,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileVendor    string       `protobuf:"bytes,7,opt,name=file_vendor,json=fileVendor,proto3" json:"file_vendor,omitempty"`
	FileVersion   string       `protobuf:"bytes,8,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`
	EventAt       string       `protobuf:"bytes,9,opt,name=event_at,json=eventAt,proto3" json:"event_at,omitempty"`
	DetectResult  DetectResult `protobuf:"varint,10,opt,name=detect_result,json=detectResult,proto3,enum=yone.DetectResult" json:"detect_result,omitempty"`
	TerminalCount int64        `protobuf:"varint,11,opt,name=terminal_count,json=terminalCount,proto3" json:"terminal_count,omitempty"`
	SignInfo      []*SignInfo  `protobuf:"bytes,12,rep,name=sign_info,json=signInfo,proto3" json:"sign_info,omitempty"`
}

func (x *ArchiveInfo) Reset() {
	*x = ArchiveInfo{}
	mi := &file_yone_yone_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArchiveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArchiveInfo) ProtoMessage() {}

func (x *ArchiveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArchiveInfo.ProtoReflect.Descriptor instead.
func (*ArchiveInfo) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{32}
}

func (x *ArchiveInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ArchiveInfo) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *ArchiveInfo) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *ArchiveInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ArchiveInfo) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *ArchiveInfo) GetFileSize() string {
	if x != nil {
		return x.FileSize
	}
	return ""
}

func (x *ArchiveInfo) GetFileVendor() string {
	if x != nil {
		return x.FileVendor
	}
	return ""
}

func (x *ArchiveInfo) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *ArchiveInfo) GetEventAt() string {
	if x != nil {
		return x.EventAt
	}
	return ""
}

func (x *ArchiveInfo) GetDetectResult() DetectResult {
	if x != nil {
		return x.DetectResult
	}
	return DetectResult_DETECT_RESULT_INVALID
}

func (x *ArchiveInfo) GetTerminalCount() int64 {
	if x != nil {
		return x.TerminalCount
	}
	return 0
}

func (x *ArchiveInfo) GetSignInfo() []*SignInfo {
	if x != nil {
		return x.SignInfo
	}
	return nil
}

// 文件签名信息
type SignInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial            string `protobuf:"bytes,1,opt,name=serial,proto3" json:"serial,omitempty"`                                                   // 签名序列号
	IssuerName        string `protobuf:"bytes,2,opt,name=issuer_name,json=issuerName,proto3" json:"issuer_name,omitempty"`                         // 签名颁发者
	Customer          string `protobuf:"bytes,3,opt,name=customer,proto3" json:"customer,omitempty"`                                               // 签名使用者
	Thumbprint        string `protobuf:"bytes,4,opt,name=thumbprint,proto3" json:"thumbprint,omitempty"`                                           // 签名指纹
	Result            string `protobuf:"bytes,5,opt,name=result,proto3" json:"result,omitempty"`                                                   // 签名状态
	Description       string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`                                         // 签名描述
	NotAfter          string `protobuf:"bytes,7,opt,name=not_after,json=notAfter,proto3" json:"not_after,omitempty"`                               // 签名有效期起始时间
	NotBefore         string `protobuf:"bytes,8,opt,name=not_before,json=notBefore,proto3" json:"not_before,omitempty"`                            // 签名有效期到期时间
	SignAlgorithm     string `protobuf:"bytes,9,opt,name=sign_algorithm,json=signAlgorithm,proto3" json:"sign_algorithm,omitempty"`                // 签名算法
	SignHashAlgorithm string `protobuf:"bytes,10,opt,name=sign_hash_algorithm,json=signHashAlgorithm,proto3" json:"sign_hash_algorithm,omitempty"` // 签名哈希算法
	Version           string `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`                                                // 签名版本
	SignStatusInfo    int32  `protobuf:"varint,12,opt,name=sign_status_info,json=signStatusInfo,proto3" json:"sign_status_info,omitempty"`         // 签名状态
}

func (x *SignInfo) Reset() {
	*x = SignInfo{}
	mi := &file_yone_yone_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInfo) ProtoMessage() {}

func (x *SignInfo) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInfo.ProtoReflect.Descriptor instead.
func (*SignInfo) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{33}
}

func (x *SignInfo) GetSerial() string {
	if x != nil {
		return x.Serial
	}
	return ""
}

func (x *SignInfo) GetIssuerName() string {
	if x != nil {
		return x.IssuerName
	}
	return ""
}

func (x *SignInfo) GetCustomer() string {
	if x != nil {
		return x.Customer
	}
	return ""
}

func (x *SignInfo) GetThumbprint() string {
	if x != nil {
		return x.Thumbprint
	}
	return ""
}

func (x *SignInfo) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *SignInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SignInfo) GetNotAfter() string {
	if x != nil {
		return x.NotAfter
	}
	return ""
}

func (x *SignInfo) GetNotBefore() string {
	if x != nil {
		return x.NotBefore
	}
	return ""
}

func (x *SignInfo) GetSignAlgorithm() string {
	if x != nil {
		return x.SignAlgorithm
	}
	return ""
}

func (x *SignInfo) GetSignHashAlgorithm() string {
	if x != nil {
		return x.SignHashAlgorithm
	}
	return ""
}

func (x *SignInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *SignInfo) GetSignStatusInfo() int32 {
	if x != nil {
		return x.SignStatusInfo
	}
	return 0
}

type FileHostListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *FileHostFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *FileHostListReq) Reset() {
	*x = FileHostListReq{}
	mi := &file_yone_yone_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHostListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHostListReq) ProtoMessage() {}

func (x *FileHostListReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHostListReq.ProtoReflect.Descriptor instead.
func (*FileHostListReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{34}
}

func (x *FileHostListReq) GetFilter() *FileHostFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FileHostListReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type FileHostFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5 string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
}

func (x *FileHostFilter) Reset() {
	*x = FileHostFilter{}
	mi := &file_yone_yone_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHostFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHostFilter) ProtoMessage() {}

func (x *FileHostFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHostFilter.ProtoReflect.Descriptor instead.
func (*FileHostFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{35}
}

func (x *FileHostFilter) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type FileHostListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*FileHostItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64           `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *FileHostListResp) Reset() {
	*x = FileHostListResp{}
	mi := &file_yone_yone_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHostListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHostListResp) ProtoMessage() {}

func (x *FileHostListResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHostListResp.ProtoReflect.Descriptor instead.
func (*FileHostListResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{36}
}

func (x *FileHostListResp) GetItems() []*FileHostItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FileHostListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FileHostListResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type FileHostItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5           string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256        string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Sha1          string `protobuf:"bytes,3,opt,name=sha1,proto3" json:"sha1,omitempty"`
	FileName      string `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType      string `protobuf:"bytes,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize      string `protobuf:"bytes,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FilePath      string `protobuf:"bytes,7,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileCreatedAt string `protobuf:"bytes,8,opt,name=file_created_at,json=fileCreatedAt,proto3" json:"file_created_at,omitempty"`
	FileFoundAt   string `protobuf:"bytes,9,opt,name=file_found_at,json=fileFoundAt,proto3" json:"file_found_at,omitempty"`
	Mac           string `protobuf:"bytes,10,opt,name=mac,proto3" json:"mac,omitempty"`
	Ip            string `protobuf:"bytes,11,opt,name=ip,proto3" json:"ip,omitempty"`
	Osver         int32  `protobuf:"varint,12,opt,name=osver,proto3" json:"osver,omitempty"`
	Os            string `protobuf:"bytes,13,opt,name=os,proto3" json:"os,omitempty"`
	HostName      string `protobuf:"bytes,14,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
	GroupId       int64  `protobuf:"varint,15,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupName     string `protobuf:"bytes,16,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Online        int32  `protobuf:"varint,17,opt,name=online,proto3" json:"online,omitempty"`
}

func (x *FileHostItem) Reset() {
	*x = FileHostItem{}
	mi := &file_yone_yone_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHostItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHostItem) ProtoMessage() {}

func (x *FileHostItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHostItem.ProtoReflect.Descriptor instead.
func (*FileHostItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{37}
}

func (x *FileHostItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileHostItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileHostItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *FileHostItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileHostItem) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FileHostItem) GetFileSize() string {
	if x != nil {
		return x.FileSize
	}
	return ""
}

func (x *FileHostItem) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileHostItem) GetFileCreatedAt() string {
	if x != nil {
		return x.FileCreatedAt
	}
	return ""
}

func (x *FileHostItem) GetFileFoundAt() string {
	if x != nil {
		return x.FileFoundAt
	}
	return ""
}

func (x *FileHostItem) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *FileHostItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FileHostItem) GetOsver() int32 {
	if x != nil {
		return x.Osver
	}
	return 0
}

func (x *FileHostItem) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *FileHostItem) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *FileHostItem) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *FileHostItem) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *FileHostItem) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

type HostViewReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *HostViewFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *HostViewReq) Reset() {
	*x = HostViewReq{}
	mi := &file_yone_yone_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostViewReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostViewReq) ProtoMessage() {}

func (x *HostViewReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostViewReq.ProtoReflect.Descriptor instead.
func (*HostViewReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{38}
}

func (x *HostViewReq) GetFilter() *HostViewFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *HostViewReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type HostViewFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip             string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	FileCountStart int64  `protobuf:"varint,2,opt,name=file_count_start,json=fileCountStart,proto3" json:"file_count_start,omitempty"`
	FileCountEnd   int32  `protobuf:"varint,3,opt,name=file_count_end,json=fileCountEnd,proto3" json:"file_count_end,omitempty"`
}

func (x *HostViewFilter) Reset() {
	*x = HostViewFilter{}
	mi := &file_yone_yone_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostViewFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostViewFilter) ProtoMessage() {}

func (x *HostViewFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostViewFilter.ProtoReflect.Descriptor instead.
func (*HostViewFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{39}
}

func (x *HostViewFilter) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *HostViewFilter) GetFileCountStart() int64 {
	if x != nil {
		return x.FileCountStart
	}
	return 0
}

func (x *HostViewFilter) GetFileCountEnd() int32 {
	if x != nil {
		return x.FileCountEnd
	}
	return 0
}

type HostViewResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*HostViewItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64           `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *HostViewResp) Reset() {
	*x = HostViewResp{}
	mi := &file_yone_yone_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostViewResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostViewResp) ProtoMessage() {}

func (x *HostViewResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostViewResp.ProtoReflect.Descriptor instead.
func (*HostViewResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{40}
}

func (x *HostViewResp) GetItems() []*HostViewItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *HostViewResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *HostViewResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type HostViewItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip        string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Osver     int32  `protobuf:"varint,2,opt,name=osver,proto3" json:"osver,omitempty"`
	Os        string `protobuf:"bytes,3,opt,name=os,proto3" json:"os,omitempty"`
	HostName  string `protobuf:"bytes,4,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
	GroupId   int64  `protobuf:"varint,5,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`
	GroupName string `protobuf:"bytes,6,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	Mac       string `protobuf:"bytes,7,opt,name=mac,proto3" json:"mac,omitempty"`
	FileCount int64  `protobuf:"varint,8,opt,name=file_count,json=fileCount,proto3" json:"file_count,omitempty"`
	Online    int32  `protobuf:"varint,9,opt,name=online,proto3" json:"online,omitempty"`
}

func (x *HostViewItem) Reset() {
	*x = HostViewItem{}
	mi := &file_yone_yone_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostViewItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostViewItem) ProtoMessage() {}

func (x *HostViewItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostViewItem.ProtoReflect.Descriptor instead.
func (*HostViewItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{41}
}

func (x *HostViewItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *HostViewItem) GetOsver() int32 {
	if x != nil {
		return x.Osver
	}
	return 0
}

func (x *HostViewItem) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *HostViewItem) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *HostViewItem) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *HostViewItem) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *HostViewItem) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *HostViewItem) GetFileCount() int64 {
	if x != nil {
		return x.FileCount
	}
	return 0
}

func (x *HostViewItem) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

type HostStatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Macs []string `protobuf:"bytes,1,rep,name=macs,proto3" json:"macs,omitempty"`
}

func (x *HostStatReq) Reset() {
	*x = HostStatReq{}
	mi := &file_yone_yone_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostStatReq) ProtoMessage() {}

func (x *HostStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostStatReq.ProtoReflect.Descriptor instead.
func (*HostStatReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{42}
}

func (x *HostStatReq) GetMacs() []string {
	if x != nil {
		return x.Macs
	}
	return nil
}

type HostStatResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hosts []*HostFileStat `protobuf:"bytes,1,rep,name=hosts,proto3" json:"hosts,omitempty"`
}

func (x *HostStatResp) Reset() {
	*x = HostStatResp{}
	mi := &file_yone_yone_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostStatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostStatResp) ProtoMessage() {}

func (x *HostStatResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostStatResp.ProtoReflect.Descriptor instead.
func (*HostStatResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{43}
}

func (x *HostStatResp) GetHosts() []*HostFileStat {
	if x != nil {
		return x.Hosts
	}
	return nil
}

type HostFileStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac   string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`      // 主机唯一标识
	Count int64  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` // 文件总数
}

func (x *HostFileStat) Reset() {
	*x = HostFileStat{}
	mi := &file_yone_yone_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostFileStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostFileStat) ProtoMessage() {}

func (x *HostFileStat) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostFileStat.ProtoReflect.Descriptor instead.
func (*HostFileStat) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{44}
}

func (x *HostFileStat) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *HostFileStat) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type HostFileListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *HostFileFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *HostFileListReq) Reset() {
	*x = HostFileListReq{}
	mi := &file_yone_yone_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostFileListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostFileListReq) ProtoMessage() {}

func (x *HostFileListReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostFileListReq.ProtoReflect.Descriptor instead.
func (*HostFileListReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{45}
}

func (x *HostFileListReq) GetFilter() *HostFileFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *HostFileListReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type HostFileFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac      string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`                           // 主机唯一标识
	FileName string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"` // 文件名
	Md5      string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`                           // 文件md5
}

func (x *HostFileFilter) Reset() {
	*x = HostFileFilter{}
	mi := &file_yone_yone_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostFileFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostFileFilter) ProtoMessage() {}

func (x *HostFileFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostFileFilter.ProtoReflect.Descriptor instead.
func (*HostFileFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{46}
}

func (x *HostFileFilter) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *HostFileFilter) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *HostFileFilter) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type HostFileListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*FileItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64       `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *HostFileListResp) Reset() {
	*x = HostFileListResp{}
	mi := &file_yone_yone_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostFileListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostFileListResp) ProtoMessage() {}

func (x *HostFileListResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostFileListResp.ProtoReflect.Descriptor instead.
func (*HostFileListResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{47}
}

func (x *HostFileListResp) GetItems() []*FileItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *HostFileListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *HostFileListResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type FileItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5           string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256        string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Sha1          string `protobuf:"bytes,3,opt,name=sha1,proto3" json:"sha1,omitempty"`
	FileName      string `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType      string `protobuf:"bytes,5,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize      string `protobuf:"bytes,6,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileCreatedAt string `protobuf:"bytes,7,opt,name=file_created_at,json=fileCreatedAt,proto3" json:"file_created_at,omitempty"` // 文件创建时间
	FileFoundAt   string `protobuf:"bytes,8,opt,name=file_found_at,json=fileFoundAt,proto3" json:"file_found_at,omitempty"`       // 文件鉴定时间
}

func (x *FileItem) Reset() {
	*x = FileItem{}
	mi := &file_yone_yone_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileItem) ProtoMessage() {}

func (x *FileItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileItem.ProtoReflect.Descriptor instead.
func (*FileItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{48}
}

func (x *FileItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *FileItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileItem) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FileItem) GetFileSize() string {
	if x != nil {
		return x.FileSize
	}
	return ""
}

func (x *FileItem) GetFileCreatedAt() string {
	if x != nil {
		return x.FileCreatedAt
	}
	return ""
}

func (x *FileItem) GetFileFoundAt() string {
	if x != nil {
		return x.FileFoundAt
	}
	return ""
}

type FileSearchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchData string `protobuf:"bytes,1,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"` // 文件md5,sha256,sha1或文件名
}

func (x *FileSearchReq) Reset() {
	*x = FileSearchReq{}
	mi := &file_yone_yone_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSearchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSearchReq) ProtoMessage() {}

func (x *FileSearchReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSearchReq.ProtoReflect.Descriptor instead.
func (*FileSearchReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{49}
}

func (x *FileSearchReq) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

type FileSearchResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5 string `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
}

func (x *FileSearchResp) Reset() {
	*x = FileSearchResp{}
	mi := &file_yone_yone_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSearchResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSearchResp) ProtoMessage() {}

func (x *FileSearchResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSearchResp.ProtoReflect.Descriptor instead.
func (*FileSearchResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{50}
}

func (x *FileSearchResp) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type OutreachDailyStatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *conan.TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *OutreachDailyStatReq) Reset() {
	*x = OutreachDailyStatReq{}
	mi := &file_yone_yone_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachDailyStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachDailyStatReq) ProtoMessage() {}

func (x *OutreachDailyStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachDailyStatReq.ProtoReflect.Descriptor instead.
func (*OutreachDailyStatReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{51}
}

func (x *OutreachDailyStatReq) GetTimeRange() *conan.TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type OutreachDailyStatResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*OutreachDailyStat `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *OutreachDailyStatResp) Reset() {
	*x = OutreachDailyStatResp{}
	mi := &file_yone_yone_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachDailyStatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachDailyStatResp) ProtoMessage() {}

func (x *OutreachDailyStatResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachDailyStatResp.ProtoReflect.Descriptor instead.
func (*OutreachDailyStatResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{52}
}

func (x *OutreachDailyStatResp) GetItems() []*OutreachDailyStat {
	if x != nil {
		return x.Items
	}
	return nil
}

type OutreachDailyStat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day   string `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"` // format example: 2025-01-01
	Count int64  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *OutreachDailyStat) Reset() {
	*x = OutreachDailyStat{}
	mi := &file_yone_yone_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachDailyStat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachDailyStat) ProtoMessage() {}

func (x *OutreachDailyStat) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachDailyStat.ProtoReflect.Descriptor instead.
func (*OutreachDailyStat) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{53}
}

func (x *OutreachDailyStat) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *OutreachDailyStat) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type OutreachTotalReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *conan.TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *OutreachTotalReq) Reset() {
	*x = OutreachTotalReq{}
	mi := &file_yone_yone_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachTotalReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachTotalReq) ProtoMessage() {}

func (x *OutreachTotalReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachTotalReq.ProtoReflect.Descriptor instead.
func (*OutreachTotalReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{54}
}

func (x *OutreachTotalReq) GetTimeRange() *conan.TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type OutreachTotalResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *OutreachTotalResp) Reset() {
	*x = OutreachTotalResp{}
	mi := &file_yone_yone_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachTotalResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachTotalResp) ProtoMessage() {}

func (x *OutreachTotalResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachTotalResp.ProtoReflect.Descriptor instead.
func (*OutreachTotalResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{55}
}

func (x *OutreachTotalResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type LogFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Os         OsType `protobuf:"varint,1,opt,name=os,proto3,enum=yone.OsType" json:"os,omitempty"`                 // 系统类型 1:windows 2:linux
	DateRange  string `protobuf:"bytes,2,opt,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`    // 时间范围筛选
	SearchData string `protobuf:"bytes,3,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"` // 检索数据
}

func (x *LogFilter) Reset() {
	*x = LogFilter{}
	mi := &file_yone_yone_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogFilter) ProtoMessage() {}

func (x *LogFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogFilter.ProtoReflect.Descriptor instead.
func (*LogFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{56}
}

func (x *LogFilter) GetOs() OsType {
	if x != nil {
		return x.Os
	}
	return OsType_OS_TYPE_UNKNOWN
}

func (x *LogFilter) GetDateRange() string {
	if x != nil {
		return x.DateRange
	}
	return ""
}

func (x *LogFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

type OutreachLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *LogFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page      `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *OutreachLogReq) Reset() {
	*x = OutreachLogReq{}
	mi := &file_yone_yone_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachLogReq) ProtoMessage() {}

func (x *OutreachLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachLogReq.ProtoReflect.Descriptor instead.
func (*OutreachLogReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{57}
}

func (x *OutreachLogReq) GetFilter() *LogFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *OutreachLogReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type OutreachLogResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*OutreachLogItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64              `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *OutreachLogResp) Reset() {
	*x = OutreachLogResp{}
	mi := &file_yone_yone_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachLogResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachLogResp) ProtoMessage() {}

func (x *OutreachLogResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachLogResp.ProtoReflect.Descriptor instead.
func (*OutreachLogResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{58}
}

func (x *OutreachLogResp) GetItems() []*OutreachLogItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *OutreachLogResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *OutreachLogResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type OutreachLogItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"` // 终端id
	Ip          string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                // 终端ip
	Hostname    string `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname,omitempty"`                    // 终端主机名称
	Os          int32  `protobuf:"varint,4,opt,name=os,proto3" json:"os,omitempty"`                               // 终端操作系统
	Pid         int64  `protobuf:"varint,5,opt,name=pid,proto3" json:"pid,omitempty"`                             // 外联进程pid
	Pname       string `protobuf:"bytes,6,opt,name=pname,proto3" json:"pname,omitempty"`                          // 外联进程名
	Ppid        int64  `protobuf:"varint,7,opt,name=ppid,proto3" json:"ppid,omitempty"`
	Ppname      string `protobuf:"bytes,8,opt,name=ppname,proto3" json:"ppname,omitempty"`
	Epid        int64  `protobuf:"varint,9,opt,name=epid,proto3" json:"epid,omitempty"`
	User        string `protobuf:"bytes,10,opt,name=user,proto3" json:"user,omitempty"` // 进程用户名
	Euid        int64  `protobuf:"varint,11,opt,name=euid,proto3" json:"euid,omitempty"`
	Cmd         string `protobuf:"bytes,12,opt,name=cmd,proto3" json:"cmd,omitempty"`
	StartAt     string `protobuf:"bytes,13,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	Path        string `protobuf:"bytes,14,opt,name=path,proto3" json:"path,omitempty"`                                  // 程序路径
	Md5         string `protobuf:"bytes,15,opt,name=md5,proto3" json:"md5,omitempty"`                                    // 程序md5
	Sha256      string `protobuf:"bytes,16,opt,name=sha256,proto3" json:"sha256,omitempty"`                              // 程序sha256
	Sha1        string `protobuf:"bytes,17,opt,name=sha1,proto3" json:"sha1,omitempty"`                                  // 程序sha1
	Size        string `protobuf:"bytes,18,opt,name=size,proto3" json:"size,omitempty"`                                  // 程序大小
	Ctime       string `protobuf:"bytes,19,opt,name=ctime,proto3" json:"ctime,omitempty"`                                // 文件创建时间
	Mtime       string `protobuf:"bytes,20,opt,name=mtime,proto3" json:"mtime,omitempty"`                                // 文件修改时间
	Atime       string `protobuf:"bytes,21,opt,name=atime,proto3" json:"atime,omitempty"`                                // 文件访问时间
	Attrs       string `protobuf:"bytes,22,opt,name=attrs,proto3" json:"attrs,omitempty"`                                // 文件属性
	Signs       string `protobuf:"bytes,23,opt,name=signs,proto3" json:"signs,omitempty"`                                // 文件签名信息
	Uuid        string `protobuf:"bytes,24,opt,name=uuid,proto3" json:"uuid,omitempty"`                                  // 日志唯一标识
	SourceIp    string `protobuf:"bytes,25,opt,name=source_ip,json=sourceIp,proto3" json:"source_ip,omitempty"`          // 源ip
	SourcePort  int32  `protobuf:"varint,26,opt,name=source_port,json=sourcePort,proto3" json:"source_port,omitempty"`   // 源端口
	RemoteIp    string `protobuf:"bytes,27,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`          // 目的ip
	RemotePort  int32  `protobuf:"varint,28,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`   // 目的端口
	Protocol    string `protobuf:"bytes,29,opt,name=protocol,proto3" json:"protocol,omitempty"`                          // 通信协议
	TriggerTime string `protobuf:"bytes,30,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"` // 发现时间
	CreateAt    string `protobuf:"bytes,31,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`          // 记录创建时间
}

func (x *OutreachLogItem) Reset() {
	*x = OutreachLogItem{}
	mi := &file_yone_yone_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachLogItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachLogItem) ProtoMessage() {}

func (x *OutreachLogItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachLogItem.ProtoReflect.Descriptor instead.
func (*OutreachLogItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{59}
}

func (x *OutreachLogItem) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *OutreachLogItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *OutreachLogItem) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *OutreachLogItem) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *OutreachLogItem) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *OutreachLogItem) GetPname() string {
	if x != nil {
		return x.Pname
	}
	return ""
}

func (x *OutreachLogItem) GetPpid() int64 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *OutreachLogItem) GetPpname() string {
	if x != nil {
		return x.Ppname
	}
	return ""
}

func (x *OutreachLogItem) GetEpid() int64 {
	if x != nil {
		return x.Epid
	}
	return 0
}

func (x *OutreachLogItem) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *OutreachLogItem) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *OutreachLogItem) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *OutreachLogItem) GetStartAt() string {
	if x != nil {
		return x.StartAt
	}
	return ""
}

func (x *OutreachLogItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *OutreachLogItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *OutreachLogItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *OutreachLogItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *OutreachLogItem) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *OutreachLogItem) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *OutreachLogItem) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *OutreachLogItem) GetAtime() string {
	if x != nil {
		return x.Atime
	}
	return ""
}

func (x *OutreachLogItem) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *OutreachLogItem) GetSigns() string {
	if x != nil {
		return x.Signs
	}
	return ""
}

func (x *OutreachLogItem) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *OutreachLogItem) GetSourceIp() string {
	if x != nil {
		return x.SourceIp
	}
	return ""
}

func (x *OutreachLogItem) GetSourcePort() int32 {
	if x != nil {
		return x.SourcePort
	}
	return 0
}

func (x *OutreachLogItem) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *OutreachLogItem) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *OutreachLogItem) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *OutreachLogItem) GetTriggerTime() string {
	if x != nil {
		return x.TriggerTime
	}
	return ""
}

func (x *OutreachLogItem) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

type FileCreateLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *LogFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page      `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *FileCreateLogReq) Reset() {
	*x = FileCreateLogReq{}
	mi := &file_yone_yone_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileCreateLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileCreateLogReq) ProtoMessage() {}

func (x *FileCreateLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileCreateLogReq.ProtoReflect.Descriptor instead.
func (*FileCreateLogReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{60}
}

func (x *FileCreateLogReq) GetFilter() *LogFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FileCreateLogReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type FileCreateLogResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*FileCreateLogItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64                `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *FileCreateLogResp) Reset() {
	*x = FileCreateLogResp{}
	mi := &file_yone_yone_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileCreateLogResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileCreateLogResp) ProtoMessage() {}

func (x *FileCreateLogResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileCreateLogResp.ProtoReflect.Descriptor instead.
func (*FileCreateLogResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{61}
}

func (x *FileCreateLogResp) GetItems() []*FileCreateLogItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FileCreateLogResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FileCreateLogResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type FileCreateLogItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"` // 终端id
	Ip          string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                // 终端ip
	Hostname    string `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname,omitempty"`                    // 终端主机名称
	Os          int32  `protobuf:"varint,4,opt,name=os,proto3" json:"os,omitempty"`                               // 终端操作系统
	Pid         int64  `protobuf:"varint,5,opt,name=pid,proto3" json:"pid,omitempty"`                             // 外联进程pid
	StartAt     string `protobuf:"bytes,6,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	Path        string `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`                                   // 程序路径
	Md5         string `protobuf:"bytes,8,opt,name=md5,proto3" json:"md5,omitempty"`                                     // 程序md5
	Sha256      string `protobuf:"bytes,9,opt,name=sha256,proto3" json:"sha256,omitempty"`                               // 程序sha256
	Sha1        string `protobuf:"bytes,10,opt,name=sha1,proto3" json:"sha1,omitempty"`                                  // 程序sha1
	Size        string `protobuf:"bytes,11,opt,name=size,proto3" json:"size,omitempty"`                                  // 程序大小
	Ctime       string `protobuf:"bytes,12,opt,name=ctime,proto3" json:"ctime,omitempty"`                                // 文件创建时间
	Mtime       string `protobuf:"bytes,14,opt,name=mtime,proto3" json:"mtime,omitempty"`                                // 文件修改时间
	Atime       string `protobuf:"bytes,15,opt,name=atime,proto3" json:"atime,omitempty"`                                // 文件访问时间
	Attrs       string `protobuf:"bytes,16,opt,name=attrs,proto3" json:"attrs,omitempty"`                                // 文件属性
	Signs       string `protobuf:"bytes,17,opt,name=signs,proto3" json:"signs,omitempty"`                                // 文件签名信息
	Permission  string `protobuf:"bytes,18,opt,name=permission,proto3" json:"permission,omitempty"`                      // 文件权限信息
	UniqueKey   string `protobuf:"bytes,19,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`       // 文件唯一标识
	TriggerTime string `protobuf:"bytes,20,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"` // 发现时间
	CreateAt    string `protobuf:"bytes,21,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`          // 记录创建时间
}

func (x *FileCreateLogItem) Reset() {
	*x = FileCreateLogItem{}
	mi := &file_yone_yone_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileCreateLogItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileCreateLogItem) ProtoMessage() {}

func (x *FileCreateLogItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileCreateLogItem.ProtoReflect.Descriptor instead.
func (*FileCreateLogItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{62}
}

func (x *FileCreateLogItem) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *FileCreateLogItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *FileCreateLogItem) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *FileCreateLogItem) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *FileCreateLogItem) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *FileCreateLogItem) GetStartAt() string {
	if x != nil {
		return x.StartAt
	}
	return ""
}

func (x *FileCreateLogItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FileCreateLogItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileCreateLogItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileCreateLogItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *FileCreateLogItem) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *FileCreateLogItem) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *FileCreateLogItem) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *FileCreateLogItem) GetAtime() string {
	if x != nil {
		return x.Atime
	}
	return ""
}

func (x *FileCreateLogItem) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *FileCreateLogItem) GetSigns() string {
	if x != nil {
		return x.Signs
	}
	return ""
}

func (x *FileCreateLogItem) GetPermission() string {
	if x != nil {
		return x.Permission
	}
	return ""
}

func (x *FileCreateLogItem) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *FileCreateLogItem) GetTriggerTime() string {
	if x != nil {
		return x.TriggerTime
	}
	return ""
}

func (x *FileCreateLogItem) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

type ProcessLogReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *LogFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page      `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ProcessLogReq) Reset() {
	*x = ProcessLogReq{}
	mi := &file_yone_yone_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessLogReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLogReq) ProtoMessage() {}

func (x *ProcessLogReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLogReq.ProtoReflect.Descriptor instead.
func (*ProcessLogReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{63}
}

func (x *ProcessLogReq) GetFilter() *LogFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ProcessLogReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type ProcessLogResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*ProcessLogItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64             `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *ProcessLogResp) Reset() {
	*x = ProcessLogResp{}
	mi := &file_yone_yone_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessLogResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLogResp) ProtoMessage() {}

func (x *ProcessLogResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLogResp.ProtoReflect.Descriptor instead.
func (*ProcessLogResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{64}
}

func (x *ProcessLogResp) GetItems() []*ProcessLogItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ProcessLogResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ProcessLogResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type ProcessLogItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"` // 终端id
	Ip          string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                // 终端ip
	Hostname    string `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname,omitempty"`                    // 终端主机名称
	Os          int32  `protobuf:"varint,4,opt,name=os,proto3" json:"os,omitempty"`                               // 终端操作系统
	Ppname      string `protobuf:"bytes,5,opt,name=ppname,proto3" json:"ppname,omitempty"`
	Ppid        int64  `protobuf:"varint,6,opt,name=ppid,proto3" json:"ppid,omitempty"`
	Epid        int64  `protobuf:"varint,7,opt,name=epid,proto3" json:"epid,omitempty"`
	Pname       string `protobuf:"bytes,8,opt,name=pname,proto3" json:"pname,omitempty"`
	Pid         int64  `protobuf:"varint,9,opt,name=pid,proto3" json:"pid,omitempty"`
	User        string `protobuf:"bytes,10,opt,name=user,proto3" json:"user,omitempty"`
	Euid        int64  `protobuf:"varint,11,opt,name=euid,proto3" json:"euid,omitempty"`
	Cmd         string `protobuf:"bytes,12,opt,name=cmd,proto3" json:"cmd,omitempty"`
	StartAt     string `protobuf:"bytes,13,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	Path        string `protobuf:"bytes,14,opt,name=path,proto3" json:"path,omitempty"`                                  // 程序路径
	Md5         string `protobuf:"bytes,15,opt,name=md5,proto3" json:"md5,omitempty"`                                    // 程序md5
	Sha256      string `protobuf:"bytes,16,opt,name=sha256,proto3" json:"sha256,omitempty"`                              // 程序sha256
	Sha1        string `protobuf:"bytes,17,opt,name=sha1,proto3" json:"sha1,omitempty"`                                  // 程序sha1
	Size        string `protobuf:"bytes,18,opt,name=size,proto3" json:"size,omitempty"`                                  // 程序大小
	Ctime       string `protobuf:"bytes,19,opt,name=ctime,proto3" json:"ctime,omitempty"`                                // 文件创建时间
	Mtime       string `protobuf:"bytes,20,opt,name=mtime,proto3" json:"mtime,omitempty"`                                // 文件修改时间
	Atime       string `protobuf:"bytes,21,opt,name=atime,proto3" json:"atime,omitempty"`                                // 文件访问时间
	Attrs       string `protobuf:"bytes,22,opt,name=attrs,proto3" json:"attrs,omitempty"`                                // 文件属性
	Signs       string `protobuf:"bytes,23,opt,name=signs,proto3" json:"signs,omitempty"`                                // 文件签名信息
	UniqueKey   string `protobuf:"bytes,24,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`       // 文件唯一标识
	TriggerTime string `protobuf:"bytes,25,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"` // 发现时间
	CreateAt    string `protobuf:"bytes,26,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`          // 记录创建时间
}

func (x *ProcessLogItem) Reset() {
	*x = ProcessLogItem{}
	mi := &file_yone_yone_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessLogItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessLogItem) ProtoMessage() {}

func (x *ProcessLogItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessLogItem.ProtoReflect.Descriptor instead.
func (*ProcessLogItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{65}
}

func (x *ProcessLogItem) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *ProcessLogItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ProcessLogItem) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *ProcessLogItem) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *ProcessLogItem) GetPpname() string {
	if x != nil {
		return x.Ppname
	}
	return ""
}

func (x *ProcessLogItem) GetPpid() int64 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *ProcessLogItem) GetEpid() int64 {
	if x != nil {
		return x.Epid
	}
	return 0
}

func (x *ProcessLogItem) GetPname() string {
	if x != nil {
		return x.Pname
	}
	return ""
}

func (x *ProcessLogItem) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcessLogItem) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *ProcessLogItem) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *ProcessLogItem) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *ProcessLogItem) GetStartAt() string {
	if x != nil {
		return x.StartAt
	}
	return ""
}

func (x *ProcessLogItem) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ProcessLogItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ProcessLogItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *ProcessLogItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *ProcessLogItem) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *ProcessLogItem) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

func (x *ProcessLogItem) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *ProcessLogItem) GetAtime() string {
	if x != nil {
		return x.Atime
	}
	return ""
}

func (x *ProcessLogItem) GetAttrs() string {
	if x != nil {
		return x.Attrs
	}
	return ""
}

func (x *ProcessLogItem) GetSigns() string {
	if x != nil {
		return x.Signs
	}
	return ""
}

func (x *ProcessLogItem) GetUniqueKey() string {
	if x != nil {
		return x.UniqueKey
	}
	return ""
}

func (x *ProcessLogItem) GetTriggerTime() string {
	if x != nil {
		return x.TriggerTime
	}
	return ""
}

func (x *ProcessLogItem) GetCreateAt() string {
	if x != nil {
		return x.CreateAt
	}
	return ""
}

type SearchSchemas struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessLinux      []*SearchField `protobuf:"bytes,1,rep,name=process_linux,json=processLinux,proto3" json:"process_linux,omitempty"`
	ProcessWindows    []*SearchField `protobuf:"bytes,2,rep,name=process_windows,json=processWindows,proto3" json:"process_windows,omitempty"`
	OutreachLinux     []*SearchField `protobuf:"bytes,3,rep,name=outreach_linux,json=outreachLinux,proto3" json:"outreach_linux,omitempty"`
	OutreachWindows   []*SearchField `protobuf:"bytes,4,rep,name=outreach_windows,json=outreachWindows,proto3" json:"outreach_windows,omitempty"`
	FilecreateLinux   []*SearchField `protobuf:"bytes,5,rep,name=filecreate_linux,json=filecreateLinux,proto3" json:"filecreate_linux,omitempty"`
	FilecreateWindows []*SearchField `protobuf:"bytes,6,rep,name=filecreate_windows,json=filecreateWindows,proto3" json:"filecreate_windows,omitempty"`
}

func (x *SearchSchemas) Reset() {
	*x = SearchSchemas{}
	mi := &file_yone_yone_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchSchemas) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchSchemas) ProtoMessage() {}

func (x *SearchSchemas) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchSchemas.ProtoReflect.Descriptor instead.
func (*SearchSchemas) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{66}
}

func (x *SearchSchemas) GetProcessLinux() []*SearchField {
	if x != nil {
		return x.ProcessLinux
	}
	return nil
}

func (x *SearchSchemas) GetProcessWindows() []*SearchField {
	if x != nil {
		return x.ProcessWindows
	}
	return nil
}

func (x *SearchSchemas) GetOutreachLinux() []*SearchField {
	if x != nil {
		return x.OutreachLinux
	}
	return nil
}

func (x *SearchSchemas) GetOutreachWindows() []*SearchField {
	if x != nil {
		return x.OutreachWindows
	}
	return nil
}

func (x *SearchSchemas) GetFilecreateLinux() []*SearchField {
	if x != nil {
		return x.FilecreateLinux
	}
	return nil
}

func (x *SearchSchemas) GetFilecreateWindows() []*SearchField {
	if x != nil {
		return x.FilecreateWindows
	}
	return nil
}

type SearchField struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Field       string  `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`                 // 字段名, 用于搜索条件中
	Label       string  `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`                 // 字段标签, 仅用于显示
	Type        int32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`                  // 字段值类型, 限制字段可比较的数据类型
	Placeholder string  `protobuf:"bytes,4,opt,name=placeholder,proto3" json:"placeholder,omitempty"`     // 字段占位符, 用于输入提示
	Operators   []int32 `protobuf:"varint,5,rep,packed,name=operators,proto3" json:"operators,omitempty"` // 字段操作符, 限制可用的比较方式
}

func (x *SearchField) Reset() {
	*x = SearchField{}
	mi := &file_yone_yone_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchField) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchField) ProtoMessage() {}

func (x *SearchField) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchField.ProtoReflect.Descriptor instead.
func (*SearchField) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{67}
}

func (x *SearchField) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SearchField) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *SearchField) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SearchField) GetPlaceholder() string {
	if x != nil {
		return x.Placeholder
	}
	return ""
}

func (x *SearchField) GetOperators() []int32 {
	if x != nil {
		return x.Operators
	}
	return nil
}

type MacProgressStatReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ProgressType `protobuf:"varint,1,opt,name=type,proto3,enum=yone.ProgressType" json:"type,omitempty"`
}

func (x *MacProgressStatReq) Reset() {
	*x = MacProgressStatReq{}
	mi := &file_yone_yone_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressStatReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressStatReq) ProtoMessage() {}

func (x *MacProgressStatReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressStatReq.ProtoReflect.Descriptor instead.
func (*MacProgressStatReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{68}
}

func (x *MacProgressStatReq) GetType() ProgressType {
	if x != nil {
		return x.Type
	}
	return ProgressType_PROGRESS_TYPE_UNKNOWN
}

type MacProgressStatResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgressingCount   int64  `protobuf:"varint,1,opt,name=progressing_count,json=progressingCount,proto3" json:"progressing_count,omitempty"`        // 正在扫描中的终端数
	TerminalTotal      int64  `protobuf:"varint,2,opt,name=terminal_total,json=terminalTotal,proto3" json:"terminal_total,omitempty"`                 // 终端总数
	ProgressingPercent string `protobuf:"bytes,3,opt,name=progressing_percent,json=progressingPercent,proto3" json:"progressing_percent,omitempty"`   // 正在扫描中的百分比
	SumFoundFileCount  int64  `protobuf:"varint,4,opt,name=sum_found_file_count,json=sumFoundFileCount,proto3" json:"sum_found_file_count,omitempty"` // 累计发现的文件总数
}

func (x *MacProgressStatResp) Reset() {
	*x = MacProgressStatResp{}
	mi := &file_yone_yone_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressStatResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressStatResp) ProtoMessage() {}

func (x *MacProgressStatResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressStatResp.ProtoReflect.Descriptor instead.
func (*MacProgressStatResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{69}
}

func (x *MacProgressStatResp) GetProgressingCount() int64 {
	if x != nil {
		return x.ProgressingCount
	}
	return 0
}

func (x *MacProgressStatResp) GetTerminalTotal() int64 {
	if x != nil {
		return x.TerminalTotal
	}
	return 0
}

func (x *MacProgressStatResp) GetProgressingPercent() string {
	if x != nil {
		return x.ProgressingPercent
	}
	return ""
}

func (x *MacProgressStatResp) GetSumFoundFileCount() int64 {
	if x != nil {
		return x.SumFoundFileCount
	}
	return 0
}

type MacProgressListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *MacProgressFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *Page              `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *MacProgressListReq) Reset() {
	*x = MacProgressListReq{}
	mi := &file_yone_yone_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressListReq) ProtoMessage() {}

func (x *MacProgressListReq) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressListReq.ProtoReflect.Descriptor instead.
func (*MacProgressListReq) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{70}
}

func (x *MacProgressListReq) GetFilter() *MacProgressFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *MacProgressListReq) GetPage() *Page {
	if x != nil {
		return x.Page
	}
	return nil
}

type MacProgressFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     ProgressType `protobuf:"varint,1,opt,name=type,proto3,enum=yone.ProgressType" json:"type,omitempty"`
	Status   []int32      `protobuf:"varint,2,rep,packed,name=status,proto3" json:"status,omitempty"`
	Ip       string       `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	HostName string       `protobuf:"bytes,4,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`
}

func (x *MacProgressFilter) Reset() {
	*x = MacProgressFilter{}
	mi := &file_yone_yone_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressFilter) ProtoMessage() {}

func (x *MacProgressFilter) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressFilter.ProtoReflect.Descriptor instead.
func (*MacProgressFilter) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{71}
}

func (x *MacProgressFilter) GetType() ProgressType {
	if x != nil {
		return x.Type
	}
	return ProgressType_PROGRESS_TYPE_UNKNOWN
}

func (x *MacProgressFilter) GetStatus() []int32 {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *MacProgressFilter) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *MacProgressFilter) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

type MacProgressListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items     []*MacProgressItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total     int64              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	PageIndex int64              `protobuf:"varint,3,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
}

func (x *MacProgressListResp) Reset() {
	*x = MacProgressListResp{}
	mi := &file_yone_yone_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressListResp) ProtoMessage() {}

func (x *MacProgressListResp) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressListResp.ProtoReflect.Descriptor instead.
func (*MacProgressListResp) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{72}
}

func (x *MacProgressListResp) GetItems() []*MacProgressItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *MacProgressListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *MacProgressListResp) GetPageIndex() int64 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

type MacProgressItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mac        string `protobuf:"bytes,1,opt,name=mac,proto3" json:"mac,omitempty"`                                  // 主机ID
	Ip         string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                    // 主机IP
	OsType     int32  `protobuf:"varint,3,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`             // 系统类型
	Os         string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`                                    // 操作系统名
	HostName   string `protobuf:"bytes,5,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`        // 主机名
	GroupId    int64  `protobuf:"varint,6,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`          // 分组ID
	GroupName  string `protobuf:"bytes,7,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`     // 分组名
	HostOnline int32  `protobuf:"varint,8,opt,name=host_online,json=hostOnline,proto3" json:"host_online,omitempty"` // 主机状态
	Status     int32  `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`                           // 采集状态 1:开始 2:扫描中 3:上传中(废弃) 4:暂停 5:完成
	StatusText string `protobuf:"bytes,10,opt,name=status_text,json=statusText,proto3" json:"status_text,omitempty"` // 采集状态文本
	Progress   string `protobuf:"bytes,11,opt,name=progress,proto3" json:"progress,omitempty"`                       // 进度百分比
	MacAddr    string `protobuf:"bytes,12,opt,name=mac_addr,json=macAddr,proto3" json:"mac_addr,omitempty"`          // 终端mac地址
}

func (x *MacProgressItem) Reset() {
	*x = MacProgressItem{}
	mi := &file_yone_yone_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressItem) ProtoMessage() {}

func (x *MacProgressItem) ProtoReflect() protoreflect.Message {
	mi := &file_yone_yone_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressItem.ProtoReflect.Descriptor instead.
func (*MacProgressItem) Descriptor() ([]byte, []int) {
	return file_yone_yone_proto_rawDescGZIP(), []int{73}
}

func (x *MacProgressItem) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *MacProgressItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *MacProgressItem) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *MacProgressItem) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *MacProgressItem) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *MacProgressItem) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *MacProgressItem) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *MacProgressItem) GetHostOnline() int32 {
	if x != nil {
		return x.HostOnline
	}
	return 0
}

func (x *MacProgressItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *MacProgressItem) GetStatusText() string {
	if x != nil {
		return x.StatusText
	}
	return ""
}

func (x *MacProgressItem) GetProgress() string {
	if x != nil {
		return x.Progress
	}
	return ""
}

func (x *MacProgressItem) GetMacAddr() string {
	if x != nil {
		return x.MacAddr
	}
	return ""
}

var File_yone_yone_proto protoreflect.FileDescriptor

var file_yone_yone_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x79, 0x6f, 0x6e, 0x65, 0x2f, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x79, 0x6f, 0x6e, 0x65, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x64, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x6d, 0x71, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0d, 0x6d, 0x71, 0x2f, 0x63, 0x6c, 0x75, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x50, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x2c, 0x0a, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x32, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x22, 0x62, 0x0a, 0x0f,
	0x56, 0x69, 0x72, 0x75, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61,
	0x63, 0x12, 0x3d, 0x0a, 0x0a, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65,
	0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x56, 0x69, 0x72, 0x75, 0x73, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x33, 0x0a, 0x10, 0x56, 0x69, 0x72, 0x75, 0x73, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4e, 0x75, 0x6d, 0x22, 0x58, 0x0a, 0x12, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x52, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0a, 0x6f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68,
	0x4d, 0x61, 0x63, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x73, 0x22,
	0x36, 0x0a, 0x13, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x52, 0x65, 0x63, 0x65, 0x69,
	0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x75, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4e, 0x75, 0x6d, 0x22, 0x4b, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x63, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a,
	0x07, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6e, 0x6c, 0x79, 0x5f,
	0x6c, 0x65, 0x61, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6f, 0x6e, 0x6c, 0x79,
	0x4c, 0x65, 0x61, 0x66, 0x22, 0x74, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x63, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12,
	0x29, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x39, 0x0a, 0x0d, 0x50, 0x72,
	0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x28, 0x0a, 0x05, 0x62,
	0x65, 0x61, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e,
	0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x65, 0x61, 0x6e, 0x52, 0x05,
	0x62, 0x65, 0x61, 0x6e, 0x73, 0x22, 0x39, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x06, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x73,
	0x22, 0x56, 0x0a, 0x09, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x43, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x57, 0x69, 0x74, 0x68, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x05, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x51, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x63,
	0x4e, 0x6f, 0x64, 0x65, 0x42, 0x65, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xd3, 0x02, 0x0a, 0x0d,
	0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x70, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x70, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x70, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78,
	0x65, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x65, 0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x22, 0xec, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x4e,
	0x6f, 0x64, 0x65, 0x57, 0x69, 0x74, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x70, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0f, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x70, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x65, 0x70, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x65, 0x63,
	0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x75,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64, 0x12, 0x30,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x92, 0x02, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x74, 0x69,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe7, 0x01, 0x0a, 0x11, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x73,
	0x72, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63,
	0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x64, 0x65, 0x73, 0x74, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x1d, 0x0a, 0x0a, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x65, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22,
	0x48, 0x0a, 0x12, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x6f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x65, 0x73, 0x22, 0x53, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x64, 0x35, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x64,
	0x35, 0x73, 0x12, 0x2a, 0x0a, 0x07, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x71, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x22, 0x14,
	0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x2a, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x6d, 0x64, 0x35, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x64, 0x35, 0x73,
	0x22, 0x42, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x22, 0xf8, 0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x12, 0x2c, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2a,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2a, 0x0a, 0x07, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x71,
	0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x07, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x73, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d,
	0x71, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x52, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x43, 0x6c, 0x75, 0x65, 0x22,
	0xae, 0x03, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x72, 0x63, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x72, 0x63, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x73, 0x74, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64,
	0x65, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x64, 0x65, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x6f, 0x75, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x69, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49,
	0x70, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x6f,
	0x73, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x68, 0x6f, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x0a, 0x05, 0x63, 0x68, 0x61,
	0x69, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x52, 0x05, 0x63, 0x68, 0x61, 0x69, 0x6e,
	0x22, 0x7b, 0x0a, 0x0e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x2f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x79, 0x22, 0xb7, 0x01,
	0x0a, 0x11, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x79, 0x6f,
	0x6e, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x0c, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x65, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x64, 0x22, 0x42, 0x0a, 0x04, 0x50, 0x61, 0x67, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6f, 0x0a, 0x0f, 0x41,
	0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27,
	0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xb1, 0x02, 0x0a,
	0x0b, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x41, 0x74,
	0x12, 0x37, 0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0d, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x41, 0x0a, 0x0e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x22, 0x72, 0x0a, 0x0b, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x0f, 0x41, 0x72, 0x63, 0x68,
	0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e,
	0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x05, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x12, 0x27, 0x0a, 0x05, 0x77, 0x68, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x05, 0x77, 0x68, 0x69, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x04, 0x67, 0x72, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f,
	0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x04,
	0x67, 0x72, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63,
	0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x22, 0x22, 0x0a, 0x0e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x8e, 0x03, 0x0a, 0x0b, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x68, 0x61, 0x31, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19,
	0x0a, 0x08, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x0c, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x79,
	0x6f, 0x6e, 0x65, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x69,
	0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x90, 0x03, 0x0a, 0x08, 0x53, 0x69, 0x67, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x69, 0x73, 0x73, 0x75, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x68, 0x75, 0x6d,
	0x62, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x68,
	0x75, 0x6d, 0x62, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x74, 0x5f, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x74, 0x42, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x41, 0x6c, 0x67, 0x6f,
	0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x68, 0x61,
	0x73, 0x68, 0x5f, 0x61, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x73, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x41, 0x6c, 0x67, 0x6f,
	0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x28, 0x0a, 0x10, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5f, 0x0a, 0x0f, 0x46, 0x69, 0x6c,
	0x65, 0x48, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x79,
	0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x22, 0x0a, 0x0e, 0x46, 0x69,
	0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x71,
	0x0a, 0x10, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x28, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0xc3, 0x03, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04,
	0x73, 0x68, 0x61, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66,
	0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x22, 0x0a, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x41, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x61, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x73, 0x76, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x6f, 0x73, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x22, 0x5b, 0x0a, 0x0b, 0x48, 0x6f, 0x73, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f,
	0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x70, 0x0a, 0x0e, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65,
	0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x45, 0x6e, 0x64, 0x22, 0x6d, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x12, 0x28, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xe4, 0x01, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x73, 0x76, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x73, 0x76, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02,
	0x6f, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x22, 0x21, 0x0a, 0x0b,
	0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x61, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x61, 0x63, 0x73, 0x22,
	0x38, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x28, 0x0a, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x52, 0x05, 0x68, 0x6f, 0x73, 0x74, 0x73, 0x22, 0x36, 0x0a, 0x0c, 0x48, 0x6f, 0x73,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x5f, 0x0a, 0x0f, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x12, 0x2c, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x51, 0x0a, 0x0e, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x6d, 0x0a, 0x10, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x22, 0xeb, 0x01, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x68, 0x61, 0x31, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x22,
	0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x46, 0x6f, 0x75, 0x6e, 0x64,
	0x41, 0x74, 0x22, 0x30, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x44, 0x61, 0x74, 0x61, 0x22, 0x22, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x47, 0x0a, 0x14, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0x46, 0x0a, 0x15, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69,
	0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x3b, 0x0a, 0x11, 0x4f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x61, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x43, 0x0a, 0x10, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x29, 0x0a, 0x11, 0x4f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x69, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0c, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x02, 0x6f,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x22, 0x59, 0x0a, 0x0e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f, 0x67,
	0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e,
	0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x73, 0x0a, 0x0f,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x2b, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0xe9, 0x05, 0x0a, 0x0f, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x70,
	0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x70, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x70, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x70,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x70, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x65, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04,
	0x65, 0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63,
	0x6d, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x68, 0x61, 0x31, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x69, 0x67, 0x6e, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x69, 0x67, 0x6e,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x70, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x22, 0x5b, 0x0a,
	0x10, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65,
	0x71, 0x12, 0x27, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4c, 0x6f, 0x67, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x77, 0x0a, 0x11, 0x46, 0x69,
	0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x2d, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4c, 0x6f, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x64, 0x65, 0x78, 0x22, 0xee, 0x03, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x02, 0x6f, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73,
	0x68, 0x61, 0x31, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x74,
	0x72, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x69, 0x67, 0x6e, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x73, 0x69, 0x67, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67,
	0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x61, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x74, 0x22, 0x58, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x71, 0x12, 0x27, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4c, 0x6f, 0x67,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x79,
	0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x71,
	0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0xdb, 0x04, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x70, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x70, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x65,
	0x70, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x70, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x70, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x70, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x65,
	0x75, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68,
	0x61, 0x31, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x18, 0x16, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x74, 0x72, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x69,
	0x67, 0x6e, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x69, 0x67, 0x6e, 0x73,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x18,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x74, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x74, 0x22,
	0xfb, 0x02, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x73, 0x12, 0x36, 0x0a, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e,
	0x75, 0x78, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0c, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x75, 0x78, 0x12, 0x3a, 0x0a, 0x0f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x57, 0x69,
	0x6e, 0x64, 0x6f, 0x77, 0x73, 0x12, 0x38, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x5f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x52, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x69, 0x6e, 0x75, 0x78, 0x12,
	0x3c, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0f, 0x6f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x12, 0x3c, 0x0a,
	0x10, 0x66, 0x69, 0x6c, 0x65, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x75,
	0x78, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x75, 0x78, 0x12, 0x40, 0x0a, 0x12, 0x66,
	0x69, 0x6c, 0x65, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x22, 0x8d, 0x01,
	0x0a, 0x0b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x12,
	0x1c, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x05, 0x52, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x73, 0x22, 0x3c, 0x0a,
	0x12, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xcb, 0x01, 0x0a, 0x13,
	0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2f, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x14, 0x73, 0x75, 0x6d, 0x5f,
	0x66, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x73, 0x75, 0x6d, 0x46, 0x6f, 0x75, 0x6e, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x65, 0x0a, 0x12, 0x4d, 0x61, 0x63,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12,
	0x2f, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x1e, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0x80, 0x01, 0x0a, 0x11, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x77, 0x0a, 0x13, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x22, 0xc4, 0x02, 0x0a,
	0x0f, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x61, 0x63, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x68,
	0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x68, 0x6f, 0x73, 0x74, 0x4f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x63, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x41,
	0x64, 0x64, 0x72, 0x2a, 0x5d, 0x0a, 0x05, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x0c,
	0x43, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1e,
	0x0a, 0x18, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x53, 0x10, 0xa1, 0x9c, 0x01, 0x12, 0x22,
	0x0a, 0x1c, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x52, 0x43,
	0x48, 0x49, 0x56, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xa2,
	0x9c, 0x01, 0x2a, 0x8e, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x17,
	0x0a, 0x13, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f,
	0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x02,
	0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x5f, 0x47, 0x52, 0x41, 0x59, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45,
	0x43, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x04, 0x2a, 0xd0, 0x02, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x45, 0x54, 0x43,
	0x48, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x49, 0x4e, 0x47,
	0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x03, 0x12, 0x1c, 0x0a, 0x18, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x1d, 0x0a, 0x19, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x05, 0x12, 0x1c,
	0x0a, 0x18, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45,
	0x54, 0x45, 0x43, 0x54, 0x5f, 0x51, 0x55, 0x45, 0x55, 0x45, 0x10, 0x06, 0x12, 0x19, 0x0a, 0x15,
	0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45,
	0x43, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x08, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x09, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x4f, 0x55, 0x54, 0x10, 0x0a, 0x2a, 0x45, 0x0a, 0x06, 0x4f, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x57, 0x49, 0x4e, 0x44, 0x4f, 0x57, 0x53, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x55, 0x58, 0x10, 0x02, 0x2a, 0x61, 0x0a,
	0x0c, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x47,
	0x52, 0x45, 0x53, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x4e, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x02,
	0x32, 0x9e, 0x0b, 0x0a, 0x04, 0x59, 0x6f, 0x6e, 0x65, 0x12, 0x4e, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x4e, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x18, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x73, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x79,
	0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x12, 0x13, 0x2e,
	0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x73, 0x52,
	0x65, 0x71, 0x1a, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x68,
	0x61, 0x69, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x12, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x45, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x1a, 0x18, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x40, 0x0a, 0x0f,
	0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63,
	0x68, 0x69, 0x76, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x40,
	0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x12, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41,
	0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x3c, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x41, 0x72, 0x63, 0x68, 0x69,
	0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65,
	0x2e, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x00, 0x12, 0x3f,
	0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x15,
	0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x6f, 0x73, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x48, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x37, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x1a, 0x12, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x37, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x12, 0x11, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x79, 0x6f,
	0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x12, 0x3f, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x15, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x48, 0x6f, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x39, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x13, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x40, 0x0a,
	0x0f, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x4e, 0x0a, 0x11, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x12, 0x1a, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71,
	0x1a, 0x1b, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x44, 0x61, 0x69, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x42, 0x0a, 0x0d, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x16, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x46, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71,
	0x1a, 0x17, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3d, 0x0a, 0x0e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x13, 0x2e,
	0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x67, 0x52,
	0x65, 0x71, 0x1a, 0x14, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x12, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x13, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x73, 0x22, 0x00, 0x12, 0x48, 0x0a,
	0x0f, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x12, 0x18, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x79, 0x6f, 0x6e,
	0x65, 0x2e, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0f, 0x4d, 0x61, 0x63, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x18, 0x2e, 0x79, 0x6f, 0x6e,
	0x65, 0x2e, 0x4d, 0x61, 0x63, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x79, 0x6f, 0x6e, 0x65, 0x2e, 0x4d, 0x61, 0x63, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x42, 0x29, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x79, 0x6f, 0x6e, 0x65, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_yone_yone_proto_rawDescOnce sync.Once
	file_yone_yone_proto_rawDescData = file_yone_yone_proto_rawDesc
)

func file_yone_yone_proto_rawDescGZIP() []byte {
	file_yone_yone_proto_rawDescOnce.Do(func() {
		file_yone_yone_proto_rawDescData = protoimpl.X.CompressGZIP(file_yone_yone_proto_rawDescData)
	})
	return file_yone_yone_proto_rawDescData
}

var file_yone_yone_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_yone_yone_proto_msgTypes = make([]protoimpl.MessageInfo, 74)
var file_yone_yone_proto_goTypes = []any{
	(Codes)(0),                                // 0: yone.Codes
	(DetectResult)(0),                         // 1: yone.DetectResult
	(FileTaskStatus)(0),                       // 2: yone.FileTaskStatus
	(OsType)(0),                               // 3: yone.OsType
	(ProgressType)(0),                         // 4: yone.ProgressType
	(*FileReceiveReq)(nil),                    // 5: yone.FileReceiveReq
	(*FileReceiveResp)(nil),                   // 6: yone.FileReceiveResp
	(*VirusReceiveReq)(nil),                   // 7: yone.VirusReceiveReq
	(*VirusReceiveResp)(nil),                  // 8: yone.VirusReceiveResp
	(*OutreachReceiveReq)(nil),                // 9: yone.OutreachReceiveReq
	(*OutreachReceiveResp)(nil),               // 10: yone.OutreachReceiveResp
	(*ProcChainsNodesReq)(nil),                // 11: yone.ProcChainsNodesReq
	(*ProcChainsNodesResp)(nil),               // 12: yone.ProcChainsNodesResp
	(*ProcChainsReq)(nil),                     // 13: yone.ProcChainsReq
	(*ProcChainsResp)(nil),                    // 14: yone.ProcChainsResp
	(*ProcChain)(nil),                         // 15: yone.ProcChain
	(*ProcNodeBean)(nil),                      // 16: yone.ProcNodeBean
	(*ProcChainNode)(nil),                     // 17: yone.ProcChainNode
	(*ProcChainNodeWithFile)(nil),             // 18: yone.ProcChainNodeWithFile
	(*ProcChainFile)(nil),                     // 19: yone.ProcChainFile
	(*OutreachSearchReq)(nil),                 // 20: yone.OutreachSearchReq
	(*OutreachSearchResp)(nil),                // 21: yone.OutreachSearchResp
	(*CreateFileTaskReq)(nil),                 // 22: yone.CreateFileTaskReq
	(*CreateFileTaskResp)(nil),                // 23: yone.CreateFileTaskResp
	(*GetFileTasksStateReq)(nil),              // 24: yone.GetFileTasksStateReq
	(*GetFileTasksStateResp)(nil),             // 25: yone.GetFileTasksStateResp
	(*FileTaskState)(nil),                     // 26: yone.FileTaskState
	(*OutreachInfo)(nil),                      // 27: yone.OutreachInfo
	(*ArchiveListReq)(nil),                    // 28: yone.ArchiveListReq
	(*ArchiveListFilter)(nil),                 // 29: yone.ArchiveListFilter
	(*Page)(nil),                              // 30: yone.Page
	(*ArchiveListResp)(nil),                   // 31: yone.ArchiveListResp
	(*ArchiveItem)(nil),                       // 32: yone.ArchiveItem
	(*ArchiveStatReq)(nil),                    // 33: yone.ArchiveStatReq
	(*ArchiveStat)(nil),                       // 34: yone.ArchiveStat
	(*ArchiveStatResp)(nil),                   // 35: yone.ArchiveStatResp
	(*ArchiveInfoReq)(nil),                    // 36: yone.ArchiveInfoReq
	(*ArchiveInfo)(nil),                       // 37: yone.ArchiveInfo
	(*SignInfo)(nil),                          // 38: yone.SignInfo
	(*FileHostListReq)(nil),                   // 39: yone.FileHostListReq
	(*FileHostFilter)(nil),                    // 40: yone.FileHostFilter
	(*FileHostListResp)(nil),                  // 41: yone.FileHostListResp
	(*FileHostItem)(nil),                      // 42: yone.FileHostItem
	(*HostViewReq)(nil),                       // 43: yone.HostViewReq
	(*HostViewFilter)(nil),                    // 44: yone.HostViewFilter
	(*HostViewResp)(nil),                      // 45: yone.HostViewResp
	(*HostViewItem)(nil),                      // 46: yone.HostViewItem
	(*HostStatReq)(nil),                       // 47: yone.HostStatReq
	(*HostStatResp)(nil),                      // 48: yone.HostStatResp
	(*HostFileStat)(nil),                      // 49: yone.HostFileStat
	(*HostFileListReq)(nil),                   // 50: yone.HostFileListReq
	(*HostFileFilter)(nil),                    // 51: yone.HostFileFilter
	(*HostFileListResp)(nil),                  // 52: yone.HostFileListResp
	(*FileItem)(nil),                          // 53: yone.FileItem
	(*FileSearchReq)(nil),                     // 54: yone.FileSearchReq
	(*FileSearchResp)(nil),                    // 55: yone.FileSearchResp
	(*OutreachDailyStatReq)(nil),              // 56: yone.OutreachDailyStatReq
	(*OutreachDailyStatResp)(nil),             // 57: yone.OutreachDailyStatResp
	(*OutreachDailyStat)(nil),                 // 58: yone.OutreachDailyStat
	(*OutreachTotalReq)(nil),                  // 59: yone.OutreachTotalReq
	(*OutreachTotalResp)(nil),                 // 60: yone.OutreachTotalResp
	(*LogFilter)(nil),                         // 61: yone.LogFilter
	(*OutreachLogReq)(nil),                    // 62: yone.OutreachLogReq
	(*OutreachLogResp)(nil),                   // 63: yone.OutreachLogResp
	(*OutreachLogItem)(nil),                   // 64: yone.OutreachLogItem
	(*FileCreateLogReq)(nil),                  // 65: yone.FileCreateLogReq
	(*FileCreateLogResp)(nil),                 // 66: yone.FileCreateLogResp
	(*FileCreateLogItem)(nil),                 // 67: yone.FileCreateLogItem
	(*ProcessLogReq)(nil),                     // 68: yone.ProcessLogReq
	(*ProcessLogResp)(nil),                    // 69: yone.ProcessLogResp
	(*ProcessLogItem)(nil),                    // 70: yone.ProcessLogItem
	(*SearchSchemas)(nil),                     // 71: yone.SearchSchemas
	(*SearchField)(nil),                       // 72: yone.SearchField
	(*MacProgressStatReq)(nil),                // 73: yone.MacProgressStatReq
	(*MacProgressStatResp)(nil),               // 74: yone.MacProgressStatResp
	(*MacProgressListReq)(nil),                // 75: yone.MacProgressListReq
	(*MacProgressFilter)(nil),                 // 76: yone.MacProgressFilter
	(*MacProgressListResp)(nil),               // 77: yone.MacProgressListResp
	(*MacProgressItem)(nil),                   // 78: yone.MacProgressItem
	(*agent.VirusDetectItem)(nil),             // 79: agent.VirusDetectItem
	(*agent.MemProtectVirusFileInfo)(nil),     // 80: agent.MemProtectVirusFileInfo
	(*agent.InternalOutreachInfoWithMac)(nil), // 81: agent.InternalOutreachInfoWithMac
	(mq.DetectEngine)(0),                      // 82: mq.DetectEngine
	(mq.ClueDetectEngine)(0),                  // 83: mq.ClueDetectEngine
	(*conan.TimeRange)(nil),                   // 84: conan.TimeRange
	(*emptypb.Empty)(nil),                     // 85: google.protobuf.Empty
}
var file_yone_yone_proto_depIdxs = []int32{
	79, // 0: yone.FileReceiveReq.files:type_name -> agent.VirusDetectItem
	80, // 1: yone.VirusReceiveReq.virus_info:type_name -> agent.MemProtectVirusFileInfo
	81, // 2: yone.OutreachReceiveReq.outreaches:type_name -> agent.InternalOutreachInfoWithMac
	17, // 3: yone.ProcChainsNodesResp.proc_nodes:type_name -> yone.ProcChainNode
	19, // 4: yone.ProcChainsNodesResp.files:type_name -> yone.ProcChainFile
	16, // 5: yone.ProcChainsReq.beans:type_name -> yone.ProcNodeBean
	15, // 6: yone.ProcChainsResp.chains:type_name -> yone.ProcChain
	18, // 7: yone.ProcChain.nodes:type_name -> yone.ProcChainNodeWithFile
	19, // 8: yone.ProcChainNodeWithFile.file_info:type_name -> yone.ProcChainFile
	27, // 9: yone.OutreachSearchResp.outreaches:type_name -> yone.OutreachInfo
	82, // 10: yone.CreateFileTaskReq.engines:type_name -> mq.DetectEngine
	26, // 11: yone.GetFileTasksStateResp.items:type_name -> yone.FileTaskState
	2,  // 12: yone.FileTaskState.status:type_name -> yone.FileTaskStatus
	1,  // 13: yone.FileTaskState.result:type_name -> yone.DetectResult
	82, // 14: yone.FileTaskState.engines:type_name -> mq.DetectEngine
	83, // 15: yone.FileTaskState.engines_clue:type_name -> mq.ClueDetectEngine
	15, // 16: yone.OutreachInfo.chain:type_name -> yone.ProcChain
	29, // 17: yone.ArchiveListReq.filter:type_name -> yone.ArchiveListFilter
	30, // 18: yone.ArchiveListReq.page:type_name -> yone.Page
	1,  // 19: yone.ArchiveListFilter.detect_result:type_name -> yone.DetectResult
	32, // 20: yone.ArchiveListResp.items:type_name -> yone.ArchiveItem
	1,  // 21: yone.ArchiveItem.detect_result:type_name -> yone.DetectResult
	84, // 22: yone.ArchiveStatReq.time_range:type_name -> conan.TimeRange
	34, // 23: yone.ArchiveStatResp.black:type_name -> yone.ArchiveStat
	34, // 24: yone.ArchiveStatResp.white:type_name -> yone.ArchiveStat
	34, // 25: yone.ArchiveStatResp.gray:type_name -> yone.ArchiveStat
	34, // 26: yone.ArchiveStatResp.unknown:type_name -> yone.ArchiveStat
	1,  // 27: yone.ArchiveInfo.detect_result:type_name -> yone.DetectResult
	38, // 28: yone.ArchiveInfo.sign_info:type_name -> yone.SignInfo
	40, // 29: yone.FileHostListReq.filter:type_name -> yone.FileHostFilter
	30, // 30: yone.FileHostListReq.page:type_name -> yone.Page
	42, // 31: yone.FileHostListResp.items:type_name -> yone.FileHostItem
	44, // 32: yone.HostViewReq.filter:type_name -> yone.HostViewFilter
	30, // 33: yone.HostViewReq.page:type_name -> yone.Page
	46, // 34: yone.HostViewResp.items:type_name -> yone.HostViewItem
	49, // 35: yone.HostStatResp.hosts:type_name -> yone.HostFileStat
	51, // 36: yone.HostFileListReq.filter:type_name -> yone.HostFileFilter
	30, // 37: yone.HostFileListReq.page:type_name -> yone.Page
	53, // 38: yone.HostFileListResp.items:type_name -> yone.FileItem
	84, // 39: yone.OutreachDailyStatReq.time_range:type_name -> conan.TimeRange
	58, // 40: yone.OutreachDailyStatResp.items:type_name -> yone.OutreachDailyStat
	84, // 41: yone.OutreachTotalReq.time_range:type_name -> conan.TimeRange
	3,  // 42: yone.LogFilter.os:type_name -> yone.OsType
	61, // 43: yone.OutreachLogReq.filter:type_name -> yone.LogFilter
	30, // 44: yone.OutreachLogReq.page:type_name -> yone.Page
	64, // 45: yone.OutreachLogResp.items:type_name -> yone.OutreachLogItem
	61, // 46: yone.FileCreateLogReq.filter:type_name -> yone.LogFilter
	30, // 47: yone.FileCreateLogReq.page:type_name -> yone.Page
	67, // 48: yone.FileCreateLogResp.items:type_name -> yone.FileCreateLogItem
	61, // 49: yone.ProcessLogReq.filter:type_name -> yone.LogFilter
	30, // 50: yone.ProcessLogReq.page:type_name -> yone.Page
	70, // 51: yone.ProcessLogResp.items:type_name -> yone.ProcessLogItem
	72, // 52: yone.SearchSchemas.process_linux:type_name -> yone.SearchField
	72, // 53: yone.SearchSchemas.process_windows:type_name -> yone.SearchField
	72, // 54: yone.SearchSchemas.outreach_linux:type_name -> yone.SearchField
	72, // 55: yone.SearchSchemas.outreach_windows:type_name -> yone.SearchField
	72, // 56: yone.SearchSchemas.filecreate_linux:type_name -> yone.SearchField
	72, // 57: yone.SearchSchemas.filecreate_windows:type_name -> yone.SearchField
	4,  // 58: yone.MacProgressStatReq.type:type_name -> yone.ProgressType
	76, // 59: yone.MacProgressListReq.filter:type_name -> yone.MacProgressFilter
	30, // 60: yone.MacProgressListReq.page:type_name -> yone.Page
	4,  // 61: yone.MacProgressFilter.type:type_name -> yone.ProgressType
	78, // 62: yone.MacProgressListResp.items:type_name -> yone.MacProgressItem
	11, // 63: yone.Yone.GetProcessChainsNodes:input_type -> yone.ProcChainsNodesReq
	13, // 64: yone.Yone.GetProcessChains:input_type -> yone.ProcChainsReq
	20, // 65: yone.Yone.SearchOutreachInfo:input_type -> yone.OutreachSearchReq
	22, // 66: yone.Yone.CreateFileTask:input_type -> yone.CreateFileTaskReq
	24, // 67: yone.Yone.GetFileTasksState:input_type -> yone.GetFileTasksStateReq
	28, // 68: yone.Yone.FileArchiveList:input_type -> yone.ArchiveListReq
	33, // 69: yone.Yone.FileArchiveStat:input_type -> yone.ArchiveStatReq
	36, // 70: yone.Yone.FileArchiveInfo:input_type -> yone.ArchiveInfoReq
	39, // 71: yone.Yone.FileHostList:input_type -> yone.FileHostListReq
	43, // 72: yone.Yone.HostViewList:input_type -> yone.HostViewReq
	47, // 73: yone.Yone.HostFileStat:input_type -> yone.HostStatReq
	50, // 74: yone.Yone.HostFileList:input_type -> yone.HostFileListReq
	54, // 75: yone.Yone.FileSearch:input_type -> yone.FileSearchReq
	62, // 76: yone.Yone.OutreachLogList:input_type -> yone.OutreachLogReq
	56, // 77: yone.Yone.OutreachDailyStat:input_type -> yone.OutreachDailyStatReq
	59, // 78: yone.Yone.OutreachTotal:input_type -> yone.OutreachTotalReq
	65, // 79: yone.Yone.FileCreateLogList:input_type -> yone.FileCreateLogReq
	68, // 80: yone.Yone.ProcessLogList:input_type -> yone.ProcessLogReq
	85, // 81: yone.Yone.GetSearchSchemas:input_type -> google.protobuf.Empty
	73, // 82: yone.Yone.MacProgressStat:input_type -> yone.MacProgressStatReq
	75, // 83: yone.Yone.MacProgressList:input_type -> yone.MacProgressListReq
	12, // 84: yone.Yone.GetProcessChainsNodes:output_type -> yone.ProcChainsNodesResp
	14, // 85: yone.Yone.GetProcessChains:output_type -> yone.ProcChainsResp
	21, // 86: yone.Yone.SearchOutreachInfo:output_type -> yone.OutreachSearchResp
	23, // 87: yone.Yone.CreateFileTask:output_type -> yone.CreateFileTaskResp
	25, // 88: yone.Yone.GetFileTasksState:output_type -> yone.GetFileTasksStateResp
	31, // 89: yone.Yone.FileArchiveList:output_type -> yone.ArchiveListResp
	35, // 90: yone.Yone.FileArchiveStat:output_type -> yone.ArchiveStatResp
	37, // 91: yone.Yone.FileArchiveInfo:output_type -> yone.ArchiveInfo
	41, // 92: yone.Yone.FileHostList:output_type -> yone.FileHostListResp
	45, // 93: yone.Yone.HostViewList:output_type -> yone.HostViewResp
	48, // 94: yone.Yone.HostFileStat:output_type -> yone.HostStatResp
	52, // 95: yone.Yone.HostFileList:output_type -> yone.HostFileListResp
	55, // 96: yone.Yone.FileSearch:output_type -> yone.FileSearchResp
	63, // 97: yone.Yone.OutreachLogList:output_type -> yone.OutreachLogResp
	57, // 98: yone.Yone.OutreachDailyStat:output_type -> yone.OutreachDailyStatResp
	60, // 99: yone.Yone.OutreachTotal:output_type -> yone.OutreachTotalResp
	66, // 100: yone.Yone.FileCreateLogList:output_type -> yone.FileCreateLogResp
	69, // 101: yone.Yone.ProcessLogList:output_type -> yone.ProcessLogResp
	71, // 102: yone.Yone.GetSearchSchemas:output_type -> yone.SearchSchemas
	74, // 103: yone.Yone.MacProgressStat:output_type -> yone.MacProgressStatResp
	77, // 104: yone.Yone.MacProgressList:output_type -> yone.MacProgressListResp
	84, // [84:105] is the sub-list for method output_type
	63, // [63:84] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_yone_yone_proto_init() }
func file_yone_yone_proto_init() {
	if File_yone_yone_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_yone_yone_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   74,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_yone_yone_proto_goTypes,
		DependencyIndexes: file_yone_yone_proto_depIdxs,
		EnumInfos:         file_yone_yone_proto_enumTypes,
		MessageInfos:      file_yone_yone_proto_msgTypes,
	}.Build()
	File_yone_yone_proto = out.File
	file_yone_yone_proto_rawDesc = nil
	file_yone_yone_proto_goTypes = nil
	file_yone_yone_proto_depIdxs = nil
}
