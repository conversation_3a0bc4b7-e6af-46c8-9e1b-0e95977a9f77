// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: yone/yone.proto

package yone

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Yone_GetProcessChainsNodes_FullMethodName = "/yone.Yone/GetProcessChainsNodes"
	Yone_GetProcessChains_FullMethodName      = "/yone.Yone/GetProcessChains"
	Yone_SearchOutreachInfo_FullMethodName    = "/yone.Yone/SearchOutreachInfo"
	Yone_CreateFileTask_FullMethodName        = "/yone.Yone/CreateFileTask"
	Yone_GetFileTasksState_FullMethodName     = "/yone.Yone/GetFileTasksState"
	Yone_FileArchiveList_FullMethodName       = "/yone.Yone/FileArchiveList"
	Yone_FileArchiveStat_FullMethodName       = "/yone.Yone/FileArchiveStat"
	Yone_FileArchiveInfo_FullMethodName       = "/yone.Yone/FileArchiveInfo"
	Yone_FileHostList_FullMethodName          = "/yone.Yone/FileHostList"
	Yone_HostViewList_FullMethodName          = "/yone.Yone/HostViewList"
	Yone_HostFileStat_FullMethodName          = "/yone.Yone/HostFileStat"
	Yone_HostFileList_FullMethodName          = "/yone.Yone/HostFileList"
	Yone_FileSearch_FullMethodName            = "/yone.Yone/FileSearch"
	Yone_OutreachLogList_FullMethodName       = "/yone.Yone/OutreachLogList"
	Yone_OutreachDailyStat_FullMethodName     = "/yone.Yone/OutreachDailyStat"
	Yone_OutreachTotal_FullMethodName         = "/yone.Yone/OutreachTotal"
	Yone_FileCreateLogList_FullMethodName     = "/yone.Yone/FileCreateLogList"
	Yone_ProcessLogList_FullMethodName        = "/yone.Yone/ProcessLogList"
	Yone_GetSearchSchemas_FullMethodName      = "/yone.Yone/GetSearchSchemas"
	Yone_MacProgressStat_FullMethodName       = "/yone.Yone/MacProgressStat"
	Yone_MacProgressList_FullMethodName       = "/yone.Yone/MacProgressList"
)

// YoneClient is the client API for Yone service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type YoneClient interface {
	// 依据进程唯一标识，批量获取进程链节点列表，不同进程链的节点混合同一个列表返回
	GetProcessChainsNodes(ctx context.Context, in *ProcChainsNodesReq, opts ...grpc.CallOption) (*ProcChainsNodesResp, error)
	// 基于进程链叶子节点批量查询进程链列表
	GetProcessChains(ctx context.Context, in *ProcChainsReq, opts ...grpc.CallOption) (*ProcChainsResp, error)
	// 检索外联信息
	SearchOutreachInfo(ctx context.Context, in *OutreachSearchReq, opts ...grpc.CallOption) (*OutreachSearchResp, error)
	// 创建文件检测任务
	CreateFileTask(ctx context.Context, in *CreateFileTaskReq, opts ...grpc.CallOption) (*CreateFileTaskResp, error)
	// 获取文件检测任务状态
	GetFileTasksState(ctx context.Context, in *GetFileTasksStateReq, opts ...grpc.CallOption) (*GetFileTasksStateResp, error)
	// 文件档案列表
	FileArchiveList(ctx context.Context, in *ArchiveListReq, opts ...grpc.CallOption) (*ArchiveListResp, error)
	// 文件档案统计数据
	FileArchiveStat(ctx context.Context, in *ArchiveStatReq, opts ...grpc.CallOption) (*ArchiveStatResp, error)
	// 文件档案信息
	FileArchiveInfo(ctx context.Context, in *ArchiveInfoReq, opts ...grpc.CallOption) (*ArchiveInfo, error)
	// 文件所在主机列表
	FileHostList(ctx context.Context, in *FileHostListReq, opts ...grpc.CallOption) (*FileHostListResp, error)
	// 文件档案主机视图
	HostViewList(ctx context.Context, in *HostViewReq, opts ...grpc.CallOption) (*HostViewResp, error)
	// 主机文件统计信息
	HostFileStat(ctx context.Context, in *HostStatReq, opts ...grpc.CallOption) (*HostStatResp, error)
	// 主机文件列表
	HostFileList(ctx context.Context, in *HostFileListReq, opts ...grpc.CallOption) (*HostFileListResp, error)
	// 文件搜索
	FileSearch(ctx context.Context, in *FileSearchReq, opts ...grpc.CallOption) (*FileSearchResp, error)
	// 外联日志列表
	OutreachLogList(ctx context.Context, in *OutreachLogReq, opts ...grpc.CallOption) (*OutreachLogResp, error)
	// 外联日志每日统计数据
	OutreachDailyStat(ctx context.Context, in *OutreachDailyStatReq, opts ...grpc.CallOption) (*OutreachDailyStatResp, error)
	// 外联日志总数
	OutreachTotal(ctx context.Context, in *OutreachTotalReq, opts ...grpc.CallOption) (*OutreachTotalResp, error)
	// 文件创建日志列表
	FileCreateLogList(ctx context.Context, in *FileCreateLogReq, opts ...grpc.CallOption) (*FileCreateLogResp, error)
	// 进程日志列表
	ProcessLogList(ctx context.Context, in *ProcessLogReq, opts ...grpc.CallOption) (*ProcessLogResp, error)
	// 获取日志检索结构
	GetSearchSchemas(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SearchSchemas, error)
	// 获取各主机上报进度统计信息
	MacProgressStat(ctx context.Context, in *MacProgressStatReq, opts ...grpc.CallOption) (*MacProgressStatResp, error)
	// 主机上报进度分页数据
	MacProgressList(ctx context.Context, in *MacProgressListReq, opts ...grpc.CallOption) (*MacProgressListResp, error)
}

type yoneClient struct {
	cc grpc.ClientConnInterface
}

func NewYoneClient(cc grpc.ClientConnInterface) YoneClient {
	return &yoneClient{cc}
}

func (c *yoneClient) GetProcessChainsNodes(ctx context.Context, in *ProcChainsNodesReq, opts ...grpc.CallOption) (*ProcChainsNodesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcChainsNodesResp)
	err := c.cc.Invoke(ctx, Yone_GetProcessChainsNodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) GetProcessChains(ctx context.Context, in *ProcChainsReq, opts ...grpc.CallOption) (*ProcChainsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcChainsResp)
	err := c.cc.Invoke(ctx, Yone_GetProcessChains_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) SearchOutreachInfo(ctx context.Context, in *OutreachSearchReq, opts ...grpc.CallOption) (*OutreachSearchResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OutreachSearchResp)
	err := c.cc.Invoke(ctx, Yone_SearchOutreachInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) CreateFileTask(ctx context.Context, in *CreateFileTaskReq, opts ...grpc.CallOption) (*CreateFileTaskResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateFileTaskResp)
	err := c.cc.Invoke(ctx, Yone_CreateFileTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) GetFileTasksState(ctx context.Context, in *GetFileTasksStateReq, opts ...grpc.CallOption) (*GetFileTasksStateResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileTasksStateResp)
	err := c.cc.Invoke(ctx, Yone_GetFileTasksState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileArchiveList(ctx context.Context, in *ArchiveListReq, opts ...grpc.CallOption) (*ArchiveListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ArchiveListResp)
	err := c.cc.Invoke(ctx, Yone_FileArchiveList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileArchiveStat(ctx context.Context, in *ArchiveStatReq, opts ...grpc.CallOption) (*ArchiveStatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ArchiveStatResp)
	err := c.cc.Invoke(ctx, Yone_FileArchiveStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileArchiveInfo(ctx context.Context, in *ArchiveInfoReq, opts ...grpc.CallOption) (*ArchiveInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ArchiveInfo)
	err := c.cc.Invoke(ctx, Yone_FileArchiveInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileHostList(ctx context.Context, in *FileHostListReq, opts ...grpc.CallOption) (*FileHostListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileHostListResp)
	err := c.cc.Invoke(ctx, Yone_FileHostList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) HostViewList(ctx context.Context, in *HostViewReq, opts ...grpc.CallOption) (*HostViewResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HostViewResp)
	err := c.cc.Invoke(ctx, Yone_HostViewList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) HostFileStat(ctx context.Context, in *HostStatReq, opts ...grpc.CallOption) (*HostStatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HostStatResp)
	err := c.cc.Invoke(ctx, Yone_HostFileStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) HostFileList(ctx context.Context, in *HostFileListReq, opts ...grpc.CallOption) (*HostFileListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HostFileListResp)
	err := c.cc.Invoke(ctx, Yone_HostFileList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileSearch(ctx context.Context, in *FileSearchReq, opts ...grpc.CallOption) (*FileSearchResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileSearchResp)
	err := c.cc.Invoke(ctx, Yone_FileSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) OutreachLogList(ctx context.Context, in *OutreachLogReq, opts ...grpc.CallOption) (*OutreachLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OutreachLogResp)
	err := c.cc.Invoke(ctx, Yone_OutreachLogList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) OutreachDailyStat(ctx context.Context, in *OutreachDailyStatReq, opts ...grpc.CallOption) (*OutreachDailyStatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OutreachDailyStatResp)
	err := c.cc.Invoke(ctx, Yone_OutreachDailyStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) OutreachTotal(ctx context.Context, in *OutreachTotalReq, opts ...grpc.CallOption) (*OutreachTotalResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OutreachTotalResp)
	err := c.cc.Invoke(ctx, Yone_OutreachTotal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) FileCreateLogList(ctx context.Context, in *FileCreateLogReq, opts ...grpc.CallOption) (*FileCreateLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileCreateLogResp)
	err := c.cc.Invoke(ctx, Yone_FileCreateLogList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) ProcessLogList(ctx context.Context, in *ProcessLogReq, opts ...grpc.CallOption) (*ProcessLogResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessLogResp)
	err := c.cc.Invoke(ctx, Yone_ProcessLogList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) GetSearchSchemas(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*SearchSchemas, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchSchemas)
	err := c.cc.Invoke(ctx, Yone_GetSearchSchemas_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) MacProgressStat(ctx context.Context, in *MacProgressStatReq, opts ...grpc.CallOption) (*MacProgressStatResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MacProgressStatResp)
	err := c.cc.Invoke(ctx, Yone_MacProgressStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *yoneClient) MacProgressList(ctx context.Context, in *MacProgressListReq, opts ...grpc.CallOption) (*MacProgressListResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MacProgressListResp)
	err := c.cc.Invoke(ctx, Yone_MacProgressList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// YoneServer is the server API for Yone service.
// All implementations must embed UnimplementedYoneServer
// for forward compatibility.
type YoneServer interface {
	// 依据进程唯一标识，批量获取进程链节点列表，不同进程链的节点混合同一个列表返回
	GetProcessChainsNodes(context.Context, *ProcChainsNodesReq) (*ProcChainsNodesResp, error)
	// 基于进程链叶子节点批量查询进程链列表
	GetProcessChains(context.Context, *ProcChainsReq) (*ProcChainsResp, error)
	// 检索外联信息
	SearchOutreachInfo(context.Context, *OutreachSearchReq) (*OutreachSearchResp, error)
	// 创建文件检测任务
	CreateFileTask(context.Context, *CreateFileTaskReq) (*CreateFileTaskResp, error)
	// 获取文件检测任务状态
	GetFileTasksState(context.Context, *GetFileTasksStateReq) (*GetFileTasksStateResp, error)
	// 文件档案列表
	FileArchiveList(context.Context, *ArchiveListReq) (*ArchiveListResp, error)
	// 文件档案统计数据
	FileArchiveStat(context.Context, *ArchiveStatReq) (*ArchiveStatResp, error)
	// 文件档案信息
	FileArchiveInfo(context.Context, *ArchiveInfoReq) (*ArchiveInfo, error)
	// 文件所在主机列表
	FileHostList(context.Context, *FileHostListReq) (*FileHostListResp, error)
	// 文件档案主机视图
	HostViewList(context.Context, *HostViewReq) (*HostViewResp, error)
	// 主机文件统计信息
	HostFileStat(context.Context, *HostStatReq) (*HostStatResp, error)
	// 主机文件列表
	HostFileList(context.Context, *HostFileListReq) (*HostFileListResp, error)
	// 文件搜索
	FileSearch(context.Context, *FileSearchReq) (*FileSearchResp, error)
	// 外联日志列表
	OutreachLogList(context.Context, *OutreachLogReq) (*OutreachLogResp, error)
	// 外联日志每日统计数据
	OutreachDailyStat(context.Context, *OutreachDailyStatReq) (*OutreachDailyStatResp, error)
	// 外联日志总数
	OutreachTotal(context.Context, *OutreachTotalReq) (*OutreachTotalResp, error)
	// 文件创建日志列表
	FileCreateLogList(context.Context, *FileCreateLogReq) (*FileCreateLogResp, error)
	// 进程日志列表
	ProcessLogList(context.Context, *ProcessLogReq) (*ProcessLogResp, error)
	// 获取日志检索结构
	GetSearchSchemas(context.Context, *emptypb.Empty) (*SearchSchemas, error)
	// 获取各主机上报进度统计信息
	MacProgressStat(context.Context, *MacProgressStatReq) (*MacProgressStatResp, error)
	// 主机上报进度分页数据
	MacProgressList(context.Context, *MacProgressListReq) (*MacProgressListResp, error)
	mustEmbedUnimplementedYoneServer()
}

// UnimplementedYoneServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedYoneServer struct{}

func (UnimplementedYoneServer) GetProcessChainsNodes(context.Context, *ProcChainsNodesReq) (*ProcChainsNodesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessChainsNodes not implemented")
}
func (UnimplementedYoneServer) GetProcessChains(context.Context, *ProcChainsReq) (*ProcChainsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProcessChains not implemented")
}
func (UnimplementedYoneServer) SearchOutreachInfo(context.Context, *OutreachSearchReq) (*OutreachSearchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchOutreachInfo not implemented")
}
func (UnimplementedYoneServer) CreateFileTask(context.Context, *CreateFileTaskReq) (*CreateFileTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFileTask not implemented")
}
func (UnimplementedYoneServer) GetFileTasksState(context.Context, *GetFileTasksStateReq) (*GetFileTasksStateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileTasksState not implemented")
}
func (UnimplementedYoneServer) FileArchiveList(context.Context, *ArchiveListReq) (*ArchiveListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileArchiveList not implemented")
}
func (UnimplementedYoneServer) FileArchiveStat(context.Context, *ArchiveStatReq) (*ArchiveStatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileArchiveStat not implemented")
}
func (UnimplementedYoneServer) FileArchiveInfo(context.Context, *ArchiveInfoReq) (*ArchiveInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileArchiveInfo not implemented")
}
func (UnimplementedYoneServer) FileHostList(context.Context, *FileHostListReq) (*FileHostListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileHostList not implemented")
}
func (UnimplementedYoneServer) HostViewList(context.Context, *HostViewReq) (*HostViewResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HostViewList not implemented")
}
func (UnimplementedYoneServer) HostFileStat(context.Context, *HostStatReq) (*HostStatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HostFileStat not implemented")
}
func (UnimplementedYoneServer) HostFileList(context.Context, *HostFileListReq) (*HostFileListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HostFileList not implemented")
}
func (UnimplementedYoneServer) FileSearch(context.Context, *FileSearchReq) (*FileSearchResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileSearch not implemented")
}
func (UnimplementedYoneServer) OutreachLogList(context.Context, *OutreachLogReq) (*OutreachLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OutreachLogList not implemented")
}
func (UnimplementedYoneServer) OutreachDailyStat(context.Context, *OutreachDailyStatReq) (*OutreachDailyStatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OutreachDailyStat not implemented")
}
func (UnimplementedYoneServer) OutreachTotal(context.Context, *OutreachTotalReq) (*OutreachTotalResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OutreachTotal not implemented")
}
func (UnimplementedYoneServer) FileCreateLogList(context.Context, *FileCreateLogReq) (*FileCreateLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FileCreateLogList not implemented")
}
func (UnimplementedYoneServer) ProcessLogList(context.Context, *ProcessLogReq) (*ProcessLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessLogList not implemented")
}
func (UnimplementedYoneServer) GetSearchSchemas(context.Context, *emptypb.Empty) (*SearchSchemas, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSearchSchemas not implemented")
}
func (UnimplementedYoneServer) MacProgressStat(context.Context, *MacProgressStatReq) (*MacProgressStatResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MacProgressStat not implemented")
}
func (UnimplementedYoneServer) MacProgressList(context.Context, *MacProgressListReq) (*MacProgressListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MacProgressList not implemented")
}
func (UnimplementedYoneServer) mustEmbedUnimplementedYoneServer() {}
func (UnimplementedYoneServer) testEmbeddedByValue()              {}

// UnsafeYoneServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to YoneServer will
// result in compilation errors.
type UnsafeYoneServer interface {
	mustEmbedUnimplementedYoneServer()
}

func RegisterYoneServer(s grpc.ServiceRegistrar, srv YoneServer) {
	// If the following call pancis, it indicates UnimplementedYoneServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Yone_ServiceDesc, srv)
}

func _Yone_GetProcessChainsNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcChainsNodesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).GetProcessChainsNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_GetProcessChainsNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).GetProcessChainsNodes(ctx, req.(*ProcChainsNodesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_GetProcessChains_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcChainsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).GetProcessChains(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_GetProcessChains_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).GetProcessChains(ctx, req.(*ProcChainsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_SearchOutreachInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OutreachSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).SearchOutreachInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_SearchOutreachInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).SearchOutreachInfo(ctx, req.(*OutreachSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_CreateFileTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFileTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).CreateFileTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_CreateFileTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).CreateFileTask(ctx, req.(*CreateFileTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_GetFileTasksState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileTasksStateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).GetFileTasksState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_GetFileTasksState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).GetFileTasksState(ctx, req.(*GetFileTasksStateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileArchiveList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArchiveListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileArchiveList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileArchiveList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileArchiveList(ctx, req.(*ArchiveListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileArchiveStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArchiveStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileArchiveStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileArchiveStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileArchiveStat(ctx, req.(*ArchiveStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileArchiveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ArchiveInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileArchiveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileArchiveInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileArchiveInfo(ctx, req.(*ArchiveInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileHostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileHostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileHostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileHostList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileHostList(ctx, req.(*FileHostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_HostViewList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HostViewReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).HostViewList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_HostViewList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).HostViewList(ctx, req.(*HostViewReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_HostFileStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HostStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).HostFileStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_HostFileStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).HostFileStat(ctx, req.(*HostStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_HostFileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HostFileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).HostFileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_HostFileList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).HostFileList(ctx, req.(*HostFileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileSearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileSearch(ctx, req.(*FileSearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_OutreachLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OutreachLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).OutreachLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_OutreachLogList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).OutreachLogList(ctx, req.(*OutreachLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_OutreachDailyStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OutreachDailyStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).OutreachDailyStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_OutreachDailyStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).OutreachDailyStat(ctx, req.(*OutreachDailyStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_OutreachTotal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OutreachTotalReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).OutreachTotal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_OutreachTotal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).OutreachTotal(ctx, req.(*OutreachTotalReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_FileCreateLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileCreateLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).FileCreateLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_FileCreateLogList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).FileCreateLogList(ctx, req.(*FileCreateLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_ProcessLogList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).ProcessLogList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_ProcessLogList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).ProcessLogList(ctx, req.(*ProcessLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_GetSearchSchemas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).GetSearchSchemas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_GetSearchSchemas_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).GetSearchSchemas(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_MacProgressStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MacProgressStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).MacProgressStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_MacProgressStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).MacProgressStat(ctx, req.(*MacProgressStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Yone_MacProgressList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MacProgressListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(YoneServer).MacProgressList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Yone_MacProgressList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(YoneServer).MacProgressList(ctx, req.(*MacProgressListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Yone_ServiceDesc is the grpc.ServiceDesc for Yone service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Yone_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "yone.Yone",
	HandlerType: (*YoneServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetProcessChainsNodes",
			Handler:    _Yone_GetProcessChainsNodes_Handler,
		},
		{
			MethodName: "GetProcessChains",
			Handler:    _Yone_GetProcessChains_Handler,
		},
		{
			MethodName: "SearchOutreachInfo",
			Handler:    _Yone_SearchOutreachInfo_Handler,
		},
		{
			MethodName: "CreateFileTask",
			Handler:    _Yone_CreateFileTask_Handler,
		},
		{
			MethodName: "GetFileTasksState",
			Handler:    _Yone_GetFileTasksState_Handler,
		},
		{
			MethodName: "FileArchiveList",
			Handler:    _Yone_FileArchiveList_Handler,
		},
		{
			MethodName: "FileArchiveStat",
			Handler:    _Yone_FileArchiveStat_Handler,
		},
		{
			MethodName: "FileArchiveInfo",
			Handler:    _Yone_FileArchiveInfo_Handler,
		},
		{
			MethodName: "FileHostList",
			Handler:    _Yone_FileHostList_Handler,
		},
		{
			MethodName: "HostViewList",
			Handler:    _Yone_HostViewList_Handler,
		},
		{
			MethodName: "HostFileStat",
			Handler:    _Yone_HostFileStat_Handler,
		},
		{
			MethodName: "HostFileList",
			Handler:    _Yone_HostFileList_Handler,
		},
		{
			MethodName: "FileSearch",
			Handler:    _Yone_FileSearch_Handler,
		},
		{
			MethodName: "OutreachLogList",
			Handler:    _Yone_OutreachLogList_Handler,
		},
		{
			MethodName: "OutreachDailyStat",
			Handler:    _Yone_OutreachDailyStat_Handler,
		},
		{
			MethodName: "OutreachTotal",
			Handler:    _Yone_OutreachTotal_Handler,
		},
		{
			MethodName: "FileCreateLogList",
			Handler:    _Yone_FileCreateLogList_Handler,
		},
		{
			MethodName: "ProcessLogList",
			Handler:    _Yone_ProcessLogList_Handler,
		},
		{
			MethodName: "GetSearchSchemas",
			Handler:    _Yone_GetSearchSchemas_Handler,
		},
		{
			MethodName: "MacProgressStat",
			Handler:    _Yone_MacProgressStat_Handler,
		},
		{
			MethodName: "MacProgressList",
			Handler:    _Yone_MacProgressList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "yone/yone.proto",
}
