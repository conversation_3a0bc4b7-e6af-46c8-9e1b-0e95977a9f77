// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: jax/jax.proto

package jax

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetConfigReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfigReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetConfigReqMultiError, or
// nil if none found.
func (m *GetConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetConfigReqMultiError(errors)
	}

	return nil
}

// GetConfigReqMultiError is an error wrapping multiple validation errors
// returned by GetConfigReq.ValidateAll() if the designated constraints aren't met.
type GetConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfigReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfigReqMultiError) AllErrors() []error { return m }

// GetConfigReqValidationError is the validation error returned by
// GetConfigReq.Validate if the designated constraints aren't met.
type GetConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfigReqValidationError) ErrorName() string { return "GetConfigReqValidationError" }

// Error satisfies the builtin error interface
func (e GetConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfigReqValidationError{}

// Validate checks the field values on GetConfigResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConfigResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfigResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetConfigRespMultiError, or
// nil if none found.
func (m *GetConfigResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfigResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return GetConfigRespMultiError(errors)
	}

	return nil
}

// GetConfigRespMultiError is an error wrapping multiple validation errors
// returned by GetConfigResp.ValidateAll() if the designated constraints
// aren't met.
type GetConfigRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfigRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfigRespMultiError) AllErrors() []error { return m }

// GetConfigRespValidationError is the validation error returned by
// GetConfigResp.Validate if the designated constraints aren't met.
type GetConfigRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfigRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfigRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfigRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfigRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfigRespValidationError) ErrorName() string { return "GetConfigRespValidationError" }

// Error satisfies the builtin error interface
func (e GetConfigRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfigResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfigRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfigRespValidationError{}

// Validate checks the field values on UpdateConfigReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateConfigReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateConfigReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateConfigReqMultiError, or nil if none found.
func (m *UpdateConfigReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateConfigReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return UpdateConfigReqMultiError(errors)
	}

	return nil
}

// UpdateConfigReqMultiError is an error wrapping multiple validation errors
// returned by UpdateConfigReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateConfigReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateConfigReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateConfigReqMultiError) AllErrors() []error { return m }

// UpdateConfigReqValidationError is the validation error returned by
// UpdateConfigReq.Validate if the designated constraints aren't met.
type UpdateConfigReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateConfigReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateConfigReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateConfigReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateConfigReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateConfigReqValidationError) ErrorName() string { return "UpdateConfigReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdateConfigReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateConfigReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateConfigReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateConfigReqValidationError{}

// Validate checks the field values on UpdateConfigResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateConfigResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateConfigResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateConfigRespMultiError, or nil if none found.
func (m *UpdateConfigResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateConfigResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateConfigRespMultiError(errors)
	}

	return nil
}

// UpdateConfigRespMultiError is an error wrapping multiple validation errors
// returned by UpdateConfigResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateConfigRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateConfigRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateConfigRespMultiError) AllErrors() []error { return m }

// UpdateConfigRespValidationError is the validation error returned by
// UpdateConfigResp.Validate if the designated constraints aren't met.
type UpdateConfigRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateConfigRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateConfigRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateConfigRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateConfigRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateConfigRespValidationError) ErrorName() string { return "UpdateConfigRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdateConfigRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateConfigResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateConfigRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateConfigRespValidationError{}
