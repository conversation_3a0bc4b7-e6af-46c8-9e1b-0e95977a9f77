// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: jax/jax.proto

package jax

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Jax_GetConfig_FullMethodName    = "/jax.Jax/GetConfig"
	Jax_UpdateConfig_FullMethodName = "/jax.Jax/UpdateConfig"
)

// JaxClient is the client API for Jax service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// B端接入网关服务
type JaxClient interface {
	// 查询配置
	GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*GetConfigResp, error)
	// 更新配置
	UpdateConfig(ctx context.Context, in *UpdateConfigReq, opts ...grpc.CallOption) (*UpdateConfigResp, error)
}

type jaxClient struct {
	cc grpc.ClientConnInterface
}

func NewJaxClient(cc grpc.ClientConnInterface) JaxClient {
	return &jaxClient{cc}
}

func (c *jaxClient) GetConfig(ctx context.Context, in *GetConfigReq, opts ...grpc.CallOption) (*GetConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigResp)
	err := c.cc.Invoke(ctx, Jax_GetConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jaxClient) UpdateConfig(ctx context.Context, in *UpdateConfigReq, opts ...grpc.CallOption) (*UpdateConfigResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateConfigResp)
	err := c.cc.Invoke(ctx, Jax_UpdateConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JaxServer is the server API for Jax service.
// All implementations must embed UnimplementedJaxServer
// for forward compatibility.
//
// B端接入网关服务
type JaxServer interface {
	// 查询配置
	GetConfig(context.Context, *GetConfigReq) (*GetConfigResp, error)
	// 更新配置
	UpdateConfig(context.Context, *UpdateConfigReq) (*UpdateConfigResp, error)
	mustEmbedUnimplementedJaxServer()
}

// UnimplementedJaxServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJaxServer struct{}

func (UnimplementedJaxServer) GetConfig(context.Context, *GetConfigReq) (*GetConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfig not implemented")
}
func (UnimplementedJaxServer) UpdateConfig(context.Context, *UpdateConfigReq) (*UpdateConfigResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateConfig not implemented")
}
func (UnimplementedJaxServer) mustEmbedUnimplementedJaxServer() {}
func (UnimplementedJaxServer) testEmbeddedByValue()             {}

// UnsafeJaxServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JaxServer will
// result in compilation errors.
type UnsafeJaxServer interface {
	mustEmbedUnimplementedJaxServer()
}

func RegisterJaxServer(s grpc.ServiceRegistrar, srv JaxServer) {
	// If the following call pancis, it indicates UnimplementedJaxServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Jax_ServiceDesc, srv)
}

func _Jax_GetConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JaxServer).GetConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jax_GetConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JaxServer).GetConfig(ctx, req.(*GetConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Jax_UpdateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JaxServer).UpdateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Jax_UpdateConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JaxServer).UpdateConfig(ctx, req.(*UpdateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Jax_ServiceDesc is the grpc.ServiceDesc for Jax service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Jax_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "jax.Jax",
	HandlerType: (*JaxServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConfig",
			Handler:    _Jax_GetConfig_Handler,
		},
		{
			MethodName: "UpdateConfig",
			Handler:    _Jax_UpdateConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "jax/jax.proto",
}
