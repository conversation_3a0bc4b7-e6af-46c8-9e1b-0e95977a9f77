// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: jax/jax.proto

package jax

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetConfigReq) Reset() {
	*x = GetConfigReq{}
	mi := &file_jax_jax_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigReq) ProtoMessage() {}

func (x *GetConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_jax_jax_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigReq.ProtoReflect.Descriptor instead.
func (*GetConfigReq) Descriptor() ([]byte, []int) {
	return file_jax_jax_proto_rawDescGZIP(), []int{0}
}

type GetConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *GetConfigResp) Reset() {
	*x = GetConfigResp{}
	mi := &file_jax_jax_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigResp) ProtoMessage() {}

func (x *GetConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_jax_jax_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigResp.ProtoReflect.Descriptor instead.
func (*GetConfigResp) Descriptor() ([]byte, []int) {
	return file_jax_jax_proto_rawDescGZIP(), []int{1}
}

func (x *GetConfigResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateConfigReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *UpdateConfigReq) Reset() {
	*x = UpdateConfigReq{}
	mi := &file_jax_jax_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfigReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfigReq) ProtoMessage() {}

func (x *UpdateConfigReq) ProtoReflect() protoreflect.Message {
	mi := &file_jax_jax_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfigReq.ProtoReflect.Descriptor instead.
func (*UpdateConfigReq) Descriptor() ([]byte, []int) {
	return file_jax_jax_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateConfigReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateConfigResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateConfigResp) Reset() {
	*x = UpdateConfigResp{}
	mi := &file_jax_jax_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfigResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfigResp) ProtoMessage() {}

func (x *UpdateConfigResp) ProtoReflect() protoreflect.Message {
	mi := &file_jax_jax_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfigResp.ProtoReflect.Descriptor instead.
func (*UpdateConfigResp) Descriptor() ([]byte, []int) {
	return file_jax_jax_proto_rawDescGZIP(), []int{3}
}

var File_jax_jax_proto protoreflect.FileDescriptor

var file_jax_jax_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6a, 0x61, 0x78, 0x2f, 0x6a, 0x61, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x03, 0x6a, 0x61, 0x78, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x0e, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x71, 0x22, 0x23, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x25, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x12,
	0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x32, 0xa6, 0x01, 0x0a, 0x03, 0x4a, 0x61, 0x78, 0x12, 0x47, 0x0a, 0x09, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x11, 0x2e, 0x6a, 0x61, 0x78, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6a, 0x61, 0x78,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x22, 0x13,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x67, 0x65, 0x74, 0x12, 0x56, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x14, 0x2e, 0x6a, 0x61, 0x78, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x6a, 0x61, 0x78, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x28, 0x5a, 0x26, 0x67,
	0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31,
	0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x6a, 0x61, 0x78, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_jax_jax_proto_rawDescOnce sync.Once
	file_jax_jax_proto_rawDescData = file_jax_jax_proto_rawDesc
)

func file_jax_jax_proto_rawDescGZIP() []byte {
	file_jax_jax_proto_rawDescOnce.Do(func() {
		file_jax_jax_proto_rawDescData = protoimpl.X.CompressGZIP(file_jax_jax_proto_rawDescData)
	})
	return file_jax_jax_proto_rawDescData
}

var file_jax_jax_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_jax_jax_proto_goTypes = []any{
	(*GetConfigReq)(nil),     // 0: jax.GetConfigReq
	(*GetConfigResp)(nil),    // 1: jax.GetConfigResp
	(*UpdateConfigReq)(nil),  // 2: jax.UpdateConfigReq
	(*UpdateConfigResp)(nil), // 3: jax.UpdateConfigResp
}
var file_jax_jax_proto_depIdxs = []int32{
	0, // 0: jax.Jax.GetConfig:input_type -> jax.GetConfigReq
	2, // 1: jax.Jax.UpdateConfig:input_type -> jax.UpdateConfigReq
	1, // 2: jax.Jax.GetConfig:output_type -> jax.GetConfigResp
	3, // 3: jax.Jax.UpdateConfig:output_type -> jax.UpdateConfigResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_jax_jax_proto_init() }
func file_jax_jax_proto_init() {
	if File_jax_jax_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_jax_jax_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_jax_jax_proto_goTypes,
		DependencyIndexes: file_jax_jax_proto_depIdxs,
		MessageInfos:      file_jax_jax_proto_msgTypes,
	}.Build()
	File_jax_jax_proto = out.File
	file_jax_jax_proto_rawDesc = nil
	file_jax_jax_proto_goTypes = nil
	file_jax_jax_proto_depIdxs = nil
}
