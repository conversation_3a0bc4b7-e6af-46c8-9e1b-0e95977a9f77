// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: mq/clue.proto

package mq

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 文件分类
type FileCategory int32

const (
	FileCategory_FC_UNKNOWN  FileCategory = 0
	FileCategory_BLACK       FileCategory = 1
	FileCategory_WHITE       FileCategory = 2
	FileCategory_GRAY        FileCategory = 3
	FileCategory_QUASI_WHITE FileCategory = 4
)

// Enum value maps for FileCategory.
var (
	FileCategory_name = map[int32]string{
		0: "FC_UNKNOWN",
		1: "BLACK",
		2: "WHITE",
		3: "GRAY",
		4: "QUASI_WHITE",
	}
	FileCategory_value = map[string]int32{
		"FC_UNKNOWN":  0,
		"BLACK":       1,
		"WHITE":       2,
		"GRAY":        3,
		"QUASI_WHITE": 4,
	}
)

func (x FileCategory) Enum() *FileCategory {
	p := new(FileCategory)
	*p = x
	return p
}

func (x FileCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_clue_proto_enumTypes[0].Descriptor()
}

func (FileCategory) Type() protoreflect.EnumType {
	return &file_mq_clue_proto_enumTypes[0]
}

func (x FileCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileCategory.Descriptor instead.
func (FileCategory) EnumDescriptor() ([]byte, []int) {
	return file_mq_clue_proto_rawDescGZIP(), []int{0}
}

type ClueDetectEngine int32

const (
	ClueDetectEngine_ENGINE_UNKNOWN       ClueDetectEngine = 0 // 未知
	ClueDetectEngine_AGENT_STATIC         ClueDetectEngine = 1 // 客户端静态检测引擎
	ClueDetectEngine_AGENT_MACHINE_LEARN  ClueDetectEngine = 2 // 客户端机器学习
	ClueDetectEngine_SERVER_STATIC        ClueDetectEngine = 3 // 服务端静态检测引擎，情报服务-内部情报库
	ClueDetectEngine_SERVER_MACHINE_LEARN ClueDetectEngine = 4 // 服务端机器学习，claimAv
	ClueDetectEngine_SERVER_PE            ClueDetectEngine = 5 // 可执行文件检测引擎，ai-pe
	ClueDetectEngine_SERVER_DOCUMENT      ClueDetectEngine = 6 // 文档检测引擎，ai
	ClueDetectEngine_SERVER_SANDBOX       ClueDetectEngine = 7 // 沙箱检测引擎，本地沙箱
	ClueDetectEngine_CLOUD_STATIC         ClueDetectEngine = 8 // 云端静态检测引擎，情报服务-网防云情报库
	ClueDetectEngine_CLOUD_SANDBOX        ClueDetectEngine = 9 // 云沙箱检测引擎，网防云沙箱
)

// Enum value maps for ClueDetectEngine.
var (
	ClueDetectEngine_name = map[int32]string{
		0: "ENGINE_UNKNOWN",
		1: "AGENT_STATIC",
		2: "AGENT_MACHINE_LEARN",
		3: "SERVER_STATIC",
		4: "SERVER_MACHINE_LEARN",
		5: "SERVER_PE",
		6: "SERVER_DOCUMENT",
		7: "SERVER_SANDBOX",
		8: "CLOUD_STATIC",
		9: "CLOUD_SANDBOX",
	}
	ClueDetectEngine_value = map[string]int32{
		"ENGINE_UNKNOWN":       0,
		"AGENT_STATIC":         1,
		"AGENT_MACHINE_LEARN":  2,
		"SERVER_STATIC":        3,
		"SERVER_MACHINE_LEARN": 4,
		"SERVER_PE":            5,
		"SERVER_DOCUMENT":      6,
		"SERVER_SANDBOX":       7,
		"CLOUD_STATIC":         8,
		"CLOUD_SANDBOX":        9,
	}
)

func (x ClueDetectEngine) Enum() *ClueDetectEngine {
	p := new(ClueDetectEngine)
	*p = x
	return p
}

func (x ClueDetectEngine) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClueDetectEngine) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_clue_proto_enumTypes[1].Descriptor()
}

func (ClueDetectEngine) Type() protoreflect.EnumType {
	return &file_mq_clue_proto_enumTypes[1]
}

func (x ClueDetectEngine) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClueDetectEngine.Descriptor instead.
func (ClueDetectEngine) EnumDescriptor() ([]byte, []int) {
	return file_mq_clue_proto_rawDescGZIP(), []int{1}
}

// 外联告警线索
type OutreachClueInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId          string                 `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`                               // 主机唯一标识
	UniqueFlag         string                 `protobuf:"bytes,2,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`                            // 线索唯一标识
	Level              int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`                                                       // 风险等级 1.低风险 2.中风险 3.高风险
	RiskType           int32                  `protobuf:"varint,4,opt,name=risk_type,json=riskType,proto3" json:"risk_type,omitempty"`                                 // 威胁类型
	Source             int32                  `protobuf:"varint,5,opt,name=source,proto3" json:"source,omitempty"`                                                     // 线索来源  1.命中共有情报 2.命中私有情报
	OutreachType       int32                  `protobuf:"varint,6,opt,name=outreach_type,json=outreachType,proto3" json:"outreach_type,omitempty"`                     // 外联类型 1.ip 2.域名
	LocalIp            string                 `protobuf:"bytes,7,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`                                     // 内网IP，可能没有
	LocalPort          int32                  `protobuf:"varint,8,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`                              // 内网端口，可能没有
	RemoteIp           string                 `protobuf:"bytes,9,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`                                  // 外联IP
	RemotePort         int32                  `protobuf:"varint,10,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`                          // 外联端口
	Domain             string                 `protobuf:"bytes,11,opt,name=domain,proto3" json:"domain,omitempty"`                                                     // 外联域名
	IpVer              string                 `protobuf:"bytes,12,opt,name=ip_ver,json=ipVer,proto3" json:"ip_ver,omitempty"`                                          // ipv4 or ipv6
	Protocol           string                 `protobuf:"bytes,13,opt,name=protocol,proto3" json:"protocol,omitempty"`                                                 // 协议 udp or tcp
	Tag                string                 `protobuf:"bytes,14,opt,name=tag,proto3" json:"tag,omitempty"`                                                           // 标签
	Apt                int32                  `protobuf:"varint,15,opt,name=apt,proto3" json:"apt,omitempty"`                                                          // 是否命中APT 1.有 2.没有
	MaliciousCode      string                 `protobuf:"bytes,16,opt,name=malicious_code,json=maliciousCode,proto3" json:"malicious_code,omitempty"`                  // 恶意代码家族
	Confidence         int32                  `protobuf:"varint,17,opt,name=confidence,proto3" json:"confidence,omitempty"`                                            // 置信度
	Direction          string                 `protobuf:"bytes,18,opt,name=direction,proto3" json:"direction,omitempty"`                                               // 定向攻击类型
	Pid                int32                  `protobuf:"varint,19,opt,name=pid,proto3" json:"pid,omitempty"`                                                          // PID
	ProcName           string                 `protobuf:"bytes,20,opt,name=proc_name,json=procName,proto3" json:"proc_name,omitempty"`                                 // 进程名
	ProcPath           string                 `protobuf:"bytes,21,opt,name=proc_path,json=procPath,proto3" json:"proc_path,omitempty"`                                 // 进程路径
	ProcStartTime      int64                  `protobuf:"varint,22,opt,name=proc_start_time,json=procStartTime,proto3" json:"proc_start_time,omitempty"`               // 进程启动时间
	ProcMd5            string                 `protobuf:"bytes,23,opt,name=proc_md5,json=procMd5,proto3" json:"proc_md5,omitempty"`                                    // 进程md5
	ProcCommand        string                 `protobuf:"bytes,24,opt,name=proc_command,json=procCommand,proto3" json:"proc_command,omitempty"`                        // 进程命令行信息
	ProcUserPermission string                 `protobuf:"bytes,25,opt,name=proc_user_permission,json=procUserPermission,proto3" json:"proc_user_permission,omitempty"` // 用户权限
	ProcUser           string                 `protobuf:"bytes,26,opt,name=proc_user,json=procUser,proto3" json:"proc_user,omitempty"`                                 // 进程所属用户
	SignatureInfo      string                 `protobuf:"bytes,27,opt,name=signature_info,json=signatureInfo,proto3" json:"signature_info,omitempty"`                  // 进程签名信息JSON后的字符串
	RootProcInfo       string                 `protobuf:"bytes,28,opt,name=root_proc_info,json=rootProcInfo,proto3" json:"root_proc_info,omitempty"`                   // 根进程信息，JSON格式数据
	CurrentProcessInfo string                 `protobuf:"bytes,29,opt,name=current_process_info,json=currentProcessInfo,proto3" json:"current_process_info,omitempty"` // 进程信息，JSON格式数据
	ParentProcessInfo  string                 `protobuf:"bytes,30,opt,name=parent_process_info,json=parentProcessInfo,proto3" json:"parent_process_info,omitempty"`    // 父进程信息，JSON格式数据
	AptStr             string                 `protobuf:"bytes,31,opt,name=apt_str,json=aptStr,proto3" json:"apt_str,omitempty"`                                       // APT组织(记录V01私有库数据)
	BlackMarket        string                 `protobuf:"bytes,32,opt,name=black_market,json=blackMarket,proto3" json:"black_market,omitempty"`                        // 黑灰产组织(记录V01私有库数据)
	Virus              string                 `protobuf:"bytes,33,opt,name=virus,proto3" json:"virus,omitempty"`                                                       // 病毒家族(记录V01私有库数据)
	UniqueFlagMd5      string                 `protobuf:"bytes,34,opt,name=unique_flag_md5,json=uniqueFlagMd5,proto3" json:"unique_flag_md5,omitempty"`                // unique_flag md5
	ClueKey            string                 `protobuf:"bytes,35,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`                                    // 主机线索唯一值
	RootPid            int64                  `protobuf:"varint,36,opt,name=root_pid,json=rootPid,proto3" json:"root_pid,omitempty"`                                   // 根进程ID
	RootStartTime      int64                  `protobuf:"varint,37,opt,name=root_start_time,json=rootStartTime,proto3" json:"root_start_time,omitempty"`               // 根程启动时间
	PidStartTime       int64                  `protobuf:"varint,38,opt,name=pid_start_time,json=pidStartTime,proto3" json:"pid_start_time,omitempty"`                  // 当前进程启动时间
	HostName           string                 `protobuf:"bytes,39,opt,name=host_name,json=hostName,proto3" json:"host_name,omitempty"`                                 // 主机名
	Euid               int64                  `protobuf:"varint,40,opt,name=euid,proto3" json:"euid,omitempty"`                                                        // 进程的euid
	GroupName          string                 `protobuf:"bytes,41,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`                              // 主机分组名
	Os                 string                 `protobuf:"bytes,42,opt,name=os,proto3" json:"os,omitempty"`                                                             // 操作系统名称
	OsType             int32                  `protobuf:"varint,43,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`                                      // 系统类型 1 windows, 2 linux
	HostIp             string                 `protobuf:"bytes,44,opt,name=host_ip,json=hostIp,proto3" json:"host_ip,omitempty"`                                       // Agent管理主机IP
	ClientVersion      string                 `protobuf:"bytes,45,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`                  // 终端版本信息
	DiscoverTime       *timestamppb.Timestamp `protobuf:"bytes,46,opt,name=discover_time,json=discoverTime,proto3" json:"discover_time,omitempty"`                     // 客户端发现时间
	CreateTime         *timestamppb.Timestamp `protobuf:"bytes,47,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                           // 创建时间
}

func (x *OutreachClueInfo) Reset() {
	*x = OutreachClueInfo{}
	mi := &file_mq_clue_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachClueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachClueInfo) ProtoMessage() {}

func (x *OutreachClueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mq_clue_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachClueInfo.ProtoReflect.Descriptor instead.
func (*OutreachClueInfo) Descriptor() ([]byte, []int) {
	return file_mq_clue_proto_rawDescGZIP(), []int{0}
}

func (x *OutreachClueInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *OutreachClueInfo) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *OutreachClueInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *OutreachClueInfo) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *OutreachClueInfo) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *OutreachClueInfo) GetOutreachType() int32 {
	if x != nil {
		return x.OutreachType
	}
	return 0
}

func (x *OutreachClueInfo) GetLocalIp() string {
	if x != nil {
		return x.LocalIp
	}
	return ""
}

func (x *OutreachClueInfo) GetLocalPort() int32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

func (x *OutreachClueInfo) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *OutreachClueInfo) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *OutreachClueInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *OutreachClueInfo) GetIpVer() string {
	if x != nil {
		return x.IpVer
	}
	return ""
}

func (x *OutreachClueInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *OutreachClueInfo) GetTag() string {
	if x != nil {
		return x.Tag
	}
	return ""
}

func (x *OutreachClueInfo) GetApt() int32 {
	if x != nil {
		return x.Apt
	}
	return 0
}

func (x *OutreachClueInfo) GetMaliciousCode() string {
	if x != nil {
		return x.MaliciousCode
	}
	return ""
}

func (x *OutreachClueInfo) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *OutreachClueInfo) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

func (x *OutreachClueInfo) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *OutreachClueInfo) GetProcName() string {
	if x != nil {
		return x.ProcName
	}
	return ""
}

func (x *OutreachClueInfo) GetProcPath() string {
	if x != nil {
		return x.ProcPath
	}
	return ""
}

func (x *OutreachClueInfo) GetProcStartTime() int64 {
	if x != nil {
		return x.ProcStartTime
	}
	return 0
}

func (x *OutreachClueInfo) GetProcMd5() string {
	if x != nil {
		return x.ProcMd5
	}
	return ""
}

func (x *OutreachClueInfo) GetProcCommand() string {
	if x != nil {
		return x.ProcCommand
	}
	return ""
}

func (x *OutreachClueInfo) GetProcUserPermission() string {
	if x != nil {
		return x.ProcUserPermission
	}
	return ""
}

func (x *OutreachClueInfo) GetProcUser() string {
	if x != nil {
		return x.ProcUser
	}
	return ""
}

func (x *OutreachClueInfo) GetSignatureInfo() string {
	if x != nil {
		return x.SignatureInfo
	}
	return ""
}

func (x *OutreachClueInfo) GetRootProcInfo() string {
	if x != nil {
		return x.RootProcInfo
	}
	return ""
}

func (x *OutreachClueInfo) GetCurrentProcessInfo() string {
	if x != nil {
		return x.CurrentProcessInfo
	}
	return ""
}

func (x *OutreachClueInfo) GetParentProcessInfo() string {
	if x != nil {
		return x.ParentProcessInfo
	}
	return ""
}

func (x *OutreachClueInfo) GetAptStr() string {
	if x != nil {
		return x.AptStr
	}
	return ""
}

func (x *OutreachClueInfo) GetBlackMarket() string {
	if x != nil {
		return x.BlackMarket
	}
	return ""
}

func (x *OutreachClueInfo) GetVirus() string {
	if x != nil {
		return x.Virus
	}
	return ""
}

func (x *OutreachClueInfo) GetUniqueFlagMd5() string {
	if x != nil {
		return x.UniqueFlagMd5
	}
	return ""
}

func (x *OutreachClueInfo) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *OutreachClueInfo) GetRootPid() int64 {
	if x != nil {
		return x.RootPid
	}
	return 0
}

func (x *OutreachClueInfo) GetRootStartTime() int64 {
	if x != nil {
		return x.RootStartTime
	}
	return 0
}

func (x *OutreachClueInfo) GetPidStartTime() int64 {
	if x != nil {
		return x.PidStartTime
	}
	return 0
}

func (x *OutreachClueInfo) GetHostName() string {
	if x != nil {
		return x.HostName
	}
	return ""
}

func (x *OutreachClueInfo) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *OutreachClueInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *OutreachClueInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *OutreachClueInfo) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

func (x *OutreachClueInfo) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *OutreachClueInfo) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *OutreachClueInfo) GetDiscoverTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscoverTime
	}
	return nil
}

func (x *OutreachClueInfo) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// 文件告警线索
type FileClueInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId              string                 `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`                                             // 主机唯一标识
	UniqueFlag             string                 `protobuf:"bytes,2,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`                                          // 线索唯一标识
	Level                  int32                  `protobuf:"varint,3,opt,name=level,proto3" json:"level,omitempty"`                                                                     // 风险等级 1.低风险 2.中风险 3.高风险
	RiskType               int32                  `protobuf:"varint,4,opt,name=risk_type,json=riskType,proto3" json:"risk_type,omitempty"`                                               // 威胁类型
	FilePath               string                 `protobuf:"bytes,5,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                                                // 文件路径
	FileName               string                 `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                                                // 文件名
	FileCategory           FileCategory           `protobuf:"varint,7,opt,name=file_category,json=fileCategory,proto3,enum=mq.FileCategory" json:"file_category,omitempty"`              // 文件分类
	CheckEngines           []ClueDetectEngine     `protobuf:"varint,8,rep,packed,name=check_engines,json=checkEngines,proto3,enum=mq.ClueDetectEngine" json:"check_engines,omitempty"`   // 检测引擎
	Tag                    []string               `protobuf:"bytes,9,rep,name=tag,proto3" json:"tag,omitempty"`                                                                          // 标签
	Apt                    int32                  `protobuf:"varint,10,opt,name=apt,proto3" json:"apt,omitempty"`                                                                        // 是否有apt情报 1.没有 2.有
	Md5                    string                 `protobuf:"bytes,11,opt,name=md5,proto3" json:"md5,omitempty"`                                                                         // 文件md5
	Sha256                 string                 `protobuf:"bytes,12,opt,name=sha256,proto3" json:"sha256,omitempty"`                                                                   // 文件sha256
	Sha1                   string                 `protobuf:"bytes,13,opt,name=sha1,proto3" json:"sha1,omitempty"`                                                                       // 文件sha1
	AccessAt               int64                  `protobuf:"varint,14,opt,name=access_at,json=accessAt,proto3" json:"access_at,omitempty"`                                              // 访问文件时间
	ModifyAt               int64                  `protobuf:"varint,15,opt,name=modify_at,json=modifyAt,proto3" json:"modify_at,omitempty"`                                              // 文件修改时间
	CreateAt               int64                  `protobuf:"varint,16,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`                                              // 文件创建时间
	StMode                 string                 `protobuf:"bytes,17,opt,name=st_mode,json=stMode,proto3" json:"st_mode,omitempty"`                                                     // 文件权限，ge: rwxr-xr--*
	FileSize               int64                  `protobuf:"varint,18,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                                              // 文件大小，单位字节
	FilePermission         string                 `protobuf:"bytes,19,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"`                             // 文件权限
	FileUser               string                 `protobuf:"bytes,20,opt,name=file_user,json=fileUser,proto3" json:"file_user,omitempty"`                                               // 文件用户
	SignatureInfo          string                 `protobuf:"bytes,21,opt,name=signature_info,json=signatureInfo,proto3" json:"signature_info,omitempty"`                                // 文件签名信息
	IsSandbox              int32                  `protobuf:"varint,22,opt,name=is_sandbox,json=isSandbox,proto3" json:"is_sandbox,omitempty"`                                           // 是否经过了沙箱检测 1.没有 2.有
	FileVersion            string                 `protobuf:"bytes,23,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`                                      // 文件版本
	FileVendor             string                 `protobuf:"bytes,24,opt,name=file_vendor,json=fileVendor,proto3" json:"file_vendor,omitempty"`                                         // 文件厂商
	Confidence             int32                  `protobuf:"varint,25,opt,name=confidence,proto3" json:"confidence,omitempty"`                                                          // 置信度
	FileThreadSource       int32                  `protobuf:"varint,26,opt,name=file_thread_source,json=fileThreadSource,proto3" json:"file_thread_source,omitempty"`                    // 文件线索来源 1.agent 2.server
	MaliciousTypes         []int32                `protobuf:"varint,27,rep,packed,name=malicious_types,json=maliciousTypes,proto3" json:"malicious_types,omitempty"`                     // 可疑类型
	MaliciousDde           []string               `protobuf:"bytes,28,rep,name=malicious_dde,json=maliciousDde,proto3" json:"malicious_dde,omitempty"`                                   // 可疑dde代码
	MaliciousVba           []string               `protobuf:"bytes,29,rep,name=malicious_vba,json=maliciousVba,proto3" json:"malicious_vba,omitempty"`                                   // 可疑vba代码
	MaliciousLnkTargetPath string                 `protobuf:"bytes,30,opt,name=malicious_lnk_target_path,json=maliciousLnkTargetPath,proto3" json:"malicious_lnk_target_path,omitempty"` // 可疑lnk目标路径
	MaliciousLnkWorkingDir string                 `protobuf:"bytes,31,opt,name=malicious_lnk_working_dir,json=maliciousLnkWorkingDir,proto3" json:"malicious_lnk_working_dir,omitempty"` // 可疑lnk文件工作路径
	MaliciousLnkCmdLine    string                 `protobuf:"bytes,32,opt,name=malicious_lnk_cmd_line,json=maliciousLnkCmdLine,proto3" json:"malicious_lnk_cmd_line,omitempty"`          // 可疑lnk文件命令行参数
	MaliciousLnkIconPath   string                 `protobuf:"bytes,33,opt,name=malicious_lnk_icon_path,json=maliciousLnkIconPath,proto3" json:"malicious_lnk_icon_path,omitempty"`       // 可疑lnk图标路径
	MaliciousUrl           string                 `protobuf:"bytes,34,opt,name=malicious_url,json=maliciousUrl,proto3" json:"malicious_url,omitempty"`                                   // 可疑图标url
	SlRuleName             string                 `protobuf:"bytes,35,opt,name=sl_rule_name,json=slRuleName,proto3" json:"sl_rule_name,omitempty"`                                       // 扫雷规则名
	SlRuleDetail           string                 `protobuf:"bytes,36,opt,name=sl_rule_detail,json=slRuleDetail,proto3" json:"sl_rule_detail,omitempty"`                                 // 扫雷规则详情
	DiscoverTime           *timestamppb.Timestamp `protobuf:"bytes,37,opt,name=discover_time,json=discoverTime,proto3" json:"discover_time,omitempty"`                                   // 客户端发现时间
}

func (x *FileClueInfo) Reset() {
	*x = FileClueInfo{}
	mi := &file_mq_clue_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileClueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileClueInfo) ProtoMessage() {}

func (x *FileClueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_mq_clue_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileClueInfo.ProtoReflect.Descriptor instead.
func (*FileClueInfo) Descriptor() ([]byte, []int) {
	return file_mq_clue_proto_rawDescGZIP(), []int{1}
}

func (x *FileClueInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *FileClueInfo) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *FileClueInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *FileClueInfo) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *FileClueInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileClueInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileClueInfo) GetFileCategory() FileCategory {
	if x != nil {
		return x.FileCategory
	}
	return FileCategory_FC_UNKNOWN
}

func (x *FileClueInfo) GetCheckEngines() []ClueDetectEngine {
	if x != nil {
		return x.CheckEngines
	}
	return nil
}

func (x *FileClueInfo) GetTag() []string {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *FileClueInfo) GetApt() int32 {
	if x != nil {
		return x.Apt
	}
	return 0
}

func (x *FileClueInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileClueInfo) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileClueInfo) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *FileClueInfo) GetAccessAt() int64 {
	if x != nil {
		return x.AccessAt
	}
	return 0
}

func (x *FileClueInfo) GetModifyAt() int64 {
	if x != nil {
		return x.ModifyAt
	}
	return 0
}

func (x *FileClueInfo) GetCreateAt() int64 {
	if x != nil {
		return x.CreateAt
	}
	return 0
}

func (x *FileClueInfo) GetStMode() string {
	if x != nil {
		return x.StMode
	}
	return ""
}

func (x *FileClueInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileClueInfo) GetFilePermission() string {
	if x != nil {
		return x.FilePermission
	}
	return ""
}

func (x *FileClueInfo) GetFileUser() string {
	if x != nil {
		return x.FileUser
	}
	return ""
}

func (x *FileClueInfo) GetSignatureInfo() string {
	if x != nil {
		return x.SignatureInfo
	}
	return ""
}

func (x *FileClueInfo) GetIsSandbox() int32 {
	if x != nil {
		return x.IsSandbox
	}
	return 0
}

func (x *FileClueInfo) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *FileClueInfo) GetFileVendor() string {
	if x != nil {
		return x.FileVendor
	}
	return ""
}

func (x *FileClueInfo) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *FileClueInfo) GetFileThreadSource() int32 {
	if x != nil {
		return x.FileThreadSource
	}
	return 0
}

func (x *FileClueInfo) GetMaliciousTypes() []int32 {
	if x != nil {
		return x.MaliciousTypes
	}
	return nil
}

func (x *FileClueInfo) GetMaliciousDde() []string {
	if x != nil {
		return x.MaliciousDde
	}
	return nil
}

func (x *FileClueInfo) GetMaliciousVba() []string {
	if x != nil {
		return x.MaliciousVba
	}
	return nil
}

func (x *FileClueInfo) GetMaliciousLnkTargetPath() string {
	if x != nil {
		return x.MaliciousLnkTargetPath
	}
	return ""
}

func (x *FileClueInfo) GetMaliciousLnkWorkingDir() string {
	if x != nil {
		return x.MaliciousLnkWorkingDir
	}
	return ""
}

func (x *FileClueInfo) GetMaliciousLnkCmdLine() string {
	if x != nil {
		return x.MaliciousLnkCmdLine
	}
	return ""
}

func (x *FileClueInfo) GetMaliciousLnkIconPath() string {
	if x != nil {
		return x.MaliciousLnkIconPath
	}
	return ""
}

func (x *FileClueInfo) GetMaliciousUrl() string {
	if x != nil {
		return x.MaliciousUrl
	}
	return ""
}

func (x *FileClueInfo) GetSlRuleName() string {
	if x != nil {
		return x.SlRuleName
	}
	return ""
}

func (x *FileClueInfo) GetSlRuleDetail() string {
	if x != nil {
		return x.SlRuleDetail
	}
	return ""
}

func (x *FileClueInfo) GetDiscoverTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DiscoverTime
	}
	return nil
}

var File_mq_clue_proto protoreflect.FileDescriptor

var file_mq_clue_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x6d, 0x71, 0x2f, 0x63, 0x6c, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x02, 0x6d, 0x71, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf3, 0x0b, 0x0a, 0x10, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x70, 0x56, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x10, 0x0a, 0x03,
	0x74, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x10,
	0x0a, 0x03, 0x61, 0x70, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x70, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f,
	0x63, 0x4d, 0x64, 0x35, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x63, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x55, 0x73, 0x65, 0x72, 0x50,
	0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x63, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a,
	0x0e, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x6f, 0x6f, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x14, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x12, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x74, 0x53, 0x74, 0x72, 0x12, 0x21,
	0x0a, 0x0c, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x18, 0x20,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x61, 0x72, 0x6b, 0x65,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x69, 0x72, 0x75, 0x73, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x69, 0x72, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x4d, 0x64, 0x35, 0x12,
	0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f,
	0x6f, 0x74, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f,
	0x6f, 0x74, 0x50, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x72, 0x6f, 0x6f, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x70, 0x69, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x26, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x70, 0x69, 0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x65, 0x75, 0x69, 0x64, 0x18, 0x28, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x65, 0x75, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x6f, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x2b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68,
	0x6f, 0x73, 0x74, 0x49, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0d,
	0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0c, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x2f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xbd, 0x0a, 0x0a, 0x0c, 0x46,
	0x69, 0x6c, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x10, 0x2e, 0x6d, 0x71, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x39, 0x0a, 0x0d, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6d, 0x71, 0x2e, 0x43, 0x6c, 0x75, 0x65,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x0c, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x61,
	0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x70, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x61, 0x70, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x1b, 0x0a, 0x09,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x41, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x69, 0x73, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x64, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x61, 0x6c, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x05,
	0x52, 0x0e, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x64, 0x64,
	0x65, 0x18, 0x1c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x44, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x5f, 0x76, 0x62, 0x61, 0x18, 0x1d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61,
	0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x56, 0x62, 0x61, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x61,
	0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6d,
	0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x69, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x72,
	0x12, 0x33, 0x0a, 0x16, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x6c, 0x6e,
	0x6b, 0x5f, 0x63, 0x6d, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x43, 0x6d,
	0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75,
	0x73, 0x4c, 0x6e, 0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d,
	0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x55, 0x72,
	0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x6c, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6c, 0x52, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6c, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6c, 0x52,
	0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3f, 0x0a, 0x0d, 0x64, 0x69, 0x73,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0c, 0x64, 0x69,
	0x73, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0x4f, 0x0a, 0x0c, 0x46, 0x69,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x43,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4c,
	0x41, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x02,
	0x12, 0x08, 0x0a, 0x04, 0x47, 0x52, 0x41, 0x59, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b, 0x51, 0x55,
	0x41, 0x53, 0x49, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x04, 0x2a, 0xdb, 0x01, 0x0a, 0x10,
	0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x12, 0x12, 0x0a, 0x0e, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x49, 0x43, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f,
	0x4d, 0x41, 0x43, 0x48, 0x49, 0x4e, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x52, 0x4e, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43,
	0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x4d, 0x41, 0x43,
	0x48, 0x49, 0x4e, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x52, 0x4e, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09,
	0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x50, 0x45, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x06,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x41, 0x4e, 0x44, 0x42,
	0x4f, 0x58, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x49, 0x43, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f,
	0x53, 0x41, 0x4e, 0x44, 0x42, 0x4f, 0x58, 0x10, 0x09, 0x42, 0x27, 0x5a, 0x25, 0x67, 0x69, 0x74,
	0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x6d, 0x71, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mq_clue_proto_rawDescOnce sync.Once
	file_mq_clue_proto_rawDescData = file_mq_clue_proto_rawDesc
)

func file_mq_clue_proto_rawDescGZIP() []byte {
	file_mq_clue_proto_rawDescOnce.Do(func() {
		file_mq_clue_proto_rawDescData = protoimpl.X.CompressGZIP(file_mq_clue_proto_rawDescData)
	})
	return file_mq_clue_proto_rawDescData
}

var file_mq_clue_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_mq_clue_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_mq_clue_proto_goTypes = []any{
	(FileCategory)(0),             // 0: mq.FileCategory
	(ClueDetectEngine)(0),         // 1: mq.ClueDetectEngine
	(*OutreachClueInfo)(nil),      // 2: mq.OutreachClueInfo
	(*FileClueInfo)(nil),          // 3: mq.FileClueInfo
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_mq_clue_proto_depIdxs = []int32{
	4, // 0: mq.OutreachClueInfo.discover_time:type_name -> google.protobuf.Timestamp
	4, // 1: mq.OutreachClueInfo.create_time:type_name -> google.protobuf.Timestamp
	0, // 2: mq.FileClueInfo.file_category:type_name -> mq.FileCategory
	1, // 3: mq.FileClueInfo.check_engines:type_name -> mq.ClueDetectEngine
	4, // 4: mq.FileClueInfo.discover_time:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_mq_clue_proto_init() }
func file_mq_clue_proto_init() {
	if File_mq_clue_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mq_clue_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mq_clue_proto_goTypes,
		DependencyIndexes: file_mq_clue_proto_depIdxs,
		EnumInfos:         file_mq_clue_proto_enumTypes,
		MessageInfos:      file_mq_clue_proto_msgTypes,
	}.Build()
	File_mq_clue_proto = out.File
	file_mq_clue_proto_rawDesc = nil
	file_mq_clue_proto_goTypes = nil
	file_mq_clue_proto_depIdxs = nil
}
