// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: mq/mq.proto

package mq

import (
	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Agent上报的数据
// 1、通信服务除了原始数据之外进行简单的封装，然后将按照消息规划方案分发到MQ。
// 2、非业务数据通过nats头部透传，例如：tenant_id、trace_id等。
// 3、消息规划 http://wiki.in.anxinsec.com/pages/viewpage.action?pageId=75763829
type AgentPacket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmdId     agent.Command          `protobuf:"varint,1,opt,name=cmd_id,json=cmdId,proto3,enum=agent.Command" json:"cmd_id,omitempty"` // 消息类型
	Ts        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=ts,proto3" json:"ts,omitempty"`                                        // 通信服务接收到agent上报数据的时间
	MachineId string                 `protobuf:"bytes,3,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`         // 设备信息
	Data      []byte                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`                                    // 消息内容
	OsType    int32                  `protobuf:"varint,5,opt,name=os_type,json=osType,proto3" json:"os_type,omitempty"`                 // 操作系统类型
}

func (x *AgentPacket) Reset() {
	*x = AgentPacket{}
	mi := &file_mq_mq_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentPacket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentPacket) ProtoMessage() {}

func (x *AgentPacket) ProtoReflect() protoreflect.Message {
	mi := &file_mq_mq_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentPacket.ProtoReflect.Descriptor instead.
func (*AgentPacket) Descriptor() ([]byte, []int) {
	return file_mq_mq_proto_rawDescGZIP(), []int{0}
}

func (x *AgentPacket) GetCmdId() agent.Command {
	if x != nil {
		return x.CmdId
	}
	return agent.Command(0)
}

func (x *AgentPacket) GetTs() *timestamppb.Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *AgentPacket) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AgentPacket) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *AgentPacket) GetOsType() int32 {
	if x != nil {
		return x.OsType
	}
	return 0
}

var File_mq_mq_proto protoreflect.FileDescriptor

var file_mq_mq_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x6d, 0x71, 0x2f, 0x6d, 0x71, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x6d,
	0x71, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xac, 0x01, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x25, 0x0a, 0x06, 0x63, 0x6d, 0x64, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x52, 0x05, 0x63, 0x6d, 0x64, 0x49, 0x64, 0x12, 0x2a,
	0x0a, 0x02, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a,
	0x07, 0x6f, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6f, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x27, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6d, 0x71, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mq_mq_proto_rawDescOnce sync.Once
	file_mq_mq_proto_rawDescData = file_mq_mq_proto_rawDesc
)

func file_mq_mq_proto_rawDescGZIP() []byte {
	file_mq_mq_proto_rawDescOnce.Do(func() {
		file_mq_mq_proto_rawDescData = protoimpl.X.CompressGZIP(file_mq_mq_proto_rawDescData)
	})
	return file_mq_mq_proto_rawDescData
}

var file_mq_mq_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_mq_mq_proto_goTypes = []any{
	(*AgentPacket)(nil),           // 0: mq.AgentPacket
	(agent.Command)(0),            // 1: agent.Command
	(*timestamppb.Timestamp)(nil), // 2: google.protobuf.Timestamp
}
var file_mq_mq_proto_depIdxs = []int32{
	1, // 0: mq.AgentPacket.cmd_id:type_name -> agent.Command
	2, // 1: mq.AgentPacket.ts:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_mq_mq_proto_init() }
func file_mq_mq_proto_init() {
	if File_mq_mq_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mq_mq_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mq_mq_proto_goTypes,
		DependencyIndexes: file_mq_mq_proto_depIdxs,
		MessageInfos:      file_mq_mq_proto_msgTypes,
	}.Build()
	File_mq_mq_proto = out.File
	file_mq_mq_proto_rawDesc = nil
	file_mq_mq_proto_goTypes = nil
	file_mq_mq_proto_depIdxs = nil
}
