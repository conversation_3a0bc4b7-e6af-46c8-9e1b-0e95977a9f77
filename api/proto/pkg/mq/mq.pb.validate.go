// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: mq/mq.proto

package mq

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	agent "git.anxin.com/v01-cluster/vapi/pkg/agent"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = agent.Command(0)
)

// Validate checks the field values on AgentPacket with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentPacket) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentPacket with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentPacketMultiError, or
// nil if none found.
func (m *AgentPacket) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentPacket) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CmdId

	if all {
		switch v := interface{}(m.GetTs()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentPacketValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentPacketValidationError{
					field:  "Ts",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTs()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentPacketValidationError{
				field:  "Ts",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MachineId

	// no validation rules for Data

	// no validation rules for OsType

	if len(errors) > 0 {
		return AgentPacketMultiError(errors)
	}

	return nil
}

// AgentPacketMultiError is an error wrapping multiple validation errors
// returned by AgentPacket.ValidateAll() if the designated constraints aren't met.
type AgentPacketMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentPacketMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentPacketMultiError) AllErrors() []error { return m }

// AgentPacketValidationError is the validation error returned by
// AgentPacket.Validate if the designated constraints aren't met.
type AgentPacketValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentPacketValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentPacketValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentPacketValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentPacketValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentPacketValidationError) ErrorName() string { return "AgentPacketValidationError" }

// Error satisfies the builtin error interface
func (e AgentPacketValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentPacket.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentPacketValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentPacketValidationError{}
