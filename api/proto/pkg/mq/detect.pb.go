// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: mq/detect.proto

package mq

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DetectStatus int32

const (
	DetectStatus_DETECT_STATUS_UNKNOWN DetectStatus = 0
	DetectStatus_DETECT_STATUS_SUCC    DetectStatus = 1 // 检测成功
	DetectStatus_DETECT_STATUS_FAIL    DetectStatus = 2 // 检测失败
)

// Enum value maps for DetectStatus.
var (
	DetectStatus_name = map[int32]string{
		0: "DETECT_STATUS_UNKNOWN",
		1: "DETECT_STATUS_SUCC",
		2: "DETECT_STATUS_FAIL",
	}
	DetectStatus_value = map[string]int32{
		"DETECT_STATUS_UNKNOWN": 0,
		"DETECT_STATUS_SUCC":    1,
		"DETECT_STATUS_FAIL":    2,
	}
)

func (x DetectStatus) Enum() *DetectStatus {
	p := new(DetectStatus)
	*p = x
	return p
}

func (x DetectStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_detect_proto_enumTypes[0].Descriptor()
}

func (DetectStatus) Type() protoreflect.EnumType {
	return &file_mq_detect_proto_enumTypes[0]
}

func (x DetectStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectStatus.Descriptor instead.
func (DetectStatus) EnumDescriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{0}
}

// DetectRes 整体检测结果
type DetectRes int32

const (
	DetectRes_DETECT_RESULT_UNKNOWN DetectRes = 0
	DetectRes_DETECT_RESULT_WHITE   DetectRes = 1 // 结果为白
	DetectRes_DETECT_RESULT_BLACK   DetectRes = 2 // 结果为黑
)

// Enum value maps for DetectRes.
var (
	DetectRes_name = map[int32]string{
		0: "DETECT_RESULT_UNKNOWN",
		1: "DETECT_RESULT_WHITE",
		2: "DETECT_RESULT_BLACK",
	}
	DetectRes_value = map[string]int32{
		"DETECT_RESULT_UNKNOWN": 0,
		"DETECT_RESULT_WHITE":   1,
		"DETECT_RESULT_BLACK":   2,
	}
)

func (x DetectRes) Enum() *DetectRes {
	p := new(DetectRes)
	*p = x
	return p
}

func (x DetectRes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectRes) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_detect_proto_enumTypes[1].Descriptor()
}

func (DetectRes) Type() protoreflect.EnumType {
	return &file_mq_detect_proto_enumTypes[1]
}

func (x DetectRes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectRes.Descriptor instead.
func (DetectRes) EnumDescriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{1}
}

type DetectEngine int32

const (
	DetectEngine_UNKNOWN_DETECT       DetectEngine = 0 // 未知
	DetectEngine_AI_DETECT            DetectEngine = 1 // AI检测
	DetectEngine_AIPE_DETECT          DetectEngine = 2 // AIPE检测
	DetectEngine_CLAIMAV_DETECT       DetectEngine = 3 // ClaimAV检测
	DetectEngine_SANDBOX_DETECT       DetectEngine = 4 // 沙箱检测
	DetectEngine_CLOUD_SANDBOX_DETECT DetectEngine = 5 // 云沙箱检测
)

// Enum value maps for DetectEngine.
var (
	DetectEngine_name = map[int32]string{
		0: "UNKNOWN_DETECT",
		1: "AI_DETECT",
		2: "AIPE_DETECT",
		3: "CLAIMAV_DETECT",
		4: "SANDBOX_DETECT",
		5: "CLOUD_SANDBOX_DETECT",
	}
	DetectEngine_value = map[string]int32{
		"UNKNOWN_DETECT":       0,
		"AI_DETECT":            1,
		"AIPE_DETECT":          2,
		"CLAIMAV_DETECT":       3,
		"SANDBOX_DETECT":       4,
		"CLOUD_SANDBOX_DETECT": 5,
	}
)

func (x DetectEngine) Enum() *DetectEngine {
	p := new(DetectEngine)
	*p = x
	return p
}

func (x DetectEngine) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectEngine) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_detect_proto_enumTypes[2].Descriptor()
}

func (DetectEngine) Type() protoreflect.EnumType {
	return &file_mq_detect_proto_enumTypes[2]
}

func (x DetectEngine) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectEngine.Descriptor instead.
func (DetectEngine) EnumDescriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{2}
}

type SandboxRiskType int32

const (
	SandboxRiskType_RISK_TYPE_NULL          SandboxRiskType = 0  // 未检测到风险
	SandboxRiskType_RISK_TYPE_FISHING       SandboxRiskType = 1  // 钓鱼
	SandboxRiskType_RISK_TYPE_WORM          SandboxRiskType = 2  // 蠕虫
	SandboxRiskType_RISK_TYPE_RANSOM        SandboxRiskType = 3  // 勒索
	SandboxRiskType_RISK_TYPE_VIRUS         SandboxRiskType = 4  // 病毒
	SandboxRiskType_RISK_TYPE_BACK_DOOR     SandboxRiskType = 5  // 后门
	SandboxRiskType_RISK_TYPE_BOTNET        SandboxRiskType = 6  // 僵尸网络
	SandboxRiskType_RISK_TYPE_SPY           SandboxRiskType = 7  // 间谍软件
	SandboxRiskType_RISK_TYPE_TROJAN        SandboxRiskType = 8  // 木马
	SandboxRiskType_RISK_TYPE_EXPLOIT       SandboxRiskType = 9  // 漏洞利用
	SandboxRiskType_RISK_TYPE_OTHER         SandboxRiskType = 10 // 其他
	SandboxRiskType_RISK_TYPE_THEFT         SandboxRiskType = 11 // 数据窃取
	SandboxRiskType_RISK_TYPE_MINING        SandboxRiskType = 12 // 挖矿
	SandboxRiskType_RISK_TYPE_REMOTE        SandboxRiskType = 13 // 远控
	SandboxRiskType_RISK_TYPE_WEB_SHELL     SandboxRiskType = 14 // webshell
	SandboxRiskType_RISK_TYPE_RISK_SOFTWARE SandboxRiskType = 15 // 风险软件
	SandboxRiskType_RISK_TYPE_SUSPECTED_APT SandboxRiskType = 16 // 疑似apt
	SandboxRiskType_RISK_TYPE_CS            SandboxRiskType = 17 // CS木马
	SandboxRiskType_RISK_TYPE_OUTLINE       SandboxRiskType = 18 // 外联
)

// Enum value maps for SandboxRiskType.
var (
	SandboxRiskType_name = map[int32]string{
		0:  "RISK_TYPE_NULL",
		1:  "RISK_TYPE_FISHING",
		2:  "RISK_TYPE_WORM",
		3:  "RISK_TYPE_RANSOM",
		4:  "RISK_TYPE_VIRUS",
		5:  "RISK_TYPE_BACK_DOOR",
		6:  "RISK_TYPE_BOTNET",
		7:  "RISK_TYPE_SPY",
		8:  "RISK_TYPE_TROJAN",
		9:  "RISK_TYPE_EXPLOIT",
		10: "RISK_TYPE_OTHER",
		11: "RISK_TYPE_THEFT",
		12: "RISK_TYPE_MINING",
		13: "RISK_TYPE_REMOTE",
		14: "RISK_TYPE_WEB_SHELL",
		15: "RISK_TYPE_RISK_SOFTWARE",
		16: "RISK_TYPE_SUSPECTED_APT",
		17: "RISK_TYPE_CS",
		18: "RISK_TYPE_OUTLINE",
	}
	SandboxRiskType_value = map[string]int32{
		"RISK_TYPE_NULL":          0,
		"RISK_TYPE_FISHING":       1,
		"RISK_TYPE_WORM":          2,
		"RISK_TYPE_RANSOM":        3,
		"RISK_TYPE_VIRUS":         4,
		"RISK_TYPE_BACK_DOOR":     5,
		"RISK_TYPE_BOTNET":        6,
		"RISK_TYPE_SPY":           7,
		"RISK_TYPE_TROJAN":        8,
		"RISK_TYPE_EXPLOIT":       9,
		"RISK_TYPE_OTHER":         10,
		"RISK_TYPE_THEFT":         11,
		"RISK_TYPE_MINING":        12,
		"RISK_TYPE_REMOTE":        13,
		"RISK_TYPE_WEB_SHELL":     14,
		"RISK_TYPE_RISK_SOFTWARE": 15,
		"RISK_TYPE_SUSPECTED_APT": 16,
		"RISK_TYPE_CS":            17,
		"RISK_TYPE_OUTLINE":       18,
	}
)

func (x SandboxRiskType) Enum() *SandboxRiskType {
	p := new(SandboxRiskType)
	*p = x
	return p
}

func (x SandboxRiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SandboxRiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_mq_detect_proto_enumTypes[3].Descriptor()
}

func (SandboxRiskType) Type() protoreflect.EnumType {
	return &file_mq_detect_proto_enumTypes[3]
}

func (x SandboxRiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SandboxRiskType.Descriptor instead.
func (SandboxRiskType) EnumDescriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{3}
}

// 引擎样本检测请求
type SampleDetectFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256   string         `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`                                // 文件sha256
	Md5      string         `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`                                      // 文件md5
	Type     string         `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                    // 文件类型，例如：PY、PE、DOC、BAT等
	Bucket   string         `protobuf:"bytes,4,opt,name=bucket,proto3" json:"bucket,omitempty"`                                // 文件存储桶
	Path     string         `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                                    // 文件路径
	Size     int32          `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`                                   // 文件大小
	Priority int32          `protobuf:"varint,7,opt,name=priority,proto3" json:"priority,omitempty"`                           // 检测优先级，0-100，默认0
	Engines  []DetectEngine `protobuf:"varint,8,rep,packed,name=engines,proto3,enum=mq.DetectEngine" json:"engines,omitempty"` // 指定检测引擎
	Retry    int32          `protobuf:"varint,9,opt,name=retry,proto3" json:"retry,omitempty"`                                 // 检测失败重试次数（主要是调用每个检测引擎接口失败的情况，默认3次，可选）
	Timeout  int32          `protobuf:"varint,10,opt,name=timeout,proto3" json:"timeout,omitempty"`                            // 检测超时时间（单位：秒，调用每个检测引擎接口超时数值，默认3秒，可选）
	// 是否启用并行检测
	// （1）串行检测：会根据 engines 顺序从前向后（数组索引0>1>2>...）依次进行检测，过程中检测到病毒则流程终止。
	// （2）并行检测：会根据 engines 并发进行检测，各个引擎没有依赖关系。
	ParallelDetect bool `protobuf:"varint,11,opt,name=parallel_detect,json=parallelDetect,proto3" json:"parallel_detect,omitempty"`
}

func (x *SampleDetectFile) Reset() {
	*x = SampleDetectFile{}
	mi := &file_mq_detect_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleDetectFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleDetectFile) ProtoMessage() {}

func (x *SampleDetectFile) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleDetectFile.ProtoReflect.Descriptor instead.
func (*SampleDetectFile) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{0}
}

func (x *SampleDetectFile) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *SampleDetectFile) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *SampleDetectFile) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SampleDetectFile) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *SampleDetectFile) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SampleDetectFile) GetSize() int32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *SampleDetectFile) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *SampleDetectFile) GetEngines() []DetectEngine {
	if x != nil {
		return x.Engines
	}
	return nil
}

func (x *SampleDetectFile) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *SampleDetectFile) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *SampleDetectFile) GetParallelDetect() bool {
	if x != nil {
		return x.ParallelDetect
	}
	return false
}

// 引擎样本检测结果
type SampleDetectResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5    string       `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`                             // 文件md5
	Sha256 string       `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                       // 文件sha256
	Bucket string       `protobuf:"bytes,3,opt,name=bucket,proto3" json:"bucket,omitempty"`                       // 文件存储桶
	Path   string       `protobuf:"bytes,4,opt,name=path,proto3" json:"path,omitempty"`                           // 文件路径
	Status DetectStatus `protobuf:"varint,5,opt,name=status,proto3,enum=mq.DetectStatus" json:"status,omitempty"` // 成功或失败
	Result DetectRes    `protobuf:"varint,6,opt,name=result,proto3,enum=mq.DetectRes" json:"result,omitempty"`    // 逐个引擎检测后，最终结果为黑或白
	// Types that are assignable to DetectRes:
	//
	//	*SampleDetectResult_ClamAv
	//	*SampleDetectResult_Ai
	//	*SampleDetectResult_AiPe
	//	*SampleDetectResult_Sandbox
	//	*SampleDetectResult_Wfy
	DetectRes isSampleDetectResult_DetectRes `protobuf_oneof:"detect_res"`
}

func (x *SampleDetectResult) Reset() {
	*x = SampleDetectResult{}
	mi := &file_mq_detect_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SampleDetectResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SampleDetectResult) ProtoMessage() {}

func (x *SampleDetectResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SampleDetectResult.ProtoReflect.Descriptor instead.
func (*SampleDetectResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{1}
}

func (x *SampleDetectResult) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *SampleDetectResult) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *SampleDetectResult) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *SampleDetectResult) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *SampleDetectResult) GetStatus() DetectStatus {
	if x != nil {
		return x.Status
	}
	return DetectStatus_DETECT_STATUS_UNKNOWN
}

func (x *SampleDetectResult) GetResult() DetectRes {
	if x != nil {
		return x.Result
	}
	return DetectRes_DETECT_RESULT_UNKNOWN
}

func (m *SampleDetectResult) GetDetectRes() isSampleDetectResult_DetectRes {
	if m != nil {
		return m.DetectRes
	}
	return nil
}

func (x *SampleDetectResult) GetClamAv() *ClamAvResult {
	if x, ok := x.GetDetectRes().(*SampleDetectResult_ClamAv); ok {
		return x.ClamAv
	}
	return nil
}

func (x *SampleDetectResult) GetAi() *AIResult {
	if x, ok := x.GetDetectRes().(*SampleDetectResult_Ai); ok {
		return x.Ai
	}
	return nil
}

func (x *SampleDetectResult) GetAiPe() *AIPeResult {
	if x, ok := x.GetDetectRes().(*SampleDetectResult_AiPe); ok {
		return x.AiPe
	}
	return nil
}

func (x *SampleDetectResult) GetSandbox() *SandboxResult {
	if x, ok := x.GetDetectRes().(*SampleDetectResult_Sandbox); ok {
		return x.Sandbox
	}
	return nil
}

func (x *SampleDetectResult) GetWfy() *WFYResult {
	if x, ok := x.GetDetectRes().(*SampleDetectResult_Wfy); ok {
		return x.Wfy
	}
	return nil
}

type isSampleDetectResult_DetectRes interface {
	isSampleDetectResult_DetectRes()
}

type SampleDetectResult_ClamAv struct {
	ClamAv *ClamAvResult `protobuf:"bytes,7,opt,name=clam_av,json=clamAv,proto3,oneof"` // clamAv检测结果
}

type SampleDetectResult_Ai struct {
	Ai *AIResult `protobuf:"bytes,8,opt,name=ai,proto3,oneof"` // ai引擎检测结果
}

type SampleDetectResult_AiPe struct {
	AiPe *AIPeResult `protobuf:"bytes,9,opt,name=ai_pe,json=aiPe,proto3,oneof"` // ai_pe检测结果
}

type SampleDetectResult_Sandbox struct {
	Sandbox *SandboxResult `protobuf:"bytes,10,opt,name=sandbox,proto3,oneof"` // 沙箱检测结果
}

type SampleDetectResult_Wfy struct {
	Wfy *WFYResult `protobuf:"bytes,11,opt,name=wfy,proto3,oneof"` // 网防云检测结果
}

func (*SampleDetectResult_ClamAv) isSampleDetectResult_DetectRes() {}

func (*SampleDetectResult_Ai) isSampleDetectResult_DetectRes() {}

func (*SampleDetectResult_AiPe) isSampleDetectResult_DetectRes() {}

func (*SampleDetectResult_Sandbox) isSampleDetectResult_DetectRes() {}

func (*SampleDetectResult_Wfy) isSampleDetectResult_DetectRes() {}

type ClamAvResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VirusName string `protobuf:"bytes,1,opt,name=virus_name,json=virusName,proto3" json:"virus_name,omitempty"` // 病毒名（空表示无病毒）
	Version   string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                      // 引擎版本
}

func (x *ClamAvResult) Reset() {
	*x = ClamAvResult{}
	mi := &file_mq_detect_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClamAvResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClamAvResult) ProtoMessage() {}

func (x *ClamAvResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClamAvResult.ProtoReflect.Descriptor instead.
func (*ClamAvResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{2}
}

func (x *ClamAvResult) GetVirusName() string {
	if x != nil {
		return x.VirusName
	}
	return ""
}

func (x *ClamAvResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type SandboxResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score     int32             `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`                                                         // 沙箱检测分数，风险由低到高0-100
	RiskTypes []SandboxRiskType `protobuf:"varint,2,rep,packed,name=risk_types,json=riskTypes,proto3,enum=mq.SandboxRiskType" json:"risk_types,omitempty"` // 风险类型列表
	TaskId    int32             `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                         // 沙箱检测任务Id
}

func (x *SandboxResult) Reset() {
	*x = SandboxResult{}
	mi := &file_mq_detect_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxResult) ProtoMessage() {}

func (x *SandboxResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxResult.ProtoReflect.Descriptor instead.
func (*SandboxResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{3}
}

func (x *SandboxResult) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SandboxResult) GetRiskTypes() []SandboxRiskType {
	if x != nil {
		return x.RiskTypes
	}
	return nil
}

func (x *SandboxResult) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type AIResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score int32 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"` // ai检测分值，风险由低到高0-100
}

func (x *AIResult) Reset() {
	*x = AIResult{}
	mi := &file_mq_detect_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIResult) ProtoMessage() {}

func (x *AIResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIResult.ProtoReflect.Descriptor instead.
func (*AIResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{4}
}

func (x *AIResult) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type AIPeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Score int32 `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"` // ai检测分值，风险由低到高0-100
}

func (x *AIPeResult) Reset() {
	*x = AIPeResult{}
	mi := &file_mq_detect_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIPeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIPeResult) ProtoMessage() {}

func (x *AIPeResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIPeResult.ProtoReflect.Descriptor instead.
func (*AIPeResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{5}
}

func (x *AIPeResult) GetScore() int32 {
	if x != nil {
		return x.Score
	}
	return 0
}

type WFYResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Confidence      int32    `protobuf:"varint,1,opt,name=confidence,proto3" json:"confidence,omitempty"`                                 // 置信度（low: 1，mid: 2，high: 3）
	RiskLevel       int32    `protobuf:"varint,2,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`                  // 风险级别（low: 1，mid: 2，high: 3）
	ThreatTypes     []int32  `protobuf:"varint,3,rep,packed,name=threat_types,json=threatTypes,proto3" json:"threat_types,omitempty"`     // 风险类型
	MalwareFamilies []string `protobuf:"bytes,4,rep,name=malware_families,json=malwareFamilies,proto3" json:"malware_families,omitempty"` // 病毒家族名称列表
	AptInfo         []string `protobuf:"bytes,5,rep,name=apt_info,json=aptInfo,proto3" json:"apt_info,omitempty"`                         // apt信息
}

func (x *WFYResult) Reset() {
	*x = WFYResult{}
	mi := &file_mq_detect_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WFYResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WFYResult) ProtoMessage() {}

func (x *WFYResult) ProtoReflect() protoreflect.Message {
	mi := &file_mq_detect_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WFYResult.ProtoReflect.Descriptor instead.
func (*WFYResult) Descriptor() ([]byte, []int) {
	return file_mq_detect_proto_rawDescGZIP(), []int{6}
}

func (x *WFYResult) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *WFYResult) GetRiskLevel() int32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *WFYResult) GetThreatTypes() []int32 {
	if x != nil {
		return x.ThreatTypes
	}
	return nil
}

func (x *WFYResult) GetMalwareFamilies() []string {
	if x != nil {
		return x.MalwareFamilies
	}
	return nil
}

func (x *WFYResult) GetAptInfo() []string {
	if x != nil {
		return x.AptInfo
	}
	return nil
}

var File_mq_detect_proto protoreflect.FileDescriptor

var file_mq_detect_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x6d, 0x71, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x02, 0x6d, 0x71, 0x22, 0xb1, 0x02, 0x0a, 0x10, 0x53, 0x61, 0x6d, 0x70, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x07, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x71, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x12, 0x27, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x61, 0x6c, 0x6c, 0x65, 0x6c, 0x5f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6c,
	0x6c, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x22, 0x8f, 0x03, 0x0a, 0x12, 0x53, 0x61,
	0x6d, 0x70, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d,
	0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x6d, 0x71, 0x2e, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x25, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0d, 0x2e, 0x6d, 0x71, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x07, 0x63, 0x6c, 0x61, 0x6d, 0x5f,
	0x61, 0x76, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6d, 0x71, 0x2e, 0x43, 0x6c,
	0x61, 0x6d, 0x41, 0x76, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x48, 0x00, 0x52, 0x06, 0x63, 0x6c,
	0x61, 0x6d, 0x41, 0x76, 0x12, 0x1e, 0x0a, 0x02, 0x61, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0c, 0x2e, 0x6d, 0x71, 0x2e, 0x41, 0x49, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x48, 0x00,
	0x52, 0x02, 0x61, 0x69, 0x12, 0x25, 0x0a, 0x05, 0x61, 0x69, 0x5f, 0x70, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6d, 0x71, 0x2e, 0x41, 0x49, 0x50, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x48, 0x00, 0x52, 0x04, 0x61, 0x69, 0x50, 0x65, 0x12, 0x2d, 0x0a, 0x07, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x6d,
	0x71, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x21, 0x0a, 0x03, 0x77, 0x66,
	0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6d, 0x71, 0x2e, 0x57, 0x46, 0x59,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x48, 0x00, 0x52, 0x03, 0x77, 0x66, 0x79, 0x42, 0x0c, 0x0a,
	0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x22, 0x47, 0x0a, 0x0c, 0x43,
	0x6c, 0x61, 0x6d, 0x41, 0x76, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x76,
	0x69, 0x72, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a, 0x0d, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x12, 0x32, 0x0a, 0x0a, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x13, 0x2e, 0x6d, 0x71, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x20, 0x0a, 0x08, 0x41, 0x49, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x22, 0x0a, 0x0a, 0x41, 0x49,
	0x50, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x22, 0xb3,
	0x01, 0x0a, 0x09, 0x57, 0x46, 0x59, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x68, 0x72, 0x65, 0x61, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x05, 0x52, 0x0b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x29,
	0x0a, 0x10, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x69,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72,
	0x65, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x74,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x2a, 0x59, 0x0a, 0x0c, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x16, 0x0a, 0x12, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a,
	0x58, 0x0a, 0x09, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x12, 0x19, 0x0a, 0x15,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x01,
	0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x02, 0x2a, 0x84, 0x01, 0x0a, 0x0c, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x41, 0x49, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x10, 0x01, 0x12, 0x0f, 0x0a,
	0x0b, 0x41, 0x49, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x10, 0x02, 0x12, 0x12,
	0x0a, 0x0e, 0x43, 0x4c, 0x41, 0x49, 0x4d, 0x41, 0x56, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54,
	0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x41, 0x4e, 0x44, 0x42, 0x4f, 0x58, 0x5f, 0x44, 0x45,
	0x54, 0x45, 0x43, 0x54, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x5f,
	0x53, 0x41, 0x4e, 0x44, 0x42, 0x4f, 0x58, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x10, 0x05,
	0x2a, 0xbc, 0x03, 0x0a, 0x0f, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12,
	0x12, 0x0a, 0x0e, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x4f, 0x52,
	0x4d, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x41, 0x4e, 0x53, 0x4f, 0x4d, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x49, 0x52, 0x55, 0x53, 0x10, 0x04, 0x12, 0x17,
	0x0a, 0x13, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x43, 0x4b,
	0x5f, 0x44, 0x4f, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x4e, 0x45, 0x54, 0x10, 0x06, 0x12, 0x11, 0x0a,
	0x0d, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x50, 0x59, 0x10, 0x07,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52,
	0x4f, 0x4a, 0x41, 0x4e, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x4c, 0x4f, 0x49, 0x54, 0x10, 0x09, 0x12, 0x13, 0x0a,
	0x0f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x54, 0x48, 0x45, 0x46, 0x54, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x12, 0x14, 0x0a,
	0x10, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x54,
	0x45, 0x10, 0x0d, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x53,
	0x4f, 0x46, 0x54, 0x57, 0x41, 0x52, 0x45, 0x10, 0x0f, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45, 0x43, 0x54, 0x45, 0x44,
	0x5f, 0x41, 0x50, 0x54, 0x10, 0x10, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x43, 0x53, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x53, 0x4b,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x12, 0x42,
	0x27, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6d, 0x71, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_mq_detect_proto_rawDescOnce sync.Once
	file_mq_detect_proto_rawDescData = file_mq_detect_proto_rawDesc
)

func file_mq_detect_proto_rawDescGZIP() []byte {
	file_mq_detect_proto_rawDescOnce.Do(func() {
		file_mq_detect_proto_rawDescData = protoimpl.X.CompressGZIP(file_mq_detect_proto_rawDescData)
	})
	return file_mq_detect_proto_rawDescData
}

var file_mq_detect_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_mq_detect_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_mq_detect_proto_goTypes = []any{
	(DetectStatus)(0),          // 0: mq.DetectStatus
	(DetectRes)(0),             // 1: mq.DetectRes
	(DetectEngine)(0),          // 2: mq.DetectEngine
	(SandboxRiskType)(0),       // 3: mq.SandboxRiskType
	(*SampleDetectFile)(nil),   // 4: mq.SampleDetectFile
	(*SampleDetectResult)(nil), // 5: mq.SampleDetectResult
	(*ClamAvResult)(nil),       // 6: mq.ClamAvResult
	(*SandboxResult)(nil),      // 7: mq.SandboxResult
	(*AIResult)(nil),           // 8: mq.AIResult
	(*AIPeResult)(nil),         // 9: mq.AIPeResult
	(*WFYResult)(nil),          // 10: mq.WFYResult
}
var file_mq_detect_proto_depIdxs = []int32{
	2,  // 0: mq.SampleDetectFile.engines:type_name -> mq.DetectEngine
	0,  // 1: mq.SampleDetectResult.status:type_name -> mq.DetectStatus
	1,  // 2: mq.SampleDetectResult.result:type_name -> mq.DetectRes
	6,  // 3: mq.SampleDetectResult.clam_av:type_name -> mq.ClamAvResult
	8,  // 4: mq.SampleDetectResult.ai:type_name -> mq.AIResult
	9,  // 5: mq.SampleDetectResult.ai_pe:type_name -> mq.AIPeResult
	7,  // 6: mq.SampleDetectResult.sandbox:type_name -> mq.SandboxResult
	10, // 7: mq.SampleDetectResult.wfy:type_name -> mq.WFYResult
	3,  // 8: mq.SandboxResult.risk_types:type_name -> mq.SandboxRiskType
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_mq_detect_proto_init() }
func file_mq_detect_proto_init() {
	if File_mq_detect_proto != nil {
		return
	}
	file_mq_detect_proto_msgTypes[1].OneofWrappers = []any{
		(*SampleDetectResult_ClamAv)(nil),
		(*SampleDetectResult_Ai)(nil),
		(*SampleDetectResult_AiPe)(nil),
		(*SampleDetectResult_Sandbox)(nil),
		(*SampleDetectResult_Wfy)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_mq_detect_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_mq_detect_proto_goTypes,
		DependencyIndexes: file_mq_detect_proto_depIdxs,
		EnumInfos:         file_mq_detect_proto_enumTypes,
		MessageInfos:      file_mq_detect_proto_msgTypes,
	}.Build()
	File_mq_detect_proto = out.File
	file_mq_detect_proto_rawDesc = nil
	file_mq_detect_proto_goTypes = nil
	file_mq_detect_proto_depIdxs = nil
}
