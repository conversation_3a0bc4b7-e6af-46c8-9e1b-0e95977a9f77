// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: mq/clue.proto

package mq

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on OutreachClueInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachClueInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachClueInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachClueInfoMultiError, or nil if none found.
func (m *OutreachClueInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachClueInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for Level

	// no validation rules for RiskType

	// no validation rules for Source

	// no validation rules for OutreachType

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	// no validation rules for Domain

	// no validation rules for IpVer

	// no validation rules for Protocol

	// no validation rules for Tag

	// no validation rules for Apt

	// no validation rules for MaliciousCode

	// no validation rules for Confidence

	// no validation rules for Direction

	// no validation rules for Pid

	// no validation rules for ProcName

	// no validation rules for ProcPath

	// no validation rules for ProcStartTime

	// no validation rules for ProcMd5

	// no validation rules for ProcCommand

	// no validation rules for ProcUserPermission

	// no validation rules for ProcUser

	// no validation rules for SignatureInfo

	// no validation rules for RootProcInfo

	// no validation rules for CurrentProcessInfo

	// no validation rules for ParentProcessInfo

	// no validation rules for AptStr

	// no validation rules for BlackMarket

	// no validation rules for Virus

	// no validation rules for UniqueFlagMd5

	// no validation rules for ClueKey

	// no validation rules for RootPid

	// no validation rules for RootStartTime

	// no validation rules for PidStartTime

	// no validation rules for HostName

	// no validation rules for Euid

	// no validation rules for GroupName

	// no validation rules for Os

	// no validation rules for OsType

	// no validation rules for HostIp

	// no validation rules for ClientVersion

	if all {
		switch v := interface{}(m.GetDiscoverTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachClueInfoValidationError{
					field:  "DiscoverTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachClueInfoValidationError{
					field:  "DiscoverTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscoverTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachClueInfoValidationError{
				field:  "DiscoverTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachClueInfoValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachClueInfoValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachClueInfoValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachClueInfoMultiError(errors)
	}

	return nil
}

// OutreachClueInfoMultiError is an error wrapping multiple validation errors
// returned by OutreachClueInfo.ValidateAll() if the designated constraints
// aren't met.
type OutreachClueInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachClueInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachClueInfoMultiError) AllErrors() []error { return m }

// OutreachClueInfoValidationError is the validation error returned by
// OutreachClueInfo.Validate if the designated constraints aren't met.
type OutreachClueInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachClueInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachClueInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachClueInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachClueInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachClueInfoValidationError) ErrorName() string { return "OutreachClueInfoValidationError" }

// Error satisfies the builtin error interface
func (e OutreachClueInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachClueInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachClueInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachClueInfoValidationError{}

// Validate checks the field values on FileClueInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileClueInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileClueInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileClueInfoMultiError, or
// nil if none found.
func (m *FileClueInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileClueInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for Level

	// no validation rules for RiskType

	// no validation rules for FilePath

	// no validation rules for FileName

	// no validation rules for FileCategory

	// no validation rules for Apt

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Sha1

	// no validation rules for AccessAt

	// no validation rules for ModifyAt

	// no validation rules for CreateAt

	// no validation rules for StMode

	// no validation rules for FileSize

	// no validation rules for FilePermission

	// no validation rules for FileUser

	// no validation rules for SignatureInfo

	// no validation rules for IsSandbox

	// no validation rules for FileVersion

	// no validation rules for FileVendor

	// no validation rules for Confidence

	// no validation rules for FileThreadSource

	// no validation rules for MaliciousLnkTargetPath

	// no validation rules for MaliciousLnkWorkingDir

	// no validation rules for MaliciousLnkCmdLine

	// no validation rules for MaliciousLnkIconPath

	// no validation rules for MaliciousUrl

	// no validation rules for SlRuleName

	// no validation rules for SlRuleDetail

	if all {
		switch v := interface{}(m.GetDiscoverTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileClueInfoValidationError{
					field:  "DiscoverTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileClueInfoValidationError{
					field:  "DiscoverTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDiscoverTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileClueInfoValidationError{
				field:  "DiscoverTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileClueInfoMultiError(errors)
	}

	return nil
}

// FileClueInfoMultiError is an error wrapping multiple validation errors
// returned by FileClueInfo.ValidateAll() if the designated constraints aren't met.
type FileClueInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileClueInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileClueInfoMultiError) AllErrors() []error { return m }

// FileClueInfoValidationError is the validation error returned by
// FileClueInfo.Validate if the designated constraints aren't met.
type FileClueInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileClueInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileClueInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileClueInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileClueInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileClueInfoValidationError) ErrorName() string { return "FileClueInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileClueInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileClueInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileClueInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileClueInfoValidationError{}
