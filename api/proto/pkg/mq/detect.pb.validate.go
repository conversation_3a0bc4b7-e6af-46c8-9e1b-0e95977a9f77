// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: mq/detect.proto

package mq

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SampleDetectFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SampleDetectFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SampleDetectFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SampleDetectFileMultiError, or nil if none found.
func (m *SampleDetectFile) ValidateAll() error {
	return m.validate(true)
}

func (m *SampleDetectFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Md5

	// no validation rules for Type

	// no validation rules for Bucket

	// no validation rules for Path

	// no validation rules for Size

	// no validation rules for Priority

	// no validation rules for Retry

	// no validation rules for Timeout

	// no validation rules for ParallelDetect

	if len(errors) > 0 {
		return SampleDetectFileMultiError(errors)
	}

	return nil
}

// SampleDetectFileMultiError is an error wrapping multiple validation errors
// returned by SampleDetectFile.ValidateAll() if the designated constraints
// aren't met.
type SampleDetectFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SampleDetectFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SampleDetectFileMultiError) AllErrors() []error { return m }

// SampleDetectFileValidationError is the validation error returned by
// SampleDetectFile.Validate if the designated constraints aren't met.
type SampleDetectFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SampleDetectFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SampleDetectFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SampleDetectFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SampleDetectFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SampleDetectFileValidationError) ErrorName() string { return "SampleDetectFileValidationError" }

// Error satisfies the builtin error interface
func (e SampleDetectFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSampleDetectFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SampleDetectFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SampleDetectFileValidationError{}

// Validate checks the field values on SampleDetectResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SampleDetectResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SampleDetectResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SampleDetectResultMultiError, or nil if none found.
func (m *SampleDetectResult) ValidateAll() error {
	return m.validate(true)
}

func (m *SampleDetectResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Bucket

	// no validation rules for Path

	// no validation rules for Status

	// no validation rules for Result

	switch v := m.DetectRes.(type) {
	case *SampleDetectResult_ClamAv:
		if v == nil {
			err := SampleDetectResultValidationError{
				field:  "DetectRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetClamAv()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "ClamAv",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "ClamAv",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetClamAv()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleDetectResultValidationError{
					field:  "ClamAv",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SampleDetectResult_Ai:
		if v == nil {
			err := SampleDetectResultValidationError{
				field:  "DetectRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAi()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Ai",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Ai",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAi()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleDetectResultValidationError{
					field:  "Ai",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SampleDetectResult_AiPe:
		if v == nil {
			err := SampleDetectResultValidationError{
				field:  "DetectRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAiPe()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "AiPe",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "AiPe",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAiPe()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleDetectResultValidationError{
					field:  "AiPe",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SampleDetectResult_Sandbox:
		if v == nil {
			err := SampleDetectResultValidationError{
				field:  "DetectRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSandbox()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Sandbox",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Sandbox",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSandbox()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleDetectResultValidationError{
					field:  "Sandbox",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SampleDetectResult_Wfy:
		if v == nil {
			err := SampleDetectResultValidationError{
				field:  "DetectRes",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWfy()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Wfy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SampleDetectResultValidationError{
						field:  "Wfy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWfy()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SampleDetectResultValidationError{
					field:  "Wfy",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SampleDetectResultMultiError(errors)
	}

	return nil
}

// SampleDetectResultMultiError is an error wrapping multiple validation errors
// returned by SampleDetectResult.ValidateAll() if the designated constraints
// aren't met.
type SampleDetectResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SampleDetectResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SampleDetectResultMultiError) AllErrors() []error { return m }

// SampleDetectResultValidationError is the validation error returned by
// SampleDetectResult.Validate if the designated constraints aren't met.
type SampleDetectResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SampleDetectResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SampleDetectResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SampleDetectResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SampleDetectResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SampleDetectResultValidationError) ErrorName() string {
	return "SampleDetectResultValidationError"
}

// Error satisfies the builtin error interface
func (e SampleDetectResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSampleDetectResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SampleDetectResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SampleDetectResultValidationError{}

// Validate checks the field values on ClamAvResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClamAvResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClamAvResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClamAvResultMultiError, or
// nil if none found.
func (m *ClamAvResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ClamAvResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VirusName

	// no validation rules for Version

	if len(errors) > 0 {
		return ClamAvResultMultiError(errors)
	}

	return nil
}

// ClamAvResultMultiError is an error wrapping multiple validation errors
// returned by ClamAvResult.ValidateAll() if the designated constraints aren't met.
type ClamAvResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClamAvResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClamAvResultMultiError) AllErrors() []error { return m }

// ClamAvResultValidationError is the validation error returned by
// ClamAvResult.Validate if the designated constraints aren't met.
type ClamAvResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClamAvResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClamAvResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClamAvResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClamAvResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClamAvResultValidationError) ErrorName() string { return "ClamAvResultValidationError" }

// Error satisfies the builtin error interface
func (e ClamAvResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClamAvResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClamAvResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClamAvResultValidationError{}

// Validate checks the field values on SandboxResult with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SandboxResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SandboxResultMultiError, or
// nil if none found.
func (m *SandboxResult) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	// no validation rules for TaskId

	if len(errors) > 0 {
		return SandboxResultMultiError(errors)
	}

	return nil
}

// SandboxResultMultiError is an error wrapping multiple validation errors
// returned by SandboxResult.ValidateAll() if the designated constraints
// aren't met.
type SandboxResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxResultMultiError) AllErrors() []error { return m }

// SandboxResultValidationError is the validation error returned by
// SandboxResult.Validate if the designated constraints aren't met.
type SandboxResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxResultValidationError) ErrorName() string { return "SandboxResultValidationError" }

// Error satisfies the builtin error interface
func (e SandboxResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxResultValidationError{}

// Validate checks the field values on AIResult with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AIResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AIResult with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AIResultMultiError, or nil
// if none found.
func (m *AIResult) ValidateAll() error {
	return m.validate(true)
}

func (m *AIResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	if len(errors) > 0 {
		return AIResultMultiError(errors)
	}

	return nil
}

// AIResultMultiError is an error wrapping multiple validation errors returned
// by AIResult.ValidateAll() if the designated constraints aren't met.
type AIResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AIResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AIResultMultiError) AllErrors() []error { return m }

// AIResultValidationError is the validation error returned by
// AIResult.Validate if the designated constraints aren't met.
type AIResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AIResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AIResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AIResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AIResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AIResultValidationError) ErrorName() string { return "AIResultValidationError" }

// Error satisfies the builtin error interface
func (e AIResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAIResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AIResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AIResultValidationError{}

// Validate checks the field values on AIPeResult with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AIPeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AIPeResult with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AIPeResultMultiError, or
// nil if none found.
func (m *AIPeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *AIPeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Score

	if len(errors) > 0 {
		return AIPeResultMultiError(errors)
	}

	return nil
}

// AIPeResultMultiError is an error wrapping multiple validation errors
// returned by AIPeResult.ValidateAll() if the designated constraints aren't met.
type AIPeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AIPeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AIPeResultMultiError) AllErrors() []error { return m }

// AIPeResultValidationError is the validation error returned by
// AIPeResult.Validate if the designated constraints aren't met.
type AIPeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AIPeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AIPeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AIPeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AIPeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AIPeResultValidationError) ErrorName() string { return "AIPeResultValidationError" }

// Error satisfies the builtin error interface
func (e AIPeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAIPeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AIPeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AIPeResultValidationError{}

// Validate checks the field values on WFYResult with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WFYResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WFYResult with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WFYResultMultiError, or nil
// if none found.
func (m *WFYResult) ValidateAll() error {
	return m.validate(true)
}

func (m *WFYResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Confidence

	// no validation rules for RiskLevel

	if len(errors) > 0 {
		return WFYResultMultiError(errors)
	}

	return nil
}

// WFYResultMultiError is an error wrapping multiple validation errors returned
// by WFYResult.ValidateAll() if the designated constraints aren't met.
type WFYResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WFYResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WFYResultMultiError) AllErrors() []error { return m }

// WFYResultValidationError is the validation error returned by
// WFYResult.Validate if the designated constraints aren't met.
type WFYResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WFYResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WFYResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WFYResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WFYResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WFYResultValidationError) ErrorName() string { return "WFYResultValidationError" }

// Error satisfies the builtin error interface
func (e WFYResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWFYResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WFYResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WFYResultValidationError{}
