// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: zyra/zyra.proto

package zyra

import (
	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 检测任务状态
type DetectTaskStatus int32

const (
	DetectTaskStatus_UNKNOWN DetectTaskStatus = 0
	DetectTaskStatus_PENDING DetectTaskStatus = 1 // 待检测
	DetectTaskStatus_RUNNING DetectTaskStatus = 2 // 检测中
	DetectTaskStatus_SUCCESS DetectTaskStatus = 3 // 检测成功
	DetectTaskStatus_FAIL    DetectTaskStatus = 4 // 检测失败
	DetectTaskStatus_FREEZE  DetectTaskStatus = 5 // 冻结中
	DetectTaskStatus_CANCEL  DetectTaskStatus = 6 // 取消
)

// Enum value maps for DetectTaskStatus.
var (
	DetectTaskStatus_name = map[int32]string{
		0: "UNKNOWN",
		1: "PENDING",
		2: "RUNNING",
		3: "SUCCESS",
		4: "FAIL",
		5: "FREEZE",
		6: "CANCEL",
	}
	DetectTaskStatus_value = map[string]int32{
		"UNKNOWN": 0,
		"PENDING": 1,
		"RUNNING": 2,
		"SUCCESS": 3,
		"FAIL":    4,
		"FREEZE":  5,
		"CANCEL":  6,
	}
)

func (x DetectTaskStatus) Enum() *DetectTaskStatus {
	p := new(DetectTaskStatus)
	*p = x
	return p
}

func (x DetectTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_zyra_zyra_proto_enumTypes[0].Descriptor()
}

func (DetectTaskStatus) Type() protoreflect.EnumType {
	return &file_zyra_zyra_proto_enumTypes[0]
}

func (x DetectTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectTaskStatus.Descriptor instead.
func (DetectTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{0}
}

// 添加检测任务
type AddTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetectInfo *mq.SampleDetectFile `protobuf:"bytes,1,opt,name=detect_info,json=detectInfo,proto3" json:"detect_info,omitempty"` // 检测文件信息
}

func (x *AddTaskReq) Reset() {
	*x = AddTaskReq{}
	mi := &file_zyra_zyra_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaskReq) ProtoMessage() {}

func (x *AddTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaskReq.ProtoReflect.Descriptor instead.
func (*AddTaskReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{0}
}

func (x *AddTaskReq) GetDetectInfo() *mq.SampleDetectFile {
	if x != nil {
		return x.DetectInfo
	}
	return nil
}

type AddTaskResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddTaskResp) Reset() {
	*x = AddTaskResp{}
	mi := &file_zyra_zyra_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddTaskResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddTaskResp) ProtoMessage() {}

func (x *AddTaskResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddTaskResp.ProtoReflect.Descriptor instead.
func (*AddTaskResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{1}
}

// 沙箱检测任务创建
type SandboxCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	File                 string `protobuf:"bytes,1,opt,name=file,proto3" json:"file,omitempty"`                                                               // 样本文件（必填）
	CompressPassword     string `protobuf:"bytes,2,opt,name=compress_password,json=compressPassword,proto3" json:"compress_password,omitempty"`               // 压缩密码（可选）
	CompressPasswordDeep string `protobuf:"bytes,3,opt,name=compress_password_deep,json=compressPasswordDeep,proto3" json:"compress_password_deep,omitempty"` // 嵌套压缩包密码（可选）
	MailSender           string `protobuf:"bytes,4,opt,name=mail_sender,json=mailSender,proto3" json:"mail_sender,omitempty"`                                 // 邮件发送者（可选）
	MailReceiver         string `protobuf:"bytes,5,opt,name=mail_receiver,json=mailReceiver,proto3" json:"mail_receiver,omitempty"`                           // 邮件接收者（可选）
	MailSubject          string `protobuf:"bytes,6,opt,name=mail_subject,json=mailSubject,proto3" json:"mail_subject,omitempty"`                              // 邮件主题（可选）
}

func (x *SandboxCreateReq) Reset() {
	*x = SandboxCreateReq{}
	mi := &file_zyra_zyra_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxCreateReq) ProtoMessage() {}

func (x *SandboxCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxCreateReq.ProtoReflect.Descriptor instead.
func (*SandboxCreateReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{2}
}

func (x *SandboxCreateReq) GetFile() string {
	if x != nil {
		return x.File
	}
	return ""
}

func (x *SandboxCreateReq) GetCompressPassword() string {
	if x != nil {
		return x.CompressPassword
	}
	return ""
}

func (x *SandboxCreateReq) GetCompressPasswordDeep() string {
	if x != nil {
		return x.CompressPasswordDeep
	}
	return ""
}

func (x *SandboxCreateReq) GetMailSender() string {
	if x != nil {
		return x.MailSender
	}
	return ""
}

func (x *SandboxCreateReq) GetMailReceiver() string {
	if x != nil {
		return x.MailReceiver
	}
	return ""
}

func (x *SandboxCreateReq) GetMailSubject() string {
	if x != nil {
		return x.MailSubject
	}
	return ""
}

type SandboxCreateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 状态码
	Msg  string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
	Data *SandboxCreateResp_Data `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 数据
}

func (x *SandboxCreateResp) Reset() {
	*x = SandboxCreateResp{}
	mi := &file_zyra_zyra_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxCreateResp) ProtoMessage() {}

func (x *SandboxCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxCreateResp.ProtoReflect.Descriptor instead.
func (*SandboxCreateResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{3}
}

func (x *SandboxCreateResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SandboxCreateResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SandboxCreateResp) GetData() *SandboxCreateResp_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

// 查询沙箱状态
type SandboxProbeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`                // 沙箱地址
	AuthKey string `protobuf:"bytes,2,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"` // 沙箱认证key
}

func (x *SandboxProbeReq) Reset() {
	*x = SandboxProbeReq{}
	mi := &file_zyra_zyra_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxProbeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxProbeReq) ProtoMessage() {}

func (x *SandboxProbeReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxProbeReq.ProtoReflect.Descriptor instead.
func (*SandboxProbeReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{4}
}

func (x *SandboxProbeReq) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *SandboxProbeReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

type SandboxProbeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SandboxProbeResp) Reset() {
	*x = SandboxProbeResp{}
	mi := &file_zyra_zyra_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxProbeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxProbeResp) ProtoMessage() {}

func (x *SandboxProbeResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxProbeResp.ProtoReflect.Descriptor instead.
func (*SandboxProbeResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{5}
}

// 沙箱检测结果回调接口
type PushSandboxDetectStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int32   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                 // 任务id
	TaskStatus int32   `protobuf:"varint,2,opt,name=task_status,json=taskStatus,proto3" json:"task_status,omitempty"`     // 任务状态 1:待检测，2:检测中，3:检测完成，4:检测失败，5:检测超时
	Filename   string  `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`                            // 样本文件名
	Sha256     string  `protobuf:"bytes,4,opt,name=sha256,proto3" json:"sha256,omitempty"`                                // 样本sha256
	Md5        string  `protobuf:"bytes,5,opt,name=md5,proto3" json:"md5,omitempty"`                                      // 样本md5
	RiskLevel  int32   `protobuf:"varint,6,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`        // 风险级别 1 安全，2 疑似，3 风险
	RiskValue  int32   `protobuf:"varint,7,opt,name=risk_value,json=riskValue,proto3" json:"risk_value,omitempty"`        // 风险分值
	RiskTypes  []int32 `protobuf:"varint,8,rep,packed,name=risk_types,json=riskTypes,proto3" json:"risk_types,omitempty"` // 风险类型
}

func (x *PushSandboxDetectStatusReq) Reset() {
	*x = PushSandboxDetectStatusReq{}
	mi := &file_zyra_zyra_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushSandboxDetectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushSandboxDetectStatusReq) ProtoMessage() {}

func (x *PushSandboxDetectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushSandboxDetectStatusReq.ProtoReflect.Descriptor instead.
func (*PushSandboxDetectStatusReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{6}
}

func (x *PushSandboxDetectStatusReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *PushSandboxDetectStatusReq) GetTaskStatus() int32 {
	if x != nil {
		return x.TaskStatus
	}
	return 0
}

func (x *PushSandboxDetectStatusReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *PushSandboxDetectStatusReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *PushSandboxDetectStatusReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *PushSandboxDetectStatusReq) GetRiskLevel() int32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *PushSandboxDetectStatusReq) GetRiskValue() int32 {
	if x != nil {
		return x.RiskValue
	}
	return 0
}

func (x *PushSandboxDetectStatusReq) GetRiskTypes() []int32 {
	if x != nil {
		return x.RiskTypes
	}
	return nil
}

type PushSandboxDetectStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 错误码
	Msg  string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 错误信息
}

func (x *PushSandboxDetectStatusResp) Reset() {
	*x = PushSandboxDetectStatusResp{}
	mi := &file_zyra_zyra_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushSandboxDetectStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushSandboxDetectStatusResp) ProtoMessage() {}

func (x *PushSandboxDetectStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushSandboxDetectStatusResp.ProtoReflect.Descriptor instead.
func (*PushSandboxDetectStatusResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{7}
}

func (x *PushSandboxDetectStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PushSandboxDetectStatusResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

type SandboxTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int32   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                 // 任务id
	TaskStatus int32   `protobuf:"varint,2,opt,name=task_status,json=taskStatus,proto3" json:"task_status,omitempty"`     // 任务状态
	Filename   string  `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`                            // 文件名
	Md5        string  `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`                                      // 文件md5
	Sha256     string  `protobuf:"bytes,5,opt,name=sha256,proto3" json:"sha256,omitempty"`                                // 文件sha256
	RiskLevel  int32   `protobuf:"varint,6,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`        // 1 安全， 2 疑似， 3 风险
	RiskValue  int32   `protobuf:"varint,7,opt,name=risk_value,json=riskValue,proto3" json:"risk_value,omitempty"`        // 分值从 0 至 100，越高风险越大，可用于细粒度的风险值判断
	RiskTypes  []int32 `protobuf:"varint,8,rep,packed,name=risk_types,json=riskTypes,proto3" json:"risk_types,omitempty"` // 包含样本具体的风险类型
	RawResult  string  `protobuf:"bytes,9,opt,name=raw_result,json=rawResult,proto3" json:"raw_result,omitempty"`         // 原始结果
}

func (x *SandboxTask) Reset() {
	*x = SandboxTask{}
	mi := &file_zyra_zyra_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxTask) ProtoMessage() {}

func (x *SandboxTask) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxTask.ProtoReflect.Descriptor instead.
func (*SandboxTask) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{8}
}

func (x *SandboxTask) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *SandboxTask) GetTaskStatus() int32 {
	if x != nil {
		return x.TaskStatus
	}
	return 0
}

func (x *SandboxTask) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *SandboxTask) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *SandboxTask) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *SandboxTask) GetRiskLevel() int32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *SandboxTask) GetRiskValue() int32 {
	if x != nil {
		return x.RiskValue
	}
	return 0
}

func (x *SandboxTask) GetRiskTypes() []int32 {
	if x != nil {
		return x.RiskTypes
	}
	return nil
}

func (x *SandboxTask) GetRawResult() string {
	if x != nil {
		return x.RawResult
	}
	return ""
}

// 自研沙箱任务状态查询
type QuerySandboxTaskStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int32  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Sha256 string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                // 文件sha256
	Md5    string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`                      // 文件md5
}

func (x *QuerySandboxTaskStatusReq) Reset() {
	*x = QuerySandboxTaskStatusReq{}
	mi := &file_zyra_zyra_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskStatusReq) ProtoMessage() {}

func (x *QuerySandboxTaskStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskStatusReq.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskStatusReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{9}
}

func (x *QuerySandboxTaskStatusReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *QuerySandboxTaskStatusReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *QuerySandboxTaskStatusReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type QuerySandboxTaskStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 沙箱返回错误码
	Msg  string       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 沙箱返回错误信息
	Data *SandboxTask `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 沙箱返回结果
}

func (x *QuerySandboxTaskStatusResp) Reset() {
	*x = QuerySandboxTaskStatusResp{}
	mi := &file_zyra_zyra_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskStatusResp) ProtoMessage() {}

func (x *QuerySandboxTaskStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskStatusResp.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskStatusResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{10}
}

func (x *QuerySandboxTaskStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QuerySandboxTaskStatusResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *QuerySandboxTaskStatusResp) GetData() *SandboxTask {
	if x != nil {
		return x.Data
	}
	return nil
}

// 自研沙箱任务状态批量查询
type QuerySandboxTaskBatchStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskIds []int32  `protobuf:"varint,1,rep,packed,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"` // 任务id
	Sha256S []string `protobuf:"bytes,2,rep,name=sha256s,proto3" json:"sha256s,omitempty"`                        // 文件sha256
	Md5S    []string `protobuf:"bytes,3,rep,name=md5s,proto3" json:"md5s,omitempty"`                              // 文件md5
}

func (x *QuerySandboxTaskBatchStatusReq) Reset() {
	*x = QuerySandboxTaskBatchStatusReq{}
	mi := &file_zyra_zyra_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskBatchStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskBatchStatusReq) ProtoMessage() {}

func (x *QuerySandboxTaskBatchStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskBatchStatusReq.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskBatchStatusReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{11}
}

func (x *QuerySandboxTaskBatchStatusReq) GetTaskIds() []int32 {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

func (x *QuerySandboxTaskBatchStatusReq) GetSha256S() []string {
	if x != nil {
		return x.Sha256S
	}
	return nil
}

func (x *QuerySandboxTaskBatchStatusReq) GetMd5S() []string {
	if x != nil {
		return x.Md5S
	}
	return nil
}

type QuerySandboxTaskBatchStatusResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32          `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 沙箱返回错误码
	Msg  string         `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 沙箱返回错误信息
	Data []*SandboxTask `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`  // 沙箱返回结果
}

func (x *QuerySandboxTaskBatchStatusResp) Reset() {
	*x = QuerySandboxTaskBatchStatusResp{}
	mi := &file_zyra_zyra_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskBatchStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskBatchStatusResp) ProtoMessage() {}

func (x *QuerySandboxTaskBatchStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskBatchStatusResp.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskBatchStatusResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{12}
}

func (x *QuerySandboxTaskBatchStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QuerySandboxTaskBatchStatusResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *QuerySandboxTaskBatchStatusResp) GetData() []*SandboxTask {
	if x != nil {
		return x.Data
	}
	return nil
}

// 自研沙箱任务结果查询
type QuerySandboxTaskResultReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int32  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Sha256 string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                // 文件sha256
	Md5    string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`                      // 文件md5
}

func (x *QuerySandboxTaskResultReq) Reset() {
	*x = QuerySandboxTaskResultReq{}
	mi := &file_zyra_zyra_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskResultReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskResultReq) ProtoMessage() {}

func (x *QuerySandboxTaskResultReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskResultReq.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskResultReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{13}
}

func (x *QuerySandboxTaskResultReq) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *QuerySandboxTaskResultReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *QuerySandboxTaskResultReq) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

type QuerySandboxTaskResultResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` // 沙箱返回错误码
	Msg  string       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`    // 沙箱返回错误信息
	Data *SandboxTask `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`  // 沙箱返回结果
}

func (x *QuerySandboxTaskResultResp) Reset() {
	*x = QuerySandboxTaskResultResp{}
	mi := &file_zyra_zyra_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QuerySandboxTaskResultResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QuerySandboxTaskResultResp) ProtoMessage() {}

func (x *QuerySandboxTaskResultResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QuerySandboxTaskResultResp.ProtoReflect.Descriptor instead.
func (*QuerySandboxTaskResultResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{14}
}

func (x *QuerySandboxTaskResultResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QuerySandboxTaskResultResp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *QuerySandboxTaskResultResp) GetData() *SandboxTask {
	if x != nil {
		return x.Data
	}
	return nil
}

// 查询云沙箱状态
type CloudSandboxProbeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthAddr     string `protobuf:"bytes,1,opt,name=auth_addr,json=authAddr,proto3" json:"auth_addr,omitempty"`             // 身份认证地址
	AuthKey      string `protobuf:"bytes,2,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"`                // 身份认证key
	CloudAddr    string `protobuf:"bytes,3,opt,name=cloud_addr,json=cloudAddr,proto3" json:"cloud_addr,omitempty"`          // 沙箱服务地址
	CloudSecret  string `protobuf:"bytes,4,opt,name=cloud_secret,json=cloudSecret,proto3" json:"cloud_secret,omitempty"`    // 沙箱服务密钥
	BucketAddr   string `protobuf:"bytes,5,opt,name=bucket_addr,json=bucketAddr,proto3" json:"bucket_addr,omitempty"`       // 存储桶地址
	BucketSecret string `protobuf:"bytes,6,opt,name=bucket_secret,json=bucketSecret,proto3" json:"bucket_secret,omitempty"` // 存储桶密钥
}

func (x *CloudSandboxProbeReq) Reset() {
	*x = CloudSandboxProbeReq{}
	mi := &file_zyra_zyra_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloudSandboxProbeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudSandboxProbeReq) ProtoMessage() {}

func (x *CloudSandboxProbeReq) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudSandboxProbeReq.ProtoReflect.Descriptor instead.
func (*CloudSandboxProbeReq) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{15}
}

func (x *CloudSandboxProbeReq) GetAuthAddr() string {
	if x != nil {
		return x.AuthAddr
	}
	return ""
}

func (x *CloudSandboxProbeReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *CloudSandboxProbeReq) GetCloudAddr() string {
	if x != nil {
		return x.CloudAddr
	}
	return ""
}

func (x *CloudSandboxProbeReq) GetCloudSecret() string {
	if x != nil {
		return x.CloudSecret
	}
	return ""
}

func (x *CloudSandboxProbeReq) GetBucketAddr() string {
	if x != nil {
		return x.BucketAddr
	}
	return ""
}

func (x *CloudSandboxProbeReq) GetBucketSecret() string {
	if x != nil {
		return x.BucketSecret
	}
	return ""
}

type CloudSandboxProbeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CloudSandboxProbeResp) Reset() {
	*x = CloudSandboxProbeResp{}
	mi := &file_zyra_zyra_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloudSandboxProbeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudSandboxProbeResp) ProtoMessage() {}

func (x *CloudSandboxProbeResp) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudSandboxProbeResp.ProtoReflect.Descriptor instead.
func (*CloudSandboxProbeResp) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{16}
}

// 文件检测条件
type DetectCond struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       int64           `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                      // 检测任务Id
	Name         string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                         // 文件名
	Type         string          `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                         // 文件类型
	Bucket       string          `protobuf:"bytes,4,opt,name=bucket,proto3" json:"bucket,omitempty"`                                     // 文件存储桶
	Path         string          `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                                         // 文件路径
	Sha256       string          `protobuf:"bytes,6,opt,name=sha256,proto3" json:"sha256,omitempty"`                                     // 文件sha256
	Md5          string          `protobuf:"bytes,7,opt,name=md5,proto3" json:"md5,omitempty"`                                           // 文件md5
	Retry        int32           `protobuf:"varint,8,opt,name=retry,proto3" json:"retry,omitempty"`                                      // 重试次数
	Timeout      int32           `protobuf:"varint,9,opt,name=timeout,proto3" json:"timeout,omitempty"`                                  // 超时时间
	Engine       mq.DetectEngine `protobuf:"varint,10,opt,name=engine,proto3,enum=mq.DetectEngine" json:"engine,omitempty"`              // 引擎类型
	BufChunkSize int32           `protobuf:"varint,11,opt,name=buf_chunk_size,json=bufChunkSize,proto3" json:"buf_chunk_size,omitempty"` // 流式数据缓冲区大小，达到这个标准就需要发送数据
}

func (x *DetectCond) Reset() {
	*x = DetectCond{}
	mi := &file_zyra_zyra_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectCond) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectCond) ProtoMessage() {}

func (x *DetectCond) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectCond.ProtoReflect.Descriptor instead.
func (*DetectCond) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{17}
}

func (x *DetectCond) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DetectCond) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DetectCond) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DetectCond) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *DetectCond) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *DetectCond) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *DetectCond) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *DetectCond) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *DetectCond) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *DetectCond) GetEngine() mq.DetectEngine {
	if x != nil {
		return x.Engine
	}
	return mq.DetectEngine(0)
}

func (x *DetectCond) GetBufChunkSize() int32 {
	if x != nil {
		return x.BufChunkSize
	}
	return 0
}

type SandboxCreateResp_Data struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int32 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 检测任务Id
}

func (x *SandboxCreateResp_Data) Reset() {
	*x = SandboxCreateResp_Data{}
	mi := &file_zyra_zyra_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxCreateResp_Data) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxCreateResp_Data) ProtoMessage() {}

func (x *SandboxCreateResp_Data) ProtoReflect() protoreflect.Message {
	mi := &file_zyra_zyra_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxCreateResp_Data.ProtoReflect.Descriptor instead.
func (*SandboxCreateResp_Data) Descriptor() ([]byte, []int) {
	return file_zyra_zyra_proto_rawDescGZIP(), []int{3, 0}
}

func (x *SandboxCreateResp_Data) GetTaskId() int32 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

var File_zyra_zyra_proto protoreflect.FileDescriptor

var file_zyra_zyra_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x7a, 0x79, 0x72, 0x61, 0x2f, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x7a, 0x79, 0x72, 0x61, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x0f, 0x6d, 0x71, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x43, 0x0a, 0x0a, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x12, 0x35, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6d, 0x71, 0x2e, 0x53, 0x61, 0x6d, 0x70,
	0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x0a, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x0d, 0x0a, 0x0b, 0x41, 0x64, 0x64, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x22, 0xf2, 0x01, 0x0a, 0x10, 0x53, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x69, 0x6c, 0x65,
	0x12, 0x2b, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x34, 0x0a,
	0x16, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x63,
	0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x44,
	0x65, 0x65, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x73, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x8c, 0x01, 0x0a,
	0x11, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x1f, 0x0a, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x0f, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68,
	0x4b, 0x65, 0x79, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0xf9, 0x01, 0x0a, 0x1a, 0x50, 0x75, 0x73, 0x68,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x73, 0x22, 0x43, 0x0a, 0x1b, 0x50, 0x75, 0x73, 0x68, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x22, 0x89, 0x02, 0x0a, 0x0b, 0x53, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69,
	0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x05, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x61, 0x77, 0x5f, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x77, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x5e, 0x0a, 0x19, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6d, 0x64, 0x35, 0x22, 0x69, 0x0a, 0x1a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0x69, 0x0a, 0x1e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54,
	0x61, 0x73, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x64, 0x35, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x64, 0x35, 0x73, 0x22, 0x6e, 0x0a, 0x1f, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6d, 0x73, 0x67, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x5e, 0x0a, 0x19, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0x69, 0x0a, 0x1a, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x25,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x7a,
	0x79, 0x72, 0x61, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd6, 0x01, 0x0a, 0x14, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x41, 0x64, 0x64, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x17,
	0x0a, 0x15, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0xa3, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x43, 0x6f, 0x6e, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70,
	0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x14, 0x0a,
	0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65,
	0x74, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x28, 0x0a,
	0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e,
	0x6d, 0x71, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x52,
	0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x62, 0x75, 0x66, 0x5f, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x62, 0x75, 0x66, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x2a, 0x68, 0x0a,
	0x10, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x52,
	0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x52, 0x45, 0x45, 0x5a, 0x45, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x43,
	0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x06, 0x32, 0xed, 0x07, 0x0a, 0x04, 0x5a, 0x79, 0x72, 0x61,
	0x12, 0x2e, 0x0a, 0x07, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x10, 0x2e, 0x7a, 0x79,
	0x72, 0x61, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x7a, 0x79, 0x72, 0x61, 0x2e, 0x41, 0x64, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x6a, 0x0a, 0x0c, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62, 0x65,
	0x12, 0x15, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50,
	0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x8c, 0x01, 0x0a,
	0x17, 0x50, 0x75, 0x73, 0x68, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e,
	0x50, 0x75, 0x73, 0x68, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x7a, 0x79, 0x72,
	0x61, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x2c, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x73, 0x61, 0x6e,
	0x64, 0x62, 0x6f, 0x78, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x73, 0x0a, 0x10, 0x50,
	0x75, 0x73, 0x68, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x41, 0x6c, 0x69, 0x76, 0x65, 0x12,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x3a, 0x01, 0x2a, 0x22, 0x24, 0x2f, 0x6f, 0x70, 0x65,
	0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x2f, 0x6b, 0x65, 0x65, 0x70, 0x61, 0x6c, 0x69, 0x76, 0x65,
	0x12, 0x8e, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f,
	0x78, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x2e, 0x7a, 0x79,
	0x72, 0x61, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x7a,
	0x79, 0x72, 0x61, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x31,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22, 0x26, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x73, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0xa2, 0x01, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62,
	0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x24, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x36,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x3a, 0x01, 0x2a, 0x22, 0x2b, 0x2f, 0x6f, 0x70, 0x65, 0x6e,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x73, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x8e, 0x01, 0x0a, 0x16, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x1f, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x61,
	0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x65, 0x71, 0x1a, 0x20, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x3a, 0x01, 0x2a, 0x22,
	0x26, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x2f, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x2f, 0x74, 0x61, 0x73, 0x6b,
	0x2f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x7e, 0x0a, 0x11, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x1a, 0x2e, 0x7a,
	0x79, 0x72, 0x61, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78,
	0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x7a, 0x79, 0x72, 0x61, 0x2e,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x50, 0x72, 0x6f, 0x62,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x3a, 0x01, 0x2a,
	0x22, 0x25, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f,
	0x78, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x42, 0x29, 0x5a, 0x27, 0x67, 0x69, 0x74, 0x2e, 0x61,
	0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x7a, 0x79,
	0x72, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_zyra_zyra_proto_rawDescOnce sync.Once
	file_zyra_zyra_proto_rawDescData = file_zyra_zyra_proto_rawDesc
)

func file_zyra_zyra_proto_rawDescGZIP() []byte {
	file_zyra_zyra_proto_rawDescOnce.Do(func() {
		file_zyra_zyra_proto_rawDescData = protoimpl.X.CompressGZIP(file_zyra_zyra_proto_rawDescData)
	})
	return file_zyra_zyra_proto_rawDescData
}

var file_zyra_zyra_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_zyra_zyra_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_zyra_zyra_proto_goTypes = []any{
	(DetectTaskStatus)(0),                   // 0: zyra.DetectTaskStatus
	(*AddTaskReq)(nil),                      // 1: zyra.AddTaskReq
	(*AddTaskResp)(nil),                     // 2: zyra.AddTaskResp
	(*SandboxCreateReq)(nil),                // 3: zyra.SandboxCreateReq
	(*SandboxCreateResp)(nil),               // 4: zyra.SandboxCreateResp
	(*SandboxProbeReq)(nil),                 // 5: zyra.SandboxProbeReq
	(*SandboxProbeResp)(nil),                // 6: zyra.SandboxProbeResp
	(*PushSandboxDetectStatusReq)(nil),      // 7: zyra.PushSandboxDetectStatusReq
	(*PushSandboxDetectStatusResp)(nil),     // 8: zyra.PushSandboxDetectStatusResp
	(*SandboxTask)(nil),                     // 9: zyra.SandboxTask
	(*QuerySandboxTaskStatusReq)(nil),       // 10: zyra.QuerySandboxTaskStatusReq
	(*QuerySandboxTaskStatusResp)(nil),      // 11: zyra.QuerySandboxTaskStatusResp
	(*QuerySandboxTaskBatchStatusReq)(nil),  // 12: zyra.QuerySandboxTaskBatchStatusReq
	(*QuerySandboxTaskBatchStatusResp)(nil), // 13: zyra.QuerySandboxTaskBatchStatusResp
	(*QuerySandboxTaskResultReq)(nil),       // 14: zyra.QuerySandboxTaskResultReq
	(*QuerySandboxTaskResultResp)(nil),      // 15: zyra.QuerySandboxTaskResultResp
	(*CloudSandboxProbeReq)(nil),            // 16: zyra.CloudSandboxProbeReq
	(*CloudSandboxProbeResp)(nil),           // 17: zyra.CloudSandboxProbeResp
	(*DetectCond)(nil),                      // 18: zyra.DetectCond
	(*SandboxCreateResp_Data)(nil),          // 19: zyra.SandboxCreateResp.Data
	(*mq.SampleDetectFile)(nil),             // 20: mq.SampleDetectFile
	(mq.DetectEngine)(0),                    // 21: mq.DetectEngine
	(*emptypb.Empty)(nil),                   // 22: google.protobuf.Empty
}
var file_zyra_zyra_proto_depIdxs = []int32{
	20, // 0: zyra.AddTaskReq.detect_info:type_name -> mq.SampleDetectFile
	19, // 1: zyra.SandboxCreateResp.data:type_name -> zyra.SandboxCreateResp.Data
	9,  // 2: zyra.QuerySandboxTaskStatusResp.data:type_name -> zyra.SandboxTask
	9,  // 3: zyra.QuerySandboxTaskBatchStatusResp.data:type_name -> zyra.SandboxTask
	9,  // 4: zyra.QuerySandboxTaskResultResp.data:type_name -> zyra.SandboxTask
	21, // 5: zyra.DetectCond.engine:type_name -> mq.DetectEngine
	1,  // 6: zyra.Zyra.AddTask:input_type -> zyra.AddTaskReq
	5,  // 7: zyra.Zyra.SandboxProbe:input_type -> zyra.SandboxProbeReq
	7,  // 8: zyra.Zyra.PushSandboxDetectStatus:input_type -> zyra.PushSandboxDetectStatusReq
	22, // 9: zyra.Zyra.PushSandboxAlive:input_type -> google.protobuf.Empty
	10, // 10: zyra.Zyra.QuerySandboxTaskStatus:input_type -> zyra.QuerySandboxTaskStatusReq
	12, // 11: zyra.Zyra.QuerySandboxTaskBatchStatus:input_type -> zyra.QuerySandboxTaskBatchStatusReq
	14, // 12: zyra.Zyra.QuerySandboxTaskResult:input_type -> zyra.QuerySandboxTaskResultReq
	16, // 13: zyra.Zyra.CloudSandboxProbe:input_type -> zyra.CloudSandboxProbeReq
	2,  // 14: zyra.Zyra.AddTask:output_type -> zyra.AddTaskResp
	6,  // 15: zyra.Zyra.SandboxProbe:output_type -> zyra.SandboxProbeResp
	8,  // 16: zyra.Zyra.PushSandboxDetectStatus:output_type -> zyra.PushSandboxDetectStatusResp
	22, // 17: zyra.Zyra.PushSandboxAlive:output_type -> google.protobuf.Empty
	11, // 18: zyra.Zyra.QuerySandboxTaskStatus:output_type -> zyra.QuerySandboxTaskStatusResp
	13, // 19: zyra.Zyra.QuerySandboxTaskBatchStatus:output_type -> zyra.QuerySandboxTaskBatchStatusResp
	15, // 20: zyra.Zyra.QuerySandboxTaskResult:output_type -> zyra.QuerySandboxTaskResultResp
	17, // 21: zyra.Zyra.CloudSandboxProbe:output_type -> zyra.CloudSandboxProbeResp
	14, // [14:22] is the sub-list for method output_type
	6,  // [6:14] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_zyra_zyra_proto_init() }
func file_zyra_zyra_proto_init() {
	if File_zyra_zyra_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_zyra_zyra_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_zyra_zyra_proto_goTypes,
		DependencyIndexes: file_zyra_zyra_proto_depIdxs,
		EnumInfos:         file_zyra_zyra_proto_enumTypes,
		MessageInfos:      file_zyra_zyra_proto_msgTypes,
	}.Build()
	File_zyra_zyra_proto = out.File
	file_zyra_zyra_proto_rawDesc = nil
	file_zyra_zyra_proto_goTypes = nil
	file_zyra_zyra_proto_depIdxs = nil
}
