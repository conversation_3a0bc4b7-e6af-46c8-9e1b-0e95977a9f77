// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: zyra/zyra.proto

package zyra

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	mq "git.anxin.com/v01-cluster/vapi/pkg/mq"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = mq.DetectEngine(0)
)

// Validate checks the field values on AddTaskReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddTaskReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddTaskReqMultiError, or
// nil if none found.
func (m *AddTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDetectInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddTaskReqValidationError{
					field:  "DetectInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddTaskReqValidationError{
					field:  "DetectInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddTaskReqValidationError{
				field:  "DetectInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddTaskReqMultiError(errors)
	}

	return nil
}

// AddTaskReqMultiError is an error wrapping multiple validation errors
// returned by AddTaskReq.ValidateAll() if the designated constraints aren't met.
type AddTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddTaskReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddTaskReqMultiError) AllErrors() []error { return m }

// AddTaskReqValidationError is the validation error returned by
// AddTaskReq.Validate if the designated constraints aren't met.
type AddTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddTaskReqValidationError) ErrorName() string { return "AddTaskReqValidationError" }

// Error satisfies the builtin error interface
func (e AddTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddTaskReqValidationError{}

// Validate checks the field values on AddTaskResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddTaskResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddTaskResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddTaskRespMultiError, or
// nil if none found.
func (m *AddTaskResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddTaskResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddTaskRespMultiError(errors)
	}

	return nil
}

// AddTaskRespMultiError is an error wrapping multiple validation errors
// returned by AddTaskResp.ValidateAll() if the designated constraints aren't met.
type AddTaskRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddTaskRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddTaskRespMultiError) AllErrors() []error { return m }

// AddTaskRespValidationError is the validation error returned by
// AddTaskResp.Validate if the designated constraints aren't met.
type AddTaskRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddTaskRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddTaskRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddTaskRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddTaskRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddTaskRespValidationError) ErrorName() string { return "AddTaskRespValidationError" }

// Error satisfies the builtin error interface
func (e AddTaskRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddTaskResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddTaskRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddTaskRespValidationError{}

// Validate checks the field values on SandboxCreateReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SandboxCreateReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxCreateReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxCreateReqMultiError, or nil if none found.
func (m *SandboxCreateReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxCreateReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for File

	// no validation rules for CompressPassword

	// no validation rules for CompressPasswordDeep

	// no validation rules for MailSender

	// no validation rules for MailReceiver

	// no validation rules for MailSubject

	if len(errors) > 0 {
		return SandboxCreateReqMultiError(errors)
	}

	return nil
}

// SandboxCreateReqMultiError is an error wrapping multiple validation errors
// returned by SandboxCreateReq.ValidateAll() if the designated constraints
// aren't met.
type SandboxCreateReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxCreateReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxCreateReqMultiError) AllErrors() []error { return m }

// SandboxCreateReqValidationError is the validation error returned by
// SandboxCreateReq.Validate if the designated constraints aren't met.
type SandboxCreateReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxCreateReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxCreateReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxCreateReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxCreateReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxCreateReqValidationError) ErrorName() string { return "SandboxCreateReqValidationError" }

// Error satisfies the builtin error interface
func (e SandboxCreateReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxCreateReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxCreateReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxCreateReqValidationError{}

// Validate checks the field values on SandboxCreateResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SandboxCreateResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxCreateResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxCreateRespMultiError, or nil if none found.
func (m *SandboxCreateResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxCreateResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SandboxCreateRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SandboxCreateRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SandboxCreateRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SandboxCreateRespMultiError(errors)
	}

	return nil
}

// SandboxCreateRespMultiError is an error wrapping multiple validation errors
// returned by SandboxCreateResp.ValidateAll() if the designated constraints
// aren't met.
type SandboxCreateRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxCreateRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxCreateRespMultiError) AllErrors() []error { return m }

// SandboxCreateRespValidationError is the validation error returned by
// SandboxCreateResp.Validate if the designated constraints aren't met.
type SandboxCreateRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxCreateRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxCreateRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxCreateRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxCreateRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxCreateRespValidationError) ErrorName() string {
	return "SandboxCreateRespValidationError"
}

// Error satisfies the builtin error interface
func (e SandboxCreateRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxCreateResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxCreateRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxCreateRespValidationError{}

// Validate checks the field values on SandboxProbeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SandboxProbeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxProbeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxProbeReqMultiError, or nil if none found.
func (m *SandboxProbeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxProbeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Address

	// no validation rules for AuthKey

	if len(errors) > 0 {
		return SandboxProbeReqMultiError(errors)
	}

	return nil
}

// SandboxProbeReqMultiError is an error wrapping multiple validation errors
// returned by SandboxProbeReq.ValidateAll() if the designated constraints
// aren't met.
type SandboxProbeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxProbeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxProbeReqMultiError) AllErrors() []error { return m }

// SandboxProbeReqValidationError is the validation error returned by
// SandboxProbeReq.Validate if the designated constraints aren't met.
type SandboxProbeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxProbeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxProbeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxProbeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxProbeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxProbeReqValidationError) ErrorName() string { return "SandboxProbeReqValidationError" }

// Error satisfies the builtin error interface
func (e SandboxProbeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxProbeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxProbeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxProbeReqValidationError{}

// Validate checks the field values on SandboxProbeResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SandboxProbeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxProbeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxProbeRespMultiError, or nil if none found.
func (m *SandboxProbeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxProbeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SandboxProbeRespMultiError(errors)
	}

	return nil
}

// SandboxProbeRespMultiError is an error wrapping multiple validation errors
// returned by SandboxProbeResp.ValidateAll() if the designated constraints
// aren't met.
type SandboxProbeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxProbeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxProbeRespMultiError) AllErrors() []error { return m }

// SandboxProbeRespValidationError is the validation error returned by
// SandboxProbeResp.Validate if the designated constraints aren't met.
type SandboxProbeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxProbeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxProbeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxProbeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxProbeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxProbeRespValidationError) ErrorName() string { return "SandboxProbeRespValidationError" }

// Error satisfies the builtin error interface
func (e SandboxProbeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxProbeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxProbeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxProbeRespValidationError{}

// Validate checks the field values on PushSandboxDetectStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PushSandboxDetectStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushSandboxDetectStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PushSandboxDetectStatusReqMultiError, or nil if none found.
func (m *PushSandboxDetectStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PushSandboxDetectStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for TaskStatus

	// no validation rules for Filename

	// no validation rules for Sha256

	// no validation rules for Md5

	// no validation rules for RiskLevel

	// no validation rules for RiskValue

	if len(errors) > 0 {
		return PushSandboxDetectStatusReqMultiError(errors)
	}

	return nil
}

// PushSandboxDetectStatusReqMultiError is an error wrapping multiple
// validation errors returned by PushSandboxDetectStatusReq.ValidateAll() if
// the designated constraints aren't met.
type PushSandboxDetectStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushSandboxDetectStatusReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushSandboxDetectStatusReqMultiError) AllErrors() []error { return m }

// PushSandboxDetectStatusReqValidationError is the validation error returned
// by PushSandboxDetectStatusReq.Validate if the designated constraints aren't met.
type PushSandboxDetectStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushSandboxDetectStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushSandboxDetectStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushSandboxDetectStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushSandboxDetectStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushSandboxDetectStatusReqValidationError) ErrorName() string {
	return "PushSandboxDetectStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e PushSandboxDetectStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushSandboxDetectStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushSandboxDetectStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushSandboxDetectStatusReqValidationError{}

// Validate checks the field values on PushSandboxDetectStatusResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PushSandboxDetectStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushSandboxDetectStatusResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PushSandboxDetectStatusRespMultiError, or nil if none found.
func (m *PushSandboxDetectStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PushSandboxDetectStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if len(errors) > 0 {
		return PushSandboxDetectStatusRespMultiError(errors)
	}

	return nil
}

// PushSandboxDetectStatusRespMultiError is an error wrapping multiple
// validation errors returned by PushSandboxDetectStatusResp.ValidateAll() if
// the designated constraints aren't met.
type PushSandboxDetectStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushSandboxDetectStatusRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushSandboxDetectStatusRespMultiError) AllErrors() []error { return m }

// PushSandboxDetectStatusRespValidationError is the validation error returned
// by PushSandboxDetectStatusResp.Validate if the designated constraints
// aren't met.
type PushSandboxDetectStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushSandboxDetectStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushSandboxDetectStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushSandboxDetectStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushSandboxDetectStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushSandboxDetectStatusRespValidationError) ErrorName() string {
	return "PushSandboxDetectStatusRespValidationError"
}

// Error satisfies the builtin error interface
func (e PushSandboxDetectStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushSandboxDetectStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushSandboxDetectStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushSandboxDetectStatusRespValidationError{}

// Validate checks the field values on SandboxTask with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SandboxTask) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxTask with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SandboxTaskMultiError, or
// nil if none found.
func (m *SandboxTask) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxTask) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for TaskStatus

	// no validation rules for Filename

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for RiskLevel

	// no validation rules for RiskValue

	// no validation rules for RawResult

	if len(errors) > 0 {
		return SandboxTaskMultiError(errors)
	}

	return nil
}

// SandboxTaskMultiError is an error wrapping multiple validation errors
// returned by SandboxTask.ValidateAll() if the designated constraints aren't met.
type SandboxTaskMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxTaskMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxTaskMultiError) AllErrors() []error { return m }

// SandboxTaskValidationError is the validation error returned by
// SandboxTask.Validate if the designated constraints aren't met.
type SandboxTaskValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxTaskValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxTaskValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxTaskValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxTaskValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxTaskValidationError) ErrorName() string { return "SandboxTaskValidationError" }

// Error satisfies the builtin error interface
func (e SandboxTaskValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxTask.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxTaskValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxTaskValidationError{}

// Validate checks the field values on QuerySandboxTaskStatusReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskStatusReqMultiError, or nil if none found.
func (m *QuerySandboxTaskStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Sha256

	// no validation rules for Md5

	if len(errors) > 0 {
		return QuerySandboxTaskStatusReqMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskStatusReqMultiError is an error wrapping multiple validation
// errors returned by QuerySandboxTaskStatusReq.ValidateAll() if the
// designated constraints aren't met.
type QuerySandboxTaskStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskStatusReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskStatusReqMultiError) AllErrors() []error { return m }

// QuerySandboxTaskStatusReqValidationError is the validation error returned by
// QuerySandboxTaskStatusReq.Validate if the designated constraints aren't met.
type QuerySandboxTaskStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskStatusReqValidationError) ErrorName() string {
	return "QuerySandboxTaskStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskStatusReqValidationError{}

// Validate checks the field values on QuerySandboxTaskStatusResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskStatusResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskStatusRespMultiError, or nil if none found.
func (m *QuerySandboxTaskStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuerySandboxTaskStatusRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuerySandboxTaskStatusRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuerySandboxTaskStatusRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QuerySandboxTaskStatusRespMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskStatusRespMultiError is an error wrapping multiple
// validation errors returned by QuerySandboxTaskStatusResp.ValidateAll() if
// the designated constraints aren't met.
type QuerySandboxTaskStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskStatusRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskStatusRespMultiError) AllErrors() []error { return m }

// QuerySandboxTaskStatusRespValidationError is the validation error returned
// by QuerySandboxTaskStatusResp.Validate if the designated constraints aren't met.
type QuerySandboxTaskStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskStatusRespValidationError) ErrorName() string {
	return "QuerySandboxTaskStatusRespValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskStatusRespValidationError{}

// Validate checks the field values on QuerySandboxTaskBatchStatusReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskBatchStatusReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskBatchStatusReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskBatchStatusReqMultiError, or nil if none found.
func (m *QuerySandboxTaskBatchStatusReq) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskBatchStatusReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return QuerySandboxTaskBatchStatusReqMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskBatchStatusReqMultiError is an error wrapping multiple
// validation errors returned by QuerySandboxTaskBatchStatusReq.ValidateAll()
// if the designated constraints aren't met.
type QuerySandboxTaskBatchStatusReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskBatchStatusReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskBatchStatusReqMultiError) AllErrors() []error { return m }

// QuerySandboxTaskBatchStatusReqValidationError is the validation error
// returned by QuerySandboxTaskBatchStatusReq.Validate if the designated
// constraints aren't met.
type QuerySandboxTaskBatchStatusReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskBatchStatusReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskBatchStatusReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskBatchStatusReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskBatchStatusReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskBatchStatusReqValidationError) ErrorName() string {
	return "QuerySandboxTaskBatchStatusReqValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskBatchStatusReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskBatchStatusReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskBatchStatusReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskBatchStatusReqValidationError{}

// Validate checks the field values on QuerySandboxTaskBatchStatusResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskBatchStatusResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskBatchStatusResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskBatchStatusRespMultiError, or nil if none found.
func (m *QuerySandboxTaskBatchStatusResp) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskBatchStatusResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QuerySandboxTaskBatchStatusRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QuerySandboxTaskBatchStatusRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QuerySandboxTaskBatchStatusRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QuerySandboxTaskBatchStatusRespMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskBatchStatusRespMultiError is an error wrapping multiple
// validation errors returned by QuerySandboxTaskBatchStatusResp.ValidateAll()
// if the designated constraints aren't met.
type QuerySandboxTaskBatchStatusRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskBatchStatusRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskBatchStatusRespMultiError) AllErrors() []error { return m }

// QuerySandboxTaskBatchStatusRespValidationError is the validation error
// returned by QuerySandboxTaskBatchStatusResp.Validate if the designated
// constraints aren't met.
type QuerySandboxTaskBatchStatusRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskBatchStatusRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskBatchStatusRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskBatchStatusRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskBatchStatusRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskBatchStatusRespValidationError) ErrorName() string {
	return "QuerySandboxTaskBatchStatusRespValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskBatchStatusRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskBatchStatusResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskBatchStatusRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskBatchStatusRespValidationError{}

// Validate checks the field values on QuerySandboxTaskResultReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskResultReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskResultReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskResultReqMultiError, or nil if none found.
func (m *QuerySandboxTaskResultReq) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskResultReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Sha256

	// no validation rules for Md5

	if len(errors) > 0 {
		return QuerySandboxTaskResultReqMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskResultReqMultiError is an error wrapping multiple validation
// errors returned by QuerySandboxTaskResultReq.ValidateAll() if the
// designated constraints aren't met.
type QuerySandboxTaskResultReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskResultReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskResultReqMultiError) AllErrors() []error { return m }

// QuerySandboxTaskResultReqValidationError is the validation error returned by
// QuerySandboxTaskResultReq.Validate if the designated constraints aren't met.
type QuerySandboxTaskResultReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskResultReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskResultReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskResultReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskResultReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskResultReqValidationError) ErrorName() string {
	return "QuerySandboxTaskResultReqValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskResultReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskResultReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskResultReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskResultReqValidationError{}

// Validate checks the field values on QuerySandboxTaskResultResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QuerySandboxTaskResultResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QuerySandboxTaskResultResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QuerySandboxTaskResultRespMultiError, or nil if none found.
func (m *QuerySandboxTaskResultResp) ValidateAll() error {
	return m.validate(true)
}

func (m *QuerySandboxTaskResultResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	// no validation rules for Msg

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QuerySandboxTaskResultRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QuerySandboxTaskResultRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QuerySandboxTaskResultRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QuerySandboxTaskResultRespMultiError(errors)
	}

	return nil
}

// QuerySandboxTaskResultRespMultiError is an error wrapping multiple
// validation errors returned by QuerySandboxTaskResultResp.ValidateAll() if
// the designated constraints aren't met.
type QuerySandboxTaskResultRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QuerySandboxTaskResultRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QuerySandboxTaskResultRespMultiError) AllErrors() []error { return m }

// QuerySandboxTaskResultRespValidationError is the validation error returned
// by QuerySandboxTaskResultResp.Validate if the designated constraints aren't met.
type QuerySandboxTaskResultRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QuerySandboxTaskResultRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QuerySandboxTaskResultRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QuerySandboxTaskResultRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QuerySandboxTaskResultRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QuerySandboxTaskResultRespValidationError) ErrorName() string {
	return "QuerySandboxTaskResultRespValidationError"
}

// Error satisfies the builtin error interface
func (e QuerySandboxTaskResultRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuerySandboxTaskResultResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QuerySandboxTaskResultRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QuerySandboxTaskResultRespValidationError{}

// Validate checks the field values on CloudSandboxProbeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloudSandboxProbeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloudSandboxProbeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloudSandboxProbeReqMultiError, or nil if none found.
func (m *CloudSandboxProbeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CloudSandboxProbeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AuthAddr

	// no validation rules for AuthKey

	// no validation rules for CloudAddr

	// no validation rules for CloudSecret

	// no validation rules for BucketAddr

	// no validation rules for BucketSecret

	if len(errors) > 0 {
		return CloudSandboxProbeReqMultiError(errors)
	}

	return nil
}

// CloudSandboxProbeReqMultiError is an error wrapping multiple validation
// errors returned by CloudSandboxProbeReq.ValidateAll() if the designated
// constraints aren't met.
type CloudSandboxProbeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloudSandboxProbeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloudSandboxProbeReqMultiError) AllErrors() []error { return m }

// CloudSandboxProbeReqValidationError is the validation error returned by
// CloudSandboxProbeReq.Validate if the designated constraints aren't met.
type CloudSandboxProbeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloudSandboxProbeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloudSandboxProbeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloudSandboxProbeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloudSandboxProbeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloudSandboxProbeReqValidationError) ErrorName() string {
	return "CloudSandboxProbeReqValidationError"
}

// Error satisfies the builtin error interface
func (e CloudSandboxProbeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloudSandboxProbeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloudSandboxProbeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloudSandboxProbeReqValidationError{}

// Validate checks the field values on CloudSandboxProbeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloudSandboxProbeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloudSandboxProbeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloudSandboxProbeRespMultiError, or nil if none found.
func (m *CloudSandboxProbeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CloudSandboxProbeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CloudSandboxProbeRespMultiError(errors)
	}

	return nil
}

// CloudSandboxProbeRespMultiError is an error wrapping multiple validation
// errors returned by CloudSandboxProbeResp.ValidateAll() if the designated
// constraints aren't met.
type CloudSandboxProbeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloudSandboxProbeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloudSandboxProbeRespMultiError) AllErrors() []error { return m }

// CloudSandboxProbeRespValidationError is the validation error returned by
// CloudSandboxProbeResp.Validate if the designated constraints aren't met.
type CloudSandboxProbeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloudSandboxProbeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloudSandboxProbeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloudSandboxProbeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloudSandboxProbeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloudSandboxProbeRespValidationError) ErrorName() string {
	return "CloudSandboxProbeRespValidationError"
}

// Error satisfies the builtin error interface
func (e CloudSandboxProbeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloudSandboxProbeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloudSandboxProbeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloudSandboxProbeRespValidationError{}

// Validate checks the field values on DetectCond with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DetectCond) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectCond with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DetectCondMultiError, or
// nil if none found.
func (m *DetectCond) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectCond) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Bucket

	// no validation rules for Path

	// no validation rules for Sha256

	// no validation rules for Md5

	// no validation rules for Retry

	// no validation rules for Timeout

	// no validation rules for Engine

	// no validation rules for BufChunkSize

	if len(errors) > 0 {
		return DetectCondMultiError(errors)
	}

	return nil
}

// DetectCondMultiError is an error wrapping multiple validation errors
// returned by DetectCond.ValidateAll() if the designated constraints aren't met.
type DetectCondMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectCondMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectCondMultiError) AllErrors() []error { return m }

// DetectCondValidationError is the validation error returned by
// DetectCond.Validate if the designated constraints aren't met.
type DetectCondValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectCondValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectCondValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectCondValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectCondValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectCondValidationError) ErrorName() string { return "DetectCondValidationError" }

// Error satisfies the builtin error interface
func (e DetectCondValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectCond.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectCondValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectCondValidationError{}

// Validate checks the field values on SandboxCreateResp_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SandboxCreateResp_Data) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxCreateResp_Data with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxCreateResp_DataMultiError, or nil if none found.
func (m *SandboxCreateResp_Data) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxCreateResp_Data) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return SandboxCreateResp_DataMultiError(errors)
	}

	return nil
}

// SandboxCreateResp_DataMultiError is an error wrapping multiple validation
// errors returned by SandboxCreateResp_Data.ValidateAll() if the designated
// constraints aren't met.
type SandboxCreateResp_DataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxCreateResp_DataMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxCreateResp_DataMultiError) AllErrors() []error { return m }

// SandboxCreateResp_DataValidationError is the validation error returned by
// SandboxCreateResp_Data.Validate if the designated constraints aren't met.
type SandboxCreateResp_DataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxCreateResp_DataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxCreateResp_DataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxCreateResp_DataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxCreateResp_DataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxCreateResp_DataValidationError) ErrorName() string {
	return "SandboxCreateResp_DataValidationError"
}

// Error satisfies the builtin error interface
func (e SandboxCreateResp_DataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxCreateResp_Data.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxCreateResp_DataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxCreateResp_DataValidationError{}
