// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: zyra/zyra.proto

package zyra

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Zyra_AddTask_FullMethodName                     = "/zyra.Zyra/AddTask"
	Zyra_SandboxProbe_FullMethodName                = "/zyra.Zyra/SandboxProbe"
	Zyra_PushSandboxDetectStatus_FullMethodName     = "/zyra.Zyra/PushSandboxDetectStatus"
	Zyra_PushSandboxAlive_FullMethodName            = "/zyra.Zyra/PushSandboxAlive"
	Zyra_QuerySandboxTaskStatus_FullMethodName      = "/zyra.Zyra/QuerySandboxTaskStatus"
	Zyra_QuerySandboxTaskBatchStatus_FullMethodName = "/zyra.Zyra/QuerySandboxTaskBatchStatus"
	Zyra_QuerySandboxTaskResult_FullMethodName      = "/zyra.Zyra/QuerySandboxTaskResult"
	Zyra_CloudSandboxProbe_FullMethodName           = "/zyra.Zyra/CloudSandboxProbe"
)

// ZyraClient is the client API for Zyra service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 文件风险检测增强服务
type ZyraClient interface {
	// 添加检测任务（模拟yone推送MQ，测试专用）
	AddTask(ctx context.Context, in *AddTaskReq, opts ...grpc.CallOption) (*AddTaskResp, error)
	// 自研沙箱检测（沙箱探活）
	SandboxProbe(ctx context.Context, in *SandboxProbeReq, opts ...grpc.CallOption) (*SandboxProbeResp, error)
	// 自研沙箱检测（检测任务状态回调）
	PushSandboxDetectStatus(ctx context.Context, in *PushSandboxDetectStatusReq, opts ...grpc.CallOption) (*PushSandboxDetectStatusResp, error)
	// 自研沙箱检测（设备探活）
	PushSandboxAlive(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 自研沙箱任务状态查询
	QuerySandboxTaskStatus(ctx context.Context, in *QuerySandboxTaskStatusReq, opts ...grpc.CallOption) (*QuerySandboxTaskStatusResp, error)
	// 自研沙箱任务状态批量查询
	QuerySandboxTaskBatchStatus(ctx context.Context, in *QuerySandboxTaskBatchStatusReq, opts ...grpc.CallOption) (*QuerySandboxTaskBatchStatusResp, error)
	// 自研沙箱任务结果查询
	QuerySandboxTaskResult(ctx context.Context, in *QuerySandboxTaskResultReq, opts ...grpc.CallOption) (*QuerySandboxTaskResultResp, error)
	// 云沙箱检测（沙箱探活）
	CloudSandboxProbe(ctx context.Context, in *CloudSandboxProbeReq, opts ...grpc.CallOption) (*CloudSandboxProbeResp, error)
}

type zyraClient struct {
	cc grpc.ClientConnInterface
}

func NewZyraClient(cc grpc.ClientConnInterface) ZyraClient {
	return &zyraClient{cc}
}

func (c *zyraClient) AddTask(ctx context.Context, in *AddTaskReq, opts ...grpc.CallOption) (*AddTaskResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddTaskResp)
	err := c.cc.Invoke(ctx, Zyra_AddTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) SandboxProbe(ctx context.Context, in *SandboxProbeReq, opts ...grpc.CallOption) (*SandboxProbeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SandboxProbeResp)
	err := c.cc.Invoke(ctx, Zyra_SandboxProbe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) PushSandboxDetectStatus(ctx context.Context, in *PushSandboxDetectStatusReq, opts ...grpc.CallOption) (*PushSandboxDetectStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushSandboxDetectStatusResp)
	err := c.cc.Invoke(ctx, Zyra_PushSandboxDetectStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) PushSandboxAlive(ctx context.Context, in *emptypb.Empty, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Zyra_PushSandboxAlive_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) QuerySandboxTaskStatus(ctx context.Context, in *QuerySandboxTaskStatusReq, opts ...grpc.CallOption) (*QuerySandboxTaskStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuerySandboxTaskStatusResp)
	err := c.cc.Invoke(ctx, Zyra_QuerySandboxTaskStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) QuerySandboxTaskBatchStatus(ctx context.Context, in *QuerySandboxTaskBatchStatusReq, opts ...grpc.CallOption) (*QuerySandboxTaskBatchStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuerySandboxTaskBatchStatusResp)
	err := c.cc.Invoke(ctx, Zyra_QuerySandboxTaskBatchStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) QuerySandboxTaskResult(ctx context.Context, in *QuerySandboxTaskResultReq, opts ...grpc.CallOption) (*QuerySandboxTaskResultResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QuerySandboxTaskResultResp)
	err := c.cc.Invoke(ctx, Zyra_QuerySandboxTaskResult_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *zyraClient) CloudSandboxProbe(ctx context.Context, in *CloudSandboxProbeReq, opts ...grpc.CallOption) (*CloudSandboxProbeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CloudSandboxProbeResp)
	err := c.cc.Invoke(ctx, Zyra_CloudSandboxProbe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ZyraServer is the server API for Zyra service.
// All implementations must embed UnimplementedZyraServer
// for forward compatibility.
//
// 文件风险检测增强服务
type ZyraServer interface {
	// 添加检测任务（模拟yone推送MQ，测试专用）
	AddTask(context.Context, *AddTaskReq) (*AddTaskResp, error)
	// 自研沙箱检测（沙箱探活）
	SandboxProbe(context.Context, *SandboxProbeReq) (*SandboxProbeResp, error)
	// 自研沙箱检测（检测任务状态回调）
	PushSandboxDetectStatus(context.Context, *PushSandboxDetectStatusReq) (*PushSandboxDetectStatusResp, error)
	// 自研沙箱检测（设备探活）
	PushSandboxAlive(context.Context, *emptypb.Empty) (*emptypb.Empty, error)
	// 自研沙箱任务状态查询
	QuerySandboxTaskStatus(context.Context, *QuerySandboxTaskStatusReq) (*QuerySandboxTaskStatusResp, error)
	// 自研沙箱任务状态批量查询
	QuerySandboxTaskBatchStatus(context.Context, *QuerySandboxTaskBatchStatusReq) (*QuerySandboxTaskBatchStatusResp, error)
	// 自研沙箱任务结果查询
	QuerySandboxTaskResult(context.Context, *QuerySandboxTaskResultReq) (*QuerySandboxTaskResultResp, error)
	// 云沙箱检测（沙箱探活）
	CloudSandboxProbe(context.Context, *CloudSandboxProbeReq) (*CloudSandboxProbeResp, error)
	mustEmbedUnimplementedZyraServer()
}

// UnimplementedZyraServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedZyraServer struct{}

func (UnimplementedZyraServer) AddTask(context.Context, *AddTaskReq) (*AddTaskResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddTask not implemented")
}
func (UnimplementedZyraServer) SandboxProbe(context.Context, *SandboxProbeReq) (*SandboxProbeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SandboxProbe not implemented")
}
func (UnimplementedZyraServer) PushSandboxDetectStatus(context.Context, *PushSandboxDetectStatusReq) (*PushSandboxDetectStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushSandboxDetectStatus not implemented")
}
func (UnimplementedZyraServer) PushSandboxAlive(context.Context, *emptypb.Empty) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushSandboxAlive not implemented")
}
func (UnimplementedZyraServer) QuerySandboxTaskStatus(context.Context, *QuerySandboxTaskStatusReq) (*QuerySandboxTaskStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySandboxTaskStatus not implemented")
}
func (UnimplementedZyraServer) QuerySandboxTaskBatchStatus(context.Context, *QuerySandboxTaskBatchStatusReq) (*QuerySandboxTaskBatchStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySandboxTaskBatchStatus not implemented")
}
func (UnimplementedZyraServer) QuerySandboxTaskResult(context.Context, *QuerySandboxTaskResultReq) (*QuerySandboxTaskResultResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuerySandboxTaskResult not implemented")
}
func (UnimplementedZyraServer) CloudSandboxProbe(context.Context, *CloudSandboxProbeReq) (*CloudSandboxProbeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloudSandboxProbe not implemented")
}
func (UnimplementedZyraServer) mustEmbedUnimplementedZyraServer() {}
func (UnimplementedZyraServer) testEmbeddedByValue()              {}

// UnsafeZyraServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ZyraServer will
// result in compilation errors.
type UnsafeZyraServer interface {
	mustEmbedUnimplementedZyraServer()
}

func RegisterZyraServer(s grpc.ServiceRegistrar, srv ZyraServer) {
	// If the following call pancis, it indicates UnimplementedZyraServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Zyra_ServiceDesc, srv)
}

func _Zyra_AddTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).AddTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_AddTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).AddTask(ctx, req.(*AddTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_SandboxProbe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SandboxProbeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).SandboxProbe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_SandboxProbe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).SandboxProbe(ctx, req.(*SandboxProbeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_PushSandboxDetectStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushSandboxDetectStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).PushSandboxDetectStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_PushSandboxDetectStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).PushSandboxDetectStatus(ctx, req.(*PushSandboxDetectStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_PushSandboxAlive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emptypb.Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).PushSandboxAlive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_PushSandboxAlive_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).PushSandboxAlive(ctx, req.(*emptypb.Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_QuerySandboxTaskStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySandboxTaskStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).QuerySandboxTaskStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_QuerySandboxTaskStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).QuerySandboxTaskStatus(ctx, req.(*QuerySandboxTaskStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_QuerySandboxTaskBatchStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySandboxTaskBatchStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).QuerySandboxTaskBatchStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_QuerySandboxTaskBatchStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).QuerySandboxTaskBatchStatus(ctx, req.(*QuerySandboxTaskBatchStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_QuerySandboxTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuerySandboxTaskResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).QuerySandboxTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_QuerySandboxTaskResult_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).QuerySandboxTaskResult(ctx, req.(*QuerySandboxTaskResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Zyra_CloudSandboxProbe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloudSandboxProbeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ZyraServer).CloudSandboxProbe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Zyra_CloudSandboxProbe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ZyraServer).CloudSandboxProbe(ctx, req.(*CloudSandboxProbeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Zyra_ServiceDesc is the grpc.ServiceDesc for Zyra service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Zyra_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zyra.Zyra",
	HandlerType: (*ZyraServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddTask",
			Handler:    _Zyra_AddTask_Handler,
		},
		{
			MethodName: "SandboxProbe",
			Handler:    _Zyra_SandboxProbe_Handler,
		},
		{
			MethodName: "PushSandboxDetectStatus",
			Handler:    _Zyra_PushSandboxDetectStatus_Handler,
		},
		{
			MethodName: "PushSandboxAlive",
			Handler:    _Zyra_PushSandboxAlive_Handler,
		},
		{
			MethodName: "QuerySandboxTaskStatus",
			Handler:    _Zyra_QuerySandboxTaskStatus_Handler,
		},
		{
			MethodName: "QuerySandboxTaskBatchStatus",
			Handler:    _Zyra_QuerySandboxTaskBatchStatus_Handler,
		},
		{
			MethodName: "QuerySandboxTaskResult",
			Handler:    _Zyra_QuerySandboxTaskResult_Handler,
		},
		{
			MethodName: "CloudSandboxProbe",
			Handler:    _Zyra_CloudSandboxProbe_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "zyra/zyra.proto",
}
