// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: canon/canon.proto

package canon

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Canon_GetEventListStream_FullMethodName = "/canon.Canon/GetEventListStream"
	Canon_PutEventListStream_FullMethodName = "/canon.Canon/PutEventListStream"
)

// CanonClient is the client API for Canon service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CanonClient interface {
	// 根据指定的查询条件, 流式提取事件列表信息, 详情信息和溯源图信息
	GetEventListStream(ctx context.Context, in *EventListStreamReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[EventListStreamResp], error)
	// 流式上传事件列信息, 详情信息和溯源图信息, 用于导入事件数据
	PutEventListStream(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[PutEventListStreamReq, emptypb.Empty], error)
}

type canonClient struct {
	cc grpc.ClientConnInterface
}

func NewCanonClient(cc grpc.ClientConnInterface) CanonClient {
	return &canonClient{cc}
}

func (c *canonClient) GetEventListStream(ctx context.Context, in *EventListStreamReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[EventListStreamResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Canon_ServiceDesc.Streams[0], Canon_GetEventListStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[EventListStreamReq, EventListStreamResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Canon_GetEventListStreamClient = grpc.ServerStreamingClient[EventListStreamResp]

func (c *canonClient) PutEventListStream(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[PutEventListStreamReq, emptypb.Empty], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Canon_ServiceDesc.Streams[1], Canon_PutEventListStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[PutEventListStreamReq, emptypb.Empty]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Canon_PutEventListStreamClient = grpc.ClientStreamingClient[PutEventListStreamReq, emptypb.Empty]

// CanonServer is the server API for Canon service.
// All implementations must embed UnimplementedCanonServer
// for forward compatibility.
type CanonServer interface {
	// 根据指定的查询条件, 流式提取事件列表信息, 详情信息和溯源图信息
	GetEventListStream(*EventListStreamReq, grpc.ServerStreamingServer[EventListStreamResp]) error
	// 流式上传事件列信息, 详情信息和溯源图信息, 用于导入事件数据
	PutEventListStream(grpc.ClientStreamingServer[PutEventListStreamReq, emptypb.Empty]) error
	mustEmbedUnimplementedCanonServer()
}

// UnimplementedCanonServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCanonServer struct{}

func (UnimplementedCanonServer) GetEventListStream(*EventListStreamReq, grpc.ServerStreamingServer[EventListStreamResp]) error {
	return status.Errorf(codes.Unimplemented, "method GetEventListStream not implemented")
}
func (UnimplementedCanonServer) PutEventListStream(grpc.ClientStreamingServer[PutEventListStreamReq, emptypb.Empty]) error {
	return status.Errorf(codes.Unimplemented, "method PutEventListStream not implemented")
}
func (UnimplementedCanonServer) mustEmbedUnimplementedCanonServer() {}
func (UnimplementedCanonServer) testEmbeddedByValue()               {}

// UnsafeCanonServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CanonServer will
// result in compilation errors.
type UnsafeCanonServer interface {
	mustEmbedUnimplementedCanonServer()
}

func RegisterCanonServer(s grpc.ServiceRegistrar, srv CanonServer) {
	// If the following call pancis, it indicates UnimplementedCanonServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Canon_ServiceDesc, srv)
}

func _Canon_GetEventListStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(EventListStreamReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(CanonServer).GetEventListStream(m, &grpc.GenericServerStream[EventListStreamReq, EventListStreamResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Canon_GetEventListStreamServer = grpc.ServerStreamingServer[EventListStreamResp]

func _Canon_PutEventListStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(CanonServer).PutEventListStream(&grpc.GenericServerStream[PutEventListStreamReq, emptypb.Empty]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Canon_PutEventListStreamServer = grpc.ClientStreamingServer[PutEventListStreamReq, emptypb.Empty]

// Canon_ServiceDesc is the grpc.ServiceDesc for Canon service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Canon_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "canon.Canon",
	HandlerType: (*CanonServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GetEventListStream",
			Handler:       _Canon_GetEventListStream_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "PutEventListStream",
			Handler:       _Canon_PutEventListStream_Handler,
			ClientStreams: true,
		},
	},
	Metadata: "canon/canon.proto",
}
