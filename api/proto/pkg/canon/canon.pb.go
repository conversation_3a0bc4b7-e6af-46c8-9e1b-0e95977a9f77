// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: canon/canon.proto

package canon

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 错误码枚举
type ErrorCodes_Enum int32

const (
	ErrorCodes_Unknown ErrorCodes_Enum = 0
)

// Enum value maps for ErrorCodes_Enum.
var (
	ErrorCodes_Enum_name = map[int32]string{
		0: "Unknown",
	}
	ErrorCodes_Enum_value = map[string]int32{
		"Unknown": 0,
	}
)

func (x ErrorCodes_Enum) Enum() *ErrorCodes_Enum {
	p := new(ErrorCodes_Enum)
	*p = x
	return p
}

func (x ErrorCodes_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorCodes_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_canon_canon_proto_enumTypes[0].Descriptor()
}

func (ErrorCodes_Enum) Type() protoreflect.EnumType {
	return &file_canon_canon_proto_enumTypes[0]
}

func (x ErrorCodes_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorCodes_Enum.Descriptor instead.
func (ErrorCodes_Enum) EnumDescriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{0, 0}
}

// 错误码
type ErrorCodes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ErrorCodes) Reset() {
	*x = ErrorCodes{}
	mi := &file_canon_canon_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorCodes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorCodes) ProtoMessage() {}

func (x *ErrorCodes) ProtoReflect() protoreflect.Message {
	mi := &file_canon_canon_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorCodes.ProtoReflect.Descriptor instead.
func (*ErrorCodes) Descriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{0}
}

// 事件查询条件
type EventListStreamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostIds    []string               `protobuf:"bytes,1,rep,name=host_ids,json=hostIds,proto3" json:"host_ids,omitempty"`
	RiskTypes  []string               `protobuf:"bytes,2,rep,name=risk_types,json=riskTypes,proto3" json:"risk_types,omitempty"`
	RiskLevels []uint32               `protobuf:"varint,3,rep,packed,name=risk_levels,json=riskLevels,proto3" json:"risk_levels,omitempty"`
	TimeSince  *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=time_since,json=timeSince,proto3" json:"time_since,omitempty"`
	TimeUntil  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=time_until,json=timeUntil,proto3" json:"time_until,omitempty"`
	Archived   bool                   `protobuf:"varint,6,opt,name=archived,proto3" json:"archived,omitempty"` // 是否查询归档数据
}

func (x *EventListStreamReq) Reset() {
	*x = EventListStreamReq{}
	mi := &file_canon_canon_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventListStreamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventListStreamReq) ProtoMessage() {}

func (x *EventListStreamReq) ProtoReflect() protoreflect.Message {
	mi := &file_canon_canon_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventListStreamReq.ProtoReflect.Descriptor instead.
func (*EventListStreamReq) Descriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{1}
}

func (x *EventListStreamReq) GetHostIds() []string {
	if x != nil {
		return x.HostIds
	}
	return nil
}

func (x *EventListStreamReq) GetRiskTypes() []string {
	if x != nil {
		return x.RiskTypes
	}
	return nil
}

func (x *EventListStreamReq) GetRiskLevels() []uint32 {
	if x != nil {
		return x.RiskLevels
	}
	return nil
}

func (x *EventListStreamReq) GetTimeSince() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeSince
	}
	return nil
}

func (x *EventListStreamReq) GetTimeUntil() *timestamppb.Timestamp {
	if x != nil {
		return x.TimeUntil
	}
	return nil
}

func (x *EventListStreamReq) GetArchived() bool {
	if x != nil {
		return x.Archived
	}
	return false
}

// 单条事件信息, 包含列表摘要, 详情和溯源图数据
type EventListStreamItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	HostId       string                 `protobuf:"bytes,2,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
	UpdatedAt    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	Details      []byte                 `protobuf:"bytes,5,opt,name=details,proto3" json:"details,omitempty"`                               // 详情页数据
	GraphDetails []byte                 `protobuf:"bytes,6,opt,name=graph_details,json=graphDetails,proto3" json:"graph_details,omitempty"` // 溯源图数据
}

func (x *EventListStreamItem) Reset() {
	*x = EventListStreamItem{}
	mi := &file_canon_canon_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventListStreamItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventListStreamItem) ProtoMessage() {}

func (x *EventListStreamItem) ProtoReflect() protoreflect.Message {
	mi := &file_canon_canon_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventListStreamItem.ProtoReflect.Descriptor instead.
func (*EventListStreamItem) Descriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{2}
}

func (x *EventListStreamItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *EventListStreamItem) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *EventListStreamItem) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *EventListStreamItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *EventListStreamItem) GetDetails() []byte {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *EventListStreamItem) GetGraphDetails() []byte {
	if x != nil {
		return x.GraphDetails
	}
	return nil
}

// 单个流数据包的内容结构, 包含一组事件信息
type EventListStreamResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*EventListStreamItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *EventListStreamResp) Reset() {
	*x = EventListStreamResp{}
	mi := &file_canon_canon_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventListStreamResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventListStreamResp) ProtoMessage() {}

func (x *EventListStreamResp) ProtoReflect() protoreflect.Message {
	mi := &file_canon_canon_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventListStreamResp.ProtoReflect.Descriptor instead.
func (*EventListStreamResp) Descriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{3}
}

func (x *EventListStreamResp) GetItems() []*EventListStreamItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type PutEventListStreamReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*EventListStreamItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *PutEventListStreamReq) Reset() {
	*x = PutEventListStreamReq{}
	mi := &file_canon_canon_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PutEventListStreamReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutEventListStreamReq) ProtoMessage() {}

func (x *PutEventListStreamReq) ProtoReflect() protoreflect.Message {
	mi := &file_canon_canon_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutEventListStreamReq.ProtoReflect.Descriptor instead.
func (*PutEventListStreamReq) Descriptor() ([]byte, []int) {
	return file_canon_canon_proto_rawDescGZIP(), []int{4}
}

func (x *PutEventListStreamReq) GetItems() []*EventListStreamItem {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_canon_canon_proto protoreflect.FileDescriptor

var file_canon_canon_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x2f, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x21, 0x0a, 0x0a, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x22, 0x13, 0x0a, 0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b,
	0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x22, 0x81, 0x02, 0x0a, 0x12,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x53, 0x69, 0x6e, 0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65,
	0x5f, 0x75, 0x6e, 0x74, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x55, 0x6e,
	0x74, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x64, 0x22,
	0xf3, 0x01, 0x0a, 0x13, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x23, 0x0a, 0x0d, 0x67, 0x72, 0x61, 0x70, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x67, 0x72, 0x61, 0x70, 0x68, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x47, 0x0a, 0x13, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61,
	0x6e, 0x6f, 0x6e, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x49,
	0x0a, 0x15, 0x50, 0x75, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x32, 0xa8, 0x01, 0x0a, 0x05, 0x43, 0x61,
	0x6e, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x19, 0x2e, 0x63, 0x61, 0x6e, 0x6f,
	0x6e, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x61, 0x6e, 0x6f, 0x6e, 0x2e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x30, 0x01, 0x12, 0x4e, 0x0a, 0x12, 0x50, 0x75, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x1c, 0x2e, 0x63, 0x61, 0x6e,
	0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x28, 0x01, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x61, 0x6e, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_canon_canon_proto_rawDescOnce sync.Once
	file_canon_canon_proto_rawDescData = file_canon_canon_proto_rawDesc
)

func file_canon_canon_proto_rawDescGZIP() []byte {
	file_canon_canon_proto_rawDescOnce.Do(func() {
		file_canon_canon_proto_rawDescData = protoimpl.X.CompressGZIP(file_canon_canon_proto_rawDescData)
	})
	return file_canon_canon_proto_rawDescData
}

var file_canon_canon_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_canon_canon_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_canon_canon_proto_goTypes = []any{
	(ErrorCodes_Enum)(0),          // 0: canon.ErrorCodes.Enum
	(*ErrorCodes)(nil),            // 1: canon.ErrorCodes
	(*EventListStreamReq)(nil),    // 2: canon.EventListStreamReq
	(*EventListStreamItem)(nil),   // 3: canon.EventListStreamItem
	(*EventListStreamResp)(nil),   // 4: canon.EventListStreamResp
	(*PutEventListStreamReq)(nil), // 5: canon.PutEventListStreamReq
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 7: google.protobuf.Empty
}
var file_canon_canon_proto_depIdxs = []int32{
	6, // 0: canon.EventListStreamReq.time_since:type_name -> google.protobuf.Timestamp
	6, // 1: canon.EventListStreamReq.time_until:type_name -> google.protobuf.Timestamp
	6, // 2: canon.EventListStreamItem.updated_at:type_name -> google.protobuf.Timestamp
	6, // 3: canon.EventListStreamItem.created_at:type_name -> google.protobuf.Timestamp
	3, // 4: canon.EventListStreamResp.items:type_name -> canon.EventListStreamItem
	3, // 5: canon.PutEventListStreamReq.items:type_name -> canon.EventListStreamItem
	2, // 6: canon.Canon.GetEventListStream:input_type -> canon.EventListStreamReq
	5, // 7: canon.Canon.PutEventListStream:input_type -> canon.PutEventListStreamReq
	4, // 8: canon.Canon.GetEventListStream:output_type -> canon.EventListStreamResp
	7, // 9: canon.Canon.PutEventListStream:output_type -> google.protobuf.Empty
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_canon_canon_proto_init() }
func file_canon_canon_proto_init() {
	if File_canon_canon_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_canon_canon_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_canon_canon_proto_goTypes,
		DependencyIndexes: file_canon_canon_proto_depIdxs,
		EnumInfos:         file_canon_canon_proto_enumTypes,
		MessageInfos:      file_canon_canon_proto_msgTypes,
	}.Build()
	File_canon_canon_proto = out.File
	file_canon_canon_proto_rawDesc = nil
	file_canon_canon_proto_goTypes = nil
	file_canon_canon_proto_depIdxs = nil
}
