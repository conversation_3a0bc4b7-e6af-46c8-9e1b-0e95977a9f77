// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: canon/canon.proto

package canon

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ErrorCodes with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ErrorCodes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ErrorCodes with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ErrorCodesMultiError, or
// nil if none found.
func (m *ErrorCodes) ValidateAll() error {
	return m.validate(true)
}

func (m *ErrorCodes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ErrorCodesMultiError(errors)
	}

	return nil
}

// ErrorCodesMultiError is an error wrapping multiple validation errors
// returned by ErrorCodes.ValidateAll() if the designated constraints aren't met.
type ErrorCodesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ErrorCodesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ErrorCodesMultiError) AllErrors() []error { return m }

// ErrorCodesValidationError is the validation error returned by
// ErrorCodes.Validate if the designated constraints aren't met.
type ErrorCodesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ErrorCodesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ErrorCodesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ErrorCodesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ErrorCodesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ErrorCodesValidationError) ErrorName() string { return "ErrorCodesValidationError" }

// Error satisfies the builtin error interface
func (e ErrorCodesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sErrorCodes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ErrorCodesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ErrorCodesValidationError{}

// Validate checks the field values on EventListStreamReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EventListStreamReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EventListStreamReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EventListStreamReqMultiError, or nil if none found.
func (m *EventListStreamReq) ValidateAll() error {
	return m.validate(true)
}

func (m *EventListStreamReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeSince()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EventListStreamReqValidationError{
					field:  "TimeSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EventListStreamReqValidationError{
					field:  "TimeSince",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeSince()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EventListStreamReqValidationError{
				field:  "TimeSince",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTimeUntil()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EventListStreamReqValidationError{
					field:  "TimeUntil",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EventListStreamReqValidationError{
					field:  "TimeUntil",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeUntil()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EventListStreamReqValidationError{
				field:  "TimeUntil",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Archived

	if len(errors) > 0 {
		return EventListStreamReqMultiError(errors)
	}

	return nil
}

// EventListStreamReqMultiError is an error wrapping multiple validation errors
// returned by EventListStreamReq.ValidateAll() if the designated constraints
// aren't met.
type EventListStreamReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EventListStreamReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EventListStreamReqMultiError) AllErrors() []error { return m }

// EventListStreamReqValidationError is the validation error returned by
// EventListStreamReq.Validate if the designated constraints aren't met.
type EventListStreamReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EventListStreamReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EventListStreamReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EventListStreamReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EventListStreamReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EventListStreamReqValidationError) ErrorName() string {
	return "EventListStreamReqValidationError"
}

// Error satisfies the builtin error interface
func (e EventListStreamReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEventListStreamReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EventListStreamReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EventListStreamReqValidationError{}

// Validate checks the field values on EventListStreamItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EventListStreamItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EventListStreamItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EventListStreamItemMultiError, or nil if none found.
func (m *EventListStreamItem) ValidateAll() error {
	return m.validate(true)
}

func (m *EventListStreamItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for HostId

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EventListStreamItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EventListStreamItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EventListStreamItemValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EventListStreamItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EventListStreamItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EventListStreamItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Details

	// no validation rules for GraphDetails

	if len(errors) > 0 {
		return EventListStreamItemMultiError(errors)
	}

	return nil
}

// EventListStreamItemMultiError is an error wrapping multiple validation
// errors returned by EventListStreamItem.ValidateAll() if the designated
// constraints aren't met.
type EventListStreamItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EventListStreamItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EventListStreamItemMultiError) AllErrors() []error { return m }

// EventListStreamItemValidationError is the validation error returned by
// EventListStreamItem.Validate if the designated constraints aren't met.
type EventListStreamItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EventListStreamItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EventListStreamItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EventListStreamItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EventListStreamItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EventListStreamItemValidationError) ErrorName() string {
	return "EventListStreamItemValidationError"
}

// Error satisfies the builtin error interface
func (e EventListStreamItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEventListStreamItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EventListStreamItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EventListStreamItemValidationError{}

// Validate checks the field values on EventListStreamResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EventListStreamResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EventListStreamResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EventListStreamRespMultiError, or nil if none found.
func (m *EventListStreamResp) ValidateAll() error {
	return m.validate(true)
}

func (m *EventListStreamResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EventListStreamRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EventListStreamRespValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EventListStreamRespValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EventListStreamRespMultiError(errors)
	}

	return nil
}

// EventListStreamRespMultiError is an error wrapping multiple validation
// errors returned by EventListStreamResp.ValidateAll() if the designated
// constraints aren't met.
type EventListStreamRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EventListStreamRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EventListStreamRespMultiError) AllErrors() []error { return m }

// EventListStreamRespValidationError is the validation error returned by
// EventListStreamResp.Validate if the designated constraints aren't met.
type EventListStreamRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EventListStreamRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EventListStreamRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EventListStreamRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EventListStreamRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EventListStreamRespValidationError) ErrorName() string {
	return "EventListStreamRespValidationError"
}

// Error satisfies the builtin error interface
func (e EventListStreamRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEventListStreamResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EventListStreamRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EventListStreamRespValidationError{}

// Validate checks the field values on PutEventListStreamReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PutEventListStreamReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PutEventListStreamReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PutEventListStreamReqMultiError, or nil if none found.
func (m *PutEventListStreamReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PutEventListStreamReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PutEventListStreamReqValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PutEventListStreamReqValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PutEventListStreamReqValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PutEventListStreamReqMultiError(errors)
	}

	return nil
}

// PutEventListStreamReqMultiError is an error wrapping multiple validation
// errors returned by PutEventListStreamReq.ValidateAll() if the designated
// constraints aren't met.
type PutEventListStreamReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PutEventListStreamReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PutEventListStreamReqMultiError) AllErrors() []error { return m }

// PutEventListStreamReqValidationError is the validation error returned by
// PutEventListStreamReq.Validate if the designated constraints aren't met.
type PutEventListStreamReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PutEventListStreamReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PutEventListStreamReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PutEventListStreamReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PutEventListStreamReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PutEventListStreamReqValidationError) ErrorName() string {
	return "PutEventListStreamReqValidationError"
}

// Error satisfies the builtin error interface
func (e PutEventListStreamReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPutEventListStreamReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PutEventListStreamReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PutEventListStreamReqValidationError{}
