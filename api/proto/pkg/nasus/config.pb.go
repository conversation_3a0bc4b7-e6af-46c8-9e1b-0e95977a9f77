// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: nasus/config.proto

package nasus

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Web端全量业务配置
type BusiConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	System     *SystemConfig     `protobuf:"bytes,1,opt,name=system,proto3" json:"system,omitempty"`                           // 系统配置
	FileThreat *FileThreatConfig `protobuf:"bytes,2,opt,name=file_threat,json=fileThreat,proto3" json:"file_threat,omitempty"` // 文件威胁配置
	LogArchive *LogArchiveConfig `protobuf:"bytes,3,opt,name=log_archive,json=logArchive,proto3" json:"log_archive,omitempty"` // 日志归档配置
}

func (x *BusiConfig) Reset() {
	*x = BusiConfig{}
	mi := &file_nasus_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BusiConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BusiConfig) ProtoMessage() {}

func (x *BusiConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BusiConfig.ProtoReflect.Descriptor instead.
func (*BusiConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{0}
}

func (x *BusiConfig) GetSystem() *SystemConfig {
	if x != nil {
		return x.System
	}
	return nil
}

func (x *BusiConfig) GetFileThreat() *FileThreatConfig {
	if x != nil {
		return x.FileThreat
	}
	return nil
}

func (x *BusiConfig) GetLogArchive() *LogArchiveConfig {
	if x != nil {
		return x.LogArchive
	}
	return nil
}

type SystemConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ganged    *GangedConfig    `protobuf:"bytes,1,opt,name=ganged,proto3" json:"ganged,omitempty"`
	EngineLib *EngineLibConfig `protobuf:"bytes,2,opt,name=engine_lib,json=engineLib,proto3" json:"engine_lib,omitempty"`
	Common    *CommonConfig    `protobuf:"bytes,3,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *SystemConfig) Reset() {
	*x = SystemConfig{}
	mi := &file_nasus_config_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemConfig) ProtoMessage() {}

func (x *SystemConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemConfig.ProtoReflect.Descriptor instead.
func (*SystemConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{1}
}

func (x *SystemConfig) GetGanged() *GangedConfig {
	if x != nil {
		return x.Ganged
	}
	return nil
}

func (x *SystemConfig) GetEngineLib() *EngineLibConfig {
	if x != nil {
		return x.EngineLib
	}
	return nil
}

func (x *SystemConfig) GetCommon() *CommonConfig {
	if x != nil {
		return x.Common
	}
	return nil
}

type GangedConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetProtectCloud *NetProtectCloudConfig `protobuf:"bytes,1,opt,name=net_protect_cloud,json=netProtectCloud,proto3" json:"net_protect_cloud,omitempty"`
	Sandbox         *SandboxConfig         `protobuf:"bytes,2,opt,name=sandbox,proto3" json:"sandbox,omitempty"`
	Ntp             *NTPConfig             `protobuf:"bytes,3,opt,name=ntp,proto3" json:"ntp,omitempty"`
	Access          *AccessConfig          `protobuf:"bytes,4,opt,name=access,proto3" json:"access,omitempty"`
	CloudV01        *CloudV01Config        `protobuf:"bytes,5,opt,name=cloud_v01,json=cloudV01,proto3" json:"cloud_v01,omitempty"`
	CloudModel      *CloudModelConfig      `protobuf:"bytes,6,opt,name=cloud_model,json=cloudModel,proto3" json:"cloud_model,omitempty"`
}

func (x *GangedConfig) Reset() {
	*x = GangedConfig{}
	mi := &file_nasus_config_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GangedConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GangedConfig) ProtoMessage() {}

func (x *GangedConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GangedConfig.ProtoReflect.Descriptor instead.
func (*GangedConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{2}
}

func (x *GangedConfig) GetNetProtectCloud() *NetProtectCloudConfig {
	if x != nil {
		return x.NetProtectCloud
	}
	return nil
}

func (x *GangedConfig) GetSandbox() *SandboxConfig {
	if x != nil {
		return x.Sandbox
	}
	return nil
}

func (x *GangedConfig) GetNtp() *NTPConfig {
	if x != nil {
		return x.Ntp
	}
	return nil
}

func (x *GangedConfig) GetAccess() *AccessConfig {
	if x != nil {
		return x.Access
	}
	return nil
}

func (x *GangedConfig) GetCloudV01() *CloudV01Config {
	if x != nil {
		return x.CloudV01
	}
	return nil
}

func (x *GangedConfig) GetCloudModel() *CloudModelConfig {
	if x != nil {
		return x.CloudModel
	}
	return nil
}

type NetProtectCloudConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable                 bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	ConnectModel           int32  `protobuf:"varint,2,opt,name=connect_model,json=connectModel,proto3" json:"connect_model,omitempty"`
	AuthAddr               string `protobuf:"bytes,3,opt,name=auth_addr,json=authAddr,proto3" json:"auth_addr,omitempty"`
	AuthKey                string `protobuf:"bytes,4,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"`
	ClientId               string `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	AuthSecret             string `protobuf:"bytes,6,opt,name=auth_secret,json=authSecret,proto3" json:"auth_secret,omitempty"`
	CloudAddr              string `protobuf:"bytes,7,opt,name=cloud_addr,json=cloudAddr,proto3" json:"cloud_addr,omitempty"`
	CloudSecret            string `protobuf:"bytes,8,opt,name=cloud_secret,json=cloudSecret,proto3" json:"cloud_secret,omitempty"`
	FileStoreAddr          string `protobuf:"bytes,9,opt,name=file_store_addr,json=fileStoreAddr,proto3" json:"file_store_addr,omitempty"`
	FileStoreSecret        string `protobuf:"bytes,10,opt,name=file_store_secret,json=fileStoreSecret,proto3" json:"file_store_secret,omitempty"`
	TaskId                 string `protobuf:"bytes,11,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	FileIntelInspectOnline bool   `protobuf:"varint,12,opt,name=file_intel_inspect_online,json=fileIntelInspectOnline,proto3" json:"file_intel_inspect_online,omitempty"`
}

func (x *NetProtectCloudConfig) Reset() {
	*x = NetProtectCloudConfig{}
	mi := &file_nasus_config_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetProtectCloudConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetProtectCloudConfig) ProtoMessage() {}

func (x *NetProtectCloudConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetProtectCloudConfig.ProtoReflect.Descriptor instead.
func (*NetProtectCloudConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{3}
}

func (x *NetProtectCloudConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *NetProtectCloudConfig) GetConnectModel() int32 {
	if x != nil {
		return x.ConnectModel
	}
	return 0
}

func (x *NetProtectCloudConfig) GetAuthAddr() string {
	if x != nil {
		return x.AuthAddr
	}
	return ""
}

func (x *NetProtectCloudConfig) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *NetProtectCloudConfig) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *NetProtectCloudConfig) GetAuthSecret() string {
	if x != nil {
		return x.AuthSecret
	}
	return ""
}

func (x *NetProtectCloudConfig) GetCloudAddr() string {
	if x != nil {
		return x.CloudAddr
	}
	return ""
}

func (x *NetProtectCloudConfig) GetCloudSecret() string {
	if x != nil {
		return x.CloudSecret
	}
	return ""
}

func (x *NetProtectCloudConfig) GetFileStoreAddr() string {
	if x != nil {
		return x.FileStoreAddr
	}
	return ""
}

func (x *NetProtectCloudConfig) GetFileStoreSecret() string {
	if x != nil {
		return x.FileStoreSecret
	}
	return ""
}

func (x *NetProtectCloudConfig) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *NetProtectCloudConfig) GetFileIntelInspectOnline() bool {
	if x != nil {
		return x.FileIntelInspectOnline
	}
	return false
}

type SandboxConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addr       string `protobuf:"bytes,1,opt,name=addr,proto3" json:"addr,omitempty"`
	ApiKey     string `protobuf:"bytes,2,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	ApiSecret  string `protobuf:"bytes,3,opt,name=api_secret,json=apiSecret,proto3" json:"api_secret,omitempty"`
	Uuid       string `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid,omitempty"`
	FileEnable bool   `protobuf:"varint,5,opt,name=file_enable,json=fileEnable,proto3" json:"file_enable,omitempty"`
}

func (x *SandboxConfig) Reset() {
	*x = SandboxConfig{}
	mi := &file_nasus_config_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxConfig) ProtoMessage() {}

func (x *SandboxConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxConfig.ProtoReflect.Descriptor instead.
func (*SandboxConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{4}
}

func (x *SandboxConfig) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *SandboxConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *SandboxConfig) GetApiSecret() string {
	if x != nil {
		return x.ApiSecret
	}
	return ""
}

func (x *SandboxConfig) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *SandboxConfig) GetFileEnable() bool {
	if x != nil {
		return x.FileEnable
	}
	return false
}

type NTPConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode int32  `protobuf:"varint,1,opt,name=mode,proto3" json:"mode,omitempty"`
	Addr string `protobuf:"bytes,2,opt,name=addr,proto3" json:"addr,omitempty"`
}

func (x *NTPConfig) Reset() {
	*x = NTPConfig{}
	mi := &file_nasus_config_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NTPConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NTPConfig) ProtoMessage() {}

func (x *NTPConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NTPConfig.ProtoReflect.Descriptor instead.
func (*NTPConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{5}
}

func (x *NTPConfig) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *NTPConfig) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

type AccessConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool            `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	GangedSystem []*GangedSystem `protobuf:"bytes,2,rep,name=ganged_system,json=gangedSystem,proto3" json:"ganged_system,omitempty"`
}

func (x *AccessConfig) Reset() {
	*x = AccessConfig{}
	mi := &file_nasus_config_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccessConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccessConfig) ProtoMessage() {}

func (x *AccessConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccessConfig.ProtoReflect.Descriptor instead.
func (*AccessConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{6}
}

func (x *AccessConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AccessConfig) GetGangedSystem() []*GangedSystem {
	if x != nil {
		return x.GangedSystem
	}
	return nil
}

type GangedSystem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ApiKey    string `protobuf:"bytes,2,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	ApiSecret string `protobuf:"bytes,3,opt,name=api_secret,json=apiSecret,proto3" json:"api_secret,omitempty"`
}

func (x *GangedSystem) Reset() {
	*x = GangedSystem{}
	mi := &file_nasus_config_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GangedSystem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GangedSystem) ProtoMessage() {}

func (x *GangedSystem) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GangedSystem.ProtoReflect.Descriptor instead.
func (*GangedSystem) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{7}
}

func (x *GangedSystem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GangedSystem) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *GangedSystem) GetApiSecret() string {
	if x != nil {
		return x.ApiSecret
	}
	return ""
}

type CloudV01Config struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	ConnectModel int32  `protobuf:"varint,2,opt,name=connect_model,json=connectModel,proto3" json:"connect_model,omitempty"`
	Addr         string `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr,omitempty"`
	ApiKey       string `protobuf:"bytes,4,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
}

func (x *CloudV01Config) Reset() {
	*x = CloudV01Config{}
	mi := &file_nasus_config_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloudV01Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudV01Config) ProtoMessage() {}

func (x *CloudV01Config) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudV01Config.ProtoReflect.Descriptor instead.
func (*CloudV01Config) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{8}
}

func (x *CloudV01Config) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *CloudV01Config) GetConnectModel() int32 {
	if x != nil {
		return x.ConnectModel
	}
	return 0
}

func (x *CloudV01Config) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *CloudV01Config) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type CloudModelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool   `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	ConnectModel int32  `protobuf:"varint,2,opt,name=connect_model,json=connectModel,proto3" json:"connect_model,omitempty"`
	Addr         string `protobuf:"bytes,3,opt,name=addr,proto3" json:"addr,omitempty"`
	ApiKey       string `protobuf:"bytes,4,opt,name=api_key,json=apiKey,proto3" json:"api_key,omitempty"`
	AuthKey      string `protobuf:"bytes,5,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"`
}

func (x *CloudModelConfig) Reset() {
	*x = CloudModelConfig{}
	mi := &file_nasus_config_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CloudModelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CloudModelConfig) ProtoMessage() {}

func (x *CloudModelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CloudModelConfig.ProtoReflect.Descriptor instead.
func (*CloudModelConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{9}
}

func (x *CloudModelConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *CloudModelConfig) GetConnectModel() int32 {
	if x != nil {
		return x.ConnectModel
	}
	return 0
}

func (x *CloudModelConfig) GetAddr() string {
	if x != nil {
		return x.Addr
	}
	return ""
}

func (x *CloudModelConfig) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *CloudModelConfig) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

type EngineLibConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable   bool  `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	Interval int32 `protobuf:"varint,2,opt,name=interval,proto3" json:"interval,omitempty"`
}

func (x *EngineLibConfig) Reset() {
	*x = EngineLibConfig{}
	mi := &file_nasus_config_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EngineLibConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineLibConfig) ProtoMessage() {}

func (x *EngineLibConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineLibConfig.ProtoReflect.Descriptor instead.
func (*EngineLibConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{10}
}

func (x *EngineLibConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *EngineLibConfig) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

type CommonConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicSecurity *BasicSecurityConfig `protobuf:"bytes,1,opt,name=basic_security,json=basicSecurity,proto3" json:"basic_security,omitempty"`
}

func (x *CommonConfig) Reset() {
	*x = CommonConfig{}
	mi := &file_nasus_config_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonConfig) ProtoMessage() {}

func (x *CommonConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonConfig.ProtoReflect.Descriptor instead.
func (*CommonConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{11}
}

func (x *CommonConfig) GetBasicSecurity() *BasicSecurityConfig {
	if x != nil {
		return x.BasicSecurity
	}
	return nil
}

type BasicSecurityConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agent  *AgentConfig  `protobuf:"bytes,1,opt,name=agent,proto3" json:"agent,omitempty"`
	Server *ServerConfig `protobuf:"bytes,2,opt,name=server,proto3" json:"server,omitempty"`
}

func (x *BasicSecurityConfig) Reset() {
	*x = BasicSecurityConfig{}
	mi := &file_nasus_config_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BasicSecurityConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BasicSecurityConfig) ProtoMessage() {}

func (x *BasicSecurityConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BasicSecurityConfig.ProtoReflect.Descriptor instead.
func (*BasicSecurityConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{12}
}

func (x *BasicSecurityConfig) GetAgent() *AgentConfig {
	if x != nil {
		return x.Agent
	}
	return nil
}

func (x *BasicSecurityConfig) GetServer() *ServerConfig {
	if x != nil {
		return x.Server
	}
	return nil
}

type AgentConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *AgentConfig) Reset() {
	*x = AgentConfig{}
	mi := &file_nasus_config_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentConfig) ProtoMessage() {}

func (x *AgentConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentConfig.ProtoReflect.Descriptor instead.
func (*AgentConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{13}
}

func (x *AgentConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ServerConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginLock     bool  `protobuf:"varint,1,opt,name=login_lock,json=loginLock,proto3" json:"login_lock,omitempty"`
	LogoutTimeout int32 `protobuf:"varint,2,opt,name=logout_timeout,json=logoutTimeout,proto3" json:"logout_timeout,omitempty"`
}

func (x *ServerConfig) Reset() {
	*x = ServerConfig{}
	mi := &file_nasus_config_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerConfig) ProtoMessage() {}

func (x *ServerConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerConfig.ProtoReflect.Descriptor instead.
func (*ServerConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{14}
}

func (x *ServerConfig) GetLoginLock() bool {
	if x != nil {
		return x.LoginLock
	}
	return false
}

func (x *ServerConfig) GetLogoutTimeout() int32 {
	if x != nil {
		return x.LogoutTimeout
	}
	return 0
}

type FileThreatConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetectEngine *DetectEngineConfig `protobuf:"bytes,1,opt,name=detect_engine,json=detectEngine,proto3" json:"detect_engine,omitempty"`
}

func (x *FileThreatConfig) Reset() {
	*x = FileThreatConfig{}
	mi := &file_nasus_config_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileThreatConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileThreatConfig) ProtoMessage() {}

func (x *FileThreatConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileThreatConfig.ProtoReflect.Descriptor instead.
func (*FileThreatConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{15}
}

func (x *FileThreatConfig) GetDetectEngine() *DetectEngineConfig {
	if x != nil {
		return x.DetectEngine
	}
	return nil
}

type DetectEngineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AiEngine         *AIEngineConfig         `protobuf:"bytes,1,opt,name=ai_engine,json=aiEngine,proto3" json:"ai_engine,omitempty"`
	LocalVirusEngine *LocalVirusEngineConfig `protobuf:"bytes,2,opt,name=local_virus_engine,json=localVirusEngine,proto3" json:"local_virus_engine,omitempty"`
	SandboxEngine    *SandboxEngineConfig    `protobuf:"bytes,3,opt,name=sandbox_engine,json=sandboxEngine,proto3" json:"sandbox_engine,omitempty"`
}

func (x *DetectEngineConfig) Reset() {
	*x = DetectEngineConfig{}
	mi := &file_nasus_config_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectEngineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectEngineConfig) ProtoMessage() {}

func (x *DetectEngineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectEngineConfig.ProtoReflect.Descriptor instead.
func (*DetectEngineConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{16}
}

func (x *DetectEngineConfig) GetAiEngine() *AIEngineConfig {
	if x != nil {
		return x.AiEngine
	}
	return nil
}

func (x *DetectEngineConfig) GetLocalVirusEngine() *LocalVirusEngineConfig {
	if x != nil {
		return x.LocalVirusEngine
	}
	return nil
}

func (x *DetectEngineConfig) GetSandboxEngine() *SandboxEngineConfig {
	if x != nil {
		return x.SandboxEngine
	}
	return nil
}

type AIEngineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DocEnable bool  `protobuf:"varint,1,opt,name=doc_enable,json=docEnable,proto3" json:"doc_enable,omitempty"`
	PeEnable  bool  `protobuf:"varint,2,opt,name=pe_enable,json=peEnable,proto3" json:"pe_enable,omitempty"`
	Sensitive int32 `protobuf:"varint,3,opt,name=sensitive,proto3" json:"sensitive,omitempty"`
}

func (x *AIEngineConfig) Reset() {
	*x = AIEngineConfig{}
	mi := &file_nasus_config_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AIEngineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AIEngineConfig) ProtoMessage() {}

func (x *AIEngineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AIEngineConfig.ProtoReflect.Descriptor instead.
func (*AIEngineConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{17}
}

func (x *AIEngineConfig) GetDocEnable() bool {
	if x != nil {
		return x.DocEnable
	}
	return false
}

func (x *AIEngineConfig) GetPeEnable() bool {
	if x != nil {
		return x.PeEnable
	}
	return false
}

func (x *AIEngineConfig) GetSensitive() int32 {
	if x != nil {
		return x.Sensitive
	}
	return 0
}

type LocalVirusEngineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
}

func (x *LocalVirusEngineConfig) Reset() {
	*x = LocalVirusEngineConfig{}
	mi := &file_nasus_config_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalVirusEngineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalVirusEngineConfig) ProtoMessage() {}

func (x *LocalVirusEngineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalVirusEngineConfig.ProtoReflect.Descriptor instead.
func (*LocalVirusEngineConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{18}
}

func (x *LocalVirusEngineConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type SandboxEngineConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable    bool  `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	Sensitive int32 `protobuf:"varint,2,opt,name=sensitive,proto3" json:"sensitive,omitempty"`
}

func (x *SandboxEngineConfig) Reset() {
	*x = SandboxEngineConfig{}
	mi := &file_nasus_config_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SandboxEngineConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SandboxEngineConfig) ProtoMessage() {}

func (x *SandboxEngineConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SandboxEngineConfig.ProtoReflect.Descriptor instead.
func (*SandboxEngineConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{19}
}

func (x *SandboxEngineConfig) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SandboxEngineConfig) GetSensitive() int32 {
	if x != nil {
		return x.Sensitive
	}
	return 0
}

type LogArchiveConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Storage *StorageConfig `protobuf:"bytes,1,opt,name=storage,proto3" json:"storage,omitempty"`
}

func (x *LogArchiveConfig) Reset() {
	*x = LogArchiveConfig{}
	mi := &file_nasus_config_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogArchiveConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogArchiveConfig) ProtoMessage() {}

func (x *LogArchiveConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogArchiveConfig.ProtoReflect.Descriptor instead.
func (*LogArchiveConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{20}
}

func (x *LogArchiveConfig) GetStorage() *StorageConfig {
	if x != nil {
		return x.Storage
	}
	return nil
}

type StorageConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day int32 `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
}

func (x *StorageConfig) Reset() {
	*x = StorageConfig{}
	mi := &file_nasus_config_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorageConfig) ProtoMessage() {}

func (x *StorageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_config_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorageConfig.ProtoReflect.Descriptor instead.
func (*StorageConfig) Descriptor() ([]byte, []int) {
	return file_nasus_config_proto_rawDescGZIP(), []int{21}
}

func (x *StorageConfig) GetDay() int32 {
	if x != nil {
		return x.Day
	}
	return 0
}

var File_nasus_config_proto protoreflect.FileDescriptor

var file_nasus_config_proto_rawDesc = []byte{
	0x0a, 0x12, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x22, 0xad, 0x01, 0x0a, 0x0a,
	0x42, 0x75, 0x73, 0x69, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x06, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x74, 0x12, 0x38, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x5f, 0x61, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c,
	0x6f, 0x67, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0a, 0x6c, 0x6f, 0x67, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x0c,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2b, 0x0a, 0x06,
	0x67, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x06, 0x67, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x12, 0x35, 0x0a, 0x0a, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62,
	0x12, 0x2b, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xc7, 0x02,
	0x0a, 0x0c, 0x47, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x48,
	0x0a, 0x11, 0x6e, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x4e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x12, 0x2e, 0x0a, 0x07, 0x73, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x07, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x12, 0x22, 0x0a, 0x03, 0x6e, 0x74, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4e, 0x54,
	0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x03, 0x6e, 0x74, 0x70, 0x12, 0x2b, 0x0a, 0x06,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x06, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x5f, 0x76, 0x30, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x56, 0x30, 0x31, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x08, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x56, 0x30, 0x31, 0x12, 0x38, 0x0a,
	0x0b, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x22, 0xb4, 0x03, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x61, 0x75, 0x74, 0x68, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x75, 0x74, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2a,
	0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x6c, 0x5f, 0x69, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x5f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65,
	0x6c, 0x49, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x22, 0x90,
	0x01, 0x0a, 0x0d, 0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x61, 0x64, 0x64, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x70, 0x69, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x70, 0x69, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x22, 0x33, 0x0a, 0x09, 0x4e, 0x54, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x22, 0x60, 0x0a, 0x0c, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x38,
	0x0a, 0x0d, 0x67, 0x61, 0x6e, 0x67, 0x65, 0x64, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x61,
	0x6e, 0x67, 0x65, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x67, 0x61, 0x6e, 0x67,
	0x65, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x22, 0x5a, 0x0a, 0x0c, 0x47, 0x61, 0x6e, 0x67,
	0x65, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x70, 0x69, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x69, 0x5f, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x69, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x22, 0x7a, 0x0a, 0x0e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x56, 0x30, 0x31,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79,
	0x22, 0x97, 0x01, 0x0a, 0x10, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x22, 0x45, 0x0a, 0x0f, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61,
	0x6c, 0x22, 0x51, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x41, 0x0a, 0x0e, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x62, 0x61, 0x73, 0x69, 0x63, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x22, 0x6c, 0x0a, 0x13, 0x42, 0x61, 0x73, 0x69, 0x63, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x28, 0x0a, 0x05, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x22, 0x25, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x54, 0x0a, 0x0c, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x6c,
	0x6f, 0x67, 0x69, 0x6e, 0x4c, 0x6f, 0x63, 0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x6f, 0x67, 0x6f,
	0x75, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x6f, 0x75, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22,
	0x52, 0x0a, 0x10, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x3e, 0x0a, 0x0d, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0c, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x22, 0xd8, 0x01, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x32, 0x0a, 0x09, 0x61, 0x69,
	0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x41, 0x49, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x08, 0x61, 0x69, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x4b,
	0x0a, 0x12, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x56, 0x69, 0x72, 0x75, 0x73, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x56, 0x69, 0x72, 0x75, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x73,
	0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x61, 0x6e, 0x64,
	0x62, 0x6f, 0x78, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0d, 0x73, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x22, 0x6a,
	0x0a, 0x0e, 0x41, 0x49, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x6f, 0x63, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x64, 0x6f, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x65, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x70, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x22, 0x30, 0x0a, 0x16, 0x4c, 0x6f,
	0x63, 0x61, 0x6c, 0x56, 0x69, 0x72, 0x75, 0x73, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x4b, 0x0a, 0x13,
	0x53, 0x61, 0x6e, 0x64, 0x62, 0x6f, 0x78, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x22, 0x42, 0x0a, 0x10, 0x4c, 0x6f, 0x67,
	0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x2e, 0x0a,
	0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x22, 0x21, 0x0a,
	0x0d, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x64, 0x61, 0x79,
	0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_nasus_config_proto_rawDescOnce sync.Once
	file_nasus_config_proto_rawDescData = file_nasus_config_proto_rawDesc
)

func file_nasus_config_proto_rawDescGZIP() []byte {
	file_nasus_config_proto_rawDescOnce.Do(func() {
		file_nasus_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_nasus_config_proto_rawDescData)
	})
	return file_nasus_config_proto_rawDescData
}

var file_nasus_config_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_nasus_config_proto_goTypes = []any{
	(*BusiConfig)(nil),             // 0: nasus.BusiConfig
	(*SystemConfig)(nil),           // 1: nasus.SystemConfig
	(*GangedConfig)(nil),           // 2: nasus.GangedConfig
	(*NetProtectCloudConfig)(nil),  // 3: nasus.NetProtectCloudConfig
	(*SandboxConfig)(nil),          // 4: nasus.SandboxConfig
	(*NTPConfig)(nil),              // 5: nasus.NTPConfig
	(*AccessConfig)(nil),           // 6: nasus.AccessConfig
	(*GangedSystem)(nil),           // 7: nasus.GangedSystem
	(*CloudV01Config)(nil),         // 8: nasus.CloudV01Config
	(*CloudModelConfig)(nil),       // 9: nasus.CloudModelConfig
	(*EngineLibConfig)(nil),        // 10: nasus.EngineLibConfig
	(*CommonConfig)(nil),           // 11: nasus.CommonConfig
	(*BasicSecurityConfig)(nil),    // 12: nasus.BasicSecurityConfig
	(*AgentConfig)(nil),            // 13: nasus.AgentConfig
	(*ServerConfig)(nil),           // 14: nasus.ServerConfig
	(*FileThreatConfig)(nil),       // 15: nasus.FileThreatConfig
	(*DetectEngineConfig)(nil),     // 16: nasus.DetectEngineConfig
	(*AIEngineConfig)(nil),         // 17: nasus.AIEngineConfig
	(*LocalVirusEngineConfig)(nil), // 18: nasus.LocalVirusEngineConfig
	(*SandboxEngineConfig)(nil),    // 19: nasus.SandboxEngineConfig
	(*LogArchiveConfig)(nil),       // 20: nasus.LogArchiveConfig
	(*StorageConfig)(nil),          // 21: nasus.StorageConfig
}
var file_nasus_config_proto_depIdxs = []int32{
	1,  // 0: nasus.BusiConfig.system:type_name -> nasus.SystemConfig
	15, // 1: nasus.BusiConfig.file_threat:type_name -> nasus.FileThreatConfig
	20, // 2: nasus.BusiConfig.log_archive:type_name -> nasus.LogArchiveConfig
	2,  // 3: nasus.SystemConfig.ganged:type_name -> nasus.GangedConfig
	10, // 4: nasus.SystemConfig.engine_lib:type_name -> nasus.EngineLibConfig
	11, // 5: nasus.SystemConfig.common:type_name -> nasus.CommonConfig
	3,  // 6: nasus.GangedConfig.net_protect_cloud:type_name -> nasus.NetProtectCloudConfig
	4,  // 7: nasus.GangedConfig.sandbox:type_name -> nasus.SandboxConfig
	5,  // 8: nasus.GangedConfig.ntp:type_name -> nasus.NTPConfig
	6,  // 9: nasus.GangedConfig.access:type_name -> nasus.AccessConfig
	8,  // 10: nasus.GangedConfig.cloud_v01:type_name -> nasus.CloudV01Config
	9,  // 11: nasus.GangedConfig.cloud_model:type_name -> nasus.CloudModelConfig
	7,  // 12: nasus.AccessConfig.ganged_system:type_name -> nasus.GangedSystem
	12, // 13: nasus.CommonConfig.basic_security:type_name -> nasus.BasicSecurityConfig
	13, // 14: nasus.BasicSecurityConfig.agent:type_name -> nasus.AgentConfig
	14, // 15: nasus.BasicSecurityConfig.server:type_name -> nasus.ServerConfig
	16, // 16: nasus.FileThreatConfig.detect_engine:type_name -> nasus.DetectEngineConfig
	17, // 17: nasus.DetectEngineConfig.ai_engine:type_name -> nasus.AIEngineConfig
	18, // 18: nasus.DetectEngineConfig.local_virus_engine:type_name -> nasus.LocalVirusEngineConfig
	19, // 19: nasus.DetectEngineConfig.sandbox_engine:type_name -> nasus.SandboxEngineConfig
	21, // 20: nasus.LogArchiveConfig.storage:type_name -> nasus.StorageConfig
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_nasus_config_proto_init() }
func file_nasus_config_proto_init() {
	if File_nasus_config_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nasus_config_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_nasus_config_proto_goTypes,
		DependencyIndexes: file_nasus_config_proto_depIdxs,
		MessageInfos:      file_nasus_config_proto_msgTypes,
	}.Build()
	File_nasus_config_proto = out.File
	file_nasus_config_proto_rawDesc = nil
	file_nasus_config_proto_goTypes = nil
	file_nasus_config_proto_depIdxs = nil
}
