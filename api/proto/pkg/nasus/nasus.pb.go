// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: nasus/nasus.proto

package nasus

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetConfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetConfReq) Reset() {
	*x = GetConfReq{}
	mi := &file_nasus_nasus_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfReq) ProtoMessage() {}

func (x *GetConfReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfReq.ProtoReflect.Descriptor instead.
func (*GetConfReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{0}
}

type GetConfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *GetConfResp) Reset() {
	*x = GetConfResp{}
	mi := &file_nasus_nasus_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfResp) ProtoMessage() {}

func (x *GetConfResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfResp.ProtoReflect.Descriptor instead.
func (*GetConfResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{1}
}

func (x *GetConfResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateConfReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *UpdateConfReq) Reset() {
	*x = UpdateConfReq{}
	mi := &file_nasus_nasus_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfReq) ProtoMessage() {}

func (x *UpdateConfReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfReq.ProtoReflect.Descriptor instead.
func (*UpdateConfReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateConfReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateConfResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateConfResp) Reset() {
	*x = UpdateConfResp{}
	mi := &file_nasus_nasus_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfResp) ProtoMessage() {}

func (x *UpdateConfResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfResp.ProtoReflect.Descriptor instead.
func (*UpdateConfResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{3}
}

type AddCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
	// map<string, google.protobuf.Any> data = 3; // 文件内容
	Data string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *AddCMReq) Reset() {
	*x = AddCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCMReq) ProtoMessage() {}

func (x *AddCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCMReq.ProtoReflect.Descriptor instead.
func (*AddCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{4}
}

func (x *AddCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *AddCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *AddCMReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type AddCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddCMResp) Reset() {
	*x = AddCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCMResp) ProtoMessage() {}

func (x *AddCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCMResp.ProtoReflect.Descriptor instead.
func (*AddCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{5}
}

type DelCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
	Data      string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                            // 文件内容
}

func (x *DelCMReq) Reset() {
	*x = DelCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelCMReq) ProtoMessage() {}

func (x *DelCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelCMReq.ProtoReflect.Descriptor instead.
func (*DelCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{6}
}

func (x *DelCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *DelCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *DelCMReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type DelCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelCMResp) Reset() {
	*x = DelCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelCMResp) ProtoMessage() {}

func (x *DelCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelCMResp.ProtoReflect.Descriptor instead.
func (*DelCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{7}
}

type UpdateCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
	Data      string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                            // 文件内容
}

func (x *UpdateCMReq) Reset() {
	*x = UpdateCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCMReq) ProtoMessage() {}

func (x *UpdateCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCMReq.ProtoReflect.Descriptor instead.
func (*UpdateCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *UpdateCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UpdateCMReq) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdateCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateCMResp) Reset() {
	*x = UpdateCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCMResp) ProtoMessage() {}

func (x *UpdateCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCMResp.ProtoReflect.Descriptor instead.
func (*UpdateCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{9}
}

type QueryCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
}

func (x *QueryCMReq) Reset() {
	*x = QueryCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCMReq) ProtoMessage() {}

func (x *QueryCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCMReq.ProtoReflect.Descriptor instead.
func (*QueryCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{10}
}

func (x *QueryCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *QueryCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type QueryCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *QueryCMResp) Reset() {
	*x = QueryCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryCMResp) ProtoMessage() {}

func (x *QueryCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryCMResp.ProtoReflect.Descriptor instead.
func (*QueryCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{11}
}

func (x *QueryCMResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type ListCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
	Limit     int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                         // 分页拉取条数
	Continue  string `protobuf:"bytes,4,opt,name=continue,proto3" json:"continue,omitempty"`                    // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

func (x *ListCMReq) Reset() {
	*x = ListCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCMReq) ProtoMessage() {}

func (x *ListCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCMReq.ProtoReflect.Descriptor instead.
func (*ListCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{12}
}

func (x *ListCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *ListCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ListCMReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListCMReq) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type ListCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*CMItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Continue string    `protobuf:"bytes,2,opt,name=continue,proto3" json:"continue,omitempty"` // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

func (x *ListCMResp) Reset() {
	*x = ListCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCMResp) ProtoMessage() {}

func (x *ListCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCMResp.ProtoReflect.Descriptor instead.
func (*ListCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{13}
}

func (x *ListCMResp) GetData() []*CMItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListCMResp) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type CMItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
	Data      string `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`                            // 文件内容
}

func (x *CMItem) Reset() {
	*x = CMItem{}
	mi := &file_nasus_nasus_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CMItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CMItem) ProtoMessage() {}

func (x *CMItem) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CMItem.ProtoReflect.Descriptor instead.
func (*CMItem) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{14}
}

func (x *CMItem) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *CMItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *CMItem) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type WatchCMReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	FileName  string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`    // 文件名
}

func (x *WatchCMReq) Reset() {
	*x = WatchCMReq{}
	mi := &file_nasus_nasus_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchCMReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchCMReq) ProtoMessage() {}

func (x *WatchCMReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchCMReq.ProtoReflect.Descriptor instead.
func (*WatchCMReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{15}
}

func (x *WatchCMReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *WatchCMReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type WatchCMResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string            `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`                                                              // 命名空间
	FileName  string            `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                                                                 // 文件名
	Data      map[string]string `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 文件内容
}

func (x *WatchCMResp) Reset() {
	*x = WatchCMResp{}
	mi := &file_nasus_nasus_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchCMResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchCMResp) ProtoMessage() {}

func (x *WatchCMResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchCMResp.ProtoReflect.Descriptor instead.
func (*WatchCMResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{16}
}

func (x *WatchCMResp) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *WatchCMResp) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *WatchCMResp) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListNodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace     string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`             // 命名空间
	LabelSelector string `protobuf:"bytes,2,opt,name=label_selector,json=labelSelector,proto3" json:"label_selector,omitempty"` // 标签
	Limit         int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`                                     // 分页拉取条数
	Continue      string `protobuf:"bytes,4,opt,name=continue,proto3" json:"continue,omitempty"`                                // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

func (x *ListNodeReq) Reset() {
	*x = ListNodeReq{}
	mi := &file_nasus_nasus_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeReq) ProtoMessage() {}

func (x *ListNodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeReq.ProtoReflect.Descriptor instead.
func (*ListNodeReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{17}
}

func (x *ListNodeReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *ListNodeReq) GetLabelSelector() string {
	if x != nil {
		return x.LabelSelector
	}
	return ""
}

func (x *ListNodeReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListNodeReq) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type ListNodeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*NodeItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Continue string      `protobuf:"bytes,2,opt,name=continue,proto3" json:"continue,omitempty"` // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

func (x *ListNodeResp) Reset() {
	*x = ListNodeResp{}
	mi := &file_nasus_nasus_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNodeResp) ProtoMessage() {}

func (x *ListNodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNodeResp.ProtoReflect.Descriptor instead.
func (*ListNodeResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{18}
}

func (x *ListNodeResp) GetData() []*NodeItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListNodeResp) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type NodeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // 节点名
	Ip            string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                            // 节点ip
	Hostname      string `protobuf:"bytes,3,opt,name=hostname,proto3" json:"hostname,omitempty"`                                // 节点主机名
	Os            string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`                                            // 操作系统
	OsVersion     string `protobuf:"bytes,5,opt,name=os_version,json=osVersion,proto3" json:"os_version,omitempty"`             // 操作系统版本
	KernelVersion string `protobuf:"bytes,6,opt,name=kernel_version,json=kernelVersion,proto3" json:"kernel_version,omitempty"` // 内核版本
	KernelRelease string `protobuf:"bytes,7,opt,name=kernel_release,json=kernelRelease,proto3" json:"kernel_release,omitempty"` // 内核发行版
	KernelArch    string `protobuf:"bytes,8,opt,name=kernel_arch,json=kernelArch,proto3" json:"kernel_arch,omitempty"`          // 内核架构
}

func (x *NodeItem) Reset() {
	*x = NodeItem{}
	mi := &file_nasus_nasus_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeItem) ProtoMessage() {}

func (x *NodeItem) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeItem.ProtoReflect.Descriptor instead.
func (*NodeItem) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{19}
}

func (x *NodeItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NodeItem) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *NodeItem) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *NodeItem) GetOsVersion() string {
	if x != nil {
		return x.OsVersion
	}
	return ""
}

func (x *NodeItem) GetKernelVersion() string {
	if x != nil {
		return x.KernelVersion
	}
	return ""
}

func (x *NodeItem) GetKernelRelease() string {
	if x != nil {
		return x.KernelRelease
	}
	return ""
}

func (x *NodeItem) GetKernelArch() string {
	if x != nil {
		return x.KernelArch
	}
	return ""
}

type WatchNodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WatchNodeReq) Reset() {
	*x = WatchNodeReq{}
	mi := &file_nasus_nasus_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchNodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchNodeReq) ProtoMessage() {}

func (x *WatchNodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchNodeReq.ProtoReflect.Descriptor instead.
func (*WatchNodeReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{20}
}

type WatchNodeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*NodeItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *WatchNodeResp) Reset() {
	*x = WatchNodeResp{}
	mi := &file_nasus_nasus_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchNodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchNodeResp) ProtoMessage() {}

func (x *WatchNodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchNodeResp.ProtoReflect.Descriptor instead.
func (*WatchNodeResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{21}
}

func (x *WatchNodeResp) GetData() []*NodeItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetEndpointsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace   string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`       // 命名空间
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"` // 服务名
}

func (x *GetEndpointsReq) Reset() {
	*x = GetEndpointsReq{}
	mi := &file_nasus_nasus_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEndpointsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEndpointsReq) ProtoMessage() {}

func (x *GetEndpointsReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEndpointsReq.ProtoReflect.Descriptor instead.
func (*GetEndpointsReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{22}
}

func (x *GetEndpointsReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *GetEndpointsReq) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type GetEndpointsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PodIps []string `protobuf:"bytes,1,rep,name=pod_ips,json=podIps,proto3" json:"pod_ips,omitempty"` // endpoints pod ip 列表
}

func (x *GetEndpointsResp) Reset() {
	*x = GetEndpointsResp{}
	mi := &file_nasus_nasus_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEndpointsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEndpointsResp) ProtoMessage() {}

func (x *GetEndpointsResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEndpointsResp.ProtoReflect.Descriptor instead.
func (*GetEndpointsResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{23}
}

func (x *GetEndpointsResp) GetPodIps() []string {
	if x != nil {
		return x.PodIps
	}
	return nil
}

type ListEndpointsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace   string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`       // 命名空间
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"` // 服务名
	Limit       int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Continue    string `protobuf:"bytes,4,opt,name=continue,proto3" json:"continue,omitempty"`
}

func (x *ListEndpointsReq) Reset() {
	*x = ListEndpointsReq{}
	mi := &file_nasus_nasus_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEndpointsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEndpointsReq) ProtoMessage() {}

func (x *ListEndpointsReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEndpointsReq.ProtoReflect.Descriptor instead.
func (*ListEndpointsReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{24}
}

func (x *ListEndpointsReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *ListEndpointsReq) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ListEndpointsReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListEndpointsReq) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type ListEndpointsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PodIps   []string `protobuf:"bytes,1,rep,name=pod_ips,json=podIps,proto3" json:"pod_ips,omitempty"` // endpoints pod ip 列表
	Continue string   `protobuf:"bytes,2,opt,name=continue,proto3" json:"continue,omitempty"`
}

func (x *ListEndpointsResp) Reset() {
	*x = ListEndpointsResp{}
	mi := &file_nasus_nasus_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEndpointsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEndpointsResp) ProtoMessage() {}

func (x *ListEndpointsResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEndpointsResp.ProtoReflect.Descriptor instead.
func (*ListEndpointsResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{25}
}

func (x *ListEndpointsResp) GetPodIps() []string {
	if x != nil {
		return x.PodIps
	}
	return nil
}

func (x *ListEndpointsResp) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type WatchEndpointsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace   string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`       // 命名空间
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"` // 服务名
}

func (x *WatchEndpointsReq) Reset() {
	*x = WatchEndpointsReq{}
	mi := &file_nasus_nasus_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchEndpointsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchEndpointsReq) ProtoMessage() {}

func (x *WatchEndpointsReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchEndpointsReq.ProtoReflect.Descriptor instead.
func (*WatchEndpointsReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{26}
}

func (x *WatchEndpointsReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *WatchEndpointsReq) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type WatchEndpointsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PodIps []string `protobuf:"bytes,1,rep,name=pod_ips,json=podIps,proto3" json:"pod_ips,omitempty"` // endpoints pod ip 列表
}

func (x *WatchEndpointsResp) Reset() {
	*x = WatchEndpointsResp{}
	mi := &file_nasus_nasus_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchEndpointsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchEndpointsResp) ProtoMessage() {}

func (x *WatchEndpointsResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchEndpointsResp.ProtoReflect.Descriptor instead.
func (*WatchEndpointsResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{27}
}

func (x *WatchEndpointsResp) GetPodIps() []string {
	if x != nil {
		return x.PodIps
	}
	return nil
}

type PodItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace  string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`    // 命名空间
	PodName    string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`          // pod名
	Ip         string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`                                   // pod ip
	HostIp     string `protobuf:"bytes,4,opt,name=host_ip,json=hostIp,proto3" json:"host_ip,omitempty"`             // pod host ip
	Status     string `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                           // pod状态
	CreateTime string `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"` // pod创建时间
	UpdateTime string `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"` // pod更新时间
	NodeName   string `protobuf:"bytes,8,opt,name=node_name,json=nodeName,proto3" json:"node_name,omitempty"`       // pod所在节点名
}

func (x *PodItem) Reset() {
	*x = PodItem{}
	mi := &file_nasus_nasus_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PodItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodItem) ProtoMessage() {}

func (x *PodItem) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodItem.ProtoReflect.Descriptor instead.
func (*PodItem) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{28}
}

func (x *PodItem) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *PodItem) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *PodItem) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *PodItem) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *PodItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PodItem) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *PodItem) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *PodItem) GetNodeName() string {
	if x != nil {
		return x.NodeName
	}
	return ""
}

type GetPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	PodName   string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`       // pod名
}

func (x *GetPodReq) Reset() {
	*x = GetPodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodReq) ProtoMessage() {}

func (x *GetPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodReq.ProtoReflect.Descriptor instead.
func (*GetPodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{29}
}

func (x *GetPodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *GetPodReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

type GetPodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data *PodItem `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GetPodResp) Reset() {
	*x = GetPodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodResp) ProtoMessage() {}

func (x *GetPodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodResp.ProtoReflect.Descriptor instead.
func (*GetPodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{30}
}

func (x *GetPodResp) GetData() *PodItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace     string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`
	LabelSelector string `protobuf:"bytes,2,opt,name=label_selector,json=labelSelector,proto3" json:"label_selector,omitempty"`
	Limit         int64  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Continue      string `protobuf:"bytes,4,opt,name=continue,proto3" json:"continue,omitempty"`
}

func (x *ListPodReq) Reset() {
	*x = ListPodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPodReq) ProtoMessage() {}

func (x *ListPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPodReq.ProtoReflect.Descriptor instead.
func (*ListPodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{31}
}

func (x *ListPodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *ListPodReq) GetLabelSelector() string {
	if x != nil {
		return x.LabelSelector
	}
	return ""
}

func (x *ListPodReq) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *ListPodReq) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type ListPodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data     []*PodItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	Continue string     `protobuf:"bytes,2,opt,name=continue,proto3" json:"continue,omitempty"` // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

func (x *ListPodResp) Reset() {
	*x = ListPodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPodResp) ProtoMessage() {}

func (x *ListPodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPodResp.ProtoReflect.Descriptor instead.
func (*ListPodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{32}
}

func (x *ListPodResp) GetData() []*PodItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ListPodResp) GetContinue() string {
	if x != nil {
		return x.Continue
	}
	return ""
}

type DelPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"` // 命名空间
	PodName   string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`       // pod名
}

func (x *DelPodReq) Reset() {
	*x = DelPodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPodReq) ProtoMessage() {}

func (x *DelPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPodReq.ProtoReflect.Descriptor instead.
func (*DelPodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{33}
}

func (x *DelPodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *DelPodReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

type DelPodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DelPodResp) Reset() {
	*x = DelPodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelPodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelPodResp) ProtoMessage() {}

func (x *DelPodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelPodResp.ProtoReflect.Descriptor instead.
func (*DelPodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{34}
}

type GetPodLogsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace     string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`             // 命名空间
	PodName       string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`                   // pod名
	ContainerName string `protobuf:"bytes,3,opt,name=container_name,json=containerName,proto3" json:"container_name,omitempty"` // 容器名
	TailLines     int64  `protobuf:"varint,4,opt,name=tail_lines,json=tailLines,proto3" json:"tail_lines,omitempty"`            // 读取日志条数
	SinceSeconds  string `protobuf:"bytes,5,opt,name=since_seconds,json=sinceSeconds,proto3" json:"since_seconds,omitempty"`    // 读取日志时间
}

func (x *GetPodLogsReq) Reset() {
	*x = GetPodLogsReq{}
	mi := &file_nasus_nasus_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodLogsReq) ProtoMessage() {}

func (x *GetPodLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodLogsReq.ProtoReflect.Descriptor instead.
func (*GetPodLogsReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{35}
}

func (x *GetPodLogsReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *GetPodLogsReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *GetPodLogsReq) GetContainerName() string {
	if x != nil {
		return x.ContainerName
	}
	return ""
}

func (x *GetPodLogsReq) GetTailLines() int64 {
	if x != nil {
		return x.TailLines
	}
	return 0
}

func (x *GetPodLogsReq) GetSinceSeconds() string {
	if x != nil {
		return x.SinceSeconds
	}
	return ""
}

type GetPodLogsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"` // 文件内容
}

func (x *GetPodLogsResp) Reset() {
	*x = GetPodLogsResp{}
	mi := &file_nasus_nasus_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodLogsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodLogsResp) ProtoMessage() {}

func (x *GetPodLogsResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodLogsResp.ProtoReflect.Descriptor instead.
func (*GetPodLogsResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{36}
}

func (x *GetPodLogsResp) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

type UpdatePodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace     string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`             // 命名空间
	PodName       string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`                   // pod名
	ContainerName string `protobuf:"bytes,3,opt,name=container_name,json=containerName,proto3" json:"container_name,omitempty"` // 容器名
	Image         string `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`                                      // 镜像
	RestartPolicy string `protobuf:"bytes,5,opt,name=restart_policy,json=restartPolicy,proto3" json:"restart_policy,omitempty"` // 重启策略
	Command       string `protobuf:"bytes,6,opt,name=command,proto3" json:"command,omitempty"`                                  // 命令
	Args          string `protobuf:"bytes,7,opt,name=args,proto3" json:"args,omitempty"`                                        // 参数
	Env           string `protobuf:"bytes,8,opt,name=env,proto3" json:"env,omitempty"`                                          //环境变量
}

func (x *UpdatePodReq) Reset() {
	*x = UpdatePodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePodReq) ProtoMessage() {}

func (x *UpdatePodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePodReq.ProtoReflect.Descriptor instead.
func (*UpdatePodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{37}
}

func (x *UpdatePodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *UpdatePodReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *UpdatePodReq) GetContainerName() string {
	if x != nil {
		return x.ContainerName
	}
	return ""
}

func (x *UpdatePodReq) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *UpdatePodReq) GetRestartPolicy() string {
	if x != nil {
		return x.RestartPolicy
	}
	return ""
}

func (x *UpdatePodReq) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *UpdatePodReq) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

func (x *UpdatePodReq) GetEnv() string {
	if x != nil {
		return x.Env
	}
	return ""
}

type UpdatePodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdatePodResp) Reset() {
	*x = UpdatePodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePodResp) ProtoMessage() {}

func (x *UpdatePodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePodResp.ProtoReflect.Descriptor instead.
func (*UpdatePodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{38}
}

type WatchPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`
	PodName   string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
}

func (x *WatchPodReq) Reset() {
	*x = WatchPodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchPodReq) ProtoMessage() {}

func (x *WatchPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchPodReq.ProtoReflect.Descriptor instead.
func (*WatchPodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{39}
}

func (x *WatchPodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *WatchPodReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

type WatchPodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WatchPodResp) Reset() {
	*x = WatchPodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WatchPodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WatchPodResp) ProtoMessage() {}

func (x *WatchPodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WatchPodResp.ProtoReflect.Descriptor instead.
func (*WatchPodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{40}
}

type ScalePodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`
	PodName   string `protobuf:"bytes,2,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	Replicas  int64  `protobuf:"varint,3,opt,name=replicas,proto3" json:"replicas,omitempty"`
}

func (x *ScalePodReq) Reset() {
	*x = ScalePodReq{}
	mi := &file_nasus_nasus_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScalePodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScalePodReq) ProtoMessage() {}

func (x *ScalePodReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScalePodReq.ProtoReflect.Descriptor instead.
func (*ScalePodReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{41}
}

func (x *ScalePodReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *ScalePodReq) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *ScalePodReq) GetReplicas() int64 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

type ScalePodResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldReplicas int64 `protobuf:"varint,1,opt,name=old_replicas,json=oldReplicas,proto3" json:"old_replicas,omitempty"` // 原始副本数
}

func (x *ScalePodResp) Reset() {
	*x = ScalePodResp{}
	mi := &file_nasus_nasus_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScalePodResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScalePodResp) ProtoMessage() {}

func (x *ScalePodResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScalePodResp.ProtoReflect.Descriptor instead.
func (*ScalePodResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{42}
}

func (x *ScalePodResp) GetOldReplicas() int64 {
	if x != nil {
		return x.OldReplicas
	}
	return 0
}

type ServiceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port     int32 `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`                         // 端口
	NodePort int32 `protobuf:"varint,2,opt,name=node_port,json=nodePort,proto3" json:"node_port,omitempty"` // 节点端口
}

func (x *ServiceItem) Reset() {
	*x = ServiceItem{}
	mi := &file_nasus_nasus_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceItem) ProtoMessage() {}

func (x *ServiceItem) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceItem.ProtoReflect.Descriptor instead.
func (*ServiceItem) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{43}
}

func (x *ServiceItem) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServiceItem) GetNodePort() int32 {
	if x != nil {
		return x.NodePort
	}
	return 0
}

type GetServiceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NameSpace   string `protobuf:"bytes,1,opt,name=name_space,json=nameSpace,proto3" json:"name_space,omitempty"`
	ServiceName string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
}

func (x *GetServiceReq) Reset() {
	*x = GetServiceReq{}
	mi := &file_nasus_nasus_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceReq) ProtoMessage() {}

func (x *GetServiceReq) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceReq.ProtoReflect.Descriptor instead.
func (*GetServiceReq) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{44}
}

func (x *GetServiceReq) GetNameSpace() string {
	if x != nil {
		return x.NameSpace
	}
	return ""
}

func (x *GetServiceReq) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type GetServiceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*ServiceItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *GetServiceResp) Reset() {
	*x = GetServiceResp{}
	mi := &file_nasus_nasus_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResp) ProtoMessage() {}

func (x *GetServiceResp) ProtoReflect() protoreflect.Message {
	mi := &file_nasus_nasus_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResp.ProtoReflect.Descriptor instead.
func (*GetServiceResp) Descriptor() ([]byte, []int) {
	return file_nasus_nasus_proto_rawDescGZIP(), []int{45}
}

func (x *GetServiceResp) GetData() []*ServiceItem {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_nasus_nasus_proto protoreflect.FileDescriptor

var file_nasus_nasus_proto_rawDesc = []byte{
	0x0a, 0x11, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2f, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x22, 0x0c, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x22, 0x21, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x23, 0x0a, 0x0d, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x10, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x5a, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x0b,
	0x0a, 0x09, 0x41, 0x64, 0x64, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x22, 0x5a, 0x0a, 0x08, 0x44,
	0x65, 0x6c, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x0b, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x43, 0x4d,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x5d, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x4d,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x0e, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x4d, 0x52,
	0x65, 0x73, 0x70, 0x22, 0x48, 0x0a, 0x0a, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x4d, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x21, 0x0a,
	0x0b, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x79, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a,
	0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x4b, 0x0a, 0x0a, 0x4c,
	0x69, 0x73, 0x74, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e,
	0x43, 0x4d, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x58, 0x0a, 0x06, 0x43, 0x4d, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x48, 0x0a, 0x0a, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x52, 0x65, 0x71,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb4, 0x01, 0x0a,
	0x0b, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x85, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61,
	0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x4f, 0x0a, 0x0c, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0xe8, 0x01, 0x0a,
	0x08, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x73, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f,
	0x73, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x72, 0x6e,
	0x65, 0x6c, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x5f, 0x61, 0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x41, 0x72, 0x63, 0x68, 0x22, 0x0e, 0x0a, 0x0c, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x22, 0x34, 0x0a, 0x0d, 0x57, 0x61, 0x74, 0x63, 0x68,
	0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x23, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x53, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x70,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x64, 0x49, 0x70, 0x73, 0x22,
	0x86, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x48, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x17, 0x0a,
	0x07, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06,
	0x70, 0x6f, 0x64, 0x49, 0x70, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e,
	0x75, 0x65, 0x22, 0x55, 0x0a, 0x11, 0x57, 0x61, 0x74, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x2d, 0x0a, 0x12, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x17, 0x0a, 0x07, 0x70, 0x6f, 0x64, 0x5f, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x70, 0x6f, 0x64, 0x49, 0x70, 0x73, 0x22, 0xe3, 0x01, 0x0a, 0x07, 0x50, 0x6f, 0x64,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x17,
	0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x45,
	0x0a, 0x09, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f,
	0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x30, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x22, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0e, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x84, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74,
	0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65,
	0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x4d,
	0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x22, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x6e, 0x61,
	0x73, 0x75, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x22, 0x45, 0x0a,
	0x09, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61,
	0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x0c, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x22, 0xb4, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x6c, 0x69,
	0x6e, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x61, 0x69, 0x6c, 0x4c,
	0x69, 0x6e, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x69, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x69, 0x6e,
	0x63, 0x65, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x24, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22,
	0xec, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x6e, 0x76, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x76, 0x22, 0x0f,
	0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x47, 0x0a, 0x0b, 0x57, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x0e, 0x0a, 0x0c, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x63, 0x0a, 0x0b, 0x53, 0x63, 0x61, 0x6c,
	0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d,
	0x65, 0x53, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x22, 0x31, 0x0a,
	0x0c, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a,
	0x0c, 0x6f, 0x6c, 0x64, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73,
	0x22, 0x3e, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x72, 0x74,
	0x22, 0x51, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x53, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x38, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x26, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x32, 0xa7, 0x09,
	0x0a, 0x05, 0x4e, 0x61, 0x73, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x12, 0x11, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x0a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x14, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x52, 0x65, 0x71, 0x1a,
	0x15, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f,
	0x6e, 0x66, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x2c, 0x0a, 0x05, 0x41, 0x64, 0x64, 0x43,
	0x4d, 0x12, 0x0f, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x4d, 0x52,
	0x65, 0x71, 0x1a, 0x10, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x41, 0x64, 0x64, 0x43, 0x4d,
	0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x2c, 0x0a, 0x05, 0x44, 0x65, 0x6c, 0x43, 0x4d, 0x12,
	0x0f, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x43, 0x4d, 0x52, 0x65, 0x71,
	0x1a, 0x10, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x43, 0x4d, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x12, 0x35, 0x0a, 0x08, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x4d,
	0x12, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x4d, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x32, 0x0a, 0x07, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x4d, 0x12, 0x11, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x51,
	0x75, 0x65, 0x72, 0x79, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12,
	0x2f, 0x0a, 0x06, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x4d, 0x12, 0x10, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6e, 0x61,
	0x73, 0x75, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x4d, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00,
	0x12, 0x34, 0x0a, 0x07, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x12, 0x11, 0x2e, 0x6e, 0x61,
	0x73, 0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x52, 0x65, 0x71, 0x1a, 0x12,
	0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x43, 0x4d, 0x52, 0x65,
	0x73, 0x70, 0x22, 0x00, 0x30, 0x01, 0x12, 0x35, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x12, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3a, 0x0a,
	0x09, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x13, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x14, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x30, 0x01, 0x12, 0x41, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x17, 0x2e,
	0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x49, 0x0a, 0x0e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x12, 0x18, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57, 0x61, 0x74,
	0x63, 0x68, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x30, 0x01, 0x12, 0x2f, 0x0a,
	0x06, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x12, 0x10, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x32,
	0x0a, 0x07, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x12, 0x11, 0x2e, 0x6e, 0x61, 0x73, 0x75,
	0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x2f, 0x0a, 0x06, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x12, 0x10, 0x2e, 0x6e,
	0x61, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x11,
	0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x22, 0x00, 0x12, 0x38, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64,
	0x12, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50,
	0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3b, 0x0a,
	0x0a, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x14, 0x2e, 0x6e, 0x61,
	0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x15, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x37, 0x0a, 0x08, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x50, 0x6f, 0x64, 0x12, 0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x6e, 0x61, 0x73,
	0x75, 0x73, 0x2e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x30, 0x01, 0x12, 0x35, 0x0a, 0x08, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x50, 0x6f, 0x64, 0x12,
	0x12, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x50, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6c,
	0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x12, 0x3b, 0x0a, 0x0a, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x14, 0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x15,
	0x2e, 0x6e, 0x61, 0x73, 0x75, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x00, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61,
	0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x6e, 0x61,
	0x73, 0x75, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_nasus_nasus_proto_rawDescOnce sync.Once
	file_nasus_nasus_proto_rawDescData = file_nasus_nasus_proto_rawDesc
)

func file_nasus_nasus_proto_rawDescGZIP() []byte {
	file_nasus_nasus_proto_rawDescOnce.Do(func() {
		file_nasus_nasus_proto_rawDescData = protoimpl.X.CompressGZIP(file_nasus_nasus_proto_rawDescData)
	})
	return file_nasus_nasus_proto_rawDescData
}

var file_nasus_nasus_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_nasus_nasus_proto_goTypes = []any{
	(*GetConfReq)(nil),         // 0: nasus.GetConfReq
	(*GetConfResp)(nil),        // 1: nasus.GetConfResp
	(*UpdateConfReq)(nil),      // 2: nasus.UpdateConfReq
	(*UpdateConfResp)(nil),     // 3: nasus.UpdateConfResp
	(*AddCMReq)(nil),           // 4: nasus.AddCMReq
	(*AddCMResp)(nil),          // 5: nasus.AddCMResp
	(*DelCMReq)(nil),           // 6: nasus.DelCMReq
	(*DelCMResp)(nil),          // 7: nasus.DelCMResp
	(*UpdateCMReq)(nil),        // 8: nasus.UpdateCMReq
	(*UpdateCMResp)(nil),       // 9: nasus.UpdateCMResp
	(*QueryCMReq)(nil),         // 10: nasus.QueryCMReq
	(*QueryCMResp)(nil),        // 11: nasus.QueryCMResp
	(*ListCMReq)(nil),          // 12: nasus.ListCMReq
	(*ListCMResp)(nil),         // 13: nasus.ListCMResp
	(*CMItem)(nil),             // 14: nasus.CMItem
	(*WatchCMReq)(nil),         // 15: nasus.WatchCMReq
	(*WatchCMResp)(nil),        // 16: nasus.WatchCMResp
	(*ListNodeReq)(nil),        // 17: nasus.ListNodeReq
	(*ListNodeResp)(nil),       // 18: nasus.ListNodeResp
	(*NodeItem)(nil),           // 19: nasus.NodeItem
	(*WatchNodeReq)(nil),       // 20: nasus.WatchNodeReq
	(*WatchNodeResp)(nil),      // 21: nasus.WatchNodeResp
	(*GetEndpointsReq)(nil),    // 22: nasus.GetEndpointsReq
	(*GetEndpointsResp)(nil),   // 23: nasus.GetEndpointsResp
	(*ListEndpointsReq)(nil),   // 24: nasus.ListEndpointsReq
	(*ListEndpointsResp)(nil),  // 25: nasus.ListEndpointsResp
	(*WatchEndpointsReq)(nil),  // 26: nasus.WatchEndpointsReq
	(*WatchEndpointsResp)(nil), // 27: nasus.WatchEndpointsResp
	(*PodItem)(nil),            // 28: nasus.PodItem
	(*GetPodReq)(nil),          // 29: nasus.GetPodReq
	(*GetPodResp)(nil),         // 30: nasus.GetPodResp
	(*ListPodReq)(nil),         // 31: nasus.ListPodReq
	(*ListPodResp)(nil),        // 32: nasus.ListPodResp
	(*DelPodReq)(nil),          // 33: nasus.DelPodReq
	(*DelPodResp)(nil),         // 34: nasus.DelPodResp
	(*GetPodLogsReq)(nil),      // 35: nasus.GetPodLogsReq
	(*GetPodLogsResp)(nil),     // 36: nasus.GetPodLogsResp
	(*UpdatePodReq)(nil),       // 37: nasus.UpdatePodReq
	(*UpdatePodResp)(nil),      // 38: nasus.UpdatePodResp
	(*WatchPodReq)(nil),        // 39: nasus.WatchPodReq
	(*WatchPodResp)(nil),       // 40: nasus.WatchPodResp
	(*ScalePodReq)(nil),        // 41: nasus.ScalePodReq
	(*ScalePodResp)(nil),       // 42: nasus.ScalePodResp
	(*ServiceItem)(nil),        // 43: nasus.ServiceItem
	(*GetServiceReq)(nil),      // 44: nasus.GetServiceReq
	(*GetServiceResp)(nil),     // 45: nasus.GetServiceResp
	nil,                        // 46: nasus.WatchCMResp.DataEntry
}
var file_nasus_nasus_proto_depIdxs = []int32{
	14, // 0: nasus.ListCMResp.data:type_name -> nasus.CMItem
	46, // 1: nasus.WatchCMResp.data:type_name -> nasus.WatchCMResp.DataEntry
	19, // 2: nasus.ListNodeResp.data:type_name -> nasus.NodeItem
	19, // 3: nasus.WatchNodeResp.data:type_name -> nasus.NodeItem
	28, // 4: nasus.GetPodResp.data:type_name -> nasus.PodItem
	28, // 5: nasus.ListPodResp.data:type_name -> nasus.PodItem
	43, // 6: nasus.GetServiceResp.data:type_name -> nasus.ServiceItem
	0,  // 7: nasus.Nasus.GetConf:input_type -> nasus.GetConfReq
	2,  // 8: nasus.Nasus.UpdateConf:input_type -> nasus.UpdateConfReq
	4,  // 9: nasus.Nasus.AddCM:input_type -> nasus.AddCMReq
	6,  // 10: nasus.Nasus.DelCM:input_type -> nasus.DelCMReq
	8,  // 11: nasus.Nasus.UpdateCM:input_type -> nasus.UpdateCMReq
	10, // 12: nasus.Nasus.QueryCM:input_type -> nasus.QueryCMReq
	12, // 13: nasus.Nasus.ListCM:input_type -> nasus.ListCMReq
	15, // 14: nasus.Nasus.WatchCM:input_type -> nasus.WatchCMReq
	17, // 15: nasus.Nasus.ListNode:input_type -> nasus.ListNodeReq
	20, // 16: nasus.Nasus.WatchNode:input_type -> nasus.WatchNodeReq
	22, // 17: nasus.Nasus.GetEndpoints:input_type -> nasus.GetEndpointsReq
	24, // 18: nasus.Nasus.ListEndpoints:input_type -> nasus.ListEndpointsReq
	26, // 19: nasus.Nasus.WatchEndpoints:input_type -> nasus.WatchEndpointsReq
	29, // 20: nasus.Nasus.GetPod:input_type -> nasus.GetPodReq
	31, // 21: nasus.Nasus.ListPod:input_type -> nasus.ListPodReq
	33, // 22: nasus.Nasus.DelPod:input_type -> nasus.DelPodReq
	37, // 23: nasus.Nasus.UpdatePod:input_type -> nasus.UpdatePodReq
	35, // 24: nasus.Nasus.GetPodLogs:input_type -> nasus.GetPodLogsReq
	39, // 25: nasus.Nasus.WatchPod:input_type -> nasus.WatchPodReq
	41, // 26: nasus.Nasus.ScalePod:input_type -> nasus.ScalePodReq
	44, // 27: nasus.Nasus.GetService:input_type -> nasus.GetServiceReq
	1,  // 28: nasus.Nasus.GetConf:output_type -> nasus.GetConfResp
	3,  // 29: nasus.Nasus.UpdateConf:output_type -> nasus.UpdateConfResp
	5,  // 30: nasus.Nasus.AddCM:output_type -> nasus.AddCMResp
	7,  // 31: nasus.Nasus.DelCM:output_type -> nasus.DelCMResp
	9,  // 32: nasus.Nasus.UpdateCM:output_type -> nasus.UpdateCMResp
	11, // 33: nasus.Nasus.QueryCM:output_type -> nasus.QueryCMResp
	13, // 34: nasus.Nasus.ListCM:output_type -> nasus.ListCMResp
	16, // 35: nasus.Nasus.WatchCM:output_type -> nasus.WatchCMResp
	18, // 36: nasus.Nasus.ListNode:output_type -> nasus.ListNodeResp
	21, // 37: nasus.Nasus.WatchNode:output_type -> nasus.WatchNodeResp
	23, // 38: nasus.Nasus.GetEndpoints:output_type -> nasus.GetEndpointsResp
	25, // 39: nasus.Nasus.ListEndpoints:output_type -> nasus.ListEndpointsResp
	27, // 40: nasus.Nasus.WatchEndpoints:output_type -> nasus.WatchEndpointsResp
	30, // 41: nasus.Nasus.GetPod:output_type -> nasus.GetPodResp
	32, // 42: nasus.Nasus.ListPod:output_type -> nasus.ListPodResp
	34, // 43: nasus.Nasus.DelPod:output_type -> nasus.DelPodResp
	38, // 44: nasus.Nasus.UpdatePod:output_type -> nasus.UpdatePodResp
	36, // 45: nasus.Nasus.GetPodLogs:output_type -> nasus.GetPodLogsResp
	40, // 46: nasus.Nasus.WatchPod:output_type -> nasus.WatchPodResp
	42, // 47: nasus.Nasus.ScalePod:output_type -> nasus.ScalePodResp
	45, // 48: nasus.Nasus.GetService:output_type -> nasus.GetServiceResp
	28, // [28:49] is the sub-list for method output_type
	7,  // [7:28] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_nasus_nasus_proto_init() }
func file_nasus_nasus_proto_init() {
	if File_nasus_nasus_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_nasus_nasus_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_nasus_nasus_proto_goTypes,
		DependencyIndexes: file_nasus_nasus_proto_depIdxs,
		MessageInfos:      file_nasus_nasus_proto_msgTypes,
	}.Build()
	File_nasus_nasus_proto = out.File
	file_nasus_nasus_proto_rawDesc = nil
	file_nasus_nasus_proto_goTypes = nil
	file_nasus_nasus_proto_depIdxs = nil
}
