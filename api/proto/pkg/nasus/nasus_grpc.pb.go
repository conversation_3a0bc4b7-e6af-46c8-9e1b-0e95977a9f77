// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: nasus/nasus.proto

package nasus

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Nasus_GetConf_FullMethodName        = "/nasus.Nasus/GetConf"
	Nasus_UpdateConf_FullMethodName     = "/nasus.Nasus/UpdateConf"
	Nasus_AddCM_FullMethodName          = "/nasus.Nasus/AddCM"
	Nasus_DelCM_FullMethodName          = "/nasus.Nasus/DelCM"
	Nasus_UpdateCM_FullMethodName       = "/nasus.Nasus/UpdateCM"
	Nasus_QueryCM_FullMethodName        = "/nasus.Nasus/QueryCM"
	Nasus_ListCM_FullMethodName         = "/nasus.Nasus/ListCM"
	Nasus_WatchCM_FullMethodName        = "/nasus.Nasus/WatchCM"
	Nasus_ListNode_FullMethodName       = "/nasus.Nasus/ListNode"
	Nasus_WatchNode_FullMethodName      = "/nasus.Nasus/WatchNode"
	Nasus_GetEndpoints_FullMethodName   = "/nasus.Nasus/GetEndpoints"
	Nasus_ListEndpoints_FullMethodName  = "/nasus.Nasus/ListEndpoints"
	Nasus_WatchEndpoints_FullMethodName = "/nasus.Nasus/WatchEndpoints"
	Nasus_GetPod_FullMethodName         = "/nasus.Nasus/GetPod"
	Nasus_ListPod_FullMethodName        = "/nasus.Nasus/ListPod"
	Nasus_DelPod_FullMethodName         = "/nasus.Nasus/DelPod"
	Nasus_UpdatePod_FullMethodName      = "/nasus.Nasus/UpdatePod"
	Nasus_GetPodLogs_FullMethodName     = "/nasus.Nasus/GetPodLogs"
	Nasus_WatchPod_FullMethodName       = "/nasus.Nasus/WatchPod"
	Nasus_ScalePod_FullMethodName       = "/nasus.Nasus/ScalePod"
	Nasus_GetService_FullMethodName     = "/nasus.Nasus/GetService"
)

// NasusClient is the client API for Nasus service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 配置中心服务
type NasusClient interface {
	// 获取业务配置
	GetConf(ctx context.Context, in *GetConfReq, opts ...grpc.CallOption) (*GetConfResp, error)
	// 更新业务配置
	UpdateConf(ctx context.Context, in *UpdateConfReq, opts ...grpc.CallOption) (*UpdateConfResp, error)
	// 添加配置
	AddCM(ctx context.Context, in *AddCMReq, opts ...grpc.CallOption) (*AddCMResp, error)
	// 删除配置
	DelCM(ctx context.Context, in *DelCMReq, opts ...grpc.CallOption) (*DelCMResp, error)
	// 更新配置
	UpdateCM(ctx context.Context, in *UpdateCMReq, opts ...grpc.CallOption) (*UpdateCMResp, error)
	// 单条查询
	QueryCM(ctx context.Context, in *QueryCMReq, opts ...grpc.CallOption) (*QueryCMResp, error)
	// 列表查询
	ListCM(ctx context.Context, in *ListCMReq, opts ...grpc.CallOption) (*ListCMResp, error)
	// 监听配置
	WatchCM(ctx context.Context, in *WatchCMReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchCMResp], error)
	// 节点列表
	ListNode(ctx context.Context, in *ListNodeReq, opts ...grpc.CallOption) (*ListNodeResp, error)
	// 监听节点
	WatchNode(ctx context.Context, in *WatchNodeReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchNodeResp], error)
	// 单条查询
	GetEndpoints(ctx context.Context, in *GetEndpointsReq, opts ...grpc.CallOption) (*GetEndpointsResp, error)
	// 列表
	ListEndpoints(ctx context.Context, in *ListEndpointsReq, opts ...grpc.CallOption) (*ListEndpointsResp, error)
	// 监听Endpoints
	WatchEndpoints(ctx context.Context, in *WatchEndpointsReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchEndpointsResp], error)
	// 获取Pod
	GetPod(ctx context.Context, in *GetPodReq, opts ...grpc.CallOption) (*GetPodResp, error)
	// 列表Pod
	ListPod(ctx context.Context, in *ListPodReq, opts ...grpc.CallOption) (*ListPodResp, error)
	// 删除Pod
	DelPod(ctx context.Context, in *DelPodReq, opts ...grpc.CallOption) (*DelPodResp, error)
	// 更新Pod
	UpdatePod(ctx context.Context, in *UpdatePodReq, opts ...grpc.CallOption) (*UpdatePodResp, error)
	// 获取Pod日志
	GetPodLogs(ctx context.Context, in *GetPodLogsReq, opts ...grpc.CallOption) (*GetPodLogsResp, error)
	// 订阅Pod
	WatchPod(ctx context.Context, in *WatchPodReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchPodResp], error)
	// 扩缩容Pod
	ScalePod(ctx context.Context, in *ScalePodReq, opts ...grpc.CallOption) (*ScalePodResp, error)
	// 获取Service
	GetService(ctx context.Context, in *GetServiceReq, opts ...grpc.CallOption) (*GetServiceResp, error)
}

type nasusClient struct {
	cc grpc.ClientConnInterface
}

func NewNasusClient(cc grpc.ClientConnInterface) NasusClient {
	return &nasusClient{cc}
}

func (c *nasusClient) GetConf(ctx context.Context, in *GetConfReq, opts ...grpc.CallOption) (*GetConfResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfResp)
	err := c.cc.Invoke(ctx, Nasus_GetConf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) UpdateConf(ctx context.Context, in *UpdateConfReq, opts ...grpc.CallOption) (*UpdateConfResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateConfResp)
	err := c.cc.Invoke(ctx, Nasus_UpdateConf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) AddCM(ctx context.Context, in *AddCMReq, opts ...grpc.CallOption) (*AddCMResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddCMResp)
	err := c.cc.Invoke(ctx, Nasus_AddCM_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) DelCM(ctx context.Context, in *DelCMReq, opts ...grpc.CallOption) (*DelCMResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelCMResp)
	err := c.cc.Invoke(ctx, Nasus_DelCM_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) UpdateCM(ctx context.Context, in *UpdateCMReq, opts ...grpc.CallOption) (*UpdateCMResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCMResp)
	err := c.cc.Invoke(ctx, Nasus_UpdateCM_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) QueryCM(ctx context.Context, in *QueryCMReq, opts ...grpc.CallOption) (*QueryCMResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryCMResp)
	err := c.cc.Invoke(ctx, Nasus_QueryCM_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) ListCM(ctx context.Context, in *ListCMReq, opts ...grpc.CallOption) (*ListCMResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCMResp)
	err := c.cc.Invoke(ctx, Nasus_ListCM_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) WatchCM(ctx context.Context, in *WatchCMReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchCMResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Nasus_ServiceDesc.Streams[0], Nasus_WatchCM_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WatchCMReq, WatchCMResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchCMClient = grpc.ServerStreamingClient[WatchCMResp]

func (c *nasusClient) ListNode(ctx context.Context, in *ListNodeReq, opts ...grpc.CallOption) (*ListNodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodeResp)
	err := c.cc.Invoke(ctx, Nasus_ListNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) WatchNode(ctx context.Context, in *WatchNodeReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchNodeResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Nasus_ServiceDesc.Streams[1], Nasus_WatchNode_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WatchNodeReq, WatchNodeResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchNodeClient = grpc.ServerStreamingClient[WatchNodeResp]

func (c *nasusClient) GetEndpoints(ctx context.Context, in *GetEndpointsReq, opts ...grpc.CallOption) (*GetEndpointsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetEndpointsResp)
	err := c.cc.Invoke(ctx, Nasus_GetEndpoints_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) ListEndpoints(ctx context.Context, in *ListEndpointsReq, opts ...grpc.CallOption) (*ListEndpointsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListEndpointsResp)
	err := c.cc.Invoke(ctx, Nasus_ListEndpoints_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) WatchEndpoints(ctx context.Context, in *WatchEndpointsReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchEndpointsResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Nasus_ServiceDesc.Streams[2], Nasus_WatchEndpoints_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WatchEndpointsReq, WatchEndpointsResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchEndpointsClient = grpc.ServerStreamingClient[WatchEndpointsResp]

func (c *nasusClient) GetPod(ctx context.Context, in *GetPodReq, opts ...grpc.CallOption) (*GetPodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPodResp)
	err := c.cc.Invoke(ctx, Nasus_GetPod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) ListPod(ctx context.Context, in *ListPodReq, opts ...grpc.CallOption) (*ListPodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPodResp)
	err := c.cc.Invoke(ctx, Nasus_ListPod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) DelPod(ctx context.Context, in *DelPodReq, opts ...grpc.CallOption) (*DelPodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelPodResp)
	err := c.cc.Invoke(ctx, Nasus_DelPod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) UpdatePod(ctx context.Context, in *UpdatePodReq, opts ...grpc.CallOption) (*UpdatePodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePodResp)
	err := c.cc.Invoke(ctx, Nasus_UpdatePod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) GetPodLogs(ctx context.Context, in *GetPodLogsReq, opts ...grpc.CallOption) (*GetPodLogsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPodLogsResp)
	err := c.cc.Invoke(ctx, Nasus_GetPodLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) WatchPod(ctx context.Context, in *WatchPodReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[WatchPodResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Nasus_ServiceDesc.Streams[3], Nasus_WatchPod_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[WatchPodReq, WatchPodResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchPodClient = grpc.ServerStreamingClient[WatchPodResp]

func (c *nasusClient) ScalePod(ctx context.Context, in *ScalePodReq, opts ...grpc.CallOption) (*ScalePodResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScalePodResp)
	err := c.cc.Invoke(ctx, Nasus_ScalePod_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nasusClient) GetService(ctx context.Context, in *GetServiceReq, opts ...grpc.CallOption) (*GetServiceResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceResp)
	err := c.cc.Invoke(ctx, Nasus_GetService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NasusServer is the server API for Nasus service.
// All implementations must embed UnimplementedNasusServer
// for forward compatibility.
//
// 配置中心服务
type NasusServer interface {
	// 获取业务配置
	GetConf(context.Context, *GetConfReq) (*GetConfResp, error)
	// 更新业务配置
	UpdateConf(context.Context, *UpdateConfReq) (*UpdateConfResp, error)
	// 添加配置
	AddCM(context.Context, *AddCMReq) (*AddCMResp, error)
	// 删除配置
	DelCM(context.Context, *DelCMReq) (*DelCMResp, error)
	// 更新配置
	UpdateCM(context.Context, *UpdateCMReq) (*UpdateCMResp, error)
	// 单条查询
	QueryCM(context.Context, *QueryCMReq) (*QueryCMResp, error)
	// 列表查询
	ListCM(context.Context, *ListCMReq) (*ListCMResp, error)
	// 监听配置
	WatchCM(*WatchCMReq, grpc.ServerStreamingServer[WatchCMResp]) error
	// 节点列表
	ListNode(context.Context, *ListNodeReq) (*ListNodeResp, error)
	// 监听节点
	WatchNode(*WatchNodeReq, grpc.ServerStreamingServer[WatchNodeResp]) error
	// 单条查询
	GetEndpoints(context.Context, *GetEndpointsReq) (*GetEndpointsResp, error)
	// 列表
	ListEndpoints(context.Context, *ListEndpointsReq) (*ListEndpointsResp, error)
	// 监听Endpoints
	WatchEndpoints(*WatchEndpointsReq, grpc.ServerStreamingServer[WatchEndpointsResp]) error
	// 获取Pod
	GetPod(context.Context, *GetPodReq) (*GetPodResp, error)
	// 列表Pod
	ListPod(context.Context, *ListPodReq) (*ListPodResp, error)
	// 删除Pod
	DelPod(context.Context, *DelPodReq) (*DelPodResp, error)
	// 更新Pod
	UpdatePod(context.Context, *UpdatePodReq) (*UpdatePodResp, error)
	// 获取Pod日志
	GetPodLogs(context.Context, *GetPodLogsReq) (*GetPodLogsResp, error)
	// 订阅Pod
	WatchPod(*WatchPodReq, grpc.ServerStreamingServer[WatchPodResp]) error
	// 扩缩容Pod
	ScalePod(context.Context, *ScalePodReq) (*ScalePodResp, error)
	// 获取Service
	GetService(context.Context, *GetServiceReq) (*GetServiceResp, error)
	mustEmbedUnimplementedNasusServer()
}

// UnimplementedNasusServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedNasusServer struct{}

func (UnimplementedNasusServer) GetConf(context.Context, *GetConfReq) (*GetConfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConf not implemented")
}
func (UnimplementedNasusServer) UpdateConf(context.Context, *UpdateConfReq) (*UpdateConfResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateConf not implemented")
}
func (UnimplementedNasusServer) AddCM(context.Context, *AddCMReq) (*AddCMResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCM not implemented")
}
func (UnimplementedNasusServer) DelCM(context.Context, *DelCMReq) (*DelCMResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelCM not implemented")
}
func (UnimplementedNasusServer) UpdateCM(context.Context, *UpdateCMReq) (*UpdateCMResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCM not implemented")
}
func (UnimplementedNasusServer) QueryCM(context.Context, *QueryCMReq) (*QueryCMResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryCM not implemented")
}
func (UnimplementedNasusServer) ListCM(context.Context, *ListCMReq) (*ListCMResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCM not implemented")
}
func (UnimplementedNasusServer) WatchCM(*WatchCMReq, grpc.ServerStreamingServer[WatchCMResp]) error {
	return status.Errorf(codes.Unimplemented, "method WatchCM not implemented")
}
func (UnimplementedNasusServer) ListNode(context.Context, *ListNodeReq) (*ListNodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNode not implemented")
}
func (UnimplementedNasusServer) WatchNode(*WatchNodeReq, grpc.ServerStreamingServer[WatchNodeResp]) error {
	return status.Errorf(codes.Unimplemented, "method WatchNode not implemented")
}
func (UnimplementedNasusServer) GetEndpoints(context.Context, *GetEndpointsReq) (*GetEndpointsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEndpoints not implemented")
}
func (UnimplementedNasusServer) ListEndpoints(context.Context, *ListEndpointsReq) (*ListEndpointsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEndpoints not implemented")
}
func (UnimplementedNasusServer) WatchEndpoints(*WatchEndpointsReq, grpc.ServerStreamingServer[WatchEndpointsResp]) error {
	return status.Errorf(codes.Unimplemented, "method WatchEndpoints not implemented")
}
func (UnimplementedNasusServer) GetPod(context.Context, *GetPodReq) (*GetPodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPod not implemented")
}
func (UnimplementedNasusServer) ListPod(context.Context, *ListPodReq) (*ListPodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPod not implemented")
}
func (UnimplementedNasusServer) DelPod(context.Context, *DelPodReq) (*DelPodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelPod not implemented")
}
func (UnimplementedNasusServer) UpdatePod(context.Context, *UpdatePodReq) (*UpdatePodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePod not implemented")
}
func (UnimplementedNasusServer) GetPodLogs(context.Context, *GetPodLogsReq) (*GetPodLogsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPodLogs not implemented")
}
func (UnimplementedNasusServer) WatchPod(*WatchPodReq, grpc.ServerStreamingServer[WatchPodResp]) error {
	return status.Errorf(codes.Unimplemented, "method WatchPod not implemented")
}
func (UnimplementedNasusServer) ScalePod(context.Context, *ScalePodReq) (*ScalePodResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScalePod not implemented")
}
func (UnimplementedNasusServer) GetService(context.Context, *GetServiceReq) (*GetServiceResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetService not implemented")
}
func (UnimplementedNasusServer) mustEmbedUnimplementedNasusServer() {}
func (UnimplementedNasusServer) testEmbeddedByValue()               {}

// UnsafeNasusServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NasusServer will
// result in compilation errors.
type UnsafeNasusServer interface {
	mustEmbedUnimplementedNasusServer()
}

func RegisterNasusServer(s grpc.ServiceRegistrar, srv NasusServer) {
	// If the following call pancis, it indicates UnimplementedNasusServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Nasus_ServiceDesc, srv)
}

func _Nasus_GetConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).GetConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_GetConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).GetConf(ctx, req.(*GetConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_UpdateConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).UpdateConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_UpdateConf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).UpdateConf(ctx, req.(*UpdateConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_AddCM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCMReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).AddCM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_AddCM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).AddCM(ctx, req.(*AddCMReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_DelCM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCMReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).DelCM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_DelCM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).DelCM(ctx, req.(*DelCMReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_UpdateCM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCMReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).UpdateCM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_UpdateCM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).UpdateCM(ctx, req.(*UpdateCMReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_QueryCM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryCMReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).QueryCM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_QueryCM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).QueryCM(ctx, req.(*QueryCMReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_ListCM_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCMReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).ListCM(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_ListCM_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).ListCM(ctx, req.(*ListCMReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_WatchCM_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchCMReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(NasusServer).WatchCM(m, &grpc.GenericServerStream[WatchCMReq, WatchCMResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchCMServer = grpc.ServerStreamingServer[WatchCMResp]

func _Nasus_ListNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).ListNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_ListNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).ListNode(ctx, req.(*ListNodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_WatchNode_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchNodeReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(NasusServer).WatchNode(m, &grpc.GenericServerStream[WatchNodeReq, WatchNodeResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchNodeServer = grpc.ServerStreamingServer[WatchNodeResp]

func _Nasus_GetEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEndpointsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).GetEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_GetEndpoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).GetEndpoints(ctx, req.(*GetEndpointsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_ListEndpoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEndpointsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).ListEndpoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_ListEndpoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).ListEndpoints(ctx, req.(*ListEndpointsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_WatchEndpoints_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchEndpointsReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(NasusServer).WatchEndpoints(m, &grpc.GenericServerStream[WatchEndpointsReq, WatchEndpointsResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchEndpointsServer = grpc.ServerStreamingServer[WatchEndpointsResp]

func _Nasus_GetPod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).GetPod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_GetPod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).GetPod(ctx, req.(*GetPodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_ListPod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).ListPod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_ListPod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).ListPod(ctx, req.(*ListPodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_DelPod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).DelPod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_DelPod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).DelPod(ctx, req.(*DelPodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_UpdatePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).UpdatePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_UpdatePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).UpdatePod(ctx, req.(*UpdatePodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_GetPodLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).GetPodLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_GetPodLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).GetPodLogs(ctx, req.(*GetPodLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_WatchPod_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(WatchPodReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(NasusServer).WatchPod(m, &grpc.GenericServerStream[WatchPodReq, WatchPodResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Nasus_WatchPodServer = grpc.ServerStreamingServer[WatchPodResp]

func _Nasus_ScalePod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScalePodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).ScalePod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_ScalePod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).ScalePod(ctx, req.(*ScalePodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Nasus_GetService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NasusServer).GetService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Nasus_GetService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NasusServer).GetService(ctx, req.(*GetServiceReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Nasus_ServiceDesc is the grpc.ServiceDesc for Nasus service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Nasus_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "nasus.Nasus",
	HandlerType: (*NasusServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConf",
			Handler:    _Nasus_GetConf_Handler,
		},
		{
			MethodName: "UpdateConf",
			Handler:    _Nasus_UpdateConf_Handler,
		},
		{
			MethodName: "AddCM",
			Handler:    _Nasus_AddCM_Handler,
		},
		{
			MethodName: "DelCM",
			Handler:    _Nasus_DelCM_Handler,
		},
		{
			MethodName: "UpdateCM",
			Handler:    _Nasus_UpdateCM_Handler,
		},
		{
			MethodName: "QueryCM",
			Handler:    _Nasus_QueryCM_Handler,
		},
		{
			MethodName: "ListCM",
			Handler:    _Nasus_ListCM_Handler,
		},
		{
			MethodName: "ListNode",
			Handler:    _Nasus_ListNode_Handler,
		},
		{
			MethodName: "GetEndpoints",
			Handler:    _Nasus_GetEndpoints_Handler,
		},
		{
			MethodName: "ListEndpoints",
			Handler:    _Nasus_ListEndpoints_Handler,
		},
		{
			MethodName: "GetPod",
			Handler:    _Nasus_GetPod_Handler,
		},
		{
			MethodName: "ListPod",
			Handler:    _Nasus_ListPod_Handler,
		},
		{
			MethodName: "DelPod",
			Handler:    _Nasus_DelPod_Handler,
		},
		{
			MethodName: "UpdatePod",
			Handler:    _Nasus_UpdatePod_Handler,
		},
		{
			MethodName: "GetPodLogs",
			Handler:    _Nasus_GetPodLogs_Handler,
		},
		{
			MethodName: "ScalePod",
			Handler:    _Nasus_ScalePod_Handler,
		},
		{
			MethodName: "GetService",
			Handler:    _Nasus_GetService_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "WatchCM",
			Handler:       _Nasus_WatchCM_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchNode",
			Handler:       _Nasus_WatchNode_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchEndpoints",
			Handler:       _Nasus_WatchEndpoints_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "WatchPod",
			Handler:       _Nasus_WatchPod_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "nasus/nasus.proto",
}
