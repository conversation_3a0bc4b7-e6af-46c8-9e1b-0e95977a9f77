// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: nasus/config.proto

package nasus

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BusiConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BusiConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BusiConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BusiConfigMultiError, or
// nil if none found.
func (m *BusiConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BusiConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSystem()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "System",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "System",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSystem()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BusiConfigValidationError{
				field:  "System",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFileThreat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "FileThreat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "FileThreat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileThreat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BusiConfigValidationError{
				field:  "FileThreat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLogArchive()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "LogArchive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BusiConfigValidationError{
					field:  "LogArchive",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLogArchive()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BusiConfigValidationError{
				field:  "LogArchive",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BusiConfigMultiError(errors)
	}

	return nil
}

// BusiConfigMultiError is an error wrapping multiple validation errors
// returned by BusiConfig.ValidateAll() if the designated constraints aren't met.
type BusiConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BusiConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BusiConfigMultiError) AllErrors() []error { return m }

// BusiConfigValidationError is the validation error returned by
// BusiConfig.Validate if the designated constraints aren't met.
type BusiConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BusiConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BusiConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BusiConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BusiConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BusiConfigValidationError) ErrorName() string { return "BusiConfigValidationError" }

// Error satisfies the builtin error interface
func (e BusiConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBusiConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BusiConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BusiConfigValidationError{}

// Validate checks the field values on SystemConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SystemConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SystemConfigMultiError, or
// nil if none found.
func (m *SystemConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetGanged()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "Ganged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "Ganged",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGanged()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemConfigValidationError{
				field:  "Ganged",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEngineLib()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "EngineLib",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "EngineLib",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEngineLib()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemConfigValidationError{
				field:  "EngineLib",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCommon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "Common",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemConfigValidationError{
					field:  "Common",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemConfigValidationError{
				field:  "Common",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SystemConfigMultiError(errors)
	}

	return nil
}

// SystemConfigMultiError is an error wrapping multiple validation errors
// returned by SystemConfig.ValidateAll() if the designated constraints aren't met.
type SystemConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemConfigMultiError) AllErrors() []error { return m }

// SystemConfigValidationError is the validation error returned by
// SystemConfig.Validate if the designated constraints aren't met.
type SystemConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemConfigValidationError) ErrorName() string { return "SystemConfigValidationError" }

// Error satisfies the builtin error interface
func (e SystemConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemConfigValidationError{}

// Validate checks the field values on GangedConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GangedConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GangedConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GangedConfigMultiError, or
// nil if none found.
func (m *GangedConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *GangedConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetProtectCloud()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "NetProtectCloud",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "NetProtectCloud",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetProtectCloud()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "NetProtectCloud",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSandbox()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Sandbox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Sandbox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSandbox()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "Sandbox",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNtp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Ntp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Ntp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNtp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "Ntp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Access",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "Access",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "Access",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloudV01()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "CloudV01",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "CloudV01",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloudV01()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "CloudV01",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCloudModel()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "CloudModel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GangedConfigValidationError{
					field:  "CloudModel",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCloudModel()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GangedConfigValidationError{
				field:  "CloudModel",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GangedConfigMultiError(errors)
	}

	return nil
}

// GangedConfigMultiError is an error wrapping multiple validation errors
// returned by GangedConfig.ValidateAll() if the designated constraints aren't met.
type GangedConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GangedConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GangedConfigMultiError) AllErrors() []error { return m }

// GangedConfigValidationError is the validation error returned by
// GangedConfig.Validate if the designated constraints aren't met.
type GangedConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GangedConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GangedConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GangedConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GangedConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GangedConfigValidationError) ErrorName() string { return "GangedConfigValidationError" }

// Error satisfies the builtin error interface
func (e GangedConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGangedConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GangedConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GangedConfigValidationError{}

// Validate checks the field values on NetProtectCloudConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetProtectCloudConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetProtectCloudConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetProtectCloudConfigMultiError, or nil if none found.
func (m *NetProtectCloudConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NetProtectCloudConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for ConnectModel

	// no validation rules for AuthAddr

	// no validation rules for AuthKey

	// no validation rules for ClientId

	// no validation rules for AuthSecret

	// no validation rules for CloudAddr

	// no validation rules for CloudSecret

	// no validation rules for FileStoreAddr

	// no validation rules for FileStoreSecret

	// no validation rules for TaskId

	// no validation rules for FileIntelInspectOnline

	if len(errors) > 0 {
		return NetProtectCloudConfigMultiError(errors)
	}

	return nil
}

// NetProtectCloudConfigMultiError is an error wrapping multiple validation
// errors returned by NetProtectCloudConfig.ValidateAll() if the designated
// constraints aren't met.
type NetProtectCloudConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetProtectCloudConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetProtectCloudConfigMultiError) AllErrors() []error { return m }

// NetProtectCloudConfigValidationError is the validation error returned by
// NetProtectCloudConfig.Validate if the designated constraints aren't met.
type NetProtectCloudConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetProtectCloudConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetProtectCloudConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetProtectCloudConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetProtectCloudConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetProtectCloudConfigValidationError) ErrorName() string {
	return "NetProtectCloudConfigValidationError"
}

// Error satisfies the builtin error interface
func (e NetProtectCloudConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetProtectCloudConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetProtectCloudConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetProtectCloudConfigValidationError{}

// Validate checks the field values on SandboxConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SandboxConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SandboxConfigMultiError, or
// nil if none found.
func (m *SandboxConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Addr

	// no validation rules for ApiKey

	// no validation rules for ApiSecret

	// no validation rules for Uuid

	// no validation rules for FileEnable

	if len(errors) > 0 {
		return SandboxConfigMultiError(errors)
	}

	return nil
}

// SandboxConfigMultiError is an error wrapping multiple validation errors
// returned by SandboxConfig.ValidateAll() if the designated constraints
// aren't met.
type SandboxConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxConfigMultiError) AllErrors() []error { return m }

// SandboxConfigValidationError is the validation error returned by
// SandboxConfig.Validate if the designated constraints aren't met.
type SandboxConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxConfigValidationError) ErrorName() string { return "SandboxConfigValidationError" }

// Error satisfies the builtin error interface
func (e SandboxConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxConfigValidationError{}

// Validate checks the field values on NTPConfig with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NTPConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NTPConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NTPConfigMultiError, or nil
// if none found.
func (m *NTPConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *NTPConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mode

	// no validation rules for Addr

	if len(errors) > 0 {
		return NTPConfigMultiError(errors)
	}

	return nil
}

// NTPConfigMultiError is an error wrapping multiple validation errors returned
// by NTPConfig.ValidateAll() if the designated constraints aren't met.
type NTPConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NTPConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NTPConfigMultiError) AllErrors() []error { return m }

// NTPConfigValidationError is the validation error returned by
// NTPConfig.Validate if the designated constraints aren't met.
type NTPConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NTPConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NTPConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NTPConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NTPConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NTPConfigValidationError) ErrorName() string { return "NTPConfigValidationError" }

// Error satisfies the builtin error interface
func (e NTPConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNTPConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NTPConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NTPConfigValidationError{}

// Validate checks the field values on AccessConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AccessConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccessConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AccessConfigMultiError, or
// nil if none found.
func (m *AccessConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AccessConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	for idx, item := range m.GetGangedSystem() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccessConfigValidationError{
						field:  fmt.Sprintf("GangedSystem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccessConfigValidationError{
						field:  fmt.Sprintf("GangedSystem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccessConfigValidationError{
					field:  fmt.Sprintf("GangedSystem[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccessConfigMultiError(errors)
	}

	return nil
}

// AccessConfigMultiError is an error wrapping multiple validation errors
// returned by AccessConfig.ValidateAll() if the designated constraints aren't met.
type AccessConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccessConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccessConfigMultiError) AllErrors() []error { return m }

// AccessConfigValidationError is the validation error returned by
// AccessConfig.Validate if the designated constraints aren't met.
type AccessConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccessConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccessConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccessConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccessConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccessConfigValidationError) ErrorName() string { return "AccessConfigValidationError" }

// Error satisfies the builtin error interface
func (e AccessConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccessConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccessConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccessConfigValidationError{}

// Validate checks the field values on GangedSystem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GangedSystem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GangedSystem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GangedSystemMultiError, or
// nil if none found.
func (m *GangedSystem) ValidateAll() error {
	return m.validate(true)
}

func (m *GangedSystem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for ApiKey

	// no validation rules for ApiSecret

	if len(errors) > 0 {
		return GangedSystemMultiError(errors)
	}

	return nil
}

// GangedSystemMultiError is an error wrapping multiple validation errors
// returned by GangedSystem.ValidateAll() if the designated constraints aren't met.
type GangedSystemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GangedSystemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GangedSystemMultiError) AllErrors() []error { return m }

// GangedSystemValidationError is the validation error returned by
// GangedSystem.Validate if the designated constraints aren't met.
type GangedSystemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GangedSystemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GangedSystemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GangedSystemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GangedSystemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GangedSystemValidationError) ErrorName() string { return "GangedSystemValidationError" }

// Error satisfies the builtin error interface
func (e GangedSystemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGangedSystem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GangedSystemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GangedSystemValidationError{}

// Validate checks the field values on CloudV01Config with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CloudV01Config) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloudV01Config with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CloudV01ConfigMultiError,
// or nil if none found.
func (m *CloudV01Config) ValidateAll() error {
	return m.validate(true)
}

func (m *CloudV01Config) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for ConnectModel

	// no validation rules for Addr

	// no validation rules for ApiKey

	if len(errors) > 0 {
		return CloudV01ConfigMultiError(errors)
	}

	return nil
}

// CloudV01ConfigMultiError is an error wrapping multiple validation errors
// returned by CloudV01Config.ValidateAll() if the designated constraints
// aren't met.
type CloudV01ConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloudV01ConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloudV01ConfigMultiError) AllErrors() []error { return m }

// CloudV01ConfigValidationError is the validation error returned by
// CloudV01Config.Validate if the designated constraints aren't met.
type CloudV01ConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloudV01ConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloudV01ConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloudV01ConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloudV01ConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloudV01ConfigValidationError) ErrorName() string { return "CloudV01ConfigValidationError" }

// Error satisfies the builtin error interface
func (e CloudV01ConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloudV01Config.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloudV01ConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloudV01ConfigValidationError{}

// Validate checks the field values on CloudModelConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CloudModelConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloudModelConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloudModelConfigMultiError, or nil if none found.
func (m *CloudModelConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CloudModelConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for ConnectModel

	// no validation rules for Addr

	// no validation rules for ApiKey

	// no validation rules for AuthKey

	if len(errors) > 0 {
		return CloudModelConfigMultiError(errors)
	}

	return nil
}

// CloudModelConfigMultiError is an error wrapping multiple validation errors
// returned by CloudModelConfig.ValidateAll() if the designated constraints
// aren't met.
type CloudModelConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloudModelConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloudModelConfigMultiError) AllErrors() []error { return m }

// CloudModelConfigValidationError is the validation error returned by
// CloudModelConfig.Validate if the designated constraints aren't met.
type CloudModelConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloudModelConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloudModelConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloudModelConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloudModelConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloudModelConfigValidationError) ErrorName() string { return "CloudModelConfigValidationError" }

// Error satisfies the builtin error interface
func (e CloudModelConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloudModelConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloudModelConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloudModelConfigValidationError{}

// Validate checks the field values on EngineLibConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EngineLibConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EngineLibConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EngineLibConfigMultiError, or nil if none found.
func (m *EngineLibConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *EngineLibConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for Interval

	if len(errors) > 0 {
		return EngineLibConfigMultiError(errors)
	}

	return nil
}

// EngineLibConfigMultiError is an error wrapping multiple validation errors
// returned by EngineLibConfig.ValidateAll() if the designated constraints
// aren't met.
type EngineLibConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EngineLibConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EngineLibConfigMultiError) AllErrors() []error { return m }

// EngineLibConfigValidationError is the validation error returned by
// EngineLibConfig.Validate if the designated constraints aren't met.
type EngineLibConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EngineLibConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EngineLibConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EngineLibConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EngineLibConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EngineLibConfigValidationError) ErrorName() string { return "EngineLibConfigValidationError" }

// Error satisfies the builtin error interface
func (e EngineLibConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEngineLibConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EngineLibConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EngineLibConfigValidationError{}

// Validate checks the field values on CommonConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CommonConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CommonConfigMultiError, or
// nil if none found.
func (m *CommonConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicSecurity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonConfigValidationError{
					field:  "BasicSecurity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonConfigValidationError{
					field:  "BasicSecurity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicSecurity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonConfigValidationError{
				field:  "BasicSecurity",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CommonConfigMultiError(errors)
	}

	return nil
}

// CommonConfigMultiError is an error wrapping multiple validation errors
// returned by CommonConfig.ValidateAll() if the designated constraints aren't met.
type CommonConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonConfigMultiError) AllErrors() []error { return m }

// CommonConfigValidationError is the validation error returned by
// CommonConfig.Validate if the designated constraints aren't met.
type CommonConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonConfigValidationError) ErrorName() string { return "CommonConfigValidationError" }

// Error satisfies the builtin error interface
func (e CommonConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonConfigValidationError{}

// Validate checks the field values on BasicSecurityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BasicSecurityConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BasicSecurityConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BasicSecurityConfigMultiError, or nil if none found.
func (m *BasicSecurityConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BasicSecurityConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAgent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BasicSecurityConfigValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BasicSecurityConfigValidationError{
					field:  "Agent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BasicSecurityConfigValidationError{
				field:  "Agent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetServer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BasicSecurityConfigValidationError{
					field:  "Server",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BasicSecurityConfigValidationError{
					field:  "Server",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BasicSecurityConfigValidationError{
				field:  "Server",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BasicSecurityConfigMultiError(errors)
	}

	return nil
}

// BasicSecurityConfigMultiError is an error wrapping multiple validation
// errors returned by BasicSecurityConfig.ValidateAll() if the designated
// constraints aren't met.
type BasicSecurityConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BasicSecurityConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BasicSecurityConfigMultiError) AllErrors() []error { return m }

// BasicSecurityConfigValidationError is the validation error returned by
// BasicSecurityConfig.Validate if the designated constraints aren't met.
type BasicSecurityConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BasicSecurityConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BasicSecurityConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BasicSecurityConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BasicSecurityConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BasicSecurityConfigValidationError) ErrorName() string {
	return "BasicSecurityConfigValidationError"
}

// Error satisfies the builtin error interface
func (e BasicSecurityConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBasicSecurityConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BasicSecurityConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BasicSecurityConfigValidationError{}

// Validate checks the field values on AgentConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentConfigMultiError, or
// nil if none found.
func (m *AgentConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	if len(errors) > 0 {
		return AgentConfigMultiError(errors)
	}

	return nil
}

// AgentConfigMultiError is an error wrapping multiple validation errors
// returned by AgentConfig.ValidateAll() if the designated constraints aren't met.
type AgentConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentConfigMultiError) AllErrors() []error { return m }

// AgentConfigValidationError is the validation error returned by
// AgentConfig.Validate if the designated constraints aren't met.
type AgentConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentConfigValidationError) ErrorName() string { return "AgentConfigValidationError" }

// Error satisfies the builtin error interface
func (e AgentConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentConfigValidationError{}

// Validate checks the field values on ServerConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServerConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServerConfigMultiError, or
// nil if none found.
func (m *ServerConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoginLock

	// no validation rules for LogoutTimeout

	if len(errors) > 0 {
		return ServerConfigMultiError(errors)
	}

	return nil
}

// ServerConfigMultiError is an error wrapping multiple validation errors
// returned by ServerConfig.ValidateAll() if the designated constraints aren't met.
type ServerConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerConfigMultiError) AllErrors() []error { return m }

// ServerConfigValidationError is the validation error returned by
// ServerConfig.Validate if the designated constraints aren't met.
type ServerConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerConfigValidationError) ErrorName() string { return "ServerConfigValidationError" }

// Error satisfies the builtin error interface
func (e ServerConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerConfigValidationError{}

// Validate checks the field values on FileThreatConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileThreatConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileThreatConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileThreatConfigMultiError, or nil if none found.
func (m *FileThreatConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *FileThreatConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDetectEngine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatConfigValidationError{
					field:  "DetectEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatConfigValidationError{
					field:  "DetectEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectEngine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatConfigValidationError{
				field:  "DetectEngine",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileThreatConfigMultiError(errors)
	}

	return nil
}

// FileThreatConfigMultiError is an error wrapping multiple validation errors
// returned by FileThreatConfig.ValidateAll() if the designated constraints
// aren't met.
type FileThreatConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileThreatConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileThreatConfigMultiError) AllErrors() []error { return m }

// FileThreatConfigValidationError is the validation error returned by
// FileThreatConfig.Validate if the designated constraints aren't met.
type FileThreatConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileThreatConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileThreatConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileThreatConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileThreatConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileThreatConfigValidationError) ErrorName() string { return "FileThreatConfigValidationError" }

// Error satisfies the builtin error interface
func (e FileThreatConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileThreatConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileThreatConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileThreatConfigValidationError{}

// Validate checks the field values on DetectEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectEngineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectEngineConfigMultiError, or nil if none found.
func (m *DetectEngineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectEngineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAiEngine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "AiEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "AiEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAiEngine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectEngineConfigValidationError{
				field:  "AiEngine",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLocalVirusEngine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "LocalVirusEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "LocalVirusEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocalVirusEngine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectEngineConfigValidationError{
				field:  "LocalVirusEngine",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSandboxEngine()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "SandboxEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetectEngineConfigValidationError{
					field:  "SandboxEngine",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSandboxEngine()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetectEngineConfigValidationError{
				field:  "SandboxEngine",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DetectEngineConfigMultiError(errors)
	}

	return nil
}

// DetectEngineConfigMultiError is an error wrapping multiple validation errors
// returned by DetectEngineConfig.ValidateAll() if the designated constraints
// aren't met.
type DetectEngineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectEngineConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectEngineConfigMultiError) AllErrors() []error { return m }

// DetectEngineConfigValidationError is the validation error returned by
// DetectEngineConfig.Validate if the designated constraints aren't met.
type DetectEngineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectEngineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectEngineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectEngineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectEngineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectEngineConfigValidationError) ErrorName() string {
	return "DetectEngineConfigValidationError"
}

// Error satisfies the builtin error interface
func (e DetectEngineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectEngineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectEngineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectEngineConfigValidationError{}

// Validate checks the field values on AIEngineConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AIEngineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AIEngineConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AIEngineConfigMultiError,
// or nil if none found.
func (m *AIEngineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AIEngineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocEnable

	// no validation rules for PeEnable

	// no validation rules for Sensitive

	if len(errors) > 0 {
		return AIEngineConfigMultiError(errors)
	}

	return nil
}

// AIEngineConfigMultiError is an error wrapping multiple validation errors
// returned by AIEngineConfig.ValidateAll() if the designated constraints
// aren't met.
type AIEngineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AIEngineConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AIEngineConfigMultiError) AllErrors() []error { return m }

// AIEngineConfigValidationError is the validation error returned by
// AIEngineConfig.Validate if the designated constraints aren't met.
type AIEngineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AIEngineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AIEngineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AIEngineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AIEngineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AIEngineConfigValidationError) ErrorName() string { return "AIEngineConfigValidationError" }

// Error satisfies the builtin error interface
func (e AIEngineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAIEngineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AIEngineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AIEngineConfigValidationError{}

// Validate checks the field values on LocalVirusEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LocalVirusEngineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LocalVirusEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LocalVirusEngineConfigMultiError, or nil if none found.
func (m *LocalVirusEngineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LocalVirusEngineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	if len(errors) > 0 {
		return LocalVirusEngineConfigMultiError(errors)
	}

	return nil
}

// LocalVirusEngineConfigMultiError is an error wrapping multiple validation
// errors returned by LocalVirusEngineConfig.ValidateAll() if the designated
// constraints aren't met.
type LocalVirusEngineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LocalVirusEngineConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LocalVirusEngineConfigMultiError) AllErrors() []error { return m }

// LocalVirusEngineConfigValidationError is the validation error returned by
// LocalVirusEngineConfig.Validate if the designated constraints aren't met.
type LocalVirusEngineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LocalVirusEngineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LocalVirusEngineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LocalVirusEngineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LocalVirusEngineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LocalVirusEngineConfigValidationError) ErrorName() string {
	return "LocalVirusEngineConfigValidationError"
}

// Error satisfies the builtin error interface
func (e LocalVirusEngineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLocalVirusEngineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LocalVirusEngineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LocalVirusEngineConfigValidationError{}

// Validate checks the field values on SandboxEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SandboxEngineConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SandboxEngineConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SandboxEngineConfigMultiError, or nil if none found.
func (m *SandboxEngineConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *SandboxEngineConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for Sensitive

	if len(errors) > 0 {
		return SandboxEngineConfigMultiError(errors)
	}

	return nil
}

// SandboxEngineConfigMultiError is an error wrapping multiple validation
// errors returned by SandboxEngineConfig.ValidateAll() if the designated
// constraints aren't met.
type SandboxEngineConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SandboxEngineConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SandboxEngineConfigMultiError) AllErrors() []error { return m }

// SandboxEngineConfigValidationError is the validation error returned by
// SandboxEngineConfig.Validate if the designated constraints aren't met.
type SandboxEngineConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SandboxEngineConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SandboxEngineConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SandboxEngineConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SandboxEngineConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SandboxEngineConfigValidationError) ErrorName() string {
	return "SandboxEngineConfigValidationError"
}

// Error satisfies the builtin error interface
func (e SandboxEngineConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSandboxEngineConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SandboxEngineConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SandboxEngineConfigValidationError{}

// Validate checks the field values on LogArchiveConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LogArchiveConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogArchiveConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogArchiveConfigMultiError, or nil if none found.
func (m *LogArchiveConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *LogArchiveConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStorage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogArchiveConfigValidationError{
					field:  "Storage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogArchiveConfigValidationError{
					field:  "Storage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStorage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogArchiveConfigValidationError{
				field:  "Storage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogArchiveConfigMultiError(errors)
	}

	return nil
}

// LogArchiveConfigMultiError is an error wrapping multiple validation errors
// returned by LogArchiveConfig.ValidateAll() if the designated constraints
// aren't met.
type LogArchiveConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogArchiveConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogArchiveConfigMultiError) AllErrors() []error { return m }

// LogArchiveConfigValidationError is the validation error returned by
// LogArchiveConfig.Validate if the designated constraints aren't met.
type LogArchiveConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogArchiveConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogArchiveConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogArchiveConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogArchiveConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogArchiveConfigValidationError) ErrorName() string { return "LogArchiveConfigValidationError" }

// Error satisfies the builtin error interface
func (e LogArchiveConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogArchiveConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogArchiveConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogArchiveConfigValidationError{}

// Validate checks the field values on StorageConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StorageConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StorageConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StorageConfigMultiError, or
// nil if none found.
func (m *StorageConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StorageConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	if len(errors) > 0 {
		return StorageConfigMultiError(errors)
	}

	return nil
}

// StorageConfigMultiError is an error wrapping multiple validation errors
// returned by StorageConfig.ValidateAll() if the designated constraints
// aren't met.
type StorageConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StorageConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StorageConfigMultiError) AllErrors() []error { return m }

// StorageConfigValidationError is the validation error returned by
// StorageConfig.Validate if the designated constraints aren't met.
type StorageConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StorageConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StorageConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StorageConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StorageConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StorageConfigValidationError) ErrorName() string { return "StorageConfigValidationError" }

// Error satisfies the builtin error interface
func (e StorageConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStorageConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StorageConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StorageConfigValidationError{}
