// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: nasus/nasus.proto

package nasus

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetConfReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConfReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetConfReqMultiError, or
// nil if none found.
func (m *GetConfReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetConfReqMultiError(errors)
	}

	return nil
}

// GetConfReqMultiError is an error wrapping multiple validation errors
// returned by GetConfReq.ValidateAll() if the designated constraints aren't met.
type GetConfReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfReqMultiError) AllErrors() []error { return m }

// GetConfReqValidationError is the validation error returned by
// GetConfReq.Validate if the designated constraints aren't met.
type GetConfReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfReqValidationError) ErrorName() string { return "GetConfReqValidationError" }

// Error satisfies the builtin error interface
func (e GetConfReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfReqValidationError{}

// Validate checks the field values on GetConfResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetConfResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetConfResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetConfRespMultiError, or
// nil if none found.
func (m *GetConfResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetConfResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return GetConfRespMultiError(errors)
	}

	return nil
}

// GetConfRespMultiError is an error wrapping multiple validation errors
// returned by GetConfResp.ValidateAll() if the designated constraints aren't met.
type GetConfRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetConfRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetConfRespMultiError) AllErrors() []error { return m }

// GetConfRespValidationError is the validation error returned by
// GetConfResp.Validate if the designated constraints aren't met.
type GetConfRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetConfRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetConfRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetConfRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetConfRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetConfRespValidationError) ErrorName() string { return "GetConfRespValidationError" }

// Error satisfies the builtin error interface
func (e GetConfRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetConfResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetConfRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetConfRespValidationError{}

// Validate checks the field values on UpdateConfReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateConfReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateConfReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateConfReqMultiError, or
// nil if none found.
func (m *UpdateConfReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateConfReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return UpdateConfReqMultiError(errors)
	}

	return nil
}

// UpdateConfReqMultiError is an error wrapping multiple validation errors
// returned by UpdateConfReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateConfReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateConfReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateConfReqMultiError) AllErrors() []error { return m }

// UpdateConfReqValidationError is the validation error returned by
// UpdateConfReq.Validate if the designated constraints aren't met.
type UpdateConfReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateConfReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateConfReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateConfReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateConfReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateConfReqValidationError) ErrorName() string { return "UpdateConfReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdateConfReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateConfReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateConfReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateConfReqValidationError{}

// Validate checks the field values on UpdateConfResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateConfResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateConfResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateConfRespMultiError,
// or nil if none found.
func (m *UpdateConfResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateConfResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateConfRespMultiError(errors)
	}

	return nil
}

// UpdateConfRespMultiError is an error wrapping multiple validation errors
// returned by UpdateConfResp.ValidateAll() if the designated constraints
// aren't met.
type UpdateConfRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateConfRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateConfRespMultiError) AllErrors() []error { return m }

// UpdateConfRespValidationError is the validation error returned by
// UpdateConfResp.Validate if the designated constraints aren't met.
type UpdateConfRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateConfRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateConfRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateConfRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateConfRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateConfRespValidationError) ErrorName() string { return "UpdateConfRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdateConfRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateConfResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateConfRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateConfRespValidationError{}

// Validate checks the field values on AddCMReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddCMReqMultiError, or nil
// if none found.
func (m *AddCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Data

	if len(errors) > 0 {
		return AddCMReqMultiError(errors)
	}

	return nil
}

// AddCMReqMultiError is an error wrapping multiple validation errors returned
// by AddCMReq.ValidateAll() if the designated constraints aren't met.
type AddCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCMReqMultiError) AllErrors() []error { return m }

// AddCMReqValidationError is the validation error returned by
// AddCMReq.Validate if the designated constraints aren't met.
type AddCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCMReqValidationError) ErrorName() string { return "AddCMReqValidationError" }

// Error satisfies the builtin error interface
func (e AddCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCMReqValidationError{}

// Validate checks the field values on AddCMResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddCMRespMultiError, or nil
// if none found.
func (m *AddCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddCMRespMultiError(errors)
	}

	return nil
}

// AddCMRespMultiError is an error wrapping multiple validation errors returned
// by AddCMResp.ValidateAll() if the designated constraints aren't met.
type AddCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCMRespMultiError) AllErrors() []error { return m }

// AddCMRespValidationError is the validation error returned by
// AddCMResp.Validate if the designated constraints aren't met.
type AddCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCMRespValidationError) ErrorName() string { return "AddCMRespValidationError" }

// Error satisfies the builtin error interface
func (e AddCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCMRespValidationError{}

// Validate checks the field values on DelCMReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DelCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DelCMReqMultiError, or nil
// if none found.
func (m *DelCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DelCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Data

	if len(errors) > 0 {
		return DelCMReqMultiError(errors)
	}

	return nil
}

// DelCMReqMultiError is an error wrapping multiple validation errors returned
// by DelCMReq.ValidateAll() if the designated constraints aren't met.
type DelCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelCMReqMultiError) AllErrors() []error { return m }

// DelCMReqValidationError is the validation error returned by
// DelCMReq.Validate if the designated constraints aren't met.
type DelCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelCMReqValidationError) ErrorName() string { return "DelCMReqValidationError" }

// Error satisfies the builtin error interface
func (e DelCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelCMReqValidationError{}

// Validate checks the field values on DelCMResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DelCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DelCMRespMultiError, or nil
// if none found.
func (m *DelCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DelCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DelCMRespMultiError(errors)
	}

	return nil
}

// DelCMRespMultiError is an error wrapping multiple validation errors returned
// by DelCMResp.ValidateAll() if the designated constraints aren't met.
type DelCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelCMRespMultiError) AllErrors() []error { return m }

// DelCMRespValidationError is the validation error returned by
// DelCMResp.Validate if the designated constraints aren't met.
type DelCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelCMRespValidationError) ErrorName() string { return "DelCMRespValidationError" }

// Error satisfies the builtin error interface
func (e DelCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelCMRespValidationError{}

// Validate checks the field values on UpdateCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateCMReqMultiError, or
// nil if none found.
func (m *UpdateCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Data

	if len(errors) > 0 {
		return UpdateCMReqMultiError(errors)
	}

	return nil
}

// UpdateCMReqMultiError is an error wrapping multiple validation errors
// returned by UpdateCMReq.ValidateAll() if the designated constraints aren't met.
type UpdateCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCMReqMultiError) AllErrors() []error { return m }

// UpdateCMReqValidationError is the validation error returned by
// UpdateCMReq.Validate if the designated constraints aren't met.
type UpdateCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCMReqValidationError) ErrorName() string { return "UpdateCMReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdateCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCMReqValidationError{}

// Validate checks the field values on UpdateCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateCMResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateCMRespMultiError, or
// nil if none found.
func (m *UpdateCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateCMRespMultiError(errors)
	}

	return nil
}

// UpdateCMRespMultiError is an error wrapping multiple validation errors
// returned by UpdateCMResp.ValidateAll() if the designated constraints aren't met.
type UpdateCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateCMRespMultiError) AllErrors() []error { return m }

// UpdateCMRespValidationError is the validation error returned by
// UpdateCMResp.Validate if the designated constraints aren't met.
type UpdateCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateCMRespValidationError) ErrorName() string { return "UpdateCMRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdateCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateCMRespValidationError{}

// Validate checks the field values on QueryCMReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueryCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueryCMReqMultiError, or
// nil if none found.
func (m *QueryCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	if len(errors) > 0 {
		return QueryCMReqMultiError(errors)
	}

	return nil
}

// QueryCMReqMultiError is an error wrapping multiple validation errors
// returned by QueryCMReq.ValidateAll() if the designated constraints aren't met.
type QueryCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryCMReqMultiError) AllErrors() []error { return m }

// QueryCMReqValidationError is the validation error returned by
// QueryCMReq.Validate if the designated constraints aren't met.
type QueryCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryCMReqValidationError) ErrorName() string { return "QueryCMReqValidationError" }

// Error satisfies the builtin error interface
func (e QueryCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryCMReqValidationError{}

// Validate checks the field values on QueryCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueryCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueryCMRespMultiError, or
// nil if none found.
func (m *QueryCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return QueryCMRespMultiError(errors)
	}

	return nil
}

// QueryCMRespMultiError is an error wrapping multiple validation errors
// returned by QueryCMResp.ValidateAll() if the designated constraints aren't met.
type QueryCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryCMRespMultiError) AllErrors() []error { return m }

// QueryCMRespValidationError is the validation error returned by
// QueryCMResp.Validate if the designated constraints aren't met.
type QueryCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryCMRespValidationError) ErrorName() string { return "QueryCMRespValidationError" }

// Error satisfies the builtin error interface
func (e QueryCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryCMRespValidationError{}

// Validate checks the field values on ListCMReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListCMReqMultiError, or nil
// if none found.
func (m *ListCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Limit

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListCMReqMultiError(errors)
	}

	return nil
}

// ListCMReqMultiError is an error wrapping multiple validation errors returned
// by ListCMReq.ValidateAll() if the designated constraints aren't met.
type ListCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCMReqMultiError) AllErrors() []error { return m }

// ListCMReqValidationError is the validation error returned by
// ListCMReq.Validate if the designated constraints aren't met.
type ListCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCMReqValidationError) ErrorName() string { return "ListCMReqValidationError" }

// Error satisfies the builtin error interface
func (e ListCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCMReqValidationError{}

// Validate checks the field values on ListCMResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListCMRespMultiError, or
// nil if none found.
func (m *ListCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCMRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCMRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCMRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListCMRespMultiError(errors)
	}

	return nil
}

// ListCMRespMultiError is an error wrapping multiple validation errors
// returned by ListCMResp.ValidateAll() if the designated constraints aren't met.
type ListCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCMRespMultiError) AllErrors() []error { return m }

// ListCMRespValidationError is the validation error returned by
// ListCMResp.Validate if the designated constraints aren't met.
type ListCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCMRespValidationError) ErrorName() string { return "ListCMRespValidationError" }

// Error satisfies the builtin error interface
func (e ListCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCMRespValidationError{}

// Validate checks the field values on CMItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CMItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CMItem with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CMItemMultiError, or nil if none found.
func (m *CMItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CMItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Data

	if len(errors) > 0 {
		return CMItemMultiError(errors)
	}

	return nil
}

// CMItemMultiError is an error wrapping multiple validation errors returned by
// CMItem.ValidateAll() if the designated constraints aren't met.
type CMItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CMItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CMItemMultiError) AllErrors() []error { return m }

// CMItemValidationError is the validation error returned by CMItem.Validate if
// the designated constraints aren't met.
type CMItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CMItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CMItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CMItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CMItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CMItemValidationError) ErrorName() string { return "CMItemValidationError" }

// Error satisfies the builtin error interface
func (e CMItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCMItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CMItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CMItemValidationError{}

// Validate checks the field values on WatchCMReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchCMReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchCMReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchCMReqMultiError, or
// nil if none found.
func (m *WatchCMReq) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchCMReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	if len(errors) > 0 {
		return WatchCMReqMultiError(errors)
	}

	return nil
}

// WatchCMReqMultiError is an error wrapping multiple validation errors
// returned by WatchCMReq.ValidateAll() if the designated constraints aren't met.
type WatchCMReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchCMReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchCMReqMultiError) AllErrors() []error { return m }

// WatchCMReqValidationError is the validation error returned by
// WatchCMReq.Validate if the designated constraints aren't met.
type WatchCMReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchCMReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchCMReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchCMReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchCMReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchCMReqValidationError) ErrorName() string { return "WatchCMReqValidationError" }

// Error satisfies the builtin error interface
func (e WatchCMReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchCMReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchCMReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchCMReqValidationError{}

// Validate checks the field values on WatchCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchCMResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchCMResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchCMRespMultiError, or
// nil if none found.
func (m *WatchCMResp) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchCMResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for FileName

	// no validation rules for Data

	if len(errors) > 0 {
		return WatchCMRespMultiError(errors)
	}

	return nil
}

// WatchCMRespMultiError is an error wrapping multiple validation errors
// returned by WatchCMResp.ValidateAll() if the designated constraints aren't met.
type WatchCMRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchCMRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchCMRespMultiError) AllErrors() []error { return m }

// WatchCMRespValidationError is the validation error returned by
// WatchCMResp.Validate if the designated constraints aren't met.
type WatchCMRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchCMRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchCMRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchCMRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchCMRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchCMRespValidationError) ErrorName() string { return "WatchCMRespValidationError" }

// Error satisfies the builtin error interface
func (e WatchCMRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchCMResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchCMRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchCMRespValidationError{}

// Validate checks the field values on ListNodeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListNodeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListNodeReqMultiError, or
// nil if none found.
func (m *ListNodeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for LabelSelector

	// no validation rules for Limit

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListNodeReqMultiError(errors)
	}

	return nil
}

// ListNodeReqMultiError is an error wrapping multiple validation errors
// returned by ListNodeReq.ValidateAll() if the designated constraints aren't met.
type ListNodeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodeReqMultiError) AllErrors() []error { return m }

// ListNodeReqValidationError is the validation error returned by
// ListNodeReq.Validate if the designated constraints aren't met.
type ListNodeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodeReqValidationError) ErrorName() string { return "ListNodeReqValidationError" }

// Error satisfies the builtin error interface
func (e ListNodeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodeReqValidationError{}

// Validate checks the field values on ListNodeResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListNodeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNodeResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListNodeRespMultiError, or
// nil if none found.
func (m *ListNodeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNodeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNodeRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNodeRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNodeRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListNodeRespMultiError(errors)
	}

	return nil
}

// ListNodeRespMultiError is an error wrapping multiple validation errors
// returned by ListNodeResp.ValidateAll() if the designated constraints aren't met.
type ListNodeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNodeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNodeRespMultiError) AllErrors() []error { return m }

// ListNodeRespValidationError is the validation error returned by
// ListNodeResp.Validate if the designated constraints aren't met.
type ListNodeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNodeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNodeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNodeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNodeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNodeRespValidationError) ErrorName() string { return "ListNodeRespValidationError" }

// Error satisfies the builtin error interface
func (e ListNodeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNodeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNodeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNodeRespValidationError{}

// Validate checks the field values on NodeItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NodeItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NodeItemMultiError, or nil
// if none found.
func (m *NodeItem) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Ip

	// no validation rules for Hostname

	// no validation rules for Os

	// no validation rules for OsVersion

	// no validation rules for KernelVersion

	// no validation rules for KernelRelease

	// no validation rules for KernelArch

	if len(errors) > 0 {
		return NodeItemMultiError(errors)
	}

	return nil
}

// NodeItemMultiError is an error wrapping multiple validation errors returned
// by NodeItem.ValidateAll() if the designated constraints aren't met.
type NodeItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeItemMultiError) AllErrors() []error { return m }

// NodeItemValidationError is the validation error returned by
// NodeItem.Validate if the designated constraints aren't met.
type NodeItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeItemValidationError) ErrorName() string { return "NodeItemValidationError" }

// Error satisfies the builtin error interface
func (e NodeItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeItemValidationError{}

// Validate checks the field values on WatchNodeReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchNodeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchNodeReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchNodeReqMultiError, or
// nil if none found.
func (m *WatchNodeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchNodeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WatchNodeReqMultiError(errors)
	}

	return nil
}

// WatchNodeReqMultiError is an error wrapping multiple validation errors
// returned by WatchNodeReq.ValidateAll() if the designated constraints aren't met.
type WatchNodeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchNodeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchNodeReqMultiError) AllErrors() []error { return m }

// WatchNodeReqValidationError is the validation error returned by
// WatchNodeReq.Validate if the designated constraints aren't met.
type WatchNodeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchNodeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchNodeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchNodeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchNodeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchNodeReqValidationError) ErrorName() string { return "WatchNodeReqValidationError" }

// Error satisfies the builtin error interface
func (e WatchNodeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchNodeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchNodeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchNodeReqValidationError{}

// Validate checks the field values on WatchNodeResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchNodeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchNodeResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchNodeRespMultiError, or
// nil if none found.
func (m *WatchNodeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchNodeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WatchNodeRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WatchNodeRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WatchNodeRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WatchNodeRespMultiError(errors)
	}

	return nil
}

// WatchNodeRespMultiError is an error wrapping multiple validation errors
// returned by WatchNodeResp.ValidateAll() if the designated constraints
// aren't met.
type WatchNodeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchNodeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchNodeRespMultiError) AllErrors() []error { return m }

// WatchNodeRespValidationError is the validation error returned by
// WatchNodeResp.Validate if the designated constraints aren't met.
type WatchNodeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchNodeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchNodeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchNodeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchNodeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchNodeRespValidationError) ErrorName() string { return "WatchNodeRespValidationError" }

// Error satisfies the builtin error interface
func (e WatchNodeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchNodeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchNodeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchNodeRespValidationError{}

// Validate checks the field values on GetEndpointsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetEndpointsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEndpointsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEndpointsReqMultiError, or nil if none found.
func (m *GetEndpointsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEndpointsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for ServiceName

	if len(errors) > 0 {
		return GetEndpointsReqMultiError(errors)
	}

	return nil
}

// GetEndpointsReqMultiError is an error wrapping multiple validation errors
// returned by GetEndpointsReq.ValidateAll() if the designated constraints
// aren't met.
type GetEndpointsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEndpointsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEndpointsReqMultiError) AllErrors() []error { return m }

// GetEndpointsReqValidationError is the validation error returned by
// GetEndpointsReq.Validate if the designated constraints aren't met.
type GetEndpointsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEndpointsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEndpointsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEndpointsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEndpointsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEndpointsReqValidationError) ErrorName() string { return "GetEndpointsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetEndpointsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEndpointsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEndpointsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEndpointsReqValidationError{}

// Validate checks the field values on GetEndpointsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetEndpointsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEndpointsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEndpointsRespMultiError, or nil if none found.
func (m *GetEndpointsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEndpointsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetEndpointsRespMultiError(errors)
	}

	return nil
}

// GetEndpointsRespMultiError is an error wrapping multiple validation errors
// returned by GetEndpointsResp.ValidateAll() if the designated constraints
// aren't met.
type GetEndpointsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEndpointsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEndpointsRespMultiError) AllErrors() []error { return m }

// GetEndpointsRespValidationError is the validation error returned by
// GetEndpointsResp.Validate if the designated constraints aren't met.
type GetEndpointsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEndpointsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEndpointsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEndpointsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEndpointsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEndpointsRespValidationError) ErrorName() string { return "GetEndpointsRespValidationError" }

// Error satisfies the builtin error interface
func (e GetEndpointsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEndpointsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEndpointsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEndpointsRespValidationError{}

// Validate checks the field values on ListEndpointsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListEndpointsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEndpointsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEndpointsReqMultiError, or nil if none found.
func (m *ListEndpointsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEndpointsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for ServiceName

	// no validation rules for Limit

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListEndpointsReqMultiError(errors)
	}

	return nil
}

// ListEndpointsReqMultiError is an error wrapping multiple validation errors
// returned by ListEndpointsReq.ValidateAll() if the designated constraints
// aren't met.
type ListEndpointsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEndpointsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEndpointsReqMultiError) AllErrors() []error { return m }

// ListEndpointsReqValidationError is the validation error returned by
// ListEndpointsReq.Validate if the designated constraints aren't met.
type ListEndpointsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEndpointsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEndpointsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEndpointsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEndpointsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEndpointsReqValidationError) ErrorName() string { return "ListEndpointsReqValidationError" }

// Error satisfies the builtin error interface
func (e ListEndpointsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEndpointsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEndpointsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEndpointsReqValidationError{}

// Validate checks the field values on ListEndpointsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListEndpointsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEndpointsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEndpointsRespMultiError, or nil if none found.
func (m *ListEndpointsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEndpointsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListEndpointsRespMultiError(errors)
	}

	return nil
}

// ListEndpointsRespMultiError is an error wrapping multiple validation errors
// returned by ListEndpointsResp.ValidateAll() if the designated constraints
// aren't met.
type ListEndpointsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEndpointsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEndpointsRespMultiError) AllErrors() []error { return m }

// ListEndpointsRespValidationError is the validation error returned by
// ListEndpointsResp.Validate if the designated constraints aren't met.
type ListEndpointsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEndpointsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEndpointsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEndpointsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEndpointsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEndpointsRespValidationError) ErrorName() string {
	return "ListEndpointsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListEndpointsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEndpointsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEndpointsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEndpointsRespValidationError{}

// Validate checks the field values on WatchEndpointsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WatchEndpointsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchEndpointsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchEndpointsReqMultiError, or nil if none found.
func (m *WatchEndpointsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchEndpointsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for ServiceName

	if len(errors) > 0 {
		return WatchEndpointsReqMultiError(errors)
	}

	return nil
}

// WatchEndpointsReqMultiError is an error wrapping multiple validation errors
// returned by WatchEndpointsReq.ValidateAll() if the designated constraints
// aren't met.
type WatchEndpointsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchEndpointsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchEndpointsReqMultiError) AllErrors() []error { return m }

// WatchEndpointsReqValidationError is the validation error returned by
// WatchEndpointsReq.Validate if the designated constraints aren't met.
type WatchEndpointsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchEndpointsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchEndpointsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchEndpointsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchEndpointsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchEndpointsReqValidationError) ErrorName() string {
	return "WatchEndpointsReqValidationError"
}

// Error satisfies the builtin error interface
func (e WatchEndpointsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchEndpointsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchEndpointsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchEndpointsReqValidationError{}

// Validate checks the field values on WatchEndpointsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WatchEndpointsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchEndpointsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WatchEndpointsRespMultiError, or nil if none found.
func (m *WatchEndpointsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchEndpointsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WatchEndpointsRespMultiError(errors)
	}

	return nil
}

// WatchEndpointsRespMultiError is an error wrapping multiple validation errors
// returned by WatchEndpointsResp.ValidateAll() if the designated constraints
// aren't met.
type WatchEndpointsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchEndpointsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchEndpointsRespMultiError) AllErrors() []error { return m }

// WatchEndpointsRespValidationError is the validation error returned by
// WatchEndpointsResp.Validate if the designated constraints aren't met.
type WatchEndpointsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchEndpointsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchEndpointsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchEndpointsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchEndpointsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchEndpointsRespValidationError) ErrorName() string {
	return "WatchEndpointsRespValidationError"
}

// Error satisfies the builtin error interface
func (e WatchEndpointsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchEndpointsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchEndpointsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchEndpointsRespValidationError{}

// Validate checks the field values on PodItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PodItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PodItem with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PodItemMultiError, or nil if none found.
func (m *PodItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PodItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	// no validation rules for Ip

	// no validation rules for HostIp

	// no validation rules for Status

	// no validation rules for CreateTime

	// no validation rules for UpdateTime

	// no validation rules for NodeName

	if len(errors) > 0 {
		return PodItemMultiError(errors)
	}

	return nil
}

// PodItemMultiError is an error wrapping multiple validation errors returned
// by PodItem.ValidateAll() if the designated constraints aren't met.
type PodItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PodItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PodItemMultiError) AllErrors() []error { return m }

// PodItemValidationError is the validation error returned by PodItem.Validate
// if the designated constraints aren't met.
type PodItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PodItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PodItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PodItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PodItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PodItemValidationError) ErrorName() string { return "PodItemValidationError" }

// Error satisfies the builtin error interface
func (e PodItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPodItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PodItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PodItemValidationError{}

// Validate checks the field values on GetPodReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPodReqMultiError, or nil
// if none found.
func (m *GetPodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	if len(errors) > 0 {
		return GetPodReqMultiError(errors)
	}

	return nil
}

// GetPodReqMultiError is an error wrapping multiple validation errors returned
// by GetPodReq.ValidateAll() if the designated constraints aren't met.
type GetPodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPodReqMultiError) AllErrors() []error { return m }

// GetPodReqValidationError is the validation error returned by
// GetPodReq.Validate if the designated constraints aren't met.
type GetPodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPodReqValidationError) ErrorName() string { return "GetPodReqValidationError" }

// Error satisfies the builtin error interface
func (e GetPodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPodReqValidationError{}

// Validate checks the field values on GetPodResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPodRespMultiError, or
// nil if none found.
func (m *GetPodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPodRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPodRespValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPodRespValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPodRespMultiError(errors)
	}

	return nil
}

// GetPodRespMultiError is an error wrapping multiple validation errors
// returned by GetPodResp.ValidateAll() if the designated constraints aren't met.
type GetPodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPodRespMultiError) AllErrors() []error { return m }

// GetPodRespValidationError is the validation error returned by
// GetPodResp.Validate if the designated constraints aren't met.
type GetPodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPodRespValidationError) ErrorName() string { return "GetPodRespValidationError" }

// Error satisfies the builtin error interface
func (e GetPodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPodRespValidationError{}

// Validate checks the field values on ListPodReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPodReqMultiError, or
// nil if none found.
func (m *ListPodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for LabelSelector

	// no validation rules for Limit

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListPodReqMultiError(errors)
	}

	return nil
}

// ListPodReqMultiError is an error wrapping multiple validation errors
// returned by ListPodReq.ValidateAll() if the designated constraints aren't met.
type ListPodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPodReqMultiError) AllErrors() []error { return m }

// ListPodReqValidationError is the validation error returned by
// ListPodReq.Validate if the designated constraints aren't met.
type ListPodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPodReqValidationError) ErrorName() string { return "ListPodReqValidationError" }

// Error satisfies the builtin error interface
func (e ListPodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPodReqValidationError{}

// Validate checks the field values on ListPodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListPodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListPodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListPodRespMultiError, or
// nil if none found.
func (m *ListPodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListPodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListPodRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListPodRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListPodRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Continue

	if len(errors) > 0 {
		return ListPodRespMultiError(errors)
	}

	return nil
}

// ListPodRespMultiError is an error wrapping multiple validation errors
// returned by ListPodResp.ValidateAll() if the designated constraints aren't met.
type ListPodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListPodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListPodRespMultiError) AllErrors() []error { return m }

// ListPodRespValidationError is the validation error returned by
// ListPodResp.Validate if the designated constraints aren't met.
type ListPodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListPodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListPodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListPodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListPodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListPodRespValidationError) ErrorName() string { return "ListPodRespValidationError" }

// Error satisfies the builtin error interface
func (e ListPodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListPodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListPodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListPodRespValidationError{}

// Validate checks the field values on DelPodReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DelPodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelPodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DelPodReqMultiError, or nil
// if none found.
func (m *DelPodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DelPodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	if len(errors) > 0 {
		return DelPodReqMultiError(errors)
	}

	return nil
}

// DelPodReqMultiError is an error wrapping multiple validation errors returned
// by DelPodReq.ValidateAll() if the designated constraints aren't met.
type DelPodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelPodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelPodReqMultiError) AllErrors() []error { return m }

// DelPodReqValidationError is the validation error returned by
// DelPodReq.Validate if the designated constraints aren't met.
type DelPodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelPodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelPodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelPodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelPodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelPodReqValidationError) ErrorName() string { return "DelPodReqValidationError" }

// Error satisfies the builtin error interface
func (e DelPodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelPodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelPodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelPodReqValidationError{}

// Validate checks the field values on DelPodResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DelPodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DelPodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DelPodRespMultiError, or
// nil if none found.
func (m *DelPodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DelPodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DelPodRespMultiError(errors)
	}

	return nil
}

// DelPodRespMultiError is an error wrapping multiple validation errors
// returned by DelPodResp.ValidateAll() if the designated constraints aren't met.
type DelPodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DelPodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DelPodRespMultiError) AllErrors() []error { return m }

// DelPodRespValidationError is the validation error returned by
// DelPodResp.Validate if the designated constraints aren't met.
type DelPodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DelPodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DelPodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DelPodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DelPodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DelPodRespValidationError) ErrorName() string { return "DelPodRespValidationError" }

// Error satisfies the builtin error interface
func (e DelPodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDelPodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DelPodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DelPodRespValidationError{}

// Validate checks the field values on GetPodLogsReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPodLogsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPodLogsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPodLogsReqMultiError, or
// nil if none found.
func (m *GetPodLogsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPodLogsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	// no validation rules for ContainerName

	// no validation rules for TailLines

	// no validation rules for SinceSeconds

	if len(errors) > 0 {
		return GetPodLogsReqMultiError(errors)
	}

	return nil
}

// GetPodLogsReqMultiError is an error wrapping multiple validation errors
// returned by GetPodLogsReq.ValidateAll() if the designated constraints
// aren't met.
type GetPodLogsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPodLogsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPodLogsReqMultiError) AllErrors() []error { return m }

// GetPodLogsReqValidationError is the validation error returned by
// GetPodLogsReq.Validate if the designated constraints aren't met.
type GetPodLogsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPodLogsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPodLogsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPodLogsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPodLogsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPodLogsReqValidationError) ErrorName() string { return "GetPodLogsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetPodLogsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPodLogsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPodLogsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPodLogsReqValidationError{}

// Validate checks the field values on GetPodLogsResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPodLogsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPodLogsResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetPodLogsRespMultiError,
// or nil if none found.
func (m *GetPodLogsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPodLogsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return GetPodLogsRespMultiError(errors)
	}

	return nil
}

// GetPodLogsRespMultiError is an error wrapping multiple validation errors
// returned by GetPodLogsResp.ValidateAll() if the designated constraints
// aren't met.
type GetPodLogsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPodLogsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPodLogsRespMultiError) AllErrors() []error { return m }

// GetPodLogsRespValidationError is the validation error returned by
// GetPodLogsResp.Validate if the designated constraints aren't met.
type GetPodLogsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPodLogsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPodLogsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPodLogsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPodLogsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPodLogsRespValidationError) ErrorName() string { return "GetPodLogsRespValidationError" }

// Error satisfies the builtin error interface
func (e GetPodLogsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPodLogsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPodLogsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPodLogsRespValidationError{}

// Validate checks the field values on UpdatePodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdatePodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePodReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdatePodReqMultiError, or
// nil if none found.
func (m *UpdatePodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	// no validation rules for ContainerName

	// no validation rules for Image

	// no validation rules for RestartPolicy

	// no validation rules for Command

	// no validation rules for Args

	// no validation rules for Env

	if len(errors) > 0 {
		return UpdatePodReqMultiError(errors)
	}

	return nil
}

// UpdatePodReqMultiError is an error wrapping multiple validation errors
// returned by UpdatePodReq.ValidateAll() if the designated constraints aren't met.
type UpdatePodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePodReqMultiError) AllErrors() []error { return m }

// UpdatePodReqValidationError is the validation error returned by
// UpdatePodReq.Validate if the designated constraints aren't met.
type UpdatePodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePodReqValidationError) ErrorName() string { return "UpdatePodReqValidationError" }

// Error satisfies the builtin error interface
func (e UpdatePodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePodReqValidationError{}

// Validate checks the field values on UpdatePodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdatePodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdatePodResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdatePodRespMultiError, or
// nil if none found.
func (m *UpdatePodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdatePodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdatePodRespMultiError(errors)
	}

	return nil
}

// UpdatePodRespMultiError is an error wrapping multiple validation errors
// returned by UpdatePodResp.ValidateAll() if the designated constraints
// aren't met.
type UpdatePodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdatePodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdatePodRespMultiError) AllErrors() []error { return m }

// UpdatePodRespValidationError is the validation error returned by
// UpdatePodResp.Validate if the designated constraints aren't met.
type UpdatePodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdatePodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdatePodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdatePodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdatePodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdatePodRespValidationError) ErrorName() string { return "UpdatePodRespValidationError" }

// Error satisfies the builtin error interface
func (e UpdatePodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdatePodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdatePodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdatePodRespValidationError{}

// Validate checks the field values on WatchPodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchPodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchPodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchPodReqMultiError, or
// nil if none found.
func (m *WatchPodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchPodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	if len(errors) > 0 {
		return WatchPodReqMultiError(errors)
	}

	return nil
}

// WatchPodReqMultiError is an error wrapping multiple validation errors
// returned by WatchPodReq.ValidateAll() if the designated constraints aren't met.
type WatchPodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchPodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchPodReqMultiError) AllErrors() []error { return m }

// WatchPodReqValidationError is the validation error returned by
// WatchPodReq.Validate if the designated constraints aren't met.
type WatchPodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchPodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchPodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchPodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchPodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchPodReqValidationError) ErrorName() string { return "WatchPodReqValidationError" }

// Error satisfies the builtin error interface
func (e WatchPodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchPodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchPodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchPodReqValidationError{}

// Validate checks the field values on WatchPodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WatchPodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WatchPodResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WatchPodRespMultiError, or
// nil if none found.
func (m *WatchPodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *WatchPodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WatchPodRespMultiError(errors)
	}

	return nil
}

// WatchPodRespMultiError is an error wrapping multiple validation errors
// returned by WatchPodResp.ValidateAll() if the designated constraints aren't met.
type WatchPodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WatchPodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WatchPodRespMultiError) AllErrors() []error { return m }

// WatchPodRespValidationError is the validation error returned by
// WatchPodResp.Validate if the designated constraints aren't met.
type WatchPodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WatchPodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WatchPodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WatchPodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WatchPodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WatchPodRespValidationError) ErrorName() string { return "WatchPodRespValidationError" }

// Error satisfies the builtin error interface
func (e WatchPodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWatchPodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WatchPodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WatchPodRespValidationError{}

// Validate checks the field values on ScalePodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScalePodReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScalePodReq with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScalePodReqMultiError, or
// nil if none found.
func (m *ScalePodReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ScalePodReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for PodName

	// no validation rules for Replicas

	if len(errors) > 0 {
		return ScalePodReqMultiError(errors)
	}

	return nil
}

// ScalePodReqMultiError is an error wrapping multiple validation errors
// returned by ScalePodReq.ValidateAll() if the designated constraints aren't met.
type ScalePodReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScalePodReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScalePodReqMultiError) AllErrors() []error { return m }

// ScalePodReqValidationError is the validation error returned by
// ScalePodReq.Validate if the designated constraints aren't met.
type ScalePodReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScalePodReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScalePodReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScalePodReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScalePodReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScalePodReqValidationError) ErrorName() string { return "ScalePodReqValidationError" }

// Error satisfies the builtin error interface
func (e ScalePodReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScalePodReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScalePodReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScalePodReqValidationError{}

// Validate checks the field values on ScalePodResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScalePodResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScalePodResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScalePodRespMultiError, or
// nil if none found.
func (m *ScalePodResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ScalePodResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldReplicas

	if len(errors) > 0 {
		return ScalePodRespMultiError(errors)
	}

	return nil
}

// ScalePodRespMultiError is an error wrapping multiple validation errors
// returned by ScalePodResp.ValidateAll() if the designated constraints aren't met.
type ScalePodRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScalePodRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScalePodRespMultiError) AllErrors() []error { return m }

// ScalePodRespValidationError is the validation error returned by
// ScalePodResp.Validate if the designated constraints aren't met.
type ScalePodRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScalePodRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScalePodRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScalePodRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScalePodRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScalePodRespValidationError) ErrorName() string { return "ScalePodRespValidationError" }

// Error satisfies the builtin error interface
func (e ScalePodRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScalePodResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScalePodRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScalePodRespValidationError{}

// Validate checks the field values on ServiceItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ServiceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ServiceItemMultiError, or
// nil if none found.
func (m *ServiceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Port

	// no validation rules for NodePort

	if len(errors) > 0 {
		return ServiceItemMultiError(errors)
	}

	return nil
}

// ServiceItemMultiError is an error wrapping multiple validation errors
// returned by ServiceItem.ValidateAll() if the designated constraints aren't met.
type ServiceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceItemMultiError) AllErrors() []error { return m }

// ServiceItemValidationError is the validation error returned by
// ServiceItem.Validate if the designated constraints aren't met.
type ServiceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceItemValidationError) ErrorName() string { return "ServiceItemValidationError" }

// Error satisfies the builtin error interface
func (e ServiceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceItemValidationError{}

// Validate checks the field values on GetServiceReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetServiceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetServiceReqMultiError, or
// nil if none found.
func (m *GetServiceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NameSpace

	// no validation rules for ServiceName

	if len(errors) > 0 {
		return GetServiceReqMultiError(errors)
	}

	return nil
}

// GetServiceReqMultiError is an error wrapping multiple validation errors
// returned by GetServiceReq.ValidateAll() if the designated constraints
// aren't met.
type GetServiceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceReqMultiError) AllErrors() []error { return m }

// GetServiceReqValidationError is the validation error returned by
// GetServiceReq.Validate if the designated constraints aren't met.
type GetServiceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceReqValidationError) ErrorName() string { return "GetServiceReqValidationError" }

// Error satisfies the builtin error interface
func (e GetServiceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceReqValidationError{}

// Validate checks the field values on GetServiceResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetServiceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetServiceResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetServiceRespMultiError,
// or nil if none found.
func (m *GetServiceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetServiceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetServiceRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetServiceRespValidationError{
						field:  fmt.Sprintf("Data[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetServiceRespValidationError{
					field:  fmt.Sprintf("Data[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetServiceRespMultiError(errors)
	}

	return nil
}

// GetServiceRespMultiError is an error wrapping multiple validation errors
// returned by GetServiceResp.ValidateAll() if the designated constraints
// aren't met.
type GetServiceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetServiceRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetServiceRespMultiError) AllErrors() []error { return m }

// GetServiceRespValidationError is the validation error returned by
// GetServiceResp.Validate if the designated constraints aren't met.
type GetServiceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetServiceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetServiceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetServiceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetServiceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetServiceRespValidationError) ErrorName() string { return "GetServiceRespValidationError" }

// Error satisfies the builtin error interface
func (e GetServiceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetServiceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetServiceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetServiceRespValidationError{}
