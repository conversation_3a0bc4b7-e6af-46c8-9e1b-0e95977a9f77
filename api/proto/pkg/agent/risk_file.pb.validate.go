// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_file.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskFileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskFileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskFileInfoMultiError, or nil if none found.
func (m *MemProtectRiskFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskFileInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskFileInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskFileInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVirusScannerList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("VirusScannerList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("VirusScannerList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("VirusScannerList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFileDllHijackList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileDllHijackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileDllHijackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("FileDllHijackList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFileEnvHijackList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileEnvHijackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileEnvHijackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("FileEnvHijackList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPeWithoutSignatureList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("PeWithoutSignatureList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("PeWithoutSignatureList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("PeWithoutSignatureList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetPePackerNameList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("PePackerNameList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("PePackerNameList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("PePackerNameList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFileSensitivityList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileSensitivityList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("FileSensitivityList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("FileSensitivityList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLnkFileList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("LnkFileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("LnkFileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("LnkFileList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetClearLogList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("ClearLogList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskFileInfoValidationError{
						field:  fmt.Sprintf("ClearLogList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskFileInfoValidationError{
					field:  fmt.Sprintf("ClearLogList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskFileInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskFileInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskFileInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskFileInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskFileInfoValidationError is the validation error returned by
// MemProtectRiskFileInfo.Validate if the designated constraints aren't met.
type MemProtectRiskFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskFileInfoValidationError) ErrorName() string {
	return "MemProtectRiskFileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskFileInfoValidationError{}

// Validate checks the field values on PEWithoutSignature with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PEWithoutSignature) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PEWithoutSignature with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PEWithoutSignatureMultiError, or nil if none found.
func (m *PEWithoutSignature) ValidateAll() error {
	return m.validate(true)
}

func (m *PEWithoutSignature) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PEWithoutSignatureValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PEWithoutSignatureValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PEWithoutSignatureValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilePath

	// no validation rules for Sha256

	if len(errors) > 0 {
		return PEWithoutSignatureMultiError(errors)
	}

	return nil
}

// PEWithoutSignatureMultiError is an error wrapping multiple validation errors
// returned by PEWithoutSignature.ValidateAll() if the designated constraints
// aren't met.
type PEWithoutSignatureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PEWithoutSignatureMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PEWithoutSignatureMultiError) AllErrors() []error { return m }

// PEWithoutSignatureValidationError is the validation error returned by
// PEWithoutSignature.Validate if the designated constraints aren't met.
type PEWithoutSignatureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PEWithoutSignatureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PEWithoutSignatureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PEWithoutSignatureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PEWithoutSignatureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PEWithoutSignatureValidationError) ErrorName() string {
	return "PEWithoutSignatureValidationError"
}

// Error satisfies the builtin error interface
func (e PEWithoutSignatureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPEWithoutSignature.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PEWithoutSignatureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PEWithoutSignatureValidationError{}

// Validate checks the field values on PEPackerName with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PEPackerName) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PEPackerName with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PEPackerNameMultiError, or
// nil if none found.
func (m *PEPackerName) ValidateAll() error {
	return m.validate(true)
}

func (m *PEPackerName) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PEPackerNameValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PEPackerNameValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PEPackerNameValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilePath

	// no validation rules for PackerName

	// no validation rules for Sha256

	if len(errors) > 0 {
		return PEPackerNameMultiError(errors)
	}

	return nil
}

// PEPackerNameMultiError is an error wrapping multiple validation errors
// returned by PEPackerName.ValidateAll() if the designated constraints aren't met.
type PEPackerNameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PEPackerNameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PEPackerNameMultiError) AllErrors() []error { return m }

// PEPackerNameValidationError is the validation error returned by
// PEPackerName.Validate if the designated constraints aren't met.
type PEPackerNameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PEPackerNameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PEPackerNameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PEPackerNameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PEPackerNameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PEPackerNameValidationError) ErrorName() string { return "PEPackerNameValidationError" }

// Error satisfies the builtin error interface
func (e PEPackerNameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPEPackerName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PEPackerNameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PEPackerNameValidationError{}

// Validate checks the field values on VirusCheckInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VirusCheckInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusCheckInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VirusCheckInfoMultiError,
// or nil if none found.
func (m *VirusCheckInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusCheckInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirusCheckInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirusCheckInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirusCheckInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FilePath

	// no validation rules for VirusName

	// no validation rules for Sha256

	if len(errors) > 0 {
		return VirusCheckInfoMultiError(errors)
	}

	return nil
}

// VirusCheckInfoMultiError is an error wrapping multiple validation errors
// returned by VirusCheckInfo.ValidateAll() if the designated constraints
// aren't met.
type VirusCheckInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusCheckInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusCheckInfoMultiError) AllErrors() []error { return m }

// VirusCheckInfoValidationError is the validation error returned by
// VirusCheckInfo.Validate if the designated constraints aren't met.
type VirusCheckInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusCheckInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusCheckInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusCheckInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusCheckInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusCheckInfoValidationError) ErrorName() string { return "VirusCheckInfoValidationError" }

// Error satisfies the builtin error interface
func (e VirusCheckInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusCheckInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusCheckInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusCheckInfoValidationError{}

// Validate checks the field values on FunName with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FunName) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FunName with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FunNameMultiError, or nil if none found.
func (m *FunName) ValidateAll() error {
	return m.validate(true)
}

func (m *FunName) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if len(errors) > 0 {
		return FunNameMultiError(errors)
	}

	return nil
}

// FunNameMultiError is an error wrapping multiple validation errors returned
// by FunName.ValidateAll() if the designated constraints aren't met.
type FunNameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FunNameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FunNameMultiError) AllErrors() []error { return m }

// FunNameValidationError is the validation error returned by FunName.Validate
// if the designated constraints aren't met.
type FunNameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FunNameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FunNameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FunNameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FunNameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FunNameValidationError) ErrorName() string { return "FunNameValidationError" }

// Error satisfies the builtin error interface
func (e FunNameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFunName.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FunNameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FunNameValidationError{}

// Validate checks the field values on FileDllHijack with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileDllHijack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileDllHijack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileDllHijackMultiError, or
// nil if none found.
func (m *FileDllHijack) ValidateAll() error {
	return m.validate(true)
}

func (m *FileDllHijack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileDllHijackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileDllHijackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileDllHijackValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileDllHijackValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileDllHijackValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileDllHijackValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DllName

	// no validation rules for DllPath

	// no validation rules for Sha256

	for idx, item := range m.GetFuncNameList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileDllHijackValidationError{
						field:  fmt.Sprintf("FuncNameList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileDllHijackValidationError{
						field:  fmt.Sprintf("FuncNameList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileDllHijackValidationError{
					field:  fmt.Sprintf("FuncNameList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FileDllHijackMultiError(errors)
	}

	return nil
}

// FileDllHijackMultiError is an error wrapping multiple validation errors
// returned by FileDllHijack.ValidateAll() if the designated constraints
// aren't met.
type FileDllHijackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileDllHijackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileDllHijackMultiError) AllErrors() []error { return m }

// FileDllHijackValidationError is the validation error returned by
// FileDllHijack.Validate if the designated constraints aren't met.
type FileDllHijackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileDllHijackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileDllHijackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileDllHijackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileDllHijackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileDllHijackValidationError) ErrorName() string { return "FileDllHijackValidationError" }

// Error satisfies the builtin error interface
func (e FileDllHijackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileDllHijack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileDllHijackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileDllHijackValidationError{}

// Validate checks the field values on FileEnvHijack with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileEnvHijack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileEnvHijack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileEnvHijackMultiError, or
// nil if none found.
func (m *FileEnvHijack) ValidateAll() error {
	return m.validate(true)
}

func (m *FileEnvHijack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileEnvHijackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileEnvHijackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileEnvHijackValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FullValue

	// no validation rules for RiskValue

	// no validation rules for Feature

	// no validation rules for Sha256

	if len(errors) > 0 {
		return FileEnvHijackMultiError(errors)
	}

	return nil
}

// FileEnvHijackMultiError is an error wrapping multiple validation errors
// returned by FileEnvHijack.ValidateAll() if the designated constraints
// aren't met.
type FileEnvHijackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileEnvHijackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileEnvHijackMultiError) AllErrors() []error { return m }

// FileEnvHijackValidationError is the validation error returned by
// FileEnvHijack.Validate if the designated constraints aren't met.
type FileEnvHijackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileEnvHijackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileEnvHijackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileEnvHijackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileEnvHijackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileEnvHijackValidationError) ErrorName() string { return "FileEnvHijackValidationError" }

// Error satisfies the builtin error interface
func (e FileEnvHijackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileEnvHijack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileEnvHijackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileEnvHijackValidationError{}

// Validate checks the field values on FileSensitivity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileSensitivity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSensitivity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileSensitivityMultiError, or nil if none found.
func (m *FileSensitivity) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSensitivity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileSensitivityValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileSensitivityValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileSensitivityValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileSensitivityValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileSensitivityValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileSensitivityValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileOp

	// no validation rules for Sha256

	if len(errors) > 0 {
		return FileSensitivityMultiError(errors)
	}

	return nil
}

// FileSensitivityMultiError is an error wrapping multiple validation errors
// returned by FileSensitivity.ValidateAll() if the designated constraints
// aren't met.
type FileSensitivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSensitivityMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSensitivityMultiError) AllErrors() []error { return m }

// FileSensitivityValidationError is the validation error returned by
// FileSensitivity.Validate if the designated constraints aren't met.
type FileSensitivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSensitivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSensitivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSensitivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSensitivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSensitivityValidationError) ErrorName() string { return "FileSensitivityValidationError" }

// Error satisfies the builtin error interface
func (e FileSensitivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSensitivity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSensitivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSensitivityValidationError{}

// Validate checks the field values on LnkFile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LnkFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LnkFile with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in LnkFileMultiError, or nil if none found.
func (m *LnkFile) ValidateAll() error {
	return m.validate(true)
}

func (m *LnkFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LnkFileValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LnkFileValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LnkFileValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for CVE_ID

	// no validation rules for Sha256

	if len(errors) > 0 {
		return LnkFileMultiError(errors)
	}

	return nil
}

// LnkFileMultiError is an error wrapping multiple validation errors returned
// by LnkFile.ValidateAll() if the designated constraints aren't met.
type LnkFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LnkFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LnkFileMultiError) AllErrors() []error { return m }

// LnkFileValidationError is the validation error returned by LnkFile.Validate
// if the designated constraints aren't met.
type LnkFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LnkFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LnkFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LnkFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LnkFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LnkFileValidationError) ErrorName() string { return "LnkFileValidationError" }

// Error satisfies the builtin error interface
func (e LnkFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLnkFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LnkFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LnkFileValidationError{}

// Validate checks the field values on ClearLog with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClearLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClearLog with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClearLogMultiError, or nil
// if none found.
func (m *ClearLog) ValidateAll() error {
	return m.validate(true)
}

func (m *ClearLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClearLogValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClearLogValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClearLogValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClearLogValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClearLogValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClearLogValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LogName

	if len(errors) > 0 {
		return ClearLogMultiError(errors)
	}

	return nil
}

// ClearLogMultiError is an error wrapping multiple validation errors returned
// by ClearLog.ValidateAll() if the designated constraints aren't met.
type ClearLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClearLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClearLogMultiError) AllErrors() []error { return m }

// ClearLogValidationError is the validation error returned by
// ClearLog.Validate if the designated constraints aren't met.
type ClearLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClearLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClearLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClearLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClearLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClearLogValidationError) ErrorName() string { return "ClearLogValidationError" }

// Error satisfies the builtin error interface
func (e ClearLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClearLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClearLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClearLogValidationError{}

// Validate checks the field values on FileHandlingDeleteFilesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileHandlingDeleteFilesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHandlingDeleteFilesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FileHandlingDeleteFilesRequestMultiError, or nil if none found.
func (m *FileHandlingDeleteFilesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHandlingDeleteFilesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	for idx, item := range m.GetDeleteFilesList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileHandlingDeleteFilesRequestValidationError{
						field:  fmt.Sprintf("DeleteFilesList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileHandlingDeleteFilesRequestValidationError{
						field:  fmt.Sprintf("DeleteFilesList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileHandlingDeleteFilesRequestValidationError{
					field:  fmt.Sprintf("DeleteFilesList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Stage

	// no validation rules for Source

	if len(errors) > 0 {
		return FileHandlingDeleteFilesRequestMultiError(errors)
	}

	return nil
}

// FileHandlingDeleteFilesRequestMultiError is an error wrapping multiple
// validation errors returned by FileHandlingDeleteFilesRequest.ValidateAll()
// if the designated constraints aren't met.
type FileHandlingDeleteFilesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHandlingDeleteFilesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHandlingDeleteFilesRequestMultiError) AllErrors() []error { return m }

// FileHandlingDeleteFilesRequestValidationError is the validation error
// returned by FileHandlingDeleteFilesRequest.Validate if the designated
// constraints aren't met.
type FileHandlingDeleteFilesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHandlingDeleteFilesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHandlingDeleteFilesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHandlingDeleteFilesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHandlingDeleteFilesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHandlingDeleteFilesRequestValidationError) ErrorName() string {
	return "FileHandlingDeleteFilesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FileHandlingDeleteFilesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHandlingDeleteFilesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHandlingDeleteFilesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHandlingDeleteFilesRequestValidationError{}

// Validate checks the field values on FileHandlingDeleteFilesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileHandlingDeleteFilesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileHandlingDeleteFilesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FileHandlingDeleteFilesResponseMultiError, or nil if none found.
func (m *FileHandlingDeleteFilesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FileHandlingDeleteFilesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	for idx, item := range m.GetResultList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileHandlingDeleteFilesResponseValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileHandlingDeleteFilesResponseValidationError{
						field:  fmt.Sprintf("ResultList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileHandlingDeleteFilesResponseValidationError{
					field:  fmt.Sprintf("ResultList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Stage

	// no validation rules for Source

	if len(errors) > 0 {
		return FileHandlingDeleteFilesResponseMultiError(errors)
	}

	return nil
}

// FileHandlingDeleteFilesResponseMultiError is an error wrapping multiple
// validation errors returned by FileHandlingDeleteFilesResponse.ValidateAll()
// if the designated constraints aren't met.
type FileHandlingDeleteFilesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileHandlingDeleteFilesResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileHandlingDeleteFilesResponseMultiError) AllErrors() []error { return m }

// FileHandlingDeleteFilesResponseValidationError is the validation error
// returned by FileHandlingDeleteFilesResponse.Validate if the designated
// constraints aren't met.
type FileHandlingDeleteFilesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileHandlingDeleteFilesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileHandlingDeleteFilesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileHandlingDeleteFilesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileHandlingDeleteFilesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileHandlingDeleteFilesResponseValidationError) ErrorName() string {
	return "FileHandlingDeleteFilesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FileHandlingDeleteFilesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileHandlingDeleteFilesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileHandlingDeleteFilesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileHandlingDeleteFilesResponseValidationError{}

// Validate checks the field values on DeleteFilesInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteFilesInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFilesInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFilesInfoMultiError, or nil if none found.
func (m *DeleteFilesInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFilesInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for MacPathKey

	if len(errors) > 0 {
		return DeleteFilesInfoMultiError(errors)
	}

	return nil
}

// DeleteFilesInfoMultiError is an error wrapping multiple validation errors
// returned by DeleteFilesInfo.ValidateAll() if the designated constraints
// aren't met.
type DeleteFilesInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFilesInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFilesInfoMultiError) AllErrors() []error { return m }

// DeleteFilesInfoValidationError is the validation error returned by
// DeleteFilesInfo.Validate if the designated constraints aren't met.
type DeleteFilesInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFilesInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFilesInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFilesInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFilesInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFilesInfoValidationError) ErrorName() string { return "DeleteFilesInfoValidationError" }

// Error satisfies the builtin error interface
func (e DeleteFilesInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFilesInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFilesInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFilesInfoValidationError{}

// Validate checks the field values on DeleteFilesResult with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteFilesResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteFilesResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteFilesResultMultiError, or nil if none found.
func (m *DeleteFilesResult) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteFilesResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MacPathKey

	// no validation rules for Result

	// no validation rules for FilePath

	if len(errors) > 0 {
		return DeleteFilesResultMultiError(errors)
	}

	return nil
}

// DeleteFilesResultMultiError is an error wrapping multiple validation errors
// returned by DeleteFilesResult.ValidateAll() if the designated constraints
// aren't met.
type DeleteFilesResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteFilesResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteFilesResultMultiError) AllErrors() []error { return m }

// DeleteFilesResultValidationError is the validation error returned by
// DeleteFilesResult.Validate if the designated constraints aren't met.
type DeleteFilesResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteFilesResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteFilesResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteFilesResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteFilesResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteFilesResultValidationError) ErrorName() string {
	return "DeleteFilesResultValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteFilesResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteFilesResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteFilesResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteFilesResultValidationError{}
