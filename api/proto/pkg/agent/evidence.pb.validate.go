// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/evidence.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ObtainEvidenceReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObtainEvidenceReqMultiError, or nil if none found.
func (m *ObtainEvidenceReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EvidenceType

	// no validation rules for Key

	// no validation rules for Url

	// no validation rules for TaskId

	if len(errors) > 0 {
		return ObtainEvidenceReqMultiError(errors)
	}

	return nil
}

// ObtainEvidenceReqMultiError is an error wrapping multiple validation errors
// returned by ObtainEvidenceReq.ValidateAll() if the designated constraints
// aren't met.
type ObtainEvidenceReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceReqMultiError) AllErrors() []error { return m }

// ObtainEvidenceReqValidationError is the validation error returned by
// ObtainEvidenceReq.Validate if the designated constraints aren't met.
type ObtainEvidenceReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceReqValidationError) ErrorName() string {
	return "ObtainEvidenceReqValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceReqValidationError{}

// Validate checks the field values on ObtainEvidenceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObtainEvidenceRespMultiError, or nil if none found.
func (m *ObtainEvidenceResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Status

	// no validation rules for Filepath

	if len(errors) > 0 {
		return ObtainEvidenceRespMultiError(errors)
	}

	return nil
}

// ObtainEvidenceRespMultiError is an error wrapping multiple validation errors
// returned by ObtainEvidenceResp.ValidateAll() if the designated constraints
// aren't met.
type ObtainEvidenceRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceRespMultiError) AllErrors() []error { return m }

// ObtainEvidenceRespValidationError is the validation error returned by
// ObtainEvidenceResp.Validate if the designated constraints aren't met.
type ObtainEvidenceRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceRespValidationError) ErrorName() string {
	return "ObtainEvidenceRespValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceRespValidationError{}
