// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/common.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ClientID with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClientID) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientID with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClientIDMultiError, or nil
// if none found.
func (m *ClientID) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientID) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DateTime

	// no validation rules for ClientTime

	// no validation rules for ClientVersion

	if len(errors) > 0 {
		return ClientIDMultiError(errors)
	}

	return nil
}

// ClientIDMultiError is an error wrapping multiple validation errors returned
// by ClientID.ValidateAll() if the designated constraints aren't met.
type ClientIDMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientIDMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientIDMultiError) AllErrors() []error { return m }

// ClientIDValidationError is the validation error returned by
// ClientID.Validate if the designated constraints aren't met.
type ClientIDValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientIDValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientIDValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientIDValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientIDValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientIDValidationError) ErrorName() string { return "ClientIDValidationError" }

// Error satisfies the builtin error interface
func (e ClientIDValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientID.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientIDValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientIDValidationError{}

// Validate checks the field values on RiskHeader with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskHeader) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskHeader with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskHeaderMultiError, or
// nil if none found.
func (m *RiskHeader) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskHeader) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Level

	// no validation rules for Comment

	// no validation rules for OperateFlag

	// no validation rules for UniqueFlag

	if len(errors) > 0 {
		return RiskHeaderMultiError(errors)
	}

	return nil
}

// RiskHeaderMultiError is an error wrapping multiple validation errors
// returned by RiskHeader.ValidateAll() if the designated constraints aren't met.
type RiskHeaderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskHeaderMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskHeaderMultiError) AllErrors() []error { return m }

// RiskHeaderValidationError is the validation error returned by
// RiskHeader.Validate if the designated constraints aren't met.
type RiskHeaderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskHeaderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskHeaderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskHeaderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskHeaderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskHeaderValidationError) ErrorName() string { return "RiskHeaderValidationError" }

// Error satisfies the builtin error interface
func (e RiskHeaderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskHeader.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskHeaderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskHeaderValidationError{}

// Validate checks the field values on ProcessInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcessInfoMultiError, or
// nil if none found.
func (m *ProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PID

	// no validation rules for ProcessName

	// no validation rules for FilePath

	// no validation rules for FileSha256

	// no validation rules for FileMd5

	// no validation rules for CommandLine

	// no validation rules for IsX86Process

	// no validation rules for UserName

	// no validation rules for CurrentCreateTime

	// no validation rules for PPID

	// no validation rules for ParentCreateTime

	// no validation rules for FileSha1

	// no validation rules for FileCreateTime

	// no validation rules for FileModifyTime

	// no validation rules for FileLastAccessTime

	// no validation rules for FileSize

	// no validation rules for FileCompanyName

	// no validation rules for FileVersion

	// no validation rules for AccessToken

	// no validation rules for User

	for idx, item := range m.GetSignatureInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcessInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcessInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcessInfoValidationError{
					field:  fmt.Sprintf("SignatureInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcessInfoMultiError(errors)
	}

	return nil
}

// ProcessInfoMultiError is an error wrapping multiple validation errors
// returned by ProcessInfo.ValidateAll() if the designated constraints aren't met.
type ProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessInfoMultiError) AllErrors() []error { return m }

// ProcessInfoValidationError is the validation error returned by
// ProcessInfo.Validate if the designated constraints aren't met.
type ProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessInfoValidationError) ErrorName() string { return "ProcessInfoValidationError" }

// Error satisfies the builtin error interface
func (e ProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessInfoValidationError{}

// Validate checks the field values on NetInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in NetInfoMultiError, or nil if none found.
func (m *NetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Port

	// no validation rules for Address

	if len(errors) > 0 {
		return NetInfoMultiError(errors)
	}

	return nil
}

// NetInfoMultiError is an error wrapping multiple validation errors returned
// by NetInfo.ValidateAll() if the designated constraints aren't met.
type NetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetInfoMultiError) AllErrors() []error { return m }

// NetInfoValidationError is the validation error returned by NetInfo.Validate
// if the designated constraints aren't met.
type NetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetInfoValidationError) ErrorName() string { return "NetInfoValidationError" }

// Error satisfies the builtin error interface
func (e NetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetInfoValidationError{}

// Validate checks the field values on OpFileInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OpFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpFileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OpFileInfoMultiError, or
// nil if none found.
func (m *OpFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OpFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileMd5

	// no validation rules for StMode

	// no validation rules for FileSize

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	if len(errors) > 0 {
		return OpFileInfoMultiError(errors)
	}

	return nil
}

// OpFileInfoMultiError is an error wrapping multiple validation errors
// returned by OpFileInfo.ValidateAll() if the designated constraints aren't met.
type OpFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpFileInfoMultiError) AllErrors() []error { return m }

// OpFileInfoValidationError is the validation error returned by
// OpFileInfo.Validate if the designated constraints aren't met.
type OpFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpFileInfoValidationError) ErrorName() string { return "OpFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e OpFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpFileInfoValidationError{}

// Validate checks the field values on SignatureInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SignatureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignatureInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SignatureInfoMultiError, or
// nil if none found.
func (m *SignatureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SignatureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Serial

	// no validation rules for IssuerName

	// no validation rules for Customer

	// no validation rules for Thumbprint

	// no validation rules for Result

	// no validation rules for Description

	// no validation rules for NotAfter

	// no validation rules for NotBefore

	// no validation rules for SignAlgorithm

	// no validation rules for SignHashAlgorithm

	// no validation rules for Version

	// no validation rules for SignStatusInfo

	if len(errors) > 0 {
		return SignatureInfoMultiError(errors)
	}

	return nil
}

// SignatureInfoMultiError is an error wrapping multiple validation errors
// returned by SignatureInfo.ValidateAll() if the designated constraints
// aren't met.
type SignatureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignatureInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignatureInfoMultiError) AllErrors() []error { return m }

// SignatureInfoValidationError is the validation error returned by
// SignatureInfo.Validate if the designated constraints aren't met.
type SignatureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignatureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignatureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignatureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignatureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignatureInfoValidationError) ErrorName() string { return "SignatureInfoValidationError" }

// Error satisfies the builtin error interface
func (e SignatureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignatureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignatureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignatureInfoValidationError{}

// Validate checks the field values on ReportEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportEvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportEvidenceInfoMultiError, or nil if none found.
func (m *ReportEvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportEvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EvidenceType

	// no validation rules for Uniqueflag

	// no validation rules for EvidenceSize

	// no validation rules for Filepath

	// no validation rules for Filename

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	// no validation rules for Filesize

	if len(errors) > 0 {
		return ReportEvidenceInfoMultiError(errors)
	}

	return nil
}

// ReportEvidenceInfoMultiError is an error wrapping multiple validation errors
// returned by ReportEvidenceInfo.ValidateAll() if the designated constraints
// aren't met.
type ReportEvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportEvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportEvidenceInfoMultiError) AllErrors() []error { return m }

// ReportEvidenceInfoValidationError is the validation error returned by
// ReportEvidenceInfo.Validate if the designated constraints aren't met.
type ReportEvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportEvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportEvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportEvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportEvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportEvidenceInfoValidationError) ErrorName() string {
	return "ReportEvidenceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ReportEvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportEvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportEvidenceInfoValidationError{}

// Validate checks the field values on ProcFileDetailInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcFileDetailInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcFileDetailInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcFileDetailInfoMultiError, or nil if none found.
func (m *ProcFileDetailInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcFileDetailInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InternalName

	// no validation rules for ProductName

	// no validation rules for CompanyName

	// no validation rules for Copyright

	// no validation rules for Version

	// no validation rules for Desc

	// no validation rules for Trademark

	// no validation rules for BuildInfo

	// no validation rules for VerCode

	// no validation rules for OriginalInfo

	// no validation rules for SpecialBuildInfo

	if len(errors) > 0 {
		return ProcFileDetailInfoMultiError(errors)
	}

	return nil
}

// ProcFileDetailInfoMultiError is an error wrapping multiple validation errors
// returned by ProcFileDetailInfo.ValidateAll() if the designated constraints
// aren't met.
type ProcFileDetailInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcFileDetailInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcFileDetailInfoMultiError) AllErrors() []error { return m }

// ProcFileDetailInfoValidationError is the validation error returned by
// ProcFileDetailInfo.Validate if the designated constraints aren't met.
type ProcFileDetailInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcFileDetailInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcFileDetailInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcFileDetailInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcFileDetailInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcFileDetailInfoValidationError) ErrorName() string {
	return "ProcFileDetailInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ProcFileDetailInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcFileDetailInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcFileDetailInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcFileDetailInfoValidationError{}
