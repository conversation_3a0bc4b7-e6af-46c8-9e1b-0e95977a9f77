// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/migrate.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ServerMigrateErrorCode int32

const (
	ServerMigrateErrorCode_SMEC_OK                      ServerMigrateErrorCode = 0 // 成功
	ServerMigrateErrorCode_SMEC_CONNECT_SERVER_FAILED   ServerMigrateErrorCode = 1 // 无法连接新的服务端
	ServerMigrateErrorCode_SMEC_OPEN_CONFIG_FILE_FAILED ServerMigrateErrorCode = 2 // 打开config文件失败
	ServerMigrateErrorCode_SMEC_OPEN_CONFIG_NOT_EXIST   ServerMigrateErrorCode = 3 // config文件不存在
	ServerMigrateErrorCode_SMEC_PORT_WRONG              ServerMigrateErrorCode = 4 // 端口号错误，大于65535
	ServerMigrateErrorCode_SMEC_WRITE_CONFIG_FAILD      ServerMigrateErrorCode = 5 // 修改配置文件失败
)

// Enum value maps for ServerMigrateErrorCode.
var (
	ServerMigrateErrorCode_name = map[int32]string{
		0: "SMEC_OK",
		1: "SMEC_CONNECT_SERVER_FAILED",
		2: "SMEC_OPEN_CONFIG_FILE_FAILED",
		3: "SMEC_OPEN_CONFIG_NOT_EXIST",
		4: "SMEC_PORT_WRONG",
		5: "SMEC_WRITE_CONFIG_FAILD",
	}
	ServerMigrateErrorCode_value = map[string]int32{
		"SMEC_OK":                      0,
		"SMEC_CONNECT_SERVER_FAILED":   1,
		"SMEC_OPEN_CONFIG_FILE_FAILED": 2,
		"SMEC_OPEN_CONFIG_NOT_EXIST":   3,
		"SMEC_PORT_WRONG":              4,
		"SMEC_WRITE_CONFIG_FAILD":      5,
	}
)

func (x ServerMigrateErrorCode) Enum() *ServerMigrateErrorCode {
	p := new(ServerMigrateErrorCode)
	*p = x
	return p
}

func (x ServerMigrateErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerMigrateErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_migrate_proto_enumTypes[0].Descriptor()
}

func (ServerMigrateErrorCode) Type() protoreflect.EnumType {
	return &file_agent_migrate_proto_enumTypes[0]
}

func (x ServerMigrateErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerMigrateErrorCode.Descriptor instead.
func (ServerMigrateErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_migrate_proto_rawDescGZIP(), []int{0}
}

// 服务器推送迁移消息
type ServerMigrateMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip    []byte `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`        // 新服务器的IP
	Port  uint32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`   // 新服务器的端口号
	Force bool   `protobuf:"varint,3,opt,name=force,proto3" json:"force,omitempty"` // 强制迁移（先保留），不验证新服务器服务器有效性，都进行迁移
}

func (x *ServerMigrateMsg) Reset() {
	*x = ServerMigrateMsg{}
	mi := &file_agent_migrate_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerMigrateMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerMigrateMsg) ProtoMessage() {}

func (x *ServerMigrateMsg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_migrate_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerMigrateMsg.ProtoReflect.Descriptor instead.
func (*ServerMigrateMsg) Descriptor() ([]byte, []int) {
	return file_agent_migrate_proto_rawDescGZIP(), []int{0}
}

func (x *ServerMigrateMsg) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *ServerMigrateMsg) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerMigrateMsg) GetForce() bool {
	if x != nil {
		return x.Force
	}
	return false
}

type ServerMigrateResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo  *ClientID              `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`                                      // 主机信息标识
	Ip        []byte                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`                                                  // 新服务器的IP
	Port      uint32                 `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`                                             // 新服务器的端口号
	ErrorCode ServerMigrateErrorCode `protobuf:"varint,4,opt,name=errorCode,proto3,enum=agent.ServerMigrateErrorCode" json:"errorCode,omitempty"` // 成功OK, 失败返回原因ERR_reason
}

func (x *ServerMigrateResult) Reset() {
	*x = ServerMigrateResult{}
	mi := &file_agent_migrate_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerMigrateResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerMigrateResult) ProtoMessage() {}

func (x *ServerMigrateResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_migrate_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerMigrateResult.ProtoReflect.Descriptor instead.
func (*ServerMigrateResult) Descriptor() ([]byte, []int) {
	return file_agent_migrate_proto_rawDescGZIP(), []int{1}
}

func (x *ServerMigrateResult) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *ServerMigrateResult) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *ServerMigrateResult) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerMigrateResult) GetErrorCode() ServerMigrateErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return ServerMigrateErrorCode_SMEC_OK
}

var File_agent_migrate_proto protoreflect.FileDescriptor

var file_agent_migrate_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x4c, 0x0a, 0x10, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74,
	0x65, 0x4d, 0x73, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x22, 0xa3,
	0x01, 0x0a, 0x13, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x2a, 0xb9, 0x01, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d,
	0x69, 0x67, 0x72, 0x61, 0x74, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x4d, 0x45, 0x43, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x53, 0x4d, 0x45, 0x43, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x45, 0x52,
	0x56, 0x45, 0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c,
	0x53, 0x4d, 0x45, 0x43, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1e,
	0x0a, 0x1a, 0x53, 0x4d, 0x45, 0x43, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x03, 0x12, 0x13,
	0x0a, 0x0f, 0x53, 0x4d, 0x45, 0x43, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x57, 0x52, 0x4f, 0x4e,
	0x47, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4d, 0x45, 0x43, 0x5f, 0x57, 0x52, 0x49, 0x54,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x44, 0x10, 0x05,
	0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_migrate_proto_rawDescOnce sync.Once
	file_agent_migrate_proto_rawDescData = file_agent_migrate_proto_rawDesc
)

func file_agent_migrate_proto_rawDescGZIP() []byte {
	file_agent_migrate_proto_rawDescOnce.Do(func() {
		file_agent_migrate_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_migrate_proto_rawDescData)
	})
	return file_agent_migrate_proto_rawDescData
}

var file_agent_migrate_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_migrate_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_migrate_proto_goTypes = []any{
	(ServerMigrateErrorCode)(0), // 0: agent.ServerMigrateErrorCode
	(*ServerMigrateMsg)(nil),    // 1: agent.ServerMigrateMsg
	(*ServerMigrateResult)(nil), // 2: agent.ServerMigrateResult
	(*ClientID)(nil),            // 3: agent.ClientID
}
var file_agent_migrate_proto_depIdxs = []int32{
	3, // 0: agent.ServerMigrateResult.baseInfo:type_name -> agent.ClientID
	0, // 1: agent.ServerMigrateResult.errorCode:type_name -> agent.ServerMigrateErrorCode
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_agent_migrate_proto_init() }
func file_agent_migrate_proto_init() {
	if File_agent_migrate_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_migrate_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_migrate_proto_goTypes,
		DependencyIndexes: file_agent_migrate_proto_depIdxs,
		EnumInfos:         file_agent_migrate_proto_enumTypes,
		MessageInfos:      file_agent_migrate_proto_msgTypes,
	}.Build()
	File_agent_migrate_proto = out.File
	file_agent_migrate_proto_rawDesc = nil
	file_agent_migrate_proto_goTypes = nil
	file_agent_migrate_proto_depIdxs = nil
}
