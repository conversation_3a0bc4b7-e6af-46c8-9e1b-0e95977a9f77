// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/migrate.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ServerMigrateMsg with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServerMigrateMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerMigrateMsg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerMigrateMsgMultiError, or nil if none found.
func (m *ServerMigrateMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerMigrateMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for Port

	// no validation rules for Force

	if len(errors) > 0 {
		return ServerMigrateMsgMultiError(errors)
	}

	return nil
}

// ServerMigrateMsgMultiError is an error wrapping multiple validation errors
// returned by ServerMigrateMsg.ValidateAll() if the designated constraints
// aren't met.
type ServerMigrateMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerMigrateMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerMigrateMsgMultiError) AllErrors() []error { return m }

// ServerMigrateMsgValidationError is the validation error returned by
// ServerMigrateMsg.Validate if the designated constraints aren't met.
type ServerMigrateMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerMigrateMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerMigrateMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerMigrateMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerMigrateMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerMigrateMsgValidationError) ErrorName() string { return "ServerMigrateMsgValidationError" }

// Error satisfies the builtin error interface
func (e ServerMigrateMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerMigrateMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerMigrateMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerMigrateMsgValidationError{}

// Validate checks the field values on ServerMigrateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServerMigrateResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerMigrateResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerMigrateResultMultiError, or nil if none found.
func (m *ServerMigrateResult) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerMigrateResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerMigrateResultValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerMigrateResultValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerMigrateResultValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Ip

	// no validation rules for Port

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return ServerMigrateResultMultiError(errors)
	}

	return nil
}

// ServerMigrateResultMultiError is an error wrapping multiple validation
// errors returned by ServerMigrateResult.ValidateAll() if the designated
// constraints aren't met.
type ServerMigrateResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerMigrateResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerMigrateResultMultiError) AllErrors() []error { return m }

// ServerMigrateResultValidationError is the validation error returned by
// ServerMigrateResult.Validate if the designated constraints aren't met.
type ServerMigrateResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerMigrateResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerMigrateResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerMigrateResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerMigrateResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerMigrateResultValidationError) ErrorName() string {
	return "ServerMigrateResultValidationError"
}

// Error satisfies the builtin error interface
func (e ServerMigrateResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerMigrateResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerMigrateResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerMigrateResultValidationError{}
