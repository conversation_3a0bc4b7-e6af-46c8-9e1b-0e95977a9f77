// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_proc.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskProcInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskProcInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskProcInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskProcInfoMultiError, or nil if none found.
func (m *MemProtectRiskProcInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskProcInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskProcInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskProcInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskProcInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProcHiddenSelfList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcHiddenSelfList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcHiddenSelfList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcHiddenSelfList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcHiddenPortList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcHiddenPortList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcHiddenPortList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcHiddenPortList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcDangerousList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcDangerousList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcDangerousList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcDangerousList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcSensitivityList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcSensitivityList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcSensitivityList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcSensitivityList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcBackShellList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcBackShellList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcBackShellList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcBackShellList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcRiskASRList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskASRList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskASRList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcRiskASRList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcRiskReuseList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskReuseList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskReuseList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcRiskReuseList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcPuppetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcPuppetList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcPuppetList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcPuppetList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcRiskOpenPortList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskOpenPortList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcRiskOpenPortList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcRiskOpenPortList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcPrivilegeElevationList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcPrivilegeElevationList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcPrivilegeElevationList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcPrivilegeElevationList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcessEscalationToRoot() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcessEscalationToRoot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("ProcessEscalationToRoot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("ProcessEscalationToRoot[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTimedTaskEscalationToRoot() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("TimedTaskEscalationToRoot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("TimedTaskEscalationToRoot[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("TimedTaskEscalationToRoot[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetHashAntivirus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("HashAntivirus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("HashAntivirus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("HashAntivirus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetIllegalConnect() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("IllegalConnect[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskProcInfoValidationError{
						field:  fmt.Sprintf("IllegalConnect[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskProcInfoValidationError{
					field:  fmt.Sprintf("IllegalConnect[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskProcInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskProcInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskProcInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskProcInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskProcInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskProcInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskProcInfoValidationError is the validation error returned by
// MemProtectRiskProcInfo.Validate if the designated constraints aren't met.
type MemProtectRiskProcInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskProcInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskProcInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskProcInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskProcInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskProcInfoValidationError) ErrorName() string {
	return "MemProtectRiskProcInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskProcInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskProcInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskProcInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskProcInfoValidationError{}

// Validate checks the field values on ProcHiddenSelf with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcHiddenSelf) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcHiddenSelf with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcHiddenSelfMultiError,
// or nil if none found.
func (m *ProcHiddenSelf) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcHiddenSelf) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcHiddenSelfValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcHiddenSelfValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcHiddenSelfValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcHiddenSelfValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcHiddenSelfValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcHiddenSelfValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileSha256

	// no validation rules for CommandLine

	// no validation rules for IsX86Process

	// no validation rules for ProcessMd5

	if len(errors) > 0 {
		return ProcHiddenSelfMultiError(errors)
	}

	return nil
}

// ProcHiddenSelfMultiError is an error wrapping multiple validation errors
// returned by ProcHiddenSelf.ValidateAll() if the designated constraints
// aren't met.
type ProcHiddenSelfMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcHiddenSelfMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcHiddenSelfMultiError) AllErrors() []error { return m }

// ProcHiddenSelfValidationError is the validation error returned by
// ProcHiddenSelf.Validate if the designated constraints aren't met.
type ProcHiddenSelfValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcHiddenSelfValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcHiddenSelfValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcHiddenSelfValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcHiddenSelfValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcHiddenSelfValidationError) ErrorName() string { return "ProcHiddenSelfValidationError" }

// Error satisfies the builtin error interface
func (e ProcHiddenSelfValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcHiddenSelf.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcHiddenSelfValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcHiddenSelfValidationError{}

// Validate checks the field values on ProcHiddenPort with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcHiddenPort) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcHiddenPort with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcHiddenPortMultiError,
// or nil if none found.
func (m *ProcHiddenPort) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcHiddenPort) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcHiddenPortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcHiddenPortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcHiddenPortValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcHiddenPortValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcHiddenPortValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcHiddenPortValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Port

	// no validation rules for Protocol

	if len(errors) > 0 {
		return ProcHiddenPortMultiError(errors)
	}

	return nil
}

// ProcHiddenPortMultiError is an error wrapping multiple validation errors
// returned by ProcHiddenPort.ValidateAll() if the designated constraints
// aren't met.
type ProcHiddenPortMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcHiddenPortMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcHiddenPortMultiError) AllErrors() []error { return m }

// ProcHiddenPortValidationError is the validation error returned by
// ProcHiddenPort.Validate if the designated constraints aren't met.
type ProcHiddenPortValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcHiddenPortValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcHiddenPortValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcHiddenPortValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcHiddenPortValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcHiddenPortValidationError) ErrorName() string { return "ProcHiddenPortValidationError" }

// Error satisfies the builtin error interface
func (e ProcHiddenPortValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcHiddenPort.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcHiddenPortValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcHiddenPortValidationError{}

// Validate checks the field values on ProcRiskOpenPort with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcRiskOpenPort) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcRiskOpenPort with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcRiskOpenPortMultiError, or nil if none found.
func (m *ProcRiskOpenPort) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcRiskOpenPort) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcRiskOpenPortValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcRiskOpenPortValidationError{
				field:  "SourceProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcRiskOpenPortValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcRiskOpenPortValidationError{
				field:  "TargetProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	if len(errors) > 0 {
		return ProcRiskOpenPortMultiError(errors)
	}

	return nil
}

// ProcRiskOpenPortMultiError is an error wrapping multiple validation errors
// returned by ProcRiskOpenPort.ValidateAll() if the designated constraints
// aren't met.
type ProcRiskOpenPortMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcRiskOpenPortMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcRiskOpenPortMultiError) AllErrors() []error { return m }

// ProcRiskOpenPortValidationError is the validation error returned by
// ProcRiskOpenPort.Validate if the designated constraints aren't met.
type ProcRiskOpenPortValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcRiskOpenPortValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcRiskOpenPortValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcRiskOpenPortValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcRiskOpenPortValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcRiskOpenPortValidationError) ErrorName() string { return "ProcRiskOpenPortValidationError" }

// Error satisfies the builtin error interface
func (e ProcRiskOpenPortValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcRiskOpenPort.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcRiskOpenPortValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcRiskOpenPortValidationError{}

// Validate checks the field values on ProcReusePort with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcReusePort) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcReusePort with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcReusePortMultiError, or
// nil if none found.
func (m *ProcReusePort) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcReusePort) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcReusePortValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcReusePortValidationError{
				field:  "SourceProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcReusePortValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcReusePortValidationError{
				field:  "TargetProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Port

	// no validation rules for Protocol

	if len(errors) > 0 {
		return ProcReusePortMultiError(errors)
	}

	return nil
}

// ProcReusePortMultiError is an error wrapping multiple validation errors
// returned by ProcReusePort.ValidateAll() if the designated constraints
// aren't met.
type ProcReusePortMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcReusePortMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcReusePortMultiError) AllErrors() []error { return m }

// ProcReusePortValidationError is the validation error returned by
// ProcReusePort.Validate if the designated constraints aren't met.
type ProcReusePortValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcReusePortValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcReusePortValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcReusePortValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcReusePortValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcReusePortValidationError) ErrorName() string { return "ProcReusePortValidationError" }

// Error satisfies the builtin error interface
func (e ProcReusePortValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcReusePort.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcReusePortValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcReusePortValidationError{}

// Validate checks the field values on ProcDangerous with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcDangerous) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcDangerous with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcDangerousMultiError, or
// nil if none found.
func (m *ProcDangerous) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcDangerous) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcDangerousValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcDangerousValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcDangerousValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcDangerousValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcDangerousValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcDangerousValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileSha256

	// no validation rules for IoLoadAvg

	// no validation rules for CommandLine

	// no validation rules for IsX86Process

	if len(errors) > 0 {
		return ProcDangerousMultiError(errors)
	}

	return nil
}

// ProcDangerousMultiError is an error wrapping multiple validation errors
// returned by ProcDangerous.ValidateAll() if the designated constraints
// aren't met.
type ProcDangerousMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcDangerousMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcDangerousMultiError) AllErrors() []error { return m }

// ProcDangerousValidationError is the validation error returned by
// ProcDangerous.Validate if the designated constraints aren't met.
type ProcDangerousValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcDangerousValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcDangerousValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcDangerousValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcDangerousValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcDangerousValidationError) ErrorName() string { return "ProcDangerousValidationError" }

// Error satisfies the builtin error interface
func (e ProcDangerousValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcDangerous.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcDangerousValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcDangerousValidationError{}

// Validate checks the field values on ProcSensitivity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcSensitivity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcSensitivity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcSensitivityMultiError, or nil if none found.
func (m *ProcSensitivity) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcSensitivity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcSensitivityValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcSensitivityValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcSensitivityValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcSensitivityValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcSensitivityValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcSensitivityValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubProcessPath

	// no validation rules for SubProcessFileSha256

	// no validation rules for SubProcessCommandLine

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcSensitivityValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcSensitivityValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.Has_PuppetProcMessage.(type) {
	case *ProcSensitivity_PuppeteProcMesssage:
		if v == nil {
			err := ProcSensitivityValidationError{
				field:  "Has_PuppetProcMessage",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPuppeteProcMesssage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  "PuppeteProcMesssage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcSensitivityValidationError{
						field:  "PuppeteProcMesssage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPuppeteProcMesssage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcSensitivityValidationError{
					field:  "PuppeteProcMesssage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProcSensitivityMultiError(errors)
	}

	return nil
}

// ProcSensitivityMultiError is an error wrapping multiple validation errors
// returned by ProcSensitivity.ValidateAll() if the designated constraints
// aren't met.
type ProcSensitivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcSensitivityMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcSensitivityMultiError) AllErrors() []error { return m }

// ProcSensitivityValidationError is the validation error returned by
// ProcSensitivity.Validate if the designated constraints aren't met.
type ProcSensitivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcSensitivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcSensitivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcSensitivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcSensitivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcSensitivityValidationError) ErrorName() string { return "ProcSensitivityValidationError" }

// Error satisfies the builtin error interface
func (e ProcSensitivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcSensitivity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcSensitivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcSensitivityValidationError{}

// Validate checks the field values on BackShell with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BackShell) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BackShell with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BackShellMultiError, or nil
// if none found.
func (m *BackShell) ValidateAll() error {
	return m.validate(true)
}

func (m *BackShell) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BackShellValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BackShellValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BackShellValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BackShellValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BackShellValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BackShellValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemotIP

	// no validation rules for RemotPort

	// no validation rules for Protocol

	if len(errors) > 0 {
		return BackShellMultiError(errors)
	}

	return nil
}

// BackShellMultiError is an error wrapping multiple validation errors returned
// by BackShell.ValidateAll() if the designated constraints aren't met.
type BackShellMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BackShellMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BackShellMultiError) AllErrors() []error { return m }

// BackShellValidationError is the validation error returned by
// BackShell.Validate if the designated constraints aren't met.
type BackShellValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BackShellValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BackShellValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BackShellValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BackShellValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BackShellValidationError) ErrorName() string { return "BackShellValidationError" }

// Error satisfies the builtin error interface
func (e BackShellValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBackShell.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BackShellValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BackShellValidationError{}

// Validate checks the field values on ProcRiskASR with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcRiskASR) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcRiskASR with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcRiskASRMultiError, or
// nil if none found.
func (m *ProcRiskASR) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcRiskASR) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcRiskASRValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcRiskASRValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcRiskASRValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcRiskASRValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcRiskASRValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcRiskASRValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskFile

	if len(errors) > 0 {
		return ProcRiskASRMultiError(errors)
	}

	return nil
}

// ProcRiskASRMultiError is an error wrapping multiple validation errors
// returned by ProcRiskASR.ValidateAll() if the designated constraints aren't met.
type ProcRiskASRMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcRiskASRMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcRiskASRMultiError) AllErrors() []error { return m }

// ProcRiskASRValidationError is the validation error returned by
// ProcRiskASR.Validate if the designated constraints aren't met.
type ProcRiskASRValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcRiskASRValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcRiskASRValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcRiskASRValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcRiskASRValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcRiskASRValidationError) ErrorName() string { return "ProcRiskASRValidationError" }

// Error satisfies the builtin error interface
func (e ProcRiskASRValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcRiskASR.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcRiskASRValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcRiskASRValidationError{}

// Validate checks the field values on TimedTaskEscalationToRoot with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TimedTaskEscalationToRoot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimedTaskEscalationToRoot with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TimedTaskEscalationToRootMultiError, or nil if none found.
func (m *TimedTaskEscalationToRoot) ValidateAll() error {
	return m.validate(true)
}

func (m *TimedTaskEscalationToRoot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimedTaskEscalationToRootValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimedTaskEscalationToRootValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimedTaskEscalationToRootValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TaskName

	// no validation rules for TaskPath

	if len(errors) > 0 {
		return TimedTaskEscalationToRootMultiError(errors)
	}

	return nil
}

// TimedTaskEscalationToRootMultiError is an error wrapping multiple validation
// errors returned by TimedTaskEscalationToRoot.ValidateAll() if the
// designated constraints aren't met.
type TimedTaskEscalationToRootMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimedTaskEscalationToRootMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimedTaskEscalationToRootMultiError) AllErrors() []error { return m }

// TimedTaskEscalationToRootValidationError is the validation error returned by
// TimedTaskEscalationToRoot.Validate if the designated constraints aren't met.
type TimedTaskEscalationToRootValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimedTaskEscalationToRootValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimedTaskEscalationToRootValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimedTaskEscalationToRootValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimedTaskEscalationToRootValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimedTaskEscalationToRootValidationError) ErrorName() string {
	return "TimedTaskEscalationToRootValidationError"
}

// Error satisfies the builtin error interface
func (e TimedTaskEscalationToRootValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimedTaskEscalationToRoot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimedTaskEscalationToRootValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimedTaskEscalationToRootValidationError{}

// Validate checks the field values on HashAntivirus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HashAntivirus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashAntivirus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HashAntivirusMultiError, or
// nil if none found.
func (m *HashAntivirus) ValidateAll() error {
	return m.validate(true)
}

func (m *HashAntivirus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashAntivirusValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashAntivirusValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashAntivirusValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HashAntivirusValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HashAntivirusValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HashAntivirusValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VirusDescribeId

	if len(errors) > 0 {
		return HashAntivirusMultiError(errors)
	}

	return nil
}

// HashAntivirusMultiError is an error wrapping multiple validation errors
// returned by HashAntivirus.ValidateAll() if the designated constraints
// aren't met.
type HashAntivirusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashAntivirusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashAntivirusMultiError) AllErrors() []error { return m }

// HashAntivirusValidationError is the validation error returned by
// HashAntivirus.Validate if the designated constraints aren't met.
type HashAntivirusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashAntivirusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashAntivirusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashAntivirusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashAntivirusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashAntivirusValidationError) ErrorName() string { return "HashAntivirusValidationError" }

// Error satisfies the builtin error interface
func (e HashAntivirusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashAntivirus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashAntivirusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashAntivirusValidationError{}

// Validate checks the field values on IllegalConnect with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IllegalConnect) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IllegalConnect with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IllegalConnectMultiError,
// or nil if none found.
func (m *IllegalConnect) ValidateAll() error {
	return m.validate(true)
}

func (m *IllegalConnect) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalConnectValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalConnectValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalConnectValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalConnectValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalConnectValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalConnectValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemotIP

	// no validation rules for RemotPort

	// no validation rules for Protocol

	if len(errors) > 0 {
		return IllegalConnectMultiError(errors)
	}

	return nil
}

// IllegalConnectMultiError is an error wrapping multiple validation errors
// returned by IllegalConnect.ValidateAll() if the designated constraints
// aren't met.
type IllegalConnectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IllegalConnectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IllegalConnectMultiError) AllErrors() []error { return m }

// IllegalConnectValidationError is the validation error returned by
// IllegalConnect.Validate if the designated constraints aren't met.
type IllegalConnectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IllegalConnectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IllegalConnectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IllegalConnectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IllegalConnectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IllegalConnectValidationError) ErrorName() string { return "IllegalConnectValidationError" }

// Error satisfies the builtin error interface
func (e IllegalConnectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIllegalConnect.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IllegalConnectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IllegalConnectValidationError{}

// Validate checks the field values on PuppetProcMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PuppetProcMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PuppetProcMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PuppetProcMessageMultiError, or nil if none found.
func (m *PuppetProcMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *PuppetProcMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleAddr

	// no validation rules for MemStr

	if len(errors) > 0 {
		return PuppetProcMessageMultiError(errors)
	}

	return nil
}

// PuppetProcMessageMultiError is an error wrapping multiple validation errors
// returned by PuppetProcMessage.ValidateAll() if the designated constraints
// aren't met.
type PuppetProcMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PuppetProcMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PuppetProcMessageMultiError) AllErrors() []error { return m }

// PuppetProcMessageValidationError is the validation error returned by
// PuppetProcMessage.Validate if the designated constraints aren't met.
type PuppetProcMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PuppetProcMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PuppetProcMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PuppetProcMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PuppetProcMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PuppetProcMessageValidationError) ErrorName() string {
	return "PuppetProcMessageValidationError"
}

// Error satisfies the builtin error interface
func (e PuppetProcMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPuppetProcMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PuppetProcMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PuppetProcMessageValidationError{}
