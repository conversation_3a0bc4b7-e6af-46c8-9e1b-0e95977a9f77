// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/uninstall.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UninstallAgentErrorCode int32

const (
	UninstallAgentErrorCode_UAEC_Unknown               UninstallAgentErrorCode = 0
	UninstallAgentErrorCode_UAEC_OK                    UninstallAgentErrorCode = 1
	UninstallAgentErrorCode_UAEC_RemoveFileFailed      UninstallAgentErrorCode = 2
	UninstallAgentErrorCode_UAEC_UninstallDriverFailed UninstallAgentErrorCode = 3
)

// Enum value maps for UninstallAgentErrorCode.
var (
	UninstallAgentErrorCode_name = map[int32]string{
		0: "UAEC_Unknown",
		1: "UAEC_OK",
		2: "UAEC_RemoveFileFailed",
		3: "UAEC_UninstallDriverFailed",
	}
	UninstallAgentErrorCode_value = map[string]int32{
		"UAEC_Unknown":               0,
		"UAEC_OK":                    1,
		"UAEC_RemoveFileFailed":      2,
		"UAEC_UninstallDriverFailed": 3,
	}
)

func (x UninstallAgentErrorCode) Enum() *UninstallAgentErrorCode {
	p := new(UninstallAgentErrorCode)
	*p = x
	return p
}

func (x UninstallAgentErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UninstallAgentErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_uninstall_proto_enumTypes[0].Descriptor()
}

func (UninstallAgentErrorCode) Type() protoreflect.EnumType {
	return &file_agent_uninstall_proto_enumTypes[0]
}

func (x UninstallAgentErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UninstallAgentErrorCode.Descriptor instead.
func (UninstallAgentErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_uninstall_proto_rawDescGZIP(), []int{0}
}

type UninstallAgent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UninstallAgent) Reset() {
	*x = UninstallAgent{}
	mi := &file_agent_uninstall_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UninstallAgent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallAgent) ProtoMessage() {}

func (x *UninstallAgent) ProtoReflect() protoreflect.Message {
	mi := &file_agent_uninstall_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallAgent.ProtoReflect.Descriptor instead.
func (*UninstallAgent) Descriptor() ([]byte, []int) {
	return file_agent_uninstall_proto_rawDescGZIP(), []int{0}
}

type UninstallAgentResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrorCode UninstallAgentErrorCode `protobuf:"varint,1,opt,name=errorCode,proto3,enum=agent.UninstallAgentErrorCode" json:"errorCode,omitempty"`
}

func (x *UninstallAgentResult) Reset() {
	*x = UninstallAgentResult{}
	mi := &file_agent_uninstall_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UninstallAgentResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallAgentResult) ProtoMessage() {}

func (x *UninstallAgentResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_uninstall_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallAgentResult.ProtoReflect.Descriptor instead.
func (*UninstallAgentResult) Descriptor() ([]byte, []int) {
	return file_agent_uninstall_proto_rawDescGZIP(), []int{1}
}

func (x *UninstallAgentResult) GetErrorCode() UninstallAgentErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return UninstallAgentErrorCode_UAEC_Unknown
}

var File_agent_uninstall_proto protoreflect.FileDescriptor

var file_agent_uninstall_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x10,
	0x0a, 0x0e, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x22, 0x54, 0x0a, 0x14, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x3c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x2a, 0x73, 0x0a, 0x17, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x41, 0x45, 0x43, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x41, 0x45, 0x43, 0x5f, 0x4f, 0x4b, 0x10, 0x01,
	0x12, 0x19, 0x0a, 0x15, 0x55, 0x41, 0x45, 0x43, 0x5f, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x55,
	0x41, 0x45, 0x43, 0x5f, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x10, 0x03, 0x42, 0x2a, 0x5a, 0x28, 0x67,
	0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31,
	0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_uninstall_proto_rawDescOnce sync.Once
	file_agent_uninstall_proto_rawDescData = file_agent_uninstall_proto_rawDesc
)

func file_agent_uninstall_proto_rawDescGZIP() []byte {
	file_agent_uninstall_proto_rawDescOnce.Do(func() {
		file_agent_uninstall_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_uninstall_proto_rawDescData)
	})
	return file_agent_uninstall_proto_rawDescData
}

var file_agent_uninstall_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_uninstall_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_uninstall_proto_goTypes = []any{
	(UninstallAgentErrorCode)(0), // 0: agent.UninstallAgentErrorCode
	(*UninstallAgent)(nil),       // 1: agent.UninstallAgent
	(*UninstallAgentResult)(nil), // 2: agent.UninstallAgentResult
}
var file_agent_uninstall_proto_depIdxs = []int32{
	0, // 0: agent.UninstallAgentResult.errorCode:type_name -> agent.UninstallAgentErrorCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_agent_uninstall_proto_init() }
func file_agent_uninstall_proto_init() {
	if File_agent_uninstall_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_uninstall_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_uninstall_proto_goTypes,
		DependencyIndexes: file_agent_uninstall_proto_depIdxs,
		EnumInfos:         file_agent_uninstall_proto_enumTypes,
		MessageInfos:      file_agent_uninstall_proto_msgTypes,
	}.Build()
	File_agent_uninstall_proto = out.File
	file_agent_uninstall_proto_rawDesc = nil
	file_agent_uninstall_proto_goTypes = nil
	file_agent_uninstall_proto_depIdxs = nil
}
