// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/file_snapshot.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SnapshotReportType int32

const (
	SnapshotReportType_SR_UNKNOWN SnapshotReportType = 0
	SnapshotReportType_SR_CREATE  SnapshotReportType = 1 // 增加文件
	SnapshotReportType_SR_DELETE  SnapshotReportType = 2 // 删除文件
	SnapshotReportType_SR_MODIFY  SnapshotReportType = 3 // 修改文件
)

// Enum value maps for SnapshotReportType.
var (
	SnapshotReportType_name = map[int32]string{
		0: "SR_UNKNOWN",
		1: "SR_CREATE",
		2: "SR_DELETE",
		3: "SR_MODIFY",
	}
	SnapshotReportType_value = map[string]int32{
		"SR_UNKNOWN": 0,
		"SR_CREATE":  1,
		"SR_DELETE":  2,
		"SR_MODIFY":  3,
	}
)

func (x SnapshotReportType) Enum() *SnapshotReportType {
	p := new(SnapshotReportType)
	*p = x
	return p
}

func (x SnapshotReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SnapshotReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_file_snapshot_proto_enumTypes[0].Descriptor()
}

func (SnapshotReportType) Type() protoreflect.EnumType {
	return &file_agent_file_snapshot_proto_enumTypes[0]
}

func (x SnapshotReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SnapshotReportType.Descriptor instead.
func (SnapshotReportType) EnumDescriptor() ([]byte, []int) {
	return file_agent_file_snapshot_proto_rawDescGZIP(), []int{0}
}

type FileSnapshotInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version            uint32                    `protobuf:"varint,1,opt,name=Version,proto3" json:"Version,omitempty"`         // 版本
	Timestamp          int64                     `protobuf:"varint,2,opt,name=Timestamp,proto3" json:"Timestamp,omitempty"`     // 采集时间戳
	FileSeqNo          int64                     `protobuf:"varint,3,opt,name=FileSeqNo,proto3" json:"FileSeqNo,omitempty"`     // 文件上传序号
	FileTotalNo        int64                     `protobuf:"varint,4,opt,name=FileTotalNo,proto3" json:"FileTotalNo,omitempty"` // 文件总上传量
	MachineID          []byte                    `protobuf:"bytes,5,opt,name=MachineID,proto3" json:"MachineID,omitempty"`      // 主机ID
	OSType             int32                     `protobuf:"varint,6,opt,name=OSType,proto3" json:"OSType,omitempty"`           // 操作系统类型
	Infos              []*FileSnapshotInfo       `protobuf:"bytes,7,rep,name=infos,proto3" json:"infos,omitempty"`
	SignInfoDictionary map[string]*SignatureInfo `protobuf:"bytes,8,rep,name=SignInfoDictionary,proto3" json:"SignInfoDictionary,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // 签名字典表
}

func (x *FileSnapshotInfos) Reset() {
	*x = FileSnapshotInfos{}
	mi := &file_agent_file_snapshot_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSnapshotInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSnapshotInfos) ProtoMessage() {}

func (x *FileSnapshotInfos) ProtoReflect() protoreflect.Message {
	mi := &file_agent_file_snapshot_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSnapshotInfos.ProtoReflect.Descriptor instead.
func (*FileSnapshotInfos) Descriptor() ([]byte, []int) {
	return file_agent_file_snapshot_proto_rawDescGZIP(), []int{0}
}

func (x *FileSnapshotInfos) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *FileSnapshotInfos) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *FileSnapshotInfos) GetFileSeqNo() int64 {
	if x != nil {
		return x.FileSeqNo
	}
	return 0
}

func (x *FileSnapshotInfos) GetFileTotalNo() int64 {
	if x != nil {
		return x.FileTotalNo
	}
	return 0
}

func (x *FileSnapshotInfos) GetMachineID() []byte {
	if x != nil {
		return x.MachineID
	}
	return nil
}

func (x *FileSnapshotInfos) GetOSType() int32 {
	if x != nil {
		return x.OSType
	}
	return 0
}

func (x *FileSnapshotInfos) GetInfos() []*FileSnapshotInfo {
	if x != nil {
		return x.Infos
	}
	return nil
}

func (x *FileSnapshotInfos) GetSignInfoDictionary() map[string]*SignatureInfo {
	if x != nil {
		return x.SignInfoDictionary
	}
	return nil
}

type FileSnapshotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType       SnapshotReportType `protobuf:"varint,1,opt,name=ReportType,proto3,enum=agent.SnapshotReportType" json:"ReportType,omitempty"` // 上报类型
	DirctoryInfoList []*DirectoryInfo   `protobuf:"bytes,2,rep,name=DirctoryInfoList,proto3" json:"DirctoryInfoList,omitempty"`                    // 目录信息列表
}

func (x *FileSnapshotInfo) Reset() {
	*x = FileSnapshotInfo{}
	mi := &file_agent_file_snapshot_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSnapshotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSnapshotInfo) ProtoMessage() {}

func (x *FileSnapshotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_file_snapshot_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSnapshotInfo.ProtoReflect.Descriptor instead.
func (*FileSnapshotInfo) Descriptor() ([]byte, []int) {
	return file_agent_file_snapshot_proto_rawDescGZIP(), []int{1}
}

func (x *FileSnapshotInfo) GetReportType() SnapshotReportType {
	if x != nil {
		return x.ReportType
	}
	return SnapshotReportType_SR_UNKNOWN
}

func (x *FileSnapshotInfo) GetDirctoryInfoList() []*DirectoryInfo {
	if x != nil {
		return x.DirctoryInfoList
	}
	return nil
}

type DirectoryInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DirectoryName string       `protobuf:"bytes,1,opt,name=DirectoryName,proto3" json:"DirectoryName,omitempty"` // 目录名, 如: C:\windows\system32
	FileInfoList  []*FileInfos `protobuf:"bytes,2,rep,name=FileInfoList,proto3" json:"FileInfoList,omitempty"`   // 文件信息列表
}

func (x *DirectoryInfo) Reset() {
	*x = DirectoryInfo{}
	mi := &file_agent_file_snapshot_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DirectoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectoryInfo) ProtoMessage() {}

func (x *DirectoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_file_snapshot_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectoryInfo.ProtoReflect.Descriptor instead.
func (*DirectoryInfo) Descriptor() ([]byte, []int) {
	return file_agent_file_snapshot_proto_rawDescGZIP(), []int{2}
}

func (x *DirectoryInfo) GetDirectoryName() string {
	if x != nil {
		return x.DirectoryName
	}
	return ""
}

func (x *DirectoryInfo) GetFileInfoList() []*FileInfos {
	if x != nil {
		return x.FileInfoList
	}
	return nil
}

type FileInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName            string        `protobuf:"bytes,1,opt,name=FileName,proto3" json:"FileName,omitempty"`
	FileSize            int64         `protobuf:"varint,2,opt,name=FileSize,proto3" json:"FileSize,omitempty"`
	CreateTime          int64         `protobuf:"varint,3,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	ModifyTime          int64         `protobuf:"varint,4,opt,name=ModifyTime,proto3" json:"ModifyTime,omitempty"`
	LastAccessTime      int64         `protobuf:"varint,5,opt,name=LastAccessTime,proto3" json:"LastAccessTime,omitempty"`
	FileMD5             []byte        `protobuf:"bytes,6,opt,name=FileMD5,proto3" json:"FileMD5,omitempty"`
	FileSha1            []byte        `protobuf:"bytes,7,opt,name=FileSha1,proto3" json:"FileSha1,omitempty"`
	FileSha256          []byte        `protobuf:"bytes,8,opt,name=FileSha256,proto3" json:"FileSha256,omitempty"`
	FileType            FileTypeIdent `protobuf:"varint,9,opt,name=FileType,proto3,enum=agent.FileTypeIdent" json:"FileType,omitempty"`
	SignatureInfoSerial []string      `protobuf:"bytes,10,rep,name=SignatureInfoSerial,proto3" json:"SignatureInfoSerial,omitempty"`
	TlshHash            []byte        `protobuf:"bytes,11,opt,name=TlshHash,proto3" json:"TlshHash,omitempty"`
	ImpHash             []byte        `protobuf:"bytes,12,opt,name=ImpHash,proto3" json:"ImpHash,omitempty"`
}

func (x *FileInfos) Reset() {
	*x = FileInfos{}
	mi := &file_agent_file_snapshot_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfos) ProtoMessage() {}

func (x *FileInfos) ProtoReflect() protoreflect.Message {
	mi := &file_agent_file_snapshot_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfos.ProtoReflect.Descriptor instead.
func (*FileInfos) Descriptor() ([]byte, []int) {
	return file_agent_file_snapshot_proto_rawDescGZIP(), []int{3}
}

func (x *FileInfos) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileInfos) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileInfos) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *FileInfos) GetModifyTime() int64 {
	if x != nil {
		return x.ModifyTime
	}
	return 0
}

func (x *FileInfos) GetLastAccessTime() int64 {
	if x != nil {
		return x.LastAccessTime
	}
	return 0
}

func (x *FileInfos) GetFileMD5() []byte {
	if x != nil {
		return x.FileMD5
	}
	return nil
}

func (x *FileInfos) GetFileSha1() []byte {
	if x != nil {
		return x.FileSha1
	}
	return nil
}

func (x *FileInfos) GetFileSha256() []byte {
	if x != nil {
		return x.FileSha256
	}
	return nil
}

func (x *FileInfos) GetFileType() FileTypeIdent {
	if x != nil {
		return x.FileType
	}
	return FileTypeIdent_FILE_TYPE_UNKNOWN
}

func (x *FileInfos) GetSignatureInfoSerial() []string {
	if x != nil {
		return x.SignatureInfoSerial
	}
	return nil
}

func (x *FileInfos) GetTlshHash() []byte {
	if x != nil {
		return x.TlshHash
	}
	return nil
}

func (x *FileInfos) GetImpHash() []byte {
	if x != nil {
		return x.ImpHash
	}
	return nil
}

var File_agent_file_snapshot_proto protoreflect.FileDescriptor

var file_agent_file_snapshot_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6e, 0x61,
	0x70, 0x73, 0x68, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xaf, 0x03, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x71, 0x4e,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x71,
	0x4e, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x4e,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x4e, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49,
	0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x4f, 0x53, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x4f, 0x53, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x60, 0x0a, 0x12, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x2e,
	0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x72, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72, 0x79, 0x1a, 0x5b, 0x0a, 0x17, 0x53,
	0x69, 0x67, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x72,
	0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x8f, 0x01, 0x0a, 0x10, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a,
	0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x40, 0x0a, 0x10, 0x44, 0x69, 0x72, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x44, 0x69, 0x72, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6b, 0x0a, 0x0d, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x24, 0x0a, 0x0d, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x34, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9b, 0x03, 0x0a, 0x09, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x44, 0x35,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x44, 0x35, 0x12,
	0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x12, 0x1e, 0x0a, 0x0a, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x30, 0x0a, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a,
	0x13, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x69, 0x61, 0x6c, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x12,
	0x1a, 0x0a, 0x08, 0x54, 0x6c, 0x73, 0x68, 0x48, 0x61, 0x73, 0x68, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x54, 0x6c, 0x73, 0x68, 0x48, 0x61, 0x73, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x49,
	0x6d, 0x70, 0x48, 0x61, 0x73, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x49, 0x6d,
	0x70, 0x48, 0x61, 0x73, 0x68, 0x2a, 0x51, 0x0a, 0x12, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x53,
	0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53,
	0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x52,
	0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x52, 0x5f,
	0x4d, 0x4f, 0x44, 0x49, 0x46, 0x59, 0x10, 0x03, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e,
	0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_file_snapshot_proto_rawDescOnce sync.Once
	file_agent_file_snapshot_proto_rawDescData = file_agent_file_snapshot_proto_rawDesc
)

func file_agent_file_snapshot_proto_rawDescGZIP() []byte {
	file_agent_file_snapshot_proto_rawDescOnce.Do(func() {
		file_agent_file_snapshot_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_file_snapshot_proto_rawDescData)
	})
	return file_agent_file_snapshot_proto_rawDescData
}

var file_agent_file_snapshot_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_file_snapshot_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_agent_file_snapshot_proto_goTypes = []any{
	(SnapshotReportType)(0),   // 0: agent.SnapshotReportType
	(*FileSnapshotInfos)(nil), // 1: agent.FileSnapshotInfos
	(*FileSnapshotInfo)(nil),  // 2: agent.FileSnapshotInfo
	(*DirectoryInfo)(nil),     // 3: agent.DirectoryInfo
	(*FileInfos)(nil),         // 4: agent.FileInfos
	nil,                       // 5: agent.FileSnapshotInfos.SignInfoDictionaryEntry
	(FileTypeIdent)(0),        // 6: agent.FileTypeIdent
	(*SignatureInfo)(nil),     // 7: agent.SignatureInfo
}
var file_agent_file_snapshot_proto_depIdxs = []int32{
	2, // 0: agent.FileSnapshotInfos.infos:type_name -> agent.FileSnapshotInfo
	5, // 1: agent.FileSnapshotInfos.SignInfoDictionary:type_name -> agent.FileSnapshotInfos.SignInfoDictionaryEntry
	0, // 2: agent.FileSnapshotInfo.ReportType:type_name -> agent.SnapshotReportType
	3, // 3: agent.FileSnapshotInfo.DirctoryInfoList:type_name -> agent.DirectoryInfo
	4, // 4: agent.DirectoryInfo.FileInfoList:type_name -> agent.FileInfos
	6, // 5: agent.FileInfos.FileType:type_name -> agent.FileTypeIdent
	7, // 6: agent.FileSnapshotInfos.SignInfoDictionaryEntry.value:type_name -> agent.SignatureInfo
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_agent_file_snapshot_proto_init() }
func file_agent_file_snapshot_proto_init() {
	if File_agent_file_snapshot_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_file_snapshot_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_file_snapshot_proto_goTypes,
		DependencyIndexes: file_agent_file_snapshot_proto_depIdxs,
		EnumInfos:         file_agent_file_snapshot_proto_enumTypes,
		MessageInfos:      file_agent_file_snapshot_proto_msgTypes,
	}.Build()
	File_agent_file_snapshot_proto = out.File
	file_agent_file_snapshot_proto_rawDesc = nil
	file_agent_file_snapshot_proto_goTypes = nil
	file_agent_file_snapshot_proto_depIdxs = nil
}
