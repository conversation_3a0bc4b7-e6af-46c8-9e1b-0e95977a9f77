// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/pull.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PullReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PullReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PullReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in PullReqMultiError, or nil if none found.
func (m *PullReq) ValidateAll() error {
	return m.validate(true)
}

func (m *PullReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxUnicastSeq

	// no validation rules for MaxMulticastSeq

	// no validation rules for MaxBroadcastSeq

	// no validation rules for GroupId

	if len(errors) > 0 {
		return PullReqMultiError(errors)
	}

	return nil
}

// PullReqMultiError is an error wrapping multiple validation errors returned
// by PullReq.ValidateAll() if the designated constraints aren't met.
type PullReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PullReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PullReqMultiError) AllErrors() []error { return m }

// PullReqValidationError is the validation error returned by PullReq.Validate
// if the designated constraints aren't met.
type PullReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PullReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PullReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PullReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PullReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PullReqValidationError) ErrorName() string { return "PullReqValidationError" }

// Error satisfies the builtin error interface
func (e PullReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPullReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PullReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PullReqValidationError{}

// Validate checks the field values on PullResp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PullResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PullResp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PullRespMultiError, or nil
// if none found.
func (m *PullResp) ValidateAll() error {
	return m.validate(true)
}

func (m *PullResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxUnicastSeq

	// no validation rules for MaxMulticastSeq

	// no validation rules for MaxBroadcastSeq

	for idx, item := range m.GetContents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PullRespValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PullRespValidationError{
						field:  fmt.Sprintf("Contents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PullRespValidationError{
					field:  fmt.Sprintf("Contents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PullRespMultiError(errors)
	}

	return nil
}

// PullRespMultiError is an error wrapping multiple validation errors returned
// by PullResp.ValidateAll() if the designated constraints aren't met.
type PullRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PullRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PullRespMultiError) AllErrors() []error { return m }

// PullRespValidationError is the validation error returned by
// PullResp.Validate if the designated constraints aren't met.
type PullRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PullRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PullRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PullRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PullRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PullRespValidationError) ErrorName() string { return "PullRespValidationError" }

// Error satisfies the builtin error interface
func (e PullRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPullResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PullRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PullRespValidationError{}

// Validate checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Content) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Content with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ContentMultiError, or nil if none found.
func (m *Content) ValidateAll() error {
	return m.validate(true)
}

func (m *Content) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uuid

	// no validation rules for Category

	// no validation rules for Content

	// no validation rules for Size

	// no validation rules for Seq

	// no validation rules for Type

	// no validation rules for Os

	if len(errors) > 0 {
		return ContentMultiError(errors)
	}

	return nil
}

// ContentMultiError is an error wrapping multiple validation errors returned
// by Content.ValidateAll() if the designated constraints aren't met.
type ContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ContentMultiError) AllErrors() []error { return m }

// ContentValidationError is the validation error returned by Content.Validate
// if the designated constraints aren't met.
type ContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ContentValidationError) ErrorName() string { return "ContentValidationError" }

// Error satisfies the builtin error interface
func (e ContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ContentValidationError{}
