// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_mem.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MemNoFileAttack_MemNoFileAttackType int32

const (
	MemNoFileAttack_REFLECTION_INJECTION         MemNoFileAttack_MemNoFileAttackType = 0 //DLL反射攻击，当类别为反射攻击时，Target进程信息有效
	MemNoFileAttack_RUN_REMOTE_SCRIPT            MemNoFileAttack_MemNoFileAttackType = 1 //远程执行脚本，脚本文件不落地
	MemNoFileAttack_SCRIPT_SCHEDULE_ADD          MemNoFileAttack_MemNoFileAttackType = 2
	MemNoFileAttack_SCRIPT_SCHEDULE_DEL          MemNoFileAttack_MemNoFileAttackType = 3
	MemNoFileAttack_SCRIPT_SCHEDULE_CHANGE       MemNoFileAttack_MemNoFileAttackType = 4
	MemNoFileAttack_SCRIPT_FILECOPY              MemNoFileAttack_MemNoFileAttackType = 5
	MemNoFileAttack_SCRIPT_DIRCOPY               MemNoFileAttack_MemNoFileAttackType = 6
	MemNoFileAttack_SCRIPT_HTTP_GET              MemNoFileAttack_MemNoFileAttackType = 7
	MemNoFileAttack_SCRIPT_HTTP_POST             MemNoFileAttack_MemNoFileAttackType = 8
	MemNoFileAttack_SCRIPT_WMICMD                MemNoFileAttack_MemNoFileAttackType = 9
	MemNoFileAttack_SCRIPT_LOADDLL               MemNoFileAttack_MemNoFileAttackType = 10
	MemNoFileAttack_SCRIPT_GETADDRESS            MemNoFileAttack_MemNoFileAttackType = 11
	MemNoFileAttack_SCRIPT_FILEMOVE              MemNoFileAttack_MemNoFileAttackType = 12
	MemNoFileAttack_SCRIPT_DIRMOVE               MemNoFileAttack_MemNoFileAttackType = 13
	MemNoFileAttack_SCRIPT_LISTEN                MemNoFileAttack_MemNoFileAttackType = 14
	MemNoFileAttack_SCRIPT_SOCKET                MemNoFileAttack_MemNoFileAttackType = 15
	MemNoFileAttack_SCRIPT_WIN32_SHARE           MemNoFileAttack_MemNoFileAttackType = 16
	MemNoFileAttack_WEB_SHELL_FILE               MemNoFileAttack_MemNoFileAttackType = 17 //发现webshell文件
	MemNoFileAttack_WEB_SHELL_JAVA               MemNoFileAttack_MemNoFileAttackType = 18 //发现java webshell
	MemNoFileAttack_WMI_PERSISTENT_BACKDOOR      MemNoFileAttack_MemNoFileAttackType = 19 //wmi持久后门
	MemNoFileAttack_WEB_SHELL_JAVA1              MemNoFileAttack_MemNoFileAttackType = 20 //发现java webshell(实时监控)
	MemNoFileAttack_APPROT_WEB_SHELL_FILE_WRITE  MemNoFileAttack_MemNoFileAttackType = 21 //写敏感在文件，有效字段 SourceProcess、doc_name
	MemNoFileAttack_APPROT_WEB_SHELL_NET_LISTEN  MemNoFileAttack_MemNoFileAttackType = 22 //监听端口，有效字段 SourceProcess、TargetProcess、listen_port
	MemNoFileAttack_APPROT_WEB_SHELL_PROCESS_RUN MemNoFileAttack_MemNoFileAttackType = 23 //启动敏感子进程，有效字段 SourceProcess、TargetProcess
	MemNoFileAttack_SCRIPT_AMSI_BY_INIT_FAILED   MemNoFileAttack_MemNoFileAttackType = 24 //过AMSI保护-通过设置InitFailed字段     不需定义oneof detail中的结构体
	MemNoFileAttack_SCRIPT_AMSI_BY_AMSI_CONTEXT  MemNoFileAttack_MemNoFileAttackType = 25 //过AMSI保护-通过修改AMSICONTEXT结构体
	MemNoFileAttack_SCRIPT_AMSI_DLLHIJACK        MemNoFileAttack_MemNoFileAttackType = 26 //过AMSI保护-通过Dll劫持
	MemNoFileAttack_SCRIPT_KEY_LOGGER            MemNoFileAttack_MemNoFileAttackType = 27 //键盘记录器                            不需定义oneof detail中的结构体
	MemNoFileAttack_SCRIPT_SCREEN_SHOT           MemNoFileAttack_MemNoFileAttackType = 28 //屏幕截图                              不需定义oneof detail中的结构体
	MemNoFileAttack_SCRIPT_EMAIL                 MemNoFileAttack_MemNoFileAttackType = 29 //发送邮件
	MemNoFileAttack_WMI_TERMINATE_PROCESS        MemNoFileAttack_MemNoFileAttackType = 30 //结束进程
	MemNoFileAttack_WMI_REG_OPER                 MemNoFileAttack_MemNoFileAttackType = 31 //操作注册表
	MemNoFileAttack_WMI_SERVICE_OPER             MemNoFileAttack_MemNoFileAttackType = 32 //操作服务
	MemNoFileAttack_WMI_QUERY                    MemNoFileAttack_MemNoFileAttackType = 33 //执行查询
	MemNoFileAttack_PATCH_EVENTLOG_GEN_LOG       MemNoFileAttack_MemNoFileAttackType = 34 //Patch Eventlog GenLog
)

// Enum value maps for MemNoFileAttack_MemNoFileAttackType.
var (
	MemNoFileAttack_MemNoFileAttackType_name = map[int32]string{
		0:  "REFLECTION_INJECTION",
		1:  "RUN_REMOTE_SCRIPT",
		2:  "SCRIPT_SCHEDULE_ADD",
		3:  "SCRIPT_SCHEDULE_DEL",
		4:  "SCRIPT_SCHEDULE_CHANGE",
		5:  "SCRIPT_FILECOPY",
		6:  "SCRIPT_DIRCOPY",
		7:  "SCRIPT_HTTP_GET",
		8:  "SCRIPT_HTTP_POST",
		9:  "SCRIPT_WMICMD",
		10: "SCRIPT_LOADDLL",
		11: "SCRIPT_GETADDRESS",
		12: "SCRIPT_FILEMOVE",
		13: "SCRIPT_DIRMOVE",
		14: "SCRIPT_LISTEN",
		15: "SCRIPT_SOCKET",
		16: "SCRIPT_WIN32_SHARE",
		17: "WEB_SHELL_FILE",
		18: "WEB_SHELL_JAVA",
		19: "WMI_PERSISTENT_BACKDOOR",
		20: "WEB_SHELL_JAVA1",
		21: "APPROT_WEB_SHELL_FILE_WRITE",
		22: "APPROT_WEB_SHELL_NET_LISTEN",
		23: "APPROT_WEB_SHELL_PROCESS_RUN",
		24: "SCRIPT_AMSI_BY_INIT_FAILED",
		25: "SCRIPT_AMSI_BY_AMSI_CONTEXT",
		26: "SCRIPT_AMSI_DLLHIJACK",
		27: "SCRIPT_KEY_LOGGER",
		28: "SCRIPT_SCREEN_SHOT",
		29: "SCRIPT_EMAIL",
		30: "WMI_TERMINATE_PROCESS",
		31: "WMI_REG_OPER",
		32: "WMI_SERVICE_OPER",
		33: "WMI_QUERY",
		34: "PATCH_EVENTLOG_GEN_LOG",
	}
	MemNoFileAttack_MemNoFileAttackType_value = map[string]int32{
		"REFLECTION_INJECTION":         0,
		"RUN_REMOTE_SCRIPT":            1,
		"SCRIPT_SCHEDULE_ADD":          2,
		"SCRIPT_SCHEDULE_DEL":          3,
		"SCRIPT_SCHEDULE_CHANGE":       4,
		"SCRIPT_FILECOPY":              5,
		"SCRIPT_DIRCOPY":               6,
		"SCRIPT_HTTP_GET":              7,
		"SCRIPT_HTTP_POST":             8,
		"SCRIPT_WMICMD":                9,
		"SCRIPT_LOADDLL":               10,
		"SCRIPT_GETADDRESS":            11,
		"SCRIPT_FILEMOVE":              12,
		"SCRIPT_DIRMOVE":               13,
		"SCRIPT_LISTEN":                14,
		"SCRIPT_SOCKET":                15,
		"SCRIPT_WIN32_SHARE":           16,
		"WEB_SHELL_FILE":               17,
		"WEB_SHELL_JAVA":               18,
		"WMI_PERSISTENT_BACKDOOR":      19,
		"WEB_SHELL_JAVA1":              20,
		"APPROT_WEB_SHELL_FILE_WRITE":  21,
		"APPROT_WEB_SHELL_NET_LISTEN":  22,
		"APPROT_WEB_SHELL_PROCESS_RUN": 23,
		"SCRIPT_AMSI_BY_INIT_FAILED":   24,
		"SCRIPT_AMSI_BY_AMSI_CONTEXT":  25,
		"SCRIPT_AMSI_DLLHIJACK":        26,
		"SCRIPT_KEY_LOGGER":            27,
		"SCRIPT_SCREEN_SHOT":           28,
		"SCRIPT_EMAIL":                 29,
		"WMI_TERMINATE_PROCESS":        30,
		"WMI_REG_OPER":                 31,
		"WMI_SERVICE_OPER":             32,
		"WMI_QUERY":                    33,
		"PATCH_EVENTLOG_GEN_LOG":       34,
	}
)

func (x MemNoFileAttack_MemNoFileAttackType) Enum() *MemNoFileAttack_MemNoFileAttackType {
	p := new(MemNoFileAttack_MemNoFileAttackType)
	*p = x
	return p
}

func (x MemNoFileAttack_MemNoFileAttackType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MemNoFileAttack_MemNoFileAttackType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[0].Descriptor()
}

func (MemNoFileAttack_MemNoFileAttackType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[0]
}

func (x MemNoFileAttack_MemNoFileAttackType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MemNoFileAttack_MemNoFileAttackType.Descriptor instead.
func (MemNoFileAttack_MemNoFileAttackType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{6, 0}
}

type NoFileAttackWebShellJava1_DetailInfoType int32

const (
	NoFileAttackWebShellJava1_JVM_RUN_CMD                   NoFileAttackWebShellJava1_DetailInfoType = 0  // JVM进程执行外部命令
	NoFileAttackWebShellJava1_JVM_LOAD_SO                   NoFileAttackWebShellJava1_DetailInfoType = 1  // JVM进程加载.so库
	NoFileAttackWebShellJava1_JVM_DRAG_LIB                  NoFileAttackWebShellJava1_DetailInfoType = 2  // JVM进程执行拖库行为
	NoFileAttackWebShellJava1_JVM_UPLOADINGFILE             NoFileAttackWebShellJava1_DetailInfoType = 3  // JVM文件上传行为
	NoFileAttackWebShellJava1_PHP_CALLABLE                  NoFileAttackWebShellJava1_DetailInfoType = 4  // PHP回调行为
	NoFileAttackWebShellJava1_PHP_FILE_OP                   NoFileAttackWebShellJava1_DetailInfoType = 5  // PHP文件操作
	NoFileAttackWebShellJava1_PHP_FILEUPLOAD                NoFileAttackWebShellJava1_DetailInfoType = 6  // PHP文件上传
	NoFileAttackWebShellJava1_PHP_DB                        NoFileAttackWebShellJava1_DetailInfoType = 7  // PHP数据库操作
	NoFileAttackWebShellJava1_PHP_ENV                       NoFileAttackWebShellJava1_DetailInfoType = 10 // PHP Env操作
	NoFileAttackWebShellJava1_PHP_SENSE_API                 NoFileAttackWebShellJava1_DetailInfoType = 11 // PHP敏感API调用
	NoFileAttackWebShellJava1_JVM_REGISTER_SERVER_COMPONENT NoFileAttackWebShellJava1_DetailInfoType = 12 // JVM 注册服务器组件
	NoFileAttackWebShellJava1_JVM_SERVER_WEBSHELL_SCAN      NoFileAttackWebShellJava1_DetailInfoType = 13 // JVM 应用服务器Webshell扫描；
	NoFileAttackWebShellJava1_JVM_SERVER_COMPONENT_SCAN     NoFileAttackWebShellJava1_DetailInfoType = 14 // JVM 应用服务器已注册组件扫描；
	NoFileAttackWebShellJava1_JVM_FILE_OP                   NoFileAttackWebShellJava1_DetailInfoType = 15 // java 读写敏感文件
	NoFileAttackWebShellJava1_JVM_VIR_CMD                   NoFileAttackWebShellJava1_DetailInfoType = 16 // java 虚拟终端
	NoFileAttackWebShellJava1_JVM_SCAN_PORT                 NoFileAttackWebShellJava1_DetailInfoType = 17
	NoFileAttackWebShellJava1_JVM_JAR_LOAD                  NoFileAttackWebShellJava1_DetailInfoType = 18
	NoFileAttackWebShellJava1_JVM_ZIP                       NoFileAttackWebShellJava1_DetailInfoType = 19
	NoFileAttackWebShellJava1_JVM_UNZIP                     NoFileAttackWebShellJava1_DetailInfoType = 20
	NoFileAttackWebShellJava1_JVM_HTTP_PARAMS_CHECK         NoFileAttackWebShellJava1_DetailInfoType = 21
	NoFileAttackWebShellJava1_JVM_SQL_INJECT                NoFileAttackWebShellJava1_DetailInfoType = 22 // java sql inject
	NoFileAttackWebShellJava1_JVM_XXE                       NoFileAttackWebShellJava1_DetailInfoType = 23 // XXE
	NoFileAttackWebShellJava1_JVM_DNSLOG                    NoFileAttackWebShellJava1_DetailInfoType = 24 // DNSLog
	NoFileAttackWebShellJava1_JVM_SSRF                      NoFileAttackWebShellJava1_DetailInfoType = 25 // SSRF
	NoFileAttackWebShellJava1_JVM_DIRECTORY_OP              NoFileAttackWebShellJava1_DetailInfoType = 26
	NoFileAttackWebShellJava1_JVM_Black_IP                  NoFileAttackWebShellJava1_DetailInfoType = 27
	NoFileAttackWebShellJava1_JVM_CSRF                      NoFileAttackWebShellJava1_DetailInfoType = 29
	NoFileAttackWebShellJava1_JVM_UNSAFE_REDIRECT           NoFileAttackWebShellJava1_DetailInfoType = 30
	NoFileAttackWebShellJava1_PHP_UNKNOWN                   NoFileAttackWebShellJava1_DetailInfoType = 200 //未知
	NoFileAttackWebShellJava1_PHP_SYSTEM_COMMAND            NoFileAttackWebShellJava1_DetailInfoType = 201 //系统命令执行
	NoFileAttackWebShellJava1_PHP_CODE_EXEC                 NoFileAttackWebShellJava1_DetailInfoType = 202 //PHP代码执行
	NoFileAttackWebShellJava1_PHP_CALLBACK_FUNC             NoFileAttackWebShellJava1_DetailInfoType = 203 //回调函数利用
	NoFileAttackWebShellJava1_PHP_UPLOAD_SUSPICIOUS_FILES   NoFileAttackWebShellJava1_DetailInfoType = 204 //上传可疑文件
	NoFileAttackWebShellJava1_PHP_SENSITIVE_FILE_ACCESS     NoFileAttackWebShellJava1_DetailInfoType = 205 //敏感文件访问
	NoFileAttackWebShellJava1_PHP_EXEC_SUSPICIOUS_FILE      NoFileAttackWebShellJava1_DetailInfoType = 206 //执行可疑文件
	NoFileAttackWebShellJava1_PHP_XSS                       NoFileAttackWebShellJava1_DetailInfoType = 207 // XSS
	NoFileAttackWebShellJava1_PHP_SQLI                      NoFileAttackWebShellJava1_DetailInfoType = 208 // SQL 注入
	NoFileAttackWebShellJava1_PHP_SQL_DRAG                  NoFileAttackWebShellJava1_DetailInfoType = 209 // SQL拖库
	NoFileAttackWebShellJava1_PHP_UNDEAD_HORSE              NoFileAttackWebShellJava1_DetailInfoType = 210 // PHP不死马 (新增)
	NoFileAttackWebShellJava1_PHP_SSRF                      NoFileAttackWebShellJava1_DetailInfoType = 211 // PHP SSRF攻击
	NoFileAttackWebShellJava1_PHP_XXE                       NoFileAttackWebShellJava1_DetailInfoType = 212 // PHP XXE
	NoFileAttackWebShellJava1_GO_RCE                        NoFileAttackWebShellJava1_DetailInfoType = 300 // GO RCE
)

// Enum value maps for NoFileAttackWebShellJava1_DetailInfoType.
var (
	NoFileAttackWebShellJava1_DetailInfoType_name = map[int32]string{
		0:   "JVM_RUN_CMD",
		1:   "JVM_LOAD_SO",
		2:   "JVM_DRAG_LIB",
		3:   "JVM_UPLOADINGFILE",
		4:   "PHP_CALLABLE",
		5:   "PHP_FILE_OP",
		6:   "PHP_FILEUPLOAD",
		7:   "PHP_DB",
		10:  "PHP_ENV",
		11:  "PHP_SENSE_API",
		12:  "JVM_REGISTER_SERVER_COMPONENT",
		13:  "JVM_SERVER_WEBSHELL_SCAN",
		14:  "JVM_SERVER_COMPONENT_SCAN",
		15:  "JVM_FILE_OP",
		16:  "JVM_VIR_CMD",
		17:  "JVM_SCAN_PORT",
		18:  "JVM_JAR_LOAD",
		19:  "JVM_ZIP",
		20:  "JVM_UNZIP",
		21:  "JVM_HTTP_PARAMS_CHECK",
		22:  "JVM_SQL_INJECT",
		23:  "JVM_XXE",
		24:  "JVM_DNSLOG",
		25:  "JVM_SSRF",
		26:  "JVM_DIRECTORY_OP",
		27:  "JVM_Black_IP",
		29:  "JVM_CSRF",
		30:  "JVM_UNSAFE_REDIRECT",
		200: "PHP_UNKNOWN",
		201: "PHP_SYSTEM_COMMAND",
		202: "PHP_CODE_EXEC",
		203: "PHP_CALLBACK_FUNC",
		204: "PHP_UPLOAD_SUSPICIOUS_FILES",
		205: "PHP_SENSITIVE_FILE_ACCESS",
		206: "PHP_EXEC_SUSPICIOUS_FILE",
		207: "PHP_XSS",
		208: "PHP_SQLI",
		209: "PHP_SQL_DRAG",
		210: "PHP_UNDEAD_HORSE",
		211: "PHP_SSRF",
		212: "PHP_XXE",
		300: "GO_RCE",
	}
	NoFileAttackWebShellJava1_DetailInfoType_value = map[string]int32{
		"JVM_RUN_CMD":                   0,
		"JVM_LOAD_SO":                   1,
		"JVM_DRAG_LIB":                  2,
		"JVM_UPLOADINGFILE":             3,
		"PHP_CALLABLE":                  4,
		"PHP_FILE_OP":                   5,
		"PHP_FILEUPLOAD":                6,
		"PHP_DB":                        7,
		"PHP_ENV":                       10,
		"PHP_SENSE_API":                 11,
		"JVM_REGISTER_SERVER_COMPONENT": 12,
		"JVM_SERVER_WEBSHELL_SCAN":      13,
		"JVM_SERVER_COMPONENT_SCAN":     14,
		"JVM_FILE_OP":                   15,
		"JVM_VIR_CMD":                   16,
		"JVM_SCAN_PORT":                 17,
		"JVM_JAR_LOAD":                  18,
		"JVM_ZIP":                       19,
		"JVM_UNZIP":                     20,
		"JVM_HTTP_PARAMS_CHECK":         21,
		"JVM_SQL_INJECT":                22,
		"JVM_XXE":                       23,
		"JVM_DNSLOG":                    24,
		"JVM_SSRF":                      25,
		"JVM_DIRECTORY_OP":              26,
		"JVM_Black_IP":                  27,
		"JVM_CSRF":                      29,
		"JVM_UNSAFE_REDIRECT":           30,
		"PHP_UNKNOWN":                   200,
		"PHP_SYSTEM_COMMAND":            201,
		"PHP_CODE_EXEC":                 202,
		"PHP_CALLBACK_FUNC":             203,
		"PHP_UPLOAD_SUSPICIOUS_FILES":   204,
		"PHP_SENSITIVE_FILE_ACCESS":     205,
		"PHP_EXEC_SUSPICIOUS_FILE":      206,
		"PHP_XSS":                       207,
		"PHP_SQLI":                      208,
		"PHP_SQL_DRAG":                  209,
		"PHP_UNDEAD_HORSE":              210,
		"PHP_SSRF":                      211,
		"PHP_XXE":                       212,
		"GO_RCE":                        300,
	}
)

func (x NoFileAttackWebShellJava1_DetailInfoType) Enum() *NoFileAttackWebShellJava1_DetailInfoType {
	p := new(NoFileAttackWebShellJava1_DetailInfoType)
	*p = x
	return p
}

func (x NoFileAttackWebShellJava1_DetailInfoType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NoFileAttackWebShellJava1_DetailInfoType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[1].Descriptor()
}

func (NoFileAttackWebShellJava1_DetailInfoType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[1]
}

func (x NoFileAttackWebShellJava1_DetailInfoType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NoFileAttackWebShellJava1_DetailInfoType.Descriptor instead.
func (NoFileAttackWebShellJava1_DetailInfoType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{29, 0}
}

type KernelVulnerableDriverRiskInfo_DriverRiskType int32

const (
	KernelVulnerableDriverRiskInfo_DRIVER_RISK_TYPE_UNKNOWN    KernelVulnerableDriverRiskInfo_DriverRiskType = 0
	KernelVulnerableDriverRiskInfo_DRIVER_RISK_TYPE_MALICIOUS  KernelVulnerableDriverRiskInfo_DriverRiskType = 1
	KernelVulnerableDriverRiskInfo_DRIVER_RISK_TYPE_VULNERABLE KernelVulnerableDriverRiskInfo_DriverRiskType = 2
)

// Enum value maps for KernelVulnerableDriverRiskInfo_DriverRiskType.
var (
	KernelVulnerableDriverRiskInfo_DriverRiskType_name = map[int32]string{
		0: "DRIVER_RISK_TYPE_UNKNOWN",
		1: "DRIVER_RISK_TYPE_MALICIOUS",
		2: "DRIVER_RISK_TYPE_VULNERABLE",
	}
	KernelVulnerableDriverRiskInfo_DriverRiskType_value = map[string]int32{
		"DRIVER_RISK_TYPE_UNKNOWN":    0,
		"DRIVER_RISK_TYPE_MALICIOUS":  1,
		"DRIVER_RISK_TYPE_VULNERABLE": 2,
	}
)

func (x KernelVulnerableDriverRiskInfo_DriverRiskType) Enum() *KernelVulnerableDriverRiskInfo_DriverRiskType {
	p := new(KernelVulnerableDriverRiskInfo_DriverRiskType)
	*p = x
	return p
}

func (x KernelVulnerableDriverRiskInfo_DriverRiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KernelVulnerableDriverRiskInfo_DriverRiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[2].Descriptor()
}

func (KernelVulnerableDriverRiskInfo_DriverRiskType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[2]
}

func (x KernelVulnerableDriverRiskInfo_DriverRiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KernelVulnerableDriverRiskInfo_DriverRiskType.Descriptor instead.
func (KernelVulnerableDriverRiskInfo_DriverRiskType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{34, 0}
}

type TunnelRiskInfo_TunnelType int32

const (
	TunnelRiskInfo_TUNNEL_TYPE_ICMP TunnelRiskInfo_TunnelType = 0 // ICMP隧道
	TunnelRiskInfo_TUNNEL_TYPE_DNS  TunnelRiskInfo_TunnelType = 1 // DNS隧道
	TunnelRiskInfo_TUNNEL_TYPE_TCP  TunnelRiskInfo_TunnelType = 2 // TCP三次握手隧道
)

// Enum value maps for TunnelRiskInfo_TunnelType.
var (
	TunnelRiskInfo_TunnelType_name = map[int32]string{
		0: "TUNNEL_TYPE_ICMP",
		1: "TUNNEL_TYPE_DNS",
		2: "TUNNEL_TYPE_TCP",
	}
	TunnelRiskInfo_TunnelType_value = map[string]int32{
		"TUNNEL_TYPE_ICMP": 0,
		"TUNNEL_TYPE_DNS":  1,
		"TUNNEL_TYPE_TCP":  2,
	}
)

func (x TunnelRiskInfo_TunnelType) Enum() *TunnelRiskInfo_TunnelType {
	p := new(TunnelRiskInfo_TunnelType)
	*p = x
	return p
}

func (x TunnelRiskInfo_TunnelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TunnelRiskInfo_TunnelType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[3].Descriptor()
}

func (TunnelRiskInfo_TunnelType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[3]
}

func (x TunnelRiskInfo_TunnelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TunnelRiskInfo_TunnelType.Descriptor instead.
func (TunnelRiskInfo_TunnelType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{36, 0}
}

// 可疑信息
type TUNNEL_INFO_ICMP_IcmpDetectType int32

const (
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_NORMAL           TUNNEL_INFO_ICMP_IcmpDetectType = 0  // 0
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_HIGH_FREQUENCY   TUNNEL_INFO_ICMP_IcmpDetectType = 1  // 2^0 icmp高频发包
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_KEYWORD          TUNNEL_INFO_ICMP_IcmpDetectType = 2  // 2^1 检出关键字
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_BIG_PACKET       TUNNEL_INFO_ICMP_IcmpDetectType = 4  // 2^2 icmp大包
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_NO_STAMP         TUNNEL_INFO_ICMP_IcmpDetectType = 8  // 2^3 icmp 没有时间戳
	TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_ABNORMAL_ENTROPY TUNNEL_INFO_ICMP_IcmpDetectType = 16 // 2^4 icmp data字段熵异常
)

// Enum value maps for TUNNEL_INFO_ICMP_IcmpDetectType.
var (
	TUNNEL_INFO_ICMP_IcmpDetectType_name = map[int32]string{
		0:  "ICMP_DETECT_TYPE_ICMP_NORMAL",
		1:  "ICMP_DETECT_TYPE_ICMP_HIGH_FREQUENCY",
		2:  "ICMP_DETECT_TYPE_ICMP_KEYWORD",
		4:  "ICMP_DETECT_TYPE_ICMP_BIG_PACKET",
		8:  "ICMP_DETECT_TYPE_ICMP_NO_STAMP",
		16: "ICMP_DETECT_TYPE_ICMP_ABNORMAL_ENTROPY",
	}
	TUNNEL_INFO_ICMP_IcmpDetectType_value = map[string]int32{
		"ICMP_DETECT_TYPE_ICMP_NORMAL":           0,
		"ICMP_DETECT_TYPE_ICMP_HIGH_FREQUENCY":   1,
		"ICMP_DETECT_TYPE_ICMP_KEYWORD":          2,
		"ICMP_DETECT_TYPE_ICMP_BIG_PACKET":       4,
		"ICMP_DETECT_TYPE_ICMP_NO_STAMP":         8,
		"ICMP_DETECT_TYPE_ICMP_ABNORMAL_ENTROPY": 16,
	}
)

func (x TUNNEL_INFO_ICMP_IcmpDetectType) Enum() *TUNNEL_INFO_ICMP_IcmpDetectType {
	p := new(TUNNEL_INFO_ICMP_IcmpDetectType)
	*p = x
	return p
}

func (x TUNNEL_INFO_ICMP_IcmpDetectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TUNNEL_INFO_ICMP_IcmpDetectType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[4].Descriptor()
}

func (TUNNEL_INFO_ICMP_IcmpDetectType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[4]
}

func (x TUNNEL_INFO_ICMP_IcmpDetectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TUNNEL_INFO_ICMP_IcmpDetectType.Descriptor instead.
func (TUNNEL_INFO_ICMP_IcmpDetectType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{37, 0}
}

// 可疑信息
type TUNNEL_INFO_DNS_DnsDetectType int32

const (
	TUNNEL_INFO_DNS_DNS_DETECT_TYPE_DNS_NORMAL                  TUNNEL_INFO_DNS_DnsDetectType = 0 // 0
	TUNNEL_INFO_DNS_DNS_DETECT_TYPE_DNS_HIGH_FREQUENCY          TUNNEL_INFO_DNS_DnsDetectType = 1 // 2^0 dns高频发包
	TUNNEL_INFO_DNS_DNS_DETECT_TYPE_DNS_ANS_ALWAYS_LESS_EQUAL_1 TUNNEL_INFO_DNS_DnsDetectType = 2 // 2^1 dns回答数<=1
	TUNNEL_INFO_DNS_DNS_DETECT_TYPE_DNS_QUERY_LONG              TUNNEL_INFO_DNS_DnsDetectType = 4 // 2^2 dns查询长
)

// Enum value maps for TUNNEL_INFO_DNS_DnsDetectType.
var (
	TUNNEL_INFO_DNS_DnsDetectType_name = map[int32]string{
		0: "DNS_DETECT_TYPE_DNS_NORMAL",
		1: "DNS_DETECT_TYPE_DNS_HIGH_FREQUENCY",
		2: "DNS_DETECT_TYPE_DNS_ANS_ALWAYS_LESS_EQUAL_1",
		4: "DNS_DETECT_TYPE_DNS_QUERY_LONG",
	}
	TUNNEL_INFO_DNS_DnsDetectType_value = map[string]int32{
		"DNS_DETECT_TYPE_DNS_NORMAL":                  0,
		"DNS_DETECT_TYPE_DNS_HIGH_FREQUENCY":          1,
		"DNS_DETECT_TYPE_DNS_ANS_ALWAYS_LESS_EQUAL_1": 2,
		"DNS_DETECT_TYPE_DNS_QUERY_LONG":              4,
	}
)

func (x TUNNEL_INFO_DNS_DnsDetectType) Enum() *TUNNEL_INFO_DNS_DnsDetectType {
	p := new(TUNNEL_INFO_DNS_DnsDetectType)
	*p = x
	return p
}

func (x TUNNEL_INFO_DNS_DnsDetectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TUNNEL_INFO_DNS_DnsDetectType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[5].Descriptor()
}

func (TUNNEL_INFO_DNS_DnsDetectType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[5]
}

func (x TUNNEL_INFO_DNS_DnsDetectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TUNNEL_INFO_DNS_DnsDetectType.Descriptor instead.
func (TUNNEL_INFO_DNS_DnsDetectType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{40, 0}
}

// 可疑信息
type TUNNEL_INFO_TCP_TcpDetectType int32

const (
	TUNNEL_INFO_TCP_TCP_DETECT_TYPE_TCP_NORMAL               TUNNEL_INFO_TCP_TcpDetectType = 0 // 0
	TUNNEL_INFO_TCP_TCP_DETECT_TYPE_TCP_HIGH_FREQUENCY       TUNNEL_INFO_TCP_TcpDetectType = 1 // 2^0 tcp高频发包
	TUNNEL_INFO_TCP_TCP_DETECT_TYPE_TCP_WITH_ADDITIONAL_DATA TUNNEL_INFO_TCP_TcpDetectType = 2 // 2^1 tcp syn包携带额外数据
)

// Enum value maps for TUNNEL_INFO_TCP_TcpDetectType.
var (
	TUNNEL_INFO_TCP_TcpDetectType_name = map[int32]string{
		0: "TCP_DETECT_TYPE_TCP_NORMAL",
		1: "TCP_DETECT_TYPE_TCP_HIGH_FREQUENCY",
		2: "TCP_DETECT_TYPE_TCP_WITH_ADDITIONAL_DATA",
	}
	TUNNEL_INFO_TCP_TcpDetectType_value = map[string]int32{
		"TCP_DETECT_TYPE_TCP_NORMAL":               0,
		"TCP_DETECT_TYPE_TCP_HIGH_FREQUENCY":       1,
		"TCP_DETECT_TYPE_TCP_WITH_ADDITIONAL_DATA": 2,
	}
)

func (x TUNNEL_INFO_TCP_TcpDetectType) Enum() *TUNNEL_INFO_TCP_TcpDetectType {
	p := new(TUNNEL_INFO_TCP_TcpDetectType)
	*p = x
	return p
}

func (x TUNNEL_INFO_TCP_TcpDetectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TUNNEL_INFO_TCP_TcpDetectType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_mem_proto_enumTypes[6].Descriptor()
}

func (TUNNEL_INFO_TCP_TcpDetectType) Type() protoreflect.EnumType {
	return &file_agent_risk_mem_proto_enumTypes[6]
}

func (x TUNNEL_INFO_TCP_TcpDetectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TUNNEL_INFO_TCP_TcpDetectType.Descriptor instead.
func (TUNNEL_INFO_TCP_TcpDetectType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{41, 0}
}

// --------------------------------------------------
//
//	内存风险 识别结果
//	对应 g_CmdMemProtectRiskMemInfo
//
// --------------------------------------------------
type MemProtectRiskMemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo                       *ClientID                         `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	LoadRemoteLibList              []*LoadRemoteLib                  `protobuf:"bytes,2,rep,name=loadRemoteLibList,proto3" json:"loadRemoteLibList,omitempty"`
	VirProtectExStkSpaceList       []*VirProtectExStkSpace           `protobuf:"bytes,3,rep,name=virProtectExStkSpaceList,proto3" json:"virProtectExStkSpaceList,omitempty"`
	ReturnStackExecList            []*ReturnStackExec                `protobuf:"bytes,4,rep,name=returnStackExecList,proto3" json:"returnStackExecList,omitempty"`
	RemoteThreadInject             []*RemoteThread                   `protobuf:"bytes,5,rep,name=remoteThreadInject,proto3" json:"remoteThreadInject,omitempty"`
	MemNoFileAttackList            []*MemNoFileAttack                `protobuf:"bytes,6,rep,name=memNoFileAttackList,proto3" json:"memNoFileAttackList,omitempty"`
	MemHeapSprayList               []*MemHeapSpray                   `protobuf:"bytes,7,rep,name=memHeapSprayList,proto3" json:"memHeapSprayList,omitempty"`
	AnalyzerVirusList              []*AnalyzerVirus                  `protobuf:"bytes,8,rep,name=analyzerVirusList,proto3" json:"analyzerVirusList,omitempty"`
	MemRopList                     []*MemRop                         `protobuf:"bytes,9,rep,name=memRopList,proto3" json:"memRopList,omitempty"`
	MemLayoutShellCodeList         []*MemLayoutShellCode             `protobuf:"bytes,10,rep,name=memLayoutShellCodeList,proto3" json:"memLayoutShellCodeList,omitempty"`
	MemStackPivotList              []*MemStackPivot                  `protobuf:"bytes,11,rep,name=memStackPivotList,proto3" json:"memStackPivotList,omitempty"`
	MemRunningShellCodeList        []*MemRunningShellCode            `protobuf:"bytes,12,rep,name=memRunningShellCodeList,proto3" json:"memRunningShellCodeList,omitempty"`
	MemStartProcessList            []*MemStartProcess                `protobuf:"bytes,13,rep,name=memStartProcessList,proto3" json:"memStartProcessList,omitempty"`
	MemEngineAttackList            []*MemEngineAttack                `protobuf:"bytes,14,rep,name=memEngineAttackList,proto3" json:"memEngineAttackList,omitempty"`
	TunnelRiskList                 []*TunnelRiskInfo                 `protobuf:"bytes,15,rep,name=tunnelRiskList,proto3" json:"tunnelRiskList,omitempty"`
	EtwRiskList                    []*EtwRiskInfo                    `protobuf:"bytes,50,rep,name=etwRiskList,proto3" json:"etwRiskList,omitempty"`
	KernelVulnerableDriverRiskList []*KernelVulnerableDriverRiskInfo `protobuf:"bytes,51,rep,name=kernelVulnerableDriverRiskList,proto3" json:"kernelVulnerableDriverRiskList,omitempty"`
}

func (x *MemProtectRiskMemInfo) Reset() {
	*x = MemProtectRiskMemInfo{}
	mi := &file_agent_risk_mem_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskMemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskMemInfo) ProtoMessage() {}

func (x *MemProtectRiskMemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskMemInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskMemInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskMemInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetLoadRemoteLibList() []*LoadRemoteLib {
	if x != nil {
		return x.LoadRemoteLibList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetVirProtectExStkSpaceList() []*VirProtectExStkSpace {
	if x != nil {
		return x.VirProtectExStkSpaceList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetReturnStackExecList() []*ReturnStackExec {
	if x != nil {
		return x.ReturnStackExecList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetRemoteThreadInject() []*RemoteThread {
	if x != nil {
		return x.RemoteThreadInject
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemNoFileAttackList() []*MemNoFileAttack {
	if x != nil {
		return x.MemNoFileAttackList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemHeapSprayList() []*MemHeapSpray {
	if x != nil {
		return x.MemHeapSprayList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetAnalyzerVirusList() []*AnalyzerVirus {
	if x != nil {
		return x.AnalyzerVirusList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemRopList() []*MemRop {
	if x != nil {
		return x.MemRopList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemLayoutShellCodeList() []*MemLayoutShellCode {
	if x != nil {
		return x.MemLayoutShellCodeList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemStackPivotList() []*MemStackPivot {
	if x != nil {
		return x.MemStackPivotList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemRunningShellCodeList() []*MemRunningShellCode {
	if x != nil {
		return x.MemRunningShellCodeList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemStartProcessList() []*MemStartProcess {
	if x != nil {
		return x.MemStartProcessList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetMemEngineAttackList() []*MemEngineAttack {
	if x != nil {
		return x.MemEngineAttackList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetTunnelRiskList() []*TunnelRiskInfo {
	if x != nil {
		return x.TunnelRiskList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetEtwRiskList() []*EtwRiskInfo {
	if x != nil {
		return x.EtwRiskList
	}
	return nil
}

func (x *MemProtectRiskMemInfo) GetKernelVulnerableDriverRiskList() []*KernelVulnerableDriverRiskInfo {
	if x != nil {
		return x.KernelVulnerableDriverRiskList
	}
	return nil
}

// 行为分析出来的病毒
type AnalyzerVirus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ImagePath  []byte      `protobuf:"bytes,2,opt,name=imagePath,proto3" json:"imagePath,omitempty"`   //文件全路径
	VirusName  []byte      `protobuf:"bytes,3,opt,name=virusName,proto3" json:"virusName,omitempty"`   //病毒名
	VirusDesc  []byte      `protobuf:"bytes,4,opt,name=virusDesc,proto3" json:"virusDesc,omitempty"`   //病毒描述信息
	ProcessID  uint32      `protobuf:"varint,5,opt,name=processID,proto3" json:"processID,omitempty"`  //进程Id
	ProcessMd5 string      `protobuf:"bytes,6,opt,name=processMd5,proto3" json:"processMd5,omitempty"` //进程MD5
}

func (x *AnalyzerVirus) Reset() {
	*x = AnalyzerVirus{}
	mi := &file_agent_risk_mem_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnalyzerVirus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalyzerVirus) ProtoMessage() {}

func (x *AnalyzerVirus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalyzerVirus.ProtoReflect.Descriptor instead.
func (*AnalyzerVirus) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{1}
}

func (x *AnalyzerVirus) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AnalyzerVirus) GetImagePath() []byte {
	if x != nil {
		return x.ImagePath
	}
	return nil
}

func (x *AnalyzerVirus) GetVirusName() []byte {
	if x != nil {
		return x.VirusName
	}
	return nil
}

func (x *AnalyzerVirus) GetVirusDesc() []byte {
	if x != nil {
		return x.VirusDesc
	}
	return nil
}

func (x *AnalyzerVirus) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *AnalyzerVirus) GetProcessMd5() string {
	if x != nil {
		return x.ProcessMd5
	}
	return ""
}

// 二进制漏洞检测:加载远程模块
type LoadRemoteLib struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ProcessID              uint32                `protobuf:"varint,2,opt,name=processID,proto3" json:"processID,omitempty"`        //进程Id
	IsX86Process           string                `protobuf:"bytes,3,opt,name=isX86Process,proto3" json:"isX86Process,omitempty"`   //是否32位进程(Yes/No)
	ImagePath              []byte                `protobuf:"bytes,4,opt,name=imagePath,proto3" json:"imagePath,omitempty"`         //进程全路径
	RemoteLibPath          []byte                `protobuf:"bytes,5,opt,name=remoteLibPath,proto3" json:"remoteLibPath,omitempty"` //远程模块路径
	ProcessMd5             string                `protobuf:"bytes,6,opt,name=processMd5,proto3" json:"processMd5,omitempty"`       //进程MD5
	ReportID               uint32                `protobuf:"varint,7,opt,name=reportID,proto3" json:"reportID,omitempty"`
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
}

func (x *LoadRemoteLib) Reset() {
	*x = LoadRemoteLib{}
	mi := &file_agent_risk_mem_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadRemoteLib) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadRemoteLib) ProtoMessage() {}

func (x *LoadRemoteLib) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadRemoteLib.ProtoReflect.Descriptor instead.
func (*LoadRemoteLib) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{2}
}

func (x *LoadRemoteLib) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LoadRemoteLib) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *LoadRemoteLib) GetIsX86Process() string {
	if x != nil {
		return x.IsX86Process
	}
	return ""
}

func (x *LoadRemoteLib) GetImagePath() []byte {
	if x != nil {
		return x.ImagePath
	}
	return nil
}

func (x *LoadRemoteLib) GetRemoteLibPath() []byte {
	if x != nil {
		return x.RemoteLibPath
	}
	return nil
}

func (x *LoadRemoteLib) GetProcessMd5() string {
	if x != nil {
		return x.ProcessMd5
	}
	return ""
}

func (x *LoadRemoteLib) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *LoadRemoteLib) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *LoadRemoteLib) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *LoadRemoteLib) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *LoadRemoteLib) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *LoadRemoteLib) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *LoadRemoteLib) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *LoadRemoteLib) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *LoadRemoteLib) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *LoadRemoteLib) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *LoadRemoteLib) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

// 栈属性攻击
// 二进制漏洞检测:让栈内存具有执行权限
type VirProtectExStkSpace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ProcessID              uint32                `protobuf:"varint,2,opt,name=processID,proto3" json:"processID,omitempty"`       //进程Id
	IsX86Process           string                `protobuf:"bytes,3,opt,name=isX86Process,proto3" json:"isX86Process,omitempty"`  //是否32位进程(Yes/No)
	ImagePath              []byte                `protobuf:"bytes,4,opt,name=imagePath,proto3" json:"imagePath,omitempty"`        //进程全路径
	Address                uint64                `protobuf:"varint,5,opt,name=address,proto3" json:"address,omitempty"`           //修改地址
	AddrSize               uint32                `protobuf:"varint,6,opt,name=addrSize,proto3" json:"addrSize,omitempty"`         //修改地址大小
	MemAttri               string                `protobuf:"bytes,7,opt,name=memAttri,proto3" json:"memAttri,omitempty"`          //新设置的内存属性
	ThreadID               uint32                `protobuf:"varint,8,opt,name=threadID,proto3" json:"threadID,omitempty"`         //线程Id
	StkBeginAddr           uint64                `protobuf:"varint,9,opt,name=stkBeginAddr,proto3" json:"stkBeginAddr,omitempty"` //线程栈开始位置
	StkEndAddr             uint64                `protobuf:"varint,10,opt,name=stkEndAddr,proto3" json:"stkEndAddr,omitempty"`    //线程栈结束位置
	ProcessMd5             string                `protobuf:"bytes,11,opt,name=processMd5,proto3" json:"processMd5,omitempty"`     //进程MD5
	ReportID               uint32                `protobuf:"varint,12,opt,name=reportID,proto3" json:"reportID,omitempty"`
	HookFuncName           string                `protobuf:"bytes,13,opt,name=HookFuncName,proto3" json:"HookFuncName,omitempty"`       // hook函数名
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程链信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
}

func (x *VirProtectExStkSpace) Reset() {
	*x = VirProtectExStkSpace{}
	mi := &file_agent_risk_mem_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirProtectExStkSpace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirProtectExStkSpace) ProtoMessage() {}

func (x *VirProtectExStkSpace) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirProtectExStkSpace.ProtoReflect.Descriptor instead.
func (*VirProtectExStkSpace) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{3}
}

func (x *VirProtectExStkSpace) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VirProtectExStkSpace) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *VirProtectExStkSpace) GetIsX86Process() string {
	if x != nil {
		return x.IsX86Process
	}
	return ""
}

func (x *VirProtectExStkSpace) GetImagePath() []byte {
	if x != nil {
		return x.ImagePath
	}
	return nil
}

func (x *VirProtectExStkSpace) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *VirProtectExStkSpace) GetAddrSize() uint32 {
	if x != nil {
		return x.AddrSize
	}
	return 0
}

func (x *VirProtectExStkSpace) GetMemAttri() string {
	if x != nil {
		return x.MemAttri
	}
	return ""
}

func (x *VirProtectExStkSpace) GetThreadID() uint32 {
	if x != nil {
		return x.ThreadID
	}
	return 0
}

func (x *VirProtectExStkSpace) GetStkBeginAddr() uint64 {
	if x != nil {
		return x.StkBeginAddr
	}
	return 0
}

func (x *VirProtectExStkSpace) GetStkEndAddr() uint64 {
	if x != nil {
		return x.StkEndAddr
	}
	return 0
}

func (x *VirProtectExStkSpace) GetProcessMd5() string {
	if x != nil {
		return x.ProcessMd5
	}
	return ""
}

func (x *VirProtectExStkSpace) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *VirProtectExStkSpace) GetHookFuncName() string {
	if x != nil {
		return x.HookFuncName
	}
	return ""
}

func (x *VirProtectExStkSpace) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *VirProtectExStkSpace) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *VirProtectExStkSpace) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *VirProtectExStkSpace) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *VirProtectExStkSpace) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *VirProtectExStkSpace) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *VirProtectExStkSpace) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *VirProtectExStkSpace) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *VirProtectExStkSpace) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *VirProtectExStkSpace) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

// 二进制漏洞检测:返回栈中执行代码
type ReturnStackExec struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ProcessID              uint32                `protobuf:"varint,2,opt,name=processID,proto3" json:"processID,omitempty"`       //进程Id
	IsX86Process           string                `protobuf:"bytes,3,opt,name=isX86Process,proto3" json:"isX86Process,omitempty"`  //是否32位进程(Yes/No)
	ImagePath              []byte                `protobuf:"bytes,4,opt,name=imagePath,proto3" json:"imagePath,omitempty"`        //进程全路径
	Address                uint64                `protobuf:"varint,5,opt,name=address,proto3" json:"address,omitempty"`           //返回执行地址
	ThreadID               uint32                `protobuf:"varint,6,opt,name=threadID,proto3" json:"threadID,omitempty"`         //线程Id
	StkBeginAddr           uint64                `protobuf:"varint,7,opt,name=stkBeginAddr,proto3" json:"stkBeginAddr,omitempty"` //线程栈开始位置
	StkEndAddr             uint64                `protobuf:"varint,8,opt,name=stkEndAddr,proto3" json:"stkEndAddr,omitempty"`     //线程栈结束位置
	ProcessMd5             string                `protobuf:"bytes,9,opt,name=processMd5,proto3" json:"processMd5,omitempty"`      //进程MD5
	ReportID               uint32                `protobuf:"varint,10,opt,name=reportID,proto3" json:"reportID,omitempty"`
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
}

func (x *ReturnStackExec) Reset() {
	*x = ReturnStackExec{}
	mi := &file_agent_risk_mem_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReturnStackExec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnStackExec) ProtoMessage() {}

func (x *ReturnStackExec) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnStackExec.ProtoReflect.Descriptor instead.
func (*ReturnStackExec) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{4}
}

func (x *ReturnStackExec) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ReturnStackExec) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *ReturnStackExec) GetIsX86Process() string {
	if x != nil {
		return x.IsX86Process
	}
	return ""
}

func (x *ReturnStackExec) GetImagePath() []byte {
	if x != nil {
		return x.ImagePath
	}
	return nil
}

func (x *ReturnStackExec) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *ReturnStackExec) GetThreadID() uint32 {
	if x != nil {
		return x.ThreadID
	}
	return 0
}

func (x *ReturnStackExec) GetStkBeginAddr() uint64 {
	if x != nil {
		return x.StkBeginAddr
	}
	return 0
}

func (x *ReturnStackExec) GetStkEndAddr() uint64 {
	if x != nil {
		return x.StkEndAddr
	}
	return 0
}

func (x *ReturnStackExec) GetProcessMd5() string {
	if x != nil {
		return x.ProcessMd5
	}
	return ""
}

func (x *ReturnStackExec) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *ReturnStackExec) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *ReturnStackExec) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *ReturnStackExec) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *ReturnStackExec) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *ReturnStackExec) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *ReturnStackExec) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *ReturnStackExec) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *ReturnStackExec) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *ReturnStackExec) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *ReturnStackExec) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

// 远线程注入
type RemoteThread struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header              *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	NHostPid            int32       `protobuf:"varint,2,opt,name=nHostPid,proto3" json:"nHostPid,omitempty"`                      //注入者进程Id
	NGuestPid           int32       `protobuf:"varint,3,opt,name=nGuestPid,proto3" json:"nGuestPid,omitempty"`                    //被注入进程Id
	StrGuestImageName   string      `protobuf:"bytes,4,opt,name=strGuestImageName,proto3" json:"strGuestImageName,omitempty"`     //被注入进程路径
	StrHostImageName    string      `protobuf:"bytes,5,opt,name=strHostImageName,proto3" json:"strHostImageName,omitempty"`       //注入者进程路径
	StrGuestProcessName string      `protobuf:"bytes,6,opt,name=strGuestProcessName,proto3" json:"strGuestProcessName,omitempty"` // 进程名
	StrHostProcessName  string      `protobuf:"bytes,7,opt,name=strHostProcessName,proto3" json:"strHostProcessName,omitempty"`
	StrHostProcessMd5   string      `protobuf:"bytes,8,opt,name=strHostProcessMd5,proto3" json:"strHostProcessMd5,omitempty"`
}

func (x *RemoteThread) Reset() {
	*x = RemoteThread{}
	mi := &file_agent_risk_mem_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoteThread) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteThread) ProtoMessage() {}

func (x *RemoteThread) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteThread.ProtoReflect.Descriptor instead.
func (*RemoteThread) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{5}
}

func (x *RemoteThread) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RemoteThread) GetNHostPid() int32 {
	if x != nil {
		return x.NHostPid
	}
	return 0
}

func (x *RemoteThread) GetNGuestPid() int32 {
	if x != nil {
		return x.NGuestPid
	}
	return 0
}

func (x *RemoteThread) GetStrGuestImageName() string {
	if x != nil {
		return x.StrGuestImageName
	}
	return ""
}

func (x *RemoteThread) GetStrHostImageName() string {
	if x != nil {
		return x.StrHostImageName
	}
	return ""
}

func (x *RemoteThread) GetStrGuestProcessName() string {
	if x != nil {
		return x.StrGuestProcessName
	}
	return ""
}

func (x *RemoteThread) GetStrHostProcessName() string {
	if x != nil {
		return x.StrHostProcessName
	}
	return ""
}

func (x *RemoteThread) GetStrHostProcessMd5() string {
	if x != nil {
		return x.StrHostProcessMd5
	}
	return ""
}

// 无文件攻击: Dll内存反射式注入
type MemNoFileAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader                         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SourceProcess *ProcessInfo                        `protobuf:"bytes,2,opt,name=SourceProcess,proto3" json:"SourceProcess,omitempty"`
	TargetProcess *ProcessInfo                        `protobuf:"bytes,3,opt,name=TargetProcess,proto3" json:"TargetProcess,omitempty"`
	AttackType    MemNoFileAttack_MemNoFileAttackType `protobuf:"varint,4,opt,name=AttackType,proto3,enum=agent.MemNoFileAttack_MemNoFileAttackType" json:"AttackType,omitempty"`
	DocName       string                              `protobuf:"bytes,5,opt,name=doc_name,json=docName,proto3" json:"doc_name,omitempty"` //对应文档名称，可能为空
	// Types that are assignable to Detail:
	//
	//	*MemNoFileAttack_Schedule
	//	*MemNoFileAttack_FileCopy
	//	*MemNoFileAttack_FileMove
	//	*MemNoFileAttack_Http
	//	*MemNoFileAttack_WimCmd
	//	*MemNoFileAttack_LoadDll
	//	*MemNoFileAttack_GetAddress
	//	*MemNoFileAttack_ListenPort
	//	*MemNoFileAttack_SocketCommunication
	//	*MemNoFileAttack_Win32Share
	//	*MemNoFileAttack_WebShellJava
	//	*MemNoFileAttack_WmiPersistentBackdoor
	//	*MemNoFileAttack_WebShellJava1
	//	*MemNoFileAttack_AmsiByAmsiContext
	//	*MemNoFileAttack_AmsiDllhijack
	//	*MemNoFileAttack_Email
	//	*MemNoFileAttack_WmiTerminateProcess
	//	*MemNoFileAttack_WmiRegOper
	//	*MemNoFileAttack_WmiServiceOper
	//	*MemNoFileAttack_WmiQuery
	Detail                 isMemNoFileAttack_Detail `protobuf_oneof:"detail"`
	ProcessInfoList        []*ProcessInfo           `protobuf:"bytes,200,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                   `protobuf:"bytes,201,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                   `protobuf:"bytes,202,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                   `protobuf:"bytes,203,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                   `protobuf:"varint,204,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo    `protobuf:"bytes,205,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	MemStr                 string                   `protobuf:"bytes,206,opt,name=MemStr,proto3" json:"MemStr,omitempty"` // 内存字符串
}

func (x *MemNoFileAttack) Reset() {
	*x = MemNoFileAttack{}
	mi := &file_agent_risk_mem_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemNoFileAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemNoFileAttack) ProtoMessage() {}

func (x *MemNoFileAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemNoFileAttack.ProtoReflect.Descriptor instead.
func (*MemNoFileAttack) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{6}
}

func (x *MemNoFileAttack) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemNoFileAttack) GetSourceProcess() *ProcessInfo {
	if x != nil {
		return x.SourceProcess
	}
	return nil
}

func (x *MemNoFileAttack) GetTargetProcess() *ProcessInfo {
	if x != nil {
		return x.TargetProcess
	}
	return nil
}

func (x *MemNoFileAttack) GetAttackType() MemNoFileAttack_MemNoFileAttackType {
	if x != nil {
		return x.AttackType
	}
	return MemNoFileAttack_REFLECTION_INJECTION
}

func (x *MemNoFileAttack) GetDocName() string {
	if x != nil {
		return x.DocName
	}
	return ""
}

func (m *MemNoFileAttack) GetDetail() isMemNoFileAttack_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *MemNoFileAttack) GetSchedule() *NoFileAttackScriptSchedule {
	if x, ok := x.GetDetail().(*MemNoFileAttack_Schedule); ok {
		return x.Schedule
	}
	return nil
}

func (x *MemNoFileAttack) GetFileCopy() *NoFileAttackScriptFileCopy {
	if x, ok := x.GetDetail().(*MemNoFileAttack_FileCopy); ok {
		return x.FileCopy
	}
	return nil
}

func (x *MemNoFileAttack) GetFileMove() *NoFileAttackScriptFileCopy {
	if x, ok := x.GetDetail().(*MemNoFileAttack_FileMove); ok {
		return x.FileMove
	}
	return nil
}

func (x *MemNoFileAttack) GetHttp() *NoFileAttackScriptHttp {
	if x, ok := x.GetDetail().(*MemNoFileAttack_Http); ok {
		return x.Http
	}
	return nil
}

func (x *MemNoFileAttack) GetWimCmd() *NoFileAttackScriptWmiCmd {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WimCmd); ok {
		return x.WimCmd
	}
	return nil
}

func (x *MemNoFileAttack) GetLoadDll() *NoFileAttackScriptLoadDll {
	if x, ok := x.GetDetail().(*MemNoFileAttack_LoadDll); ok {
		return x.LoadDll
	}
	return nil
}

func (x *MemNoFileAttack) GetGetAddress() *NoFileAttackScriptGetAddress {
	if x, ok := x.GetDetail().(*MemNoFileAttack_GetAddress); ok {
		return x.GetAddress
	}
	return nil
}

func (x *MemNoFileAttack) GetListenPort() *NoFileAttackScriptListen {
	if x, ok := x.GetDetail().(*MemNoFileAttack_ListenPort); ok {
		return x.ListenPort
	}
	return nil
}

func (x *MemNoFileAttack) GetSocketCommunication() *NoFileAttackScriptSocket {
	if x, ok := x.GetDetail().(*MemNoFileAttack_SocketCommunication); ok {
		return x.SocketCommunication
	}
	return nil
}

func (x *MemNoFileAttack) GetWin32Share() *NoFileAttackScriptWin32Share {
	if x, ok := x.GetDetail().(*MemNoFileAttack_Win32Share); ok {
		return x.Win32Share
	}
	return nil
}

func (x *MemNoFileAttack) GetWebShellJava() *NoFileAttackWebShellJava {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WebShellJava); ok {
		return x.WebShellJava
	}
	return nil
}

func (x *MemNoFileAttack) GetWmiPersistentBackdoor() *NoFileAttackWMIPersistentBackdoor {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WmiPersistentBackdoor); ok {
		return x.WmiPersistentBackdoor
	}
	return nil
}

func (x *MemNoFileAttack) GetWebShellJava1() *NoFileAttackWebShellJava1 {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WebShellJava1); ok {
		return x.WebShellJava1
	}
	return nil
}

func (x *MemNoFileAttack) GetAmsiByAmsiContext() *NoFileAttackScriptAmsiByAmsiContext {
	if x, ok := x.GetDetail().(*MemNoFileAttack_AmsiByAmsiContext); ok {
		return x.AmsiByAmsiContext
	}
	return nil
}

func (x *MemNoFileAttack) GetAmsiDllhijack() *NoFileAttackScriptAmsiDllHijack {
	if x, ok := x.GetDetail().(*MemNoFileAttack_AmsiDllhijack); ok {
		return x.AmsiDllhijack
	}
	return nil
}

func (x *MemNoFileAttack) GetEmail() *NoFileAttackScriptEmail {
	if x, ok := x.GetDetail().(*MemNoFileAttack_Email); ok {
		return x.Email
	}
	return nil
}

func (x *MemNoFileAttack) GetWmiTerminateProcess() *NoFileAttackScriptWmiTerminateProcess {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WmiTerminateProcess); ok {
		return x.WmiTerminateProcess
	}
	return nil
}

func (x *MemNoFileAttack) GetWmiRegOper() *NoFileAttackScriptWmiOperReg {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WmiRegOper); ok {
		return x.WmiRegOper
	}
	return nil
}

func (x *MemNoFileAttack) GetWmiServiceOper() *NoFileAttackScriptWmiOperService {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WmiServiceOper); ok {
		return x.WmiServiceOper
	}
	return nil
}

func (x *MemNoFileAttack) GetWmiQuery() *NoFileAttackScriptWmiExeQuery {
	if x, ok := x.GetDetail().(*MemNoFileAttack_WmiQuery); ok {
		return x.WmiQuery
	}
	return nil
}

func (x *MemNoFileAttack) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemNoFileAttack) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemNoFileAttack) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemNoFileAttack) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemNoFileAttack) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemNoFileAttack) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemNoFileAttack) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

type isMemNoFileAttack_Detail interface {
	isMemNoFileAttack_Detail()
}

type MemNoFileAttack_Schedule struct {
	Schedule *NoFileAttackScriptSchedule `protobuf:"bytes,21,opt,name=schedule,proto3,oneof"`
}

type MemNoFileAttack_FileCopy struct {
	FileCopy *NoFileAttackScriptFileCopy `protobuf:"bytes,22,opt,name=file_copy,json=fileCopy,proto3,oneof"`
}

type MemNoFileAttack_FileMove struct {
	FileMove *NoFileAttackScriptFileCopy `protobuf:"bytes,27,opt,name=file_move,json=fileMove,proto3,oneof"`
}

type MemNoFileAttack_Http struct {
	Http *NoFileAttackScriptHttp `protobuf:"bytes,23,opt,name=http,proto3,oneof"`
}

type MemNoFileAttack_WimCmd struct {
	WimCmd *NoFileAttackScriptWmiCmd `protobuf:"bytes,24,opt,name=wim_cmd,json=wimCmd,proto3,oneof"`
}

type MemNoFileAttack_LoadDll struct {
	LoadDll *NoFileAttackScriptLoadDll `protobuf:"bytes,25,opt,name=load_dll,json=loadDll,proto3,oneof"`
}

type MemNoFileAttack_GetAddress struct {
	GetAddress *NoFileAttackScriptGetAddress `protobuf:"bytes,26,opt,name=get_address,json=getAddress,proto3,oneof"`
}

type MemNoFileAttack_ListenPort struct {
	ListenPort *NoFileAttackScriptListen `protobuf:"bytes,28,opt,name=listen_port,json=listenPort,proto3,oneof"`
}

type MemNoFileAttack_SocketCommunication struct {
	SocketCommunication *NoFileAttackScriptSocket `protobuf:"bytes,29,opt,name=socket_communication,json=socketCommunication,proto3,oneof"`
}

type MemNoFileAttack_Win32Share struct {
	Win32Share *NoFileAttackScriptWin32Share `protobuf:"bytes,30,opt,name=win32_share,json=win32Share,proto3,oneof"`
}

type MemNoFileAttack_WebShellJava struct {
	WebShellJava *NoFileAttackWebShellJava `protobuf:"bytes,31,opt,name=web_shell_java,json=webShellJava,proto3,oneof"`
}

type MemNoFileAttack_WmiPersistentBackdoor struct {
	WmiPersistentBackdoor *NoFileAttackWMIPersistentBackdoor `protobuf:"bytes,32,opt,name=wmi_persistent_backdoor,json=wmiPersistentBackdoor,proto3,oneof"`
}

type MemNoFileAttack_WebShellJava1 struct {
	WebShellJava1 *NoFileAttackWebShellJava1 `protobuf:"bytes,33,opt,name=web_shell_java1,json=webShellJava1,proto3,oneof"`
}

type MemNoFileAttack_AmsiByAmsiContext struct {
	AmsiByAmsiContext *NoFileAttackScriptAmsiByAmsiContext `protobuf:"bytes,34,opt,name=amsi_by_amsi_context,json=amsiByAmsiContext,proto3,oneof"`
}

type MemNoFileAttack_AmsiDllhijack struct {
	AmsiDllhijack *NoFileAttackScriptAmsiDllHijack `protobuf:"bytes,35,opt,name=amsi_dllhijack,json=amsiDllhijack,proto3,oneof"`
}

type MemNoFileAttack_Email struct {
	Email *NoFileAttackScriptEmail `protobuf:"bytes,36,opt,name=email,proto3,oneof"`
}

type MemNoFileAttack_WmiTerminateProcess struct {
	WmiTerminateProcess *NoFileAttackScriptWmiTerminateProcess `protobuf:"bytes,37,opt,name=wmi_terminate_process,json=wmiTerminateProcess,proto3,oneof"`
}

type MemNoFileAttack_WmiRegOper struct {
	WmiRegOper *NoFileAttackScriptWmiOperReg `protobuf:"bytes,38,opt,name=wmi_reg_oper,json=wmiRegOper,proto3,oneof"`
}

type MemNoFileAttack_WmiServiceOper struct {
	WmiServiceOper *NoFileAttackScriptWmiOperService `protobuf:"bytes,39,opt,name=wmi_service_oper,json=wmiServiceOper,proto3,oneof"`
}

type MemNoFileAttack_WmiQuery struct {
	WmiQuery *NoFileAttackScriptWmiExeQuery `protobuf:"bytes,40,opt,name=wmi_query,json=wmiQuery,proto3,oneof"`
}

func (*MemNoFileAttack_Schedule) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_FileCopy) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_FileMove) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_Http) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WimCmd) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_LoadDll) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_GetAddress) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_ListenPort) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_SocketCommunication) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_Win32Share) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WebShellJava) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WmiPersistentBackdoor) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WebShellJava1) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_AmsiByAmsiContext) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_AmsiDllhijack) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_Email) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WmiTerminateProcess) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WmiRegOper) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WmiServiceOper) isMemNoFileAttack_Detail() {}

func (*MemNoFileAttack_WmiQuery) isMemNoFileAttack_Detail() {}

// 堆喷射
type MemHeapSpray struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID               uint32                `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	HookFuncName           string                `protobuf:"bytes,4,opt,name=HookFuncName,proto3" json:"HookFuncName,omitempty"`             // hook函数名
	Address                uint64                `protobuf:"varint,5,opt,name=address,proto3" json:"address,omitempty"`                      // 修改地址
	AddrSize               uint32                `protobuf:"varint,6,opt,name=addrSize,proto3" json:"addrSize,omitempty"`                    //修改地址大小
	InvalidInstruction     string                `protobuf:"bytes,7,opt,name=InvalidInstruction,proto3" json:"InvalidInstruction,omitempty"` // 无效指令
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"`      // 进程链信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`                    // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`                    // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`                // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`           // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	HeapBase               uint64                `protobuf:"varint,26,opt,name=HeapBase,proto3" json:"HeapBase,omitempty"`        //堆喷发生时申请的堆地址
	CheckBase              uint64                `protobuf:"varint,27,opt,name=CheckBase,proto3" json:"CheckBase,omitempty"`      //滑块
	HeapSize               uint64                `protobuf:"varint,28,opt,name=HeapSize,proto3" json:"HeapSize,omitempty"`        //堆块大小
	RetAddress             uint64                `protobuf:"varint,29,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,30,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,31,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,32,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
	MemStr                 string                `protobuf:"bytes,33,opt,name=MemStr,proto3" json:"MemStr,omitempty"`             // 内存字符串
}

func (x *MemHeapSpray) Reset() {
	*x = MemHeapSpray{}
	mi := &file_agent_risk_mem_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemHeapSpray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemHeapSpray) ProtoMessage() {}

func (x *MemHeapSpray) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemHeapSpray.ProtoReflect.Descriptor instead.
func (*MemHeapSpray) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{7}
}

func (x *MemHeapSpray) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemHeapSpray) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemHeapSpray) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemHeapSpray) GetHookFuncName() string {
	if x != nil {
		return x.HookFuncName
	}
	return ""
}

func (x *MemHeapSpray) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *MemHeapSpray) GetAddrSize() uint32 {
	if x != nil {
		return x.AddrSize
	}
	return 0
}

func (x *MemHeapSpray) GetInvalidInstruction() string {
	if x != nil {
		return x.InvalidInstruction
	}
	return ""
}

func (x *MemHeapSpray) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemHeapSpray) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemHeapSpray) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemHeapSpray) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemHeapSpray) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemHeapSpray) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemHeapSpray) GetHeapBase() uint64 {
	if x != nil {
		return x.HeapBase
	}
	return 0
}

func (x *MemHeapSpray) GetCheckBase() uint64 {
	if x != nil {
		return x.CheckBase
	}
	return 0
}

func (x *MemHeapSpray) GetHeapSize() uint64 {
	if x != nil {
		return x.HeapSize
	}
	return 0
}

func (x *MemHeapSpray) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemHeapSpray) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemHeapSpray) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemHeapSpray) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

func (x *MemHeapSpray) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

// ROP
type MemRop struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID               uint32                `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	RemoteIp               string                `protobuf:"bytes,4,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"` //可能为空
	HookFuncName           string                `protobuf:"bytes,5,opt,name=HookFuncName,proto3" json:"HookFuncName,omitempty"`         // hook函数名
	CheckContent           string                `protobuf:"bytes,6,opt,name=CheckContent,proto3" json:"CheckContent,omitempty"`         // 检测的8字节内容
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"`  // 进程链信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`                // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`                // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`            // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`       // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
}

func (x *MemRop) Reset() {
	*x = MemRop{}
	mi := &file_agent_risk_mem_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemRop) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemRop) ProtoMessage() {}

func (x *MemRop) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemRop.ProtoReflect.Descriptor instead.
func (*MemRop) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{8}
}

func (x *MemRop) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemRop) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemRop) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemRop) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *MemRop) GetHookFuncName() string {
	if x != nil {
		return x.HookFuncName
	}
	return ""
}

func (x *MemRop) GetCheckContent() string {
	if x != nil {
		return x.CheckContent
	}
	return ""
}

func (x *MemRop) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemRop) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemRop) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemRop) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemRop) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemRop) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemRop) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemRop) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemRop) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemRop) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

// 布局shellcode
type MemLayoutShellCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID               uint32                `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
	MemStr                 string                `protobuf:"bytes,30,opt,name=MemStr,proto3" json:"MemStr,omitempty"`             // 内存字符串
}

func (x *MemLayoutShellCode) Reset() {
	*x = MemLayoutShellCode{}
	mi := &file_agent_risk_mem_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemLayoutShellCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemLayoutShellCode) ProtoMessage() {}

func (x *MemLayoutShellCode) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemLayoutShellCode.ProtoReflect.Descriptor instead.
func (*MemLayoutShellCode) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{9}
}

func (x *MemLayoutShellCode) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemLayoutShellCode) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemLayoutShellCode) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemLayoutShellCode) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemLayoutShellCode) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemLayoutShellCode) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemLayoutShellCode) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemLayoutShellCode) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemLayoutShellCode) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemLayoutShellCode) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemLayoutShellCode) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemLayoutShellCode) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemLayoutShellCode) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

func (x *MemLayoutShellCode) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

// 栈翻转
type MemStackPivot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process      *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID     uint32       `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	HookFuncName string       `protobuf:"bytes,4,opt,name=HookFuncName,proto3" json:"HookFuncName,omitempty"`  // hook函数名
	Address      uint64       `protobuf:"varint,5,opt,name=address,proto3" json:"address,omitempty"`           //修改地址
	AddrSize     uint32       `protobuf:"varint,6,opt,name=addrSize,proto3" json:"addrSize,omitempty"`         //修改地址大小
	MemAttri     string       `protobuf:"bytes,7,opt,name=memAttri,proto3" json:"memAttri,omitempty"`          //新设置的内存属性
	StkBeginAddr uint64       `protobuf:"varint,8,opt,name=stkBeginAddr,proto3" json:"stkBeginAddr,omitempty"` //线程栈开始位置
	StkEndAddr   uint64       `protobuf:"varint,9,opt,name=stkEndAddr,proto3" json:"stkEndAddr,omitempty"`     //线程栈结束位置
	// uint64  RetAddress = 11; // 被检测的返回地址
	EspAddress             uint64                `protobuf:"varint,12,opt,name=EspAddress,proto3" json:"EspAddress,omitempty"`          // 当前的栈顶地址
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程链信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	OldStackBase           uint64                `protobuf:"varint,26,opt,name=OldStackBase,proto3" json:"OldStackBase,omitempty"`
	NewStackBase           uint64                `protobuf:"varint,27,opt,name=NewStackBase,proto3" json:"NewStackBase,omitempty"`
	RetAddress             uint64                `protobuf:"varint,28,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,29,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,30,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,31,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
	MemStr                 string                `protobuf:"bytes,32,opt,name=MemStr,proto3" json:"MemStr,omitempty"`             // 内存字符串
}

func (x *MemStackPivot) Reset() {
	*x = MemStackPivot{}
	mi := &file_agent_risk_mem_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemStackPivot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemStackPivot) ProtoMessage() {}

func (x *MemStackPivot) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemStackPivot.ProtoReflect.Descriptor instead.
func (*MemStackPivot) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{10}
}

func (x *MemStackPivot) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemStackPivot) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemStackPivot) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemStackPivot) GetHookFuncName() string {
	if x != nil {
		return x.HookFuncName
	}
	return ""
}

func (x *MemStackPivot) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *MemStackPivot) GetAddrSize() uint32 {
	if x != nil {
		return x.AddrSize
	}
	return 0
}

func (x *MemStackPivot) GetMemAttri() string {
	if x != nil {
		return x.MemAttri
	}
	return ""
}

func (x *MemStackPivot) GetStkBeginAddr() uint64 {
	if x != nil {
		return x.StkBeginAddr
	}
	return 0
}

func (x *MemStackPivot) GetStkEndAddr() uint64 {
	if x != nil {
		return x.StkEndAddr
	}
	return 0
}

func (x *MemStackPivot) GetEspAddress() uint64 {
	if x != nil {
		return x.EspAddress
	}
	return 0
}

func (x *MemStackPivot) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemStackPivot) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemStackPivot) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemStackPivot) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemStackPivot) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemStackPivot) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemStackPivot) GetOldStackBase() uint64 {
	if x != nil {
		return x.OldStackBase
	}
	return 0
}

func (x *MemStackPivot) GetNewStackBase() uint64 {
	if x != nil {
		return x.NewStackBase
	}
	return 0
}

func (x *MemStackPivot) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemStackPivot) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemStackPivot) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemStackPivot) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

func (x *MemStackPivot) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

// 启动进程
type MemStartProcess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                   *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                  *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	TargetProcessFilePath    string       `protobuf:"bytes,3,opt,name=targetProcessFilePath,proto3" json:"targetProcessFilePath,omitempty"`
	TargetProcessCommandLine string       `protobuf:"bytes,4,opt,name=targetProcessCommandLine,proto3" json:"targetProcessCommandLine,omitempty"`
}

func (x *MemStartProcess) Reset() {
	*x = MemStartProcess{}
	mi := &file_agent_risk_mem_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemStartProcess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemStartProcess) ProtoMessage() {}

func (x *MemStartProcess) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemStartProcess.ProtoReflect.Descriptor instead.
func (*MemStartProcess) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{11}
}

func (x *MemStartProcess) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemStartProcess) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemStartProcess) GetTargetProcessFilePath() string {
	if x != nil {
		return x.TargetProcessFilePath
	}
	return ""
}

func (x *MemStartProcess) GetTargetProcessCommandLine() string {
	if x != nil {
		return x.TargetProcessCommandLine
	}
	return ""
}

// 执行ShellCode
type MemRunningShellCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID               uint32                `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数
	MemStr                 string                `protobuf:"bytes,30,opt,name=MemStr,proto3" json:"MemStr,omitempty"`             // 内存字符串
}

func (x *MemRunningShellCode) Reset() {
	*x = MemRunningShellCode{}
	mi := &file_agent_risk_mem_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemRunningShellCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemRunningShellCode) ProtoMessage() {}

func (x *MemRunningShellCode) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemRunningShellCode.ProtoReflect.Descriptor instead.
func (*MemRunningShellCode) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{12}
}

func (x *MemRunningShellCode) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemRunningShellCode) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemRunningShellCode) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemRunningShellCode) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemRunningShellCode) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemRunningShellCode) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemRunningShellCode) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemRunningShellCode) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemRunningShellCode) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemRunningShellCode) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemRunningShellCode) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemRunningShellCode) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemRunningShellCode) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

func (x *MemRunningShellCode) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

// 引擎攻击
type MemEngineAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID               uint32                `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`               // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`               // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`           // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`      // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
	RetAddress             uint64                `protobuf:"varint,26,opt,name=retAddress,proto3" json:"retAddress,omitempty"`    //指令所在地址
	ModuleName             string                `protobuf:"bytes,27,opt,name=moduleName,proto3" json:"moduleName,omitempty"`     //指令所在模块
	HookName               string                `protobuf:"bytes,28,opt,name=hookName,proto3" json:"hookName,omitempty"`         //hook点
	CodeFragment           string                `protobuf:"bytes,29,opt,name=CodeFragment,proto3" json:"CodeFragment,omitempty"` // 调用函数的代码片段
}

func (x *MemEngineAttack) Reset() {
	*x = MemEngineAttack{}
	mi := &file_agent_risk_mem_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemEngineAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemEngineAttack) ProtoMessage() {}

func (x *MemEngineAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemEngineAttack.ProtoReflect.Descriptor instead.
func (*MemEngineAttack) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{13}
}

func (x *MemEngineAttack) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemEngineAttack) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemEngineAttack) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *MemEngineAttack) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *MemEngineAttack) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *MemEngineAttack) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *MemEngineAttack) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *MemEngineAttack) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *MemEngineAttack) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (x *MemEngineAttack) GetRetAddress() uint64 {
	if x != nil {
		return x.RetAddress
	}
	return 0
}

func (x *MemEngineAttack) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *MemEngineAttack) GetHookName() string {
	if x != nil {
		return x.HookName
	}
	return ""
}

func (x *MemEngineAttack) GetCodeFragment() string {
	if x != nil {
		return x.CodeFragment
	}
	return ""
}

// 脚本方式操作计划任务
type NoFileAttackScriptSchedule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Args string `protobuf:"bytes,2,opt,name=args,proto3" json:"args,omitempty"`
}

func (x *NoFileAttackScriptSchedule) Reset() {
	*x = NoFileAttackScriptSchedule{}
	mi := &file_agent_risk_mem_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptSchedule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptSchedule) ProtoMessage() {}

func (x *NoFileAttackScriptSchedule) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptSchedule.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptSchedule) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{14}
}

func (x *NoFileAttackScriptSchedule) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *NoFileAttackScriptSchedule) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

// 脚本方式拷贝文件
type NoFileAttackScriptFileCopy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcPath  string `protobuf:"bytes,1,opt,name=src_path,json=srcPath,proto3" json:"src_path,omitempty"`
	DestPath string `protobuf:"bytes,2,opt,name=dest_path,json=destPath,proto3" json:"dest_path,omitempty"`
}

func (x *NoFileAttackScriptFileCopy) Reset() {
	*x = NoFileAttackScriptFileCopy{}
	mi := &file_agent_risk_mem_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptFileCopy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptFileCopy) ProtoMessage() {}

func (x *NoFileAttackScriptFileCopy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptFileCopy.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptFileCopy) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{15}
}

func (x *NoFileAttackScriptFileCopy) GetSrcPath() string {
	if x != nil {
		return x.SrcPath
	}
	return ""
}

func (x *NoFileAttackScriptFileCopy) GetDestPath() string {
	if x != nil {
		return x.DestPath
	}
	return ""
}

// 脚本方式操作http
type NoFileAttackScriptHttp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *NoFileAttackScriptHttp) Reset() {
	*x = NoFileAttackScriptHttp{}
	mi := &file_agent_risk_mem_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptHttp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptHttp) ProtoMessage() {}

func (x *NoFileAttackScriptHttp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptHttp.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptHttp) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{16}
}

func (x *NoFileAttackScriptHttp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// 脚本方式操作wmi
type NoFileAttackScriptWmiCmd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd      string `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	RemoteIp string `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`  //可能为空
	IsRemote bool   `protobuf:"varint,3,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"` //是本地还是远程
}

func (x *NoFileAttackScriptWmiCmd) Reset() {
	*x = NoFileAttackScriptWmiCmd{}
	mi := &file_agent_risk_mem_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWmiCmd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWmiCmd) ProtoMessage() {}

func (x *NoFileAttackScriptWmiCmd) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWmiCmd.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWmiCmd) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{17}
}

func (x *NoFileAttackScriptWmiCmd) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *NoFileAttackScriptWmiCmd) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWmiCmd) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

// wmi结束进程
type NoFileAttackScriptWmiTerminateProcess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsRemote          bool   `protobuf:"varint,1,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"`                             //是本地还是远程
	RemoteIp          string `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`                              //远程ip(可能为空)
	TargetProcessPath string `protobuf:"bytes,3,opt,name=target_process_path,json=targetProcessPath,proto3" json:"target_process_path,omitempty"` //要被结束的进程路径
}

func (x *NoFileAttackScriptWmiTerminateProcess) Reset() {
	*x = NoFileAttackScriptWmiTerminateProcess{}
	mi := &file_agent_risk_mem_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWmiTerminateProcess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWmiTerminateProcess) ProtoMessage() {}

func (x *NoFileAttackScriptWmiTerminateProcess) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWmiTerminateProcess.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWmiTerminateProcess) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{18}
}

func (x *NoFileAttackScriptWmiTerminateProcess) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

func (x *NoFileAttackScriptWmiTerminateProcess) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWmiTerminateProcess) GetTargetProcessPath() string {
	if x != nil {
		return x.TargetProcessPath
	}
	return ""
}

// wmi操作注册表
type NoFileAttackScriptWmiOperReg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsRemote     bool   `protobuf:"varint,1,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"`              //是本地还是远程
	RemoteIp     string `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`               //远程ip(可能为空)
	OperType     string `protobuf:"bytes,3,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`               //操作类型
	RegKeyPath   string `protobuf:"bytes,4,opt,name=reg_key_path,json=regKeyPath,proto3" json:"reg_key_path,omitempty"`       //操作注册表key路径
	RegValueName string `protobuf:"bytes,5,opt,name=reg_value_name,json=regValueName,proto3" json:"reg_value_name,omitempty"` //操作注册表value名(可能为空)
}

func (x *NoFileAttackScriptWmiOperReg) Reset() {
	*x = NoFileAttackScriptWmiOperReg{}
	mi := &file_agent_risk_mem_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWmiOperReg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWmiOperReg) ProtoMessage() {}

func (x *NoFileAttackScriptWmiOperReg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWmiOperReg.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWmiOperReg) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{19}
}

func (x *NoFileAttackScriptWmiOperReg) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

func (x *NoFileAttackScriptWmiOperReg) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperReg) GetOperType() string {
	if x != nil {
		return x.OperType
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperReg) GetRegKeyPath() string {
	if x != nil {
		return x.RegKeyPath
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperReg) GetRegValueName() string {
	if x != nil {
		return x.RegValueName
	}
	return ""
}

// wmi操作服务
type NoFileAttackScriptWmiOperService struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsRemote       bool   `protobuf:"varint,1,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"`                    //是本地还是远程
	RemoteIp       string `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`                     //远程ip(可能为空)
	OperType       string `protobuf:"bytes,3,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`                     //操作类型
	ServiceName    string `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`            //服务名称
	ServiceExePath string `protobuf:"bytes,5,opt,name=service_exe_path,json=serviceExePath,proto3" json:"service_exe_path,omitempty"` //服务exe路径
}

func (x *NoFileAttackScriptWmiOperService) Reset() {
	*x = NoFileAttackScriptWmiOperService{}
	mi := &file_agent_risk_mem_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWmiOperService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWmiOperService) ProtoMessage() {}

func (x *NoFileAttackScriptWmiOperService) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWmiOperService.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWmiOperService) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{20}
}

func (x *NoFileAttackScriptWmiOperService) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

func (x *NoFileAttackScriptWmiOperService) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperService) GetOperType() string {
	if x != nil {
		return x.OperType
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperService) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *NoFileAttackScriptWmiOperService) GetServiceExePath() string {
	if x != nil {
		return x.ServiceExePath
	}
	return ""
}

// wmi执行查询
type NoFileAttackScriptWmiExeQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsRemote      bool   `protobuf:"varint,1,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"`               //是本地还是远程
	RemoteIp      string `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`                //远程ip(可能为空)
	QueryLanguage string `protobuf:"bytes,3,opt,name=query_language,json=queryLanguage,proto3" json:"query_language,omitempty"` //查询语言
	QueryCmd      string `protobuf:"bytes,4,opt,name=query_cmd,json=queryCmd,proto3" json:"query_cmd,omitempty"`                //查询内容
}

func (x *NoFileAttackScriptWmiExeQuery) Reset() {
	*x = NoFileAttackScriptWmiExeQuery{}
	mi := &file_agent_risk_mem_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWmiExeQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWmiExeQuery) ProtoMessage() {}

func (x *NoFileAttackScriptWmiExeQuery) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWmiExeQuery.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWmiExeQuery) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{21}
}

func (x *NoFileAttackScriptWmiExeQuery) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

func (x *NoFileAttackScriptWmiExeQuery) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWmiExeQuery) GetQueryLanguage() string {
	if x != nil {
		return x.QueryLanguage
	}
	return ""
}

func (x *NoFileAttackScriptWmiExeQuery) GetQueryCmd() string {
	if x != nil {
		return x.QueryCmd
	}
	return ""
}

// 脚本方式加载DLL镜像
type NoFileAttackScriptLoadDll struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *NoFileAttackScriptLoadDll) Reset() {
	*x = NoFileAttackScriptLoadDll{}
	mi := &file_agent_risk_mem_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptLoadDll) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptLoadDll) ProtoMessage() {}

func (x *NoFileAttackScriptLoadDll) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptLoadDll.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptLoadDll) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{22}
}

func (x *NoFileAttackScriptLoadDll) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

// 脚本方式操作获取api地址
type NoFileAttackScriptGetAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiName string `protobuf:"bytes,1,opt,name=api_name,json=apiName,proto3" json:"api_name,omitempty"`
}

func (x *NoFileAttackScriptGetAddress) Reset() {
	*x = NoFileAttackScriptGetAddress{}
	mi := &file_agent_risk_mem_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptGetAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptGetAddress) ProtoMessage() {}

func (x *NoFileAttackScriptGetAddress) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptGetAddress.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptGetAddress) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{23}
}

func (x *NoFileAttackScriptGetAddress) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

// 脚本方式监听端口
type NoFileAttackScriptListen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalIp   string `protobuf:"bytes,1,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`
	LocalPort int32  `protobuf:"varint,2,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`
}

func (x *NoFileAttackScriptListen) Reset() {
	*x = NoFileAttackScriptListen{}
	mi := &file_agent_risk_mem_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptListen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptListen) ProtoMessage() {}

func (x *NoFileAttackScriptListen) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptListen.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptListen) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{24}
}

func (x *NoFileAttackScriptListen) GetLocalIp() string {
	if x != nil {
		return x.LocalIp
	}
	return ""
}

func (x *NoFileAttackScriptListen) GetLocalPort() int32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

// 脚本方式socket通讯
type NoFileAttackScriptSocket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RemoteIp   string `protobuf:"bytes,1,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`
	RemotePort int32  `protobuf:"varint,2,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`
}

func (x *NoFileAttackScriptSocket) Reset() {
	*x = NoFileAttackScriptSocket{}
	mi := &file_agent_risk_mem_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptSocket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptSocket) ProtoMessage() {}

func (x *NoFileAttackScriptSocket) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptSocket.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptSocket) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{25}
}

func (x *NoFileAttackScriptSocket) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptSocket) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

// 脚本方式共享文件夹
type NoFileAttackScriptWin32Share struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShareName string `protobuf:"bytes,1,opt,name=share_name,json=shareName,proto3" json:"share_name,omitempty"`
	SharePath string `protobuf:"bytes,2,opt,name=share_path,json=sharePath,proto3" json:"share_path,omitempty"`
	RemoteIp  string `protobuf:"bytes,3,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`  //远程ip(可能为空)
	IsRemote  bool   `protobuf:"varint,4,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"` //是本地还是远程
}

func (x *NoFileAttackScriptWin32Share) Reset() {
	*x = NoFileAttackScriptWin32Share{}
	mi := &file_agent_risk_mem_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptWin32Share) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptWin32Share) ProtoMessage() {}

func (x *NoFileAttackScriptWin32Share) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptWin32Share.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptWin32Share) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{26}
}

func (x *NoFileAttackScriptWin32Share) GetShareName() string {
	if x != nil {
		return x.ShareName
	}
	return ""
}

func (x *NoFileAttackScriptWin32Share) GetSharePath() string {
	if x != nil {
		return x.SharePath
	}
	return ""
}

func (x *NoFileAttackScriptWin32Share) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackScriptWin32Share) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

// java web shell
type NoFileAttackWebShellJava struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebShellName   string `protobuf:"bytes,1,opt,name=web_shell_name,json=webShellName,proto3" json:"web_shell_name,omitempty"`       //shell风险名称
	WebShellDetail string `protobuf:"bytes,2,opt,name=web_shell_detail,json=webShellDetail,proto3" json:"web_shell_detail,omitempty"` //shell风险详情,命中黑java类的信息
}

func (x *NoFileAttackWebShellJava) Reset() {
	*x = NoFileAttackWebShellJava{}
	mi := &file_agent_risk_mem_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackWebShellJava) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackWebShellJava) ProtoMessage() {}

func (x *NoFileAttackWebShellJava) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackWebShellJava.ProtoReflect.Descriptor instead.
func (*NoFileAttackWebShellJava) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{27}
}

func (x *NoFileAttackWebShellJava) GetWebShellName() string {
	if x != nil {
		return x.WebShellName
	}
	return ""
}

func (x *NoFileAttackWebShellJava) GetWebShellDetail() string {
	if x != nil {
		return x.WebShellDetail
	}
	return ""
}

type NoFileAttackWMIPersistentBackdoor struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ObjName    string `protobuf:"bytes,1,opt,name=obj_name,json=objName,proto3" json:"obj_name,omitempty"`
	WmiContext string `protobuf:"bytes,2,opt,name=wmi_context,json=wmiContext,proto3" json:"wmi_context,omitempty"`
	RemoteIp   string `protobuf:"bytes,3,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"` //可能为空
}

func (x *NoFileAttackWMIPersistentBackdoor) Reset() {
	*x = NoFileAttackWMIPersistentBackdoor{}
	mi := &file_agent_risk_mem_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackWMIPersistentBackdoor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackWMIPersistentBackdoor) ProtoMessage() {}

func (x *NoFileAttackWMIPersistentBackdoor) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackWMIPersistentBackdoor.ProtoReflect.Descriptor instead.
func (*NoFileAttackWMIPersistentBackdoor) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{28}
}

func (x *NoFileAttackWMIPersistentBackdoor) GetObjName() string {
	if x != nil {
		return x.ObjName
	}
	return ""
}

func (x *NoFileAttackWMIPersistentBackdoor) GetWmiContext() string {
	if x != nil {
		return x.WmiContext
	}
	return ""
}

func (x *NoFileAttackWMIPersistentBackdoor) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

type NoFileAttackWebShellJava1 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailInfo []string                                 `protobuf:"bytes,1,rep,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"`
	RemoteIp   string                                   `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`
	Infotype   NoFileAttackWebShellJava1_DetailInfoType `protobuf:"varint,3,opt,name=infotype,proto3,enum=agent.NoFileAttackWebShellJava1_DetailInfoType" json:"infotype,omitempty"`
}

func (x *NoFileAttackWebShellJava1) Reset() {
	*x = NoFileAttackWebShellJava1{}
	mi := &file_agent_risk_mem_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackWebShellJava1) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackWebShellJava1) ProtoMessage() {}

func (x *NoFileAttackWebShellJava1) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackWebShellJava1.ProtoReflect.Descriptor instead.
func (*NoFileAttackWebShellJava1) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{29}
}

func (x *NoFileAttackWebShellJava1) GetDetailInfo() []string {
	if x != nil {
		return x.DetailInfo
	}
	return nil
}

func (x *NoFileAttackWebShellJava1) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NoFileAttackWebShellJava1) GetInfotype() NoFileAttackWebShellJava1_DetailInfoType {
	if x != nil {
		return x.Infotype
	}
	return NoFileAttackWebShellJava1_JVM_RUN_CMD
}

// 过AMSI保护-通过修改AMSICONTEXT结构体
type NoFileAttackScriptAmsiByAmsiContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModifiedValue string `protobuf:"bytes,1,opt,name=modified_value,json=modifiedValue,proto3" json:"modified_value,omitempty"`
}

func (x *NoFileAttackScriptAmsiByAmsiContext) Reset() {
	*x = NoFileAttackScriptAmsiByAmsiContext{}
	mi := &file_agent_risk_mem_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptAmsiByAmsiContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptAmsiByAmsiContext) ProtoMessage() {}

func (x *NoFileAttackScriptAmsiByAmsiContext) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptAmsiByAmsiContext.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptAmsiByAmsiContext) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{30}
}

func (x *NoFileAttackScriptAmsiByAmsiContext) GetModifiedValue() string {
	if x != nil {
		return x.ModifiedValue
	}
	return ""
}

// 过AMSI保护-通过Dll劫持
type NoFileAttackScriptAmsiDllHijack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DllPath string `protobuf:"bytes,1,opt,name=dll_path,json=dllPath,proto3" json:"dll_path,omitempty"`
}

func (x *NoFileAttackScriptAmsiDllHijack) Reset() {
	*x = NoFileAttackScriptAmsiDllHijack{}
	mi := &file_agent_risk_mem_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptAmsiDllHijack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptAmsiDllHijack) ProtoMessage() {}

func (x *NoFileAttackScriptAmsiDllHijack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptAmsiDllHijack.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptAmsiDllHijack) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{31}
}

func (x *NoFileAttackScriptAmsiDllHijack) GetDllPath() string {
	if x != nil {
		return x.DllPath
	}
	return ""
}

// 发送邮件
type NoFileAttackScriptEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderAddr string `protobuf:"bytes,1,opt,name=sender_addr,json=senderAddr,proto3" json:"sender_addr,omitempty"`
	RecvAddr   string `protobuf:"bytes,2,opt,name=recv_addr,json=recvAddr,proto3" json:"recv_addr,omitempty"`
}

func (x *NoFileAttackScriptEmail) Reset() {
	*x = NoFileAttackScriptEmail{}
	mi := &file_agent_risk_mem_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoFileAttackScriptEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoFileAttackScriptEmail) ProtoMessage() {}

func (x *NoFileAttackScriptEmail) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoFileAttackScriptEmail.ProtoReflect.Descriptor instead.
func (*NoFileAttackScriptEmail) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{32}
}

func (x *NoFileAttackScriptEmail) GetSenderAddr() string {
	if x != nil {
		return x.SenderAddr
	}
	return ""
}

func (x *NoFileAttackScriptEmail) GetRecvAddr() string {
	if x != nil {
		return x.RecvAddr
	}
	return ""
}

type EtwRiskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TargetProcessInfo      *ProcessInfo          `protobuf:"bytes,2,opt,name=TargetProcessInfo,proto3" json:"TargetProcessInfo,omitempty"`           // 目标进程基础信息
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,3,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"`               // 源进程基础信息链
	ProcFlag               string                `protobuf:"bytes,4,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`                             // 进程链标记
	DumpFlag               string                `protobuf:"bytes,5,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`                             // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,6,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`                         // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,7,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`                    // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,8,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"` // 证据信息链
}

func (x *EtwRiskInfo) Reset() {
	*x = EtwRiskInfo{}
	mi := &file_agent_risk_mem_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EtwRiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EtwRiskInfo) ProtoMessage() {}

func (x *EtwRiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EtwRiskInfo.ProtoReflect.Descriptor instead.
func (*EtwRiskInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{33}
}

func (x *EtwRiskInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *EtwRiskInfo) GetTargetProcessInfo() *ProcessInfo {
	if x != nil {
		return x.TargetProcessInfo
	}
	return nil
}

func (x *EtwRiskInfo) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *EtwRiskInfo) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *EtwRiskInfo) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *EtwRiskInfo) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *EtwRiskInfo) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *EtwRiskInfo) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

// 具有内核漏洞驱动
type KernelVulnerableDriverRiskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *RiskHeader                                   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	DriverName     string                                        `protobuf:"bytes,2,opt,name=DriverName,proto3" json:"DriverName,omitempty"`                                                       // 驱动名称
	DriverPath     string                                        `protobuf:"bytes,3,opt,name=DriverPath,proto3" json:"DriverPath,omitempty"`                                                       // 驱动路径
	RiskType       KernelVulnerableDriverRiskInfo_DriverRiskType `protobuf:"varint,4,opt,name=RiskType,proto3,enum=agent.KernelVulnerableDriverRiskInfo_DriverRiskType" json:"RiskType,omitempty"` // 驱动风险类型
	DriverFileList []*ProcessInfo                                `protobuf:"bytes,5,rep,name=DriverFileList,proto3" json:"DriverFileList,omitempty"`                                               // 驱动文件信息，先按照数组来定义
	OriginFileName string                                        `protobuf:"bytes,6,opt,name=OriginFileName,proto3" json:"OriginFileName,omitempty"`                                               // 原始文件名
	Process        *ProcessInfo                                  `protobuf:"bytes,7,opt,name=Process,proto3" json:"Process,omitempty"`                                                             // 加载当前驱动的进程信息
}

func (x *KernelVulnerableDriverRiskInfo) Reset() {
	*x = KernelVulnerableDriverRiskInfo{}
	mi := &file_agent_risk_mem_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KernelVulnerableDriverRiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KernelVulnerableDriverRiskInfo) ProtoMessage() {}

func (x *KernelVulnerableDriverRiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KernelVulnerableDriverRiskInfo.ProtoReflect.Descriptor instead.
func (*KernelVulnerableDriverRiskInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{34}
}

func (x *KernelVulnerableDriverRiskInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *KernelVulnerableDriverRiskInfo) GetDriverName() string {
	if x != nil {
		return x.DriverName
	}
	return ""
}

func (x *KernelVulnerableDriverRiskInfo) GetDriverPath() string {
	if x != nil {
		return x.DriverPath
	}
	return ""
}

func (x *KernelVulnerableDriverRiskInfo) GetRiskType() KernelVulnerableDriverRiskInfo_DriverRiskType {
	if x != nil {
		return x.RiskType
	}
	return KernelVulnerableDriverRiskInfo_DRIVER_RISK_TYPE_UNKNOWN
}

func (x *KernelVulnerableDriverRiskInfo) GetDriverFileList() []*ProcessInfo {
	if x != nil {
		return x.DriverFileList
	}
	return nil
}

func (x *KernelVulnerableDriverRiskInfo) GetOriginFileName() string {
	if x != nil {
		return x.OriginFileName
	}
	return ""
}

func (x *KernelVulnerableDriverRiskInfo) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

// 0地址利用
type NullAddressAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *RiskHeader    `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	HookFuncName    string         `protobuf:"bytes,2,opt,name=HookFuncName,proto3" json:"HookFuncName,omitempty"`        // hook函数名
	Address         uint64         `protobuf:"varint,3,opt,name=address,proto3" json:"address,omitempty"`                 // 修改地址
	AddrSize        uint32         `protobuf:"varint,4,opt,name=addrSize,proto3" json:"addrSize,omitempty"`               // 修改地址大小
	MemAttri        string         `protobuf:"bytes,5,opt,name=memAttri,proto3" json:"memAttri,omitempty"`                // 新设置的内存属性
	FuncFlags       uint32         `protobuf:"varint,6,opt,name=FuncFlags,proto3" json:"FuncFlags,omitempty"`             // 函数参数flags
	ProcessInfoList []*ProcessInfo `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程链信息
}

func (x *NullAddressAttack) Reset() {
	*x = NullAddressAttack{}
	mi := &file_agent_risk_mem_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NullAddressAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NullAddressAttack) ProtoMessage() {}

func (x *NullAddressAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NullAddressAttack.ProtoReflect.Descriptor instead.
func (*NullAddressAttack) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{35}
}

func (x *NullAddressAttack) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *NullAddressAttack) GetHookFuncName() string {
	if x != nil {
		return x.HookFuncName
	}
	return ""
}

func (x *NullAddressAttack) GetAddress() uint64 {
	if x != nil {
		return x.Address
	}
	return 0
}

func (x *NullAddressAttack) GetAddrSize() uint32 {
	if x != nil {
		return x.AddrSize
	}
	return 0
}

func (x *NullAddressAttack) GetMemAttri() string {
	if x != nil {
		return x.MemAttri
	}
	return ""
}

func (x *NullAddressAttack) GetFuncFlags() uint32 {
	if x != nil {
		return x.FuncFlags
	}
	return 0
}

func (x *NullAddressAttack) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

// 隧道通信
type TunnelRiskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *RiskHeader               `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process    *ProcessInfo              `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	ReportID   uint32                    `protobuf:"varint,3,opt,name=reportID,proto3" json:"reportID,omitempty"`
	SrcIp      string                    `protobuf:"bytes,5,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`                                                      // 源IP
	SrcPort    uint32                    `protobuf:"varint,6,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`                                               // 源端口
	DstIp      string                    `protobuf:"bytes,7,opt,name=dst_ip,json=dstIp,proto3" json:"dst_ip,omitempty"`                                                      // 目的IP
	DstPort    uint32                    `protobuf:"varint,8,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`                                               // 目的端口
	TunnelType TunnelRiskInfo_TunnelType `protobuf:"varint,9,opt,name=tunnel_type,json=tunnelType,proto3,enum=agent.TunnelRiskInfo_TunnelType" json:"tunnel_type,omitempty"` // 隧道类型
	// Types that are assignable to Detail:
	//
	//	*TunnelRiskInfo_InfoIcmp
	//	*TunnelRiskInfo_InfoDns
	//	*TunnelRiskInfo_InfoTcp
	Detail                 isTunnelRiskInfo_Detail `protobuf_oneof:"detail"`
	ProcessInfoList        []*ProcessInfo          `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"` // 进程信息
	ReportEvidenceInfoList []*ReportEvidenceInfo   `protobuf:"bytes,21,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"`
}

func (x *TunnelRiskInfo) Reset() {
	*x = TunnelRiskInfo{}
	mi := &file_agent_risk_mem_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TunnelRiskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TunnelRiskInfo) ProtoMessage() {}

func (x *TunnelRiskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TunnelRiskInfo.ProtoReflect.Descriptor instead.
func (*TunnelRiskInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{36}
}

func (x *TunnelRiskInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *TunnelRiskInfo) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *TunnelRiskInfo) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *TunnelRiskInfo) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *TunnelRiskInfo) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *TunnelRiskInfo) GetDstIp() string {
	if x != nil {
		return x.DstIp
	}
	return ""
}

func (x *TunnelRiskInfo) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *TunnelRiskInfo) GetTunnelType() TunnelRiskInfo_TunnelType {
	if x != nil {
		return x.TunnelType
	}
	return TunnelRiskInfo_TUNNEL_TYPE_ICMP
}

func (m *TunnelRiskInfo) GetDetail() isTunnelRiskInfo_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *TunnelRiskInfo) GetInfoIcmp() *TUNNEL_INFO_ICMP {
	if x, ok := x.GetDetail().(*TunnelRiskInfo_InfoIcmp); ok {
		return x.InfoIcmp
	}
	return nil
}

func (x *TunnelRiskInfo) GetInfoDns() *TUNNEL_INFO_DNS {
	if x, ok := x.GetDetail().(*TunnelRiskInfo_InfoDns); ok {
		return x.InfoDns
	}
	return nil
}

func (x *TunnelRiskInfo) GetInfoTcp() *TUNNEL_INFO_TCP {
	if x, ok := x.GetDetail().(*TunnelRiskInfo_InfoTcp); ok {
		return x.InfoTcp
	}
	return nil
}

func (x *TunnelRiskInfo) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *TunnelRiskInfo) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

type isTunnelRiskInfo_Detail interface {
	isTunnelRiskInfo_Detail()
}

type TunnelRiskInfo_InfoIcmp struct {
	InfoIcmp *TUNNEL_INFO_ICMP `protobuf:"bytes,10,opt,name=info_icmp,json=infoIcmp,proto3,oneof"` // ICMP隧道信息
}

type TunnelRiskInfo_InfoDns struct {
	InfoDns *TUNNEL_INFO_DNS `protobuf:"bytes,11,opt,name=info_dns,json=infoDns,proto3,oneof"` // DNS隧道信息
}

type TunnelRiskInfo_InfoTcp struct {
	InfoTcp *TUNNEL_INFO_TCP `protobuf:"bytes,12,opt,name=info_tcp,json=infoTcp,proto3,oneof"` // TCP三次握手隧道信息
}

func (*TunnelRiskInfo_InfoIcmp) isTunnelRiskInfo_Detail() {}

func (*TunnelRiskInfo_InfoDns) isTunnelRiskInfo_Detail() {}

func (*TunnelRiskInfo_InfoTcp) isTunnelRiskInfo_Detail() {}

// icmp隧道信息
type TUNNEL_INFO_ICMP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type     uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`         // icmp固定字段
	Code     uint32 `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`         // icmp固定字段
	Checksum uint32 `protobuf:"varint,3,opt,name=checksum,proto3" json:"checksum,omitempty"` // icmp固定字段
	Other    uint32 `protobuf:"varint,4,opt,name=other,proto3" json:"other,omitempty"`       // icmp可变字段，不同的type，other字段含义不同
	// 数据包内容
	HexData    string                          `protobuf:"bytes,5,opt,name=hex_data,json=hexData,proto3" json:"hex_data,omitempty"`                                                      // 01020304
	StrData    string                          `protobuf:"bytes,6,opt,name=str_data,json=strData,proto3" json:"str_data,omitempty"`                                                      // ..abc..ls..
	DetectFlag TUNNEL_INFO_ICMP_IcmpDetectType `protobuf:"varint,7,opt,name=detect_flag,json=detectFlag,proto3,enum=agent.TUNNEL_INFO_ICMP_IcmpDetectType" json:"detect_flag,omitempty"` // 每一个bit位代表一种检测点
	Offest     []uint32                        `protobuf:"varint,8,rep,packed,name=offest,proto3" json:"offest,omitempty"`                                                               // ICMP可疑关键字从str_data的开始的偏移
	Len        []uint32                        `protobuf:"varint,9,rep,packed,name=len,proto3" json:"len,omitempty"`                                                                     // ICMP可疑关键字长度
}

func (x *TUNNEL_INFO_ICMP) Reset() {
	*x = TUNNEL_INFO_ICMP{}
	mi := &file_agent_risk_mem_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TUNNEL_INFO_ICMP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TUNNEL_INFO_ICMP) ProtoMessage() {}

func (x *TUNNEL_INFO_ICMP) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TUNNEL_INFO_ICMP.ProtoReflect.Descriptor instead.
func (*TUNNEL_INFO_ICMP) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{37}
}

func (x *TUNNEL_INFO_ICMP) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TUNNEL_INFO_ICMP) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TUNNEL_INFO_ICMP) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

func (x *TUNNEL_INFO_ICMP) GetOther() uint32 {
	if x != nil {
		return x.Other
	}
	return 0
}

func (x *TUNNEL_INFO_ICMP) GetHexData() string {
	if x != nil {
		return x.HexData
	}
	return ""
}

func (x *TUNNEL_INFO_ICMP) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

func (x *TUNNEL_INFO_ICMP) GetDetectFlag() TUNNEL_INFO_ICMP_IcmpDetectType {
	if x != nil {
		return x.DetectFlag
	}
	return TUNNEL_INFO_ICMP_ICMP_DETECT_TYPE_ICMP_NORMAL
}

func (x *TUNNEL_INFO_ICMP) GetOffest() []uint32 {
	if x != nil {
		return x.Offest
	}
	return nil
}

func (x *TUNNEL_INFO_ICMP) GetLen() []uint32 {
	if x != nil {
		return x.Len
	}
	return nil
}

type Query struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`    // 查询名
	Type  uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`   // 查询类型
	Class uint32 `protobuf:"varint,3,opt,name=class,proto3" json:"class,omitempty"` // 查询类
}

func (x *Query) Reset() {
	*x = Query{}
	mi := &file_agent_risk_mem_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Query) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Query) ProtoMessage() {}

func (x *Query) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Query.ProtoReflect.Descriptor instead.
func (*Query) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{38}
}

func (x *Query) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Query) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Query) GetClass() uint32 {
	if x != nil {
		return x.Class
	}
	return 0
}

type Answer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`    // 回答名
	Type  uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`   // 回答类型
	Class uint32 `protobuf:"varint,3,opt,name=class,proto3" json:"class,omitempty"` // 回答类
	Ttl   uint32 `protobuf:"varint,4,opt,name=ttl,proto3" json:"ttl,omitempty"`     // 回答生存时间
}

func (x *Answer) Reset() {
	*x = Answer{}
	mi := &file_agent_risk_mem_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Answer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Answer) ProtoMessage() {}

func (x *Answer) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Answer.ProtoReflect.Descriptor instead.
func (*Answer) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{39}
}

func (x *Answer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Answer) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Answer) GetClass() uint32 {
	if x != nil {
		return x.Class
	}
	return 0
}

func (x *Answer) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

// dns隧道信息
type TUNNEL_INFO_DNS struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            uint32    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                            // dns固定字段
	Flags         uint32    `protobuf:"varint,2,opt,name=flags,proto3" json:"flags,omitempty"`                                      // dns固定字段
	Questions     uint32    `protobuf:"varint,3,opt,name=questions,proto3" json:"questions,omitempty"`                              // dns固定字段
	AnswerRrs     uint32    `protobuf:"varint,4,opt,name=answer_rrs,json=answerRrs,proto3" json:"answer_rrs,omitempty"`             // dns固定字段
	AuthorityRrs  uint32    `protobuf:"varint,5,opt,name=authority_rrs,json=authorityRrs,proto3" json:"authority_rrs,omitempty"`    // dns固定字段
	AdditionalRrs uint32    `protobuf:"varint,6,opt,name=additional_rrs,json=additionalRrs,proto3" json:"additional_rrs,omitempty"` // dns固定字段
	Queries       []*Query  `protobuf:"bytes,7,rep,name=queries,proto3" json:"queries,omitempty"`                                   // dns查询信息 可变
	Answers       []*Answer `protobuf:"bytes,8,rep,name=answers,proto3" json:"answers,omitempty"`                                   // dns回答信息 可变
	// 数据包内容
	HexData    string                        `protobuf:"bytes,9,opt,name=hex_data,json=hexData,proto3" json:"hex_data,omitempty"`                                                     // 01020304
	StrData    string                        `protobuf:"bytes,10,opt,name=str_data,json=strData,proto3" json:"str_data,omitempty"`                                                    // ..abc..ls..
	DetectFlag TUNNEL_INFO_DNS_DnsDetectType `protobuf:"varint,11,opt,name=detect_flag,json=detectFlag,proto3,enum=agent.TUNNEL_INFO_DNS_DnsDetectType" json:"detect_flag,omitempty"` // 每一个bit位代表一种检测点
	Offest     []uint32                      `protobuf:"varint,12,rep,packed,name=offest,proto3" json:"offest,omitempty"`                                                             // DNS可疑关键字偏移从str_data的开始的偏移
	Len        []uint32                      `protobuf:"varint,13,rep,packed,name=len,proto3" json:"len,omitempty"`                                                                   // DNS可疑关键字长度
}

func (x *TUNNEL_INFO_DNS) Reset() {
	*x = TUNNEL_INFO_DNS{}
	mi := &file_agent_risk_mem_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TUNNEL_INFO_DNS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TUNNEL_INFO_DNS) ProtoMessage() {}

func (x *TUNNEL_INFO_DNS) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TUNNEL_INFO_DNS.ProtoReflect.Descriptor instead.
func (*TUNNEL_INFO_DNS) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{40}
}

func (x *TUNNEL_INFO_DNS) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetFlags() uint32 {
	if x != nil {
		return x.Flags
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetQuestions() uint32 {
	if x != nil {
		return x.Questions
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetAnswerRrs() uint32 {
	if x != nil {
		return x.AnswerRrs
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetAuthorityRrs() uint32 {
	if x != nil {
		return x.AuthorityRrs
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetAdditionalRrs() uint32 {
	if x != nil {
		return x.AdditionalRrs
	}
	return 0
}

func (x *TUNNEL_INFO_DNS) GetQueries() []*Query {
	if x != nil {
		return x.Queries
	}
	return nil
}

func (x *TUNNEL_INFO_DNS) GetAnswers() []*Answer {
	if x != nil {
		return x.Answers
	}
	return nil
}

func (x *TUNNEL_INFO_DNS) GetHexData() string {
	if x != nil {
		return x.HexData
	}
	return ""
}

func (x *TUNNEL_INFO_DNS) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

func (x *TUNNEL_INFO_DNS) GetDetectFlag() TUNNEL_INFO_DNS_DnsDetectType {
	if x != nil {
		return x.DetectFlag
	}
	return TUNNEL_INFO_DNS_DNS_DETECT_TYPE_DNS_NORMAL
}

func (x *TUNNEL_INFO_DNS) GetOffest() []uint32 {
	if x != nil {
		return x.Offest
	}
	return nil
}

func (x *TUNNEL_INFO_DNS) GetLen() []uint32 {
	if x != nil {
		return x.Len
	}
	return nil
}

// tcp三次握手隧道信息
type TUNNEL_INFO_TCP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SrcPort    uint32   `protobuf:"varint,1,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`          // 源端口
	DstPort    uint32   `protobuf:"varint,2,opt,name=dst_port,json=dstPort,proto3" json:"dst_port,omitempty"`          // 目的端口
	SeqNum     uint32   `protobuf:"varint,3,opt,name=seq_num,json=seqNum,proto3" json:"seq_num,omitempty"`             // TCP序列号
	AckNum     uint32   `protobuf:"varint,4,opt,name=ack_num,json=ackNum,proto3" json:"ack_num,omitempty"`             // TCP确认号
	DataOffset uint32   `protobuf:"varint,5,opt,name=data_offset,json=dataOffset,proto3" json:"data_offset,omitempty"` // 数据偏移
	Flags      uint32   `protobuf:"varint,6,opt,name=flags,proto3" json:"flags,omitempty"`                             // TCP标志
	WindowSize uint32   `protobuf:"varint,7,opt,name=window_size,json=windowSize,proto3" json:"window_size,omitempty"` // 窗口大小
	Checksum   uint32   `protobuf:"varint,8,opt,name=checksum,proto3" json:"checksum,omitempty"`                       // TCP校验和
	UrgentPtr  uint32   `protobuf:"varint,9,opt,name=urgent_ptr,json=urgentPtr,proto3" json:"urgent_ptr,omitempty"`    // 紧急指针
	Options    []string `protobuf:"bytes,10,rep,name=options,proto3" json:"options,omitempty"`                         // TCP选项 可变
	// 数据包内容
	HexData    string                        `protobuf:"bytes,11,opt,name=hex_data,json=hexData,proto3" json:"hex_data,omitempty"`                                                    // 01020304
	StrData    string                        `protobuf:"bytes,12,opt,name=str_data,json=strData,proto3" json:"str_data,omitempty"`                                                    // ..abc..ls..
	DetectFlag TUNNEL_INFO_TCP_TcpDetectType `protobuf:"varint,13,opt,name=detect_flag,json=detectFlag,proto3,enum=agent.TUNNEL_INFO_TCP_TcpDetectType" json:"detect_flag,omitempty"` // 每一个bit位代表一种检测点
	Offest     []uint32                      `protobuf:"varint,14,rep,packed,name=offest,proto3" json:"offest,omitempty"`                                                             // TCP可疑关键字偏移从str_data的开始的偏移
	Len        []uint32                      `protobuf:"varint,15,rep,packed,name=len,proto3" json:"len,omitempty"`                                                                   // TCP可疑关键字长度
}

func (x *TUNNEL_INFO_TCP) Reset() {
	*x = TUNNEL_INFO_TCP{}
	mi := &file_agent_risk_mem_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TUNNEL_INFO_TCP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TUNNEL_INFO_TCP) ProtoMessage() {}

func (x *TUNNEL_INFO_TCP) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_mem_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TUNNEL_INFO_TCP.ProtoReflect.Descriptor instead.
func (*TUNNEL_INFO_TCP) Descriptor() ([]byte, []int) {
	return file_agent_risk_mem_proto_rawDescGZIP(), []int{41}
}

func (x *TUNNEL_INFO_TCP) GetSrcPort() uint32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetDstPort() uint32 {
	if x != nil {
		return x.DstPort
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetSeqNum() uint32 {
	if x != nil {
		return x.SeqNum
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetAckNum() uint32 {
	if x != nil {
		return x.AckNum
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetDataOffset() uint32 {
	if x != nil {
		return x.DataOffset
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetFlags() uint32 {
	if x != nil {
		return x.Flags
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetWindowSize() uint32 {
	if x != nil {
		return x.WindowSize
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetChecksum() uint32 {
	if x != nil {
		return x.Checksum
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetUrgentPtr() uint32 {
	if x != nil {
		return x.UrgentPtr
	}
	return 0
}

func (x *TUNNEL_INFO_TCP) GetOptions() []string {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *TUNNEL_INFO_TCP) GetHexData() string {
	if x != nil {
		return x.HexData
	}
	return ""
}

func (x *TUNNEL_INFO_TCP) GetStrData() string {
	if x != nil {
		return x.StrData
	}
	return ""
}

func (x *TUNNEL_INFO_TCP) GetDetectFlag() TUNNEL_INFO_TCP_TcpDetectType {
	if x != nil {
		return x.DetectFlag
	}
	return TUNNEL_INFO_TCP_TCP_DETECT_TYPE_TCP_NORMAL
}

func (x *TUNNEL_INFO_TCP) GetOffest() []uint32 {
	if x != nil {
		return x.Offest
	}
	return nil
}

func (x *TUNNEL_INFO_TCP) GetLen() []uint32 {
	if x != nil {
		return x.Len
	}
	return nil
}

var File_agent_risk_mem_proto protoreflect.FileDescriptor

var file_agent_risk_mem_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6d, 0x65, 0x6d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xd3, 0x09, 0x0a, 0x15, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x52, 0x69, 0x73, 0x6b, 0x4d, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08,
	0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x42, 0x0a, 0x11, 0x6c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x61, 0x64,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x52, 0x11, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x57, 0x0a, 0x18,
	0x76, 0x69, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x45, 0x78, 0x53, 0x74, 0x6b, 0x53,
	0x70, 0x61, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x69, 0x72, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x45, 0x78, 0x53, 0x74, 0x6b, 0x53, 0x70, 0x61, 0x63, 0x65, 0x52, 0x18, 0x76, 0x69, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x45, 0x78, 0x53, 0x74, 0x6b, 0x53, 0x70, 0x61, 0x63,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x53,
	0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x52, 0x13, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x43, 0x0a, 0x12, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64,
	0x52, 0x12, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6e,
	0x6a, 0x65, 0x63, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x4e, 0x6f, 0x46, 0x69, 0x6c,
	0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x4e, 0x6f, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x13, 0x6d, 0x65, 0x6d, 0x4e, 0x6f,
	0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f,
	0x0a, 0x10, 0x6d, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x53, 0x70, 0x72, 0x61, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4d, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x53, 0x70, 0x72, 0x61, 0x79, 0x52, 0x10, 0x6d,
	0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x53, 0x70, 0x72, 0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x42, 0x0a, 0x11, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x72, 0x56, 0x69, 0x72, 0x75, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x72, 0x56, 0x69, 0x72, 0x75, 0x73,
	0x52, 0x11, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x7a, 0x65, 0x72, 0x56, 0x69, 0x72, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x0a, 0x6d, 0x65, 0x6d, 0x52, 0x6f, 0x70, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4d, 0x65, 0x6d, 0x52, 0x6f, 0x70, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x52, 0x6f, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x6d, 0x65, 0x6d, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53,
	0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x4c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x16, 0x6d,
	0x65, 0x6d, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x11, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x52, 0x11, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x63, 0x6b,
	0x50, 0x69, 0x76, 0x6f, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x17, 0x6d, 0x65, 0x6d,
	0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x68, 0x65,
	0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x17, 0x6d, 0x65, 0x6d, 0x52, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x48, 0x0a, 0x13, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x13, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x6d, 0x65, 0x6d,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d,
	0x65, 0x6d, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x13,
	0x6d, 0x65, 0x6d, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x0e, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0e, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x65, 0x74, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x32, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x45, 0x74, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x65, 0x74, 0x77,
	0x52, 0x69, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x1e, 0x6b, 0x65, 0x72, 0x6e,
	0x65, 0x6c, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56,
	0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52,
	0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x1e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56,
	0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52,
	0x69, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd2, 0x01, 0x0a, 0x0d, 0x41, 0x6e, 0x61, 0x6c,
	0x79, 0x7a, 0x65, 0x72, 0x56, 0x69, 0x72, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1c,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x22, 0x89, 0x05, 0x0a,
	0x0d, 0x4c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x58, 0x38, 0x36,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69,
	0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x24, 0x0a, 0x0d, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x50, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x0f, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f,
	0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f,
	0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65,
	0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xc0, 0x06, 0x0a, 0x14, 0x56, 0x69, 0x72,
	0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x45, 0x78, 0x53, 0x74, 0x6b, 0x53, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73,
	0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x69, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x12, 0x1a,
	0x0a, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74,
	0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0c, 0x73, 0x74, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0a, 0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1e,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x48, 0x6f,
	0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c,
	0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70,
	0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c,
	0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68,
	0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68,
	0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46,
	0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43,
	0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xdf, 0x05, 0x0a, 0x0f,
	0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x12,
	0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x58, 0x38,
	0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x69, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x44,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x44,
	0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x73, 0x74, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64, 0x41, 0x64,
	0x64, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d,
	0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4d, 0x64, 0x35, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44,
	0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75,
	0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75,
	0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x46, 0x6c, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a,
	0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x64,
	0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xdd, 0x02,
	0x0a, 0x0c, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x48, 0x6f,
	0x73, 0x74, 0x50, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x6e, 0x48, 0x6f,
	0x73, 0x74, 0x50, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x47, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x69, 0x64, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x74, 0x72, 0x47, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x73, 0x74, 0x72, 0x47, 0x75, 0x65, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x72,
	0x48, 0x6f, 0x73, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x13, 0x73, 0x74, 0x72, 0x47, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x72, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x72,
	0x48, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2c, 0x0a, 0x11, 0x73, 0x74, 0x72, 0x48, 0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x74, 0x72, 0x48,
	0x6f, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x64, 0x35, 0x22, 0xed, 0x16,
	0x0a, 0x0f, 0x4d, 0x65, 0x6d, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0d,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x4a, 0x0a, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d,
	0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x2e, 0x4d, 0x65, 0x6d,
	0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6f, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x48, 0x00, 0x52, 0x08,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x63, 0x6f, 0x70, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x48, 0x00,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x70, 0x79, 0x12, 0x40, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x76, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x70, 0x79,
	0x48, 0x00, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x76, 0x65, 0x12, 0x33, 0x0a, 0x04,
	0x68, 0x74, 0x74, 0x70, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x48, 0x74, 0x74, 0x70, 0x48, 0x00, 0x52, 0x04, 0x68, 0x74, 0x74,
	0x70, 0x12, 0x3a, 0x0a, 0x07, 0x77, 0x69, 0x6d, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c,
	0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69,
	0x43, 0x6d, 0x64, 0x48, 0x00, 0x52, 0x06, 0x77, 0x69, 0x6d, 0x43, 0x6d, 0x64, 0x12, 0x3d, 0x0a,
	0x08, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x64, 0x6c, 0x6c, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x6c,
	0x6c, 0x48, 0x00, 0x52, 0x07, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x6c, 0x6c, 0x12, 0x46, 0x0a, 0x0b,
	0x67, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x47, 0x65, 0x74, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x67, 0x65, 0x74, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x48, 0x00, 0x52, 0x0a, 0x6c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x54, 0x0a, 0x14, 0x73, 0x6f, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x48, 0x00, 0x52, 0x13, 0x73, 0x6f, 0x63, 0x6b, 0x65,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46,
	0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x33, 0x32, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69,
	0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x69,
	0x6e, 0x33, 0x32, 0x53, 0x68, 0x61, 0x72, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x77, 0x69, 0x6e, 0x33,
	0x32, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x47, 0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x68,
	0x65, 0x6c, 0x6c, 0x5f, 0x6a, 0x61, 0x76, 0x61, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x48,
	0x00, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x12,
	0x62, 0x0a, 0x17, 0x77, 0x6d, 0x69, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x64, 0x6f, 0x6f, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x4d, 0x49, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x64, 0x6f, 0x6f, 0x72, 0x48, 0x00, 0x52, 0x15, 0x77, 0x6d,
	0x69, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x64,
	0x6f, 0x6f, 0x72, 0x12, 0x4a, 0x0a, 0x0f, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x68, 0x65, 0x6c, 0x6c,
	0x5f, 0x6a, 0x61, 0x76, 0x61, 0x31, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x31, 0x48, 0x00,
	0x52, 0x0d, 0x77, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x31, 0x12,
	0x5d, 0x0a, 0x14, 0x61, 0x6d, 0x73, 0x69, 0x5f, 0x62, 0x79, 0x5f, 0x61, 0x6d, 0x73, 0x69, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x41, 0x6d, 0x73, 0x69, 0x42, 0x79, 0x41, 0x6d,
	0x73, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x11, 0x61, 0x6d, 0x73,
	0x69, 0x42, 0x79, 0x41, 0x6d, 0x73, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x4f,
	0x0a, 0x0e, 0x61, 0x6d, 0x73, 0x69, 0x5f, 0x64, 0x6c, 0x6c, 0x68, 0x69, 0x6a, 0x61, 0x63, 0x6b,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x41, 0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x48, 0x00,
	0x52, 0x0d, 0x61, 0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c, 0x68, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x12,
	0x36, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x48, 0x00,
	0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x62, 0x0a, 0x15, 0x77, 0x6d, 0x69, 0x5f, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x57, 0x6d, 0x69, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x13, 0x77, 0x6d, 0x69, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x77,
	0x6d, 0x69, 0x5f, 0x72, 0x65, 0x67, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x23, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x4f,
	0x70, 0x65, 0x72, 0x52, 0x65, 0x67, 0x48, 0x00, 0x52, 0x0a, 0x77, 0x6d, 0x69, 0x52, 0x65, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x10, 0x77, 0x6d, 0x69, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x4f, 0x70, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x48, 0x00, 0x52, 0x0e, 0x77, 0x6d, 0x69, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x09, 0x77, 0x6d, 0x69,
	0x5f, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x45, 0x78, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x48, 0x00, 0x52, 0x08, 0x77, 0x6d, 0x69, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x3d,
	0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0xc8, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a,
	0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0xc9, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1b, 0x0a, 0x08, 0x44, 0x75,
	0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0xca, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44,
	0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0xcb, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x23, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0xcc, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x52, 0x0a,
	0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0xcd, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x18, 0xce, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x22, 0xd6, 0x06, 0x0a, 0x13, 0x4d,
	0x65, 0x6d, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x45, 0x46, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x55, 0x4e, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43,
	0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f,
	0x44, 0x45, 0x4c, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f,
	0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x45, 0x10,
	0x04, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x43, 0x4f, 0x50, 0x59, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x5f, 0x44, 0x49, 0x52, 0x43, 0x4f, 0x50, 0x59, 0x10, 0x06, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x47, 0x45, 0x54, 0x10, 0x07, 0x12,
	0x14, 0x0a, 0x10, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x50,
	0x4f, 0x53, 0x54, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f,
	0x57, 0x4d, 0x49, 0x43, 0x4d, 0x44, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x44, 0x4c, 0x4c, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x47, 0x45, 0x54, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x5f, 0x44, 0x49, 0x52, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d,
	0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x10, 0x0e, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x4f, 0x43, 0x4b, 0x45, 0x54,
	0x10, 0x0f, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x57, 0x49, 0x4e,
	0x33, 0x32, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x10, 0x10, 0x12, 0x12, 0x0a, 0x0e, 0x57, 0x45,
	0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x11, 0x12, 0x12,
	0x0a, 0x0e, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x4a, 0x41, 0x56, 0x41,
	0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x4d, 0x49, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x49, 0x53,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x44, 0x4f, 0x4f, 0x52, 0x10, 0x13, 0x12,
	0x13, 0x0a, 0x0f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x4a, 0x41, 0x56,
	0x41, 0x31, 0x10, 0x14, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x54, 0x5f, 0x57,
	0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x57, 0x52,
	0x49, 0x54, 0x45, 0x10, 0x15, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x54, 0x5f,
	0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x4c, 0x49,
	0x53, 0x54, 0x45, 0x4e, 0x10, 0x16, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x54,
	0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x5f, 0x52, 0x55, 0x4e, 0x10, 0x17, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f, 0x42, 0x59, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x18, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f, 0x42, 0x59, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f,
	0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x19, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x43, 0x52,
	0x49, 0x50, 0x54, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f, 0x44, 0x4c, 0x4c, 0x48, 0x49, 0x4a, 0x41,
	0x43, 0x4b, 0x10, 0x1a, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x4b,
	0x45, 0x59, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x45, 0x52, 0x10, 0x1b, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x53, 0x48, 0x4f,
	0x54, 0x10, 0x1c, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x45, 0x4d,
	0x41, 0x49, 0x4c, 0x10, 0x1d, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x4d, 0x49, 0x5f, 0x54, 0x45, 0x52,
	0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1e,
	0x12, 0x10, 0x0a, 0x0c, 0x57, 0x4d, 0x49, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x10, 0x1f, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x4d, 0x49, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x20, 0x12, 0x0d, 0x0a, 0x09, 0x57, 0x4d, 0x49, 0x5f,
	0x51, 0x55, 0x45, 0x52, 0x59, 0x10, 0x21, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x45, 0x56, 0x45, 0x4e, 0x54, 0x4c, 0x4f, 0x47, 0x5f, 0x47, 0x45, 0x4e, 0x5f, 0x4c, 0x4f,
	0x47, 0x10, 0x22, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x88, 0x06,
	0x0a, 0x0c, 0x4d, 0x65, 0x6d, 0x48, 0x65, 0x61, 0x70, 0x53, 0x70, 0x72, 0x61, 0x79, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46,
	0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2e, 0x0a,
	0x12, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a,
	0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x65,
	0x61, 0x70, 0x42, 0x61, 0x73, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x48, 0x65,
	0x61, 0x70, 0x42, 0x61, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42,
	0x61, 0x73, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x42, 0x61, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x65, 0x61, 0x70, 0x53, 0x69, 0x7a, 0x65,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x48, 0x65, 0x61, 0x70, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x22, 0xef, 0x04, 0x0a, 0x06, 0x4d, 0x65, 0x6d,
	0x52, 0x6f, 0x70, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x22, 0x0a, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x6f, 0x6f,
	0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3c, 0x0a,
	0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a,
	0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f,
	0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f,
	0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72,
	0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f,
	0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xae, 0x04, 0x0a, 0x12, 0x4d,
	0x65, 0x6d, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x22, 0xcb, 0x06, 0x0a, 0x0d,
	0x4d, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x12, 0x29, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75,
	0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x08, 0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6d, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x6d, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x69, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x74, 0x6b, 0x42,
	0x65, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c,
	0x73, 0x74, 0x6b, 0x42, 0x65, 0x67, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x73, 0x74, 0x6b, 0x45, 0x6e, 0x64, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1e, 0x0a, 0x0a,
	0x45, 0x73, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x45, 0x73, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x0a, 0x0f,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72,
	0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72,
	0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c,
	0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c,
	0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c,
	0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x4f, 0x6c, 0x64,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x4f, 0x6c, 0x64, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x4e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x18, 0x1b, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0c, 0x4e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x42, 0x61, 0x73,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a,
	0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x18, 0x20, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x22, 0xdc, 0x01, 0x0a, 0x0f, 0x4d, 0x65,
	0x6d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x29, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x34, 0x0a, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x3a, 0x0a, 0x18,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0xaf, 0x04, 0x0a, 0x13, 0x4d, 0x65, 0x6d,
	0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x18, 0x1e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x4d, 0x65, 0x6d, 0x53, 0x74, 0x72, 0x22, 0x93, 0x04, 0x0a, 0x0f, 0x4d,
	0x65, 0x6d, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a,
	0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a,
	0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x1e, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x6f, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c,
	0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x46, 0x72, 0x61, 0x67, 0x6d, 0x65, 0x6e, 0x74,
	0x22, 0x44, 0x0a, 0x1a, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x22, 0x54, 0x0a, 0x1a, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x43, 0x6f, 0x70, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x61, 0x74, 0x68, 0x22, 0x2a, 0x0a, 0x16,
	0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x48, 0x74, 0x74, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x66, 0x0a, 0x18, 0x4e, 0x6f, 0x46, 0x69,
	0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d,
	0x69, 0x43, 0x6d, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x22, 0x91, 0x01, 0x0a, 0x25, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73,
	0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69,
	0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x49, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x50, 0x61, 0x74, 0x68, 0x22, 0xbd, 0x01, 0x0a, 0x1c, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x4f, 0x70,
	0x65, 0x72, 0x52, 0x65, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0c,
	0x72, 0x65, 0x67, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x4b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x24,
	0x0a, 0x0e, 0x72, 0x65, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x20, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x4f, 0x70,
	0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65,
	0x78, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x45, 0x78, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x9d, 0x01,
	0x0a, 0x1d, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x45, 0x78, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65,
	0x72, 0x79, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6d, 0x64, 0x22, 0x2f, 0x0a,
	0x19, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x6c, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x39,
	0x0a, 0x1c, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x47, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x18, 0x4e, 0x6f, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x70,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x22,
	0x58, 0x0a, 0x18, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x6f, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x22, 0x96, 0x01, 0x0a, 0x1c, 0x4e, 0x6f,
	0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x57, 0x69, 0x6e, 0x33, 0x32, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68,
	0x61, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x73, 0x68, 0x61, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d,
	0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x22, 0x6a, 0x0a, 0x18, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x12, 0x24,
	0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x68, 0x65, 0x6c,
	0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x77, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x7c,
	0x0a, 0x21, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x4d,
	0x49, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x63, 0x6b, 0x64,
	0x6f, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x62, 0x6a, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x62, 0x6a, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x6d, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6d, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x22, 0xfc, 0x07, 0x0a,
	0x19, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x65, 0x62,
	0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x4b, 0x0a, 0x08, 0x69, 0x6e, 0x66, 0x6f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57,
	0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4a, 0x61, 0x76, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x69, 0x6e, 0x66,
	0x6f, 0x74, 0x79, 0x70, 0x65, 0x22, 0xd3, 0x06, 0x0a, 0x0e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x56, 0x4d, 0x5f,
	0x52, 0x55, 0x4e, 0x5f, 0x43, 0x4d, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x56, 0x4d,
	0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x4f, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x56,
	0x4d, 0x5f, 0x44, 0x52, 0x41, 0x47, 0x5f, 0x4c, 0x49, 0x42, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11,
	0x4a, 0x56, 0x4d, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x48, 0x50, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x48, 0x50, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x4f, 0x50, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x48, 0x50, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x48,
	0x50, 0x5f, 0x44, 0x42, 0x10, 0x07, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x48, 0x50, 0x5f, 0x45, 0x4e,
	0x56, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x48, 0x50, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x45,
	0x5f, 0x41, 0x50, 0x49, 0x10, 0x0b, 0x12, 0x21, 0x0a, 0x1d, 0x4a, 0x56, 0x4d, 0x5f, 0x52, 0x45,
	0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x43, 0x4f,
	0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x4a, 0x56, 0x4d,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x57, 0x45, 0x42, 0x53, 0x48, 0x45, 0x4c, 0x4c,
	0x5f, 0x53, 0x43, 0x41, 0x4e, 0x10, 0x0d, 0x12, 0x1d, 0x0a, 0x19, 0x4a, 0x56, 0x4d, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x43, 0x41, 0x4e, 0x10, 0x0e, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x56, 0x4d, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x4f, 0x50, 0x10, 0x0f, 0x12, 0x0f, 0x0a, 0x0b, 0x4a, 0x56, 0x4d, 0x5f, 0x56,
	0x49, 0x52, 0x5f, 0x43, 0x4d, 0x44, 0x10, 0x10, 0x12, 0x11, 0x0a, 0x0d, 0x4a, 0x56, 0x4d, 0x5f,
	0x53, 0x43, 0x41, 0x4e, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x11, 0x12, 0x10, 0x0a, 0x0c, 0x4a,
	0x56, 0x4d, 0x5f, 0x4a, 0x41, 0x52, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x12, 0x12, 0x0b, 0x0a,
	0x07, 0x4a, 0x56, 0x4d, 0x5f, 0x5a, 0x49, 0x50, 0x10, 0x13, 0x12, 0x0d, 0x0a, 0x09, 0x4a, 0x56,
	0x4d, 0x5f, 0x55, 0x4e, 0x5a, 0x49, 0x50, 0x10, 0x14, 0x12, 0x19, 0x0a, 0x15, 0x4a, 0x56, 0x4d,
	0x5f, 0x48, 0x54, 0x54, 0x50, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x10, 0x15, 0x12, 0x12, 0x0a, 0x0e, 0x4a, 0x56, 0x4d, 0x5f, 0x53, 0x51, 0x4c, 0x5f,
	0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x16, 0x12, 0x0b, 0x0a, 0x07, 0x4a, 0x56, 0x4d, 0x5f,
	0x58, 0x58, 0x45, 0x10, 0x17, 0x12, 0x0e, 0x0a, 0x0a, 0x4a, 0x56, 0x4d, 0x5f, 0x44, 0x4e, 0x53,
	0x4c, 0x4f, 0x47, 0x10, 0x18, 0x12, 0x0c, 0x0a, 0x08, 0x4a, 0x56, 0x4d, 0x5f, 0x53, 0x53, 0x52,
	0x46, 0x10, 0x19, 0x12, 0x14, 0x0a, 0x10, 0x4a, 0x56, 0x4d, 0x5f, 0x44, 0x49, 0x52, 0x45, 0x43,
	0x54, 0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x10, 0x1a, 0x12, 0x10, 0x0a, 0x0c, 0x4a, 0x56, 0x4d,
	0x5f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x49, 0x50, 0x10, 0x1b, 0x12, 0x0c, 0x0a, 0x08, 0x4a,
	0x56, 0x4d, 0x5f, 0x43, 0x53, 0x52, 0x46, 0x10, 0x1d, 0x12, 0x17, 0x0a, 0x13, 0x4a, 0x56, 0x4d,
	0x5f, 0x55, 0x4e, 0x53, 0x41, 0x46, 0x45, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x52, 0x45, 0x43, 0x54,
	0x10, 0x1e, 0x12, 0x10, 0x0a, 0x0b, 0x50, 0x48, 0x50, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0xc8, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x50, 0x48, 0x50, 0x5f, 0x53, 0x59, 0x53, 0x54,
	0x45, 0x4d, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x10, 0xc9, 0x01, 0x12, 0x12, 0x0a,
	0x0d, 0x50, 0x48, 0x50, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x10, 0xca,
	0x01, 0x12, 0x16, 0x0a, 0x11, 0x50, 0x48, 0x50, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43,
	0x4b, 0x5f, 0x46, 0x55, 0x4e, 0x43, 0x10, 0xcb, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x50, 0x48, 0x50,
	0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x49, 0x43, 0x49, 0x4f,
	0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53, 0x10, 0xcc, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x50,
	0x48, 0x50, 0x5f, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0xcd, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x50,
	0x48, 0x50, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x49, 0x43, 0x49, 0x4f,
	0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xce, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x50, 0x48,
	0x50, 0x5f, 0x58, 0x53, 0x53, 0x10, 0xcf, 0x01, 0x12, 0x0d, 0x0a, 0x08, 0x50, 0x48, 0x50, 0x5f,
	0x53, 0x51, 0x4c, 0x49, 0x10, 0xd0, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x50, 0x48, 0x50, 0x5f, 0x53,
	0x51, 0x4c, 0x5f, 0x44, 0x52, 0x41, 0x47, 0x10, 0xd1, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x48,
	0x50, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x41, 0x44, 0x5f, 0x48, 0x4f, 0x52, 0x53, 0x45, 0x10, 0xd2,
	0x01, 0x12, 0x0d, 0x0a, 0x08, 0x50, 0x48, 0x50, 0x5f, 0x53, 0x53, 0x52, 0x46, 0x10, 0xd3, 0x01,
	0x12, 0x0c, 0x0a, 0x07, 0x50, 0x48, 0x50, 0x5f, 0x58, 0x58, 0x45, 0x10, 0xd4, 0x01, 0x12, 0x0b,
	0x0a, 0x06, 0x47, 0x4f, 0x5f, 0x52, 0x43, 0x45, 0x10, 0xac, 0x02, 0x22, 0x4c, 0x0a, 0x23, 0x4e,
	0x6f, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x41, 0x6d, 0x73, 0x69, 0x42, 0x79, 0x41, 0x6d, 0x73, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x65, 0x64, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x65, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3c, 0x0a, 0x1f, 0x4e, 0x6f, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x41,
	0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x6c, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x22, 0x57, 0x0a, 0x17, 0x4e, 0x6f, 0x46, 0x69, 0x6c,
	0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x41,
	0x64, 0x64, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x76, 0x41, 0x64, 0x64, 0x72,
	0x22, 0x87, 0x03, 0x0a, 0x0b, 0x45, 0x74, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x11, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x0a,
	0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50,
	0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x75, 0x6d, 0x70, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61,
	0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xe0, 0x03, 0x0a, 0x1e, 0x4b,
	0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x50, 0x0a, 0x08, 0x52, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x75, 0x6c, 0x6e, 0x65, 0x72, 0x61,
	0x62, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x0e, 0x44, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x46, 0x69,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x22, 0x6f, 0x0a, 0x0e,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4d, 0x41, 0x4c, 0x49, 0x43, 0x49, 0x4f, 0x55, 0x53, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b,
	0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x56, 0x55, 0x4c, 0x4e, 0x45, 0x52, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x22, 0x90, 0x02,
	0x0a, 0x11, 0x4e, 0x75, 0x6c, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x22,
	0x0a, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x48, 0x6f, 0x6f, 0x6b, 0x46, 0x75, 0x6e, 0x63, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x61, 0x64, 0x64, 0x72, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x6d, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65, 0x6d, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x75, 0x6e, 0x63, 0x46, 0x6c, 0x61, 0x67,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x46, 0x75, 0x6e, 0x63, 0x46, 0x6c, 0x61,
	0x67, 0x73, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0xb7, 0x05, 0x0a, 0x0e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72, 0x63, 0x5f,
	0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x64, 0x73,
	0x74, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x64, 0x73, 0x74, 0x49,
	0x70, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x41, 0x0a, 0x0b,
	0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c,
	0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x36, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x69, 0x63, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x55, 0x4e, 0x4e, 0x45,
	0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x48, 0x00, 0x52, 0x08, 0x69,
	0x6e, 0x66, 0x6f, 0x49, 0x63, 0x6d, 0x70, 0x12, 0x33, 0x0a, 0x08, 0x69, 0x6e, 0x66, 0x6f, 0x5f,
	0x64, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x44, 0x4e,
	0x53, 0x48, 0x00, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x44, 0x6e, 0x73, 0x12, 0x33, 0x0a, 0x08,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x63, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e,
	0x46, 0x4f, 0x5f, 0x54, 0x43, 0x50, 0x48, 0x00, 0x52, 0x07, 0x69, 0x6e, 0x66, 0x6f, 0x54, 0x63,
	0x70, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x51, 0x0a, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x4c, 0x0a, 0x0a, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x14, 0x0a, 0x10, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x49, 0x43, 0x4d, 0x50, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4e, 0x53, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x54,
	0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x43, 0x50, 0x10, 0x02,
	0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x8d, 0x04, 0x0a, 0x10, 0x54,
	0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x65, 0x78,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x68, 0x65, 0x78,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x47, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x55, 0x4e,
	0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x2e, 0x49, 0x63,
	0x6d, 0x70, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x64, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x65,
	0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x65, 0x73, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x03, 0x6c,
	0x65, 0x6e, 0x22, 0xf5, 0x01, 0x0a, 0x0e, 0x49, 0x63, 0x6d, 0x70, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x44, 0x45,
	0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x4e,
	0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x28, 0x0a, 0x24, 0x49, 0x43, 0x4d, 0x50, 0x5f,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4d, 0x50,
	0x5f, 0x48, 0x49, 0x47, 0x48, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x10,
	0x01, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x4b, 0x45, 0x59, 0x57, 0x4f,
	0x52, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x44, 0x45, 0x54,
	0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x42, 0x49,
	0x47, 0x5f, 0x50, 0x41, 0x43, 0x4b, 0x45, 0x54, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x43,
	0x4d, 0x50, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x43, 0x4d, 0x50, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x08, 0x12, 0x2a,
	0x0a, 0x26, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x49, 0x43, 0x4d, 0x50, 0x5f, 0x41, 0x42, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c,
	0x5f, 0x45, 0x4e, 0x54, 0x52, 0x4f, 0x50, 0x59, 0x10, 0x10, 0x22, 0x45, 0x0a, 0x05, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x22, 0x58, 0x0a, 0x06, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x74, 0x74, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x74, 0x74, 0x6c, 0x22, 0xe7, 0x04, 0x0a, 0x0f,
	0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x44, 0x4e, 0x53, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x72, 0x72,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52,
	0x72, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x72, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x61, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x69, 0x74, 0x79, 0x52, 0x72, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x72, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0d, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x52, 0x72, 0x73, 0x12, 0x26,
	0x0a, 0x07, 0x71, 0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x07, 0x71,
	0x75, 0x65, 0x72, 0x69, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x07, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x52, 0x07, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x68, 0x65, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x68, 0x65, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74,
	0x72, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74,
	0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f,
	0x66, 0x6c, 0x61, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x44,
	0x4e, 0x53, 0x2e, 0x44, 0x6e, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06,
	0x6f, 0x66, 0x66, 0x65, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66,
	0x66, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0d, 0x52, 0x03, 0x6c, 0x65, 0x6e, 0x22, 0xac, 0x01, 0x0a, 0x0d, 0x44, 0x6e, 0x73, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x44, 0x4e, 0x53, 0x5f,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4e, 0x53, 0x5f,
	0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x44, 0x4e, 0x53, 0x5f,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4e, 0x53, 0x5f,
	0x48, 0x49, 0x47, 0x48, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x01,
	0x12, 0x2f, 0x0a, 0x2b, 0x44, 0x4e, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x44, 0x4e, 0x53, 0x5f, 0x41, 0x4e, 0x53, 0x5f, 0x41, 0x4c, 0x57, 0x41,
	0x59, 0x53, 0x5f, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x5f, 0x31, 0x10,
	0x02, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x4e, 0x53, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4e, 0x53, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f, 0x4c,
	0x4f, 0x4e, 0x47, 0x10, 0x04, 0x22, 0xd5, 0x04, 0x0a, 0x0f, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c,
	0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x43, 0x50, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x72, 0x63,
	0x50, 0x6f, 0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x64, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x17, 0x0a, 0x07, 0x73, 0x65, 0x71, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x06, 0x73, 0x65, 0x71, 0x4e, 0x75, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x63, 0x6b, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x61, 0x63, 0x6b, 0x4e, 0x75,
	0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x61, 0x4f, 0x66, 0x66, 0x73,
	0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x69, 0x6e, 0x64,
	0x6f, 0x77, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x77,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x72, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x70, 0x74, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x75, 0x72, 0x67, 0x65, 0x6e,
	0x74, 0x50, 0x74, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x68, 0x65, 0x78, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x68, 0x65, 0x78, 0x44, 0x61, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x74, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x74, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x55, 0x4e, 0x4e, 0x45, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x54, 0x43,
	0x50, 0x2e, 0x54, 0x63, 0x70, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x66, 0x66, 0x65, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x06, 0x6f, 0x66, 0x66,
	0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x65, 0x6e, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0d,
	0x52, 0x03, 0x6c, 0x65, 0x6e, 0x22, 0x85, 0x01, 0x0a, 0x0d, 0x54, 0x63, 0x70, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x43, 0x50, 0x5f, 0x44,
	0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x43, 0x50, 0x5f, 0x4e,
	0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x22, 0x54, 0x43, 0x50, 0x5f, 0x44,
	0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x43, 0x50, 0x5f, 0x48,
	0x49, 0x47, 0x48, 0x5f, 0x46, 0x52, 0x45, 0x51, 0x55, 0x45, 0x4e, 0x43, 0x59, 0x10, 0x01, 0x12,
	0x2c, 0x0a, 0x28, 0x54, 0x43, 0x50, 0x5f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x43, 0x50, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x41, 0x44, 0x44, 0x49,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02, 0x42, 0x2a, 0x5a,
	0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76,
	0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_agent_risk_mem_proto_rawDescOnce sync.Once
	file_agent_risk_mem_proto_rawDescData = file_agent_risk_mem_proto_rawDesc
)

func file_agent_risk_mem_proto_rawDescGZIP() []byte {
	file_agent_risk_mem_proto_rawDescOnce.Do(func() {
		file_agent_risk_mem_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_mem_proto_rawDescData)
	})
	return file_agent_risk_mem_proto_rawDescData
}

var file_agent_risk_mem_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_agent_risk_mem_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_agent_risk_mem_proto_goTypes = []any{
	(MemNoFileAttack_MemNoFileAttackType)(0),           // 0: agent.MemNoFileAttack.MemNoFileAttackType
	(NoFileAttackWebShellJava1_DetailInfoType)(0),      // 1: agent.NoFileAttackWebShellJava1.DetailInfoType
	(KernelVulnerableDriverRiskInfo_DriverRiskType)(0), // 2: agent.KernelVulnerableDriverRiskInfo.DriverRiskType
	(TunnelRiskInfo_TunnelType)(0),                     // 3: agent.TunnelRiskInfo.TunnelType
	(TUNNEL_INFO_ICMP_IcmpDetectType)(0),               // 4: agent.TUNNEL_INFO_ICMP.IcmpDetectType
	(TUNNEL_INFO_DNS_DnsDetectType)(0),                 // 5: agent.TUNNEL_INFO_DNS.DnsDetectType
	(TUNNEL_INFO_TCP_TcpDetectType)(0),                 // 6: agent.TUNNEL_INFO_TCP.TcpDetectType
	(*MemProtectRiskMemInfo)(nil),                      // 7: agent.MemProtectRiskMemInfo
	(*AnalyzerVirus)(nil),                              // 8: agent.AnalyzerVirus
	(*LoadRemoteLib)(nil),                              // 9: agent.LoadRemoteLib
	(*VirProtectExStkSpace)(nil),                       // 10: agent.VirProtectExStkSpace
	(*ReturnStackExec)(nil),                            // 11: agent.ReturnStackExec
	(*RemoteThread)(nil),                               // 12: agent.RemoteThread
	(*MemNoFileAttack)(nil),                            // 13: agent.MemNoFileAttack
	(*MemHeapSpray)(nil),                               // 14: agent.MemHeapSpray
	(*MemRop)(nil),                                     // 15: agent.MemRop
	(*MemLayoutShellCode)(nil),                         // 16: agent.MemLayoutShellCode
	(*MemStackPivot)(nil),                              // 17: agent.MemStackPivot
	(*MemStartProcess)(nil),                            // 18: agent.MemStartProcess
	(*MemRunningShellCode)(nil),                        // 19: agent.MemRunningShellCode
	(*MemEngineAttack)(nil),                            // 20: agent.MemEngineAttack
	(*NoFileAttackScriptSchedule)(nil),                 // 21: agent.NoFileAttackScriptSchedule
	(*NoFileAttackScriptFileCopy)(nil),                 // 22: agent.NoFileAttackScriptFileCopy
	(*NoFileAttackScriptHttp)(nil),                     // 23: agent.NoFileAttackScriptHttp
	(*NoFileAttackScriptWmiCmd)(nil),                   // 24: agent.NoFileAttackScriptWmiCmd
	(*NoFileAttackScriptWmiTerminateProcess)(nil),      // 25: agent.NoFileAttackScriptWmiTerminateProcess
	(*NoFileAttackScriptWmiOperReg)(nil),               // 26: agent.NoFileAttackScriptWmiOperReg
	(*NoFileAttackScriptWmiOperService)(nil),           // 27: agent.NoFileAttackScriptWmiOperService
	(*NoFileAttackScriptWmiExeQuery)(nil),              // 28: agent.NoFileAttackScriptWmiExeQuery
	(*NoFileAttackScriptLoadDll)(nil),                  // 29: agent.NoFileAttackScriptLoadDll
	(*NoFileAttackScriptGetAddress)(nil),               // 30: agent.NoFileAttackScriptGetAddress
	(*NoFileAttackScriptListen)(nil),                   // 31: agent.NoFileAttackScriptListen
	(*NoFileAttackScriptSocket)(nil),                   // 32: agent.NoFileAttackScriptSocket
	(*NoFileAttackScriptWin32Share)(nil),               // 33: agent.NoFileAttackScriptWin32Share
	(*NoFileAttackWebShellJava)(nil),                   // 34: agent.NoFileAttackWebShellJava
	(*NoFileAttackWMIPersistentBackdoor)(nil),          // 35: agent.NoFileAttackWMIPersistentBackdoor
	(*NoFileAttackWebShellJava1)(nil),                  // 36: agent.NoFileAttackWebShellJava1
	(*NoFileAttackScriptAmsiByAmsiContext)(nil),        // 37: agent.NoFileAttackScriptAmsiByAmsiContext
	(*NoFileAttackScriptAmsiDllHijack)(nil),            // 38: agent.NoFileAttackScriptAmsiDllHijack
	(*NoFileAttackScriptEmail)(nil),                    // 39: agent.NoFileAttackScriptEmail
	(*EtwRiskInfo)(nil),                                // 40: agent.EtwRiskInfo
	(*KernelVulnerableDriverRiskInfo)(nil),             // 41: agent.KernelVulnerableDriverRiskInfo
	(*NullAddressAttack)(nil),                          // 42: agent.NullAddressAttack
	(*TunnelRiskInfo)(nil),                             // 43: agent.TunnelRiskInfo
	(*TUNNEL_INFO_ICMP)(nil),                           // 44: agent.TUNNEL_INFO_ICMP
	(*Query)(nil),                                      // 45: agent.Query
	(*Answer)(nil),                                     // 46: agent.Answer
	(*TUNNEL_INFO_DNS)(nil),                            // 47: agent.TUNNEL_INFO_DNS
	(*TUNNEL_INFO_TCP)(nil),                            // 48: agent.TUNNEL_INFO_TCP
	(*ClientID)(nil),                                   // 49: agent.ClientID
	(*RiskHeader)(nil),                                 // 50: agent.RiskHeader
	(*ProcessInfo)(nil),                                // 51: agent.ProcessInfo
	(*ReportEvidenceInfo)(nil),                         // 52: agent.ReportEvidenceInfo
}
var file_agent_risk_mem_proto_depIdxs = []int32{
	49,  // 0: agent.MemProtectRiskMemInfo.baseInfo:type_name -> agent.ClientID
	9,   // 1: agent.MemProtectRiskMemInfo.loadRemoteLibList:type_name -> agent.LoadRemoteLib
	10,  // 2: agent.MemProtectRiskMemInfo.virProtectExStkSpaceList:type_name -> agent.VirProtectExStkSpace
	11,  // 3: agent.MemProtectRiskMemInfo.returnStackExecList:type_name -> agent.ReturnStackExec
	12,  // 4: agent.MemProtectRiskMemInfo.remoteThreadInject:type_name -> agent.RemoteThread
	13,  // 5: agent.MemProtectRiskMemInfo.memNoFileAttackList:type_name -> agent.MemNoFileAttack
	14,  // 6: agent.MemProtectRiskMemInfo.memHeapSprayList:type_name -> agent.MemHeapSpray
	8,   // 7: agent.MemProtectRiskMemInfo.analyzerVirusList:type_name -> agent.AnalyzerVirus
	15,  // 8: agent.MemProtectRiskMemInfo.memRopList:type_name -> agent.MemRop
	16,  // 9: agent.MemProtectRiskMemInfo.memLayoutShellCodeList:type_name -> agent.MemLayoutShellCode
	17,  // 10: agent.MemProtectRiskMemInfo.memStackPivotList:type_name -> agent.MemStackPivot
	19,  // 11: agent.MemProtectRiskMemInfo.memRunningShellCodeList:type_name -> agent.MemRunningShellCode
	18,  // 12: agent.MemProtectRiskMemInfo.memStartProcessList:type_name -> agent.MemStartProcess
	20,  // 13: agent.MemProtectRiskMemInfo.memEngineAttackList:type_name -> agent.MemEngineAttack
	43,  // 14: agent.MemProtectRiskMemInfo.tunnelRiskList:type_name -> agent.TunnelRiskInfo
	40,  // 15: agent.MemProtectRiskMemInfo.etwRiskList:type_name -> agent.EtwRiskInfo
	41,  // 16: agent.MemProtectRiskMemInfo.kernelVulnerableDriverRiskList:type_name -> agent.KernelVulnerableDriverRiskInfo
	50,  // 17: agent.AnalyzerVirus.header:type_name -> agent.RiskHeader
	50,  // 18: agent.LoadRemoteLib.header:type_name -> agent.RiskHeader
	51,  // 19: agent.LoadRemoteLib.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 20: agent.LoadRemoteLib.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 21: agent.VirProtectExStkSpace.header:type_name -> agent.RiskHeader
	51,  // 22: agent.VirProtectExStkSpace.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 23: agent.VirProtectExStkSpace.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 24: agent.ReturnStackExec.header:type_name -> agent.RiskHeader
	51,  // 25: agent.ReturnStackExec.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 26: agent.ReturnStackExec.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 27: agent.RemoteThread.header:type_name -> agent.RiskHeader
	50,  // 28: agent.MemNoFileAttack.header:type_name -> agent.RiskHeader
	51,  // 29: agent.MemNoFileAttack.SourceProcess:type_name -> agent.ProcessInfo
	51,  // 30: agent.MemNoFileAttack.TargetProcess:type_name -> agent.ProcessInfo
	0,   // 31: agent.MemNoFileAttack.AttackType:type_name -> agent.MemNoFileAttack.MemNoFileAttackType
	21,  // 32: agent.MemNoFileAttack.schedule:type_name -> agent.NoFileAttackScriptSchedule
	22,  // 33: agent.MemNoFileAttack.file_copy:type_name -> agent.NoFileAttackScriptFileCopy
	22,  // 34: agent.MemNoFileAttack.file_move:type_name -> agent.NoFileAttackScriptFileCopy
	23,  // 35: agent.MemNoFileAttack.http:type_name -> agent.NoFileAttackScriptHttp
	24,  // 36: agent.MemNoFileAttack.wim_cmd:type_name -> agent.NoFileAttackScriptWmiCmd
	29,  // 37: agent.MemNoFileAttack.load_dll:type_name -> agent.NoFileAttackScriptLoadDll
	30,  // 38: agent.MemNoFileAttack.get_address:type_name -> agent.NoFileAttackScriptGetAddress
	31,  // 39: agent.MemNoFileAttack.listen_port:type_name -> agent.NoFileAttackScriptListen
	32,  // 40: agent.MemNoFileAttack.socket_communication:type_name -> agent.NoFileAttackScriptSocket
	33,  // 41: agent.MemNoFileAttack.win32_share:type_name -> agent.NoFileAttackScriptWin32Share
	34,  // 42: agent.MemNoFileAttack.web_shell_java:type_name -> agent.NoFileAttackWebShellJava
	35,  // 43: agent.MemNoFileAttack.wmi_persistent_backdoor:type_name -> agent.NoFileAttackWMIPersistentBackdoor
	36,  // 44: agent.MemNoFileAttack.web_shell_java1:type_name -> agent.NoFileAttackWebShellJava1
	37,  // 45: agent.MemNoFileAttack.amsi_by_amsi_context:type_name -> agent.NoFileAttackScriptAmsiByAmsiContext
	38,  // 46: agent.MemNoFileAttack.amsi_dllhijack:type_name -> agent.NoFileAttackScriptAmsiDllHijack
	39,  // 47: agent.MemNoFileAttack.email:type_name -> agent.NoFileAttackScriptEmail
	25,  // 48: agent.MemNoFileAttack.wmi_terminate_process:type_name -> agent.NoFileAttackScriptWmiTerminateProcess
	26,  // 49: agent.MemNoFileAttack.wmi_reg_oper:type_name -> agent.NoFileAttackScriptWmiOperReg
	27,  // 50: agent.MemNoFileAttack.wmi_service_oper:type_name -> agent.NoFileAttackScriptWmiOperService
	28,  // 51: agent.MemNoFileAttack.wmi_query:type_name -> agent.NoFileAttackScriptWmiExeQuery
	51,  // 52: agent.MemNoFileAttack.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 53: agent.MemNoFileAttack.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 54: agent.MemHeapSpray.header:type_name -> agent.RiskHeader
	51,  // 55: agent.MemHeapSpray.Process:type_name -> agent.ProcessInfo
	51,  // 56: agent.MemHeapSpray.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 57: agent.MemHeapSpray.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 58: agent.MemRop.header:type_name -> agent.RiskHeader
	51,  // 59: agent.MemRop.Process:type_name -> agent.ProcessInfo
	51,  // 60: agent.MemRop.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 61: agent.MemRop.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 62: agent.MemLayoutShellCode.header:type_name -> agent.RiskHeader
	51,  // 63: agent.MemLayoutShellCode.Process:type_name -> agent.ProcessInfo
	51,  // 64: agent.MemLayoutShellCode.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 65: agent.MemLayoutShellCode.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 66: agent.MemStackPivot.header:type_name -> agent.RiskHeader
	51,  // 67: agent.MemStackPivot.Process:type_name -> agent.ProcessInfo
	51,  // 68: agent.MemStackPivot.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 69: agent.MemStackPivot.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 70: agent.MemStartProcess.header:type_name -> agent.RiskHeader
	51,  // 71: agent.MemStartProcess.Process:type_name -> agent.ProcessInfo
	50,  // 72: agent.MemRunningShellCode.header:type_name -> agent.RiskHeader
	51,  // 73: agent.MemRunningShellCode.Process:type_name -> agent.ProcessInfo
	51,  // 74: agent.MemRunningShellCode.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 75: agent.MemRunningShellCode.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 76: agent.MemEngineAttack.header:type_name -> agent.RiskHeader
	51,  // 77: agent.MemEngineAttack.Process:type_name -> agent.ProcessInfo
	51,  // 78: agent.MemEngineAttack.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 79: agent.MemEngineAttack.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	1,   // 80: agent.NoFileAttackWebShellJava1.infotype:type_name -> agent.NoFileAttackWebShellJava1.DetailInfoType
	50,  // 81: agent.EtwRiskInfo.header:type_name -> agent.RiskHeader
	51,  // 82: agent.EtwRiskInfo.TargetProcessInfo:type_name -> agent.ProcessInfo
	51,  // 83: agent.EtwRiskInfo.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 84: agent.EtwRiskInfo.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	50,  // 85: agent.KernelVulnerableDriverRiskInfo.header:type_name -> agent.RiskHeader
	2,   // 86: agent.KernelVulnerableDriverRiskInfo.RiskType:type_name -> agent.KernelVulnerableDriverRiskInfo.DriverRiskType
	51,  // 87: agent.KernelVulnerableDriverRiskInfo.DriverFileList:type_name -> agent.ProcessInfo
	51,  // 88: agent.KernelVulnerableDriverRiskInfo.Process:type_name -> agent.ProcessInfo
	50,  // 89: agent.NullAddressAttack.header:type_name -> agent.RiskHeader
	51,  // 90: agent.NullAddressAttack.ProcessInfoList:type_name -> agent.ProcessInfo
	50,  // 91: agent.TunnelRiskInfo.header:type_name -> agent.RiskHeader
	51,  // 92: agent.TunnelRiskInfo.Process:type_name -> agent.ProcessInfo
	3,   // 93: agent.TunnelRiskInfo.tunnel_type:type_name -> agent.TunnelRiskInfo.TunnelType
	44,  // 94: agent.TunnelRiskInfo.info_icmp:type_name -> agent.TUNNEL_INFO_ICMP
	47,  // 95: agent.TunnelRiskInfo.info_dns:type_name -> agent.TUNNEL_INFO_DNS
	48,  // 96: agent.TunnelRiskInfo.info_tcp:type_name -> agent.TUNNEL_INFO_TCP
	51,  // 97: agent.TunnelRiskInfo.ProcessInfoList:type_name -> agent.ProcessInfo
	52,  // 98: agent.TunnelRiskInfo.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	4,   // 99: agent.TUNNEL_INFO_ICMP.detect_flag:type_name -> agent.TUNNEL_INFO_ICMP.IcmpDetectType
	45,  // 100: agent.TUNNEL_INFO_DNS.queries:type_name -> agent.Query
	46,  // 101: agent.TUNNEL_INFO_DNS.answers:type_name -> agent.Answer
	5,   // 102: agent.TUNNEL_INFO_DNS.detect_flag:type_name -> agent.TUNNEL_INFO_DNS.DnsDetectType
	6,   // 103: agent.TUNNEL_INFO_TCP.detect_flag:type_name -> agent.TUNNEL_INFO_TCP.TcpDetectType
	104, // [104:104] is the sub-list for method output_type
	104, // [104:104] is the sub-list for method input_type
	104, // [104:104] is the sub-list for extension type_name
	104, // [104:104] is the sub-list for extension extendee
	0,   // [0:104] is the sub-list for field type_name
}

func init() { file_agent_risk_mem_proto_init() }
func file_agent_risk_mem_proto_init() {
	if File_agent_risk_mem_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_risk_mem_proto_msgTypes[6].OneofWrappers = []any{
		(*MemNoFileAttack_Schedule)(nil),
		(*MemNoFileAttack_FileCopy)(nil),
		(*MemNoFileAttack_FileMove)(nil),
		(*MemNoFileAttack_Http)(nil),
		(*MemNoFileAttack_WimCmd)(nil),
		(*MemNoFileAttack_LoadDll)(nil),
		(*MemNoFileAttack_GetAddress)(nil),
		(*MemNoFileAttack_ListenPort)(nil),
		(*MemNoFileAttack_SocketCommunication)(nil),
		(*MemNoFileAttack_Win32Share)(nil),
		(*MemNoFileAttack_WebShellJava)(nil),
		(*MemNoFileAttack_WmiPersistentBackdoor)(nil),
		(*MemNoFileAttack_WebShellJava1)(nil),
		(*MemNoFileAttack_AmsiByAmsiContext)(nil),
		(*MemNoFileAttack_AmsiDllhijack)(nil),
		(*MemNoFileAttack_Email)(nil),
		(*MemNoFileAttack_WmiTerminateProcess)(nil),
		(*MemNoFileAttack_WmiRegOper)(nil),
		(*MemNoFileAttack_WmiServiceOper)(nil),
		(*MemNoFileAttack_WmiQuery)(nil),
	}
	file_agent_risk_mem_proto_msgTypes[36].OneofWrappers = []any{
		(*TunnelRiskInfo_InfoIcmp)(nil),
		(*TunnelRiskInfo_InfoDns)(nil),
		(*TunnelRiskInfo_InfoTcp)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_mem_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_mem_proto_goTypes,
		DependencyIndexes: file_agent_risk_mem_proto_depIdxs,
		EnumInfos:         file_agent_risk_mem_proto_enumTypes,
		MessageInfos:      file_agent_risk_mem_proto_msgTypes,
	}.Build()
	File_agent_risk_mem_proto = out.File
	file_agent_risk_mem_proto_rawDesc = nil
	file_agent_risk_mem_proto_goTypes = nil
	file_agent_risk_mem_proto_depIdxs = nil
}
