// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/file_snapshot.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FileSnapshotInfos with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileSnapshotInfos) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSnapshotInfos with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileSnapshotInfosMultiError, or nil if none found.
func (m *FileSnapshotInfos) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSnapshotInfos) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for Timestamp

	// no validation rules for FileSeqNo

	// no validation rules for FileTotalNo

	// no validation rules for MachineID

	// no validation rules for OSType

	for idx, item := range m.GetInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileSnapshotInfosValidationError{
						field:  fmt.Sprintf("Infos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileSnapshotInfosValidationError{
						field:  fmt.Sprintf("Infos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileSnapshotInfosValidationError{
					field:  fmt.Sprintf("Infos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	{
		sorted_keys := make([]string, len(m.GetSignInfoDictionary()))
		i := 0
		for key := range m.GetSignInfoDictionary() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetSignInfoDictionary()[key]
			_ = val

			// no validation rules for SignInfoDictionary[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FileSnapshotInfosValidationError{
							field:  fmt.Sprintf("SignInfoDictionary[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FileSnapshotInfosValidationError{
							field:  fmt.Sprintf("SignInfoDictionary[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FileSnapshotInfosValidationError{
						field:  fmt.Sprintf("SignInfoDictionary[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FileSnapshotInfosMultiError(errors)
	}

	return nil
}

// FileSnapshotInfosMultiError is an error wrapping multiple validation errors
// returned by FileSnapshotInfos.ValidateAll() if the designated constraints
// aren't met.
type FileSnapshotInfosMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSnapshotInfosMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSnapshotInfosMultiError) AllErrors() []error { return m }

// FileSnapshotInfosValidationError is the validation error returned by
// FileSnapshotInfos.Validate if the designated constraints aren't met.
type FileSnapshotInfosValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSnapshotInfosValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSnapshotInfosValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSnapshotInfosValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSnapshotInfosValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSnapshotInfosValidationError) ErrorName() string {
	return "FileSnapshotInfosValidationError"
}

// Error satisfies the builtin error interface
func (e FileSnapshotInfosValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSnapshotInfos.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSnapshotInfosValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSnapshotInfosValidationError{}

// Validate checks the field values on FileSnapshotInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileSnapshotInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSnapshotInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileSnapshotInfoMultiError, or nil if none found.
func (m *FileSnapshotInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSnapshotInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportType

	for idx, item := range m.GetDirctoryInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileSnapshotInfoValidationError{
						field:  fmt.Sprintf("DirctoryInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileSnapshotInfoValidationError{
						field:  fmt.Sprintf("DirctoryInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileSnapshotInfoValidationError{
					field:  fmt.Sprintf("DirctoryInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FileSnapshotInfoMultiError(errors)
	}

	return nil
}

// FileSnapshotInfoMultiError is an error wrapping multiple validation errors
// returned by FileSnapshotInfo.ValidateAll() if the designated constraints
// aren't met.
type FileSnapshotInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSnapshotInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSnapshotInfoMultiError) AllErrors() []error { return m }

// FileSnapshotInfoValidationError is the validation error returned by
// FileSnapshotInfo.Validate if the designated constraints aren't met.
type FileSnapshotInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSnapshotInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSnapshotInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSnapshotInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSnapshotInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSnapshotInfoValidationError) ErrorName() string { return "FileSnapshotInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileSnapshotInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSnapshotInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSnapshotInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSnapshotInfoValidationError{}

// Validate checks the field values on DirectoryInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DirectoryInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirectoryInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DirectoryInfoMultiError, or
// nil if none found.
func (m *DirectoryInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DirectoryInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DirectoryName

	for idx, item := range m.GetFileInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DirectoryInfoValidationError{
						field:  fmt.Sprintf("FileInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DirectoryInfoValidationError{
						field:  fmt.Sprintf("FileInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DirectoryInfoValidationError{
					field:  fmt.Sprintf("FileInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DirectoryInfoMultiError(errors)
	}

	return nil
}

// DirectoryInfoMultiError is an error wrapping multiple validation errors
// returned by DirectoryInfo.ValidateAll() if the designated constraints
// aren't met.
type DirectoryInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirectoryInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirectoryInfoMultiError) AllErrors() []error { return m }

// DirectoryInfoValidationError is the validation error returned by
// DirectoryInfo.Validate if the designated constraints aren't met.
type DirectoryInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirectoryInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirectoryInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirectoryInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirectoryInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirectoryInfoValidationError) ErrorName() string { return "DirectoryInfoValidationError" }

// Error satisfies the builtin error interface
func (e DirectoryInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirectoryInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirectoryInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirectoryInfoValidationError{}

// Validate checks the field values on FileInfos with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileInfos) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileInfos with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileInfosMultiError, or nil
// if none found.
func (m *FileInfos) ValidateAll() error {
	return m.validate(true)
}

func (m *FileInfos) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for FileSize

	// no validation rules for CreateTime

	// no validation rules for ModifyTime

	// no validation rules for LastAccessTime

	// no validation rules for FileMD5

	// no validation rules for FileSha1

	// no validation rules for FileSha256

	// no validation rules for FileType

	// no validation rules for TlshHash

	// no validation rules for ImpHash

	if len(errors) > 0 {
		return FileInfosMultiError(errors)
	}

	return nil
}

// FileInfosMultiError is an error wrapping multiple validation errors returned
// by FileInfos.ValidateAll() if the designated constraints aren't met.
type FileInfosMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileInfosMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileInfosMultiError) AllErrors() []error { return m }

// FileInfosValidationError is the validation error returned by
// FileInfos.Validate if the designated constraints aren't met.
type FileInfosValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileInfosValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileInfosValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileInfosValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileInfosValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileInfosValidationError) ErrorName() string { return "FileInfosValidationError" }

// Error satisfies the builtin error interface
func (e FileInfosValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileInfos.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileInfosValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileInfosValidationError{}
