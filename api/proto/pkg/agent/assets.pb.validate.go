// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/assets.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectAssetsInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectAssetsInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectAssetsInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectAssetsInfoMultiError, or nil if none found.
func (m *MemProtectAssetsInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectAssetsInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectAssetsInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectAssetsInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectAssetsInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHostInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("HostInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("HostInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("HostInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAccountInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("AccountInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("AccountInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("AccountInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEnvInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("EnvInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("EnvInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("EnvInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetKernelInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("KernelInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("KernelInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("KernelInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetServiceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("ServiceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("ServiceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("ServiceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("ProcInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  fmt.Sprintf("ProcInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  fmt.Sprintf("ProcInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.HasDomainInfo.(type) {
	case *MemProtectAssetsInfo_DomainInfo:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasDomainInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDomainInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "DomainInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "DomainInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDomainInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "DomainInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWebMiddleware.(type) {
	case *MemProtectAssetsInfo_MiddlewareList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasWebMiddleware",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMiddlewareList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "MiddlewareList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "MiddlewareList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMiddlewareList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "MiddlewareList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWebSiteInfo.(type) {
	case *MemProtectAssetsInfo_WebSiteInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasWebSiteInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebSiteInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebSiteInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebSiteInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebSiteInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "WebSiteInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWebFrame.(type) {
	case *MemProtectAssetsInfo_WebFrameList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasWebFrame",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebFrameList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebFrameList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebFrameList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebFrameList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "WebFrameList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasHostApplication.(type) {
	case *MemProtectAssetsInfo_HostApplicationList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasHostApplication",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHostApplicationList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "HostApplicationList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "HostApplicationList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHostApplicationList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "HostApplicationList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasPackageInfo.(type) {
	case *MemProtectAssetsInfo_PackageInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasPackageInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPackageInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "PackageInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "PackageInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPackageInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "PackageInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasPythonPackageInfo.(type) {
	case *MemProtectAssetsInfo_PythonPackageInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasPythonPackageInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPythonPackageInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "PythonPackageInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "PythonPackageInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPythonPackageInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "PythonPackageInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWebApp.(type) {
	case *MemProtectAssetsInfo_WebAppList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasWebApp",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebAppList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebAppList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "WebAppList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebAppList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "WebAppList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasNpmInfo.(type) {
	case *MemProtectAssetsInfo_NpmInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasNpmInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNpmInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "NpmInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "NpmInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNpmInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "NpmInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasEthInfo.(type) {
	case *MemProtectAssetsInfo_EthInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasEthInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEthInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "EthInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "EthInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEthInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "EthInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasResourceInfo.(type) {
	case *MemProtectAssetsInfo_ResourceUtilInfo:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasResourceInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResourceUtilInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "ResourceUtilInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "ResourceUtilInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResourceUtilInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "ResourceUtilInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasJobTaskInfo.(type) {
	case *MemProtectAssetsInfo_JobTaskInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasJobTaskInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetJobTaskInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "JobTaskInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "JobTaskInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetJobTaskInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "JobTaskInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasAppWeakPwdInfo.(type) {
	case *MemProtectAssetsInfo_AppWeakPwdInfoList:
		if v == nil {
			err := MemProtectAssetsInfoValidationError{
				field:  "HasAppWeakPwdInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAppWeakPwdInfoList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "AppWeakPwdInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAssetsInfoValidationError{
						field:  "AppWeakPwdInfoList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAppWeakPwdInfoList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAssetsInfoValidationError{
					field:  "AppWeakPwdInfoList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MemProtectAssetsInfoMultiError(errors)
	}

	return nil
}

// MemProtectAssetsInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectAssetsInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectAssetsInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectAssetsInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectAssetsInfoMultiError) AllErrors() []error { return m }

// MemProtectAssetsInfoValidationError is the validation error returned by
// MemProtectAssetsInfo.Validate if the designated constraints aren't met.
type MemProtectAssetsInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectAssetsInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectAssetsInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectAssetsInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectAssetsInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectAssetsInfoValidationError) ErrorName() string {
	return "MemProtectAssetsInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectAssetsInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectAssetsInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectAssetsInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectAssetsInfoValidationError{}

// Validate checks the field values on HostInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HostInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostInformationMultiError, or nil if none found.
func (m *HostInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *HostInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OsInfo

	// no validation rules for ComputerName

	// no validation rules for UserName

	// no validation rules for IEVersion

	// no validation rules for IP

	// no validation rules for MAC

	// no validation rules for IPMASK

	// no validation rules for Gateway

	// no validation rules for MemSize

	// no validation rules for TCPPorts

	// no validation rules for UDPPorts

	// no validation rules for CpuUtilization

	// no validation rules for MemUtilization

	// no validation rules for DiskUtilization

	// no validation rules for AgentVer

	// no validation rules for VersionCode

	// no validation rules for AgentKernelVer

	// no validation rules for LogonUserName

	// no validation rules for Arch

	// no validation rules for CpuCores

	// no validation rules for DiskSize

	// no validation rules for JdkVersion

	// no validation rules for WebMiddlewareVersion

	// no validation rules for DriverLibVersion

	// no validation rules for BehaviorLibVersion

	// no validation rules for EnvLibVersion

	// no validation rules for AgentJdkVersion

	// no validation rules for LinuxOSInfoLong

	// no validation rules for LinuxOSInfoShort

	// no validation rules for RecentlyLogonUserName

	// no validation rules for HashEngineVersion

	// no validation rules for Sha256EngineVersion

	// no validation rules for NgavLibVersion

	// no validation rules for HostIpPolicyType

	// no validation rules for AgentInstallTime

	// no validation rules for DriverInstallTime

	for idx, item := range m.GetEthInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostInformationValidationError{
						field:  fmt.Sprintf("EthInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostInformationValidationError{
						field:  fmt.Sprintf("EthInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostInformationValidationError{
					field:  fmt.Sprintf("EthInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsResourceUpdate

	// no validation rules for RaspRuleVersion

	// no validation rules for BaselineLibVersion

	// no validation rules for BaselineOS

	// no validation rules for DomainWhiteLibVersion

	// no validation rules for FileSignComWhiteLibVersion

	// no validation rules for OsInfoDisplay

	// no validation rules for DomainUserName

	// no validation rules for FileDriverBlockLibVersion

	// no validation rules for WinOSInfoShort

	// no validation rules for ProxyIpPort

	if len(errors) > 0 {
		return HostInformationMultiError(errors)
	}

	return nil
}

// HostInformationMultiError is an error wrapping multiple validation errors
// returned by HostInformation.ValidateAll() if the designated constraints
// aren't met.
type HostInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostInformationMultiError) AllErrors() []error { return m }

// HostInformationValidationError is the validation error returned by
// HostInformation.Validate if the designated constraints aren't met.
type HostInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostInformationValidationError) ErrorName() string { return "HostInformationValidationError" }

// Error satisfies the builtin error interface
func (e HostInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostInformationValidationError{}

// Validate checks the field values on EtherNetList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EtherNetList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EtherNetList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EtherNetListMultiError, or
// nil if none found.
func (m *EtherNetList) ValidateAll() error {
	return m.validate(true)
}

func (m *EtherNetList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HostIpPolicyType

	// no validation rules for HostIp

	for idx, item := range m.GetEthInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EtherNetListValidationError{
						field:  fmt.Sprintf("EthInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EtherNetListValidationError{
						field:  fmt.Sprintf("EthInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EtherNetListValidationError{
					field:  fmt.Sprintf("EthInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EtherNetListMultiError(errors)
	}

	return nil
}

// EtherNetListMultiError is an error wrapping multiple validation errors
// returned by EtherNetList.ValidateAll() if the designated constraints aren't met.
type EtherNetListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EtherNetListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EtherNetListMultiError) AllErrors() []error { return m }

// EtherNetListValidationError is the validation error returned by
// EtherNetList.Validate if the designated constraints aren't met.
type EtherNetListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EtherNetListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EtherNetListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EtherNetListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EtherNetListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EtherNetListValidationError) ErrorName() string { return "EtherNetListValidationError" }

// Error satisfies the builtin error interface
func (e EtherNetListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEtherNetList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EtherNetListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EtherNetListValidationError{}

// Validate checks the field values on ResourceUtilization with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResourceUtilization) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceUtilization with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceUtilizationMultiError, or nil if none found.
func (m *ResourceUtilization) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceUtilization) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectionTime

	// no validation rules for CpuUtilization

	// no validation rules for MemUtilization

	// no validation rules for DiskUtilization

	// no validation rules for CpuAlarmEnable

	// no validation rules for CpuThreshold

	// no validation rules for MemAlarmEnable

	// no validation rules for MemThreshold

	// no validation rules for DiskAlarmEnable

	// no validation rules for DiskThreshold

	if len(errors) > 0 {
		return ResourceUtilizationMultiError(errors)
	}

	return nil
}

// ResourceUtilizationMultiError is an error wrapping multiple validation
// errors returned by ResourceUtilization.ValidateAll() if the designated
// constraints aren't met.
type ResourceUtilizationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceUtilizationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceUtilizationMultiError) AllErrors() []error { return m }

// ResourceUtilizationValidationError is the validation error returned by
// ResourceUtilization.Validate if the designated constraints aren't met.
type ResourceUtilizationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceUtilizationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceUtilizationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceUtilizationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceUtilizationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceUtilizationValidationError) ErrorName() string {
	return "ResourceUtilizationValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceUtilizationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceUtilization.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceUtilizationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceUtilizationValidationError{}

// Validate checks the field values on ResourceAlarmList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResourceAlarmList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceAlarmList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceAlarmListMultiError, or nil if none found.
func (m *ResourceAlarmList) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceAlarmList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAlarmList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ResourceAlarmListValidationError{
						field:  fmt.Sprintf("AlarmList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ResourceAlarmListValidationError{
						field:  fmt.Sprintf("AlarmList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ResourceAlarmListValidationError{
					field:  fmt.Sprintf("AlarmList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ResourceAlarmListMultiError(errors)
	}

	return nil
}

// ResourceAlarmListMultiError is an error wrapping multiple validation errors
// returned by ResourceAlarmList.ValidateAll() if the designated constraints
// aren't met.
type ResourceAlarmListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceAlarmListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceAlarmListMultiError) AllErrors() []error { return m }

// ResourceAlarmListValidationError is the validation error returned by
// ResourceAlarmList.Validate if the designated constraints aren't met.
type ResourceAlarmListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceAlarmListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceAlarmListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceAlarmListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceAlarmListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceAlarmListValidationError) ErrorName() string {
	return "ResourceAlarmListValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceAlarmListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceAlarmList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceAlarmListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceAlarmListValidationError{}

// Validate checks the field values on ResourceAlarm with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResourceAlarm) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceAlarm with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourceAlarmMultiError, or
// nil if none found.
func (m *ResourceAlarm) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceAlarm) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceType

	// no validation rules for AlarmTime

	// no validation rules for Utilization

	// no validation rules for PolicyThreshold

	if len(errors) > 0 {
		return ResourceAlarmMultiError(errors)
	}

	return nil
}

// ResourceAlarmMultiError is an error wrapping multiple validation errors
// returned by ResourceAlarm.ValidateAll() if the designated constraints
// aren't met.
type ResourceAlarmMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceAlarmMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceAlarmMultiError) AllErrors() []error { return m }

// ResourceAlarmValidationError is the validation error returned by
// ResourceAlarm.Validate if the designated constraints aren't met.
type ResourceAlarmValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceAlarmValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceAlarmValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceAlarmValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceAlarmValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceAlarmValidationError) ErrorName() string { return "ResourceAlarmValidationError" }

// Error satisfies the builtin error interface
func (e ResourceAlarmValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceAlarm.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceAlarmValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceAlarmValidationError{}

// Validate checks the field values on AccountInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountInformationMultiError, or nil if none found.
func (m *AccountInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserName

	// no validation rules for UserId

	// no validation rules for GroupId

	// no validation rules for State

	// no validation rules for ExpireDate

	// no validation rules for IsSuperUser

	// no validation rules for LoginPort

	// no validation rules for LoginIP

	// no validation rules for LoginTime

	// no validation rules for PasswordChangeTime

	// no validation rules for WeakPassword

	// no validation rules for Interactive

	// no validation rules for HomePath

	// no validation rules for IsClone

	// no validation rules for IsHidden

	// no validation rules for AccountExpireDate

	// no validation rules for Shell

	// no validation rules for Description

	// no validation rules for IsDomainAccount

	// no validation rules for WeakpwdRegex

	// no validation rules for WeakpwdType

	for idx, item := range m.GetWeakpwdhistory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountInformationValidationError{
						field:  fmt.Sprintf("Weakpwdhistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountInformationValidationError{
						field:  fmt.Sprintf("Weakpwdhistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountInformationValidationError{
					field:  fmt.Sprintf("Weakpwdhistory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountInformationMultiError(errors)
	}

	return nil
}

// AccountInformationMultiError is an error wrapping multiple validation errors
// returned by AccountInformation.ValidateAll() if the designated constraints
// aren't met.
type AccountInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInformationMultiError) AllErrors() []error { return m }

// AccountInformationValidationError is the validation error returned by
// AccountInformation.Validate if the designated constraints aren't met.
type AccountInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInformationValidationError) ErrorName() string {
	return "AccountInformationValidationError"
}

// Error satisfies the builtin error interface
func (e AccountInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInformationValidationError{}

// Validate checks the field values on WeakPasswordhistory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WeakPasswordhistory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WeakPasswordhistory with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WeakPasswordhistoryMultiError, or nil if none found.
func (m *WeakPasswordhistory) ValidateAll() error {
	return m.validate(true)
}

func (m *WeakPasswordhistory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for LoginTimes

	if len(errors) > 0 {
		return WeakPasswordhistoryMultiError(errors)
	}

	return nil
}

// WeakPasswordhistoryMultiError is an error wrapping multiple validation
// errors returned by WeakPasswordhistory.ValidateAll() if the designated
// constraints aren't met.
type WeakPasswordhistoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WeakPasswordhistoryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WeakPasswordhistoryMultiError) AllErrors() []error { return m }

// WeakPasswordhistoryValidationError is the validation error returned by
// WeakPasswordhistory.Validate if the designated constraints aren't met.
type WeakPasswordhistoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WeakPasswordhistoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WeakPasswordhistoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WeakPasswordhistoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WeakPasswordhistoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WeakPasswordhistoryValidationError) ErrorName() string {
	return "WeakPasswordhistoryValidationError"
}

// Error satisfies the builtin error interface
func (e WeakPasswordhistoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWeakPasswordhistory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WeakPasswordhistoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WeakPasswordhistoryValidationError{}

// Validate checks the field values on EnvInformation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EnvInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnvInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EnvInformationMultiError,
// or nil if none found.
func (m *EnvInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *EnvInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	if len(errors) > 0 {
		return EnvInformationMultiError(errors)
	}

	return nil
}

// EnvInformationMultiError is an error wrapping multiple validation errors
// returned by EnvInformation.ValidateAll() if the designated constraints
// aren't met.
type EnvInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnvInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnvInformationMultiError) AllErrors() []error { return m }

// EnvInformationValidationError is the validation error returned by
// EnvInformation.Validate if the designated constraints aren't met.
type EnvInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnvInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnvInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnvInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnvInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnvInformationValidationError) ErrorName() string { return "EnvInformationValidationError" }

// Error satisfies the builtin error interface
func (e EnvInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnvInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnvInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnvInformationValidationError{}

// Validate checks the field values on KernelInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KernelInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KernelInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KernelInformationMultiError, or nil if none found.
func (m *KernelInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *KernelInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Path

	// no validation rules for License

	// no validation rules for Signature

	// no validation rules for Description

	// no validation rules for Services

	// no validation rules for Loaded

	// no validation rules for RiskLevel

	// no validation rules for ImageSize

	// no validation rules for Sha256

	// no validation rules for Company

	if len(errors) > 0 {
		return KernelInformationMultiError(errors)
	}

	return nil
}

// KernelInformationMultiError is an error wrapping multiple validation errors
// returned by KernelInformation.ValidateAll() if the designated constraints
// aren't met.
type KernelInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KernelInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KernelInformationMultiError) AllErrors() []error { return m }

// KernelInformationValidationError is the validation error returned by
// KernelInformation.Validate if the designated constraints aren't met.
type KernelInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KernelInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KernelInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KernelInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KernelInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KernelInformationValidationError) ErrorName() string {
	return "KernelInformationValidationError"
}

// Error satisfies the builtin error interface
func (e KernelInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKernelInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KernelInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KernelInformationValidationError{}

// Validate checks the field values on ServiceInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceInformationMultiError, or nil if none found.
func (m *ServiceInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Path

	// no validation rules for State

	// no validation rules for StartTime

	// no validation rules for StartType

	// no validation rules for Company

	// no validation rules for DllPath

	// no validation rules for DllCompany

	// no validation rules for Sha256

	// no validation rules for Sha256Dll

	// no validation rules for ProcessID

	// no validation rules for Description

	if len(errors) > 0 {
		return ServiceInformationMultiError(errors)
	}

	return nil
}

// ServiceInformationMultiError is an error wrapping multiple validation errors
// returned by ServiceInformation.ValidateAll() if the designated constraints
// aren't met.
type ServiceInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceInformationMultiError) AllErrors() []error { return m }

// ServiceInformationValidationError is the validation error returned by
// ServiceInformation.Validate if the designated constraints aren't met.
type ServiceInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceInformationValidationError) ErrorName() string {
	return "ServiceInformationValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceInformationValidationError{}

// Validate checks the field values on ProcInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcInformationMultiError, or nil if none found.
func (m *ProcInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Path

	// no validation rules for Type

	// no validation rules for UserName

	// no validation rules for ThreadNum

	// no validation rules for MemRss

	// no validation rules for MemVss

	// no validation rules for IoRead

	// no validation rules for IoWrite

	// no validation rules for CpuUtilization

	// no validation rules for StartTime

	// no validation rules for ProcessID

	// no validation rules for CommandInfo

	// no validation rules for Description

	// no validation rules for MemUtilization

	for idx, item := range m.GetPorts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProcInformationValidationError{
						field:  fmt.Sprintf("Ports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProcInformationValidationError{
						field:  fmt.Sprintf("Ports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProcInformationValidationError{
					field:  fmt.Sprintf("Ports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ProcInformationMultiError(errors)
	}

	return nil
}

// ProcInformationMultiError is an error wrapping multiple validation errors
// returned by ProcInformation.ValidateAll() if the designated constraints
// aren't met.
type ProcInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcInformationMultiError) AllErrors() []error { return m }

// ProcInformationValidationError is the validation error returned by
// ProcInformation.Validate if the designated constraints aren't met.
type ProcInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcInformationValidationError) ErrorName() string { return "ProcInformationValidationError" }

// Error satisfies the builtin error interface
func (e ProcInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcInformationValidationError{}

// Validate checks the field values on PortInformation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PortInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortInformation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortInformationMultiError, or nil if none found.
func (m *PortInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *PortInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Port

	// no validation rules for Protocol

	if len(errors) > 0 {
		return PortInformationMultiError(errors)
	}

	return nil
}

// PortInformationMultiError is an error wrapping multiple validation errors
// returned by PortInformation.ValidateAll() if the designated constraints
// aren't met.
type PortInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortInformationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortInformationMultiError) AllErrors() []error { return m }

// PortInformationValidationError is the validation error returned by
// PortInformation.Validate if the designated constraints aren't met.
type PortInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortInformationValidationError) ErrorName() string { return "PortInformationValidationError" }

// Error satisfies the builtin error interface
func (e PortInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortInformationValidationError{}

// Validate checks the field values on DomainInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DomainInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DomainInfoMultiError, or
// nil if none found.
func (m *DomainInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BaseDN

	// no validation rules for DomainName

	// no validation rules for Dns

	// no validation rules for NetBIOS

	for idx, item := range m.GetNetShareInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DomainInfoValidationError{
						field:  fmt.Sprintf("NetShareInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DomainInfoValidationError{
						field:  fmt.Sprintf("NetShareInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DomainInfoValidationError{
					field:  fmt.Sprintf("NetShareInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DomainInfoMultiError(errors)
	}

	return nil
}

// DomainInfoMultiError is an error wrapping multiple validation errors
// returned by DomainInfo.ValidateAll() if the designated constraints aren't met.
type DomainInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainInfoMultiError) AllErrors() []error { return m }

// DomainInfoValidationError is the validation error returned by
// DomainInfo.Validate if the designated constraints aren't met.
type DomainInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainInfoValidationError) ErrorName() string { return "DomainInfoValidationError" }

// Error satisfies the builtin error interface
func (e DomainInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainInfoValidationError{}

// Validate checks the field values on NetShareInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NetShareInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetShareInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NetShareInfoMultiError, or
// nil if none found.
func (m *NetShareInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetShareInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Path

	if len(errors) > 0 {
		return NetShareInfoMultiError(errors)
	}

	return nil
}

// NetShareInfoMultiError is an error wrapping multiple validation errors
// returned by NetShareInfo.ValidateAll() if the designated constraints aren't met.
type NetShareInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetShareInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetShareInfoMultiError) AllErrors() []error { return m }

// NetShareInfoValidationError is the validation error returned by
// NetShareInfo.Validate if the designated constraints aren't met.
type NetShareInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetShareInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetShareInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetShareInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetShareInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetShareInfoValidationError) ErrorName() string { return "NetShareInfoValidationError" }

// Error satisfies the builtin error interface
func (e NetShareInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetShareInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetShareInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetShareInfoValidationError{}

// Validate checks the field values on JarInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JarInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JarInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in JarInfoMultiError, or nil if none found.
func (m *JarInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *JarInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Summary

	// no validation rules for IsExec

	// no validation rules for Version

	// no validation rules for Path

	// no validation rules for BusiName

	// no validation rules for Pid

	if len(errors) > 0 {
		return JarInfoMultiError(errors)
	}

	return nil
}

// JarInfoMultiError is an error wrapping multiple validation errors returned
// by JarInfo.ValidateAll() if the designated constraints aren't met.
type JarInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JarInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JarInfoMultiError) AllErrors() []error { return m }

// JarInfoValidationError is the validation error returned by JarInfo.Validate
// if the designated constraints aren't met.
type JarInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JarInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JarInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JarInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JarInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JarInfoValidationError) ErrorName() string { return "JarInfoValidationError" }

// Error satisfies the builtin error interface
func (e JarInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJarInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JarInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JarInfoValidationError{}

// Validate checks the field values on WebMiddlewareList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WebMiddlewareList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebMiddlewareList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebMiddlewareListMultiError, or nil if none found.
func (m *WebMiddlewareList) ValidateAll() error {
	return m.validate(true)
}

func (m *WebMiddlewareList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWebMiddleware() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebMiddlewareListValidationError{
						field:  fmt.Sprintf("WebMiddleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebMiddlewareListValidationError{
						field:  fmt.Sprintf("WebMiddleware[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebMiddlewareListValidationError{
					field:  fmt.Sprintf("WebMiddleware[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebMiddlewareListMultiError(errors)
	}

	return nil
}

// WebMiddlewareListMultiError is an error wrapping multiple validation errors
// returned by WebMiddlewareList.ValidateAll() if the designated constraints
// aren't met.
type WebMiddlewareListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebMiddlewareListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebMiddlewareListMultiError) AllErrors() []error { return m }

// WebMiddlewareListValidationError is the validation error returned by
// WebMiddlewareList.Validate if the designated constraints aren't met.
type WebMiddlewareListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebMiddlewareListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebMiddlewareListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebMiddlewareListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebMiddlewareListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebMiddlewareListValidationError) ErrorName() string {
	return "WebMiddlewareListValidationError"
}

// Error satisfies the builtin error interface
func (e WebMiddlewareListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebMiddlewareList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebMiddlewareListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebMiddlewareListValidationError{}

// Validate checks the field values on WebMiddleware with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebMiddleware) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebMiddleware with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebMiddlewareMultiError, or
// nil if none found.
func (m *WebMiddleware) ValidateAll() error {
	return m.validate(true)
}

func (m *WebMiddleware) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WebName

	// no validation rules for WebVersion

	// no validation rules for BinaryPath

	// no validation rules for StartUser

	// no validation rules for ConfigurationPath

	// no validation rules for ProcessId

	for idx, item := range m.GetJarInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebMiddlewareValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebMiddlewareValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebMiddlewareValidationError{
					field:  fmt.Sprintf("JarInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebMiddlewareMultiError(errors)
	}

	return nil
}

// WebMiddlewareMultiError is an error wrapping multiple validation errors
// returned by WebMiddleware.ValidateAll() if the designated constraints
// aren't met.
type WebMiddlewareMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebMiddlewareMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebMiddlewareMultiError) AllErrors() []error { return m }

// WebMiddlewareValidationError is the validation error returned by
// WebMiddleware.Validate if the designated constraints aren't met.
type WebMiddlewareValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebMiddlewareValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebMiddlewareValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebMiddlewareValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebMiddlewareValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebMiddlewareValidationError) ErrorName() string { return "WebMiddlewareValidationError" }

// Error satisfies the builtin error interface
func (e WebMiddlewareValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebMiddleware.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebMiddlewareValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebMiddlewareValidationError{}

// Validate checks the field values on WebSiteAppInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebSiteAppInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebSiteAppInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebSiteAppInfoMultiError,
// or nil if none found.
func (m *WebSiteAppInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *WebSiteAppInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VirtualPath

	// no validation rules for RealPath

	// no validation rules for Author

	// no validation rules for Group

	// no validation rules for Authority

	if len(errors) > 0 {
		return WebSiteAppInfoMultiError(errors)
	}

	return nil
}

// WebSiteAppInfoMultiError is an error wrapping multiple validation errors
// returned by WebSiteAppInfo.ValidateAll() if the designated constraints
// aren't met.
type WebSiteAppInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebSiteAppInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebSiteAppInfoMultiError) AllErrors() []error { return m }

// WebSiteAppInfoValidationError is the validation error returned by
// WebSiteAppInfo.Validate if the designated constraints aren't met.
type WebSiteAppInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebSiteAppInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebSiteAppInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebSiteAppInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebSiteAppInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebSiteAppInfoValidationError) ErrorName() string { return "WebSiteAppInfoValidationError" }

// Error satisfies the builtin error interface
func (e WebSiteAppInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebSiteAppInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebSiteAppInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebSiteAppInfoValidationError{}

// Validate checks the field values on WebSiteInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebSiteInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebSiteInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebSiteInfoMultiError, or
// nil if none found.
func (m *WebSiteInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *WebSiteInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for Port

	// no validation rules for Protocol

	// no validation rules for User

	// no validation rules for ServerName

	// no validation rules for WarDir

	// no validation rules for RootDir

	// no validation rules for RootDirAuthority

	// no validation rules for SiteCount

	for idx, item := range m.GetWebSiteApps() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebSiteInfoValidationError{
						field:  fmt.Sprintf("WebSiteApps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebSiteInfoValidationError{
						field:  fmt.Sprintf("WebSiteApps[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebSiteInfoValidationError{
					field:  fmt.Sprintf("WebSiteApps[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebSiteInfoMultiError(errors)
	}

	return nil
}

// WebSiteInfoMultiError is an error wrapping multiple validation errors
// returned by WebSiteInfo.ValidateAll() if the designated constraints aren't met.
type WebSiteInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebSiteInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebSiteInfoMultiError) AllErrors() []error { return m }

// WebSiteInfoValidationError is the validation error returned by
// WebSiteInfo.Validate if the designated constraints aren't met.
type WebSiteInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebSiteInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebSiteInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebSiteInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebSiteInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebSiteInfoValidationError) ErrorName() string { return "WebSiteInfoValidationError" }

// Error satisfies the builtin error interface
func (e WebSiteInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebSiteInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebSiteInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebSiteInfoValidationError{}

// Validate checks the field values on WebSiteInfoList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WebSiteInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebSiteInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebSiteInfoListMultiError, or nil if none found.
func (m *WebSiteInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *WebSiteInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWebSiteInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebSiteInfoListValidationError{
						field:  fmt.Sprintf("WebSiteInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebSiteInfoListValidationError{
						field:  fmt.Sprintf("WebSiteInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebSiteInfoListValidationError{
					field:  fmt.Sprintf("WebSiteInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebSiteInfoListMultiError(errors)
	}

	return nil
}

// WebSiteInfoListMultiError is an error wrapping multiple validation errors
// returned by WebSiteInfoList.ValidateAll() if the designated constraints
// aren't met.
type WebSiteInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebSiteInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebSiteInfoListMultiError) AllErrors() []error { return m }

// WebSiteInfoListValidationError is the validation error returned by
// WebSiteInfoList.Validate if the designated constraints aren't met.
type WebSiteInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebSiteInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebSiteInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebSiteInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebSiteInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebSiteInfoListValidationError) ErrorName() string { return "WebSiteInfoListValidationError" }

// Error satisfies the builtin error interface
func (e WebSiteInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebSiteInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebSiteInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebSiteInfoListValidationError{}

// Validate checks the field values on WebFrame with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebFrame) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebFrame with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebFrameMultiError, or nil
// if none found.
func (m *WebFrame) ValidateAll() error {
	return m.validate(true)
}

func (m *WebFrame) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FrameName

	// no validation rules for FrameVersion

	// no validation rules for WebServer

	// no validation rules for WebPath

	// no validation rules for FrameLanguage

	for idx, item := range m.GetJarInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebFrameValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebFrameValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebFrameValidationError{
					field:  fmt.Sprintf("JarInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebFrameMultiError(errors)
	}

	return nil
}

// WebFrameMultiError is an error wrapping multiple validation errors returned
// by WebFrame.ValidateAll() if the designated constraints aren't met.
type WebFrameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebFrameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebFrameMultiError) AllErrors() []error { return m }

// WebFrameValidationError is the validation error returned by
// WebFrame.Validate if the designated constraints aren't met.
type WebFrameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebFrameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebFrameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebFrameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebFrameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebFrameValidationError) ErrorName() string { return "WebFrameValidationError" }

// Error satisfies the builtin error interface
func (e WebFrameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebFrame.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebFrameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebFrameValidationError{}

// Validate checks the field values on WebFrameList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebFrameList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebFrameList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebFrameListMultiError, or
// nil if none found.
func (m *WebFrameList) ValidateAll() error {
	return m.validate(true)
}

func (m *WebFrameList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWebFrameInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebFrameListValidationError{
						field:  fmt.Sprintf("WebFrameInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebFrameListValidationError{
						field:  fmt.Sprintf("WebFrameInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebFrameListValidationError{
					field:  fmt.Sprintf("WebFrameInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebFrameListMultiError(errors)
	}

	return nil
}

// WebFrameListMultiError is an error wrapping multiple validation errors
// returned by WebFrameList.ValidateAll() if the designated constraints aren't met.
type WebFrameListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebFrameListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebFrameListMultiError) AllErrors() []error { return m }

// WebFrameListValidationError is the validation error returned by
// WebFrameList.Validate if the designated constraints aren't met.
type WebFrameListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebFrameListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebFrameListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebFrameListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebFrameListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebFrameListValidationError) ErrorName() string { return "WebFrameListValidationError" }

// Error satisfies the builtin error interface
func (e WebFrameListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebFrameList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebFrameListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebFrameListValidationError{}

// Validate checks the field values on RelateProcInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RelateProcInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelateProcInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelateProcInfoMultiError,
// or nil if none found.
func (m *RelateProcInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *RelateProcInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Version

	// no validation rules for ProcessId

	// no validation rules for StartTime

	// no validation rules for Path

	if len(errors) > 0 {
		return RelateProcInfoMultiError(errors)
	}

	return nil
}

// RelateProcInfoMultiError is an error wrapping multiple validation errors
// returned by RelateProcInfo.ValidateAll() if the designated constraints
// aren't met.
type RelateProcInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelateProcInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelateProcInfoMultiError) AllErrors() []error { return m }

// RelateProcInfoValidationError is the validation error returned by
// RelateProcInfo.Validate if the designated constraints aren't met.
type RelateProcInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelateProcInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelateProcInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelateProcInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelateProcInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelateProcInfoValidationError) ErrorName() string { return "RelateProcInfoValidationError" }

// Error satisfies the builtin error interface
func (e RelateProcInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelateProcInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelateProcInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelateProcInfoValidationError{}

// Validate checks the field values on HostApplicationInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HostApplicationInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostApplicationInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostApplicationInfoMultiError, or nil if none found.
func (m *HostApplicationInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *HostApplicationInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Version

	// no validation rules for User

	// no validation rules for Path

	// no validation rules for ConfigPath

	// no validation rules for ProcNum

	for idx, item := range m.GetAppProcList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostApplicationInfoValidationError{
						field:  fmt.Sprintf("AppProcList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostApplicationInfoValidationError{
						field:  fmt.Sprintf("AppProcList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostApplicationInfoValidationError{
					field:  fmt.Sprintf("AppProcList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetJarInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostApplicationInfoValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostApplicationInfoValidationError{
						field:  fmt.Sprintf("JarInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostApplicationInfoValidationError{
					field:  fmt.Sprintf("JarInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HostApplicationInfoMultiError(errors)
	}

	return nil
}

// HostApplicationInfoMultiError is an error wrapping multiple validation
// errors returned by HostApplicationInfo.ValidateAll() if the designated
// constraints aren't met.
type HostApplicationInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostApplicationInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostApplicationInfoMultiError) AllErrors() []error { return m }

// HostApplicationInfoValidationError is the validation error returned by
// HostApplicationInfo.Validate if the designated constraints aren't met.
type HostApplicationInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostApplicationInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostApplicationInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostApplicationInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostApplicationInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostApplicationInfoValidationError) ErrorName() string {
	return "HostApplicationInfoValidationError"
}

// Error satisfies the builtin error interface
func (e HostApplicationInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostApplicationInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostApplicationInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostApplicationInfoValidationError{}

// Validate checks the field values on HostApplicationList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HostApplicationList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostApplicationList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HostApplicationListMultiError, or nil if none found.
func (m *HostApplicationList) ValidateAll() error {
	return m.validate(true)
}

func (m *HostApplicationList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHostAppList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HostApplicationListValidationError{
						field:  fmt.Sprintf("HostAppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HostApplicationListValidationError{
						field:  fmt.Sprintf("HostAppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HostApplicationListValidationError{
					field:  fmt.Sprintf("HostAppList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HostApplicationListMultiError(errors)
	}

	return nil
}

// HostApplicationListMultiError is an error wrapping multiple validation
// errors returned by HostApplicationList.ValidateAll() if the designated
// constraints aren't met.
type HostApplicationListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostApplicationListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostApplicationListMultiError) AllErrors() []error { return m }

// HostApplicationListValidationError is the validation error returned by
// HostApplicationList.Validate if the designated constraints aren't met.
type HostApplicationListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostApplicationListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostApplicationListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostApplicationListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostApplicationListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostApplicationListValidationError) ErrorName() string {
	return "HostApplicationListValidationError"
}

// Error satisfies the builtin error interface
func (e HostApplicationListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostApplicationList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostApplicationListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostApplicationListValidationError{}

// Validate checks the field values on PackageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PackageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PackageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PackageInfoMultiError, or
// nil if none found.
func (m *PackageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PackageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Summary

	// no validation rules for Version

	// no validation rules for Release

	// no validation rules for PackageType

	// no validation rules for Vendor

	// no validation rules for Architecture

	// no validation rules for SizeInKb

	// no validation rules for CreationTime

	if len(errors) > 0 {
		return PackageInfoMultiError(errors)
	}

	return nil
}

// PackageInfoMultiError is an error wrapping multiple validation errors
// returned by PackageInfo.ValidateAll() if the designated constraints aren't met.
type PackageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PackageInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PackageInfoMultiError) AllErrors() []error { return m }

// PackageInfoValidationError is the validation error returned by
// PackageInfo.Validate if the designated constraints aren't met.
type PackageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PackageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PackageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PackageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PackageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PackageInfoValidationError) ErrorName() string { return "PackageInfoValidationError" }

// Error satisfies the builtin error interface
func (e PackageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPackageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PackageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PackageInfoValidationError{}

// Validate checks the field values on PackageInfoList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PackageInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PackageInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PackageInfoListMultiError, or nil if none found.
func (m *PackageInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *PackageInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPackageInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PackageInfoListValidationError{
						field:  fmt.Sprintf("PackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PackageInfoListValidationError{
						field:  fmt.Sprintf("PackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PackageInfoListValidationError{
					field:  fmt.Sprintf("PackageInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PackageInfoListMultiError(errors)
	}

	return nil
}

// PackageInfoListMultiError is an error wrapping multiple validation errors
// returned by PackageInfoList.ValidateAll() if the designated constraints
// aren't met.
type PackageInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PackageInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PackageInfoListMultiError) AllErrors() []error { return m }

// PackageInfoListValidationError is the validation error returned by
// PackageInfoList.Validate if the designated constraints aren't met.
type PackageInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PackageInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PackageInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PackageInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PackageInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PackageInfoListValidationError) ErrorName() string { return "PackageInfoListValidationError" }

// Error satisfies the builtin error interface
func (e PackageInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPackageInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PackageInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PackageInfoListValidationError{}

// Validate checks the field values on PythonPackageInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PythonPackageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PythonPackageInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PythonPackageInfoMultiError, or nil if none found.
func (m *PythonPackageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PythonPackageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Version

	// no validation rules for Dir

	// no validation rules for PythonVersion

	if len(errors) > 0 {
		return PythonPackageInfoMultiError(errors)
	}

	return nil
}

// PythonPackageInfoMultiError is an error wrapping multiple validation errors
// returned by PythonPackageInfo.ValidateAll() if the designated constraints
// aren't met.
type PythonPackageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PythonPackageInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PythonPackageInfoMultiError) AllErrors() []error { return m }

// PythonPackageInfoValidationError is the validation error returned by
// PythonPackageInfo.Validate if the designated constraints aren't met.
type PythonPackageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PythonPackageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PythonPackageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PythonPackageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PythonPackageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PythonPackageInfoValidationError) ErrorName() string {
	return "PythonPackageInfoValidationError"
}

// Error satisfies the builtin error interface
func (e PythonPackageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPythonPackageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PythonPackageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PythonPackageInfoValidationError{}

// Validate checks the field values on PythonPackageInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PythonPackageInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PythonPackageInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PythonPackageInfoListMultiError, or nil if none found.
func (m *PythonPackageInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *PythonPackageInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPythonPackageInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PythonPackageInfoListValidationError{
						field:  fmt.Sprintf("PythonPackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PythonPackageInfoListValidationError{
						field:  fmt.Sprintf("PythonPackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PythonPackageInfoListValidationError{
					field:  fmt.Sprintf("PythonPackageInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PythonPackageInfoListMultiError(errors)
	}

	return nil
}

// PythonPackageInfoListMultiError is an error wrapping multiple validation
// errors returned by PythonPackageInfoList.ValidateAll() if the designated
// constraints aren't met.
type PythonPackageInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PythonPackageInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PythonPackageInfoListMultiError) AllErrors() []error { return m }

// PythonPackageInfoListValidationError is the validation error returned by
// PythonPackageInfoList.Validate if the designated constraints aren't met.
type PythonPackageInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PythonPackageInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PythonPackageInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PythonPackageInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PythonPackageInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PythonPackageInfoListValidationError) ErrorName() string {
	return "PythonPackageInfoListValidationError"
}

// Error satisfies the builtin error interface
func (e PythonPackageInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPythonPackageInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PythonPackageInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PythonPackageInfoListValidationError{}

// Validate checks the field values on WebApp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebApp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebApp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in WebAppMultiError, or nil if none found.
func (m *WebApp) ValidateAll() error {
	return m.validate(true)
}

func (m *WebApp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	// no validation rules for AppVersion

	// no validation rules for WebServer

	// no validation rules for WebPath

	// no validation rules for FrameLanguage

	if len(errors) > 0 {
		return WebAppMultiError(errors)
	}

	return nil
}

// WebAppMultiError is an error wrapping multiple validation errors returned by
// WebApp.ValidateAll() if the designated constraints aren't met.
type WebAppMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebAppMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebAppMultiError) AllErrors() []error { return m }

// WebAppValidationError is the validation error returned by WebApp.Validate if
// the designated constraints aren't met.
type WebAppValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebAppValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebAppValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebAppValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebAppValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebAppValidationError) ErrorName() string { return "WebAppValidationError" }

// Error satisfies the builtin error interface
func (e WebAppValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebApp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebAppValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebAppValidationError{}

// Validate checks the field values on WebAppList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WebAppList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebAppList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WebAppListMultiError, or
// nil if none found.
func (m *WebAppList) ValidateAll() error {
	return m.validate(true)
}

func (m *WebAppList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWebAppInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WebAppListValidationError{
						field:  fmt.Sprintf("WebAppInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WebAppListValidationError{
						field:  fmt.Sprintf("WebAppInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WebAppListValidationError{
					field:  fmt.Sprintf("WebAppInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WebAppListMultiError(errors)
	}

	return nil
}

// WebAppListMultiError is an error wrapping multiple validation errors
// returned by WebAppList.ValidateAll() if the designated constraints aren't met.
type WebAppListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebAppListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebAppListMultiError) AllErrors() []error { return m }

// WebAppListValidationError is the validation error returned by
// WebAppList.Validate if the designated constraints aren't met.
type WebAppListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebAppListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebAppListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebAppListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebAppListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebAppListValidationError) ErrorName() string { return "WebAppListValidationError" }

// Error satisfies the builtin error interface
func (e WebAppListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebAppList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebAppListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebAppListValidationError{}

// Validate checks the field values on EtherNetInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EtherNetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EtherNetInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EtherNetInfoMultiError, or
// nil if none found.
func (m *EtherNetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EtherNetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EthName

	// no validation rules for EthMac

	// no validation rules for EthLinkState

	// no validation rules for EthSpeed

	// no validation rules for UpFlowBytes

	// no validation rules for DownFlowBytes

	// no validation rules for FlowTime

	if len(errors) > 0 {
		return EtherNetInfoMultiError(errors)
	}

	return nil
}

// EtherNetInfoMultiError is an error wrapping multiple validation errors
// returned by EtherNetInfo.ValidateAll() if the designated constraints aren't met.
type EtherNetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EtherNetInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EtherNetInfoMultiError) AllErrors() []error { return m }

// EtherNetInfoValidationError is the validation error returned by
// EtherNetInfo.Validate if the designated constraints aren't met.
type EtherNetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EtherNetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EtherNetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EtherNetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EtherNetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EtherNetInfoValidationError) ErrorName() string { return "EtherNetInfoValidationError" }

// Error satisfies the builtin error interface
func (e EtherNetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEtherNetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EtherNetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EtherNetInfoValidationError{}

// Validate checks the field values on NpmPackageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NpmPackageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NpmPackageInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NpmPackageInfoMultiError,
// or nil if none found.
func (m *NpmPackageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NpmPackageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Version

	// no validation rules for Path

	// no validation rules for Scope

	// no validation rules for Pid

	// no validation rules for Command

	if len(errors) > 0 {
		return NpmPackageInfoMultiError(errors)
	}

	return nil
}

// NpmPackageInfoMultiError is an error wrapping multiple validation errors
// returned by NpmPackageInfo.ValidateAll() if the designated constraints
// aren't met.
type NpmPackageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NpmPackageInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NpmPackageInfoMultiError) AllErrors() []error { return m }

// NpmPackageInfoValidationError is the validation error returned by
// NpmPackageInfo.Validate if the designated constraints aren't met.
type NpmPackageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NpmPackageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NpmPackageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NpmPackageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NpmPackageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NpmPackageInfoValidationError) ErrorName() string { return "NpmPackageInfoValidationError" }

// Error satisfies the builtin error interface
func (e NpmPackageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNpmPackageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NpmPackageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NpmPackageInfoValidationError{}

// Validate checks the field values on NpmPackageInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NpmPackageInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NpmPackageInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NpmPackageInfoListMultiError, or nil if none found.
func (m *NpmPackageInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *NpmPackageInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNpmPackageInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NpmPackageInfoListValidationError{
						field:  fmt.Sprintf("NpmPackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NpmPackageInfoListValidationError{
						field:  fmt.Sprintf("NpmPackageInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NpmPackageInfoListValidationError{
					field:  fmt.Sprintf("NpmPackageInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NpmPackageInfoListMultiError(errors)
	}

	return nil
}

// NpmPackageInfoListMultiError is an error wrapping multiple validation errors
// returned by NpmPackageInfoList.ValidateAll() if the designated constraints
// aren't met.
type NpmPackageInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NpmPackageInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NpmPackageInfoListMultiError) AllErrors() []error { return m }

// NpmPackageInfoListValidationError is the validation error returned by
// NpmPackageInfoList.Validate if the designated constraints aren't met.
type NpmPackageInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NpmPackageInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NpmPackageInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NpmPackageInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NpmPackageInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NpmPackageInfoListValidationError) ErrorName() string {
	return "NpmPackageInfoListValidationError"
}

// Error satisfies the builtin error interface
func (e NpmPackageInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNpmPackageInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NpmPackageInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NpmPackageInfoListValidationError{}

// Validate checks the field values on JobTaskInfoList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *JobTaskInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTaskInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// JobTaskInfoListMultiError, or nil if none found.
func (m *JobTaskInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTaskInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetJobTaskInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, JobTaskInfoListValidationError{
						field:  fmt.Sprintf("JobTaskInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, JobTaskInfoListValidationError{
						field:  fmt.Sprintf("JobTaskInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return JobTaskInfoListValidationError{
					field:  fmt.Sprintf("JobTaskInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return JobTaskInfoListMultiError(errors)
	}

	return nil
}

// JobTaskInfoListMultiError is an error wrapping multiple validation errors
// returned by JobTaskInfoList.ValidateAll() if the designated constraints
// aren't met.
type JobTaskInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTaskInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTaskInfoListMultiError) AllErrors() []error { return m }

// JobTaskInfoListValidationError is the validation error returned by
// JobTaskInfoList.Validate if the designated constraints aren't met.
type JobTaskInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTaskInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTaskInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTaskInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTaskInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTaskInfoListValidationError) ErrorName() string { return "JobTaskInfoListValidationError" }

// Error satisfies the builtin error interface
func (e JobTaskInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTaskInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTaskInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTaskInfoListValidationError{}

// Validate checks the field values on JobTaskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *JobTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on JobTaskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in JobTaskInfoMultiError, or
// nil if none found.
func (m *JobTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *JobTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for IsEnable

	// no validation rules for ExecTime

	// no validation rules for ExecUser

	// no validation rules for EtcPath

	if len(errors) > 0 {
		return JobTaskInfoMultiError(errors)
	}

	return nil
}

// JobTaskInfoMultiError is an error wrapping multiple validation errors
// returned by JobTaskInfo.ValidateAll() if the designated constraints aren't met.
type JobTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m JobTaskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m JobTaskInfoMultiError) AllErrors() []error { return m }

// JobTaskInfoValidationError is the validation error returned by
// JobTaskInfo.Validate if the designated constraints aren't met.
type JobTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e JobTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e JobTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e JobTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e JobTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e JobTaskInfoValidationError) ErrorName() string { return "JobTaskInfoValidationError" }

// Error satisfies the builtin error interface
func (e JobTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sJobTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = JobTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = JobTaskInfoValidationError{}

// Validate checks the field values on AppWeakPwdInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AppWeakPwdInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppWeakPwdInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AppWeakPwdInfoMultiError,
// or nil if none found.
func (m *AppWeakPwdInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AppWeakPwdInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AppName

	// no validation rules for UserName

	// no validation rules for WeakPwdType

	// no validation rules for WeakPwdScanTime

	// no validation rules for WeakPwdRegexRule

	if len(errors) > 0 {
		return AppWeakPwdInfoMultiError(errors)
	}

	return nil
}

// AppWeakPwdInfoMultiError is an error wrapping multiple validation errors
// returned by AppWeakPwdInfo.ValidateAll() if the designated constraints
// aren't met.
type AppWeakPwdInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppWeakPwdInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppWeakPwdInfoMultiError) AllErrors() []error { return m }

// AppWeakPwdInfoValidationError is the validation error returned by
// AppWeakPwdInfo.Validate if the designated constraints aren't met.
type AppWeakPwdInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppWeakPwdInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppWeakPwdInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppWeakPwdInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppWeakPwdInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppWeakPwdInfoValidationError) ErrorName() string { return "AppWeakPwdInfoValidationError" }

// Error satisfies the builtin error interface
func (e AppWeakPwdInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppWeakPwdInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppWeakPwdInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppWeakPwdInfoValidationError{}

// Validate checks the field values on AppWeakPwdInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppWeakPwdInfoList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppWeakPwdInfoList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppWeakPwdInfoListMultiError, or nil if none found.
func (m *AppWeakPwdInfoList) ValidateAll() error {
	return m.validate(true)
}

func (m *AppWeakPwdInfoList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAppWeakPwdInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AppWeakPwdInfoListValidationError{
						field:  fmt.Sprintf("AppWeakPwdInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AppWeakPwdInfoListValidationError{
						field:  fmt.Sprintf("AppWeakPwdInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AppWeakPwdInfoListValidationError{
					field:  fmt.Sprintf("AppWeakPwdInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AppWeakPwdInfoListMultiError(errors)
	}

	return nil
}

// AppWeakPwdInfoListMultiError is an error wrapping multiple validation errors
// returned by AppWeakPwdInfoList.ValidateAll() if the designated constraints
// aren't met.
type AppWeakPwdInfoListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppWeakPwdInfoListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppWeakPwdInfoListMultiError) AllErrors() []error { return m }

// AppWeakPwdInfoListValidationError is the validation error returned by
// AppWeakPwdInfoList.Validate if the designated constraints aren't met.
type AppWeakPwdInfoListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppWeakPwdInfoListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppWeakPwdInfoListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppWeakPwdInfoListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppWeakPwdInfoListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppWeakPwdInfoListValidationError) ErrorName() string {
	return "AppWeakPwdInfoListValidationError"
}

// Error satisfies the builtin error interface
func (e AppWeakPwdInfoListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppWeakPwdInfoList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppWeakPwdInfoListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppWeakPwdInfoListValidationError{}
