// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/policy.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on BlackWhiteItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BlackWhiteItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BlackWhiteItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BlackWhiteItemMultiError,
// or nil if none found.
func (m *BlackWhiteItem) ValidateAll() error {
	return m.validate(true)
}

func (m *BlackWhiteItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Module

	// no validation rules for UniqueFlag

	// no validation rules for BwType

	// no validation rules for AddOrDel

	if len(errors) > 0 {
		return BlackWhiteItemMultiError(errors)
	}

	return nil
}

// BlackWhiteItemMultiError is an error wrapping multiple validation errors
// returned by BlackWhiteItem.ValidateAll() if the designated constraints
// aren't met.
type BlackWhiteItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BlackWhiteItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BlackWhiteItemMultiError) AllErrors() []error { return m }

// BlackWhiteItemValidationError is the validation error returned by
// BlackWhiteItem.Validate if the designated constraints aren't met.
type BlackWhiteItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BlackWhiteItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BlackWhiteItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BlackWhiteItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BlackWhiteItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BlackWhiteItemValidationError) ErrorName() string { return "BlackWhiteItemValidationError" }

// Error satisfies the builtin error interface
func (e BlackWhiteItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBlackWhiteItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BlackWhiteItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BlackWhiteItemValidationError{}

// Validate checks the field values on UpdateBlackWhitePolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBlackWhitePolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBlackWhitePolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBlackWhitePolicyMultiError, or nil if none found.
func (m *UpdateBlackWhitePolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBlackWhitePolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OldTimestamp

	// no validation rules for CurTimestamp

	for idx, item := range m.GetBlackwhiteList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateBlackWhitePolicyValidationError{
						field:  fmt.Sprintf("BlackwhiteList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateBlackWhitePolicyValidationError{
						field:  fmt.Sprintf("BlackwhiteList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateBlackWhitePolicyValidationError{
					field:  fmt.Sprintf("BlackwhiteList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateBlackWhitePolicyMultiError(errors)
	}

	return nil
}

// UpdateBlackWhitePolicyMultiError is an error wrapping multiple validation
// errors returned by UpdateBlackWhitePolicy.ValidateAll() if the designated
// constraints aren't met.
type UpdateBlackWhitePolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBlackWhitePolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBlackWhitePolicyMultiError) AllErrors() []error { return m }

// UpdateBlackWhitePolicyValidationError is the validation error returned by
// UpdateBlackWhitePolicy.Validate if the designated constraints aren't met.
type UpdateBlackWhitePolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBlackWhitePolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBlackWhitePolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBlackWhitePolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBlackWhitePolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBlackWhitePolicyValidationError) ErrorName() string {
	return "UpdateBlackWhitePolicyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBlackWhitePolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBlackWhitePolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBlackWhitePolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBlackWhitePolicyValidationError{}

// Validate checks the field values on FileMonitorItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileMonitorItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileMonitorItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileMonitorItemMultiError, or nil if none found.
func (m *FileMonitorItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FileMonitorItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filepath

	// no validation rules for Filetype

	// no validation rules for Operate

	// no validation rules for WhitePath

	// no validation rules for WhiteProc

	// no validation rules for Filenamelist

	// no validation rules for IncludeExt

	// no validation rules for ExcludeExt

	if len(errors) > 0 {
		return FileMonitorItemMultiError(errors)
	}

	return nil
}

// FileMonitorItemMultiError is an error wrapping multiple validation errors
// returned by FileMonitorItem.ValidateAll() if the designated constraints
// aren't met.
type FileMonitorItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileMonitorItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileMonitorItemMultiError) AllErrors() []error { return m }

// FileMonitorItemValidationError is the validation error returned by
// FileMonitorItem.Validate if the designated constraints aren't met.
type FileMonitorItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileMonitorItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileMonitorItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileMonitorItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileMonitorItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileMonitorItemValidationError) ErrorName() string { return "FileMonitorItemValidationError" }

// Error satisfies the builtin error interface
func (e FileMonitorItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileMonitorItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileMonitorItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileMonitorItemValidationError{}

// Validate checks the field values on UpdateFileMonitorPolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateFileMonitorPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileMonitorPolicy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateFileMonitorPolicyMultiError, or nil if none found.
func (m *UpdateFileMonitorPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileMonitorPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	for idx, item := range m.GetFileMonitorList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateFileMonitorPolicyValidationError{
						field:  fmt.Sprintf("FileMonitorList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateFileMonitorPolicyValidationError{
						field:  fmt.Sprintf("FileMonitorList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateFileMonitorPolicyValidationError{
					field:  fmt.Sprintf("FileMonitorList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateFileMonitorPolicyMultiError(errors)
	}

	return nil
}

// UpdateFileMonitorPolicyMultiError is an error wrapping multiple validation
// errors returned by UpdateFileMonitorPolicy.ValidateAll() if the designated
// constraints aren't met.
type UpdateFileMonitorPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileMonitorPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileMonitorPolicyMultiError) AllErrors() []error { return m }

// UpdateFileMonitorPolicyValidationError is the validation error returned by
// UpdateFileMonitorPolicy.Validate if the designated constraints aren't met.
type UpdateFileMonitorPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileMonitorPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileMonitorPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileMonitorPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileMonitorPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileMonitorPolicyValidationError) ErrorName() string {
	return "UpdateFileMonitorPolicyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateFileMonitorPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileMonitorPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileMonitorPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileMonitorPolicyValidationError{}

// Validate checks the field values on RequestPolicyList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RequestPolicyList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestPolicyList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RequestPolicyListMultiError, or nil if none found.
func (m *RequestPolicyList) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestPolicyList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocalTimestamp

	// no validation rules for ReqType

	if len(errors) > 0 {
		return RequestPolicyListMultiError(errors)
	}

	return nil
}

// RequestPolicyListMultiError is an error wrapping multiple validation errors
// returned by RequestPolicyList.ValidateAll() if the designated constraints
// aren't met.
type RequestPolicyListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestPolicyListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestPolicyListMultiError) AllErrors() []error { return m }

// RequestPolicyListValidationError is the validation error returned by
// RequestPolicyList.Validate if the designated constraints aren't met.
type RequestPolicyListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestPolicyListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestPolicyListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestPolicyListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestPolicyListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestPolicyListValidationError) ErrorName() string {
	return "RequestPolicyListValidationError"
}

// Error satisfies the builtin error interface
func (e RequestPolicyListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestPolicyList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestPolicyListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestPolicyListValidationError{}

// Validate checks the field values on RiskInterceptSwitchMsg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskInterceptSwitchMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInterceptSwitchMsg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInterceptSwitchMsgMultiError, or nil if none found.
func (m *RiskInterceptSwitchMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInterceptSwitchMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRiskBehaviorSwitch()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskInterceptSwitchMsgValidationError{
					field:  "RiskBehaviorSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskInterceptSwitchMsgValidationError{
					field:  "RiskBehaviorSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskBehaviorSwitch()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskInterceptSwitchMsgValidationError{
				field:  "RiskBehaviorSwitch",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskInterceptSwitchMsgMultiError(errors)
	}

	return nil
}

// RiskInterceptSwitchMsgMultiError is an error wrapping multiple validation
// errors returned by RiskInterceptSwitchMsg.ValidateAll() if the designated
// constraints aren't met.
type RiskInterceptSwitchMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInterceptSwitchMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInterceptSwitchMsgMultiError) AllErrors() []error { return m }

// RiskInterceptSwitchMsgValidationError is the validation error returned by
// RiskInterceptSwitchMsg.Validate if the designated constraints aren't met.
type RiskInterceptSwitchMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInterceptSwitchMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInterceptSwitchMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInterceptSwitchMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInterceptSwitchMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInterceptSwitchMsgValidationError) ErrorName() string {
	return "RiskInterceptSwitchMsgValidationError"
}

// Error satisfies the builtin error interface
func (e RiskInterceptSwitchMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInterceptSwitchMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInterceptSwitchMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInterceptSwitchMsgValidationError{}

// Validate checks the field values on RiskBehaviorSwitch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskBehaviorSwitch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskBehaviorSwitch with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskBehaviorSwitchMultiError, or nil if none found.
func (m *RiskBehaviorSwitch) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskBehaviorSwitch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskSwitch

	if len(errors) > 0 {
		return RiskBehaviorSwitchMultiError(errors)
	}

	return nil
}

// RiskBehaviorSwitchMultiError is an error wrapping multiple validation errors
// returned by RiskBehaviorSwitch.ValidateAll() if the designated constraints
// aren't met.
type RiskBehaviorSwitchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskBehaviorSwitchMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskBehaviorSwitchMultiError) AllErrors() []error { return m }

// RiskBehaviorSwitchValidationError is the validation error returned by
// RiskBehaviorSwitch.Validate if the designated constraints aren't met.
type RiskBehaviorSwitchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskBehaviorSwitchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskBehaviorSwitchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskBehaviorSwitchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskBehaviorSwitchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskBehaviorSwitchValidationError) ErrorName() string {
	return "RiskBehaviorSwitchValidationError"
}

// Error satisfies the builtin error interface
func (e RiskBehaviorSwitchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskBehaviorSwitch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskBehaviorSwitchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskBehaviorSwitchValidationError{}

// Validate checks the field values on GlobalSwitch with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GlobalSwitch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GlobalSwitch with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GlobalSwitchMultiError, or
// nil if none found.
func (m *GlobalSwitch) ValidateAll() error {
	return m.validate(true)
}

func (m *GlobalSwitch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PTHEventLog

	// no validation rules for DumpLsass

	// no validation rules for NetLogon

	// no validation rules for AccRisk

	// no validation rules for PuppetProc

	// no validation rules for WebShellMemory

	// no validation rules for KernelInject

	// no validation rules for SelfProtect

	// no validation rules for CheckRootkit

	for idx, item := range m.GetKernelInjectList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GlobalSwitchValidationError{
						field:  fmt.Sprintf("KernelInjectList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GlobalSwitchValidationError{
						field:  fmt.Sprintf("KernelInjectList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GlobalSwitchValidationError{
					field:  fmt.Sprintf("KernelInjectList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for InjectTomcat

	// no validation rules for InjectWeblogic

	// no validation rules for InjectWebsphere

	// no validation rules for InjectJetty

	// no validation rules for InjectJbossWildfly

	// no validation rules for InjectResin

	// no validation rules for InjectSpring

	// no validation rules for InjectBES

	// no validation rules for InjectPHP

	// no validation rules for Performance

	// no validation rules for PHPApp

	// no validation rules for BankCompatible

	// no validation rules for V01FileMonitor

	// no validation rules for V01IllegalOutreach

	// no validation rules for V01MemoryAttack

	// no validation rules for V01ShellAttack

	// no validation rules for VO1CheckLdr

	// no validation rules for V01ACDRSwitch

	if len(errors) > 0 {
		return GlobalSwitchMultiError(errors)
	}

	return nil
}

// GlobalSwitchMultiError is an error wrapping multiple validation errors
// returned by GlobalSwitch.ValidateAll() if the designated constraints aren't met.
type GlobalSwitchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GlobalSwitchMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GlobalSwitchMultiError) AllErrors() []error { return m }

// GlobalSwitchValidationError is the validation error returned by
// GlobalSwitch.Validate if the designated constraints aren't met.
type GlobalSwitchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GlobalSwitchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GlobalSwitchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GlobalSwitchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GlobalSwitchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GlobalSwitchValidationError) ErrorName() string { return "GlobalSwitchValidationError" }

// Error satisfies the builtin error interface
func (e GlobalSwitchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGlobalSwitch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GlobalSwitchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GlobalSwitchValidationError{}

// Validate checks the field values on PTHPolicy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PTHPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PTHPolicy with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PTHPolicyMultiError, or nil
// if none found.
func (m *PTHPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *PTHPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Switch

	// no validation rules for PthMode

	if len(errors) > 0 {
		return PTHPolicyMultiError(errors)
	}

	return nil
}

// PTHPolicyMultiError is an error wrapping multiple validation errors returned
// by PTHPolicy.ValidateAll() if the designated constraints aren't met.
type PTHPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PTHPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PTHPolicyMultiError) AllErrors() []error { return m }

// PTHPolicyValidationError is the validation error returned by
// PTHPolicy.Validate if the designated constraints aren't met.
type PTHPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PTHPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PTHPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PTHPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PTHPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PTHPolicyValidationError) ErrorName() string { return "PTHPolicyValidationError" }

// Error satisfies the builtin error interface
func (e PTHPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPTHPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PTHPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PTHPolicyValidationError{}

// Validate checks the field values on CustomFileBlackWhitePolicy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomFileBlackWhitePolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomFileBlackWhitePolicy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomFileBlackWhitePolicyMultiError, or nil if none found.
func (m *CustomFileBlackWhitePolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomFileBlackWhitePolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBwList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomFileBlackWhitePolicyValidationError{
						field:  fmt.Sprintf("BwList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomFileBlackWhitePolicyValidationError{
						field:  fmt.Sprintf("BwList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomFileBlackWhitePolicyValidationError{
					field:  fmt.Sprintf("BwList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CustomFileBlackWhitePolicyMultiError(errors)
	}

	return nil
}

// CustomFileBlackWhitePolicyMultiError is an error wrapping multiple
// validation errors returned by CustomFileBlackWhitePolicy.ValidateAll() if
// the designated constraints aren't met.
type CustomFileBlackWhitePolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomFileBlackWhitePolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomFileBlackWhitePolicyMultiError) AllErrors() []error { return m }

// CustomFileBlackWhitePolicyValidationError is the validation error returned
// by CustomFileBlackWhitePolicy.Validate if the designated constraints aren't met.
type CustomFileBlackWhitePolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomFileBlackWhitePolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomFileBlackWhitePolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomFileBlackWhitePolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomFileBlackWhitePolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomFileBlackWhitePolicyValidationError) ErrorName() string {
	return "CustomFileBlackWhitePolicyValidationError"
}

// Error satisfies the builtin error interface
func (e CustomFileBlackWhitePolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomFileBlackWhitePolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomFileBlackWhitePolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomFileBlackWhitePolicyValidationError{}

// Validate checks the field values on CustomFileBlackWhiteItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomFileBlackWhiteItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomFileBlackWhiteItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomFileBlackWhiteItemMultiError, or nil if none found.
func (m *CustomFileBlackWhiteItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomFileBlackWhiteItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha1

	// no validation rules for BwType

	if len(errors) > 0 {
		return CustomFileBlackWhiteItemMultiError(errors)
	}

	return nil
}

// CustomFileBlackWhiteItemMultiError is an error wrapping multiple validation
// errors returned by CustomFileBlackWhiteItem.ValidateAll() if the designated
// constraints aren't met.
type CustomFileBlackWhiteItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomFileBlackWhiteItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomFileBlackWhiteItemMultiError) AllErrors() []error { return m }

// CustomFileBlackWhiteItemValidationError is the validation error returned by
// CustomFileBlackWhiteItem.Validate if the designated constraints aren't met.
type CustomFileBlackWhiteItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomFileBlackWhiteItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomFileBlackWhiteItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomFileBlackWhiteItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomFileBlackWhiteItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomFileBlackWhiteItemValidationError) ErrorName() string {
	return "CustomFileBlackWhiteItemValidationError"
}

// Error satisfies the builtin error interface
func (e CustomFileBlackWhiteItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomFileBlackWhiteItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomFileBlackWhiteItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomFileBlackWhiteItemValidationError{}

// Validate checks the field values on DigitalSignaturePolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DigitalSignaturePolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigitalSignaturePolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DigitalSignaturePolicyMultiError, or nil if none found.
func (m *DigitalSignaturePolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *DigitalSignaturePolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DigitalSignaturePolicyValidationError{
						field:  fmt.Sprintf("DsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DigitalSignaturePolicyValidationError{
						field:  fmt.Sprintf("DsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DigitalSignaturePolicyValidationError{
					field:  fmt.Sprintf("DsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return DigitalSignaturePolicyMultiError(errors)
	}

	return nil
}

// DigitalSignaturePolicyMultiError is an error wrapping multiple validation
// errors returned by DigitalSignaturePolicy.ValidateAll() if the designated
// constraints aren't met.
type DigitalSignaturePolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigitalSignaturePolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigitalSignaturePolicyMultiError) AllErrors() []error { return m }

// DigitalSignaturePolicyValidationError is the validation error returned by
// DigitalSignaturePolicy.Validate if the designated constraints aren't met.
type DigitalSignaturePolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigitalSignaturePolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigitalSignaturePolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigitalSignaturePolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigitalSignaturePolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigitalSignaturePolicyValidationError) ErrorName() string {
	return "DigitalSignaturePolicyValidationError"
}

// Error satisfies the builtin error interface
func (e DigitalSignaturePolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigitalSignaturePolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigitalSignaturePolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigitalSignaturePolicyValidationError{}

// Validate checks the field values on DigitalSignatureItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DigitalSignatureItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigitalSignatureItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DigitalSignatureItemMultiError, or nil if none found.
func (m *DigitalSignatureItem) ValidateAll() error {
	return m.validate(true)
}

func (m *DigitalSignatureItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Signature

	// no validation rules for Certificate

	// no validation rules for BwType

	if len(errors) > 0 {
		return DigitalSignatureItemMultiError(errors)
	}

	return nil
}

// DigitalSignatureItemMultiError is an error wrapping multiple validation
// errors returned by DigitalSignatureItem.ValidateAll() if the designated
// constraints aren't met.
type DigitalSignatureItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigitalSignatureItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigitalSignatureItemMultiError) AllErrors() []error { return m }

// DigitalSignatureItemValidationError is the validation error returned by
// DigitalSignatureItem.Validate if the designated constraints aren't met.
type DigitalSignatureItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigitalSignatureItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigitalSignatureItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigitalSignatureItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigitalSignatureItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigitalSignatureItemValidationError) ErrorName() string {
	return "DigitalSignatureItemValidationError"
}

// Error satisfies the builtin error interface
func (e DigitalSignatureItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigitalSignatureItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigitalSignatureItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigitalSignatureItemValidationError{}

// Validate checks the field values on KernelInjectItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KernelInjectItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KernelInjectItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KernelInjectItemMultiError, or nil if none found.
func (m *KernelInjectItem) ValidateAll() error {
	return m.validate(true)
}

func (m *KernelInjectItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for CmdLineArgs

	if len(errors) > 0 {
		return KernelInjectItemMultiError(errors)
	}

	return nil
}

// KernelInjectItemMultiError is an error wrapping multiple validation errors
// returned by KernelInjectItem.ValidateAll() if the designated constraints
// aren't met.
type KernelInjectItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KernelInjectItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KernelInjectItemMultiError) AllErrors() []error { return m }

// KernelInjectItemValidationError is the validation error returned by
// KernelInjectItem.Validate if the designated constraints aren't met.
type KernelInjectItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KernelInjectItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KernelInjectItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KernelInjectItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KernelInjectItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KernelInjectItemValidationError) ErrorName() string { return "KernelInjectItemValidationError" }

// Error satisfies the builtin error interface
func (e KernelInjectItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKernelInjectItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KernelInjectItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KernelInjectItemValidationError{}

// Validate checks the field values on KernelInjectList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KernelInjectList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KernelInjectList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KernelInjectListMultiError, or nil if none found.
func (m *KernelInjectList) ValidateAll() error {
	return m.validate(true)
}

func (m *KernelInjectList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetKernelInjectItem() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KernelInjectListValidationError{
						field:  fmt.Sprintf("KernelInjectItem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KernelInjectListValidationError{
						field:  fmt.Sprintf("KernelInjectItem[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KernelInjectListValidationError{
					field:  fmt.Sprintf("KernelInjectItem[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return KernelInjectListMultiError(errors)
	}

	return nil
}

// KernelInjectListMultiError is an error wrapping multiple validation errors
// returned by KernelInjectList.ValidateAll() if the designated constraints
// aren't met.
type KernelInjectListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KernelInjectListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KernelInjectListMultiError) AllErrors() []error { return m }

// KernelInjectListValidationError is the validation error returned by
// KernelInjectList.Validate if the designated constraints aren't met.
type KernelInjectListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KernelInjectListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KernelInjectListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KernelInjectListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KernelInjectListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KernelInjectListValidationError) ErrorName() string { return "KernelInjectListValidationError" }

// Error satisfies the builtin error interface
func (e KernelInjectListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKernelInjectList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KernelInjectListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KernelInjectListValidationError{}

// Validate checks the field values on LonginTimes with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LonginTimes) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LonginTimes with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LonginTimesMultiError, or
// nil if none found.
func (m *LonginTimes) ValidateAll() error {
	return m.validate(true)
}

func (m *LonginTimes) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LonginFrequency

	// no validation rules for StartTime

	// no validation rules for EndTime

	if len(errors) > 0 {
		return LonginTimesMultiError(errors)
	}

	return nil
}

// LonginTimesMultiError is an error wrapping multiple validation errors
// returned by LonginTimes.ValidateAll() if the designated constraints aren't met.
type LonginTimesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LonginTimesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LonginTimesMultiError) AllErrors() []error { return m }

// LonginTimesValidationError is the validation error returned by
// LonginTimes.Validate if the designated constraints aren't met.
type LonginTimesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LonginTimesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LonginTimesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LonginTimesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LonginTimesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LonginTimesValidationError) ErrorName() string { return "LonginTimesValidationError" }

// Error satisfies the builtin error interface
func (e LonginTimesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLonginTimes.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LonginTimesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LonginTimesValidationError{}

// Validate checks the field values on AbnormalLoginConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AbnormalLoginConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AbnormalLoginConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AbnormalLoginConfigMultiError, or nil if none found.
func (m *AbnormalLoginConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *AbnormalLoginConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetLoginTimes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AbnormalLoginConfigValidationError{
						field:  fmt.Sprintf("LoginTimes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AbnormalLoginConfigValidationError{
						field:  fmt.Sprintf("LoginTimes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AbnormalLoginConfigValidationError{
					field:  fmt.Sprintf("LoginTimes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for StudyMode

	if len(errors) > 0 {
		return AbnormalLoginConfigMultiError(errors)
	}

	return nil
}

// AbnormalLoginConfigMultiError is an error wrapping multiple validation
// errors returned by AbnormalLoginConfig.ValidateAll() if the designated
// constraints aren't met.
type AbnormalLoginConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AbnormalLoginConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AbnormalLoginConfigMultiError) AllErrors() []error { return m }

// AbnormalLoginConfigValidationError is the validation error returned by
// AbnormalLoginConfig.Validate if the designated constraints aren't met.
type AbnormalLoginConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AbnormalLoginConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AbnormalLoginConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AbnormalLoginConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AbnormalLoginConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AbnormalLoginConfigValidationError) ErrorName() string {
	return "AbnormalLoginConfigValidationError"
}

// Error satisfies the builtin error interface
func (e AbnormalLoginConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAbnormalLoginConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AbnormalLoginConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AbnormalLoginConfigValidationError{}

// Validate checks the field values on UpdateFileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpdateFileInfoMultiError,
// or nil if none found.
func (m *UpdateFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for Md5

	if len(errors) > 0 {
		return UpdateFileInfoMultiError(errors)
	}

	return nil
}

// UpdateFileInfoMultiError is an error wrapping multiple validation errors
// returned by UpdateFileInfo.ValidateAll() if the designated constraints
// aren't met.
type UpdateFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateFileInfoMultiError) AllErrors() []error { return m }

// UpdateFileInfoValidationError is the validation error returned by
// UpdateFileInfo.Validate if the designated constraints aren't met.
type UpdateFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateFileInfoValidationError) ErrorName() string { return "UpdateFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e UpdateFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateFileInfoValidationError{}

// Validate checks the field values on BWRuleDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BWRuleDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BWRuleDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BWRuleDetailMultiError, or
// nil if none found.
func (m *BWRuleDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *BWRuleDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Attribute

	// no validation rules for Condition

	// no validation rules for Scope

	if len(errors) > 0 {
		return BWRuleDetailMultiError(errors)
	}

	return nil
}

// BWRuleDetailMultiError is an error wrapping multiple validation errors
// returned by BWRuleDetail.ValidateAll() if the designated constraints aren't met.
type BWRuleDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BWRuleDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BWRuleDetailMultiError) AllErrors() []error { return m }

// BWRuleDetailValidationError is the validation error returned by
// BWRuleDetail.Validate if the designated constraints aren't met.
type BWRuleDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BWRuleDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BWRuleDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BWRuleDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BWRuleDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BWRuleDetailValidationError) ErrorName() string { return "BWRuleDetailValidationError" }

// Error satisfies the builtin error interface
func (e BWRuleDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBWRuleDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BWRuleDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BWRuleDetailValidationError{}

// Validate checks the field values on BWRulePkg with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BWRulePkg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BWRulePkg with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BWRulePkgMultiError, or nil
// if none found.
func (m *BWRulePkg) ValidateAll() error {
	return m.validate(true)
}

func (m *BWRulePkg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Checkpoint

	// no validation rules for Id

	for idx, item := range m.GetRuleDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BWRulePkgValidationError{
						field:  fmt.Sprintf("RuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BWRulePkgValidationError{
						field:  fmt.Sprintf("RuleDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BWRulePkgValidationError{
					field:  fmt.Sprintf("RuleDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MinVer

	// no validation rules for MaxVer

	if len(errors) > 0 {
		return BWRulePkgMultiError(errors)
	}

	return nil
}

// BWRulePkgMultiError is an error wrapping multiple validation errors returned
// by BWRulePkg.ValidateAll() if the designated constraints aren't met.
type BWRulePkgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BWRulePkgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BWRulePkgMultiError) AllErrors() []error { return m }

// BWRulePkgValidationError is the validation error returned by
// BWRulePkg.Validate if the designated constraints aren't met.
type BWRulePkgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BWRulePkgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BWRulePkgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BWRulePkgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BWRulePkgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BWRulePkgValidationError) ErrorName() string { return "BWRulePkgValidationError" }

// Error satisfies the builtin error interface
func (e BWRulePkgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBWRulePkg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BWRulePkgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BWRulePkgValidationError{}

// Validate checks the field values on BWRuleList with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BWRuleList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BWRuleList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BWRuleListMultiError, or
// nil if none found.
func (m *BWRuleList) ValidateAll() error {
	return m.validate(true)
}

func (m *BWRuleList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRuleList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BWRuleListValidationError{
						field:  fmt.Sprintf("RuleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BWRuleListValidationError{
						field:  fmt.Sprintf("RuleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BWRuleListValidationError{
					field:  fmt.Sprintf("RuleList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BWRuleListMultiError(errors)
	}

	return nil
}

// BWRuleListMultiError is an error wrapping multiple validation errors
// returned by BWRuleList.ValidateAll() if the designated constraints aren't met.
type BWRuleListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BWRuleListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BWRuleListMultiError) AllErrors() []error { return m }

// BWRuleListValidationError is the validation error returned by
// BWRuleList.Validate if the designated constraints aren't met.
type BWRuleListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BWRuleListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BWRuleListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BWRuleListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BWRuleListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BWRuleListValidationError) ErrorName() string { return "BWRuleListValidationError" }

// Error satisfies the builtin error interface
func (e BWRuleListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBWRuleList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BWRuleListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BWRuleListValidationError{}

// Validate checks the field values on Settings with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Settings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Settings with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SettingsMultiError, or nil
// if none found.
func (m *Settings) ValidateAll() error {
	return m.validate(true)
}

func (m *Settings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.HasUsualLogonThreshold.(type) {
	case *Settings_UsualLogonThreshold:
		if v == nil {
			err := SettingsValidationError{
				field:  "HasUsualLogonThreshold",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for UsualLogonThreshold
	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWeakPasswordListTimestamp.(type) {
	case *Settings_WeakPasswordListTimestamp:
		if v == nil {
			err := SettingsValidationError{
				field:  "HasWeakPasswordListTimestamp",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for WeakPasswordListTimestamp
	default:
		_ = v // ensures v is used
	}
	switch v := m.HasRiskInterceptSwitchMsg.(type) {
	case *Settings_RiskInterceptSwitch:
		if v == nil {
			err := SettingsValidationError{
				field:  "HasRiskInterceptSwitchMsg",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRiskInterceptSwitch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "RiskInterceptSwitch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "RiskInterceptSwitch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRiskInterceptSwitch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SettingsValidationError{
					field:  "RiskInterceptSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasGlobalSwitch.(type) {
	case *Settings_GlobalSwitch:
		if v == nil {
			err := SettingsValidationError{
				field:  "HasGlobalSwitch",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGlobalSwitch()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "GlobalSwitch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "GlobalSwitch",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGlobalSwitch()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SettingsValidationError{
					field:  "GlobalSwitch",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.Has_AbnormalLoginConfig.(type) {
	case *Settings_AbnormalLoginConfig:
		if v == nil {
			err := SettingsValidationError{
				field:  "Has_AbnormalLoginConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAbnormalLoginConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "AbnormalLoginConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "AbnormalLoginConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAbnormalLoginConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SettingsValidationError{
					field:  "AbnormalLoginConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.Has_WhiteRuleList.(type) {
	case *Settings_WhiteRuleList:
		if v == nil {
			err := SettingsValidationError{
				field:  "Has_WhiteRuleList",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWhiteRuleList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "WhiteRuleList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SettingsValidationError{
						field:  "WhiteRuleList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWhiteRuleList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SettingsValidationError{
					field:  "WhiteRuleList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SettingsMultiError(errors)
	}

	return nil
}

// SettingsMultiError is an error wrapping multiple validation errors returned
// by Settings.ValidateAll() if the designated constraints aren't met.
type SettingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SettingsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SettingsMultiError) AllErrors() []error { return m }

// SettingsValidationError is the validation error returned by
// Settings.Validate if the designated constraints aren't met.
type SettingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SettingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SettingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SettingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SettingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SettingsValidationError) ErrorName() string { return "SettingsValidationError" }

// Error satisfies the builtin error interface
func (e SettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSettings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SettingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SettingsValidationError{}

// Validate checks the field values on AgentSettings with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentSettings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentSettings with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentSettingsMultiError, or
// nil if none found.
func (m *AgentSettings) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentSettings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetServerSettings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentSettingsValidationError{
					field:  "ServerSettings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentSettingsValidationError{
					field:  "ServerSettings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServerSettings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentSettingsValidationError{
				field:  "ServerSettings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.HashHashlibinfo.(type) {
	case *AgentSettings_HashLibInfo:
		if v == nil {
			err := AgentSettingsValidationError{
				field:  "HashHashlibinfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHashLibInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "HashLibInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "HashLibInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHashLibInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentSettingsValidationError{
					field:  "HashLibInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasSha256Libinfo.(type) {
	case *AgentSettings_Sha256LibInfo:
		if v == nil {
			err := AgentSettingsValidationError{
				field:  "HasSha256Libinfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSha256LibInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "Sha256LibInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "Sha256LibInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSha256LibInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentSettingsValidationError{
					field:  "Sha256LibInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasAntiviuspolicy.(type) {
	case *AgentSettings_AntiVirusPolicy:
		if v == nil {
			err := AgentSettingsValidationError{
				field:  "HasAntiviuspolicy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAntiVirusPolicy()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "AntiVirusPolicy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "AntiVirusPolicy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAntiVirusPolicy()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentSettingsValidationError{
					field:  "AntiVirusPolicy",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasWebshellengineVer.(type) {
	case *AgentSettings_WebshellEngineVer:
		if v == nil {
			err := AgentSettingsValidationError{
				field:  "HasWebshellengineVer",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebshellEngineVer()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "WebshellEngineVer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "WebshellEngineVer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebshellEngineVer()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentSettingsValidationError{
					field:  "WebshellEngineVer",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasVirusengineVer.(type) {
	case *AgentSettings_VirusEngineVer:
		if v == nil {
			err := AgentSettingsValidationError{
				field:  "HasVirusengineVer",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVirusEngineVer()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "VirusEngineVer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentSettingsValidationError{
						field:  "VirusEngineVer",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVirusEngineVer()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentSettingsValidationError{
					field:  "VirusEngineVer",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AgentSettingsMultiError(errors)
	}

	return nil
}

// AgentSettingsMultiError is an error wrapping multiple validation errors
// returned by AgentSettings.ValidateAll() if the designated constraints
// aren't met.
type AgentSettingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentSettingsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentSettingsMultiError) AllErrors() []error { return m }

// AgentSettingsValidationError is the validation error returned by
// AgentSettings.Validate if the designated constraints aren't met.
type AgentSettingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentSettingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentSettingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentSettingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentSettingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentSettingsValidationError) ErrorName() string { return "AgentSettingsValidationError" }

// Error satisfies the builtin error interface
func (e AgentSettingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentSettings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentSettingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentSettingsValidationError{}

// Validate checks the field values on WeakPwdListReponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WeakPwdListReponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WeakPwdListReponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WeakPwdListReponseMultiError, or nil if none found.
func (m *WeakPwdListReponse) ValidateAll() error {
	return m.validate(true)
}

func (m *WeakPwdListReponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return WeakPwdListReponseMultiError(errors)
	}

	return nil
}

// WeakPwdListReponseMultiError is an error wrapping multiple validation errors
// returned by WeakPwdListReponse.ValidateAll() if the designated constraints
// aren't met.
type WeakPwdListReponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WeakPwdListReponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WeakPwdListReponseMultiError) AllErrors() []error { return m }

// WeakPwdListReponseValidationError is the validation error returned by
// WeakPwdListReponse.Validate if the designated constraints aren't met.
type WeakPwdListReponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WeakPwdListReponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WeakPwdListReponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WeakPwdListReponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WeakPwdListReponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WeakPwdListReponseValidationError) ErrorName() string {
	return "WeakPwdListReponseValidationError"
}

// Error satisfies the builtin error interface
func (e WeakPwdListReponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWeakPwdListReponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WeakPwdListReponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WeakPwdListReponseValidationError{}

// Validate checks the field values on TimeItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeItemMultiError, or nil
// if none found.
func (m *TimeItem) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StartTime

	// no validation rules for EndTime

	if len(errors) > 0 {
		return TimeItemMultiError(errors)
	}

	return nil
}

// TimeItemMultiError is an error wrapping multiple validation errors returned
// by TimeItem.ValidateAll() if the designated constraints aren't met.
type TimeItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeItemMultiError) AllErrors() []error { return m }

// TimeItemValidationError is the validation error returned by
// TimeItem.Validate if the designated constraints aren't met.
type TimeItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeItemValidationError) ErrorName() string { return "TimeItemValidationError" }

// Error satisfies the builtin error interface
func (e TimeItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeItemValidationError{}

// Validate checks the field values on TimeScheduled with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeScheduled) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeScheduled with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeScheduledMultiError, or
// nil if none found.
func (m *TimeScheduled) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeScheduled) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Frequency

	for idx, item := range m.GetTimeItemList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TimeScheduledValidationError{
						field:  fmt.Sprintf("TimeItemList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TimeScheduledValidationError{
						field:  fmt.Sprintf("TimeItemList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TimeScheduledValidationError{
					field:  fmt.Sprintf("TimeItemList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Immediately

	if len(errors) > 0 {
		return TimeScheduledMultiError(errors)
	}

	return nil
}

// TimeScheduledMultiError is an error wrapping multiple validation errors
// returned by TimeScheduled.ValidateAll() if the designated constraints
// aren't met.
type TimeScheduledMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeScheduledMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeScheduledMultiError) AllErrors() []error { return m }

// TimeScheduledValidationError is the validation error returned by
// TimeScheduled.Validate if the designated constraints aren't met.
type TimeScheduledValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeScheduledValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeScheduledValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeScheduledValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeScheduledValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeScheduledValidationError) ErrorName() string { return "TimeScheduledValidationError" }

// Error satisfies the builtin error interface
func (e TimeScheduledValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeScheduled.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeScheduledValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeScheduledValidationError{}

// Validate checks the field values on WebshellScanItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WebshellScanItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebshellScanItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebshellScanItemMultiError, or nil if none found.
func (m *WebshellScanItem) ValidateAll() error {
	return m.validate(true)
}

func (m *WebshellScanItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scanpathpolicy

	if all {
		switch v := interface{}(m.GetTimeScheduled()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebshellScanItemValidationError{
					field:  "TimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebshellScanItemValidationError{
					field:  "TimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeScheduled()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebshellScanItemValidationError{
				field:  "TimeScheduled",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MonitorPath

	// no validation rules for IgnorePath

	if len(errors) > 0 {
		return WebshellScanItemMultiError(errors)
	}

	return nil
}

// WebshellScanItemMultiError is an error wrapping multiple validation errors
// returned by WebshellScanItem.ValidateAll() if the designated constraints
// aren't met.
type WebshellScanItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebshellScanItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebshellScanItemMultiError) AllErrors() []error { return m }

// WebshellScanItemValidationError is the validation error returned by
// WebshellScanItem.Validate if the designated constraints aren't met.
type WebshellScanItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebshellScanItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebshellScanItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebshellScanItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebshellScanItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebshellScanItemValidationError) ErrorName() string { return "WebshellScanItemValidationError" }

// Error satisfies the builtin error interface
func (e WebshellScanItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebshellScanItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebshellScanItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebshellScanItemValidationError{}

// Validate checks the field values on UpdateWebshellScanPolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateWebshellScanPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateWebshellScanPolicy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateWebshellScanPolicyMultiError, or nil if none found.
func (m *UpdateWebshellScanPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateWebshellScanPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	for idx, item := range m.GetWebshellScanList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateWebshellScanPolicyValidationError{
						field:  fmt.Sprintf("WebshellScanList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateWebshellScanPolicyValidationError{
						field:  fmt.Sprintf("WebshellScanList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateWebshellScanPolicyValidationError{
					field:  fmt.Sprintf("WebshellScanList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateWebshellScanPolicyMultiError(errors)
	}

	return nil
}

// UpdateWebshellScanPolicyMultiError is an error wrapping multiple validation
// errors returned by UpdateWebshellScanPolicy.ValidateAll() if the designated
// constraints aren't met.
type UpdateWebshellScanPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateWebshellScanPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateWebshellScanPolicyMultiError) AllErrors() []error { return m }

// UpdateWebshellScanPolicyValidationError is the validation error returned by
// UpdateWebshellScanPolicy.Validate if the designated constraints aren't met.
type UpdateWebshellScanPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateWebshellScanPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateWebshellScanPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateWebshellScanPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateWebshellScanPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateWebshellScanPolicyValidationError) ErrorName() string {
	return "UpdateWebshellScanPolicyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateWebshellScanPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateWebshellScanPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateWebshellScanPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateWebshellScanPolicyValidationError{}

// Validate checks the field values on AntiVirusPolicy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AntiVirusPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AntiVirusPolicy with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AntiVirusPolicyMultiError, or nil if none found.
func (m *AntiVirusPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *AntiVirusPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Scanpathpolicy

	if all {
		switch v := interface{}(m.GetTimeScheduled()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AntiVirusPolicyValidationError{
					field:  "TimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AntiVirusPolicyValidationError{
					field:  "TimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeScheduled()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AntiVirusPolicyValidationError{
				field:  "TimeScheduled",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MonitorPath

	// no validation rules for IgnorePath

	// no validation rules for Enable

	// no validation rules for MaxFilesize

	// no validation rules for SnapshotEnable

	if all {
		switch v := interface{}(m.GetSnapshotTimeScheduled()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AntiVirusPolicyValidationError{
					field:  "SnapshotTimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AntiVirusPolicyValidationError{
					field:  "SnapshotTimeScheduled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSnapshotTimeScheduled()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AntiVirusPolicyValidationError{
				field:  "SnapshotTimeScheduled",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SnapshotMaxFilesize

	// no validation rules for AntiScanTypeConf

	// no validation rules for PhishingDetect

	// no validation rules for RealTimeScanLinux

	// no validation rules for RealTimeScanWindows

	// no validation rules for UdsikPhishingDetect

	// no validation rules for ImPhishingDetect

	// no validation rules for EmailPhishingDetect

	if len(errors) > 0 {
		return AntiVirusPolicyMultiError(errors)
	}

	return nil
}

// AntiVirusPolicyMultiError is an error wrapping multiple validation errors
// returned by AntiVirusPolicy.ValidateAll() if the designated constraints
// aren't met.
type AntiVirusPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AntiVirusPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AntiVirusPolicyMultiError) AllErrors() []error { return m }

// AntiVirusPolicyValidationError is the validation error returned by
// AntiVirusPolicy.Validate if the designated constraints aren't met.
type AntiVirusPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AntiVirusPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AntiVirusPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AntiVirusPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AntiVirusPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AntiVirusPolicyValidationError) ErrorName() string { return "AntiVirusPolicyValidationError" }

// Error satisfies the builtin error interface
func (e AntiVirusPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAntiVirusPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AntiVirusPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AntiVirusPolicyValidationError{}

// Validate checks the field values on VirusDetectItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VirusDetectItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusDetectItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VirusDetectItemMultiError, or nil if none found.
func (m *VirusDetectItem) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusDetectItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Result

	// no validation rules for RiskLevel

	// no validation rules for RiskType

	// no validation rules for VirusName

	// no validation rules for MalwareType

	// no validation rules for FileType

	// no validation rules for FilePath

	// no validation rules for ServerAddr

	// no validation rules for AgentAddr

	// no validation rules for Source

	// no validation rules for ReqTime

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	// no validation rules for StMode

	// no validation rules for Sha1

	// no validation rules for Md5

	// no validation rules for FileSize

	// no validation rules for UniqueFlag

	// no validation rules for FileVersion

	// no validation rules for FileVendor

	for idx, item := range m.GetSignatureInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VirusDetectItemValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VirusDetectItemValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VirusDetectItemValidationError{
					field:  fmt.Sprintf("SignatureInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TlshHash

	// no validation rules for ImpHash

	// no validation rules for Priority

	if len(errors) > 0 {
		return VirusDetectItemMultiError(errors)
	}

	return nil
}

// VirusDetectItemMultiError is an error wrapping multiple validation errors
// returned by VirusDetectItem.ValidateAll() if the designated constraints
// aren't met.
type VirusDetectItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusDetectItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusDetectItemMultiError) AllErrors() []error { return m }

// VirusDetectItemValidationError is the validation error returned by
// VirusDetectItem.Validate if the designated constraints aren't met.
type VirusDetectItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusDetectItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusDetectItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusDetectItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusDetectItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusDetectItemValidationError) ErrorName() string { return "VirusDetectItemValidationError" }

// Error satisfies the builtin error interface
func (e VirusDetectItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusDetectItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusDetectItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusDetectItemValidationError{}

// Validate checks the field values on SearchBySha256Req with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchBySha256Req) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchBySha256Req with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchBySha256ReqMultiError, or nil if none found.
func (m *SearchBySha256Req) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchBySha256Req) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskScanType

	for idx, item := range m.GetSha256List() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchBySha256ReqValidationError{
						field:  fmt.Sprintf("Sha256List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchBySha256ReqValidationError{
						field:  fmt.Sprintf("Sha256List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchBySha256ReqValidationError{
					field:  fmt.Sprintf("Sha256List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchBySha256ReqMultiError(errors)
	}

	return nil
}

// SearchBySha256ReqMultiError is an error wrapping multiple validation errors
// returned by SearchBySha256Req.ValidateAll() if the designated constraints
// aren't met.
type SearchBySha256ReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchBySha256ReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchBySha256ReqMultiError) AllErrors() []error { return m }

// SearchBySha256ReqValidationError is the validation error returned by
// SearchBySha256Req.Validate if the designated constraints aren't met.
type SearchBySha256ReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchBySha256ReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchBySha256ReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchBySha256ReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchBySha256ReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchBySha256ReqValidationError) ErrorName() string {
	return "SearchBySha256ReqValidationError"
}

// Error satisfies the builtin error interface
func (e SearchBySha256ReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchBySha256Req.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchBySha256ReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchBySha256ReqValidationError{}

// Validate checks the field values on SearchBySha256Resp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchBySha256Resp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchBySha256Resp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchBySha256RespMultiError, or nil if none found.
func (m *SearchBySha256Resp) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchBySha256Resp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVirusDetect_List() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchBySha256RespValidationError{
						field:  fmt.Sprintf("VirusDetect_List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchBySha256RespValidationError{
						field:  fmt.Sprintf("VirusDetect_List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchBySha256RespValidationError{
					field:  fmt.Sprintf("VirusDetect_List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SearchBySha256RespMultiError(errors)
	}

	return nil
}

// SearchBySha256RespMultiError is an error wrapping multiple validation errors
// returned by SearchBySha256Resp.ValidateAll() if the designated constraints
// aren't met.
type SearchBySha256RespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchBySha256RespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchBySha256RespMultiError) AllErrors() []error { return m }

// SearchBySha256RespValidationError is the validation error returned by
// SearchBySha256Resp.Validate if the designated constraints aren't met.
type SearchBySha256RespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchBySha256RespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchBySha256RespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchBySha256RespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchBySha256RespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchBySha256RespValidationError) ErrorName() string {
	return "SearchBySha256RespValidationError"
}

// Error satisfies the builtin error interface
func (e SearchBySha256RespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchBySha256Resp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchBySha256RespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchBySha256RespValidationError{}

// Validate checks the field values on UploadFileReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadFileReqMultiError, or
// nil if none found.
func (m *UploadFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	// no validation rules for Url

	// no validation rules for Params

	// no validation rules for Filepath

	if len(errors) > 0 {
		return UploadFileReqMultiError(errors)
	}

	return nil
}

// UploadFileReqMultiError is an error wrapping multiple validation errors
// returned by UploadFileReq.ValidateAll() if the designated constraints
// aren't met.
type UploadFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileReqMultiError) AllErrors() []error { return m }

// UploadFileReqValidationError is the validation error returned by
// UploadFileReq.Validate if the designated constraints aren't met.
type UploadFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileReqValidationError) ErrorName() string { return "UploadFileReqValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileReqValidationError{}

// Validate checks the field values on UploadFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFileResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadFileRespMultiError,
// or nil if none found.
func (m *UploadFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for Sha256

	// no validation rules for FileType

	// no validation rules for Filename

	if len(errors) > 0 {
		return UploadFileRespMultiError(errors)
	}

	return nil
}

// UploadFileRespMultiError is an error wrapping multiple validation errors
// returned by UploadFileResp.ValidateAll() if the designated constraints
// aren't met.
type UploadFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileRespMultiError) AllErrors() []error { return m }

// UploadFileRespValidationError is the validation error returned by
// UploadFileResp.Validate if the designated constraints aren't met.
type UploadFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileRespValidationError) ErrorName() string { return "UploadFileRespValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileRespValidationError{}

// Validate checks the field values on DetectEngineVerReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectEngineVerReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectEngineVerReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectEngineVerReqMultiError, or nil if none found.
func (m *DetectEngineVerReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectEngineVerReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	if len(errors) > 0 {
		return DetectEngineVerReqMultiError(errors)
	}

	return nil
}

// DetectEngineVerReqMultiError is an error wrapping multiple validation errors
// returned by DetectEngineVerReq.ValidateAll() if the designated constraints
// aren't met.
type DetectEngineVerReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectEngineVerReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectEngineVerReqMultiError) AllErrors() []error { return m }

// DetectEngineVerReqValidationError is the validation error returned by
// DetectEngineVerReq.Validate if the designated constraints aren't met.
type DetectEngineVerReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectEngineVerReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectEngineVerReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectEngineVerReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectEngineVerReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectEngineVerReqValidationError) ErrorName() string {
	return "DetectEngineVerReqValidationError"
}

// Error satisfies the builtin error interface
func (e DetectEngineVerReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectEngineVerReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectEngineVerReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectEngineVerReqValidationError{}

// Validate checks the field values on DetectEngineVerResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DetectEngineVerResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DetectEngineVerResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DetectEngineVerRespMultiError, or nil if none found.
func (m *DetectEngineVerResp) ValidateAll() error {
	return m.validate(true)
}

func (m *DetectEngineVerResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for EngineVerStamp

	// no validation rules for EngineSwitchStamp

	if len(errors) > 0 {
		return DetectEngineVerRespMultiError(errors)
	}

	return nil
}

// DetectEngineVerRespMultiError is an error wrapping multiple validation
// errors returned by DetectEngineVerResp.ValidateAll() if the designated
// constraints aren't met.
type DetectEngineVerRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetectEngineVerRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetectEngineVerRespMultiError) AllErrors() []error { return m }

// DetectEngineVerRespValidationError is the validation error returned by
// DetectEngineVerResp.Validate if the designated constraints aren't met.
type DetectEngineVerRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetectEngineVerRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetectEngineVerRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetectEngineVerRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetectEngineVerRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetectEngineVerRespValidationError) ErrorName() string {
	return "DetectEngineVerRespValidationError"
}

// Error satisfies the builtin error interface
func (e DetectEngineVerRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetectEngineVerResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetectEngineVerRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetectEngineVerRespValidationError{}

// Validate checks the field values on AgentFileReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentFileReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentFileReqMultiError, or
// nil if none found.
func (m *AgentFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	// no validation rules for FilePath

	// no validation rules for ProcessId

	// no validation rules for ClassName

	// no validation rules for ClassLoaderName

	// no validation rules for Url

	// no validation rules for Params

	// no validation rules for Kind

	// no validation rules for Host

	if len(errors) > 0 {
		return AgentFileReqMultiError(errors)
	}

	return nil
}

// AgentFileReqMultiError is an error wrapping multiple validation errors
// returned by AgentFileReq.ValidateAll() if the designated constraints aren't met.
type AgentFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentFileReqMultiError) AllErrors() []error { return m }

// AgentFileReqValidationError is the validation error returned by
// AgentFileReq.Validate if the designated constraints aren't met.
type AgentFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentFileReqValidationError) ErrorName() string { return "AgentFileReqValidationError" }

// Error satisfies the builtin error interface
func (e AgentFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentFileReqValidationError{}

// Validate checks the field values on AgentFileResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentFileResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentFileRespMultiError, or
// nil if none found.
func (m *AgentFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	// no validation rules for Kind

	// no validation rules for Status

	if len(errors) > 0 {
		return AgentFileRespMultiError(errors)
	}

	return nil
}

// AgentFileRespMultiError is an error wrapping multiple validation errors
// returned by AgentFileResp.ValidateAll() if the designated constraints
// aren't met.
type AgentFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentFileRespMultiError) AllErrors() []error { return m }

// AgentFileRespValidationError is the validation error returned by
// AgentFileResp.Validate if the designated constraints aren't met.
type AgentFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentFileRespValidationError) ErrorName() string { return "AgentFileRespValidationError" }

// Error satisfies the builtin error interface
func (e AgentFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentFileRespValidationError{}

// Validate checks the field values on GetProcPathPidsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProcPathPidsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProcPathPidsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProcPathPidsReqMultiError, or nil if none found.
func (m *GetProcPathPidsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProcPathPidsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	if len(errors) > 0 {
		return GetProcPathPidsReqMultiError(errors)
	}

	return nil
}

// GetProcPathPidsReqMultiError is an error wrapping multiple validation errors
// returned by GetProcPathPidsReq.ValidateAll() if the designated constraints
// aren't met.
type GetProcPathPidsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProcPathPidsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProcPathPidsReqMultiError) AllErrors() []error { return m }

// GetProcPathPidsReqValidationError is the validation error returned by
// GetProcPathPidsReq.Validate if the designated constraints aren't met.
type GetProcPathPidsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProcPathPidsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProcPathPidsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProcPathPidsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProcPathPidsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProcPathPidsReqValidationError) ErrorName() string {
	return "GetProcPathPidsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetProcPathPidsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProcPathPidsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProcPathPidsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProcPathPidsReqValidationError{}

// Validate checks the field values on ProcPathPidsItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcPathPidsItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcPathPidsItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcPathPidsItemMultiError, or nil if none found.
func (m *ProcPathPidsItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcPathPidsItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	// no validation rules for ProcessPath

	if len(errors) > 0 {
		return ProcPathPidsItemMultiError(errors)
	}

	return nil
}

// ProcPathPidsItemMultiError is an error wrapping multiple validation errors
// returned by ProcPathPidsItem.ValidateAll() if the designated constraints
// aren't met.
type ProcPathPidsItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcPathPidsItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcPathPidsItemMultiError) AllErrors() []error { return m }

// ProcPathPidsItemValidationError is the validation error returned by
// ProcPathPidsItem.Validate if the designated constraints aren't met.
type ProcPathPidsItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcPathPidsItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcPathPidsItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcPathPidsItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcPathPidsItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcPathPidsItemValidationError) ErrorName() string { return "ProcPathPidsItemValidationError" }

// Error satisfies the builtin error interface
func (e ProcPathPidsItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcPathPidsItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcPathPidsItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcPathPidsItemValidationError{}

// Validate checks the field values on GetProcPathPidsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetProcPathPidsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetProcPathPidsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetProcPathPidsRespMultiError, or nil if none found.
func (m *GetProcPathPidsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetProcPathPidsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetProcPathPidsRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetProcPathPidsRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetProcPathPidsRespValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetProcPathPidsRespMultiError(errors)
	}

	return nil
}

// GetProcPathPidsRespMultiError is an error wrapping multiple validation
// errors returned by GetProcPathPidsResp.ValidateAll() if the designated
// constraints aren't met.
type GetProcPathPidsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetProcPathPidsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetProcPathPidsRespMultiError) AllErrors() []error { return m }

// GetProcPathPidsRespValidationError is the validation error returned by
// GetProcPathPidsResp.Validate if the designated constraints aren't met.
type GetProcPathPidsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetProcPathPidsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetProcPathPidsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetProcPathPidsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetProcPathPidsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetProcPathPidsRespValidationError) ErrorName() string {
	return "GetProcPathPidsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetProcPathPidsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetProcPathPidsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetProcPathPidsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetProcPathPidsRespValidationError{}

// Validate checks the field values on ThreatenHandleItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThreatenHandleItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThreatenHandleItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThreatenHandleItemMultiError, or nil if none found.
func (m *ThreatenHandleItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ThreatenHandleItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessPath

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThreatenHandleItemValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThreatenHandleItemValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThreatenHandleItemValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ThreatenHandleItemMultiError(errors)
	}

	return nil
}

// ThreatenHandleItemMultiError is an error wrapping multiple validation errors
// returned by ThreatenHandleItem.ValidateAll() if the designated constraints
// aren't met.
type ThreatenHandleItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThreatenHandleItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThreatenHandleItemMultiError) AllErrors() []error { return m }

// ThreatenHandleItemValidationError is the validation error returned by
// ThreatenHandleItem.Validate if the designated constraints aren't met.
type ThreatenHandleItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThreatenHandleItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThreatenHandleItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThreatenHandleItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThreatenHandleItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThreatenHandleItemValidationError) ErrorName() string {
	return "ThreatenHandleItemValidationError"
}

// Error satisfies the builtin error interface
func (e ThreatenHandleItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThreatenHandleItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThreatenHandleItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThreatenHandleItemValidationError{}

// Validate checks the field values on ThreatenHandleReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ThreatenHandleReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThreatenHandleReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThreatenHandleReqMultiError, or nil if none found.
func (m *ThreatenHandleReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ThreatenHandleReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	// no validation rules for HandleTime

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThreatenHandleReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThreatenHandleReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThreatenHandleReqValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ThreatenHandleReqMultiError(errors)
	}

	return nil
}

// ThreatenHandleReqMultiError is an error wrapping multiple validation errors
// returned by ThreatenHandleReq.ValidateAll() if the designated constraints
// aren't met.
type ThreatenHandleReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThreatenHandleReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThreatenHandleReqMultiError) AllErrors() []error { return m }

// ThreatenHandleReqValidationError is the validation error returned by
// ThreatenHandleReq.Validate if the designated constraints aren't met.
type ThreatenHandleReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThreatenHandleReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThreatenHandleReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThreatenHandleReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThreatenHandleReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThreatenHandleReqValidationError) ErrorName() string {
	return "ThreatenHandleReqValidationError"
}

// Error satisfies the builtin error interface
func (e ThreatenHandleReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThreatenHandleReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThreatenHandleReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThreatenHandleReqValidationError{}

// Validate checks the field values on ThreatenHandleResItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThreatenHandleResItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThreatenHandleResItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThreatenHandleResItemMultiError, or nil if none found.
func (m *ThreatenHandleResItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ThreatenHandleResItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Result

	if len(errors) > 0 {
		return ThreatenHandleResItemMultiError(errors)
	}

	return nil
}

// ThreatenHandleResItemMultiError is an error wrapping multiple validation
// errors returned by ThreatenHandleResItem.ValidateAll() if the designated
// constraints aren't met.
type ThreatenHandleResItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThreatenHandleResItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThreatenHandleResItemMultiError) AllErrors() []error { return m }

// ThreatenHandleResItemValidationError is the validation error returned by
// ThreatenHandleResItem.Validate if the designated constraints aren't met.
type ThreatenHandleResItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThreatenHandleResItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThreatenHandleResItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThreatenHandleResItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThreatenHandleResItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThreatenHandleResItemValidationError) ErrorName() string {
	return "ThreatenHandleResItemValidationError"
}

// Error satisfies the builtin error interface
func (e ThreatenHandleResItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThreatenHandleResItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThreatenHandleResItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThreatenHandleResItemValidationError{}

// Validate checks the field values on ThreatenHandleResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ThreatenHandleResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThreatenHandleResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThreatenHandleRespMultiError, or nil if none found.
func (m *ThreatenHandleResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ThreatenHandleResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DetailId

	// no validation rules for HandleTime

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ThreatenHandleRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ThreatenHandleRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ThreatenHandleRespValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ThreatenHandleRespMultiError(errors)
	}

	return nil
}

// ThreatenHandleRespMultiError is an error wrapping multiple validation errors
// returned by ThreatenHandleResp.ValidateAll() if the designated constraints
// aren't met.
type ThreatenHandleRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThreatenHandleRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThreatenHandleRespMultiError) AllErrors() []error { return m }

// ThreatenHandleRespValidationError is the validation error returned by
// ThreatenHandleResp.Validate if the designated constraints aren't met.
type ThreatenHandleRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThreatenHandleRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThreatenHandleRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThreatenHandleRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThreatenHandleRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThreatenHandleRespValidationError) ErrorName() string {
	return "ThreatenHandleRespValidationError"
}

// Error satisfies the builtin error interface
func (e ThreatenHandleRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThreatenHandleResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThreatenHandleRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThreatenHandleRespValidationError{}

// Validate checks the field values on FileVirusDetectionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileVirusDetectionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileVirusDetectionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileVirusDetectionInfoMultiError, or nil if none found.
func (m *FileVirusDetectionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileVirusDetectionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileKind

	// no validation rules for TotalScannedNumbers

	// no validation rules for FinishScannedNumbers

	if len(errors) > 0 {
		return FileVirusDetectionInfoMultiError(errors)
	}

	return nil
}

// FileVirusDetectionInfoMultiError is an error wrapping multiple validation
// errors returned by FileVirusDetectionInfo.ValidateAll() if the designated
// constraints aren't met.
type FileVirusDetectionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileVirusDetectionInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileVirusDetectionInfoMultiError) AllErrors() []error { return m }

// FileVirusDetectionInfoValidationError is the validation error returned by
// FileVirusDetectionInfo.Validate if the designated constraints aren't met.
type FileVirusDetectionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileVirusDetectionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileVirusDetectionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileVirusDetectionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileVirusDetectionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileVirusDetectionInfoValidationError) ErrorName() string {
	return "FileVirusDetectionInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileVirusDetectionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileVirusDetectionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileVirusDetectionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileVirusDetectionInfoValidationError{}
