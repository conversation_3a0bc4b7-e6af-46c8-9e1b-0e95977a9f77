// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_system.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskSystemInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskSystemInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskSystemInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskSystemInfoMultiError, or nil if none found.
func (m *MemProtectRiskSystemInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskSystemInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRiskSystemInfoOfRegList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfRegList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfRegList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskSystemInfoOfRegList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRiskSystemInfoOfAppList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfAppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfAppList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskSystemInfoOfAppList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRiskSystemKernelList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemKernelList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemKernelList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskSystemKernelList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRiskAttackList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskAttackList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDirtyCowList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("DirtyCowList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("DirtyCowList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("DirtyCowList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcessPrivilegeList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("ProcessPrivilegeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("ProcessPrivilegeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("ProcessPrivilegeList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRiskSystemInfoOfAccountList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfAccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskSystemInfoOfAccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskSystemInfoOfAccountList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRiskDomainEventLogList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskDomainEventLogList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskSystemInfoValidationError{
						field:  fmt.Sprintf("RiskDomainEventLogList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskSystemInfoValidationError{
					field:  fmt.Sprintf("RiskDomainEventLogList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskSystemInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskSystemInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskSystemInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskSystemInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskSystemInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskSystemInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskSystemInfoValidationError is the validation error returned by
// MemProtectRiskSystemInfo.Validate if the designated constraints aren't met.
type MemProtectRiskSystemInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskSystemInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskSystemInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskSystemInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskSystemInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskSystemInfoValidationError) ErrorName() string {
	return "MemProtectRiskSystemInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskSystemInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskSystemInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskSystemInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskSystemInfoValidationError{}

// Validate checks the field values on MemProtectRiskBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskBaseInfoMultiError, or nil if none found.
func (m *MemProtectRiskBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DateTime

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskBaseInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskBaseInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskBaseInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskBaseInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskBaseInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskBaseInfoValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientTime

	if len(errors) > 0 {
		return MemProtectRiskBaseInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskBaseInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskBaseInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskBaseInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskBaseInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskBaseInfoValidationError is the validation error returned by
// MemProtectRiskBaseInfo.Validate if the designated constraints aren't met.
type MemProtectRiskBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskBaseInfoValidationError) ErrorName() string {
	return "MemProtectRiskBaseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskBaseInfoValidationError{}

// Validate checks the field values on RiskSystemInfoOfReg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemInfoOfReg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemInfoOfReg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemInfoOfRegMultiError, or nil if none found.
func (m *RiskSystemInfoOfReg) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemInfoOfReg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSystemInfoOfRegValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSystemInfoOfRegValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSystemInfoOfRegValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RegPath

	// no validation rules for RegOp

	// no validation rules for RegSetValue

	if len(errors) > 0 {
		return RiskSystemInfoOfRegMultiError(errors)
	}

	return nil
}

// RiskSystemInfoOfRegMultiError is an error wrapping multiple validation
// errors returned by RiskSystemInfoOfReg.ValidateAll() if the designated
// constraints aren't met.
type RiskSystemInfoOfRegMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemInfoOfRegMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemInfoOfRegMultiError) AllErrors() []error { return m }

// RiskSystemInfoOfRegValidationError is the validation error returned by
// RiskSystemInfoOfReg.Validate if the designated constraints aren't met.
type RiskSystemInfoOfRegValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemInfoOfRegValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemInfoOfRegValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemInfoOfRegValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemInfoOfRegValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemInfoOfRegValidationError) ErrorName() string {
	return "RiskSystemInfoOfRegValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSystemInfoOfRegValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemInfoOfReg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemInfoOfRegValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemInfoOfRegValidationError{}

// Validate checks the field values on RiskSystemInfoOfApp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemInfoOfApp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemInfoOfApp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemInfoOfAppMultiError, or nil if none found.
func (m *RiskSystemInfoOfApp) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemInfoOfApp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSystemInfoOfAppValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSystemInfoOfAppValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSystemInfoOfAppValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SubProcessPath

	// no validation rules for SubProcessFileSha256

	// no validation rules for SubProcessCommandLine

	if len(errors) > 0 {
		return RiskSystemInfoOfAppMultiError(errors)
	}

	return nil
}

// RiskSystemInfoOfAppMultiError is an error wrapping multiple validation
// errors returned by RiskSystemInfoOfApp.ValidateAll() if the designated
// constraints aren't met.
type RiskSystemInfoOfAppMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemInfoOfAppMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemInfoOfAppMultiError) AllErrors() []error { return m }

// RiskSystemInfoOfAppValidationError is the validation error returned by
// RiskSystemInfoOfApp.Validate if the designated constraints aren't met.
type RiskSystemInfoOfAppValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemInfoOfAppValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemInfoOfAppValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemInfoOfAppValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemInfoOfAppValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemInfoOfAppValidationError) ErrorName() string {
	return "RiskSystemInfoOfAppValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSystemInfoOfAppValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemInfoOfApp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemInfoOfAppValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemInfoOfAppValidationError{}

// Validate checks the field values on RiskSystemKernel with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemKernel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemKernel with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemKernelMultiError, or nil if none found.
func (m *RiskSystemKernel) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemKernel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DateTime

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSystemKernelValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSystemKernelValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSystemKernelValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskType

	// no validation rules for ClientVersion

	// no validation rules for ClientTime

	switch v := m.Desc.(type) {
	case *RiskSystemKernel_IntegrityFailure:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIntegrityFailure()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "IntegrityFailure",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "IntegrityFailure",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIntegrityFailure()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "IntegrityFailure",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernel_Hidemod:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHidemod()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "Hidemod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "Hidemod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHidemod()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "Hidemod",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernel_EntryExs:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEntryExs()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "EntryExs",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "EntryExs",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEntryExs()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "EntryExs",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernel_HideProcess:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHideProcess()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "HideProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "HideProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHideProcess()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "HideProcess",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernel_Leakinfo:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLeakinfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "Leakinfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "Leakinfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLeakinfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "Leakinfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernel_CveInfo:
		if v == nil {
			err := RiskSystemKernelValidationError{
				field:  "Desc",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCveInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "CveInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelValidationError{
						field:  "CveInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCveInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelValidationError{
					field:  "CveInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RiskSystemKernelMultiError(errors)
	}

	return nil
}

// RiskSystemKernelMultiError is an error wrapping multiple validation errors
// returned by RiskSystemKernel.ValidateAll() if the designated constraints
// aren't met.
type RiskSystemKernelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemKernelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemKernelMultiError) AllErrors() []error { return m }

// RiskSystemKernelValidationError is the validation error returned by
// RiskSystemKernel.Validate if the designated constraints aren't met.
type RiskSystemKernelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemKernelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemKernelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemKernelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemKernelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemKernelValidationError) ErrorName() string { return "RiskSystemKernelValidationError" }

// Error satisfies the builtin error interface
func (e RiskSystemKernelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemKernel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemKernelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemKernelValidationError{}

// Validate checks the field values on LeakOverflowInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LeakOverflowInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LeakOverflowInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LeakOverflowInfoMultiError, or nil if none found.
func (m *LeakOverflowInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LeakOverflowInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportID

	// no validation rules for RemoteAddr

	// no validation rules for Cve

	// no validation rules for NetData

	if len(errors) > 0 {
		return LeakOverflowInfoMultiError(errors)
	}

	return nil
}

// LeakOverflowInfoMultiError is an error wrapping multiple validation errors
// returned by LeakOverflowInfo.ValidateAll() if the designated constraints
// aren't met.
type LeakOverflowInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LeakOverflowInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LeakOverflowInfoMultiError) AllErrors() []error { return m }

// LeakOverflowInfoValidationError is the validation error returned by
// LeakOverflowInfo.Validate if the designated constraints aren't met.
type LeakOverflowInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LeakOverflowInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LeakOverflowInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LeakOverflowInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LeakOverflowInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LeakOverflowInfoValidationError) ErrorName() string { return "LeakOverflowInfoValidationError" }

// Error satisfies the builtin error interface
func (e LeakOverflowInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLeakOverflowInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LeakOverflowInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LeakOverflowInfoValidationError{}

// Validate checks the field values on CveInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CveInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CveInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CveInfoMultiError, or nil if none found.
func (m *CveInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CveInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AttackSource

	if len(errors) > 0 {
		return CveInfoMultiError(errors)
	}

	return nil
}

// CveInfoMultiError is an error wrapping multiple validation errors returned
// by CveInfo.ValidateAll() if the designated constraints aren't met.
type CveInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CveInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CveInfoMultiError) AllErrors() []error { return m }

// CveInfoValidationError is the validation error returned by CveInfo.Validate
// if the designated constraints aren't met.
type CveInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CveInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CveInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CveInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CveInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CveInfoValidationError) ErrorName() string { return "CveInfoValidationError" }

// Error satisfies the builtin error interface
func (e CveInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCveInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CveInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CveInfoValidationError{}

// Validate checks the field values on RiskAttack with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskAttack with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskAttackMultiError, or
// nil if none found.
func (m *RiskAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskAttackValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskAttackValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskAttackValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RiskType

	// no validation rules for HostIP

	// no validation rules for UserName

	// no validation rules for ComputerName

	if len(errors) > 0 {
		return RiskAttackMultiError(errors)
	}

	return nil
}

// RiskAttackMultiError is an error wrapping multiple validation errors
// returned by RiskAttack.ValidateAll() if the designated constraints aren't met.
type RiskAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskAttackMultiError) AllErrors() []error { return m }

// RiskAttackValidationError is the validation error returned by
// RiskAttack.Validate if the designated constraints aren't met.
type RiskAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskAttackValidationError) ErrorName() string { return "RiskAttackValidationError" }

// Error satisfies the builtin error interface
func (e RiskAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskAttackValidationError{}

// Validate checks the field values on DirtyCow with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DirtyCow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DirtyCow with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DirtyCowMultiError, or nil
// if none found.
func (m *DirtyCow) ValidateAll() error {
	return m.validate(true)
}

func (m *DirtyCow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DirtyCowValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DirtyCowValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DirtyCowValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DirtyCowMultiError(errors)
	}

	return nil
}

// DirtyCowMultiError is an error wrapping multiple validation errors returned
// by DirtyCow.ValidateAll() if the designated constraints aren't met.
type DirtyCowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DirtyCowMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DirtyCowMultiError) AllErrors() []error { return m }

// DirtyCowValidationError is the validation error returned by
// DirtyCow.Validate if the designated constraints aren't met.
type DirtyCowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DirtyCowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DirtyCowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DirtyCowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DirtyCowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DirtyCowValidationError) ErrorName() string { return "DirtyCowValidationError" }

// Error satisfies the builtin error interface
func (e DirtyCowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDirtyCow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DirtyCowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DirtyCowValidationError{}

// Validate checks the field values on ProcessPrivilege with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcessPrivilege) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessPrivilege with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessPrivilegeMultiError, or nil if none found.
func (m *ProcessPrivilege) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessPrivilege) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessPrivilegeValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessPrivilegeValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessPrivilegeValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Modname

	if len(errors) > 0 {
		return ProcessPrivilegeMultiError(errors)
	}

	return nil
}

// ProcessPrivilegeMultiError is an error wrapping multiple validation errors
// returned by ProcessPrivilege.ValidateAll() if the designated constraints
// aren't met.
type ProcessPrivilegeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessPrivilegeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessPrivilegeMultiError) AllErrors() []error { return m }

// ProcessPrivilegeValidationError is the validation error returned by
// ProcessPrivilege.Validate if the designated constraints aren't met.
type ProcessPrivilegeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessPrivilegeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessPrivilegeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessPrivilegeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessPrivilegeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessPrivilegeValidationError) ErrorName() string { return "ProcessPrivilegeValidationError" }

// Error satisfies the builtin error interface
func (e ProcessPrivilegeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessPrivilege.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessPrivilegeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessPrivilegeValidationError{}

// Validate checks the field values on HideMod with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HideMod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HideMod with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in HideModMultiError, or nil if none found.
func (m *HideMod) ValidateAll() error {
	return m.validate(true)
}

func (m *HideMod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	if len(errors) > 0 {
		return HideModMultiError(errors)
	}

	return nil
}

// HideModMultiError is an error wrapping multiple validation errors returned
// by HideMod.ValidateAll() if the designated constraints aren't met.
type HideModMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HideModMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HideModMultiError) AllErrors() []error { return m }

// HideModValidationError is the validation error returned by HideMod.Validate
// if the designated constraints aren't met.
type HideModValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HideModValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HideModValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HideModValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HideModValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HideModValidationError) ErrorName() string { return "HideModValidationError" }

// Error satisfies the builtin error interface
func (e HideModValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHideMod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HideModValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HideModValidationError{}

// Validate checks the field values on IntegrityFailure with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IntegrityFailure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IntegrityFailure with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IntegrityFailureMultiError, or nil if none found.
func (m *IntegrityFailure) ValidateAll() error {
	return m.validate(true)
}

func (m *IntegrityFailure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	if len(errors) > 0 {
		return IntegrityFailureMultiError(errors)
	}

	return nil
}

// IntegrityFailureMultiError is an error wrapping multiple validation errors
// returned by IntegrityFailure.ValidateAll() if the designated constraints
// aren't met.
type IntegrityFailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntegrityFailureMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntegrityFailureMultiError) AllErrors() []error { return m }

// IntegrityFailureValidationError is the validation error returned by
// IntegrityFailure.Validate if the designated constraints aren't met.
type IntegrityFailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntegrityFailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntegrityFailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntegrityFailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntegrityFailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntegrityFailureValidationError) ErrorName() string { return "IntegrityFailureValidationError" }

// Error satisfies the builtin error interface
func (e IntegrityFailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIntegrityFailure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntegrityFailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntegrityFailureValidationError{}

// Validate checks the field values on RiskInfoCallbackNoMod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskInfoCallbackNoMod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfoCallbackNoMod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInfoCallbackNoModMultiError, or nil if none found.
func (m *RiskInfoCallbackNoMod) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfoCallbackNoMod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrCallBackType

	// no validation rules for StrCallBackSubType

	// no validation rules for StrCallBackAddr

	if len(errors) > 0 {
		return RiskInfoCallbackNoModMultiError(errors)
	}

	return nil
}

// RiskInfoCallbackNoModMultiError is an error wrapping multiple validation
// errors returned by RiskInfoCallbackNoMod.ValidateAll() if the designated
// constraints aren't met.
type RiskInfoCallbackNoModMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoCallbackNoModMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoCallbackNoModMultiError) AllErrors() []error { return m }

// RiskInfoCallbackNoModValidationError is the validation error returned by
// RiskInfoCallbackNoMod.Validate if the designated constraints aren't met.
type RiskInfoCallbackNoModValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoCallbackNoModValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoCallbackNoModValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoCallbackNoModValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoCallbackNoModValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoCallbackNoModValidationError) ErrorName() string {
	return "RiskInfoCallbackNoModValidationError"
}

// Error satisfies the builtin error interface
func (e RiskInfoCallbackNoModValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfoCallbackNoMod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoCallbackNoModValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoCallbackNoModValidationError{}

// Validate checks the field values on RiskInfoValueAbnoraml with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskInfoValueAbnoraml) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfoValueAbnoraml with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInfoValueAbnoramlMultiError, or nil if none found.
func (m *RiskInfoValueAbnoraml) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfoValueAbnoraml) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrAbnormalType

	// no validation rules for StrAbnormalSubType

	// no validation rules for StrAbnormalAddr

	// no validation rules for StrNormalAddr

	if len(errors) > 0 {
		return RiskInfoValueAbnoramlMultiError(errors)
	}

	return nil
}

// RiskInfoValueAbnoramlMultiError is an error wrapping multiple validation
// errors returned by RiskInfoValueAbnoraml.ValidateAll() if the designated
// constraints aren't met.
type RiskInfoValueAbnoramlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoValueAbnoramlMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoValueAbnoramlMultiError) AllErrors() []error { return m }

// RiskInfoValueAbnoramlValidationError is the validation error returned by
// RiskInfoValueAbnoraml.Validate if the designated constraints aren't met.
type RiskInfoValueAbnoramlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoValueAbnoramlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoValueAbnoramlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoValueAbnoramlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoValueAbnoramlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoValueAbnoramlValidationError) ErrorName() string {
	return "RiskInfoValueAbnoramlValidationError"
}

// Error satisfies the builtin error interface
func (e RiskInfoValueAbnoramlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfoValueAbnoraml.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoValueAbnoramlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoValueAbnoramlValidationError{}

// Validate checks the field values on RiskInfoBlackCharacter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskInfoBlackCharacter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfoBlackCharacter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInfoBlackCharacterMultiError, or nil if none found.
func (m *RiskInfoBlackCharacter) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfoBlackCharacter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrCharacterType

	// no validation rules for StrCharacterSubType

	// no validation rules for StrCharacterInfo

	if len(errors) > 0 {
		return RiskInfoBlackCharacterMultiError(errors)
	}

	return nil
}

// RiskInfoBlackCharacterMultiError is an error wrapping multiple validation
// errors returned by RiskInfoBlackCharacter.ValidateAll() if the designated
// constraints aren't met.
type RiskInfoBlackCharacterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoBlackCharacterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoBlackCharacterMultiError) AllErrors() []error { return m }

// RiskInfoBlackCharacterValidationError is the validation error returned by
// RiskInfoBlackCharacter.Validate if the designated constraints aren't met.
type RiskInfoBlackCharacterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoBlackCharacterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoBlackCharacterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoBlackCharacterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoBlackCharacterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoBlackCharacterValidationError) ErrorName() string {
	return "RiskInfoBlackCharacterValidationError"
}

// Error satisfies the builtin error interface
func (e RiskInfoBlackCharacterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfoBlackCharacter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoBlackCharacterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoBlackCharacterValidationError{}

// Validate checks the field values on RiskInfoBlackMod with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskInfoBlackMod) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfoBlackMod with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInfoBlackModMultiError, or nil if none found.
func (m *RiskInfoBlackMod) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfoBlackMod) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrBlackModName

	// no validation rules for StrBlackModFilePath

	// no validation rules for StrBlackModBaseAddr

	// no validation rules for StrBlackModSize

	// no validation rules for StrBlackDetail

	if len(errors) > 0 {
		return RiskInfoBlackModMultiError(errors)
	}

	return nil
}

// RiskInfoBlackModMultiError is an error wrapping multiple validation errors
// returned by RiskInfoBlackMod.ValidateAll() if the designated constraints
// aren't met.
type RiskInfoBlackModMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoBlackModMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoBlackModMultiError) AllErrors() []error { return m }

// RiskInfoBlackModValidationError is the validation error returned by
// RiskInfoBlackMod.Validate if the designated constraints aren't met.
type RiskInfoBlackModValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoBlackModValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoBlackModValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoBlackModValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoBlackModValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoBlackModValidationError) ErrorName() string { return "RiskInfoBlackModValidationError" }

// Error satisfies the builtin error interface
func (e RiskInfoBlackModValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfoBlackMod.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoBlackModValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoBlackModValidationError{}

// Validate checks the field values on RiskInfoWhiteModTamper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskInfoWhiteModTamper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskInfoWhiteModTamper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskInfoWhiteModTamperMultiError, or nil if none found.
func (m *RiskInfoWhiteModTamper) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskInfoWhiteModTamper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StrModName

	// no validation rules for StrModBase

	// no validation rules for StrTamperDetail

	if len(errors) > 0 {
		return RiskInfoWhiteModTamperMultiError(errors)
	}

	return nil
}

// RiskInfoWhiteModTamperMultiError is an error wrapping multiple validation
// errors returned by RiskInfoWhiteModTamper.ValidateAll() if the designated
// constraints aren't met.
type RiskInfoWhiteModTamperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskInfoWhiteModTamperMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskInfoWhiteModTamperMultiError) AllErrors() []error { return m }

// RiskInfoWhiteModTamperValidationError is the validation error returned by
// RiskInfoWhiteModTamper.Validate if the designated constraints aren't met.
type RiskInfoWhiteModTamperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskInfoWhiteModTamperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskInfoWhiteModTamperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskInfoWhiteModTamperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskInfoWhiteModTamperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskInfoWhiteModTamperValidationError) ErrorName() string {
	return "RiskInfoWhiteModTamperValidationError"
}

// Error satisfies the builtin error interface
func (e RiskInfoWhiteModTamperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskInfoWhiteModTamper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskInfoWhiteModTamperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskInfoWhiteModTamperValidationError{}

// Validate checks the field values on RiskSystemKernelEntry with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemKernelEntry) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemKernelEntry with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemKernelEntryMultiError, or nil if none found.
func (m *RiskSystemKernelEntry) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemKernelEntry) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EKrnlRiskType

	switch v := m.KrnlRiskEntryDetail.(type) {
	case *RiskSystemKernelEntry_StCallbackNoMod:
		if v == nil {
			err := RiskSystemKernelEntryValidationError{
				field:  "KrnlRiskEntryDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStCallbackNoMod()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StCallbackNoMod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StCallbackNoMod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStCallbackNoMod()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelEntryValidationError{
					field:  "StCallbackNoMod",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernelEntry_StValueAbnormal:
		if v == nil {
			err := RiskSystemKernelEntryValidationError{
				field:  "KrnlRiskEntryDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStValueAbnormal()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StValueAbnormal",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StValueAbnormal",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStValueAbnormal()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelEntryValidationError{
					field:  "StValueAbnormal",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernelEntry_StBlackCharacter:
		if v == nil {
			err := RiskSystemKernelEntryValidationError{
				field:  "KrnlRiskEntryDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStBlackCharacter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StBlackCharacter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StBlackCharacter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStBlackCharacter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelEntryValidationError{
					field:  "StBlackCharacter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernelEntry_StBlackMod:
		if v == nil {
			err := RiskSystemKernelEntryValidationError{
				field:  "KrnlRiskEntryDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStBlackMod()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StBlackMod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StBlackMod",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStBlackMod()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelEntryValidationError{
					field:  "StBlackMod",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskSystemKernelEntry_StWhiteModTamper:
		if v == nil {
			err := RiskSystemKernelEntryValidationError{
				field:  "KrnlRiskEntryDetail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStWhiteModTamper()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StWhiteModTamper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelEntryValidationError{
						field:  "StWhiteModTamper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStWhiteModTamper()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelEntryValidationError{
					field:  "StWhiteModTamper",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RiskSystemKernelEntryMultiError(errors)
	}

	return nil
}

// RiskSystemKernelEntryMultiError is an error wrapping multiple validation
// errors returned by RiskSystemKernelEntry.ValidateAll() if the designated
// constraints aren't met.
type RiskSystemKernelEntryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemKernelEntryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemKernelEntryMultiError) AllErrors() []error { return m }

// RiskSystemKernelEntryValidationError is the validation error returned by
// RiskSystemKernelEntry.Validate if the designated constraints aren't met.
type RiskSystemKernelEntryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemKernelEntryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemKernelEntryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemKernelEntryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemKernelEntryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemKernelEntryValidationError) ErrorName() string {
	return "RiskSystemKernelEntryValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSystemKernelEntryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemKernelEntry.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemKernelEntryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemKernelEntryValidationError{}

// Validate checks the field values on RiskSystemKernelExs with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemKernelExs) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemKernelExs with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemKernelExsMultiError, or nil if none found.
func (m *RiskSystemKernelExs) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemKernelExs) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskName

	for idx, item := range m.GetRiskSystemKernelEntryList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskSystemKernelExsValidationError{
						field:  fmt.Sprintf("RiskSystemKernelEntryList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskSystemKernelExsValidationError{
						field:  fmt.Sprintf("RiskSystemKernelEntryList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskSystemKernelExsValidationError{
					field:  fmt.Sprintf("RiskSystemKernelEntryList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RiskSystemKernelExsMultiError(errors)
	}

	return nil
}

// RiskSystemKernelExsMultiError is an error wrapping multiple validation
// errors returned by RiskSystemKernelExs.ValidateAll() if the designated
// constraints aren't met.
type RiskSystemKernelExsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemKernelExsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemKernelExsMultiError) AllErrors() []error { return m }

// RiskSystemKernelExsValidationError is the validation error returned by
// RiskSystemKernelExs.Validate if the designated constraints aren't met.
type RiskSystemKernelExsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemKernelExsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemKernelExsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemKernelExsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemKernelExsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemKernelExsValidationError) ErrorName() string {
	return "RiskSystemKernelExsValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSystemKernelExsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemKernelExs.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemKernelExsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemKernelExsValidationError{}

// Validate checks the field values on RiskSystemInfoOfAccount with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSystemInfoOfAccount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSystemInfoOfAccount with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSystemInfoOfAccountMultiError, or nil if none found.
func (m *RiskSystemInfoOfAccount) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSystemInfoOfAccount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBasicPorcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSystemInfoOfAccountValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSystemInfoOfAccountValidationError{
					field:  "BasicPorcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicPorcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSystemInfoOfAccountValidationError{
				field:  "BasicPorcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemoteIp

	// no validation rules for UserName

	// no validation rules for IsSuperUser

	// no validation rules for LogonType

	// no validation rules for AbnormalReason

	if len(errors) > 0 {
		return RiskSystemInfoOfAccountMultiError(errors)
	}

	return nil
}

// RiskSystemInfoOfAccountMultiError is an error wrapping multiple validation
// errors returned by RiskSystemInfoOfAccount.ValidateAll() if the designated
// constraints aren't met.
type RiskSystemInfoOfAccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSystemInfoOfAccountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSystemInfoOfAccountMultiError) AllErrors() []error { return m }

// RiskSystemInfoOfAccountValidationError is the validation error returned by
// RiskSystemInfoOfAccount.Validate if the designated constraints aren't met.
type RiskSystemInfoOfAccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSystemInfoOfAccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSystemInfoOfAccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSystemInfoOfAccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSystemInfoOfAccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSystemInfoOfAccountValidationError) ErrorName() string {
	return "RiskSystemInfoOfAccountValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSystemInfoOfAccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSystemInfoOfAccount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSystemInfoOfAccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSystemInfoOfAccountValidationError{}

// Validate checks the field values on RiskDomainEventLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskDomainEventLog) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskDomainEventLog with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskDomainEventLogMultiError, or nil if none found.
func (m *RiskDomainEventLog) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskDomainEventLog) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskDomainEventLogValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskDomainEventLogValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskDomainEventLogValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TimeCreated

	// no validation rules for ComputerName

	// no validation rules for UserName

	// no validation rules for DomainName

	// no validation rules for EventId

	// no validation rules for EventRecordId

	// no validation rules for EventMessage

	// no validation rules for EventXml

	// no validation rules for RiskType

	if all {
		switch v := interface{}(m.GetProcessSource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskDomainEventLogValidationError{
					field:  "ProcessSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskDomainEventLogValidationError{
					field:  "ProcessSource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessSource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskDomainEventLogValidationError{
				field:  "ProcessSource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Detail.(type) {
	case *RiskDomainEventLog_Rs_EventLogClear:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_EventLogClear()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_EventLogClear",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_EventLogClear",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_EventLogClear()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_EventLogClear",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_LoginWithCredentials:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_LoginWithCredentials()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_LoginWithCredentials",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_LoginWithCredentials",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_LoginWithCredentials()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_LoginWithCredentials",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_CreateDirectoryServiceObject:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_CreateDirectoryServiceObject()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_CreateDirectoryServiceObject",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_CreateDirectoryServiceObject",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_CreateDirectoryServiceObject()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_CreateDirectoryServiceObject",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_DomainAddPlanningTasks:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_DomainAddPlanningTasks()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainAddPlanningTasks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainAddPlanningTasks",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_DomainAddPlanningTasks()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_DomainAddPlanningTasks",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_DomainAddSystemservice:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_DomainAddSystemservice()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainAddSystemservice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainAddSystemservice",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_DomainAddSystemservice()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_DomainAddSystemservice",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_PsloggedonHandler:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_PsloggedonHandler()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_PsloggedonHandler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_PsloggedonHandler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_PsloggedonHandler()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_PsloggedonHandler",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_ModifySensitiveGroup:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_ModifySensitiveGroup()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_ModifySensitiveGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_ModifySensitiveGroup",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_ModifySensitiveGroup()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_ModifySensitiveGroup",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_DomainCVE2021_42278:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_DomainCVE2021_42278()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainCVE2021_42278",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainCVE2021_42278",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_DomainCVE2021_42278()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_DomainCVE2021_42278",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_AclAbnormalUpdate:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_AclAbnormalUpdate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_AclAbnormalUpdate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_AclAbnormalUpdate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_AclAbnormalUpdate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_AclAbnormalUpdate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_DomainCVE2021_42287:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_DomainCVE2021_42287()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainCVE2021_42287",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_DomainCVE2021_42287",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_DomainCVE2021_42287()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_DomainCVE2021_42287",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_CheckSensitiveGroupOrUser()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_CheckSensitiveGroupOrUser",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_CheckSensitiveGroupOrUser",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_CheckSensitiveGroupOrUser()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_CheckSensitiveGroupOrUser",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskDomainEventLog_Rs_ASREPRoasting:
		if v == nil {
			err := RiskDomainEventLogValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRs_ASREPRoasting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_ASREPRoasting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskDomainEventLogValidationError{
						field:  "Rs_ASREPRoasting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRs_ASREPRoasting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskDomainEventLogValidationError{
					field:  "Rs_ASREPRoasting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RiskDomainEventLogMultiError(errors)
	}

	return nil
}

// RiskDomainEventLogMultiError is an error wrapping multiple validation errors
// returned by RiskDomainEventLog.ValidateAll() if the designated constraints
// aren't met.
type RiskDomainEventLogMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskDomainEventLogMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskDomainEventLogMultiError) AllErrors() []error { return m }

// RiskDomainEventLogValidationError is the validation error returned by
// RiskDomainEventLog.Validate if the designated constraints aren't met.
type RiskDomainEventLogValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskDomainEventLogValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskDomainEventLogValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskDomainEventLogValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskDomainEventLogValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskDomainEventLogValidationError) ErrorName() string {
	return "RiskDomainEventLogValidationError"
}

// Error satisfies the builtin error interface
func (e RiskDomainEventLogValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskDomainEventLog.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskDomainEventLogValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskDomainEventLogValidationError{}

// Validate checks the field values on SubjectType with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubjectType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubjectType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubjectTypeMultiError, or
// nil if none found.
func (m *SubjectType) ValidateAll() error {
	return m.validate(true)
}

func (m *SubjectType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubjectDomainName

	// no validation rules for SubjectUserName

	// no validation rules for SubjectUserSid

	// no validation rules for SubjectLogonId

	if len(errors) > 0 {
		return SubjectTypeMultiError(errors)
	}

	return nil
}

// SubjectTypeMultiError is an error wrapping multiple validation errors
// returned by SubjectType.ValidateAll() if the designated constraints aren't met.
type SubjectTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubjectTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubjectTypeMultiError) AllErrors() []error { return m }

// SubjectTypeValidationError is the validation error returned by
// SubjectType.Validate if the designated constraints aren't met.
type SubjectTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubjectTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubjectTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubjectTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubjectTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubjectTypeValidationError) ErrorName() string { return "SubjectTypeValidationError" }

// Error satisfies the builtin error interface
func (e SubjectTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubjectType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubjectTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubjectTypeValidationError{}

// Validate checks the field values on TargetType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TargetType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TargetTypeMultiError, or
// nil if none found.
func (m *TargetType) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetDomainName

	// no validation rules for TargetUserName

	// no validation rules for TargetUserSid

	// no validation rules for TargetSid

	// no validation rules for TargetLogonId

	// no validation rules for TargetInfo

	// no validation rules for TargetServerName

	if len(errors) > 0 {
		return TargetTypeMultiError(errors)
	}

	return nil
}

// TargetTypeMultiError is an error wrapping multiple validation errors
// returned by TargetType.ValidateAll() if the designated constraints aren't met.
type TargetTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetTypeMultiError) AllErrors() []error { return m }

// TargetTypeValidationError is the validation error returned by
// TargetType.Validate if the designated constraints aren't met.
type TargetTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetTypeValidationError) ErrorName() string { return "TargetTypeValidationError" }

// Error satisfies the builtin error interface
func (e TargetTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetTypeValidationError{}

// Validate checks the field values on Sourcetype with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Sourcetype) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Sourcetype with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SourcetypeMultiError, or
// nil if none found.
func (m *Sourcetype) ValidateAll() error {
	return m.validate(true)
}

func (m *Sourcetype) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WorkstationName

	// no validation rules for IpAddress

	// no validation rules for IpPort

	if len(errors) > 0 {
		return SourcetypeMultiError(errors)
	}

	return nil
}

// SourcetypeMultiError is an error wrapping multiple validation errors
// returned by Sourcetype.ValidateAll() if the designated constraints aren't met.
type SourcetypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SourcetypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SourcetypeMultiError) AllErrors() []error { return m }

// SourcetypeValidationError is the validation error returned by
// Sourcetype.Validate if the designated constraints aren't met.
type SourcetypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SourcetypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SourcetypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SourcetypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SourcetypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SourcetypeValidationError) ErrorName() string { return "SourcetypeValidationError" }

// Error satisfies the builtin error interface
func (e SourcetypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSourcetype.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SourcetypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SourcetypeValidationError{}

// Validate checks the field values on TicketType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TicketType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TicketType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TicketTypeMultiError, or
// nil if none found.
func (m *TicketType) ValidateAll() error {
	return m.validate(true)
}

func (m *TicketType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TicketEncryptionType

	// no validation rules for TicketOptions

	// no validation rules for Status

	if len(errors) > 0 {
		return TicketTypeMultiError(errors)
	}

	return nil
}

// TicketTypeMultiError is an error wrapping multiple validation errors
// returned by TicketType.ValidateAll() if the designated constraints aren't met.
type TicketTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TicketTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TicketTypeMultiError) AllErrors() []error { return m }

// TicketTypeValidationError is the validation error returned by
// TicketType.Validate if the designated constraints aren't met.
type TicketTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TicketTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TicketTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TicketTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TicketTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TicketTypeValidationError) ErrorName() string { return "TicketTypeValidationError" }

// Error satisfies the builtin error interface
func (e TicketTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTicketType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TicketTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TicketTypeValidationError{}

// Validate checks the field values on ObjectType with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ObjectType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObjectType with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ObjectTypeMultiError, or
// nil if none found.
func (m *ObjectType) ValidateAll() error {
	return m.validate(true)
}

func (m *ObjectType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ObjectDN

	// no validation rules for ObjectGUID

	// no validation rules for ObjectClass

	// no validation rules for ObjectServer

	// no validation rules for ObjectType

	// no validation rules for ObjectName

	if len(errors) > 0 {
		return ObjectTypeMultiError(errors)
	}

	return nil
}

// ObjectTypeMultiError is an error wrapping multiple validation errors
// returned by ObjectType.ValidateAll() if the designated constraints aren't met.
type ObjectTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObjectTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObjectTypeMultiError) AllErrors() []error { return m }

// ObjectTypeValidationError is the validation error returned by
// ObjectType.Validate if the designated constraints aren't met.
type ObjectTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObjectTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObjectTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObjectTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObjectTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObjectTypeValidationError) ErrorName() string { return "ObjectTypeValidationError" }

// Error satisfies the builtin error interface
func (e ObjectTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObjectType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObjectTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObjectTypeValidationError{}

// Validate checks the field values on DomainRiskEventLogClear with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskEventLogClear) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskEventLogClear with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskEventLogClearMultiError, or nil if none found.
func (m *DomainRiskEventLogClear) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskEventLogClear) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskEventLogClearValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskEventLogClearValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskEventLogClearValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	if len(errors) > 0 {
		return DomainRiskEventLogClearMultiError(errors)
	}

	return nil
}

// DomainRiskEventLogClearMultiError is an error wrapping multiple validation
// errors returned by DomainRiskEventLogClear.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskEventLogClearMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskEventLogClearMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskEventLogClearMultiError) AllErrors() []error { return m }

// DomainRiskEventLogClearValidationError is the validation error returned by
// DomainRiskEventLogClear.Validate if the designated constraints aren't met.
type DomainRiskEventLogClearValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskEventLogClearValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskEventLogClearValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskEventLogClearValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskEventLogClearValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskEventLogClearValidationError) ErrorName() string {
	return "DomainRiskEventLogClearValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskEventLogClearValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskEventLogClear.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskEventLogClearValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskEventLogClearValidationError{}

// Validate checks the field values on DomainRiskLoginWithCredentials with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskLoginWithCredentials) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskLoginWithCredentials with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DomainRiskLoginWithCredentialsMultiError, or nil if none found.
func (m *DomainRiskLoginWithCredentials) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskLoginWithCredentials) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTaget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskLoginWithCredentialsValidationError{
					field:  "Taget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskLoginWithCredentialsValidationError{
					field:  "Taget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTaget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskLoginWithCredentialsValidationError{
				field:  "Taget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskLoginWithCredentialsValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskLoginWithCredentialsValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskLoginWithCredentialsValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DomainRiskLoginWithCredentialsMultiError(errors)
	}

	return nil
}

// DomainRiskLoginWithCredentialsMultiError is an error wrapping multiple
// validation errors returned by DomainRiskLoginWithCredentials.ValidateAll()
// if the designated constraints aren't met.
type DomainRiskLoginWithCredentialsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskLoginWithCredentialsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskLoginWithCredentialsMultiError) AllErrors() []error { return m }

// DomainRiskLoginWithCredentialsValidationError is the validation error
// returned by DomainRiskLoginWithCredentials.Validate if the designated
// constraints aren't met.
type DomainRiskLoginWithCredentialsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskLoginWithCredentialsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskLoginWithCredentialsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskLoginWithCredentialsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskLoginWithCredentialsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskLoginWithCredentialsValidationError) ErrorName() string {
	return "DomainRiskLoginWithCredentialsValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskLoginWithCredentialsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskLoginWithCredentials.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskLoginWithCredentialsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskLoginWithCredentialsValidationError{}

// Validate checks the field values on DomainRiskCreateDirectoryServiceObject
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DomainRiskCreateDirectoryServiceObject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DomainRiskCreateDirectoryServiceObject with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// DomainRiskCreateDirectoryServiceObjectMultiError, or nil if none found.
func (m *DomainRiskCreateDirectoryServiceObject) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskCreateDirectoryServiceObject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCreateDirectoryServiceObjectValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCreateDirectoryServiceObjectValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCreateDirectoryServiceObjectValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	if len(errors) > 0 {
		return DomainRiskCreateDirectoryServiceObjectMultiError(errors)
	}

	return nil
}

// DomainRiskCreateDirectoryServiceObjectMultiError is an error wrapping
// multiple validation errors returned by
// DomainRiskCreateDirectoryServiceObject.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskCreateDirectoryServiceObjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskCreateDirectoryServiceObjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskCreateDirectoryServiceObjectMultiError) AllErrors() []error { return m }

// DomainRiskCreateDirectoryServiceObjectValidationError is the validation
// error returned by DomainRiskCreateDirectoryServiceObject.Validate if the
// designated constraints aren't met.
type DomainRiskCreateDirectoryServiceObjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskCreateDirectoryServiceObjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskCreateDirectoryServiceObjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskCreateDirectoryServiceObjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskCreateDirectoryServiceObjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskCreateDirectoryServiceObjectValidationError) ErrorName() string {
	return "DomainRiskCreateDirectoryServiceObjectValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskCreateDirectoryServiceObjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskCreateDirectoryServiceObject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskCreateDirectoryServiceObjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskCreateDirectoryServiceObjectValidationError{}

// Validate checks the field values on DomainRiskAddPlanningTasks with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskAddPlanningTasks) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskAddPlanningTasks with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskAddPlanningTasksMultiError, or nil if none found.
func (m *DomainRiskAddPlanningTasks) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskAddPlanningTasks) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskAddPlanningTasksValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskAddPlanningTasksValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskAddPlanningTasksValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrTaskName

	// no validation rules for StrIp

	if len(errors) > 0 {
		return DomainRiskAddPlanningTasksMultiError(errors)
	}

	return nil
}

// DomainRiskAddPlanningTasksMultiError is an error wrapping multiple
// validation errors returned by DomainRiskAddPlanningTasks.ValidateAll() if
// the designated constraints aren't met.
type DomainRiskAddPlanningTasksMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskAddPlanningTasksMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskAddPlanningTasksMultiError) AllErrors() []error { return m }

// DomainRiskAddPlanningTasksValidationError is the validation error returned
// by DomainRiskAddPlanningTasks.Validate if the designated constraints aren't met.
type DomainRiskAddPlanningTasksValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskAddPlanningTasksValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskAddPlanningTasksValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskAddPlanningTasksValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskAddPlanningTasksValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskAddPlanningTasksValidationError) ErrorName() string {
	return "DomainRiskAddPlanningTasksValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskAddPlanningTasksValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskAddPlanningTasks.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskAddPlanningTasksValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskAddPlanningTasksValidationError{}

// Validate checks the field values on DomainRiskAddSystemservice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskAddSystemservice) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskAddSystemservice with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskAddSystemserviceMultiError, or nil if none found.
func (m *DomainRiskAddSystemservice) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskAddSystemservice) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskAddSystemserviceValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskAddSystemserviceValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskAddSystemserviceValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrServiceName

	// no validation rules for StrIp

	if len(errors) > 0 {
		return DomainRiskAddSystemserviceMultiError(errors)
	}

	return nil
}

// DomainRiskAddSystemserviceMultiError is an error wrapping multiple
// validation errors returned by DomainRiskAddSystemservice.ValidateAll() if
// the designated constraints aren't met.
type DomainRiskAddSystemserviceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskAddSystemserviceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskAddSystemserviceMultiError) AllErrors() []error { return m }

// DomainRiskAddSystemserviceValidationError is the validation error returned
// by DomainRiskAddSystemservice.Validate if the designated constraints aren't met.
type DomainRiskAddSystemserviceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskAddSystemserviceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskAddSystemserviceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskAddSystemserviceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskAddSystemserviceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskAddSystemserviceValidationError) ErrorName() string {
	return "DomainRiskAddSystemserviceValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskAddSystemserviceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskAddSystemservice.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskAddSystemserviceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskAddSystemserviceValidationError{}

// Validate checks the field values on DomainRiskPsloggedonHandler with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskPsloggedonHandler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskPsloggedonHandler with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskPsloggedonHandlerMultiError, or nil if none found.
func (m *DomainRiskPsloggedonHandler) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskPsloggedonHandler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskPsloggedonHandlerValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskPsloggedonHandlerValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskPsloggedonHandlerValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	if len(errors) > 0 {
		return DomainRiskPsloggedonHandlerMultiError(errors)
	}

	return nil
}

// DomainRiskPsloggedonHandlerMultiError is an error wrapping multiple
// validation errors returned by DomainRiskPsloggedonHandler.ValidateAll() if
// the designated constraints aren't met.
type DomainRiskPsloggedonHandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskPsloggedonHandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskPsloggedonHandlerMultiError) AllErrors() []error { return m }

// DomainRiskPsloggedonHandlerValidationError is the validation error returned
// by DomainRiskPsloggedonHandler.Validate if the designated constraints
// aren't met.
type DomainRiskPsloggedonHandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskPsloggedonHandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskPsloggedonHandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskPsloggedonHandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskPsloggedonHandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskPsloggedonHandlerValidationError) ErrorName() string {
	return "DomainRiskPsloggedonHandlerValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskPsloggedonHandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskPsloggedonHandler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskPsloggedonHandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskPsloggedonHandlerValidationError{}

// Validate checks the field values on DomainRiskModifySensitiveGroupHandler
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DomainRiskModifySensitiveGroupHandler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskModifySensitiveGroupHandler
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DomainRiskModifySensitiveGroupHandlerMultiError, or nil if none found.
func (m *DomainRiskModifySensitiveGroupHandler) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskModifySensitiveGroupHandler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskModifySensitiveGroupHandlerValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskModifySensitiveGroupHandlerValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskModifySensitiveGroupHandlerValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTaget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskModifySensitiveGroupHandlerValidationError{
					field:  "Taget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskModifySensitiveGroupHandlerValidationError{
					field:  "Taget",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTaget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskModifySensitiveGroupHandlerValidationError{
				field:  "Taget",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	// no validation rules for StrMemberName

	if len(errors) > 0 {
		return DomainRiskModifySensitiveGroupHandlerMultiError(errors)
	}

	return nil
}

// DomainRiskModifySensitiveGroupHandlerMultiError is an error wrapping
// multiple validation errors returned by
// DomainRiskModifySensitiveGroupHandler.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskModifySensitiveGroupHandlerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskModifySensitiveGroupHandlerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskModifySensitiveGroupHandlerMultiError) AllErrors() []error { return m }

// DomainRiskModifySensitiveGroupHandlerValidationError is the validation error
// returned by DomainRiskModifySensitiveGroupHandler.Validate if the
// designated constraints aren't met.
type DomainRiskModifySensitiveGroupHandlerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskModifySensitiveGroupHandlerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskModifySensitiveGroupHandlerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskModifySensitiveGroupHandlerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskModifySensitiveGroupHandlerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskModifySensitiveGroupHandlerValidationError) ErrorName() string {
	return "DomainRiskModifySensitiveGroupHandlerValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskModifySensitiveGroupHandlerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskModifySensitiveGroupHandler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskModifySensitiveGroupHandlerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskModifySensitiveGroupHandlerValidationError{}

// Validate checks the field values on DomainRiskCVE2021_42278 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskCVE2021_42278) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskCVE2021_42278 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskCVE2021_42278MultiError, or nil if none found.
func (m *DomainRiskCVE2021_42278) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskCVE2021_42278) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42278ValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42278ValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCVE2021_42278ValidationError{
				field:  "Target",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42278ValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42278ValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCVE2021_42278ValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OldTargetUserName

	// no validation rules for NewTargetUserName

	if len(errors) > 0 {
		return DomainRiskCVE2021_42278MultiError(errors)
	}

	return nil
}

// DomainRiskCVE2021_42278MultiError is an error wrapping multiple validation
// errors returned by DomainRiskCVE2021_42278.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskCVE2021_42278MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskCVE2021_42278MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskCVE2021_42278MultiError) AllErrors() []error { return m }

// DomainRiskCVE2021_42278ValidationError is the validation error returned by
// DomainRiskCVE2021_42278.Validate if the designated constraints aren't met.
type DomainRiskCVE2021_42278ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskCVE2021_42278ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskCVE2021_42278ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskCVE2021_42278ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskCVE2021_42278ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskCVE2021_42278ValidationError) ErrorName() string {
	return "DomainRiskCVE2021_42278ValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskCVE2021_42278ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskCVE2021_42278.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskCVE2021_42278ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskCVE2021_42278ValidationError{}

// Validate checks the field values on DomainRiskAclAbnormalUpdate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskAclAbnormalUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskAclAbnormalUpdate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskAclAbnormalUpdateMultiError, or nil if none found.
func (m *DomainRiskAclAbnormalUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskAclAbnormalUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskAclAbnormalUpdateValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskAclAbnormalUpdateValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskAclAbnormalUpdateValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	if all {
		switch v := interface{}(m.GetObject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskAclAbnormalUpdateValidationError{
					field:  "Object",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskAclAbnormalUpdateValidationError{
					field:  "Object",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetObject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskAclAbnormalUpdateValidationError{
				field:  "Object",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DomainRiskAclAbnormalUpdateMultiError(errors)
	}

	return nil
}

// DomainRiskAclAbnormalUpdateMultiError is an error wrapping multiple
// validation errors returned by DomainRiskAclAbnormalUpdate.ValidateAll() if
// the designated constraints aren't met.
type DomainRiskAclAbnormalUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskAclAbnormalUpdateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskAclAbnormalUpdateMultiError) AllErrors() []error { return m }

// DomainRiskAclAbnormalUpdateValidationError is the validation error returned
// by DomainRiskAclAbnormalUpdate.Validate if the designated constraints
// aren't met.
type DomainRiskAclAbnormalUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskAclAbnormalUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskAclAbnormalUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskAclAbnormalUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskAclAbnormalUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskAclAbnormalUpdateValidationError) ErrorName() string {
	return "DomainRiskAclAbnormalUpdateValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskAclAbnormalUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskAclAbnormalUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskAclAbnormalUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskAclAbnormalUpdateValidationError{}

// Validate checks the field values on DomainRiskCVE2021_42287 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskCVE2021_42287) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskCVE2021_42287 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskCVE2021_42287MultiError, or nil if none found.
func (m *DomainRiskCVE2021_42287) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskCVE2021_42287) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42287ValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42287ValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCVE2021_42287ValidationError{
				field:  "Target",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Family

	if all {
		switch v := interface{}(m.GetRemoteAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42287ValidationError{
					field:  "RemoteAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCVE2021_42287ValidationError{
					field:  "RemoteAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemoteAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCVE2021_42287ValidationError{
				field:  "RemoteAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DomainRiskCVE2021_42287MultiError(errors)
	}

	return nil
}

// DomainRiskCVE2021_42287MultiError is an error wrapping multiple validation
// errors returned by DomainRiskCVE2021_42287.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskCVE2021_42287MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskCVE2021_42287MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskCVE2021_42287MultiError) AllErrors() []error { return m }

// DomainRiskCVE2021_42287ValidationError is the validation error returned by
// DomainRiskCVE2021_42287.Validate if the designated constraints aren't met.
type DomainRiskCVE2021_42287ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskCVE2021_42287ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskCVE2021_42287ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskCVE2021_42287ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskCVE2021_42287ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskCVE2021_42287ValidationError) ErrorName() string {
	return "DomainRiskCVE2021_42287ValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskCVE2021_42287ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskCVE2021_42287.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskCVE2021_42287ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskCVE2021_42287ValidationError{}

// Validate checks the field values on DomainRiskCheckSensitiveGroupOrUser with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DomainRiskCheckSensitiveGroupOrUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskCheckSensitiveGroupOrUser
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DomainRiskCheckSensitiveGroupOrUserMultiError, or nil if none found.
func (m *DomainRiskCheckSensitiveGroupOrUser) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskCheckSensitiveGroupOrUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSubject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskCheckSensitiveGroupOrUserValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskCheckSensitiveGroupOrUserValidationError{
					field:  "Subject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskCheckSensitiveGroupOrUserValidationError{
				field:  "Subject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	// no validation rules for StrTargetName

	// no validation rules for Modify

	if len(errors) > 0 {
		return DomainRiskCheckSensitiveGroupOrUserMultiError(errors)
	}

	return nil
}

// DomainRiskCheckSensitiveGroupOrUserMultiError is an error wrapping multiple
// validation errors returned by
// DomainRiskCheckSensitiveGroupOrUser.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskCheckSensitiveGroupOrUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskCheckSensitiveGroupOrUserMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskCheckSensitiveGroupOrUserMultiError) AllErrors() []error { return m }

// DomainRiskCheckSensitiveGroupOrUserValidationError is the validation error
// returned by DomainRiskCheckSensitiveGroupOrUser.Validate if the designated
// constraints aren't met.
type DomainRiskCheckSensitiveGroupOrUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) ErrorName() string {
	return "DomainRiskCheckSensitiveGroupOrUserValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskCheckSensitiveGroupOrUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskCheckSensitiveGroupOrUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskCheckSensitiveGroupOrUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskCheckSensitiveGroupOrUserValidationError{}

// Validate checks the field values on DomainRiskASREPRoasting with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainRiskASREPRoasting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainRiskASREPRoasting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainRiskASREPRoastingMultiError, or nil if none found.
func (m *DomainRiskASREPRoasting) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainRiskASREPRoasting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainRiskASREPRoastingValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainRiskASREPRoastingValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainRiskASREPRoastingValidationError{
				field:  "Target",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StrIp

	// no validation rules for MatchTool

	if len(errors) > 0 {
		return DomainRiskASREPRoastingMultiError(errors)
	}

	return nil
}

// DomainRiskASREPRoastingMultiError is an error wrapping multiple validation
// errors returned by DomainRiskASREPRoasting.ValidateAll() if the designated
// constraints aren't met.
type DomainRiskASREPRoastingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainRiskASREPRoastingMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainRiskASREPRoastingMultiError) AllErrors() []error { return m }

// DomainRiskASREPRoastingValidationError is the validation error returned by
// DomainRiskASREPRoasting.Validate if the designated constraints aren't met.
type DomainRiskASREPRoastingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainRiskASREPRoastingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainRiskASREPRoastingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainRiskASREPRoastingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainRiskASREPRoastingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainRiskASREPRoastingValidationError) ErrorName() string {
	return "DomainRiskASREPRoastingValidationError"
}

// Error satisfies the builtin error interface
func (e DomainRiskASREPRoastingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainRiskASREPRoasting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainRiskASREPRoastingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainRiskASREPRoastingValidationError{}
