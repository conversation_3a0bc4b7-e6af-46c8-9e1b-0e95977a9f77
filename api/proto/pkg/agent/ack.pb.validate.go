// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/ack.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Ack with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Ack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Ack with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AckMultiError, or nil if none found.
func (m *Ack) ValidateAll() error {
	return m.validate(true)
}

func (m *Ack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CmdId

	// no validation rules for Msg

	if len(errors) > 0 {
		return AckMultiError(errors)
	}

	return nil
}

// AckMultiError is an error wrapping multiple validation errors returned by
// Ack.ValidateAll() if the designated constraints aren't met.
type AckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AckMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AckMultiError) AllErrors() []error { return m }

// AckValidationError is the validation error returned by Ack.Validate if the
// designated constraints aren't met.
type AckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AckValidationError) ErrorName() string { return "AckValidationError" }

// Error satisfies the builtin error interface
func (e AckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AckValidationError{}
