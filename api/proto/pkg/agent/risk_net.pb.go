// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_net.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MemProtectRiskNetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo *ClientID   `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	ScanList []*PortScan `protobuf:"bytes,2,rep,name=scan_list,json=scanList,proto3" json:"scan_list,omitempty"`
}

func (x *MemProtectRiskNetInfo) Reset() {
	*x = MemProtectRiskNetInfo{}
	mi := &file_agent_risk_net_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskNetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskNetInfo) ProtoMessage() {}

func (x *MemProtectRiskNetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_net_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskNetInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskNetInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_net_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskNetInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectRiskNetInfo) GetScanList() []*PortScan {
	if x != nil {
		return x.ScanList
	}
	return nil
}

// 端口扫描
type PortScan struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RemoteIp []byte           `protobuf:"bytes,4,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`
	Protocol InternetProtocol `protobuf:"varint,6,opt,name=protocol,proto3,enum=agent.InternetProtocol" json:"protocol,omitempty"`
}

func (x *PortScan) Reset() {
	*x = PortScan{}
	mi := &file_agent_risk_net_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortScan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortScan) ProtoMessage() {}

func (x *PortScan) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_net_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortScan.ProtoReflect.Descriptor instead.
func (*PortScan) Descriptor() ([]byte, []int) {
	return file_agent_risk_net_proto_rawDescGZIP(), []int{1}
}

func (x *PortScan) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PortScan) GetRemoteIp() []byte {
	if x != nil {
		return x.RemoteIp
	}
	return nil
}

func (x *PortScan) GetProtocol() InternetProtocol {
	if x != nil {
		return x.Protocol
	}
	return InternetProtocol_IP_TCP
}

var File_agent_risk_net_proto protoreflect.FileDescriptor

var file_agent_risk_net_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6e, 0x65, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x72, 0x0a, 0x15, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x09, 0x73, 0x63, 0x61, 0x6e, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x52, 0x08, 0x73, 0x63, 0x61,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x87, 0x01, 0x0a, 0x08, 0x50, 0x6f, 0x72, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42,
	0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_net_proto_rawDescOnce sync.Once
	file_agent_risk_net_proto_rawDescData = file_agent_risk_net_proto_rawDesc
)

func file_agent_risk_net_proto_rawDescGZIP() []byte {
	file_agent_risk_net_proto_rawDescOnce.Do(func() {
		file_agent_risk_net_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_net_proto_rawDescData)
	})
	return file_agent_risk_net_proto_rawDescData
}

var file_agent_risk_net_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_risk_net_proto_goTypes = []any{
	(*MemProtectRiskNetInfo)(nil), // 0: agent.MemProtectRiskNetInfo
	(*PortScan)(nil),              // 1: agent.PortScan
	(*ClientID)(nil),              // 2: agent.ClientID
	(*RiskHeader)(nil),            // 3: agent.RiskHeader
	(InternetProtocol)(0),         // 4: agent.InternetProtocol
}
var file_agent_risk_net_proto_depIdxs = []int32{
	2, // 0: agent.MemProtectRiskNetInfo.baseInfo:type_name -> agent.ClientID
	1, // 1: agent.MemProtectRiskNetInfo.scan_list:type_name -> agent.PortScan
	3, // 2: agent.PortScan.header:type_name -> agent.RiskHeader
	4, // 3: agent.PortScan.protocol:type_name -> agent.InternetProtocol
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_agent_risk_net_proto_init() }
func file_agent_risk_net_proto_init() {
	if File_agent_risk_net_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_net_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_net_proto_goTypes,
		DependencyIndexes: file_agent_risk_net_proto_depIdxs,
		MessageInfos:      file_agent_risk_net_proto_msgTypes,
	}.Build()
	File_agent_risk_net_proto = out.File
	file_agent_risk_net_proto_rawDesc = nil
	file_agent_risk_net_proto_goTypes = nil
	file_agent_risk_net_proto_depIdxs = nil
}
