// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/evidence.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ObtainEvidenceStatus int32

const (
	ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_UNKNOWN             ObtainEvidenceStatus = 0
	ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_OK                  ObtainEvidenceStatus = 1
	ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_FILE_NOT_FOUND      ObtainEvidenceStatus = 2
	ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_UPLOAD_FAILED       ObtainEvidenceStatus = 3
	ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_EVIDENCE_FLAG_ERROR ObtainEvidenceStatus = 4
)

// Enum value maps for ObtainEvidenceStatus.
var (
	ObtainEvidenceStatus_name = map[int32]string{
		0: "OBTAIN_EVIDENCE_STATUS_UNKNOWN",
		1: "OBTAIN_EVIDENCE_STATUS_OK",
		2: "OBTAIN_EVIDENCE_STATUS_FILE_NOT_FOUND",
		3: "OBTAIN_EVIDENCE_STATUS_UPLOAD_FAILED",
		4: "OBTAIN_EVIDENCE_STATUS_EVIDENCE_FLAG_ERROR",
	}
	ObtainEvidenceStatus_value = map[string]int32{
		"OBTAIN_EVIDENCE_STATUS_UNKNOWN":             0,
		"OBTAIN_EVIDENCE_STATUS_OK":                  1,
		"OBTAIN_EVIDENCE_STATUS_FILE_NOT_FOUND":      2,
		"OBTAIN_EVIDENCE_STATUS_UPLOAD_FAILED":       3,
		"OBTAIN_EVIDENCE_STATUS_EVIDENCE_FLAG_ERROR": 4,
	}
)

func (x ObtainEvidenceStatus) Enum() *ObtainEvidenceStatus {
	p := new(ObtainEvidenceStatus)
	*p = x
	return p
}

func (x ObtainEvidenceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ObtainEvidenceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_evidence_proto_enumTypes[0].Descriptor()
}

func (ObtainEvidenceStatus) Type() protoreflect.EnumType {
	return &file_agent_evidence_proto_enumTypes[0]
}

func (x ObtainEvidenceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ObtainEvidenceStatus.Descriptor instead.
func (ObtainEvidenceStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_evidence_proto_rawDescGZIP(), []int{0}
}

// server -> agent
type ObtainEvidenceReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvidenceType EvidenceType `protobuf:"varint,1,opt,name=EvidenceType,proto3,enum=agent.EvidenceType" json:"EvidenceType,omitempty"` // 证据文件类型
	Key          string       `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`                                            // 文件路径/md5/sha1/...
	Url          string       `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`                                            // 证据上传URL, 如果URL为空，则不需要上传，仅返回文件路径信息
	TaskId       uint64       `protobuf:"varint,4,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                       // 任务ID
}

func (x *ObtainEvidenceReq) Reset() {
	*x = ObtainEvidenceReq{}
	mi := &file_agent_evidence_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceReq) ProtoMessage() {}

func (x *ObtainEvidenceReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_evidence_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceReq.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceReq) Descriptor() ([]byte, []int) {
	return file_agent_evidence_proto_rawDescGZIP(), []int{0}
}

func (x *ObtainEvidenceReq) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_UNKNOWN
}

func (x *ObtainEvidenceReq) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ObtainEvidenceReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ObtainEvidenceReq) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// agent -> server （仅失败时上报）
type ObtainEvidenceResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId   uint64               `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                   // 任务ID
	Status   ObtainEvidenceStatus `protobuf:"varint,2,opt,name=status,proto3,enum=agent.ObtainEvidenceStatus" json:"status,omitempty"` // 任务状态
	Filepath string               `protobuf:"bytes,3,opt,name=filepath,proto3" json:"filepath,omitempty"`                              // 文件路径
}

func (x *ObtainEvidenceResp) Reset() {
	*x = ObtainEvidenceResp{}
	mi := &file_agent_evidence_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceResp) ProtoMessage() {}

func (x *ObtainEvidenceResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_evidence_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceResp.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceResp) Descriptor() ([]byte, []int) {
	return file_agent_evidence_proto_rawDescGZIP(), []int{1}
}

func (x *ObtainEvidenceResp) GetTaskId() uint64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ObtainEvidenceResp) GetStatus() ObtainEvidenceStatus {
	if x != nil {
		return x.Status
	}
	return ObtainEvidenceStatus_OBTAIN_EVIDENCE_STATUS_UNKNOWN
}

func (x *ObtainEvidenceResp) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

var File_agent_evidence_proto protoreflect.FileDescriptor

var file_agent_evidence_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x89, 0x01, 0x0a, 0x11, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x12, 0x37, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x7e, 0x0a,
	0x12, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x2a, 0xde, 0x01,
	0x0a, 0x14, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x42, 0x54, 0x41, 0x49, 0x4e,
	0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x42,
	0x54, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4b, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x4f, 0x42, 0x54,
	0x41, 0x49, 0x4e, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x4f, 0x42, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x45,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x2e,
	0x0a, 0x2a, 0x4f, 0x42, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x46, 0x4c, 0x41, 0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x04, 0x42, 0x2a,
	0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_agent_evidence_proto_rawDescOnce sync.Once
	file_agent_evidence_proto_rawDescData = file_agent_evidence_proto_rawDesc
)

func file_agent_evidence_proto_rawDescGZIP() []byte {
	file_agent_evidence_proto_rawDescOnce.Do(func() {
		file_agent_evidence_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_evidence_proto_rawDescData)
	})
	return file_agent_evidence_proto_rawDescData
}

var file_agent_evidence_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_evidence_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_evidence_proto_goTypes = []any{
	(ObtainEvidenceStatus)(0),  // 0: agent.ObtainEvidenceStatus
	(*ObtainEvidenceReq)(nil),  // 1: agent.ObtainEvidenceReq
	(*ObtainEvidenceResp)(nil), // 2: agent.ObtainEvidenceResp
	(EvidenceType)(0),          // 3: agent.EvidenceType
}
var file_agent_evidence_proto_depIdxs = []int32{
	3, // 0: agent.ObtainEvidenceReq.EvidenceType:type_name -> agent.EvidenceType
	0, // 1: agent.ObtainEvidenceResp.status:type_name -> agent.ObtainEvidenceStatus
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_agent_evidence_proto_init() }
func file_agent_evidence_proto_init() {
	if File_agent_evidence_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_evidence_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_evidence_proto_goTypes,
		DependencyIndexes: file_agent_evidence_proto_depIdxs,
		EnumInfos:         file_agent_evidence_proto_enumTypes,
		MessageInfos:      file_agent_evidence_proto_msgTypes,
	}.Build()
	File_agent_evidence_proto = out.File
	file_agent_evidence_proto_rawDesc = nil
	file_agent_evidence_proto_goTypes = nil
	file_agent_evidence_proto_depIdxs = nil
}
