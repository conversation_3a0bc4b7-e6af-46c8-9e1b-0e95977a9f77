// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/upgrade.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ServerPushMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ServerPushMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServerPushMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServerPushMessageMultiError, or nil if none found.
func (m *ServerPushMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *ServerPushMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseDownloadInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ServerPushMessageValidationError{
					field:  "BaseDownloadInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ServerPushMessageValidationError{
					field:  "BaseDownloadInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseDownloadInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ServerPushMessageValidationError{
				field:  "BaseDownloadInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.HasDriverUpgradeMessage.(type) {
	case *ServerPushMessage_DriverUpgradeMessage:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasDriverUpgradeMessage",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDriverUpgradeMessage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DriverUpgradeMessage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DriverUpgradeMessage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDriverUpgradeMessage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "DriverUpgradeMessage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasHashenginefile.(type) {
	case *ServerPushMessage_HashEngineFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasHashenginefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHashEngineFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "HashEngineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "HashEngineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHashEngineFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "HashEngineFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasSha256Enginefile.(type) {
	case *ServerPushMessage_Sha256EngineFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasSha256Enginefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSha256EngineFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "Sha256EngineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "Sha256EngineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSha256EngineFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "Sha256EngineFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasNgavfile.(type) {
	case *ServerPushMessage_NgavFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasNgavfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNgavFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "NgavFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "NgavFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNgavFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "NgavFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasRaspfile.(type) {
	case *ServerPushMessage_RaspFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasRaspfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRaspFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "RaspFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "RaspFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRaspFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "RaspFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasBaselinefile.(type) {
	case *ServerPushMessage_BaselineFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasBaselinefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBaselineFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "BaselineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "BaselineFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBaselineFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "BaselineFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasDomainwhitefile.(type) {
	case *ServerPushMessage_DomainwhiteFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasDomainwhitefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDomainwhiteFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DomainwhiteFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DomainwhiteFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDomainwhiteFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "DomainwhiteFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasFilesigncomwhitefile.(type) {
	case *ServerPushMessage_FilesigncomwhiteFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasFilesigncomwhitefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFilesigncomwhiteFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "FilesigncomwhiteFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "FilesigncomwhiteFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilesigncomwhiteFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "FilesigncomwhiteFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasDriverblacklistfile.(type) {
	case *ServerPushMessage_DriverblacklistFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasDriverblacklistfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDriverblacklistFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DriverblacklistFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "DriverblacklistFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDriverblacklistFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "DriverblacklistFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasAcdrlibfile.(type) {
	case *ServerPushMessage_AcdrlibFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasAcdrlibfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAcdrlibFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "AcdrlibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "AcdrlibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAcdrlibFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "AcdrlibFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasAttackwhitefile.(type) {
	case *ServerPushMessage_AttackwhitelibFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasAttackwhitefile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAttackwhitelibFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "AttackwhitelibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "AttackwhitelibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAttackwhitelibFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "AttackwhitelibFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasSignblacklibfile.(type) {
	case *ServerPushMessage_SignblacklibFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasSignblacklibfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSignblacklibFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "SignblacklibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "SignblacklibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSignblacklibFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "SignblacklibFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.HasSllibfile.(type) {
	case *ServerPushMessage_SllibFile:
		if v == nil {
			err := ServerPushMessageValidationError{
				field:  "HasSllibfile",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSllibFile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "SllibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ServerPushMessageValidationError{
						field:  "SllibFile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSllibFile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ServerPushMessageValidationError{
					field:  "SllibFile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ServerPushMessageMultiError(errors)
	}

	return nil
}

// ServerPushMessageMultiError is an error wrapping multiple validation errors
// returned by ServerPushMessage.ValidateAll() if the designated constraints
// aren't met.
type ServerPushMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServerPushMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServerPushMessageMultiError) AllErrors() []error { return m }

// ServerPushMessageValidationError is the validation error returned by
// ServerPushMessage.Validate if the designated constraints aren't met.
type ServerPushMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServerPushMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServerPushMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServerPushMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServerPushMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServerPushMessageValidationError) ErrorName() string {
	return "ServerPushMessageValidationError"
}

// Error satisfies the builtin error interface
func (e ServerPushMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServerPushMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServerPushMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServerPushMessageValidationError{}

// Validate checks the field values on BaseDownloadInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BaseDownloadInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaseDownloadInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BaseDownloadInfoMultiError, or nil if none found.
func (m *BaseDownloadInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BaseDownloadInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PkgUrl

	// no validation rules for Way

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BaseDownloadInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BaseDownloadInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BaseDownloadInfoValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BaseDownloadInfoMultiError(errors)
	}

	return nil
}

// BaseDownloadInfoMultiError is an error wrapping multiple validation errors
// returned by BaseDownloadInfo.ValidateAll() if the designated constraints
// aren't met.
type BaseDownloadInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaseDownloadInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaseDownloadInfoMultiError) AllErrors() []error { return m }

// BaseDownloadInfoValidationError is the validation error returned by
// BaseDownloadInfo.Validate if the designated constraints aren't met.
type BaseDownloadInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaseDownloadInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaseDownloadInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaseDownloadInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaseDownloadInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaseDownloadInfoValidationError) ErrorName() string { return "BaseDownloadInfoValidationError" }

// Error satisfies the builtin error interface
func (e BaseDownloadInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaseDownloadInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaseDownloadInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaseDownloadInfoValidationError{}

// Validate checks the field values on RequestFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RequestFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestFile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RequestFileMultiError, or
// nil if none found.
func (m *RequestFile) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrigName

	// no validation rules for FileBlockSize

	// no validation rules for FileNum

	// no validation rules for RelativePath

	if len(errors) > 0 {
		return RequestFileMultiError(errors)
	}

	return nil
}

// RequestFileMultiError is an error wrapping multiple validation errors
// returned by RequestFile.ValidateAll() if the designated constraints aren't met.
type RequestFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestFileMultiError) AllErrors() []error { return m }

// RequestFileValidationError is the validation error returned by
// RequestFile.Validate if the designated constraints aren't met.
type RequestFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestFileValidationError) ErrorName() string { return "RequestFileValidationError" }

// Error satisfies the builtin error interface
func (e RequestFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestFileValidationError{}

// Validate checks the field values on ResponseFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ResponseFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResponseFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResponseFileMultiError, or
// nil if none found.
func (m *ResponseFile) ValidateAll() error {
	return m.validate(true)
}

func (m *ResponseFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrigName

	// no validation rules for FileBlockSize

	// no validation rules for FileNum

	// no validation rules for FileContent

	if len(errors) > 0 {
		return ResponseFileMultiError(errors)
	}

	return nil
}

// ResponseFileMultiError is an error wrapping multiple validation errors
// returned by ResponseFile.ValidateAll() if the designated constraints aren't met.
type ResponseFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResponseFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResponseFileMultiError) AllErrors() []error { return m }

// ResponseFileValidationError is the validation error returned by
// ResponseFile.Validate if the designated constraints aren't met.
type ResponseFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResponseFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResponseFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResponseFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResponseFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResponseFileValidationError) ErrorName() string { return "ResponseFileValidationError" }

// Error satisfies the builtin error interface
func (e ResponseFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResponseFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResponseFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResponseFileValidationError{}

// Validate checks the field values on UploadFile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadFile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadFileMultiError, or
// nil if none found.
func (m *UploadFile) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	// no validation rules for TotalBlock

	// no validation rules for FileNum

	// no validation rules for FileContent

	if len(errors) > 0 {
		return UploadFileMultiError(errors)
	}

	return nil
}

// UploadFileMultiError is an error wrapping multiple validation errors
// returned by UploadFile.ValidateAll() if the designated constraints aren't met.
type UploadFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadFileMultiError) AllErrors() []error { return m }

// UploadFileValidationError is the validation error returned by
// UploadFile.Validate if the designated constraints aren't met.
type UploadFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadFileValidationError) ErrorName() string { return "UploadFileValidationError" }

// Error satisfies the builtin error interface
func (e UploadFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadFileValidationError{}

// Validate checks the field values on FileInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileInfoMultiError, or nil
// if none found.
func (m *FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OrigName

	// no validation rules for FileSize

	// no validation rules for FileMd5

	// no validation rules for RelativePath

	if len(errors) > 0 {
		return FileInfoMultiError(errors)
	}

	return nil
}

// FileInfoMultiError is an error wrapping multiple validation errors returned
// by FileInfo.ValidateAll() if the designated constraints aren't met.
type FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileInfoMultiError) AllErrors() []error { return m }

// FileInfoValidationError is the validation error returned by
// FileInfo.Validate if the designated constraints aren't met.
type FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileInfoValidationError) ErrorName() string { return "FileInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileInfoValidationError{}

// Validate checks the field values on AgentUpgradeMsg with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentUpgradeMsg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentUpgradeMsg with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentUpgradeMsgMultiError, or nil if none found.
func (m *AgentUpgradeMsg) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentUpgradeMsg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VersionName

	// no validation rules for VersionCode

	// no validation rules for ArchInfo

	// no validation rules for PkgMd5

	// no validation rules for PkgUrl

	// no validation rules for Flag

	// no validation rules for Way

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentUpgradeMsgValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentUpgradeMsgValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentUpgradeMsgValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentUpgradeMsgMultiError(errors)
	}

	return nil
}

// AgentUpgradeMsgMultiError is an error wrapping multiple validation errors
// returned by AgentUpgradeMsg.ValidateAll() if the designated constraints
// aren't met.
type AgentUpgradeMsgMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentUpgradeMsgMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentUpgradeMsgMultiError) AllErrors() []error { return m }

// AgentUpgradeMsgValidationError is the validation error returned by
// AgentUpgradeMsg.Validate if the designated constraints aren't met.
type AgentUpgradeMsgValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentUpgradeMsgValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentUpgradeMsgValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentUpgradeMsgValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentUpgradeMsgValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentUpgradeMsgValidationError) ErrorName() string { return "AgentUpgradeMsgValidationError" }

// Error satisfies the builtin error interface
func (e AgentUpgradeMsgValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentUpgradeMsg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentUpgradeMsgValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentUpgradeMsgValidationError{}

// Validate checks the field values on AgentUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentUpgradeResultMultiError, or nil if none found.
func (m *AgentUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentUpgradeResultValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentUpgradeResultValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentUpgradeResultValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VersionName

	// no validation rules for VersionCode

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return AgentUpgradeResultMultiError(errors)
	}

	return nil
}

// AgentUpgradeResultMultiError is an error wrapping multiple validation errors
// returned by AgentUpgradeResult.ValidateAll() if the designated constraints
// aren't met.
type AgentUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentUpgradeResultMultiError) AllErrors() []error { return m }

// AgentUpgradeResultValidationError is the validation error returned by
// AgentUpgradeResult.Validate if the designated constraints aren't met.
type AgentUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentUpgradeResultValidationError) ErrorName() string {
	return "AgentUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e AgentUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentUpgradeResultValidationError{}

// Validate checks the field values on DriverUpgradeMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DriverUpgradeMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DriverUpgradeMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DriverUpgradeMessageMultiError, or nil if none found.
func (m *DriverUpgradeMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *DriverUpgradeMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return DriverUpgradeMessageMultiError(errors)
	}

	return nil
}

// DriverUpgradeMessageMultiError is an error wrapping multiple validation
// errors returned by DriverUpgradeMessage.ValidateAll() if the designated
// constraints aren't met.
type DriverUpgradeMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DriverUpgradeMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DriverUpgradeMessageMultiError) AllErrors() []error { return m }

// DriverUpgradeMessageValidationError is the validation error returned by
// DriverUpgradeMessage.Validate if the designated constraints aren't met.
type DriverUpgradeMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DriverUpgradeMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DriverUpgradeMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DriverUpgradeMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DriverUpgradeMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DriverUpgradeMessageValidationError) ErrorName() string {
	return "DriverUpgradeMessageValidationError"
}

// Error satisfies the builtin error interface
func (e DriverUpgradeMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDriverUpgradeMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DriverUpgradeMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DriverUpgradeMessageValidationError{}

// Validate checks the field values on HashEngineFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HashEngineFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashEngineFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HashEngineFileMultiError,
// or nil if none found.
func (m *HashEngineFile) ValidateAll() error {
	return m.validate(true)
}

func (m *HashEngineFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return HashEngineFileMultiError(errors)
	}

	return nil
}

// HashEngineFileMultiError is an error wrapping multiple validation errors
// returned by HashEngineFile.ValidateAll() if the designated constraints
// aren't met.
type HashEngineFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashEngineFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashEngineFileMultiError) AllErrors() []error { return m }

// HashEngineFileValidationError is the validation error returned by
// HashEngineFile.Validate if the designated constraints aren't met.
type HashEngineFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashEngineFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashEngineFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashEngineFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashEngineFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashEngineFileValidationError) ErrorName() string { return "HashEngineFileValidationError" }

// Error satisfies the builtin error interface
func (e HashEngineFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashEngineFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashEngineFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashEngineFileValidationError{}

// Validate checks the field values on NgavFile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NgavFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NgavFile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NgavFileMultiError, or nil
// if none found.
func (m *NgavFile) ValidateAll() error {
	return m.validate(true)
}

func (m *NgavFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return NgavFileMultiError(errors)
	}

	return nil
}

// NgavFileMultiError is an error wrapping multiple validation errors returned
// by NgavFile.ValidateAll() if the designated constraints aren't met.
type NgavFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NgavFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NgavFileMultiError) AllErrors() []error { return m }

// NgavFileValidationError is the validation error returned by
// NgavFile.Validate if the designated constraints aren't met.
type NgavFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NgavFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NgavFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NgavFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NgavFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NgavFileValidationError) ErrorName() string { return "NgavFileValidationError" }

// Error satisfies the builtin error interface
func (e NgavFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNgavFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NgavFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NgavFileValidationError{}

// Validate checks the field values on RaspFile with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RaspFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaspFile with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RaspFileMultiError, or nil
// if none found.
func (m *RaspFile) ValidateAll() error {
	return m.validate(true)
}

func (m *RaspFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return RaspFileMultiError(errors)
	}

	return nil
}

// RaspFileMultiError is an error wrapping multiple validation errors returned
// by RaspFile.ValidateAll() if the designated constraints aren't met.
type RaspFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaspFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaspFileMultiError) AllErrors() []error { return m }

// RaspFileValidationError is the validation error returned by
// RaspFile.Validate if the designated constraints aren't met.
type RaspFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaspFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaspFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaspFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaspFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaspFileValidationError) ErrorName() string { return "RaspFileValidationError" }

// Error satisfies the builtin error interface
func (e RaspFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaspFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaspFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaspFileValidationError{}

// Validate checks the field values on BaselineFile with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BaselineFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaselineFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BaselineFileMultiError, or
// nil if none found.
func (m *BaselineFile) ValidateAll() error {
	return m.validate(true)
}

func (m *BaselineFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return BaselineFileMultiError(errors)
	}

	return nil
}

// BaselineFileMultiError is an error wrapping multiple validation errors
// returned by BaselineFile.ValidateAll() if the designated constraints aren't met.
type BaselineFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaselineFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaselineFileMultiError) AllErrors() []error { return m }

// BaselineFileValidationError is the validation error returned by
// BaselineFile.Validate if the designated constraints aren't met.
type BaselineFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaselineFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaselineFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaselineFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaselineFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaselineFileValidationError) ErrorName() string { return "BaselineFileValidationError" }

// Error satisfies the builtin error interface
func (e BaselineFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaselineFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaselineFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaselineFileValidationError{}

// Validate checks the field values on LinuxAgentOSInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LinuxAgentOSInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LinuxAgentOSInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LinuxAgentOSInfoMultiError, or nil if none found.
func (m *LinuxAgentOSInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LinuxAgentOSInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DriverVersion

	// no validation rules for AgentVersion

	// no validation rules for LinuxOSInfoLong

	// no validation rules for LinuxOSInfoShort

	if len(errors) > 0 {
		return LinuxAgentOSInfoMultiError(errors)
	}

	return nil
}

// LinuxAgentOSInfoMultiError is an error wrapping multiple validation errors
// returned by LinuxAgentOSInfo.ValidateAll() if the designated constraints
// aren't met.
type LinuxAgentOSInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LinuxAgentOSInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LinuxAgentOSInfoMultiError) AllErrors() []error { return m }

// LinuxAgentOSInfoValidationError is the validation error returned by
// LinuxAgentOSInfo.Validate if the designated constraints aren't met.
type LinuxAgentOSInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LinuxAgentOSInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LinuxAgentOSInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LinuxAgentOSInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LinuxAgentOSInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LinuxAgentOSInfoValidationError) ErrorName() string { return "LinuxAgentOSInfoValidationError" }

// Error satisfies the builtin error interface
func (e LinuxAgentOSInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLinuxAgentOSInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LinuxAgentOSInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LinuxAgentOSInfoValidationError{}

// Validate checks the field values on DriverUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DriverUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DriverUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DriverUpgradeResultMultiError, or nil if none found.
func (m *DriverUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *DriverUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DriverVersion

	// no validation rules for ErrorCode

	// no validation rules for LinuxOSInfoLong

	// no validation rules for LinuxOSInfoShort

	if len(errors) > 0 {
		return DriverUpgradeResultMultiError(errors)
	}

	return nil
}

// DriverUpgradeResultMultiError is an error wrapping multiple validation
// errors returned by DriverUpgradeResult.ValidateAll() if the designated
// constraints aren't met.
type DriverUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DriverUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DriverUpgradeResultMultiError) AllErrors() []error { return m }

// DriverUpgradeResultValidationError is the validation error returned by
// DriverUpgradeResult.Validate if the designated constraints aren't met.
type DriverUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DriverUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DriverUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DriverUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DriverUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DriverUpgradeResultValidationError) ErrorName() string {
	return "DriverUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e DriverUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDriverUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DriverUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DriverUpgradeResultValidationError{}

// Validate checks the field values on HashEngineFileUpgradeResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HashEngineFileUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HashEngineFileUpgradeResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HashEngineFileUpgradeResultMultiError, or nil if none found.
func (m *HashEngineFileUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *HashEngineFileUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return HashEngineFileUpgradeResultMultiError(errors)
	}

	return nil
}

// HashEngineFileUpgradeResultMultiError is an error wrapping multiple
// validation errors returned by HashEngineFileUpgradeResult.ValidateAll() if
// the designated constraints aren't met.
type HashEngineFileUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HashEngineFileUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HashEngineFileUpgradeResultMultiError) AllErrors() []error { return m }

// HashEngineFileUpgradeResultValidationError is the validation error returned
// by HashEngineFileUpgradeResult.Validate if the designated constraints
// aren't met.
type HashEngineFileUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HashEngineFileUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HashEngineFileUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HashEngineFileUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HashEngineFileUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HashEngineFileUpgradeResultValidationError) ErrorName() string {
	return "HashEngineFileUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e HashEngineFileUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHashEngineFileUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HashEngineFileUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HashEngineFileUpgradeResultValidationError{}

// Validate checks the field values on NgavFileUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NgavFileUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NgavFileUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NgavFileUpgradeResultMultiError, or nil if none found.
func (m *NgavFileUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *NgavFileUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return NgavFileUpgradeResultMultiError(errors)
	}

	return nil
}

// NgavFileUpgradeResultMultiError is an error wrapping multiple validation
// errors returned by NgavFileUpgradeResult.ValidateAll() if the designated
// constraints aren't met.
type NgavFileUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NgavFileUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NgavFileUpgradeResultMultiError) AllErrors() []error { return m }

// NgavFileUpgradeResultValidationError is the validation error returned by
// NgavFileUpgradeResult.Validate if the designated constraints aren't met.
type NgavFileUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NgavFileUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NgavFileUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NgavFileUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NgavFileUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NgavFileUpgradeResultValidationError) ErrorName() string {
	return "NgavFileUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e NgavFileUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNgavFileUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NgavFileUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NgavFileUpgradeResultValidationError{}

// Validate checks the field values on RaspFileUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RaspFileUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RaspFileUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RaspFileUpgradeResultMultiError, or nil if none found.
func (m *RaspFileUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *RaspFileUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return RaspFileUpgradeResultMultiError(errors)
	}

	return nil
}

// RaspFileUpgradeResultMultiError is an error wrapping multiple validation
// errors returned by RaspFileUpgradeResult.ValidateAll() if the designated
// constraints aren't met.
type RaspFileUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RaspFileUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RaspFileUpgradeResultMultiError) AllErrors() []error { return m }

// RaspFileUpgradeResultValidationError is the validation error returned by
// RaspFileUpgradeResult.Validate if the designated constraints aren't met.
type RaspFileUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RaspFileUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RaspFileUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RaspFileUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RaspFileUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RaspFileUpgradeResultValidationError) ErrorName() string {
	return "RaspFileUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e RaspFileUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRaspFileUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RaspFileUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RaspFileUpgradeResultValidationError{}

// Validate checks the field values on BaselineFileUpgradeResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BaselineFileUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BaselineFileUpgradeResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BaselineFileUpgradeResultMultiError, or nil if none found.
func (m *BaselineFileUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *BaselineFileUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return BaselineFileUpgradeResultMultiError(errors)
	}

	return nil
}

// BaselineFileUpgradeResultMultiError is an error wrapping multiple validation
// errors returned by BaselineFileUpgradeResult.ValidateAll() if the
// designated constraints aren't met.
type BaselineFileUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BaselineFileUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BaselineFileUpgradeResultMultiError) AllErrors() []error { return m }

// BaselineFileUpgradeResultValidationError is the validation error returned by
// BaselineFileUpgradeResult.Validate if the designated constraints aren't met.
type BaselineFileUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BaselineFileUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BaselineFileUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BaselineFileUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BaselineFileUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BaselineFileUpgradeResultValidationError) ErrorName() string {
	return "BaselineFileUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e BaselineFileUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBaselineFileUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BaselineFileUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BaselineFileUpgradeResultValidationError{}

// Validate checks the field values on AgentEngineLibUpgradeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentEngineLibUpgradeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentEngineLibUpgradeRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentEngineLibUpgradeRequestMultiError, or nil if none found.
func (m *AgentEngineLibUpgradeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentEngineLibUpgradeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.HasUpgradeRequest.(type) {
	case *AgentEngineLibUpgradeRequest_DomainWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDomainWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "DomainWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "DomainWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDomainWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "DomainWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_FileSignComWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSignComWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSignComWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSignComWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSignComWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "FileSignComWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_FileDriverblacklistLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileDriverblacklistLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileDriverblacklistLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileDriverblacklistLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileDriverblacklistLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "FileDriverblacklistLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_AcdrLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAcdrLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "AcdrLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "AcdrLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAcdrLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "AcdrLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_AttackWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAttackWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "AttackWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "AttackWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAttackWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "AttackWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_FileSignBlackLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSignBlackLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSignBlackLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSignBlackLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSignBlackLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "FileSignBlackLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeRequest_FileSlLib:
		if v == nil {
			err := AgentEngineLibUpgradeRequestValidationError{
				field:  "HasUpgradeRequest",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSlLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSlLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeRequestValidationError{
						field:  "FileSlLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSlLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeRequestValidationError{
					field:  "FileSlLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AgentEngineLibUpgradeRequestMultiError(errors)
	}

	return nil
}

// AgentEngineLibUpgradeRequestMultiError is an error wrapping multiple
// validation errors returned by AgentEngineLibUpgradeRequest.ValidateAll() if
// the designated constraints aren't met.
type AgentEngineLibUpgradeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentEngineLibUpgradeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentEngineLibUpgradeRequestMultiError) AllErrors() []error { return m }

// AgentEngineLibUpgradeRequestValidationError is the validation error returned
// by AgentEngineLibUpgradeRequest.Validate if the designated constraints
// aren't met.
type AgentEngineLibUpgradeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentEngineLibUpgradeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentEngineLibUpgradeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentEngineLibUpgradeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentEngineLibUpgradeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentEngineLibUpgradeRequestValidationError) ErrorName() string {
	return "AgentEngineLibUpgradeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AgentEngineLibUpgradeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentEngineLibUpgradeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentEngineLibUpgradeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentEngineLibUpgradeRequestValidationError{}

// Validate checks the field values on AgentEngineLibVersion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentEngineLibVersion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentEngineLibVersion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentEngineLibVersionMultiError, or nil if none found.
func (m *AgentEngineLibVersion) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentEngineLibVersion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	if len(errors) > 0 {
		return AgentEngineLibVersionMultiError(errors)
	}

	return nil
}

// AgentEngineLibVersionMultiError is an error wrapping multiple validation
// errors returned by AgentEngineLibVersion.ValidateAll() if the designated
// constraints aren't met.
type AgentEngineLibVersionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentEngineLibVersionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentEngineLibVersionMultiError) AllErrors() []error { return m }

// AgentEngineLibVersionValidationError is the validation error returned by
// AgentEngineLibVersion.Validate if the designated constraints aren't met.
type AgentEngineLibVersionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentEngineLibVersionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentEngineLibVersionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentEngineLibVersionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentEngineLibVersionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentEngineLibVersionValidationError) ErrorName() string {
	return "AgentEngineLibVersionValidationError"
}

// Error satisfies the builtin error interface
func (e AgentEngineLibVersionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentEngineLibVersion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentEngineLibVersionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentEngineLibVersionValidationError{}

// Validate checks the field values on AgentEngineLibUpgradeResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentEngineLibUpgradeResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentEngineLibUpgradeResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentEngineLibUpgradeResultMultiError, or nil if none found.
func (m *AgentEngineLibUpgradeResult) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentEngineLibUpgradeResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.HasUpgradeResult.(type) {
	case *AgentEngineLibUpgradeResult_DomainWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDomainWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "DomainWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "DomainWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDomainWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "DomainWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_FileSignComWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSignComWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSignComWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSignComWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSignComWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "FileSignComWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_FileDriverblacklistLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileDriverblacklistLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileDriverblacklistLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileDriverblacklistLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileDriverblacklistLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "FileDriverblacklistLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_AcdrLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAcdrLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "AcdrLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "AcdrLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAcdrLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "AcdrLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_AttackWhiteLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAttackWhiteLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "AttackWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "AttackWhiteLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAttackWhiteLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "AttackWhiteLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_FileSignBlackLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSignBlackLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSignBlackLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSignBlackLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSignBlackLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "FileSignBlackLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AgentEngineLibUpgradeResult_FileSlLib:
		if v == nil {
			err := AgentEngineLibUpgradeResultValidationError{
				field:  "HasUpgradeResult",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSlLib()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSlLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AgentEngineLibUpgradeResultValidationError{
						field:  "FileSlLib",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSlLib()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AgentEngineLibUpgradeResultValidationError{
					field:  "FileSlLib",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AgentEngineLibUpgradeResultMultiError(errors)
	}

	return nil
}

// AgentEngineLibUpgradeResultMultiError is an error wrapping multiple
// validation errors returned by AgentEngineLibUpgradeResult.ValidateAll() if
// the designated constraints aren't met.
type AgentEngineLibUpgradeResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentEngineLibUpgradeResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentEngineLibUpgradeResultMultiError) AllErrors() []error { return m }

// AgentEngineLibUpgradeResultValidationError is the validation error returned
// by AgentEngineLibUpgradeResult.Validate if the designated constraints
// aren't met.
type AgentEngineLibUpgradeResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentEngineLibUpgradeResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentEngineLibUpgradeResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentEngineLibUpgradeResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentEngineLibUpgradeResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentEngineLibUpgradeResultValidationError) ErrorName() string {
	return "AgentEngineLibUpgradeResultValidationError"
}

// Error satisfies the builtin error interface
func (e AgentEngineLibUpgradeResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentEngineLibUpgradeResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentEngineLibUpgradeResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentEngineLibUpgradeResultValidationError{}

// Validate checks the field values on AgentEngineLibUpgradeItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentEngineLibUpgradeItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentEngineLibUpgradeItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentEngineLibUpgradeItemMultiError, or nil if none found.
func (m *AgentEngineLibUpgradeItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentEngineLibUpgradeItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Version

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return AgentEngineLibUpgradeItemMultiError(errors)
	}

	return nil
}

// AgentEngineLibUpgradeItemMultiError is an error wrapping multiple validation
// errors returned by AgentEngineLibUpgradeItem.ValidateAll() if the
// designated constraints aren't met.
type AgentEngineLibUpgradeItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentEngineLibUpgradeItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentEngineLibUpgradeItemMultiError) AllErrors() []error { return m }

// AgentEngineLibUpgradeItemValidationError is the validation error returned by
// AgentEngineLibUpgradeItem.Validate if the designated constraints aren't met.
type AgentEngineLibUpgradeItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentEngineLibUpgradeItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentEngineLibUpgradeItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentEngineLibUpgradeItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentEngineLibUpgradeItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentEngineLibUpgradeItemValidationError) ErrorName() string {
	return "AgentEngineLibUpgradeItemValidationError"
}

// Error satisfies the builtin error interface
func (e AgentEngineLibUpgradeItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentEngineLibUpgradeItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentEngineLibUpgradeItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentEngineLibUpgradeItemValidationError{}

// Validate checks the field values on UpgradeNotifyRespone with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeNotifyRespone) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeNotifyRespone with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeNotifyResponeMultiError, or nil if none found.
func (m *UpgradeNotifyRespone) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeNotifyRespone) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Code

	// no validation rules for ErrMsg

	if len(errors) > 0 {
		return UpgradeNotifyResponeMultiError(errors)
	}

	return nil
}

// UpgradeNotifyResponeMultiError is an error wrapping multiple validation
// errors returned by UpgradeNotifyRespone.ValidateAll() if the designated
// constraints aren't met.
type UpgradeNotifyResponeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeNotifyResponeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeNotifyResponeMultiError) AllErrors() []error { return m }

// UpgradeNotifyResponeValidationError is the validation error returned by
// UpgradeNotifyRespone.Validate if the designated constraints aren't met.
type UpgradeNotifyResponeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeNotifyResponeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeNotifyResponeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeNotifyResponeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeNotifyResponeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeNotifyResponeValidationError) ErrorName() string {
	return "UpgradeNotifyResponeValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeNotifyResponeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeNotifyRespone.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeNotifyResponeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeNotifyResponeValidationError{}
