// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/ack.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Ack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmdId Command `protobuf:"varint,1,opt,name=cmd_id,json=cmdId,proto3,enum=agent.Command" json:"cmd_id,omitempty"` // 消息类型
	Msg   string  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`                                      // 您可以打印出来看看
}

func (x *Ack) Reset() {
	*x = Ack{}
	mi := &file_agent_ack_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Ack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ack) ProtoMessage() {}

func (x *Ack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_ack_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ack.ProtoReflect.Descriptor instead.
func (*Ack) Descriptor() ([]byte, []int) {
	return file_agent_ack_proto_rawDescGZIP(), []int{0}
}

func (x *Ack) GetCmdId() Command {
	if x != nil {
		return x.CmdId
	}
	return Command_CMD_UNKNOWN
}

func (x *Ack) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_agent_ack_proto protoreflect.FileDescriptor

var file_agent_ack_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e, 0x0a,
	0x03, 0x41, 0x63, 0x6b, 0x12, 0x25, 0x0a, 0x06, 0x63, 0x6d, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x61, 0x6e, 0x64, 0x52, 0x05, 0x63, 0x6d, 0x64, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x42, 0x2a, 0x5a,
	0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76,
	0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_agent_ack_proto_rawDescOnce sync.Once
	file_agent_ack_proto_rawDescData = file_agent_ack_proto_rawDesc
)

func file_agent_ack_proto_rawDescGZIP() []byte {
	file_agent_ack_proto_rawDescOnce.Do(func() {
		file_agent_ack_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_ack_proto_rawDescData)
	})
	return file_agent_ack_proto_rawDescData
}

var file_agent_ack_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_agent_ack_proto_goTypes = []any{
	(*Ack)(nil),  // 0: agent.Ack
	(Command)(0), // 1: agent.Command
}
var file_agent_ack_proto_depIdxs = []int32{
	1, // 0: agent.Ack.cmd_id:type_name -> agent.Command
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_agent_ack_proto_init() }
func file_agent_ack_proto_init() {
	if File_agent_ack_proto != nil {
		return
	}
	file_agent_command_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_ack_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_ack_proto_goTypes,
		DependencyIndexes: file_agent_ack_proto_depIdxs,
		MessageInfos:      file_agent_ack_proto_msgTypes,
	}.Build()
	File_agent_ack_proto = out.File
	file_agent_ack_proto_rawDesc = nil
	file_agent_ack_proto_goTypes = nil
	file_agent_ack_proto_depIdxs = nil
}
