// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/file_scan_progress.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 上报类型
type ReportProgressType int32

const (
	ReportProgressType_RPT_FILE_UNKNOWN  ReportProgressType = 0
	ReportProgressType_RPT_FILE_THREATEN ReportProgressType = 1 // 文件威胁进度
	ReportProgressType_RPT_FILE_SNAPSHOT ReportProgressType = 2 // 文件快照进度
)

// Enum value maps for ReportProgressType.
var (
	ReportProgressType_name = map[int32]string{
		0: "RPT_FILE_UNKNOWN",
		1: "RPT_FILE_THREATEN",
		2: "RPT_FILE_SNAPSHOT",
	}
	ReportProgressType_value = map[string]int32{
		"RPT_FILE_UNKNOWN":  0,
		"RPT_FILE_THREATEN": 1,
		"RPT_FILE_SNAPSHOT": 2,
	}
)

func (x ReportProgressType) Enum() *ReportProgressType {
	p := new(ReportProgressType)
	*p = x
	return p
}

func (x ReportProgressType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportProgressType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_file_scan_progress_proto_enumTypes[0].Descriptor()
}

func (ReportProgressType) Type() protoreflect.EnumType {
	return &file_agent_file_scan_progress_proto_enumTypes[0]
}

func (x ReportProgressType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportProgressType.Descriptor instead.
func (ReportProgressType) EnumDescriptor() ([]byte, []int) {
	return file_agent_file_scan_progress_proto_rawDescGZIP(), []int{0}
}

type ReportProgressStatus int32

const (
	ReportProgressStatus_STATUS_UNKNOWN   ReportProgressStatus = 0
	ReportProgressStatus_STATUS_START     ReportProgressStatus = 1 // 开始
	ReportProgressStatus_STATUS_SCANNING  ReportProgressStatus = 2 // 扫描中
	ReportProgressStatus_STATUS_UPLOADING ReportProgressStatus = 3 // 上传中
	ReportProgressStatus_STATUS_PENDING   ReportProgressStatus = 4 // 暂停
	ReportProgressStatus_STATUS_DONE      ReportProgressStatus = 5 // 完成
)

// Enum value maps for ReportProgressStatus.
var (
	ReportProgressStatus_name = map[int32]string{
		0: "STATUS_UNKNOWN",
		1: "STATUS_START",
		2: "STATUS_SCANNING",
		3: "STATUS_UPLOADING",
		4: "STATUS_PENDING",
		5: "STATUS_DONE",
	}
	ReportProgressStatus_value = map[string]int32{
		"STATUS_UNKNOWN":   0,
		"STATUS_START":     1,
		"STATUS_SCANNING":  2,
		"STATUS_UPLOADING": 3,
		"STATUS_PENDING":   4,
		"STATUS_DONE":      5,
	}
)

func (x ReportProgressStatus) Enum() *ReportProgressStatus {
	p := new(ReportProgressStatus)
	*p = x
	return p
}

func (x ReportProgressStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReportProgressStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_file_scan_progress_proto_enumTypes[1].Descriptor()
}

func (ReportProgressStatus) Type() protoreflect.EnumType {
	return &file_agent_file_scan_progress_proto_enumTypes[1]
}

func (x ReportProgressStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReportProgressStatus.Descriptor instead.
func (ReportProgressStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_file_scan_progress_proto_rawDescGZIP(), []int{1}
}

type MacProgressStatusReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineID  []byte               `protobuf:"bytes,1,opt,name=MachineID,proto3" json:"MachineID,omitempty"`                                  // 主机ID
	ReportType ReportProgressType   `protobuf:"varint,2,opt,name=reportType,proto3,enum=agent.ReportProgressType" json:"reportType,omitempty"` // 上报进度类型
	Progress   int32                `protobuf:"varint,3,opt,name=Progress,proto3" json:"Progress,omitempty"`                                   // 进度百分比整数部分[0~100]
	Status     ReportProgressStatus `protobuf:"varint,4,opt,name=status,proto3,enum=agent.ReportProgressStatus" json:"status,omitempty"`       // 采集状态
	ReportTime int64                `protobuf:"varint,5,opt,name=ReportTime,proto3" json:"ReportTime,omitempty"`                               // 状态上报时间戳
	IncreaseID int64                `protobuf:"varint,6,opt,name=IncreaseID,proto3" json:"IncreaseID,omitempty"`                               // 增长ID，防并发
	JobID      int64                `protobuf:"varint,7,opt,name=JobID,proto3" json:"JobID,omitempty"`                                         // 标识唯一上报任务
}

func (x *MacProgressStatusReport) Reset() {
	*x = MacProgressStatusReport{}
	mi := &file_agent_file_scan_progress_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MacProgressStatusReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MacProgressStatusReport) ProtoMessage() {}

func (x *MacProgressStatusReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_file_scan_progress_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MacProgressStatusReport.ProtoReflect.Descriptor instead.
func (*MacProgressStatusReport) Descriptor() ([]byte, []int) {
	return file_agent_file_scan_progress_proto_rawDescGZIP(), []int{0}
}

func (x *MacProgressStatusReport) GetMachineID() []byte {
	if x != nil {
		return x.MachineID
	}
	return nil
}

func (x *MacProgressStatusReport) GetReportType() ReportProgressType {
	if x != nil {
		return x.ReportType
	}
	return ReportProgressType_RPT_FILE_UNKNOWN
}

func (x *MacProgressStatusReport) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *MacProgressStatusReport) GetStatus() ReportProgressStatus {
	if x != nil {
		return x.Status
	}
	return ReportProgressStatus_STATUS_UNKNOWN
}

func (x *MacProgressStatusReport) GetReportTime() int64 {
	if x != nil {
		return x.ReportTime
	}
	return 0
}

func (x *MacProgressStatusReport) GetIncreaseID() int64 {
	if x != nil {
		return x.IncreaseID
	}
	return 0
}

func (x *MacProgressStatusReport) GetJobID() int64 {
	if x != nil {
		return x.JobID
	}
	return 0
}

var File_agent_file_scan_progress_proto protoreflect.FileDescriptor

var file_agent_file_scan_progress_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x63, 0x61,
	0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x99, 0x02, 0x0a, 0x17, 0x4d, 0x61, 0x63, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49,
	0x44, 0x12, 0x39, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x33, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x14, 0x0a,
	0x05, 0x4a, 0x6f, 0x62, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x4a, 0x6f,
	0x62, 0x49, 0x44, 0x2a, 0x58, 0x0a, 0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x50, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x15, 0x0a, 0x11, 0x52, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x50, 0x54, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x53, 0x4e, 0x41, 0x50, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x02, 0x2a, 0x8c, 0x01,
	0x0a, 0x14, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x4f, 0x4e, 0x45, 0x10, 0x05, 0x42, 0x2a, 0x5a, 0x28,
	0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30,
	0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_file_scan_progress_proto_rawDescOnce sync.Once
	file_agent_file_scan_progress_proto_rawDescData = file_agent_file_scan_progress_proto_rawDesc
)

func file_agent_file_scan_progress_proto_rawDescGZIP() []byte {
	file_agent_file_scan_progress_proto_rawDescOnce.Do(func() {
		file_agent_file_scan_progress_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_file_scan_progress_proto_rawDescData)
	})
	return file_agent_file_scan_progress_proto_rawDescData
}

var file_agent_file_scan_progress_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_agent_file_scan_progress_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_agent_file_scan_progress_proto_goTypes = []any{
	(ReportProgressType)(0),         // 0: agent.ReportProgressType
	(ReportProgressStatus)(0),       // 1: agent.ReportProgressStatus
	(*MacProgressStatusReport)(nil), // 2: agent.MacProgressStatusReport
}
var file_agent_file_scan_progress_proto_depIdxs = []int32{
	0, // 0: agent.MacProgressStatusReport.reportType:type_name -> agent.ReportProgressType
	1, // 1: agent.MacProgressStatusReport.status:type_name -> agent.ReportProgressStatus
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_agent_file_scan_progress_proto_init() }
func file_agent_file_scan_progress_proto_init() {
	if File_agent_file_scan_progress_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_file_scan_progress_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_file_scan_progress_proto_goTypes,
		DependencyIndexes: file_agent_file_scan_progress_proto_depIdxs,
		EnumInfos:         file_agent_file_scan_progress_proto_enumTypes,
		MessageInfos:      file_agent_file_scan_progress_proto_msgTypes,
	}.Build()
	File_agent_file_scan_progress_proto = out.File
	file_agent_file_scan_progress_proto_rawDesc = nil
	file_agent_file_scan_progress_proto_goTypes = nil
	file_agent_file_scan_progress_proto_depIdxs = nil
}
