// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_proc.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//--------------------------------------------------
//
//  进程风险 识别结果
//  对应 g_CmdMemProtectRiskProcInfo
//--------------------------------------------------
type MemProtectRiskProcInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo                   *ClientID                    `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	ProcHiddenSelfList         []*ProcHiddenSelf            `protobuf:"bytes,2,rep,name=procHiddenSelfList,proto3" json:"procHiddenSelfList,omitempty"`
	ProcHiddenPortList         []*ProcHiddenPort            `protobuf:"bytes,3,rep,name=procHiddenPortList,proto3" json:"procHiddenPortList,omitempty"`
	ProcDangerousList          []*ProcDangerous             `protobuf:"bytes,4,rep,name=procDangerousList,proto3" json:"procDangerousList,omitempty"`
	ProcSensitivityList        []*ProcSensitivity           `protobuf:"bytes,5,rep,name=procSensitivityList,proto3" json:"procSensitivityList,omitempty"`
	ProcBackShellList          []*BackShell                 `protobuf:"bytes,6,rep,name=procBackShellList,proto3" json:"procBackShellList,omitempty"`
	ProcRiskASRList            []*ProcRiskASR               `protobuf:"bytes,7,rep,name=procRiskASRList,proto3" json:"procRiskASRList,omitempty"`
	ProcRiskReuseList          []*ProcReusePort             `protobuf:"bytes,8,rep,name=procRiskReuseList,proto3" json:"procRiskReuseList,omitempty"`
	ProcPuppetList             []*ProcSensitivity           `protobuf:"bytes,9,rep,name=procPuppetList,proto3" json:"procPuppetList,omitempty"` //创建傀儡进程
	ProcRiskOpenPortList       []*ProcRiskOpenPort          `protobuf:"bytes,10,rep,name=procRiskOpenPortList,proto3" json:"procRiskOpenPortList,omitempty"`
	ProcPrivilegeElevationList []*ProcSensitivity           `protobuf:"bytes,11,rep,name=procPrivilegeElevationList,proto3" json:"procPrivilegeElevationList,omitempty"` // 进程提权,subProcessPath可能不存在
	ProcessEscalationToRoot    []*ProcSensitivity           `protobuf:"bytes,12,rep,name=processEscalationToRoot,proto3" json:"processEscalationToRoot,omitempty"`       // linux 进程提权
	TimedTaskEscalationToRoot  []*TimedTaskEscalationToRoot `protobuf:"bytes,13,rep,name=timedTaskEscalationToRoot,proto3" json:"timedTaskEscalationToRoot,omitempty"`   // linux 疑似定时任务提权
	HashAntivirus              []*HashAntivirus             `protobuf:"bytes,14,rep,name=hashAntivirus,proto3" json:"hashAntivirus,omitempty"`                           // 恶意文件哈希检测
	IllegalConnect             []*IllegalConnect            `protobuf:"bytes,15,rep,name=illegalConnect,proto3" json:"illegalConnect,omitempty"`
}

func (x *MemProtectRiskProcInfo) Reset() {
	*x = MemProtectRiskProcInfo{}
	mi := &file_agent_risk_proc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskProcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskProcInfo) ProtoMessage() {}

func (x *MemProtectRiskProcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskProcInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskProcInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskProcInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcHiddenSelfList() []*ProcHiddenSelf {
	if x != nil {
		return x.ProcHiddenSelfList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcHiddenPortList() []*ProcHiddenPort {
	if x != nil {
		return x.ProcHiddenPortList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcDangerousList() []*ProcDangerous {
	if x != nil {
		return x.ProcDangerousList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcSensitivityList() []*ProcSensitivity {
	if x != nil {
		return x.ProcSensitivityList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcBackShellList() []*BackShell {
	if x != nil {
		return x.ProcBackShellList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcRiskASRList() []*ProcRiskASR {
	if x != nil {
		return x.ProcRiskASRList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcRiskReuseList() []*ProcReusePort {
	if x != nil {
		return x.ProcRiskReuseList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcPuppetList() []*ProcSensitivity {
	if x != nil {
		return x.ProcPuppetList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcRiskOpenPortList() []*ProcRiskOpenPort {
	if x != nil {
		return x.ProcRiskOpenPortList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcPrivilegeElevationList() []*ProcSensitivity {
	if x != nil {
		return x.ProcPrivilegeElevationList
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetProcessEscalationToRoot() []*ProcSensitivity {
	if x != nil {
		return x.ProcessEscalationToRoot
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetTimedTaskEscalationToRoot() []*TimedTaskEscalationToRoot {
	if x != nil {
		return x.TimedTaskEscalationToRoot
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetHashAntivirus() []*HashAntivirus {
	if x != nil {
		return x.HashAntivirus
	}
	return nil
}

func (x *MemProtectRiskProcInfo) GetIllegalConnect() []*IllegalConnect {
	if x != nil {
		return x.IllegalConnect
	}
	return nil
}

// 隐身进程
type ProcHiddenSelf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *RiskHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process      *ProcInformation `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	FileSha256   []byte           `protobuf:"bytes,3,opt,name=FileSha256,proto3" json:"FileSha256,omitempty"`      // 文件sha256
	CommandLine  []byte           `protobuf:"bytes,4,opt,name=CommandLine,proto3" json:"CommandLine,omitempty"`    // 进程命令行
	IsX86Process uint32           `protobuf:"varint,5,opt,name=IsX86Process,proto3" json:"IsX86Process,omitempty"` // 是否是32位进程
	ProcessMd5   string           `protobuf:"bytes,6,opt,name=ProcessMd5,proto3" json:"ProcessMd5,omitempty"`
}

func (x *ProcHiddenSelf) Reset() {
	*x = ProcHiddenSelf{}
	mi := &file_agent_risk_proc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcHiddenSelf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcHiddenSelf) ProtoMessage() {}

func (x *ProcHiddenSelf) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcHiddenSelf.ProtoReflect.Descriptor instead.
func (*ProcHiddenSelf) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{1}
}

func (x *ProcHiddenSelf) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcHiddenSelf) GetProcess() *ProcInformation {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ProcHiddenSelf) GetFileSha256() []byte {
	if x != nil {
		return x.FileSha256
	}
	return nil
}

func (x *ProcHiddenSelf) GetCommandLine() []byte {
	if x != nil {
		return x.CommandLine
	}
	return nil
}

func (x *ProcHiddenSelf) GetIsX86Process() uint32 {
	if x != nil {
		return x.IsX86Process
	}
	return 0
}

func (x *ProcHiddenSelf) GetProcessMd5() string {
	if x != nil {
		return x.ProcessMd5
	}
	return ""
}

// 隐藏端口进程
type ProcHiddenPort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process  *ProcessInfo     `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	Port     uint32           `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	Protocol InternetProtocol `protobuf:"varint,4,opt,name=protocol,proto3,enum=agent.InternetProtocol" json:"protocol,omitempty"`
}

func (x *ProcHiddenPort) Reset() {
	*x = ProcHiddenPort{}
	mi := &file_agent_risk_proc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcHiddenPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcHiddenPort) ProtoMessage() {}

func (x *ProcHiddenPort) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcHiddenPort.ProtoReflect.Descriptor instead.
func (*ProcHiddenPort) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{2}
}

func (x *ProcHiddenPort) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcHiddenPort) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ProcHiddenPort) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ProcHiddenPort) GetProtocol() InternetProtocol {
	if x != nil {
		return x.Protocol
	}
	return InternetProtocol_IP_TCP
}

// 风险进程开放端口
type ProcRiskOpenPort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SourceProcess *ProcessInfo `protobuf:"bytes,2,opt,name=SourceProcess,proto3" json:"SourceProcess,omitempty"`
	TargetProcess *ProcessInfo `protobuf:"bytes,3,opt,name=TargetProcess,proto3" json:"TargetProcess,omitempty"`
	LocalIp       string       `protobuf:"bytes,4,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`
	LocalPort     int32        `protobuf:"varint,5,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`
}

func (x *ProcRiskOpenPort) Reset() {
	*x = ProcRiskOpenPort{}
	mi := &file_agent_risk_proc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcRiskOpenPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcRiskOpenPort) ProtoMessage() {}

func (x *ProcRiskOpenPort) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcRiskOpenPort.ProtoReflect.Descriptor instead.
func (*ProcRiskOpenPort) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{3}
}

func (x *ProcRiskOpenPort) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcRiskOpenPort) GetSourceProcess() *ProcessInfo {
	if x != nil {
		return x.SourceProcess
	}
	return nil
}

func (x *ProcRiskOpenPort) GetTargetProcess() *ProcessInfo {
	if x != nil {
		return x.TargetProcess
	}
	return nil
}

func (x *ProcRiskOpenPort) GetLocalIp() string {
	if x != nil {
		return x.LocalIp
	}
	return ""
}

func (x *ProcRiskOpenPort) GetLocalPort() int32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

// 端口复用
type ProcReusePort struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	SourceProcess *ProcessInfo     `protobuf:"bytes,2,opt,name=SourceProcess,proto3" json:"SourceProcess,omitempty"`                    // 复用端口风险进程
	TargetProcess *ProcessInfo     `protobuf:"bytes,3,opt,name=TargetProcess,proto3" json:"TargetProcess,omitempty"`                    // 被复用端口系统进程
	Port          uint32           `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`                                     // 被复用的端口
	Protocol      InternetProtocol `protobuf:"varint,5,opt,name=protocol,proto3,enum=agent.InternetProtocol" json:"protocol,omitempty"` // 端口类型
}

func (x *ProcReusePort) Reset() {
	*x = ProcReusePort{}
	mi := &file_agent_risk_proc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcReusePort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcReusePort) ProtoMessage() {}

func (x *ProcReusePort) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcReusePort.ProtoReflect.Descriptor instead.
func (*ProcReusePort) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{4}
}

func (x *ProcReusePort) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcReusePort) GetSourceProcess() *ProcessInfo {
	if x != nil {
		return x.SourceProcess
	}
	return nil
}

func (x *ProcReusePort) GetTargetProcess() *ProcessInfo {
	if x != nil {
		return x.TargetProcess
	}
	return nil
}

func (x *ProcReusePort) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ProcReusePort) GetProtocol() InternetProtocol {
	if x != nil {
		return x.Protocol
	}
	return InternetProtocol_IP_TCP
}

// 高风险进程，CPU或者IO高负载运行
type ProcDangerous struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *RiskHeader      `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process      *ProcInformation `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	FileSha256   []byte           `protobuf:"bytes,3,opt,name=FileSha256,proto3" json:"FileSha256,omitempty"`      // 文件sha256
	IoLoadAvg    uint64           `protobuf:"varint,4,opt,name=ioLoadAvg,proto3" json:"ioLoadAvg,omitempty"`       // IO负载
	CommandLine  []byte           `protobuf:"bytes,5,opt,name=CommandLine,proto3" json:"CommandLine,omitempty"`    // 进程命令行
	IsX86Process uint32           `protobuf:"varint,6,opt,name=IsX86Process,proto3" json:"IsX86Process,omitempty"` // 是否是32位进程
}

func (x *ProcDangerous) Reset() {
	*x = ProcDangerous{}
	mi := &file_agent_risk_proc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcDangerous) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcDangerous) ProtoMessage() {}

func (x *ProcDangerous) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcDangerous.ProtoReflect.Descriptor instead.
func (*ProcDangerous) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{5}
}

func (x *ProcDangerous) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcDangerous) GetProcess() *ProcInformation {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ProcDangerous) GetFileSha256() []byte {
	if x != nil {
		return x.FileSha256
	}
	return nil
}

func (x *ProcDangerous) GetIoLoadAvg() uint64 {
	if x != nil {
		return x.IoLoadAvg
	}
	return 0
}

func (x *ProcDangerous) GetCommandLine() []byte {
	if x != nil {
		return x.CommandLine
	}
	return nil
}

func (x *ProcDangerous) GetIsX86Process() uint32 {
	if x != nil {
		return x.IsX86Process
	}
	return 0
}

// 启动敏感子进程
type ProcSensitivity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *RiskHeader           `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process                *ProcessInfo          `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	SubProcessPath         []byte                `protobuf:"bytes,3,opt,name=subProcessPath,proto3" json:"subProcessPath,omitempty"`                  // 子进程路径
	SubProcessFileSha256   []byte                `protobuf:"bytes,4,opt,name=subProcessFileSha256,proto3" json:"subProcessFileSha256,omitempty"`      // 子进程文件sha256
	SubProcessCommandLine  []byte                `protobuf:"bytes,5,opt,name=subProcessCommandLine,proto3" json:"subProcessCommandLine,omitempty"`    // 子进程命令行
	ProcessInfoList        []*ProcessInfo        `protobuf:"bytes,20,rep,name=ProcessInfoList,proto3" json:"ProcessInfoList,omitempty"`               // 进程信息
	ProcFlag               string                `protobuf:"bytes,21,opt,name=ProcFlag,proto3" json:"ProcFlag,omitempty"`                             // 进程链标记
	DumpFlag               string                `protobuf:"bytes,22,opt,name=DumpFlag,proto3" json:"DumpFlag,omitempty"`                             // 内存dump标记
	ScriptFlag             string                `protobuf:"bytes,23,opt,name=ScriptFlag,proto3" json:"ScriptFlag,omitempty"`                         // 脚本标记
	EvidenceSize           uint64                `protobuf:"varint,24,opt,name=EvidenceSize,proto3" json:"EvidenceSize,omitempty"`                    // 证据大小
	ReportEvidenceInfoList []*ReportEvidenceInfo `protobuf:"bytes,25,rep,name=ReportEvidenceInfoList,proto3" json:"ReportEvidenceInfoList,omitempty"` //证据信息链
	// Types that are assignable to Has_PuppetProcMessage:
	//
	//	*ProcSensitivity_PuppeteProcMesssage
	Has_PuppetProcMessage isProcSensitivity_Has_PuppetProcMessage `protobuf_oneof:"has_PuppetProcMessage"`
}

func (x *ProcSensitivity) Reset() {
	*x = ProcSensitivity{}
	mi := &file_agent_risk_proc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcSensitivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcSensitivity) ProtoMessage() {}

func (x *ProcSensitivity) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcSensitivity.ProtoReflect.Descriptor instead.
func (*ProcSensitivity) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{6}
}

func (x *ProcSensitivity) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcSensitivity) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ProcSensitivity) GetSubProcessPath() []byte {
	if x != nil {
		return x.SubProcessPath
	}
	return nil
}

func (x *ProcSensitivity) GetSubProcessFileSha256() []byte {
	if x != nil {
		return x.SubProcessFileSha256
	}
	return nil
}

func (x *ProcSensitivity) GetSubProcessCommandLine() []byte {
	if x != nil {
		return x.SubProcessCommandLine
	}
	return nil
}

func (x *ProcSensitivity) GetProcessInfoList() []*ProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *ProcSensitivity) GetProcFlag() string {
	if x != nil {
		return x.ProcFlag
	}
	return ""
}

func (x *ProcSensitivity) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *ProcSensitivity) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *ProcSensitivity) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *ProcSensitivity) GetReportEvidenceInfoList() []*ReportEvidenceInfo {
	if x != nil {
		return x.ReportEvidenceInfoList
	}
	return nil
}

func (m *ProcSensitivity) GetHas_PuppetProcMessage() isProcSensitivity_Has_PuppetProcMessage {
	if m != nil {
		return m.Has_PuppetProcMessage
	}
	return nil
}

func (x *ProcSensitivity) GetPuppeteProcMesssage() *PuppetProcMessage {
	if x, ok := x.GetHas_PuppetProcMessage().(*ProcSensitivity_PuppeteProcMesssage); ok {
		return x.PuppeteProcMesssage
	}
	return nil
}

type isProcSensitivity_Has_PuppetProcMessage interface {
	isProcSensitivity_Has_PuppetProcMessage()
}

type ProcSensitivity_PuppeteProcMesssage struct {
	PuppeteProcMesssage *PuppetProcMessage `protobuf:"bytes,26,opt,name=puppeteProcMesssage,proto3,oneof"`
}

func (*ProcSensitivity_PuppeteProcMesssage) isProcSensitivity_Has_PuppetProcMessage() {}

// 反弹shell（仅Linux）
type BackShell struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process   *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	RemotIP   []byte       `protobuf:"bytes,3,opt,name=RemotIP,proto3" json:"RemotIP,omitempty"`      // 远端IP
	RemotPort uint32       `protobuf:"varint,4,opt,name=RemotPort,proto3" json:"RemotPort,omitempty"` // 远程端口
	Protocol  []byte       `protobuf:"bytes,5,opt,name=Protocol,proto3" json:"Protocol,omitempty"`    // 协议tcp等
}

func (x *BackShell) Reset() {
	*x = BackShell{}
	mi := &file_agent_risk_proc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BackShell) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackShell) ProtoMessage() {}

func (x *BackShell) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackShell.ProtoReflect.Descriptor instead.
func (*BackShell) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{7}
}

func (x *BackShell) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *BackShell) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *BackShell) GetRemotIP() []byte {
	if x != nil {
		return x.RemotIP
	}
	return nil
}

func (x *BackShell) GetRemotPort() uint32 {
	if x != nil {
		return x.RemotPort
	}
	return 0
}

func (x *BackShell) GetProtocol() []byte {
	if x != nil {
		return x.Protocol
	}
	return nil
}

//
// 高风险进程：减少攻击面(ASR Attack Surface Reduction)
// 系统风险->进程风险  中危            默认不拦截
// 攻击特征：发现进程风险：/bin/ps 加载/tmp/test.so
type ProcRiskASR struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process  *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`   // 存在风险的进程
	RiskFile []byte       `protobuf:"bytes,3,opt,name=RiskFile,proto3" json:"RiskFile,omitempty"` // 进程加载的风险执行文件
}

func (x *ProcRiskASR) Reset() {
	*x = ProcRiskASR{}
	mi := &file_agent_risk_proc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcRiskASR) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcRiskASR) ProtoMessage() {}

func (x *ProcRiskASR) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcRiskASR.ProtoReflect.Descriptor instead.
func (*ProcRiskASR) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{8}
}

func (x *ProcRiskASR) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ProcRiskASR) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ProcRiskASR) GetRiskFile() []byte {
	if x != nil {
		return x.RiskFile
	}
	return nil
}

// 疑似定时任务提权
type TimedTaskEscalationToRoot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TaskName string      `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskPath string      `protobuf:"bytes,3,opt,name=task_path,json=taskPath,proto3" json:"task_path,omitempty"`
}

func (x *TimedTaskEscalationToRoot) Reset() {
	*x = TimedTaskEscalationToRoot{}
	mi := &file_agent_risk_proc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimedTaskEscalationToRoot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimedTaskEscalationToRoot) ProtoMessage() {}

func (x *TimedTaskEscalationToRoot) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimedTaskEscalationToRoot.ProtoReflect.Descriptor instead.
func (*TimedTaskEscalationToRoot) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{9}
}

func (x *TimedTaskEscalationToRoot) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *TimedTaskEscalationToRoot) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *TimedTaskEscalationToRoot) GetTaskPath() string {
	if x != nil {
		return x.TaskPath
	}
	return ""
}

// 恶意文件哈希检测
type HashAntivirus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process         *ProcessInfo `protobuf:"bytes,2,opt,name=process,proto3" json:"process,omitempty"`
	VirusDescribeId uint32       `protobuf:"varint,3,opt,name=virus_describe_id,json=virusDescribeId,proto3" json:"virus_describe_id,omitempty"` // 病毒类型id
}

func (x *HashAntivirus) Reset() {
	*x = HashAntivirus{}
	mi := &file_agent_risk_proc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HashAntivirus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashAntivirus) ProtoMessage() {}

func (x *HashAntivirus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashAntivirus.ProtoReflect.Descriptor instead.
func (*HashAntivirus) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{10}
}

func (x *HashAntivirus) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *HashAntivirus) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *HashAntivirus) GetVirusDescribeId() uint32 {
	if x != nil {
		return x.VirusDescribeId
	}
	return 0
}

//非法外联
type IllegalConnect struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process   *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	RemotIP   []byte       `protobuf:"bytes,3,opt,name=RemotIP,proto3" json:"RemotIP,omitempty"`      // 远端IP
	RemotPort uint32       `protobuf:"varint,4,opt,name=RemotPort,proto3" json:"RemotPort,omitempty"` // 远程端口
	Protocol  []byte       `protobuf:"bytes,5,opt,name=Protocol,proto3" json:"Protocol,omitempty"`    // 协议tcp等
}

func (x *IllegalConnect) Reset() {
	*x = IllegalConnect{}
	mi := &file_agent_risk_proc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IllegalConnect) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IllegalConnect) ProtoMessage() {}

func (x *IllegalConnect) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IllegalConnect.ProtoReflect.Descriptor instead.
func (*IllegalConnect) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{11}
}

func (x *IllegalConnect) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *IllegalConnect) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *IllegalConnect) GetRemotIP() []byte {
	if x != nil {
		return x.RemotIP
	}
	return nil
}

func (x *IllegalConnect) GetRemotPort() uint32 {
	if x != nil {
		return x.RemotPort
	}
	return 0
}

func (x *IllegalConnect) GetProtocol() []byte {
	if x != nil {
		return x.Protocol
	}
	return nil
}

// 傀儡进程补充上报信息
type PuppetProcMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleAddr uint64 `protobuf:"varint,1,opt,name=module_addr,json=moduleAddr,proto3" json:"module_addr,omitempty"` // 发现傀儡进程模块地址
	MemStr     string `protobuf:"bytes,2,opt,name=mem_str,json=memStr,proto3" json:"mem_str,omitempty"`              // 检测块包含的字符串
}

func (x *PuppetProcMessage) Reset() {
	*x = PuppetProcMessage{}
	mi := &file_agent_risk_proc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PuppetProcMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PuppetProcMessage) ProtoMessage() {}

func (x *PuppetProcMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_proc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PuppetProcMessage.ProtoReflect.Descriptor instead.
func (*PuppetProcMessage) Descriptor() ([]byte, []int) {
	return file_agent_risk_proc_proto_rawDescGZIP(), []int{12}
}

func (x *PuppetProcMessage) GetModuleAddr() uint64 {
	if x != nil {
		return x.ModuleAddr
	}
	return 0
}

func (x *PuppetProcMessage) GetMemStr() string {
	if x != nil {
		return x.MemStr
	}
	return ""
}

var File_agent_risk_proc_proto protoreflect.FileDescriptor

var file_agent_risk_proc_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x08, 0x0a, 0x16, 0x4d, 0x65, 0x6d, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45,
	0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x53, 0x65, 0x6c, 0x66,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x53, 0x65, 0x6c,
	0x66, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x53, 0x65, 0x6c,
	0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x48, 0x69,
	0x64, 0x64, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x48, 0x69,
	0x64, 0x64, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x11,
	0x70, 0x72, 0x6f, 0x63, 0x44, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x6f, 0x75, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x44, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x6f, 0x75, 0x73, 0x52, 0x11, 0x70,
	0x72, 0x6f, 0x63, 0x44, 0x61, 0x6e, 0x67, 0x65, 0x72, 0x6f, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x48, 0x0a, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x11, 0x70, 0x72,
	0x6f, 0x63, 0x42, 0x61, 0x63, 0x6b, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x42, 0x61, 0x63,
	0x6b, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x0f, 0x70, 0x72,
	0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x53, 0x52, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x52, 0x69, 0x73, 0x6b, 0x41, 0x53, 0x52, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x52, 0x69, 0x73,
	0x6b, 0x41, 0x53, 0x52, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63,
	0x52, 0x69, 0x73, 0x6b, 0x52, 0x65, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x52, 0x65, 0x75, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x52,
	0x69, 0x73, 0x6b, 0x52, 0x65, 0x75, 0x73, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x0e,
	0x70, 0x72, 0x6f, 0x63, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x0e, 0x70, 0x72,
	0x6f, 0x63, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x14,
	0x70, 0x72, 0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x65, 0x6e, 0x50,
	0x6f, 0x72, 0x74, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x65,
	0x6e, 0x50, 0x6f, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x56, 0x0a, 0x1a, 0x70, 0x72, 0x6f,
	0x63, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x1a, 0x70, 0x72, 0x6f, 0x63, 0x50, 0x72, 0x69, 0x76, 0x69,
	0x6c, 0x65, 0x67, 0x65, 0x45, 0x6c, 0x65, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x50, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x73, 0x63, 0x61,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x53,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52,
	0x6f, 0x6f, 0x74, 0x12, 0x5e, 0x0a, 0x19, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52, 0x6f, 0x6f, 0x74,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x6f, 0x52, 0x6f, 0x6f, 0x74, 0x52, 0x19, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x54,
	0x61, 0x73, 0x6b, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52,
	0x6f, 0x6f, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x68, 0x41, 0x6e, 0x74, 0x69, 0x76,
	0x69, 0x72, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x41, 0x6e, 0x74, 0x69, 0x76, 0x69, 0x72, 0x75, 0x73,
	0x52, 0x0d, 0x68, 0x61, 0x73, 0x68, 0x41, 0x6e, 0x74, 0x69, 0x76, 0x69, 0x72, 0x75, 0x73, 0x12,
	0x3d, 0x0a, 0x0e, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x52, 0x0e,
	0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x22, 0xf3,
	0x01, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x53, 0x65, 0x6c,
	0x66, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x07,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1e,
	0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x20,
	0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x49, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4d,
	0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4d, 0x64, 0x35, 0x22, 0xb2, 0x01, 0x0a, 0x0e, 0x50, 0x72, 0x6f, 0x63, 0x48, 0x69, 0x64,
	0x64, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0xeb, 0x01, 0x0a, 0x10, 0x50, 0x72,
	0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x4f, 0x70, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x29,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0d, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x12, 0x38, 0x0a, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xf7, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63,
	0x52, 0x65, 0x75, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x38,
	0x0a, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x22, 0xf0, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x44, 0x61, 0x6e, 0x67, 0x65, 0x72,
	0x6f, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x30,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6f, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x76, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x69, 0x6f, 0x4c, 0x6f, 0x61, 0x64, 0x41, 0x76, 0x67, 0x12, 0x20,
	0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x49, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x22, 0xf0, 0x04, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x53, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x32, 0x0a, 0x14, 0x73, 0x75, 0x62,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x14, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x34, 0x0a,
	0x15, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x73, 0x75,
	0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c,
	0x69, 0x6e, 0x65, 0x12, 0x3c, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x15, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1a, 0x0a,
	0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x44, 0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x51, 0x0a,
	0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x16, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x4c, 0x0a, 0x13, 0x70, 0x75, 0x70, 0x70, 0x65, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x4d,
	0x65, 0x73, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x13, 0x70, 0x75, 0x70, 0x70, 0x65,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x73, 0x61, 0x67, 0x65, 0x42, 0x17,
	0x0a, 0x15, 0x68, 0x61, 0x73, 0x5f, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x09, 0x42, 0x61, 0x63, 0x6b,
	0x53, 0x68, 0x65, 0x6c, 0x6c, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18,
	0x0a, 0x07, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x49, 0x50, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x49, 0x50, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x52, 0x65, 0x6d,
	0x6f, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x22, 0x82, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x63, 0x52, 0x69, 0x73, 0x6b, 0x41,
	0x53, 0x52, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a,
	0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x52,
	0x69, 0x73, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x52,
	0x69, 0x73, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x19, 0x54, 0x69, 0x6d, 0x65,
	0x64, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x52, 0x6f, 0x6f, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x74, 0x68, 0x22, 0x94, 0x01, 0x0a, 0x0d, 0x48,
	0x61, 0x73, 0x68, 0x41, 0x6e, 0x74, 0x69, 0x76, 0x69, 0x72, 0x75, 0x73, 0x12, 0x29, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x49,
	0x64, 0x22, 0xbd, 0x01, 0x0a, 0x0e, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x49, 0x50, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x52, 0x65, 0x6d, 0x6f, 0x74, 0x49, 0x50, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x6d, 0x6f, 0x74,
	0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x22, 0x4d, 0x0a, 0x11, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x5f, 0x73,
	0x74, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x53, 0x74, 0x72,
	0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61,
	0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_proc_proto_rawDescOnce sync.Once
	file_agent_risk_proc_proto_rawDescData = file_agent_risk_proc_proto_rawDesc
)

func file_agent_risk_proc_proto_rawDescGZIP() []byte {
	file_agent_risk_proc_proto_rawDescOnce.Do(func() {
		file_agent_risk_proc_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_proc_proto_rawDescData)
	})
	return file_agent_risk_proc_proto_rawDescData
}

var file_agent_risk_proc_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_agent_risk_proc_proto_goTypes = []any{
	(*MemProtectRiskProcInfo)(nil),    // 0: agent.MemProtectRiskProcInfo
	(*ProcHiddenSelf)(nil),            // 1: agent.ProcHiddenSelf
	(*ProcHiddenPort)(nil),            // 2: agent.ProcHiddenPort
	(*ProcRiskOpenPort)(nil),          // 3: agent.ProcRiskOpenPort
	(*ProcReusePort)(nil),             // 4: agent.ProcReusePort
	(*ProcDangerous)(nil),             // 5: agent.ProcDangerous
	(*ProcSensitivity)(nil),           // 6: agent.ProcSensitivity
	(*BackShell)(nil),                 // 7: agent.BackShell
	(*ProcRiskASR)(nil),               // 8: agent.ProcRiskASR
	(*TimedTaskEscalationToRoot)(nil), // 9: agent.TimedTaskEscalationToRoot
	(*HashAntivirus)(nil),             // 10: agent.HashAntivirus
	(*IllegalConnect)(nil),            // 11: agent.IllegalConnect
	(*PuppetProcMessage)(nil),         // 12: agent.PuppetProcMessage
	(*ClientID)(nil),                  // 13: agent.ClientID
	(*RiskHeader)(nil),                // 14: agent.RiskHeader
	(*ProcInformation)(nil),           // 15: agent.ProcInformation
	(*ProcessInfo)(nil),               // 16: agent.ProcessInfo
	(InternetProtocol)(0),             // 17: agent.InternetProtocol
	(*ReportEvidenceInfo)(nil),        // 18: agent.ReportEvidenceInfo
}
var file_agent_risk_proc_proto_depIdxs = []int32{
	13, // 0: agent.MemProtectRiskProcInfo.baseInfo:type_name -> agent.ClientID
	1,  // 1: agent.MemProtectRiskProcInfo.procHiddenSelfList:type_name -> agent.ProcHiddenSelf
	2,  // 2: agent.MemProtectRiskProcInfo.procHiddenPortList:type_name -> agent.ProcHiddenPort
	5,  // 3: agent.MemProtectRiskProcInfo.procDangerousList:type_name -> agent.ProcDangerous
	6,  // 4: agent.MemProtectRiskProcInfo.procSensitivityList:type_name -> agent.ProcSensitivity
	7,  // 5: agent.MemProtectRiskProcInfo.procBackShellList:type_name -> agent.BackShell
	8,  // 6: agent.MemProtectRiskProcInfo.procRiskASRList:type_name -> agent.ProcRiskASR
	4,  // 7: agent.MemProtectRiskProcInfo.procRiskReuseList:type_name -> agent.ProcReusePort
	6,  // 8: agent.MemProtectRiskProcInfo.procPuppetList:type_name -> agent.ProcSensitivity
	3,  // 9: agent.MemProtectRiskProcInfo.procRiskOpenPortList:type_name -> agent.ProcRiskOpenPort
	6,  // 10: agent.MemProtectRiskProcInfo.procPrivilegeElevationList:type_name -> agent.ProcSensitivity
	6,  // 11: agent.MemProtectRiskProcInfo.processEscalationToRoot:type_name -> agent.ProcSensitivity
	9,  // 12: agent.MemProtectRiskProcInfo.timedTaskEscalationToRoot:type_name -> agent.TimedTaskEscalationToRoot
	10, // 13: agent.MemProtectRiskProcInfo.hashAntivirus:type_name -> agent.HashAntivirus
	11, // 14: agent.MemProtectRiskProcInfo.illegalConnect:type_name -> agent.IllegalConnect
	14, // 15: agent.ProcHiddenSelf.header:type_name -> agent.RiskHeader
	15, // 16: agent.ProcHiddenSelf.Process:type_name -> agent.ProcInformation
	14, // 17: agent.ProcHiddenPort.header:type_name -> agent.RiskHeader
	16, // 18: agent.ProcHiddenPort.Process:type_name -> agent.ProcessInfo
	17, // 19: agent.ProcHiddenPort.protocol:type_name -> agent.InternetProtocol
	14, // 20: agent.ProcRiskOpenPort.header:type_name -> agent.RiskHeader
	16, // 21: agent.ProcRiskOpenPort.SourceProcess:type_name -> agent.ProcessInfo
	16, // 22: agent.ProcRiskOpenPort.TargetProcess:type_name -> agent.ProcessInfo
	14, // 23: agent.ProcReusePort.header:type_name -> agent.RiskHeader
	16, // 24: agent.ProcReusePort.SourceProcess:type_name -> agent.ProcessInfo
	16, // 25: agent.ProcReusePort.TargetProcess:type_name -> agent.ProcessInfo
	17, // 26: agent.ProcReusePort.protocol:type_name -> agent.InternetProtocol
	14, // 27: agent.ProcDangerous.header:type_name -> agent.RiskHeader
	15, // 28: agent.ProcDangerous.Process:type_name -> agent.ProcInformation
	14, // 29: agent.ProcSensitivity.header:type_name -> agent.RiskHeader
	16, // 30: agent.ProcSensitivity.Process:type_name -> agent.ProcessInfo
	16, // 31: agent.ProcSensitivity.ProcessInfoList:type_name -> agent.ProcessInfo
	18, // 32: agent.ProcSensitivity.ReportEvidenceInfoList:type_name -> agent.ReportEvidenceInfo
	12, // 33: agent.ProcSensitivity.puppeteProcMesssage:type_name -> agent.PuppetProcMessage
	14, // 34: agent.BackShell.header:type_name -> agent.RiskHeader
	16, // 35: agent.BackShell.Process:type_name -> agent.ProcessInfo
	14, // 36: agent.ProcRiskASR.header:type_name -> agent.RiskHeader
	16, // 37: agent.ProcRiskASR.Process:type_name -> agent.ProcessInfo
	14, // 38: agent.TimedTaskEscalationToRoot.header:type_name -> agent.RiskHeader
	14, // 39: agent.HashAntivirus.header:type_name -> agent.RiskHeader
	16, // 40: agent.HashAntivirus.process:type_name -> agent.ProcessInfo
	14, // 41: agent.IllegalConnect.header:type_name -> agent.RiskHeader
	16, // 42: agent.IllegalConnect.Process:type_name -> agent.ProcessInfo
	43, // [43:43] is the sub-list for method output_type
	43, // [43:43] is the sub-list for method input_type
	43, // [43:43] is the sub-list for extension type_name
	43, // [43:43] is the sub-list for extension extendee
	0,  // [0:43] is the sub-list for field type_name
}

func init() { file_agent_risk_proc_proto_init() }
func file_agent_risk_proc_proto_init() {
	if File_agent_risk_proc_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_assets_proto_init()
	file_agent_risk_proc_proto_msgTypes[6].OneofWrappers = []any{
		(*ProcSensitivity_PuppeteProcMesssage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_proc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_proc_proto_goTypes,
		DependencyIndexes: file_agent_risk_proc_proto_depIdxs,
		MessageInfos:      file_agent_risk_proc_proto_msgTypes,
	}.Build()
	File_agent_risk_proc_proto = out.File
	file_agent_risk_proc_proto_rawDesc = nil
	file_agent_risk_proc_proto_goTypes = nil
	file_agent_risk_proc_proto_depIdxs = nil
}
