// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/module_status.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectModulesStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectModulesStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectModulesStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectModulesStatusMultiError, or nil if none found.
func (m *MemProtectModulesStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectModulesStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetModuleStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("ModuleStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("ModuleStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectModulesStatusValidationError{
					field:  fmt.Sprintf("ModuleStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAgentStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("AgentStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("AgentStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectModulesStatusValidationError{
					field:  fmt.Sprintf("AgentStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsBlueCountOverLimit

	if all {
		switch v := interface{}(m.GetAgentCrashStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectModulesStatusValidationError{
					field:  "AgentCrashStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectModulesStatusValidationError{
					field:  "AgentCrashStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAgentCrashStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectModulesStatusValidationError{
				field:  "AgentCrashStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Conflict

	// no validation rules for IsAgentResOverLimitRestart

	for idx, item := range m.GetWarningReport() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("WarningReport[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectModulesStatusValidationError{
						field:  fmt.Sprintf("WarningReport[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectModulesStatusValidationError{
					field:  fmt.Sprintf("WarningReport[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectModulesStatusMultiError(errors)
	}

	return nil
}

// MemProtectModulesStatusMultiError is an error wrapping multiple validation
// errors returned by MemProtectModulesStatus.ValidateAll() if the designated
// constraints aren't met.
type MemProtectModulesStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectModulesStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectModulesStatusMultiError) AllErrors() []error { return m }

// MemProtectModulesStatusValidationError is the validation error returned by
// MemProtectModulesStatus.Validate if the designated constraints aren't met.
type MemProtectModulesStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectModulesStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectModulesStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectModulesStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectModulesStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectModulesStatusValidationError) ErrorName() string {
	return "MemProtectModulesStatusValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectModulesStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectModulesStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectModulesStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectModulesStatusValidationError{}

// Validate checks the field values on ModuleStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ModuleStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModuleStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ModuleStatusMultiError, or
// nil if none found.
func (m *ModuleStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ModuleStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleType

	// no validation rules for IsRun

	// no validation rules for StopTime

	// no validation rules for ModuleLoadingStatus

	// no validation rules for ModuleEnableStatus

	if len(errors) > 0 {
		return ModuleStatusMultiError(errors)
	}

	return nil
}

// ModuleStatusMultiError is an error wrapping multiple validation errors
// returned by ModuleStatus.ValidateAll() if the designated constraints aren't met.
type ModuleStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModuleStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModuleStatusMultiError) AllErrors() []error { return m }

// ModuleStatusValidationError is the validation error returned by
// ModuleStatus.Validate if the designated constraints aren't met.
type ModuleStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModuleStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModuleStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModuleStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModuleStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModuleStatusValidationError) ErrorName() string { return "ModuleStatusValidationError" }

// Error satisfies the builtin error interface
func (e ModuleStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModuleStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModuleStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModuleStatusValidationError{}

// Validate checks the field values on AgentStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentStatusMultiError, or
// nil if none found.
func (m *AgentStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for Status

	if len(errors) > 0 {
		return AgentStatusMultiError(errors)
	}

	return nil
}

// AgentStatusMultiError is an error wrapping multiple validation errors
// returned by AgentStatus.ValidateAll() if the designated constraints aren't met.
type AgentStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentStatusMultiError) AllErrors() []error { return m }

// AgentStatusValidationError is the validation error returned by
// AgentStatus.Validate if the designated constraints aren't met.
type AgentStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentStatusValidationError) ErrorName() string { return "AgentStatusValidationError" }

// Error satisfies the builtin error interface
func (e AgentStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentStatusValidationError{}

// Validate checks the field values on AgentCrashStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AgentCrashStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentCrashStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentCrashStatusMultiError, or nil if none found.
func (m *AgentCrashStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentCrashStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StopTime

	if len(errors) > 0 {
		return AgentCrashStatusMultiError(errors)
	}

	return nil
}

// AgentCrashStatusMultiError is an error wrapping multiple validation errors
// returned by AgentCrashStatus.ValidateAll() if the designated constraints
// aren't met.
type AgentCrashStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentCrashStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentCrashStatusMultiError) AllErrors() []error { return m }

// AgentCrashStatusValidationError is the validation error returned by
// AgentCrashStatus.Validate if the designated constraints aren't met.
type AgentCrashStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentCrashStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentCrashStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentCrashStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentCrashStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentCrashStatusValidationError) ErrorName() string { return "AgentCrashStatusValidationError" }

// Error satisfies the builtin error interface
func (e AgentCrashStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentCrashStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentCrashStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentCrashStatusValidationError{}

// Validate checks the field values on InjectionStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InjectionStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectionStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectionStatusMultiError, or nil if none found.
func (m *InjectionStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectionStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessName

	// no validation rules for Status

	// no validation rules for MiddlewareVersion

	// no validation rules for Ppid

	// no validation rules for ProcessApplication

	// no validation rules for ApplicationVersion

	// no validation rules for InjectionUnixTimeMsecFrom1970

	// no validation rules for Pid

	// no validation rules for ErrorNum

	if len(errors) > 0 {
		return InjectionStatusMultiError(errors)
	}

	return nil
}

// InjectionStatusMultiError is an error wrapping multiple validation errors
// returned by InjectionStatus.ValidateAll() if the designated constraints
// aren't met.
type InjectionStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectionStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectionStatusMultiError) AllErrors() []error { return m }

// InjectionStatusValidationError is the validation error returned by
// InjectionStatus.Validate if the designated constraints aren't met.
type InjectionStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectionStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectionStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectionStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectionStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectionStatusValidationError) ErrorName() string { return "InjectionStatusValidationError" }

// Error satisfies the builtin error interface
func (e InjectionStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectionStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectionStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectionStatusValidationError{}

// Validate checks the field values on InjectionStatusList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InjectionStatusList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectionStatusList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectionStatusListMultiError, or nil if none found.
func (m *InjectionStatusList) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectionStatusList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InjectionStatusListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InjectionStatusListValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InjectionStatusListValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcessApplication

	if len(errors) > 0 {
		return InjectionStatusListMultiError(errors)
	}

	return nil
}

// InjectionStatusListMultiError is an error wrapping multiple validation
// errors returned by InjectionStatusList.ValidateAll() if the designated
// constraints aren't met.
type InjectionStatusListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectionStatusListMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectionStatusListMultiError) AllErrors() []error { return m }

// InjectionStatusListValidationError is the validation error returned by
// InjectionStatusList.Validate if the designated constraints aren't met.
type InjectionStatusListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectionStatusListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectionStatusListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectionStatusListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectionStatusListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectionStatusListValidationError) ErrorName() string {
	return "InjectionStatusListValidationError"
}

// Error satisfies the builtin error interface
func (e InjectionStatusListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectionStatusList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectionStatusListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectionStatusListValidationError{}

// Validate checks the field values on InjectionStatusMsgBody with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InjectionStatusMsgBody) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectionStatusMsgBody with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectionStatusMsgBodyMultiError, or nil if none found.
func (m *InjectionStatusMsgBody) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectionStatusMsgBody) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InjectionStatusMsgBodyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InjectionStatusMsgBodyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InjectionStatusMsgBodyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InjectionStatusMsgBodyMultiError(errors)
	}

	return nil
}

// InjectionStatusMsgBodyMultiError is an error wrapping multiple validation
// errors returned by InjectionStatusMsgBody.ValidateAll() if the designated
// constraints aren't met.
type InjectionStatusMsgBodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectionStatusMsgBodyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectionStatusMsgBodyMultiError) AllErrors() []error { return m }

// InjectionStatusMsgBodyValidationError is the validation error returned by
// InjectionStatusMsgBody.Validate if the designated constraints aren't met.
type InjectionStatusMsgBodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectionStatusMsgBodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectionStatusMsgBodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectionStatusMsgBodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectionStatusMsgBodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectionStatusMsgBodyValidationError) ErrorName() string {
	return "InjectionStatusMsgBodyValidationError"
}

// Error satisfies the builtin error interface
func (e InjectionStatusMsgBodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectionStatusMsgBody.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectionStatusMsgBodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectionStatusMsgBodyValidationError{}

// Validate checks the field values on InjectionRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InjectionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectionRequestMultiError, or nil if none found.
func (m *InjectionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessName

	// no validation rules for Pid

	// no validation rules for ProcessApplication

	if len(errors) > 0 {
		return InjectionRequestMultiError(errors)
	}

	return nil
}

// InjectionRequestMultiError is an error wrapping multiple validation errors
// returned by InjectionRequest.ValidateAll() if the designated constraints
// aren't met.
type InjectionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectionRequestMultiError) AllErrors() []error { return m }

// InjectionRequestValidationError is the validation error returned by
// InjectionRequest.Validate if the designated constraints aren't met.
type InjectionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectionRequestValidationError) ErrorName() string { return "InjectionRequestValidationError" }

// Error satisfies the builtin error interface
func (e InjectionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectionRequestValidationError{}

// Validate checks the field values on InjectionResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InjectionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectionResponseMultiError, or nil if none found.
func (m *InjectionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for ProcessName

	// no validation rules for Pid

	// no validation rules for ProcessApplication

	if len(errors) > 0 {
		return InjectionResponseMultiError(errors)
	}

	return nil
}

// InjectionResponseMultiError is an error wrapping multiple validation errors
// returned by InjectionResponse.ValidateAll() if the designated constraints
// aren't met.
type InjectionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectionResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectionResponseMultiError) AllErrors() []error { return m }

// InjectionResponseValidationError is the validation error returned by
// InjectionResponse.Validate if the designated constraints aren't met.
type InjectionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectionResponseValidationError) ErrorName() string {
	return "InjectionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InjectionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectionResponseValidationError{}

// Validate checks the field values on UnInstallRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnInstallRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnInstallRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnInstallRequestMultiError, or nil if none found.
func (m *UnInstallRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnInstallRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ProcessName

	// no validation rules for Pid

	// no validation rules for ProcessApplication

	if len(errors) > 0 {
		return UnInstallRequestMultiError(errors)
	}

	return nil
}

// UnInstallRequestMultiError is an error wrapping multiple validation errors
// returned by UnInstallRequest.ValidateAll() if the designated constraints
// aren't met.
type UnInstallRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnInstallRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnInstallRequestMultiError) AllErrors() []error { return m }

// UnInstallRequestValidationError is the validation error returned by
// UnInstallRequest.Validate if the designated constraints aren't met.
type UnInstallRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnInstallRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnInstallRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnInstallRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnInstallRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnInstallRequestValidationError) ErrorName() string { return "UnInstallRequestValidationError" }

// Error satisfies the builtin error interface
func (e UnInstallRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnInstallRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnInstallRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnInstallRequestValidationError{}

// Validate checks the field values on UnInstallResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnInstallResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnInstallResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnInstallResponseMultiError, or nil if none found.
func (m *UnInstallResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UnInstallResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for ProcessName

	// no validation rules for Pid

	// no validation rules for ProcessApplication

	if len(errors) > 0 {
		return UnInstallResponseMultiError(errors)
	}

	return nil
}

// UnInstallResponseMultiError is an error wrapping multiple validation errors
// returned by UnInstallResponse.ValidateAll() if the designated constraints
// aren't met.
type UnInstallResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnInstallResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnInstallResponseMultiError) AllErrors() []error { return m }

// UnInstallResponseValidationError is the validation error returned by
// UnInstallResponse.Validate if the designated constraints aren't met.
type UnInstallResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnInstallResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnInstallResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnInstallResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnInstallResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnInstallResponseValidationError) ErrorName() string {
	return "UnInstallResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UnInstallResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnInstallResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnInstallResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnInstallResponseValidationError{}

// Validate checks the field values on NgavColletorStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NgavColletorStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NgavColletorStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NgavColletorStatusMultiError, or nil if none found.
func (m *NgavColletorStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *NgavColletorStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CollectorType

	// no validation rules for RunningStatus

	if len(errors) > 0 {
		return NgavColletorStatusMultiError(errors)
	}

	return nil
}

// NgavColletorStatusMultiError is an error wrapping multiple validation errors
// returned by NgavColletorStatus.ValidateAll() if the designated constraints
// aren't met.
type NgavColletorStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NgavColletorStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NgavColletorStatusMultiError) AllErrors() []error { return m }

// NgavColletorStatusValidationError is the validation error returned by
// NgavColletorStatus.Validate if the designated constraints aren't met.
type NgavColletorStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NgavColletorStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NgavColletorStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NgavColletorStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NgavColletorStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NgavColletorStatusValidationError) ErrorName() string {
	return "NgavColletorStatusValidationError"
}

// Error satisfies the builtin error interface
func (e NgavColletorStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNgavColletorStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NgavColletorStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NgavColletorStatusValidationError{}

// Validate checks the field values on NgavColletorStatusReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NgavColletorStatusReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NgavColletorStatusReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NgavColletorStatusReportMultiError, or nil if none found.
func (m *NgavColletorStatusReport) ValidateAll() error {
	return m.validate(true)
}

func (m *NgavColletorStatusReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetReports() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NgavColletorStatusReportValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NgavColletorStatusReportValidationError{
						field:  fmt.Sprintf("Reports[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NgavColletorStatusReportValidationError{
					field:  fmt.Sprintf("Reports[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NgavColletorStatusReportMultiError(errors)
	}

	return nil
}

// NgavColletorStatusReportMultiError is an error wrapping multiple validation
// errors returned by NgavColletorStatusReport.ValidateAll() if the designated
// constraints aren't met.
type NgavColletorStatusReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NgavColletorStatusReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NgavColletorStatusReportMultiError) AllErrors() []error { return m }

// NgavColletorStatusReportValidationError is the validation error returned by
// NgavColletorStatusReport.Validate if the designated constraints aren't met.
type NgavColletorStatusReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NgavColletorStatusReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NgavColletorStatusReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NgavColletorStatusReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NgavColletorStatusReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NgavColletorStatusReportValidationError) ErrorName() string {
	return "NgavColletorStatusReportValidationError"
}

// Error satisfies the builtin error interface
func (e NgavColletorStatusReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNgavColletorStatusReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NgavColletorStatusReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NgavColletorStatusReportValidationError{}

// Validate checks the field values on DriverRunningStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DriverRunningStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DriverRunningStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DriverRunningStatusMultiError, or nil if none found.
func (m *DriverRunningStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DriverRunningStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleReportType

	// no validation rules for Enable

	if len(errors) > 0 {
		return DriverRunningStatusMultiError(errors)
	}

	return nil
}

// DriverRunningStatusMultiError is an error wrapping multiple validation
// errors returned by DriverRunningStatus.ValidateAll() if the designated
// constraints aren't met.
type DriverRunningStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DriverRunningStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DriverRunningStatusMultiError) AllErrors() []error { return m }

// DriverRunningStatusValidationError is the validation error returned by
// DriverRunningStatus.Validate if the designated constraints aren't met.
type DriverRunningStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DriverRunningStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DriverRunningStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DriverRunningStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DriverRunningStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DriverRunningStatusValidationError) ErrorName() string {
	return "DriverRunningStatusValidationError"
}

// Error satisfies the builtin error interface
func (e DriverRunningStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDriverRunningStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DriverRunningStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DriverRunningStatusValidationError{}

// Validate checks the field values on SetDriverStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetDriverStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetDriverStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetDriverStatusMultiError, or nil if none found.
func (m *SetDriverStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *SetDriverStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for SwitchPlugin

	// no validation rules for SwitchDriver

	for idx, item := range m.GetDriverRunningStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetDriverStatusValidationError{
						field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetDriverStatusValidationError{
						field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetDriverStatusValidationError{
					field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetDriverStatusMultiError(errors)
	}

	return nil
}

// SetDriverStatusMultiError is an error wrapping multiple validation errors
// returned by SetDriverStatus.ValidateAll() if the designated constraints
// aren't met.
type SetDriverStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetDriverStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetDriverStatusMultiError) AllErrors() []error { return m }

// SetDriverStatusValidationError is the validation error returned by
// SetDriverStatus.Validate if the designated constraints aren't met.
type SetDriverStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetDriverStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetDriverStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetDriverStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetDriverStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetDriverStatusValidationError) ErrorName() string { return "SetDriverStatusValidationError" }

// Error satisfies the builtin error interface
func (e SetDriverStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetDriverStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetDriverStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetDriverStatusValidationError{}

// Validate checks the field values on ReportDriverStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportDriverStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportDriverStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportDriverStatusMultiError, or nil if none found.
func (m *ReportDriverStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportDriverStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	for idx, item := range m.GetDriverRunningStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReportDriverStatusValidationError{
						field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReportDriverStatusValidationError{
						field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReportDriverStatusValidationError{
					field:  fmt.Sprintf("DriverRunningStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ReportDriverStatusMultiError(errors)
	}

	return nil
}

// ReportDriverStatusMultiError is an error wrapping multiple validation errors
// returned by ReportDriverStatus.ValidateAll() if the designated constraints
// aren't met.
type ReportDriverStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportDriverStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportDriverStatusMultiError) AllErrors() []error { return m }

// ReportDriverStatusValidationError is the validation error returned by
// ReportDriverStatus.Validate if the designated constraints aren't met.
type ReportDriverStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportDriverStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportDriverStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportDriverStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportDriverStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportDriverStatusValidationError) ErrorName() string {
	return "ReportDriverStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ReportDriverStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportDriverStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportDriverStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportDriverStatusValidationError{}

// Validate checks the field values on MemProtectWarningReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectWarningReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectWarningReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectWarningReportMultiError, or nil if none found.
func (m *MemProtectWarningReport) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectWarningReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetWarningReport() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectWarningReportValidationError{
						field:  fmt.Sprintf("WarningReport[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectWarningReportValidationError{
						field:  fmt.Sprintf("WarningReport[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectWarningReportValidationError{
					field:  fmt.Sprintf("WarningReport[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectWarningReportMultiError(errors)
	}

	return nil
}

// MemProtectWarningReportMultiError is an error wrapping multiple validation
// errors returned by MemProtectWarningReport.ValidateAll() if the designated
// constraints aren't met.
type MemProtectWarningReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectWarningReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectWarningReportMultiError) AllErrors() []error { return m }

// MemProtectWarningReportValidationError is the validation error returned by
// MemProtectWarningReport.Validate if the designated constraints aren't met.
type MemProtectWarningReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectWarningReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectWarningReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectWarningReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectWarningReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectWarningReportValidationError) ErrorName() string {
	return "MemProtectWarningReportValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectWarningReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectWarningReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectWarningReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectWarningReportValidationError{}

// Validate checks the field values on TimeOutCheckReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TimeOutCheckReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeOutCheckReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TimeOutCheckReportMultiError, or nil if none found.
func (m *TimeOutCheckReport) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeOutCheckReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModuleName

	// no validation rules for TimeOutTime

	if len(errors) > 0 {
		return TimeOutCheckReportMultiError(errors)
	}

	return nil
}

// TimeOutCheckReportMultiError is an error wrapping multiple validation errors
// returned by TimeOutCheckReport.ValidateAll() if the designated constraints
// aren't met.
type TimeOutCheckReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeOutCheckReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeOutCheckReportMultiError) AllErrors() []error { return m }

// TimeOutCheckReportValidationError is the validation error returned by
// TimeOutCheckReport.Validate if the designated constraints aren't met.
type TimeOutCheckReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeOutCheckReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeOutCheckReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeOutCheckReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeOutCheckReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeOutCheckReportValidationError) ErrorName() string {
	return "TimeOutCheckReportValidationError"
}

// Error satisfies the builtin error interface
func (e TimeOutCheckReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeOutCheckReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeOutCheckReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeOutCheckReportValidationError{}

// Validate checks the field values on ServiceInjectTimeOuthReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ServiceInjectTimeOuthReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ServiceInjectTimeOuthReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ServiceInjectTimeOuthReportMultiError, or nil if none found.
func (m *ServiceInjectTimeOuthReport) ValidateAll() error {
	return m.validate(true)
}

func (m *ServiceInjectTimeOuthReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceName

	if len(errors) > 0 {
		return ServiceInjectTimeOuthReportMultiError(errors)
	}

	return nil
}

// ServiceInjectTimeOuthReportMultiError is an error wrapping multiple
// validation errors returned by ServiceInjectTimeOuthReport.ValidateAll() if
// the designated constraints aren't met.
type ServiceInjectTimeOuthReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ServiceInjectTimeOuthReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ServiceInjectTimeOuthReportMultiError) AllErrors() []error { return m }

// ServiceInjectTimeOuthReportValidationError is the validation error returned
// by ServiceInjectTimeOuthReport.Validate if the designated constraints
// aren't met.
type ServiceInjectTimeOuthReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ServiceInjectTimeOuthReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ServiceInjectTimeOuthReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ServiceInjectTimeOuthReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ServiceInjectTimeOuthReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ServiceInjectTimeOuthReportValidationError) ErrorName() string {
	return "ServiceInjectTimeOuthReportValidationError"
}

// Error satisfies the builtin error interface
func (e ServiceInjectTimeOuthReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sServiceInjectTimeOuthReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ServiceInjectTimeOuthReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ServiceInjectTimeOuthReportValidationError{}

// Validate checks the field values on UnexpectShutdownReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnexpectShutdownReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnexpectShutdownReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnexpectShutdownReportMultiError, or nil if none found.
func (m *UnexpectShutdownReport) ValidateAll() error {
	return m.validate(true)
}

func (m *UnexpectShutdownReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstallAgentTime

	// no validation rules for BeforeNum

	// no validation rules for RecentlyNum

	if len(errors) > 0 {
		return UnexpectShutdownReportMultiError(errors)
	}

	return nil
}

// UnexpectShutdownReportMultiError is an error wrapping multiple validation
// errors returned by UnexpectShutdownReport.ValidateAll() if the designated
// constraints aren't met.
type UnexpectShutdownReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnexpectShutdownReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnexpectShutdownReportMultiError) AllErrors() []error { return m }

// UnexpectShutdownReportValidationError is the validation error returned by
// UnexpectShutdownReport.Validate if the designated constraints aren't met.
type UnexpectShutdownReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnexpectShutdownReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnexpectShutdownReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnexpectShutdownReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnexpectShutdownReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnexpectShutdownReportValidationError) ErrorName() string {
	return "UnexpectShutdownReportValidationError"
}

// Error satisfies the builtin error interface
func (e UnexpectShutdownReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnexpectShutdownReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnexpectShutdownReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnexpectShutdownReportValidationError{}

// Validate checks the field values on InjectCrashReport with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *InjectCrashReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectCrashReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectCrashReportMultiError, or nil if none found.
func (m *InjectCrashReport) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectCrashReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	if len(errors) > 0 {
		return InjectCrashReportMultiError(errors)
	}

	return nil
}

// InjectCrashReportMultiError is an error wrapping multiple validation errors
// returned by InjectCrashReport.ValidateAll() if the designated constraints
// aren't met.
type InjectCrashReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectCrashReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectCrashReportMultiError) AllErrors() []error { return m }

// InjectCrashReportValidationError is the validation error returned by
// InjectCrashReport.Validate if the designated constraints aren't met.
type InjectCrashReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectCrashReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectCrashReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectCrashReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectCrashReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectCrashReportValidationError) ErrorName() string {
	return "InjectCrashReportValidationError"
}

// Error satisfies the builtin error interface
func (e InjectCrashReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectCrashReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectCrashReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectCrashReportValidationError{}

// Validate checks the field values on InjectDllCrashFoundReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InjectDllCrashFoundReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InjectDllCrashFoundReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InjectDllCrashFoundReportMultiError, or nil if none found.
func (m *InjectDllCrashFoundReport) ValidateAll() error {
	return m.validate(true)
}

func (m *InjectDllCrashFoundReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	if len(errors) > 0 {
		return InjectDllCrashFoundReportMultiError(errors)
	}

	return nil
}

// InjectDllCrashFoundReportMultiError is an error wrapping multiple validation
// errors returned by InjectDllCrashFoundReport.ValidateAll() if the
// designated constraints aren't met.
type InjectDllCrashFoundReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InjectDllCrashFoundReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InjectDllCrashFoundReportMultiError) AllErrors() []error { return m }

// InjectDllCrashFoundReportValidationError is the validation error returned by
// InjectDllCrashFoundReport.Validate if the designated constraints aren't met.
type InjectDllCrashFoundReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InjectDllCrashFoundReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InjectDllCrashFoundReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InjectDllCrashFoundReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InjectDllCrashFoundReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InjectDllCrashFoundReportValidationError) ErrorName() string {
	return "InjectDllCrashFoundReportValidationError"
}

// Error satisfies the builtin error interface
func (e InjectDllCrashFoundReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInjectDllCrashFoundReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InjectDllCrashFoundReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InjectDllCrashFoundReportValidationError{}

// Validate checks the field values on ReportSuppressionReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportSuppressionReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSuppressionReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportSuppressionReportMultiError, or nil if none found.
func (m *ReportSuppressionReport) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSuppressionReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Count

	if len(errors) > 0 {
		return ReportSuppressionReportMultiError(errors)
	}

	return nil
}

// ReportSuppressionReportMultiError is an error wrapping multiple validation
// errors returned by ReportSuppressionReport.ValidateAll() if the designated
// constraints aren't met.
type ReportSuppressionReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSuppressionReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSuppressionReportMultiError) AllErrors() []error { return m }

// ReportSuppressionReportValidationError is the validation error returned by
// ReportSuppressionReport.Validate if the designated constraints aren't met.
type ReportSuppressionReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSuppressionReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSuppressionReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSuppressionReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSuppressionReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSuppressionReportValidationError) ErrorName() string {
	return "ReportSuppressionReportValidationError"
}

// Error satisfies the builtin error interface
func (e ReportSuppressionReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSuppressionReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSuppressionReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSuppressionReportValidationError{}

// Validate checks the field values on ResourceOverLimitReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResourceOverLimitReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceOverLimitReport with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceOverLimitReportMultiError, or nil if none found.
func (m *ResourceOverLimitReport) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceOverLimitReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerTime

	if len(errors) > 0 {
		return ResourceOverLimitReportMultiError(errors)
	}

	return nil
}

// ResourceOverLimitReportMultiError is an error wrapping multiple validation
// errors returned by ResourceOverLimitReport.ValidateAll() if the designated
// constraints aren't met.
type ResourceOverLimitReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceOverLimitReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceOverLimitReportMultiError) AllErrors() []error { return m }

// ResourceOverLimitReportValidationError is the validation error returned by
// ResourceOverLimitReport.Validate if the designated constraints aren't met.
type ResourceOverLimitReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceOverLimitReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceOverLimitReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceOverLimitReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceOverLimitReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceOverLimitReportValidationError) ErrorName() string {
	return "ResourceOverLimitReportValidationError"
}

// Error satisfies the builtin error interface
func (e ResourceOverLimitReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceOverLimitReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceOverLimitReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceOverLimitReportValidationError{}

// Validate checks the field values on DriverAbnormalReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DriverAbnormalReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DriverAbnormalReport with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DriverAbnormalReportMultiError, or nil if none found.
func (m *DriverAbnormalReport) ValidateAll() error {
	return m.validate(true)
}

func (m *DriverAbnormalReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if len(errors) > 0 {
		return DriverAbnormalReportMultiError(errors)
	}

	return nil
}

// DriverAbnormalReportMultiError is an error wrapping multiple validation
// errors returned by DriverAbnormalReport.ValidateAll() if the designated
// constraints aren't met.
type DriverAbnormalReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DriverAbnormalReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DriverAbnormalReportMultiError) AllErrors() []error { return m }

// DriverAbnormalReportValidationError is the validation error returned by
// DriverAbnormalReport.Validate if the designated constraints aren't met.
type DriverAbnormalReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DriverAbnormalReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DriverAbnormalReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DriverAbnormalReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DriverAbnormalReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DriverAbnormalReportValidationError) ErrorName() string {
	return "DriverAbnormalReportValidationError"
}

// Error satisfies the builtin error interface
func (e DriverAbnormalReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDriverAbnormalReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DriverAbnormalReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DriverAbnormalReportValidationError{}

// Validate checks the field values on WarningReport with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WarningReport) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WarningReport with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WarningReportMultiError, or
// nil if none found.
func (m *WarningReport) ValidateAll() error {
	return m.validate(true)
}

func (m *WarningReport) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportType

	// no validation rules for TimeStamp

	switch v := m.ReportData.(type) {
	case *WarningReport_TimeOutCheckReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTimeOutCheckReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "TimeOutCheckReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "TimeOutCheckReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTimeOutCheckReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "TimeOutCheckReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_ServiceInjectTimeOuthReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetServiceInjectTimeOuthReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ServiceInjectTimeOuthReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ServiceInjectTimeOuthReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetServiceInjectTimeOuthReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "ServiceInjectTimeOuthReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_UnexpectShutdownReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUnexpectShutdownReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "UnexpectShutdownReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "UnexpectShutdownReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnexpectShutdownReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "UnexpectShutdownReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_InjectCrashReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInjectCrashReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "InjectCrashReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "InjectCrashReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInjectCrashReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "InjectCrashReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_InjectDllCrashFoundReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInjectDllCrashFoundReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "InjectDllCrashFoundReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "InjectDllCrashFoundReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInjectDllCrashFoundReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "InjectDllCrashFoundReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_ReportSuppressionReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReportSuppressionReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ReportSuppressionReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ReportSuppressionReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReportSuppressionReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "ReportSuppressionReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_ResOverLimitReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResOverLimitReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ResOverLimitReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "ResOverLimitReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResOverLimitReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "ResOverLimitReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningReport_DriverAbnormalReport:
		if v == nil {
			err := WarningReportValidationError{
				field:  "ReportData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDriverAbnormalReport()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "DriverAbnormalReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningReportValidationError{
						field:  "DriverAbnormalReport",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDriverAbnormalReport()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningReportValidationError{
					field:  "DriverAbnormalReport",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WarningReportMultiError(errors)
	}

	return nil
}

// WarningReportMultiError is an error wrapping multiple validation errors
// returned by WarningReport.ValidateAll() if the designated constraints
// aren't met.
type WarningReportMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WarningReportMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WarningReportMultiError) AllErrors() []error { return m }

// WarningReportValidationError is the validation error returned by
// WarningReport.Validate if the designated constraints aren't met.
type WarningReportValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WarningReportValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WarningReportValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WarningReportValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WarningReportValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WarningReportValidationError) ErrorName() string { return "WarningReportValidationError" }

// Error satisfies the builtin error interface
func (e WarningReportValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWarningReport.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WarningReportValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WarningReportValidationError{}

// Validate checks the field values on NoticeUploadFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NoticeUploadFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoticeUploadFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoticeUploadFileMultiError, or nil if none found.
func (m *NoticeUploadFile) ValidateAll() error {
	return m.validate(true)
}

func (m *NoticeUploadFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	for idx, item := range m.GetIgnoreWarnings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NoticeUploadFileValidationError{
						field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NoticeUploadFileValidationError{
						field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NoticeUploadFileValidationError{
					field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NoticeUploadFileMultiError(errors)
	}

	return nil
}

// NoticeUploadFileMultiError is an error wrapping multiple validation errors
// returned by NoticeUploadFile.ValidateAll() if the designated constraints
// aren't met.
type NoticeUploadFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoticeUploadFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoticeUploadFileMultiError) AllErrors() []error { return m }

// NoticeUploadFileValidationError is the validation error returned by
// NoticeUploadFile.Validate if the designated constraints aren't met.
type NoticeUploadFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoticeUploadFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoticeUploadFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoticeUploadFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoticeUploadFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoticeUploadFileValidationError) ErrorName() string { return "NoticeUploadFileValidationError" }

// Error satisfies the builtin error interface
func (e NoticeUploadFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoticeUploadFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoticeUploadFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoticeUploadFileValidationError{}

// Validate checks the field values on IgnoreWarning with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IgnoreWarning) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IgnoreWarning with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IgnoreWarningMultiError, or
// nil if none found.
func (m *IgnoreWarning) ValidateAll() error {
	return m.validate(true)
}

func (m *IgnoreWarning) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WarningType

	// no validation rules for TimeStamp

	if len(errors) > 0 {
		return IgnoreWarningMultiError(errors)
	}

	return nil
}

// IgnoreWarningMultiError is an error wrapping multiple validation errors
// returned by IgnoreWarning.ValidateAll() if the designated constraints
// aren't met.
type IgnoreWarningMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IgnoreWarningMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IgnoreWarningMultiError) AllErrors() []error { return m }

// IgnoreWarningValidationError is the validation error returned by
// IgnoreWarning.Validate if the designated constraints aren't met.
type IgnoreWarningValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IgnoreWarningValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IgnoreWarningValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IgnoreWarningValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IgnoreWarningValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IgnoreWarningValidationError) ErrorName() string { return "IgnoreWarningValidationError" }

// Error satisfies the builtin error interface
func (e IgnoreWarningValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIgnoreWarning.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IgnoreWarningValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IgnoreWarningValidationError{}

// Validate checks the field values on CollectLogsType with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CollectLogsType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectLogsType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectLogsTypeMultiError, or nil if none found.
func (m *CollectLogsType) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectLogsType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CollectLogsTypeMultiError(errors)
	}

	return nil
}

// CollectLogsTypeMultiError is an error wrapping multiple validation errors
// returned by CollectLogsType.ValidateAll() if the designated constraints
// aren't met.
type CollectLogsTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectLogsTypeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectLogsTypeMultiError) AllErrors() []error { return m }

// CollectLogsTypeValidationError is the validation error returned by
// CollectLogsType.Validate if the designated constraints aren't met.
type CollectLogsTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectLogsTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectLogsTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectLogsTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectLogsTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectLogsTypeValidationError) ErrorName() string { return "CollectLogsTypeValidationError" }

// Error satisfies the builtin error interface
func (e CollectLogsTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectLogsType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectLogsTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectLogsTypeValidationError{}

// Validate checks the field values on CollectAndUploadLogs with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectAndUploadLogs) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectAndUploadLogs with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectAndUploadLogsMultiError, or nil if none found.
func (m *CollectAndUploadLogs) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectAndUploadLogs) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	for idx, item := range m.GetIgnoreWarnings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectAndUploadLogsValidationError{
						field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectAndUploadLogsValidationError{
						field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectAndUploadLogsValidationError{
					field:  fmt.Sprintf("IgnoreWarnings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UploadUri

	if len(errors) > 0 {
		return CollectAndUploadLogsMultiError(errors)
	}

	return nil
}

// CollectAndUploadLogsMultiError is an error wrapping multiple validation
// errors returned by CollectAndUploadLogs.ValidateAll() if the designated
// constraints aren't met.
type CollectAndUploadLogsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectAndUploadLogsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectAndUploadLogsMultiError) AllErrors() []error { return m }

// CollectAndUploadLogsValidationError is the validation error returned by
// CollectAndUploadLogs.Validate if the designated constraints aren't met.
type CollectAndUploadLogsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectAndUploadLogsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectAndUploadLogsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectAndUploadLogsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectAndUploadLogsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectAndUploadLogsValidationError) ErrorName() string {
	return "CollectAndUploadLogsValidationError"
}

// Error satisfies the builtin error interface
func (e CollectAndUploadLogsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectAndUploadLogs.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectAndUploadLogsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectAndUploadLogsValidationError{}
