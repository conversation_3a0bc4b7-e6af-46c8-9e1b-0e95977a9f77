// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/virus_file.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectVirusFileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectVirusFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectVirusFileInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectVirusFileInfoMultiError, or nil if none found.
func (m *MemProtectVirusFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectVirusFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectVirusFileInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectVirusFileInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectVirusFileInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVirusList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectVirusFileInfoValidationError{
						field:  fmt.Sprintf("VirusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectVirusFileInfoValidationError{
						field:  fmt.Sprintf("VirusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectVirusFileInfoValidationError{
					field:  fmt.Sprintf("VirusList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectVirusFileInfoMultiError(errors)
	}

	return nil
}

// MemProtectVirusFileInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectVirusFileInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectVirusFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectVirusFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectVirusFileInfoMultiError) AllErrors() []error { return m }

// MemProtectVirusFileInfoValidationError is the validation error returned by
// MemProtectVirusFileInfo.Validate if the designated constraints aren't met.
type MemProtectVirusFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectVirusFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectVirusFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectVirusFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectVirusFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectVirusFileInfoValidationError) ErrorName() string {
	return "MemProtectVirusFileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectVirusFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectVirusFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectVirusFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectVirusFileInfoValidationError{}

// Validate checks the field values on ReportSLInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReportSLInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportSLInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReportSLInfoMultiError, or
// nil if none found.
func (m *ReportSLInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportSLInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleName

	// no validation rules for MatchDetails

	if len(errors) > 0 {
		return ReportSLInfoMultiError(errors)
	}

	return nil
}

// ReportSLInfoMultiError is an error wrapping multiple validation errors
// returned by ReportSLInfo.ValidateAll() if the designated constraints aren't met.
type ReportSLInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportSLInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportSLInfoMultiError) AllErrors() []error { return m }

// ReportSLInfoValidationError is the validation error returned by
// ReportSLInfo.Validate if the designated constraints aren't met.
type ReportSLInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportSLInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportSLInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportSLInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportSLInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportSLInfoValidationError) ErrorName() string { return "ReportSLInfoValidationError" }

// Error satisfies the builtin error interface
func (e ReportSLInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportSLInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportSLInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportSLInfoValidationError{}

// Validate checks the field values on VirusSendInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VirusSendInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusSendInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VirusSendInfoMultiError, or
// nil if none found.
func (m *VirusSendInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusSendInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirusSendInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Filepath

	// no validation rules for Sha256

	// no validation rules for RiskType

	// no validation rules for VirusName

	// no validation rules for Md5

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	// no validation rules for StMode

	// no validation rules for FileSize

	// no validation rules for DetectSource

	// no validation rules for DetectKind

	// no validation rules for MalwareType

	// no validation rules for AccessTime

	// no validation rules for ModifyTime

	// no validation rules for CreateTime

	// no validation rules for Filename

	// no validation rules for Sha1

	// no validation rules for FileVersion

	// no validation rules for FileVendor

	for idx, item := range m.GetSignatureInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VirusSendInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VirusSendInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VirusSendInfoValidationError{
					field:  fmt.Sprintf("SignatureInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FileType

	if all {
		switch v := interface{}(m.GetReportMaliciousInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "ReportMaliciousInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "ReportMaliciousInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportMaliciousInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirusSendInfoValidationError{
				field:  "ReportMaliciousInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReportSlInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "ReportSlInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirusSendInfoValidationError{
					field:  "ReportSlInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportSlInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirusSendInfoValidationError{
				field:  "ReportSlInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SlDetail

	// no validation rules for FileSourceList

	// no validation rules for FileSourceSoftware

	// no validation rules for FileScore

	if len(errors) > 0 {
		return VirusSendInfoMultiError(errors)
	}

	return nil
}

// VirusSendInfoMultiError is an error wrapping multiple validation errors
// returned by VirusSendInfo.ValidateAll() if the designated constraints
// aren't met.
type VirusSendInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusSendInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusSendInfoMultiError) AllErrors() []error { return m }

// VirusSendInfoValidationError is the validation error returned by
// VirusSendInfo.Validate if the designated constraints aren't met.
type VirusSendInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusSendInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusSendInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusSendInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusSendInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusSendInfoValidationError) ErrorName() string { return "VirusSendInfoValidationError" }

// Error satisfies the builtin error interface
func (e VirusSendInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusSendInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusSendInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusSendInfoValidationError{}

// Validate checks the field values on VirusRecvInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VirusRecvInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirusRecvInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VirusRecvInfoMultiError, or
// nil if none found.
func (m *VirusRecvInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VirusRecvInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Sha256

	if len(errors) > 0 {
		return VirusRecvInfoMultiError(errors)
	}

	return nil
}

// VirusRecvInfoMultiError is an error wrapping multiple validation errors
// returned by VirusRecvInfo.ValidateAll() if the designated constraints
// aren't met.
type VirusRecvInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirusRecvInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirusRecvInfoMultiError) AllErrors() []error { return m }

// VirusRecvInfoValidationError is the validation error returned by
// VirusRecvInfo.Validate if the designated constraints aren't met.
type VirusRecvInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirusRecvInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirusRecvInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirusRecvInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirusRecvInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirusRecvInfoValidationError) ErrorName() string { return "VirusRecvInfoValidationError" }

// Error satisfies the builtin error interface
func (e VirusRecvInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirusRecvInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirusRecvInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirusRecvInfoValidationError{}

// Validate checks the field values on DdeMessage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DdeMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DdeMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DdeMessageMultiError, or
// nil if none found.
func (m *DdeMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *DdeMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DdeMessageMultiError(errors)
	}

	return nil
}

// DdeMessageMultiError is an error wrapping multiple validation errors
// returned by DdeMessage.ValidateAll() if the designated constraints aren't met.
type DdeMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DdeMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DdeMessageMultiError) AllErrors() []error { return m }

// DdeMessageValidationError is the validation error returned by
// DdeMessage.Validate if the designated constraints aren't met.
type DdeMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DdeMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DdeMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DdeMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DdeMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DdeMessageValidationError) ErrorName() string { return "DdeMessageValidationError" }

// Error satisfies the builtin error interface
func (e DdeMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDdeMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DdeMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DdeMessageValidationError{}

// Validate checks the field values on VbaMessage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VbaMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VbaMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VbaMessageMultiError, or
// nil if none found.
func (m *VbaMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *VbaMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return VbaMessageMultiError(errors)
	}

	return nil
}

// VbaMessageMultiError is an error wrapping multiple validation errors
// returned by VbaMessage.ValidateAll() if the designated constraints aren't met.
type VbaMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VbaMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VbaMessageMultiError) AllErrors() []error { return m }

// VbaMessageValidationError is the validation error returned by
// VbaMessage.Validate if the designated constraints aren't met.
type VbaMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VbaMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VbaMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VbaMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VbaMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VbaMessageValidationError) ErrorName() string { return "VbaMessageValidationError" }

// Error satisfies the builtin error interface
func (e VbaMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVbaMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VbaMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VbaMessageValidationError{}

// Validate checks the field values on LnkMessage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LnkMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LnkMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LnkMessageMultiError, or
// nil if none found.
func (m *LnkMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *LnkMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetPath

	// no validation rules for WorkingDir

	// no validation rules for CmdLine

	// no validation rules for IconPath

	if len(errors) > 0 {
		return LnkMessageMultiError(errors)
	}

	return nil
}

// LnkMessageMultiError is an error wrapping multiple validation errors
// returned by LnkMessage.ValidateAll() if the designated constraints aren't met.
type LnkMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LnkMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LnkMessageMultiError) AllErrors() []error { return m }

// LnkMessageValidationError is the validation error returned by
// LnkMessage.Validate if the designated constraints aren't met.
type LnkMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LnkMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LnkMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LnkMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LnkMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LnkMessageValidationError) ErrorName() string { return "LnkMessageValidationError" }

// Error satisfies the builtin error interface
func (e LnkMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLnkMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LnkMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LnkMessageValidationError{}

// Validate checks the field values on UrlMessage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UrlMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UrlMessage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UrlMessageMultiError, or
// nil if none found.
func (m *UrlMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *UrlMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return UrlMessageMultiError(errors)
	}

	return nil
}

// UrlMessageMultiError is an error wrapping multiple validation errors
// returned by UrlMessage.ValidateAll() if the designated constraints aren't met.
type UrlMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UrlMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UrlMessageMultiError) AllErrors() []error { return m }

// UrlMessageValidationError is the validation error returned by
// UrlMessage.Validate if the designated constraints aren't met.
type UrlMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UrlMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UrlMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UrlMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UrlMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UrlMessageValidationError) ErrorName() string { return "UrlMessageValidationError" }

// Error satisfies the builtin error interface
func (e UrlMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUrlMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UrlMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UrlMessageValidationError{}

// Validate checks the field values on ReportMaliciousInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportMaliciousInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportMaliciousInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportMaliciousInfoMultiError, or nil if none found.
func (m *ReportMaliciousInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportMaliciousInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDdeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "DdeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "DdeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDdeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportMaliciousInfoValidationError{
				field:  "DdeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVbaInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "VbaInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "VbaInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVbaInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportMaliciousInfoValidationError{
				field:  "VbaInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLnkInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "LnkInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "LnkInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLnkInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportMaliciousInfoValidationError{
				field:  "LnkInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUrlInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "UrlInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportMaliciousInfoValidationError{
					field:  "UrlInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUrlInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportMaliciousInfoValidationError{
				field:  "UrlInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReportMaliciousInfoMultiError(errors)
	}

	return nil
}

// ReportMaliciousInfoMultiError is an error wrapping multiple validation
// errors returned by ReportMaliciousInfo.ValidateAll() if the designated
// constraints aren't met.
type ReportMaliciousInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportMaliciousInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportMaliciousInfoMultiError) AllErrors() []error { return m }

// ReportMaliciousInfoValidationError is the validation error returned by
// ReportMaliciousInfo.Validate if the designated constraints aren't met.
type ReportMaliciousInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportMaliciousInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportMaliciousInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportMaliciousInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportMaliciousInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportMaliciousInfoValidationError) ErrorName() string {
	return "ReportMaliciousInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ReportMaliciousInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportMaliciousInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportMaliciousInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportMaliciousInfoValidationError{}
