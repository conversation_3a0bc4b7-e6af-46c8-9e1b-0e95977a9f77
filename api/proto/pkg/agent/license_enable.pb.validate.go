// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/license_enable.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on LicenseEnableRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LicenseEnableRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LicenseEnableRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LicenseEnableRequestMultiError, or nil if none found.
func (m *LicenseEnableRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LicenseEnableRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ip

	// no validation rules for Enable

	// no validation rules for RandomKey

	// no validation rules for ProcductName

	// no validation rules for Groupid

	// no validation rules for ServerAddr

	if len(errors) > 0 {
		return LicenseEnableRequestMultiError(errors)
	}

	return nil
}

// LicenseEnableRequestMultiError is an error wrapping multiple validation
// errors returned by LicenseEnableRequest.ValidateAll() if the designated
// constraints aren't met.
type LicenseEnableRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LicenseEnableRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LicenseEnableRequestMultiError) AllErrors() []error { return m }

// LicenseEnableRequestValidationError is the validation error returned by
// LicenseEnableRequest.Validate if the designated constraints aren't met.
type LicenseEnableRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LicenseEnableRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LicenseEnableRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LicenseEnableRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LicenseEnableRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LicenseEnableRequestValidationError) ErrorName() string {
	return "LicenseEnableRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LicenseEnableRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLicenseEnableRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LicenseEnableRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LicenseEnableRequestValidationError{}

// Validate checks the field values on LicenseEnableResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LicenseEnableResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LicenseEnableResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LicenseEnableResponseMultiError, or nil if none found.
func (m *LicenseEnableResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LicenseEnableResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NeedSave

	// no validation rules for Enable

	// no validation rules for RandomKey

	// no validation rules for ProcductName

	// no validation rules for Machineid

	// no validation rules for GroupId

	if len(errors) > 0 {
		return LicenseEnableResponseMultiError(errors)
	}

	return nil
}

// LicenseEnableResponseMultiError is an error wrapping multiple validation
// errors returned by LicenseEnableResponse.ValidateAll() if the designated
// constraints aren't met.
type LicenseEnableResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LicenseEnableResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LicenseEnableResponseMultiError) AllErrors() []error { return m }

// LicenseEnableResponseValidationError is the validation error returned by
// LicenseEnableResponse.Validate if the designated constraints aren't met.
type LicenseEnableResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LicenseEnableResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LicenseEnableResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LicenseEnableResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LicenseEnableResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LicenseEnableResponseValidationError) ErrorName() string {
	return "LicenseEnableResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LicenseEnableResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLicenseEnableResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LicenseEnableResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LicenseEnableResponseValidationError{}
