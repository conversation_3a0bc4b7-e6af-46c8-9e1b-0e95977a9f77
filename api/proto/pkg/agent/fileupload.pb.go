// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/fileupload.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 文件上传类型
type FileUploadType int32

const (
	FileUploadType_UPLOAD_TYPE_UNKNOWN          FileUploadType = 0
	FileUploadType_UPLOAD_TYPE_ILLEGAL_OUTREACH FileUploadType = 1 // 非法外联请求
	FileUploadType_UPLOAD_TYPE_SNAPSHOT         FileUploadType = 2 // 文件快照采集
)

// Enum value maps for FileUploadType.
var (
	FileUploadType_name = map[int32]string{
		0: "UPLOAD_TYPE_UNKNOWN",
		1: "UPLOAD_TYPE_ILLEGAL_OUTREACH",
		2: "UPLOAD_TYPE_SNAPSHOT",
	}
	FileUploadType_value = map[string]int32{
		"UPLOAD_TYPE_UNKNOWN":          0,
		"UPLOAD_TYPE_ILLEGAL_OUTREACH": 1,
		"UPLOAD_TYPE_SNAPSHOT":         2,
	}
)

func (x FileUploadType) Enum() *FileUploadType {
	p := new(FileUploadType)
	*p = x
	return p
}

func (x FileUploadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileUploadType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_fileupload_proto_enumTypes[0].Descriptor()
}

func (FileUploadType) Type() protoreflect.EnumType {
	return &file_agent_fileupload_proto_enumTypes[0]
}

func (x FileUploadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileUploadType.Descriptor instead.
func (FileUploadType) EnumDescriptor() ([]byte, []int) {
	return file_agent_fileupload_proto_rawDescGZIP(), []int{0}
}

// agent -> server
type FileUploadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadType FileUploadType `protobuf:"varint,1,opt,name=upload_type,json=uploadType,proto3,enum=agent.FileUploadType" json:"upload_type,omitempty"` // 文件上传类型
	Identifier string         `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                              // 标识符
}

func (x *FileUploadReq) Reset() {
	*x = FileUploadReq{}
	mi := &file_agent_fileupload_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadReq) ProtoMessage() {}

func (x *FileUploadReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_fileupload_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadReq.ProtoReflect.Descriptor instead.
func (*FileUploadReq) Descriptor() ([]byte, []int) {
	return file_agent_fileupload_proto_rawDescGZIP(), []int{0}
}

func (x *FileUploadReq) GetUploadType() FileUploadType {
	if x != nil {
		return x.UploadType
	}
	return FileUploadType_UPLOAD_TYPE_UNKNOWN
}

func (x *FileUploadReq) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

// server -> agent
type FileUploadResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadType FileUploadType `protobuf:"varint,1,opt,name=upload_type,json=uploadType,proto3,enum=agent.FileUploadType" json:"upload_type,omitempty"` // 证据文件类型
	Identifier string         `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`                                              // 标识符
	Url        string         `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *FileUploadResp) Reset() {
	*x = FileUploadResp{}
	mi := &file_agent_fileupload_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadResp) ProtoMessage() {}

func (x *FileUploadResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_fileupload_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadResp.ProtoReflect.Descriptor instead.
func (*FileUploadResp) Descriptor() ([]byte, []int) {
	return file_agent_fileupload_proto_rawDescGZIP(), []int{1}
}

func (x *FileUploadResp) GetUploadType() FileUploadType {
	if x != nil {
		return x.UploadType
	}
	return FileUploadType_UPLOAD_TYPE_UNKNOWN
}

func (x *FileUploadResp) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *FileUploadResp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_agent_fileupload_proto protoreflect.FileDescriptor

var file_agent_fileupload_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22,
	0x67, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x36, 0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0b, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x75, 0x72, 0x6c, 0x2a, 0x65, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x20, 0x0a, 0x1c, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4f, 0x55, 0x54, 0x52, 0x45, 0x41, 0x43, 0x48, 0x10,
	0x01, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x4e, 0x41, 0x50, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x02, 0x42, 0x2a, 0x5a, 0x28, 0x67,
	0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31,
	0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_fileupload_proto_rawDescOnce sync.Once
	file_agent_fileupload_proto_rawDescData = file_agent_fileupload_proto_rawDesc
)

func file_agent_fileupload_proto_rawDescGZIP() []byte {
	file_agent_fileupload_proto_rawDescOnce.Do(func() {
		file_agent_fileupload_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_fileupload_proto_rawDescData)
	})
	return file_agent_fileupload_proto_rawDescData
}

var file_agent_fileupload_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_fileupload_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_fileupload_proto_goTypes = []any{
	(FileUploadType)(0),    // 0: agent.FileUploadType
	(*FileUploadReq)(nil),  // 1: agent.FileUploadReq
	(*FileUploadResp)(nil), // 2: agent.FileUploadResp
}
var file_agent_fileupload_proto_depIdxs = []int32{
	0, // 0: agent.FileUploadReq.upload_type:type_name -> agent.FileUploadType
	0, // 1: agent.FileUploadResp.upload_type:type_name -> agent.FileUploadType
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_agent_fileupload_proto_init() }
func file_agent_fileupload_proto_init() {
	if File_agent_fileupload_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_fileupload_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_fileupload_proto_goTypes,
		DependencyIndexes: file_agent_fileupload_proto_depIdxs,
		EnumInfos:         file_agent_fileupload_proto_enumTypes,
		MessageInfos:      file_agent_fileupload_proto_msgTypes,
	}.Build()
	File_agent_fileupload_proto = out.File
	file_agent_fileupload_proto_rawDesc = nil
	file_agent_fileupload_proto_goTypes = nil
	file_agent_fileupload_proto_depIdxs = nil
}
