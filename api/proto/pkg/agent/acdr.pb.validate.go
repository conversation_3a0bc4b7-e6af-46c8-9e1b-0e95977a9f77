// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/acdr.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RuleInfoMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RuleInfoMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleInfoMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RuleInfoMessageMultiError, or nil if none found.
func (m *RuleInfoMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleInfoMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleInfoMessageValidationError{
					field:  "DateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleInfoMessageValidationError{
					field:  "DateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleInfoMessageValidationError{
				field:  "DateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RuleInfoMessageValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RuleInfoMessageValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RuleInfoMessageValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RuleName

	// no validation rules for RiskLevel

	// no validation rules for HandleMode

	// no validation rules for Score

	for idx, item := range m.GetRuleActionList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleInfoMessageValidationError{
						field:  fmt.Sprintf("RuleActionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleInfoMessageValidationError{
						field:  fmt.Sprintf("RuleActionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleInfoMessageValidationError{
					field:  fmt.Sprintf("RuleActionList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleInfoMessageValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleInfoMessageValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleInfoMessageValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NotShowProcessInfo

	if len(errors) > 0 {
		return RuleInfoMessageMultiError(errors)
	}

	return nil
}

// RuleInfoMessageMultiError is an error wrapping multiple validation errors
// returned by RuleInfoMessage.ValidateAll() if the designated constraints
// aren't met.
type RuleInfoMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleInfoMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleInfoMessageMultiError) AllErrors() []error { return m }

// RuleInfoMessageValidationError is the validation error returned by
// RuleInfoMessage.Validate if the designated constraints aren't met.
type RuleInfoMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleInfoMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleInfoMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleInfoMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleInfoMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleInfoMessageValidationError) ErrorName() string { return "RuleInfoMessageValidationError" }

// Error satisfies the builtin error interface
func (e RuleInfoMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleInfoMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleInfoMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleInfoMessageValidationError{}

// Validate checks the field values on NgavProcessInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NgavProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NgavProcessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NgavProcessInfoMultiError, or nil if none found.
func (m *NgavProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NgavProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for Ppid

	// no validation rules for StartTime

	// no validation rules for ExitTime

	// no validation rules for ProcessPath

	// no validation rules for Command

	// no validation rules for IsExist

	// no validation rules for FileMd5

	// no validation rules for User

	// no validation rules for FileAccessPermission

	// no validation rules for FileSize

	// no validation rules for FileMtime

	// no validation rules for FileCtime

	// no validation rules for FileAtime

	// no validation rules for IsKilled

	// no validation rules for PpidStartTime

	if len(errors) > 0 {
		return NgavProcessInfoMultiError(errors)
	}

	return nil
}

// NgavProcessInfoMultiError is an error wrapping multiple validation errors
// returned by NgavProcessInfo.ValidateAll() if the designated constraints
// aren't met.
type NgavProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NgavProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NgavProcessInfoMultiError) AllErrors() []error { return m }

// NgavProcessInfoValidationError is the validation error returned by
// NgavProcessInfo.Validate if the designated constraints aren't met.
type NgavProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NgavProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NgavProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NgavProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NgavProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NgavProcessInfoValidationError) ErrorName() string { return "NgavProcessInfoValidationError" }

// Error satisfies the builtin error interface
func (e NgavProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNgavProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NgavProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NgavProcessInfoValidationError{}

// Validate checks the field values on FileCreateContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileCreateContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileCreateContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileCreateContentInfoMultiError, or nil if none found.
func (m *FileCreateContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileCreateContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FileCreateContentInfoMultiError(errors)
	}

	return nil
}

// FileCreateContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileCreateContentInfo.ValidateAll() if the designated
// constraints aren't met.
type FileCreateContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileCreateContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileCreateContentInfoMultiError) AllErrors() []error { return m }

// FileCreateContentInfoValidationError is the validation error returned by
// FileCreateContentInfo.Validate if the designated constraints aren't met.
type FileCreateContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileCreateContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileCreateContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileCreateContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileCreateContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileCreateContentInfoValidationError) ErrorName() string {
	return "FileCreateContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileCreateContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileCreateContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileCreateContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileCreateContentInfoValidationError{}

// Validate checks the field values on FileOpenContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileOpenContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileOpenContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileOpenContentInfoMultiError, or nil if none found.
func (m *FileOpenContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileOpenContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FileOpenContentInfoMultiError(errors)
	}

	return nil
}

// FileOpenContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileOpenContentInfo.ValidateAll() if the designated
// constraints aren't met.
type FileOpenContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileOpenContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileOpenContentInfoMultiError) AllErrors() []error { return m }

// FileOpenContentInfoValidationError is the validation error returned by
// FileOpenContentInfo.Validate if the designated constraints aren't met.
type FileOpenContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileOpenContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileOpenContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileOpenContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileOpenContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileOpenContentInfoValidationError) ErrorName() string {
	return "FileOpenContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileOpenContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileOpenContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileOpenContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileOpenContentInfoValidationError{}

// Validate checks the field values on FileWriteContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileWriteContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileWriteContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileWriteContentInfoMultiError, or nil if none found.
func (m *FileWriteContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileWriteContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FileWriteContentInfoMultiError(errors)
	}

	return nil
}

// FileWriteContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileWriteContentInfo.ValidateAll() if the designated
// constraints aren't met.
type FileWriteContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileWriteContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileWriteContentInfoMultiError) AllErrors() []error { return m }

// FileWriteContentInfoValidationError is the validation error returned by
// FileWriteContentInfo.Validate if the designated constraints aren't met.
type FileWriteContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileWriteContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileWriteContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileWriteContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileWriteContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileWriteContentInfoValidationError) ErrorName() string {
	return "FileWriteContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileWriteContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileWriteContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileWriteContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileWriteContentInfoValidationError{}

// Validate checks the field values on FileRenameContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileRenameContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileRenameContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileRenameContentInfoMultiError, or nil if none found.
func (m *FileRenameContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileRenameContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePathSrc

	// no validation rules for FilePathDest

	// no validation rules for IsSrcExist

	// no validation rules for IsDestExist

	if len(errors) > 0 {
		return FileRenameContentInfoMultiError(errors)
	}

	return nil
}

// FileRenameContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileRenameContentInfo.ValidateAll() if the designated
// constraints aren't met.
type FileRenameContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileRenameContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileRenameContentInfoMultiError) AllErrors() []error { return m }

// FileRenameContentInfoValidationError is the validation error returned by
// FileRenameContentInfo.Validate if the designated constraints aren't met.
type FileRenameContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileRenameContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileRenameContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileRenameContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileRenameContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileRenameContentInfoValidationError) ErrorName() string {
	return "FileRenameContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileRenameContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileRenameContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileRenameContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileRenameContentInfoValidationError{}

// Validate checks the field values on FileDeleteContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileDeleteContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileDeleteContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileDeleteContentInfoMultiError, or nil if none found.
func (m *FileDeleteContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileDeleteContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FileDeleteContentInfoMultiError(errors)
	}

	return nil
}

// FileDeleteContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileDeleteContentInfo.ValidateAll() if the designated
// constraints aren't met.
type FileDeleteContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileDeleteContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileDeleteContentInfoMultiError) AllErrors() []error { return m }

// FileDeleteContentInfoValidationError is the validation error returned by
// FileDeleteContentInfo.Validate if the designated constraints aren't met.
type FileDeleteContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileDeleteContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileDeleteContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileDeleteContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileDeleteContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileDeleteContentInfoValidationError) ErrorName() string {
	return "FileDeleteContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileDeleteContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileDeleteContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileDeleteContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileDeleteContentInfoValidationError{}

// Validate checks the field values on FileModeChangeContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileModeChangeContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileModeChangeContentInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileModeChangeContentInfoMultiError, or nil if none found.
func (m *FileModeChangeContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileModeChangeContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileModeChangeContentInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileModeChangeContentInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileModeChangeContentInfoValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChangeInfo

	if len(errors) > 0 {
		return FileModeChangeContentInfoMultiError(errors)
	}

	return nil
}

// FileModeChangeContentInfoMultiError is an error wrapping multiple validation
// errors returned by FileModeChangeContentInfo.ValidateAll() if the
// designated constraints aren't met.
type FileModeChangeContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileModeChangeContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileModeChangeContentInfoMultiError) AllErrors() []error { return m }

// FileModeChangeContentInfoValidationError is the validation error returned by
// FileModeChangeContentInfo.Validate if the designated constraints aren't met.
type FileModeChangeContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileModeChangeContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileModeChangeContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileModeChangeContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileModeChangeContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileModeChangeContentInfoValidationError) ErrorName() string {
	return "FileModeChangeContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileModeChangeContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileModeChangeContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileModeChangeContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileModeChangeContentInfoValidationError{}

// Validate checks the field values on FileIoctlImmutableContentInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileIoctlImmutableContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileIoctlImmutableContentInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FileIoctlImmutableContentInfoMultiError, or nil if none found.
func (m *FileIoctlImmutableContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileIoctlImmutableContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FileIoctlImmutableContentInfoMultiError(errors)
	}

	return nil
}

// FileIoctlImmutableContentInfoMultiError is an error wrapping multiple
// validation errors returned by FileIoctlImmutableContentInfo.ValidateAll()
// if the designated constraints aren't met.
type FileIoctlImmutableContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileIoctlImmutableContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileIoctlImmutableContentInfoMultiError) AllErrors() []error { return m }

// FileIoctlImmutableContentInfoValidationError is the validation error
// returned by FileIoctlImmutableContentInfo.Validate if the designated
// constraints aren't met.
type FileIoctlImmutableContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileIoctlImmutableContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileIoctlImmutableContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileIoctlImmutableContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileIoctlImmutableContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileIoctlImmutableContentInfoValidationError) ErrorName() string {
	return "FileIoctlImmutableContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileIoctlImmutableContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileIoctlImmutableContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileIoctlImmutableContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileIoctlImmutableContentInfoValidationError{}

// Validate checks the field values on Mkdir with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Mkdir) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Mkdir with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MkdirMultiError, or nil if none found.
func (m *Mkdir) ValidateAll() error {
	return m.validate(true)
}

func (m *Mkdir) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return MkdirMultiError(errors)
	}

	return nil
}

// MkdirMultiError is an error wrapping multiple validation errors returned by
// Mkdir.ValidateAll() if the designated constraints aren't met.
type MkdirMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MkdirMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MkdirMultiError) AllErrors() []error { return m }

// MkdirValidationError is the validation error returned by Mkdir.Validate if
// the designated constraints aren't met.
type MkdirValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MkdirValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MkdirValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MkdirValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MkdirValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MkdirValidationError) ErrorName() string { return "MkdirValidationError" }

// Error satisfies the builtin error interface
func (e MkdirValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMkdir.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MkdirValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MkdirValidationError{}

// Validate checks the field values on FileSymlink with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileSymlink) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSymlink with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileSymlinkMultiError, or
// nil if none found.
func (m *FileSymlink) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSymlink) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFileInfoSrc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileSymlinkValidationError{
					field:  "FileInfoSrc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileSymlinkValidationError{
					field:  "FileInfoSrc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfoSrc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileSymlinkValidationError{
				field:  "FileInfoSrc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFileInfoDest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileSymlinkValidationError{
					field:  "FileInfoDest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileSymlinkValidationError{
					field:  "FileInfoDest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfoDest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileSymlinkValidationError{
				field:  "FileInfoDest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileSymlinkMultiError(errors)
	}

	return nil
}

// FileSymlinkMultiError is an error wrapping multiple validation errors
// returned by FileSymlink.ValidateAll() if the designated constraints aren't met.
type FileSymlinkMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSymlinkMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSymlinkMultiError) AllErrors() []error { return m }

// FileSymlinkValidationError is the validation error returned by
// FileSymlink.Validate if the designated constraints aren't met.
type FileSymlinkValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSymlinkValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSymlinkValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSymlinkValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSymlinkValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSymlinkValidationError) ErrorName() string { return "FileSymlinkValidationError" }

// Error satisfies the builtin error interface
func (e FileSymlinkValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSymlink.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSymlinkValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSymlinkValidationError{}

// Validate checks the field values on FileSetUid with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileSetUid) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileSetUid with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileSetUidMultiError, or
// nil if none found.
func (m *FileSetUid) ValidateAll() error {
	return m.validate(true)
}

func (m *FileSetUid) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	if len(errors) > 0 {
		return FileSetUidMultiError(errors)
	}

	return nil
}

// FileSetUidMultiError is an error wrapping multiple validation errors
// returned by FileSetUid.ValidateAll() if the designated constraints aren't met.
type FileSetUidMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileSetUidMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileSetUidMultiError) AllErrors() []error { return m }

// FileSetUidValidationError is the validation error returned by
// FileSetUid.Validate if the designated constraints aren't met.
type FileSetUidValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileSetUidValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileSetUidValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileSetUidValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileSetUidValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileSetUidValidationError) ErrorName() string { return "FileSetUidValidationError" }

// Error satisfies the builtin error interface
func (e FileSetUidValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileSetUid.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileSetUidValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileSetUidValidationError{}

// Validate checks the field values on NetContentBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetContentBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetContentBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetContentBaseInfoMultiError, or nil if none found.
func (m *NetContentBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetContentBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	// no validation rules for AddressFamily

	// no validation rules for Protocol

	if len(errors) > 0 {
		return NetContentBaseInfoMultiError(errors)
	}

	return nil
}

// NetContentBaseInfoMultiError is an error wrapping multiple validation errors
// returned by NetContentBaseInfo.ValidateAll() if the designated constraints
// aren't met.
type NetContentBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetContentBaseInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetContentBaseInfoMultiError) AllErrors() []error { return m }

// NetContentBaseInfoValidationError is the validation error returned by
// NetContentBaseInfo.Validate if the designated constraints aren't met.
type NetContentBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetContentBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetContentBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetContentBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetContentBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetContentBaseInfoValidationError) ErrorName() string {
	return "NetContentBaseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetContentBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetContentBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetContentBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetContentBaseInfoValidationError{}

// Validate checks the field values on NetConnectContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetConnectContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetConnectContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetConnectContentInfoMultiError, or nil if none found.
func (m *NetConnectContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetConnectContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetConnectContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetConnectContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetConnectContentInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return NetConnectContentInfoMultiError(errors)
	}

	return nil
}

// NetConnectContentInfoMultiError is an error wrapping multiple validation
// errors returned by NetConnectContentInfo.ValidateAll() if the designated
// constraints aren't met.
type NetConnectContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetConnectContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetConnectContentInfoMultiError) AllErrors() []error { return m }

// NetConnectContentInfoValidationError is the validation error returned by
// NetConnectContentInfo.Validate if the designated constraints aren't met.
type NetConnectContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetConnectContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetConnectContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetConnectContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetConnectContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetConnectContentInfoValidationError) ErrorName() string {
	return "NetConnectContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetConnectContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetConnectContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetConnectContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetConnectContentInfoValidationError{}

// Validate checks the field values on NetAcceptContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetAcceptContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetAcceptContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetAcceptContentInfoMultiError, or nil if none found.
func (m *NetAcceptContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetAcceptContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetAcceptContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetAcceptContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetAcceptContentInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return NetAcceptContentInfoMultiError(errors)
	}

	return nil
}

// NetAcceptContentInfoMultiError is an error wrapping multiple validation
// errors returned by NetAcceptContentInfo.ValidateAll() if the designated
// constraints aren't met.
type NetAcceptContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetAcceptContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetAcceptContentInfoMultiError) AllErrors() []error { return m }

// NetAcceptContentInfoValidationError is the validation error returned by
// NetAcceptContentInfo.Validate if the designated constraints aren't met.
type NetAcceptContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetAcceptContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetAcceptContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetAcceptContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetAcceptContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetAcceptContentInfoValidationError) ErrorName() string {
	return "NetAcceptContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetAcceptContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetAcceptContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetAcceptContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetAcceptContentInfoValidationError{}

// Validate checks the field values on NetListenContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetListenContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetListenContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetListenContentInfoMultiError, or nil if none found.
func (m *NetListenContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetListenContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetListenContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetListenContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetListenContentInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return NetListenContentInfoMultiError(errors)
	}

	return nil
}

// NetListenContentInfoMultiError is an error wrapping multiple validation
// errors returned by NetListenContentInfo.ValidateAll() if the designated
// constraints aren't met.
type NetListenContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetListenContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetListenContentInfoMultiError) AllErrors() []error { return m }

// NetListenContentInfoValidationError is the validation error returned by
// NetListenContentInfo.Validate if the designated constraints aren't met.
type NetListenContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetListenContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetListenContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetListenContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetListenContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetListenContentInfoValidationError) ErrorName() string {
	return "NetListenContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetListenContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetListenContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetListenContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetListenContentInfoValidationError{}

// Validate checks the field values on NetSendContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetSendContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetSendContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetSendContentInfoMultiError, or nil if none found.
func (m *NetSendContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetSendContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetSendContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetSendContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetSendContentInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DirectionIn

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return NetSendContentInfoMultiError(errors)
	}

	return nil
}

// NetSendContentInfoMultiError is an error wrapping multiple validation errors
// returned by NetSendContentInfo.ValidateAll() if the designated constraints
// aren't met.
type NetSendContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetSendContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetSendContentInfoMultiError) AllErrors() []error { return m }

// NetSendContentInfoValidationError is the validation error returned by
// NetSendContentInfo.Validate if the designated constraints aren't met.
type NetSendContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetSendContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetSendContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetSendContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetSendContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetSendContentInfoValidationError) ErrorName() string {
	return "NetSendContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetSendContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetSendContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetSendContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetSendContentInfoValidationError{}

// Validate checks the field values on NetRecvContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetRecvContentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetRecvContentInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetRecvContentInfoMultiError, or nil if none found.
func (m *NetRecvContentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetRecvContentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetRecvContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetRecvContentInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetRecvContentInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DirectionIn

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return NetRecvContentInfoMultiError(errors)
	}

	return nil
}

// NetRecvContentInfoMultiError is an error wrapping multiple validation errors
// returned by NetRecvContentInfo.ValidateAll() if the designated constraints
// aren't met.
type NetRecvContentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetRecvContentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetRecvContentInfoMultiError) AllErrors() []error { return m }

// NetRecvContentInfoValidationError is the validation error returned by
// NetRecvContentInfo.Validate if the designated constraints aren't met.
type NetRecvContentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetRecvContentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetRecvContentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetRecvContentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetRecvContentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetRecvContentInfoValidationError) ErrorName() string {
	return "NetRecvContentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetRecvContentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetRecvContentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetRecvContentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetRecvContentInfoValidationError{}

// Validate checks the field values on FilelessAttackInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FilelessAttackInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FilelessAttackInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FilelessAttackInfoMultiError, or nil if none found.
func (m *FilelessAttackInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FilelessAttackInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return FilelessAttackInfoMultiError(errors)
	}

	return nil
}

// FilelessAttackInfoMultiError is an error wrapping multiple validation errors
// returned by FilelessAttackInfo.ValidateAll() if the designated constraints
// aren't met.
type FilelessAttackInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FilelessAttackInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FilelessAttackInfoMultiError) AllErrors() []error { return m }

// FilelessAttackInfoValidationError is the validation error returned by
// FilelessAttackInfo.Validate if the designated constraints aren't met.
type FilelessAttackInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FilelessAttackInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FilelessAttackInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FilelessAttackInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FilelessAttackInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FilelessAttackInfoValidationError) ErrorName() string {
	return "FilelessAttackInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FilelessAttackInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilelessAttackInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FilelessAttackInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FilelessAttackInfoValidationError{}

// Validate checks the field values on NetSnifferInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NetSnifferInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetSnifferInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NetSnifferInfoMultiError,
// or nil if none found.
func (m *NetSnifferInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetSnifferInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Devname

	if len(errors) > 0 {
		return NetSnifferInfoMultiError(errors)
	}

	return nil
}

// NetSnifferInfoMultiError is an error wrapping multiple validation errors
// returned by NetSnifferInfo.ValidateAll() if the designated constraints
// aren't met.
type NetSnifferInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetSnifferInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetSnifferInfoMultiError) AllErrors() []error { return m }

// NetSnifferInfoValidationError is the validation error returned by
// NetSnifferInfo.Validate if the designated constraints aren't met.
type NetSnifferInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetSnifferInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetSnifferInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetSnifferInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetSnifferInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetSnifferInfoValidationError) ErrorName() string { return "NetSnifferInfoValidationError" }

// Error satisfies the builtin error interface
func (e NetSnifferInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetSnifferInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetSnifferInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetSnifferInfoValidationError{}

// Validate checks the field values on EnvHijackInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EnvHijackInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EnvHijackInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EnvHijackInfoMultiError, or
// nil if none found.
func (m *EnvHijackInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EnvHijackInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EnvName

	if len(errors) > 0 {
		return EnvHijackInfoMultiError(errors)
	}

	return nil
}

// EnvHijackInfoMultiError is an error wrapping multiple validation errors
// returned by EnvHijackInfo.ValidateAll() if the designated constraints
// aren't met.
type EnvHijackInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EnvHijackInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EnvHijackInfoMultiError) AllErrors() []error { return m }

// EnvHijackInfoValidationError is the validation error returned by
// EnvHijackInfo.Validate if the designated constraints aren't met.
type EnvHijackInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EnvHijackInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EnvHijackInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EnvHijackInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EnvHijackInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EnvHijackInfoValidationError) ErrorName() string { return "EnvHijackInfoValidationError" }

// Error satisfies the builtin error interface
func (e EnvHijackInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEnvHijackInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EnvHijackInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EnvHijackInfoValidationError{}

// Validate checks the field values on SelfDeleteInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SelfDeleteInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelfDeleteInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SelfDeleteInfoMultiError,
// or nil if none found.
func (m *SelfDeleteInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SelfDeleteInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return SelfDeleteInfoMultiError(errors)
	}

	return nil
}

// SelfDeleteInfoMultiError is an error wrapping multiple validation errors
// returned by SelfDeleteInfo.ValidateAll() if the designated constraints
// aren't met.
type SelfDeleteInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelfDeleteInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelfDeleteInfoMultiError) AllErrors() []error { return m }

// SelfDeleteInfoValidationError is the validation error returned by
// SelfDeleteInfo.Validate if the designated constraints aren't met.
type SelfDeleteInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelfDeleteInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelfDeleteInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelfDeleteInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelfDeleteInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelfDeleteInfoValidationError) ErrorName() string { return "SelfDeleteInfoValidationError" }

// Error satisfies the builtin error interface
func (e SelfDeleteInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelfDeleteInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelfDeleteInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelfDeleteInfoValidationError{}

// Validate checks the field values on PtraceInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PtraceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PtraceInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PtraceInfoMultiError, or
// nil if none found.
func (m *PtraceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PtraceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TracerPid

	// no validation rules for TracerStartTime

	// no validation rules for TraceePid

	// no validation rules for TraceeStartTime

	// no validation rules for TraceePath

	// no validation rules for Mode

	// no validation rules for Addr

	if len(errors) > 0 {
		return PtraceInfoMultiError(errors)
	}

	return nil
}

// PtraceInfoMultiError is an error wrapping multiple validation errors
// returned by PtraceInfo.ValidateAll() if the designated constraints aren't met.
type PtraceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PtraceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PtraceInfoMultiError) AllErrors() []error { return m }

// PtraceInfoValidationError is the validation error returned by
// PtraceInfo.Validate if the designated constraints aren't met.
type PtraceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PtraceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PtraceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PtraceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PtraceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PtraceInfoValidationError) ErrorName() string { return "PtraceInfoValidationError" }

// Error satisfies the builtin error interface
func (e PtraceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPtraceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PtraceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PtraceInfoValidationError{}

// Validate checks the field values on FileLinkInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileLinkInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileLinkInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileLinkInfoMultiError, or
// nil if none found.
func (m *FileLinkInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileLinkInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePathSrc

	// no validation rules for FilePathDest

	// no validation rules for IsSrcExist

	// no validation rules for IsDestExist

	if len(errors) > 0 {
		return FileLinkInfoMultiError(errors)
	}

	return nil
}

// FileLinkInfoMultiError is an error wrapping multiple validation errors
// returned by FileLinkInfo.ValidateAll() if the designated constraints aren't met.
type FileLinkInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileLinkInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileLinkInfoMultiError) AllErrors() []error { return m }

// FileLinkInfoValidationError is the validation error returned by
// FileLinkInfo.Validate if the designated constraints aren't met.
type FileLinkInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileLinkInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileLinkInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileLinkInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileLinkInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileLinkInfoValidationError) ErrorName() string { return "FileLinkInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileLinkInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileLinkInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileLinkInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileLinkInfoValidationError{}

// Validate checks the field values on CallUsermodehelperInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CallUsermodehelperInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallUsermodehelperInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CallUsermodehelperInfoMultiError, or nil if none found.
func (m *CallUsermodehelperInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CallUsermodehelperInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	if len(errors) > 0 {
		return CallUsermodehelperInfoMultiError(errors)
	}

	return nil
}

// CallUsermodehelperInfoMultiError is an error wrapping multiple validation
// errors returned by CallUsermodehelperInfo.ValidateAll() if the designated
// constraints aren't met.
type CallUsermodehelperInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallUsermodehelperInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallUsermodehelperInfoMultiError) AllErrors() []error { return m }

// CallUsermodehelperInfoValidationError is the validation error returned by
// CallUsermodehelperInfo.Validate if the designated constraints aren't met.
type CallUsermodehelperInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallUsermodehelperInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallUsermodehelperInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallUsermodehelperInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallUsermodehelperInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallUsermodehelperInfoValidationError) ErrorName() string {
	return "CallUsermodehelperInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CallUsermodehelperInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallUsermodehelperInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallUsermodehelperInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallUsermodehelperInfoValidationError{}

// Validate checks the field values on ReverseShellInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReverseShellInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReverseShellInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReverseShellInfoMultiError, or nil if none found.
func (m *ReverseShellInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ReverseShellInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InPid

	// no validation rules for InIp

	// no validation rules for InPort

	// no validation rules for InAddrFamily

	// no validation rules for InProto

	// no validation rules for InStartTime

	// no validation rules for OutPid

	// no validation rules for OutIp

	// no validation rules for OutPort

	// no validation rules for OutAddrFamily

	// no validation rules for OutProto

	// no validation rules for OutStartTime

	// no validation rules for IsNetworkConnected

	if len(errors) > 0 {
		return ReverseShellInfoMultiError(errors)
	}

	return nil
}

// ReverseShellInfoMultiError is an error wrapping multiple validation errors
// returned by ReverseShellInfo.ValidateAll() if the designated constraints
// aren't met.
type ReverseShellInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReverseShellInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReverseShellInfoMultiError) AllErrors() []error { return m }

// ReverseShellInfoValidationError is the validation error returned by
// ReverseShellInfo.Validate if the designated constraints aren't met.
type ReverseShellInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReverseShellInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReverseShellInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReverseShellInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReverseShellInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReverseShellInfoValidationError) ErrorName() string { return "ReverseShellInfoValidationError" }

// Error satisfies the builtin error interface
func (e ReverseShellInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReverseShellInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReverseShellInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReverseShellInfoValidationError{}

// Validate checks the field values on ProcessExecInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProcessExecInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessExecInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProcessExecInfoMultiError, or nil if none found.
func (m *ProcessExecInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessExecInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Exepath

	if len(errors) > 0 {
		return ProcessExecInfoMultiError(errors)
	}

	return nil
}

// ProcessExecInfoMultiError is an error wrapping multiple validation errors
// returned by ProcessExecInfo.ValidateAll() if the designated constraints
// aren't met.
type ProcessExecInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessExecInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessExecInfoMultiError) AllErrors() []error { return m }

// ProcessExecInfoValidationError is the validation error returned by
// ProcessExecInfo.Validate if the designated constraints aren't met.
type ProcessExecInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessExecInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessExecInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessExecInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessExecInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessExecInfoValidationError) ErrorName() string { return "ProcessExecInfoValidationError" }

// Error satisfies the builtin error interface
func (e ProcessExecInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessExecInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessExecInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessExecInfoValidationError{}

// Validate checks the field values on PrivilegeEscalation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrivilegeEscalation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrivilegeEscalation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrivilegeEscalationMultiError, or nil if none found.
func (m *PrivilegeEscalation) ValidateAll() error {
	return m.validate(true)
}

func (m *PrivilegeEscalation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrivilegePath

	// no validation rules for PrivilegeCmdline

	if len(errors) > 0 {
		return PrivilegeEscalationMultiError(errors)
	}

	return nil
}

// PrivilegeEscalationMultiError is an error wrapping multiple validation
// errors returned by PrivilegeEscalation.ValidateAll() if the designated
// constraints aren't met.
type PrivilegeEscalationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrivilegeEscalationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrivilegeEscalationMultiError) AllErrors() []error { return m }

// PrivilegeEscalationValidationError is the validation error returned by
// PrivilegeEscalation.Validate if the designated constraints aren't met.
type PrivilegeEscalationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrivilegeEscalationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrivilegeEscalationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrivilegeEscalationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrivilegeEscalationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrivilegeEscalationValidationError) ErrorName() string {
	return "PrivilegeEscalationValidationError"
}

// Error satisfies the builtin error interface
func (e PrivilegeEscalationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrivilegeEscalation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrivilegeEscalationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrivilegeEscalationValidationError{}

// Validate checks the field values on RestoreUtime with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RestoreUtime) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RestoreUtime with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RestoreUtimeMultiError, or
// nil if none found.
func (m *RestoreUtime) ValidateAll() error {
	return m.validate(true)
}

func (m *RestoreUtime) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return RestoreUtimeMultiError(errors)
	}

	return nil
}

// RestoreUtimeMultiError is an error wrapping multiple validation errors
// returned by RestoreUtime.ValidateAll() if the designated constraints aren't met.
type RestoreUtimeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RestoreUtimeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RestoreUtimeMultiError) AllErrors() []error { return m }

// RestoreUtimeValidationError is the validation error returned by
// RestoreUtime.Validate if the designated constraints aren't met.
type RestoreUtimeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RestoreUtimeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RestoreUtimeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RestoreUtimeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RestoreUtimeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RestoreUtimeValidationError) ErrorName() string { return "RestoreUtimeValidationError" }

// Error satisfies the builtin error interface
func (e RestoreUtimeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRestoreUtime.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RestoreUtimeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RestoreUtimeValidationError{}

// Validate checks the field values on Bpf with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Bpf) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bpf with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BpfMultiError, or nil if none found.
func (m *Bpf) ValidateAll() error {
	return m.validate(true)
}

func (m *Bpf) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TypeName

	if len(errors) > 0 {
		return BpfMultiError(errors)
	}

	return nil
}

// BpfMultiError is an error wrapping multiple validation errors returned by
// Bpf.ValidateAll() if the designated constraints aren't met.
type BpfMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BpfMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BpfMultiError) AllErrors() []error { return m }

// BpfValidationError is the validation error returned by Bpf.Validate if the
// designated constraints aren't met.
type BpfValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BpfValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BpfValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BpfValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BpfValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BpfValidationError) ErrorName() string { return "BpfValidationError" }

// Error satisfies the builtin error interface
func (e BpfValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBpf.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BpfValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BpfValidationError{}

// Validate checks the field values on FakeExeFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FakeExeFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FakeExeFileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FakeExeFileInfoMultiError, or nil if none found.
func (m *FakeExeFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FakeExeFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FakePid

	// no validation rules for StartTime

	// no validation rules for FakeExepath

	// no validation rules for Exepath

	// no validation rules for IsExeExist

	if len(errors) > 0 {
		return FakeExeFileInfoMultiError(errors)
	}

	return nil
}

// FakeExeFileInfoMultiError is an error wrapping multiple validation errors
// returned by FakeExeFileInfo.ValidateAll() if the designated constraints
// aren't met.
type FakeExeFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FakeExeFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FakeExeFileInfoMultiError) AllErrors() []error { return m }

// FakeExeFileInfoValidationError is the validation error returned by
// FakeExeFileInfo.Validate if the designated constraints aren't met.
type FakeExeFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FakeExeFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FakeExeFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FakeExeFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FakeExeFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FakeExeFileInfoValidationError) ErrorName() string { return "FakeExeFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e FakeExeFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFakeExeFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FakeExeFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FakeExeFileInfoValidationError{}

// Validate checks the field values on HideModuleInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HideModuleInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HideModuleInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HideModuleInfoMultiError,
// or nil if none found.
func (m *HideModuleInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *HideModuleInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Modname

	if len(errors) > 0 {
		return HideModuleInfoMultiError(errors)
	}

	return nil
}

// HideModuleInfoMultiError is an error wrapping multiple validation errors
// returned by HideModuleInfo.ValidateAll() if the designated constraints
// aren't met.
type HideModuleInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HideModuleInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HideModuleInfoMultiError) AllErrors() []error { return m }

// HideModuleInfoValidationError is the validation error returned by
// HideModuleInfo.Validate if the designated constraints aren't met.
type HideModuleInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HideModuleInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HideModuleInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HideModuleInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HideModuleInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HideModuleInfoValidationError) ErrorName() string { return "HideModuleInfoValidationError" }

// Error satisfies the builtin error interface
func (e HideModuleInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHideModuleInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HideModuleInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HideModuleInfoValidationError{}

// Validate checks the field values on KillInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KillInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KillInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KillInfoMultiError, or nil
// if none found.
func (m *KillInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KillInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetPid

	// no validation rules for StartTime

	// no validation rules for Signal

	// no validation rules for TargetPath

	// no validation rules for IsExist

	if len(errors) > 0 {
		return KillInfoMultiError(errors)
	}

	return nil
}

// KillInfoMultiError is an error wrapping multiple validation errors returned
// by KillInfo.ValidateAll() if the designated constraints aren't met.
type KillInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KillInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KillInfoMultiError) AllErrors() []error { return m }

// KillInfoValidationError is the validation error returned by
// KillInfo.Validate if the designated constraints aren't met.
type KillInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KillInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KillInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KillInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KillInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KillInfoValidationError) ErrorName() string { return "KillInfoValidationError" }

// Error satisfies the builtin error interface
func (e KillInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKillInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KillInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KillInfoValidationError{}

// Validate checks the field values on RuleActionItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RuleActionItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleActionItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RuleActionItemMultiError,
// or nil if none found.
func (m *RuleActionItem) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleActionItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionType

	// no validation rules for TriggerTime

	// no validation rules for Pid

	// no validation rules for IsRule

	// no validation rules for StartTime

	// no validation rules for IsRefused

	switch v := m.ActionContent.(type) {
	case *RuleActionItem_RuleMsg:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRuleMsg()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "RuleMsg",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "RuleMsg",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRuleMsg()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "RuleMsg",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileCreateContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileCreateContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileCreateContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileCreateContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileCreateContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileCreateContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileOpenContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileOpenContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileOpenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileOpenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileOpenContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileOpenContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileWriteContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileWriteContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileWriteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileWriteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileWriteContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileWriteContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileRenameContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileRenameContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileRenameContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileRenameContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileRenameContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileRenameContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileDeleteContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileDeleteContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileDeleteContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileDeleteContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetConnectContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetConnectContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetConnectContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetConnectContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetConnectContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetConnectContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetAcceptContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetAcceptContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetAcceptContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetAcceptContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetAcceptContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetAcceptContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetListenContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetListenContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetListenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetListenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetListenContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetListenContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetSendContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetSendContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetSendContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetSendContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetSendContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetSendContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetRecvContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetRecvContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetRecvContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetRecvContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetRecvContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetRecvContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FilelessAttackContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFilelessAttackContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FilelessAttackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FilelessAttackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilelessAttackContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FilelessAttackContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_NetSnifferContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetSnifferContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetSnifferContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "NetSnifferContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetSnifferContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "NetSnifferContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_EnvHijackContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnvHijackContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "EnvHijackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "EnvHijackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnvHijackContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "EnvHijackContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_SelfDeleteContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSelfDeleteContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "SelfDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "SelfDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSelfDeleteContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "SelfDeleteContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_PtraceContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPtraceContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "PtraceContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "PtraceContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPtraceContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "PtraceContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileLinkContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileLinkContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileLinkContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileLinkContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileLinkContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileLinkContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_CallUsermodehelperContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCallUsermodehelperContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "CallUsermodehelperContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "CallUsermodehelperContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCallUsermodehelperContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "CallUsermodehelperContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_ReverseShellContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReverseShellContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "ReverseShellContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "ReverseShellContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReverseShellContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "ReverseShellContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_ProcessExecContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcessExecContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "ProcessExecContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "ProcessExecContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcessExecContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "ProcessExecContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_RestoreUtimeContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRestoreUtimeContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "RestoreUtimeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "RestoreUtimeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRestoreUtimeContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "RestoreUtimeContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileIoctlImmutableContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileIoctlImmutableContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileIoctlImmutableContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileIoctlImmutableContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileIoctlImmutableContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileIoctlImmutableContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_MkdirContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMkdirContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "MkdirContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "MkdirContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMkdirContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "MkdirContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileSymlink:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSymlink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileSymlink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileSymlink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSymlink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileSymlink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FileSetUid:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSetUid()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileSetUid",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FileSetUid",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSetUid()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FileSetUid",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_Bpf:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBpf()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "Bpf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "Bpf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBpf()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "Bpf",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_PrivilegeEscalation:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrivilegeEscalation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "PrivilegeEscalation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "PrivilegeEscalation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivilegeEscalation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "PrivilegeEscalation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_FakeExeFileContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFakeExeFileContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FakeExeFileContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "FakeExeFileContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFakeExeFileContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "FakeExeFileContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_HideModuleContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHideModuleContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "HideModuleContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "HideModuleContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHideModuleContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "HideModuleContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RuleActionItem_KillContent:
		if v == nil {
			err := RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKillContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "KillContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleActionItemValidationError{
						field:  "KillContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKillContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleActionItemValidationError{
					field:  "KillContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RuleActionItemMultiError(errors)
	}

	return nil
}

// RuleActionItemMultiError is an error wrapping multiple validation errors
// returned by RuleActionItem.ValidateAll() if the designated constraints
// aren't met.
type RuleActionItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleActionItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleActionItemMultiError) AllErrors() []error { return m }

// RuleActionItemValidationError is the validation error returned by
// RuleActionItem.Validate if the designated constraints aren't met.
type RuleActionItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleActionItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleActionItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleActionItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleActionItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleActionItemValidationError) ErrorName() string { return "RuleActionItemValidationError" }

// Error satisfies the builtin error interface
func (e RuleActionItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleActionItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleActionItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleActionItemValidationError{}

// Validate checks the field values on StatusProcessInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StatusProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatusProcessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StatusProcessInfoMultiError, or nil if none found.
func (m *StatusProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StatusProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for StartTime

	if len(errors) > 0 {
		return StatusProcessInfoMultiError(errors)
	}

	return nil
}

// StatusProcessInfoMultiError is an error wrapping multiple validation errors
// returned by StatusProcessInfo.ValidateAll() if the designated constraints
// aren't met.
type StatusProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatusProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatusProcessInfoMultiError) AllErrors() []error { return m }

// StatusProcessInfoValidationError is the validation error returned by
// StatusProcessInfo.Validate if the designated constraints aren't met.
type StatusProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatusProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatusProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatusProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatusProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatusProcessInfoValidationError) ErrorName() string {
	return "StatusProcessInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StatusProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatusProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatusProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatusProcessInfoValidationError{}

// Validate checks the field values on StatusFileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatusFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatusFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatusFileInfoMultiError,
// or nil if none found.
func (m *StatusFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StatusFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	if len(errors) > 0 {
		return StatusFileInfoMultiError(errors)
	}

	return nil
}

// StatusFileInfoMultiError is an error wrapping multiple validation errors
// returned by StatusFileInfo.ValidateAll() if the designated constraints
// aren't met.
type StatusFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatusFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatusFileInfoMultiError) AllErrors() []error { return m }

// StatusFileInfoValidationError is the validation error returned by
// StatusFileInfo.Validate if the designated constraints aren't met.
type StatusFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatusFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatusFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatusFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatusFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatusFileInfoValidationError) ErrorName() string { return "StatusFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e StatusFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatusFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatusFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatusFileInfoValidationError{}

// Validate checks the field values on StatusNetInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StatusNetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StatusNetInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StatusNetInfoMultiError, or
// nil if none found.
func (m *StatusNetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StatusNetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNetContentBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StatusNetInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StatusNetInfoValidationError{
					field:  "NetContentBaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetContentBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StatusNetInfoValidationError{
				field:  "NetContentBaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StatusNetInfoMultiError(errors)
	}

	return nil
}

// StatusNetInfoMultiError is an error wrapping multiple validation errors
// returned by StatusNetInfo.ValidateAll() if the designated constraints
// aren't met.
type StatusNetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatusNetInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatusNetInfoMultiError) AllErrors() []error { return m }

// StatusNetInfoValidationError is the validation error returned by
// StatusNetInfo.Validate if the designated constraints aren't met.
type StatusNetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatusNetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatusNetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatusNetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatusNetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatusNetInfoValidationError) ErrorName() string { return "StatusNetInfoValidationError" }

// Error satisfies the builtin error interface
func (e StatusNetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStatusNetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatusNetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatusNetInfoValidationError{}

// Validate checks the field values on RiskStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskStatusMultiError, or
// nil if none found.
func (m *RiskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.StatusContext.(type) {
	case *RiskStatus_ProcInfo:
		if v == nil {
			err := RiskStatusValidationError{
				field:  "StatusContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "ProcInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "ProcInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskStatusValidationError{
					field:  "ProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskStatus_NetInfo:
		if v == nil {
			err := RiskStatusValidationError{
				field:  "StatusContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "NetInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "NetInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskStatusValidationError{
					field:  "NetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *RiskStatus_FileInfo:
		if v == nil {
			err := RiskStatusValidationError{
				field:  "StatusContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "FileInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskStatusValidationError{
						field:  "FileInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskStatusValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return RiskStatusMultiError(errors)
	}

	return nil
}

// RiskStatusMultiError is an error wrapping multiple validation errors
// returned by RiskStatus.ValidateAll() if the designated constraints aren't met.
type RiskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskStatusMultiError) AllErrors() []error { return m }

// RiskStatusValidationError is the validation error returned by
// RiskStatus.Validate if the designated constraints aren't met.
type RiskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskStatusValidationError) ErrorName() string { return "RiskStatusValidationError" }

// Error satisfies the builtin error interface
func (e RiskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskStatusValidationError{}

// Validate checks the field values on RiskStatusListReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskStatusListReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskStatusListReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskStatusListReqMultiError, or nil if none found.
func (m *RiskStatusListReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskStatusListReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	for idx, item := range m.GetRiskStatusList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskStatusListReqValidationError{
						field:  fmt.Sprintf("RiskStatusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskStatusListReqValidationError{
						field:  fmt.Sprintf("RiskStatusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskStatusListReqValidationError{
					field:  fmt.Sprintf("RiskStatusList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RiskStatusListReqMultiError(errors)
	}

	return nil
}

// RiskStatusListReqMultiError is an error wrapping multiple validation errors
// returned by RiskStatusListReq.ValidateAll() if the designated constraints
// aren't met.
type RiskStatusListReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskStatusListReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskStatusListReqMultiError) AllErrors() []error { return m }

// RiskStatusListReqValidationError is the validation error returned by
// RiskStatusListReq.Validate if the designated constraints aren't met.
type RiskStatusListReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskStatusListReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskStatusListReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskStatusListReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskStatusListReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskStatusListReqValidationError) ErrorName() string {
	return "RiskStatusListReqValidationError"
}

// Error satisfies the builtin error interface
func (e RiskStatusListReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskStatusListReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskStatusListReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskStatusListReqValidationError{}

// Validate checks the field values on RiskStatusListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskStatusListResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskStatusListResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskStatusListRespMultiError, or nil if none found.
func (m *RiskStatusListResp) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskStatusListResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return RiskStatusListRespMultiError(errors)
	}

	return nil
}

// RiskStatusListRespMultiError is an error wrapping multiple validation errors
// returned by RiskStatusListResp.ValidateAll() if the designated constraints
// aren't met.
type RiskStatusListRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskStatusListRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskStatusListRespMultiError) AllErrors() []error { return m }

// RiskStatusListRespValidationError is the validation error returned by
// RiskStatusListResp.Validate if the designated constraints aren't met.
type RiskStatusListRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskStatusListRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskStatusListRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskStatusListRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskStatusListRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskStatusListRespValidationError) ErrorName() string {
	return "RiskStatusListRespValidationError"
}

// Error satisfies the builtin error interface
func (e RiskStatusListRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskStatusListResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskStatusListRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskStatusListRespValidationError{}

// Validate checks the field values on NodeAddrInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NodeAddrInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeAddrInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NodeAddrInfoMultiError, or
// nil if none found.
func (m *NodeAddrInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeAddrInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Port

	// no validation rules for Ip

	// no validation rules for AddressFamily

	// no validation rules for Protocol

	if len(errors) > 0 {
		return NodeAddrInfoMultiError(errors)
	}

	return nil
}

// NodeAddrInfoMultiError is an error wrapping multiple validation errors
// returned by NodeAddrInfo.ValidateAll() if the designated constraints aren't met.
type NodeAddrInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeAddrInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeAddrInfoMultiError) AllErrors() []error { return m }

// NodeAddrInfoValidationError is the validation error returned by
// NodeAddrInfo.Validate if the designated constraints aren't met.
type NodeAddrInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeAddrInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeAddrInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeAddrInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeAddrInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeAddrInfoValidationError) ErrorName() string { return "NodeAddrInfoValidationError" }

// Error satisfies the builtin error interface
func (e NodeAddrInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeAddrInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeAddrInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeAddrInfoValidationError{}

// Validate checks the field values on ConnectionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConnectionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConnectionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConnectionInfoMultiError,
// or nil if none found.
func (m *ConnectionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ConnectionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRemote()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectionInfoValidationError{
					field:  "Remote",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectionInfoValidationError{
					field:  "Remote",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemote()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectionInfoValidationError{
				field:  "Remote",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLocal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConnectionInfoValidationError{
					field:  "Local",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConnectionInfoValidationError{
					field:  "Local",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConnectionInfoValidationError{
				field:  "Local",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConnectionInfoMultiError(errors)
	}

	return nil
}

// ConnectionInfoMultiError is an error wrapping multiple validation errors
// returned by ConnectionInfo.ValidateAll() if the designated constraints
// aren't met.
type ConnectionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConnectionInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConnectionInfoMultiError) AllErrors() []error { return m }

// ConnectionInfoValidationError is the validation error returned by
// ConnectionInfo.Validate if the designated constraints aren't met.
type ConnectionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConnectionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConnectionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConnectionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConnectionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConnectionInfoValidationError) ErrorName() string { return "ConnectionInfoValidationError" }

// Error satisfies the builtin error interface
func (e ConnectionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConnectionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConnectionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConnectionInfoValidationError{}

// Validate checks the field values on DomainConnectionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DomainConnectionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DomainConnectionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DomainConnectionInfoMultiError, or nil if none found.
func (m *DomainConnectionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DomainConnectionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRemote()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainConnectionInfoValidationError{
					field:  "Remote",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainConnectionInfoValidationError{
					field:  "Remote",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRemote()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainConnectionInfoValidationError{
				field:  "Remote",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLocal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DomainConnectionInfoValidationError{
					field:  "Local",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DomainConnectionInfoValidationError{
					field:  "Local",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DomainConnectionInfoValidationError{
				field:  "Local",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DomainConnectionInfoMultiError(errors)
	}

	return nil
}

// DomainConnectionInfoMultiError is an error wrapping multiple validation
// errors returned by DomainConnectionInfo.ValidateAll() if the designated
// constraints aren't met.
type DomainConnectionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DomainConnectionInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DomainConnectionInfoMultiError) AllErrors() []error { return m }

// DomainConnectionInfoValidationError is the validation error returned by
// DomainConnectionInfo.Validate if the designated constraints aren't met.
type DomainConnectionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DomainConnectionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DomainConnectionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DomainConnectionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DomainConnectionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DomainConnectionInfoValidationError) ErrorName() string {
	return "DomainConnectionInfoValidationError"
}

// Error satisfies the builtin error interface
func (e DomainConnectionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDomainConnectionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DomainConnectionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DomainConnectionInfoValidationError{}

// Validate checks the field values on NodeFileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NodeFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NodeFileInfoMultiError, or
// nil if none found.
func (m *NodeFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilePath

	// no validation rules for FileSha256

	// no validation rules for FileSize

	// no validation rules for FileMtime

	// no validation rules for FileCtime

	// no validation rules for FileAtime

	// no validation rules for FileAccessPermission

	// no validation rules for FileMd5

	if all {
		switch v := interface{}(m.GetDetailInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeFileInfoValidationError{
					field:  "DetailInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeFileInfoValidationError{
					field:  "DetailInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetailInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeFileInfoValidationError{
				field:  "DetailInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSignatureInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NodeFileInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NodeFileInfoValidationError{
						field:  fmt.Sprintf("SignatureInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NodeFileInfoValidationError{
					field:  fmt.Sprintf("SignatureInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NodeFileInfoMultiError(errors)
	}

	return nil
}

// NodeFileInfoMultiError is an error wrapping multiple validation errors
// returned by NodeFileInfo.ValidateAll() if the designated constraints aren't met.
type NodeFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeFileInfoMultiError) AllErrors() []error { return m }

// NodeFileInfoValidationError is the validation error returned by
// NodeFileInfo.Validate if the designated constraints aren't met.
type NodeFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeFileInfoValidationError) ErrorName() string { return "NodeFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e NodeFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeFileInfoValidationError{}

// Validate checks the field values on NodeRemoteUrl with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NodeRemoteUrl) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeRemoteUrl with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NodeRemoteUrlMultiError, or
// nil if none found.
func (m *NodeRemoteUrl) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeRemoteUrl) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return NodeRemoteUrlMultiError(errors)
	}

	return nil
}

// NodeRemoteUrlMultiError is an error wrapping multiple validation errors
// returned by NodeRemoteUrl.ValidateAll() if the designated constraints
// aren't met.
type NodeRemoteUrlMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeRemoteUrlMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeRemoteUrlMultiError) AllErrors() []error { return m }

// NodeRemoteUrlValidationError is the validation error returned by
// NodeRemoteUrl.Validate if the designated constraints aren't met.
type NodeRemoteUrlValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeRemoteUrlValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeRemoteUrlValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeRemoteUrlValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeRemoteUrlValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeRemoteUrlValidationError) ErrorName() string { return "NodeRemoteUrlValidationError" }

// Error satisfies the builtin error interface
func (e NodeRemoteUrlValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeRemoteUrl.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeRemoteUrlValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeRemoteUrlValidationError{}

// Validate checks the field values on AcdrProcessUnique with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcdrProcessUnique) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcdrProcessUnique with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcdrProcessUniqueMultiError, or nil if none found.
func (m *AcdrProcessUnique) ValidateAll() error {
	return m.validate(true)
}

func (m *AcdrProcessUnique) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for StartTime

	if len(errors) > 0 {
		return AcdrProcessUniqueMultiError(errors)
	}

	return nil
}

// AcdrProcessUniqueMultiError is an error wrapping multiple validation errors
// returned by AcdrProcessUnique.ValidateAll() if the designated constraints
// aren't met.
type AcdrProcessUniqueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcdrProcessUniqueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcdrProcessUniqueMultiError) AllErrors() []error { return m }

// AcdrProcessUniqueValidationError is the validation error returned by
// AcdrProcessUnique.Validate if the designated constraints aren't met.
type AcdrProcessUniqueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcdrProcessUniqueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcdrProcessUniqueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcdrProcessUniqueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcdrProcessUniqueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcdrProcessUniqueValidationError) ErrorName() string {
	return "AcdrProcessUniqueValidationError"
}

// Error satisfies the builtin error interface
func (e AcdrProcessUniqueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcdrProcessUnique.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcdrProcessUniqueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcdrProcessUniqueValidationError{}

// Validate checks the field values on AcdrProcessSimple with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcdrProcessSimple) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcdrProcessSimple with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcdrProcessSimpleMultiError, or nil if none found.
func (m *AcdrProcessSimple) ValidateAll() error {
	return m.validate(true)
}

func (m *AcdrProcessSimple) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for StartTime

	// no validation rules for UserName

	// no validation rules for Command

	if all {
		switch v := interface{}(m.GetProcessFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AcdrProcessSimpleValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AcdrProcessSimpleValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AcdrProcessSimpleValidationError{
				field:  "ProcessFileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Euid

	if len(errors) > 0 {
		return AcdrProcessSimpleMultiError(errors)
	}

	return nil
}

// AcdrProcessSimpleMultiError is an error wrapping multiple validation errors
// returned by AcdrProcessSimple.ValidateAll() if the designated constraints
// aren't met.
type AcdrProcessSimpleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcdrProcessSimpleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcdrProcessSimpleMultiError) AllErrors() []error { return m }

// AcdrProcessSimpleValidationError is the validation error returned by
// AcdrProcessSimple.Validate if the designated constraints aren't met.
type AcdrProcessSimpleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcdrProcessSimpleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcdrProcessSimpleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcdrProcessSimpleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcdrProcessSimpleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcdrProcessSimpleValidationError) ErrorName() string {
	return "AcdrProcessSimpleValidationError"
}

// Error satisfies the builtin error interface
func (e AcdrProcessSimpleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcdrProcessSimple.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcdrProcessSimpleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcdrProcessSimpleValidationError{}

// Validate checks the field values on FileBaseInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileBaseInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileBaseInfoMultiError, or
// nil if none found.
func (m *FileBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileSize

	// no validation rules for FileAtime

	// no validation rules for FileMtime

	// no validation rules for FileCtime

	if len(errors) > 0 {
		return FileBaseInfoMultiError(errors)
	}

	return nil
}

// FileBaseInfoMultiError is an error wrapping multiple validation errors
// returned by FileBaseInfo.ValidateAll() if the designated constraints aren't met.
type FileBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileBaseInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileBaseInfoMultiError) AllErrors() []error { return m }

// FileBaseInfoValidationError is the validation error returned by
// FileBaseInfo.Validate if the designated constraints aren't met.
type FileBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileBaseInfoValidationError) ErrorName() string { return "FileBaseInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileBaseInfoValidationError{}

// Validate checks the field values on NodeProcessInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NodeProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NodeProcessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NodeProcessInfoMultiError, or nil if none found.
func (m *NodeProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NodeProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUniqueChild()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "UniqueChild",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "UniqueChild",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUniqueChild()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeProcessInfoValidationError{
				field:  "UniqueChild",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRootProcess

	// no validation rules for Command

	if all {
		switch v := interface{}(m.GetProcessFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeProcessInfoValidationError{
				field:  "ProcessFileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserName

	// no validation rules for Euid

	if all {
		switch v := interface{}(m.GetExecSrc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "ExecSrc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NodeProcessInfoValidationError{
					field:  "ExecSrc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecSrc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NodeProcessInfoValidationError{
				field:  "ExecSrc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NodeProcessInfoMultiError(errors)
	}

	return nil
}

// NodeProcessInfoMultiError is an error wrapping multiple validation errors
// returned by NodeProcessInfo.ValidateAll() if the designated constraints
// aren't met.
type NodeProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NodeProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NodeProcessInfoMultiError) AllErrors() []error { return m }

// NodeProcessInfoValidationError is the validation error returned by
// NodeProcessInfo.Validate if the designated constraints aren't met.
type NodeProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NodeProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NodeProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NodeProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NodeProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NodeProcessInfoValidationError) ErrorName() string { return "NodeProcessInfoValidationError" }

// Error satisfies the builtin error interface
func (e NodeProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNodeProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NodeProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NodeProcessInfoValidationError{}

// Validate checks the field values on AcdrProcessParrentRelationUpdate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AcdrProcessParrentRelationUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcdrProcessParrentRelationUpdate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AcdrProcessParrentRelationUpdateMultiError, or nil if none found.
func (m *AcdrProcessParrentRelationUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *AcdrProcessParrentRelationUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HardRelation

	if all {
		switch v := interface{}(m.GetProcessParrent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AcdrProcessParrentRelationUpdateValidationError{
					field:  "ProcessParrent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AcdrProcessParrentRelationUpdateValidationError{
					field:  "ProcessParrent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessParrent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AcdrProcessParrentRelationUpdateValidationError{
				field:  "ProcessParrent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessChiled()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AcdrProcessParrentRelationUpdateValidationError{
					field:  "ProcessChiled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AcdrProcessParrentRelationUpdateValidationError{
					field:  "ProcessChiled",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessChiled()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AcdrProcessParrentRelationUpdateValidationError{
				field:  "ProcessChiled",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AcdrProcessParrentRelationUpdateMultiError(errors)
	}

	return nil
}

// AcdrProcessParrentRelationUpdateMultiError is an error wrapping multiple
// validation errors returned by
// AcdrProcessParrentRelationUpdate.ValidateAll() if the designated
// constraints aren't met.
type AcdrProcessParrentRelationUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcdrProcessParrentRelationUpdateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcdrProcessParrentRelationUpdateMultiError) AllErrors() []error { return m }

// AcdrProcessParrentRelationUpdateValidationError is the validation error
// returned by AcdrProcessParrentRelationUpdate.Validate if the designated
// constraints aren't met.
type AcdrProcessParrentRelationUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcdrProcessParrentRelationUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcdrProcessParrentRelationUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcdrProcessParrentRelationUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcdrProcessParrentRelationUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcdrProcessParrentRelationUpdateValidationError) ErrorName() string {
	return "AcdrProcessParrentRelationUpdateValidationError"
}

// Error satisfies the builtin error interface
func (e AcdrProcessParrentRelationUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcdrProcessParrentRelationUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcdrProcessParrentRelationUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcdrProcessParrentRelationUpdateValidationError{}

// Validate checks the field values on FileRename with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileRename) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileRename with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileRenameMultiError, or
// nil if none found.
func (m *FileRename) ValidateAll() error {
	return m.validate(true)
}

func (m *FileRename) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileRenameValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileRenameValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileRenameValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileRenameValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileRenameValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileRenameValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileRenameMultiError(errors)
	}

	return nil
}

// FileRenameMultiError is an error wrapping multiple validation errors
// returned by FileRename.ValidateAll() if the designated constraints aren't met.
type FileRenameMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileRenameMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileRenameMultiError) AllErrors() []error { return m }

// FileRenameValidationError is the validation error returned by
// FileRename.Validate if the designated constraints aren't met.
type FileRenameValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileRenameValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileRenameValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileRenameValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileRenameValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileRenameValidationError) ErrorName() string { return "FileRenameValidationError" }

// Error satisfies the builtin error interface
func (e FileRenameValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileRename.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileRenameValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileRenameValidationError{}

// Validate checks the field values on RegCreate with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegCreate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegCreate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegCreateMultiError, or nil
// if none found.
func (m *RegCreate) ValidateAll() error {
	return m.validate(true)
}

func (m *RegCreate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RegPath

	if len(errors) > 0 {
		return RegCreateMultiError(errors)
	}

	return nil
}

// RegCreateMultiError is an error wrapping multiple validation errors returned
// by RegCreate.ValidateAll() if the designated constraints aren't met.
type RegCreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegCreateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegCreateMultiError) AllErrors() []error { return m }

// RegCreateValidationError is the validation error returned by
// RegCreate.Validate if the designated constraints aren't met.
type RegCreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegCreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegCreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegCreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegCreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegCreateValidationError) ErrorName() string { return "RegCreateValidationError" }

// Error satisfies the builtin error interface
func (e RegCreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegCreate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegCreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegCreateValidationError{}

// Validate checks the field values on RegDelete with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegDelete) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegDelete with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegDeleteMultiError, or nil
// if none found.
func (m *RegDelete) ValidateAll() error {
	return m.validate(true)
}

func (m *RegDelete) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RegPath

	if len(errors) > 0 {
		return RegDeleteMultiError(errors)
	}

	return nil
}

// RegDeleteMultiError is an error wrapping multiple validation errors returned
// by RegDelete.ValidateAll() if the designated constraints aren't met.
type RegDeleteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegDeleteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegDeleteMultiError) AllErrors() []error { return m }

// RegDeleteValidationError is the validation error returned by
// RegDelete.Validate if the designated constraints aren't met.
type RegDeleteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegDeleteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegDeleteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegDeleteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegDeleteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegDeleteValidationError) ErrorName() string { return "RegDeleteValidationError" }

// Error satisfies the builtin error interface
func (e RegDeleteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegDelete.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegDeleteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegDeleteValidationError{}

// Validate checks the field values on RegWrite with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegWrite) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegWrite with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegWriteMultiError, or nil
// if none found.
func (m *RegWrite) ValidateAll() error {
	return m.validate(true)
}

func (m *RegWrite) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RegPath

	// no validation rules for WriteValue

	if len(errors) > 0 {
		return RegWriteMultiError(errors)
	}

	return nil
}

// RegWriteMultiError is an error wrapping multiple validation errors returned
// by RegWrite.ValidateAll() if the designated constraints aren't met.
type RegWriteMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegWriteMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegWriteMultiError) AllErrors() []error { return m }

// RegWriteValidationError is the validation error returned by
// RegWrite.Validate if the designated constraints aren't met.
type RegWriteValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegWriteValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegWriteValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegWriteValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegWriteValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegWriteValidationError) ErrorName() string { return "RegWriteValidationError" }

// Error satisfies the builtin error interface
func (e RegWriteValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegWrite.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegWriteValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegWriteValidationError{}

// Validate checks the field values on RegSetSecurity with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RegSetSecurity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegSetSecurity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RegSetSecurityMultiError,
// or nil if none found.
func (m *RegSetSecurity) ValidateAll() error {
	return m.validate(true)
}

func (m *RegSetSecurity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RegPath

	if len(errors) > 0 {
		return RegSetSecurityMultiError(errors)
	}

	return nil
}

// RegSetSecurityMultiError is an error wrapping multiple validation errors
// returned by RegSetSecurity.ValidateAll() if the designated constraints
// aren't met.
type RegSetSecurityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegSetSecurityMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegSetSecurityMultiError) AllErrors() []error { return m }

// RegSetSecurityValidationError is the validation error returned by
// RegSetSecurity.Validate if the designated constraints aren't met.
type RegSetSecurityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegSetSecurityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegSetSecurityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegSetSecurityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegSetSecurityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegSetSecurityValidationError) ErrorName() string { return "RegSetSecurityValidationError" }

// Error satisfies the builtin error interface
func (e RegSetSecurityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegSetSecurity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegSetSecurityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegSetSecurityValidationError{}

// Validate checks the field values on ScriptHttp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScriptHttp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptHttp with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScriptHttpMultiError, or
// nil if none found.
func (m *ScriptHttp) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptHttp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for IsGet

	if len(errors) > 0 {
		return ScriptHttpMultiError(errors)
	}

	return nil
}

// ScriptHttpMultiError is an error wrapping multiple validation errors
// returned by ScriptHttp.ValidateAll() if the designated constraints aren't met.
type ScriptHttpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptHttpMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptHttpMultiError) AllErrors() []error { return m }

// ScriptHttpValidationError is the validation error returned by
// ScriptHttp.Validate if the designated constraints aren't met.
type ScriptHttpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptHttpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptHttpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptHttpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptHttpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptHttpValidationError) ErrorName() string { return "ScriptHttpValidationError" }

// Error satisfies the builtin error interface
func (e ScriptHttpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptHttp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptHttpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptHttpValidationError{}

// Validate checks the field values on ScriptImageLoad with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScriptImageLoad) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptImageLoad with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptImageLoadMultiError, or nil if none found.
func (m *ScriptImageLoad) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptImageLoad) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImagePath

	if len(errors) > 0 {
		return ScriptImageLoadMultiError(errors)
	}

	return nil
}

// ScriptImageLoadMultiError is an error wrapping multiple validation errors
// returned by ScriptImageLoad.ValidateAll() if the designated constraints
// aren't met.
type ScriptImageLoadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptImageLoadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptImageLoadMultiError) AllErrors() []error { return m }

// ScriptImageLoadValidationError is the validation error returned by
// ScriptImageLoad.Validate if the designated constraints aren't met.
type ScriptImageLoadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptImageLoadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptImageLoadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptImageLoadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptImageLoadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptImageLoadValidationError) ErrorName() string { return "ScriptImageLoadValidationError" }

// Error satisfies the builtin error interface
func (e ScriptImageLoadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptImageLoad.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptImageLoadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptImageLoadValidationError{}

// Validate checks the field values on ScriptRunWmicCode with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScriptRunWmicCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptRunWmicCode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptRunWmicCodeMultiError, or nil if none found.
func (m *ScriptRunWmicCode) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptRunWmicCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cmd

	// no validation rules for IsRemote

	if len(errors) > 0 {
		return ScriptRunWmicCodeMultiError(errors)
	}

	return nil
}

// ScriptRunWmicCodeMultiError is an error wrapping multiple validation errors
// returned by ScriptRunWmicCode.ValidateAll() if the designated constraints
// aren't met.
type ScriptRunWmicCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptRunWmicCodeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptRunWmicCodeMultiError) AllErrors() []error { return m }

// ScriptRunWmicCodeValidationError is the validation error returned by
// ScriptRunWmicCode.Validate if the designated constraints aren't met.
type ScriptRunWmicCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptRunWmicCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptRunWmicCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptRunWmicCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptRunWmicCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptRunWmicCodeValidationError) ErrorName() string {
	return "ScriptRunWmicCodeValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptRunWmicCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptRunWmicCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptRunWmicCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptRunWmicCodeValidationError{}

// Validate checks the field values on ScriptScheduleCreate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptScheduleCreate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptScheduleCreate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptScheduleCreateMultiError, or nil if none found.
func (m *ScriptScheduleCreate) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptScheduleCreate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	// no validation rules for Args

	if len(errors) > 0 {
		return ScriptScheduleCreateMultiError(errors)
	}

	return nil
}

// ScriptScheduleCreateMultiError is an error wrapping multiple validation
// errors returned by ScriptScheduleCreate.ValidateAll() if the designated
// constraints aren't met.
type ScriptScheduleCreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptScheduleCreateMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptScheduleCreateMultiError) AllErrors() []error { return m }

// ScriptScheduleCreateValidationError is the validation error returned by
// ScriptScheduleCreate.Validate if the designated constraints aren't met.
type ScriptScheduleCreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptScheduleCreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptScheduleCreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptScheduleCreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptScheduleCreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptScheduleCreateValidationError) ErrorName() string {
	return "ScriptScheduleCreateValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptScheduleCreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptScheduleCreate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptScheduleCreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptScheduleCreateValidationError{}

// Validate checks the field values on ScriptGetApiAddr with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScriptGetApiAddr) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptGetApiAddr with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptGetApiAddrMultiError, or nil if none found.
func (m *ScriptGetApiAddr) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptGetApiAddr) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApiName

	if len(errors) > 0 {
		return ScriptGetApiAddrMultiError(errors)
	}

	return nil
}

// ScriptGetApiAddrMultiError is an error wrapping multiple validation errors
// returned by ScriptGetApiAddr.ValidateAll() if the designated constraints
// aren't met.
type ScriptGetApiAddrMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptGetApiAddrMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptGetApiAddrMultiError) AllErrors() []error { return m }

// ScriptGetApiAddrValidationError is the validation error returned by
// ScriptGetApiAddr.Validate if the designated constraints aren't met.
type ScriptGetApiAddrValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptGetApiAddrValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptGetApiAddrValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptGetApiAddrValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptGetApiAddrValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptGetApiAddrValidationError) ErrorName() string { return "ScriptGetApiAddrValidationError" }

// Error satisfies the builtin error interface
func (e ScriptGetApiAddrValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptGetApiAddr.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptGetApiAddrValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptGetApiAddrValidationError{}

// Validate checks the field values on ScriptWmicWin32Share with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptWmicWin32Share) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptWmicWin32Share with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptWmicWin32ShareMultiError, or nil if none found.
func (m *ScriptWmicWin32Share) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptWmicWin32Share) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShareName

	// no validation rules for SharePath

	if len(errors) > 0 {
		return ScriptWmicWin32ShareMultiError(errors)
	}

	return nil
}

// ScriptWmicWin32ShareMultiError is an error wrapping multiple validation
// errors returned by ScriptWmicWin32Share.ValidateAll() if the designated
// constraints aren't met.
type ScriptWmicWin32ShareMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptWmicWin32ShareMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptWmicWin32ShareMultiError) AllErrors() []error { return m }

// ScriptWmicWin32ShareValidationError is the validation error returned by
// ScriptWmicWin32Share.Validate if the designated constraints aren't met.
type ScriptWmicWin32ShareValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptWmicWin32ShareValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptWmicWin32ShareValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptWmicWin32ShareValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptWmicWin32ShareValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptWmicWin32ShareValidationError) ErrorName() string {
	return "ScriptWmicWin32ShareValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptWmicWin32ShareValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptWmicWin32Share.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptWmicWin32ShareValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptWmicWin32ShareValidationError{}

// Validate checks the field values on ScriptWmicTerminateProcess with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptWmicTerminateProcess) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptWmicTerminateProcess with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptWmicTerminateProcessMultiError, or nil if none found.
func (m *ScriptWmicTerminateProcess) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptWmicTerminateProcess) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScriptWmicTerminateProcessValidationError{
					field:  "Proc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScriptWmicTerminateProcessValidationError{
					field:  "Proc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScriptWmicTerminateProcessValidationError{
				field:  "Proc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScriptWmicTerminateProcessMultiError(errors)
	}

	return nil
}

// ScriptWmicTerminateProcessMultiError is an error wrapping multiple
// validation errors returned by ScriptWmicTerminateProcess.ValidateAll() if
// the designated constraints aren't met.
type ScriptWmicTerminateProcessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptWmicTerminateProcessMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptWmicTerminateProcessMultiError) AllErrors() []error { return m }

// ScriptWmicTerminateProcessValidationError is the validation error returned
// by ScriptWmicTerminateProcess.Validate if the designated constraints aren't met.
type ScriptWmicTerminateProcessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptWmicTerminateProcessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptWmicTerminateProcessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptWmicTerminateProcessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptWmicTerminateProcessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptWmicTerminateProcessValidationError) ErrorName() string {
	return "ScriptWmicTerminateProcessValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptWmicTerminateProcessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptWmicTerminateProcess.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptWmicTerminateProcessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptWmicTerminateProcessValidationError{}

// Validate checks the field values on ScriptWmicRegOper with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScriptWmicRegOper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptWmicRegOper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptWmicRegOperMultiError, or nil if none found.
func (m *ScriptWmicRegOper) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptWmicRegOper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperType

	// no validation rules for KeyPath

	// no validation rules for ValuePath

	if len(errors) > 0 {
		return ScriptWmicRegOperMultiError(errors)
	}

	return nil
}

// ScriptWmicRegOperMultiError is an error wrapping multiple validation errors
// returned by ScriptWmicRegOper.ValidateAll() if the designated constraints
// aren't met.
type ScriptWmicRegOperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptWmicRegOperMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptWmicRegOperMultiError) AllErrors() []error { return m }

// ScriptWmicRegOperValidationError is the validation error returned by
// ScriptWmicRegOper.Validate if the designated constraints aren't met.
type ScriptWmicRegOperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptWmicRegOperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptWmicRegOperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptWmicRegOperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptWmicRegOperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptWmicRegOperValidationError) ErrorName() string {
	return "ScriptWmicRegOperValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptWmicRegOperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptWmicRegOper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptWmicRegOperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptWmicRegOperValidationError{}

// Validate checks the field values on ScriptWmicServiceOper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptWmicServiceOper) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptWmicServiceOper with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptWmicServiceOperMultiError, or nil if none found.
func (m *ScriptWmicServiceOper) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptWmicServiceOper) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OperType

	// no validation rules for ServiceName

	// no validation rules for ServiceExePath

	if len(errors) > 0 {
		return ScriptWmicServiceOperMultiError(errors)
	}

	return nil
}

// ScriptWmicServiceOperMultiError is an error wrapping multiple validation
// errors returned by ScriptWmicServiceOper.ValidateAll() if the designated
// constraints aren't met.
type ScriptWmicServiceOperMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptWmicServiceOperMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptWmicServiceOperMultiError) AllErrors() []error { return m }

// ScriptWmicServiceOperValidationError is the validation error returned by
// ScriptWmicServiceOper.Validate if the designated constraints aren't met.
type ScriptWmicServiceOperValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptWmicServiceOperValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptWmicServiceOperValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptWmicServiceOperValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptWmicServiceOperValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptWmicServiceOperValidationError) ErrorName() string {
	return "ScriptWmicServiceOperValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptWmicServiceOperValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptWmicServiceOper.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptWmicServiceOperValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptWmicServiceOperValidationError{}

// Validate checks the field values on ScriptWmicQuery with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ScriptWmicQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptWmicQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptWmicQueryMultiError, or nil if none found.
func (m *ScriptWmicQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptWmicQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QueryLanguage

	// no validation rules for QueryCmd

	if len(errors) > 0 {
		return ScriptWmicQueryMultiError(errors)
	}

	return nil
}

// ScriptWmicQueryMultiError is an error wrapping multiple validation errors
// returned by ScriptWmicQuery.ValidateAll() if the designated constraints
// aren't met.
type ScriptWmicQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptWmicQueryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptWmicQueryMultiError) AllErrors() []error { return m }

// ScriptWmicQueryValidationError is the validation error returned by
// ScriptWmicQuery.Validate if the designated constraints aren't met.
type ScriptWmicQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptWmicQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptWmicQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptWmicQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptWmicQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptWmicQueryValidationError) ErrorName() string { return "ScriptWmicQueryValidationError" }

// Error satisfies the builtin error interface
func (e ScriptWmicQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptWmicQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptWmicQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptWmicQueryValidationError{}

// Validate checks the field values on ScriptAmsiByAmsiContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptAmsiByAmsiContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptAmsiByAmsiContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptAmsiByAmsiContextMultiError, or nil if none found.
func (m *ScriptAmsiByAmsiContext) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptAmsiByAmsiContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Value

	if len(errors) > 0 {
		return ScriptAmsiByAmsiContextMultiError(errors)
	}

	return nil
}

// ScriptAmsiByAmsiContextMultiError is an error wrapping multiple validation
// errors returned by ScriptAmsiByAmsiContext.ValidateAll() if the designated
// constraints aren't met.
type ScriptAmsiByAmsiContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptAmsiByAmsiContextMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptAmsiByAmsiContextMultiError) AllErrors() []error { return m }

// ScriptAmsiByAmsiContextValidationError is the validation error returned by
// ScriptAmsiByAmsiContext.Validate if the designated constraints aren't met.
type ScriptAmsiByAmsiContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptAmsiByAmsiContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptAmsiByAmsiContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptAmsiByAmsiContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptAmsiByAmsiContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptAmsiByAmsiContextValidationError) ErrorName() string {
	return "ScriptAmsiByAmsiContextValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptAmsiByAmsiContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptAmsiByAmsiContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptAmsiByAmsiContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptAmsiByAmsiContextValidationError{}

// Validate checks the field values on ScriptAmsiDllHijack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ScriptAmsiDllHijack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptAmsiDllHijack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ScriptAmsiDllHijackMultiError, or nil if none found.
func (m *ScriptAmsiDllHijack) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptAmsiDllHijack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPath()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ScriptAmsiDllHijackValidationError{
					field:  "Path",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ScriptAmsiDllHijackValidationError{
					field:  "Path",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPath()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ScriptAmsiDllHijackValidationError{
				field:  "Path",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ScriptAmsiDllHijackMultiError(errors)
	}

	return nil
}

// ScriptAmsiDllHijackMultiError is an error wrapping multiple validation
// errors returned by ScriptAmsiDllHijack.ValidateAll() if the designated
// constraints aren't met.
type ScriptAmsiDllHijackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptAmsiDllHijackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptAmsiDllHijackMultiError) AllErrors() []error { return m }

// ScriptAmsiDllHijackValidationError is the validation error returned by
// ScriptAmsiDllHijack.Validate if the designated constraints aren't met.
type ScriptAmsiDllHijackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptAmsiDllHijackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptAmsiDllHijackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptAmsiDllHijackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptAmsiDllHijackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptAmsiDllHijackValidationError) ErrorName() string {
	return "ScriptAmsiDllHijackValidationError"
}

// Error satisfies the builtin error interface
func (e ScriptAmsiDllHijackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptAmsiDllHijack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptAmsiDllHijackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptAmsiDllHijackValidationError{}

// Validate checks the field values on ScriptEmail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ScriptEmail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ScriptEmail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ScriptEmailMultiError, or
// nil if none found.
func (m *ScriptEmail) ValidateAll() error {
	return m.validate(true)
}

func (m *ScriptEmail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderAddr

	// no validation rules for RecvAddr

	if len(errors) > 0 {
		return ScriptEmailMultiError(errors)
	}

	return nil
}

// ScriptEmailMultiError is an error wrapping multiple validation errors
// returned by ScriptEmail.ValidateAll() if the designated constraints aren't met.
type ScriptEmailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ScriptEmailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ScriptEmailMultiError) AllErrors() []error { return m }

// ScriptEmailValidationError is the validation error returned by
// ScriptEmail.Validate if the designated constraints aren't met.
type ScriptEmailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ScriptEmailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ScriptEmailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ScriptEmailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ScriptEmailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ScriptEmailValidationError) ErrorName() string { return "ScriptEmailValidationError" }

// Error satisfies the builtin error interface
func (e ScriptEmailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sScriptEmail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ScriptEmailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ScriptEmailValidationError{}

// Validate checks the field values on LogClear with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LogClear) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogClear with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LogClearMultiError, or nil
// if none found.
func (m *LogClear) ValidateAll() error {
	return m.validate(true)
}

func (m *LogClear) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogFile

	if len(errors) > 0 {
		return LogClearMultiError(errors)
	}

	return nil
}

// LogClearMultiError is an error wrapping multiple validation errors returned
// by LogClear.ValidateAll() if the designated constraints aren't met.
type LogClearMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogClearMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogClearMultiError) AllErrors() []error { return m }

// LogClearValidationError is the validation error returned by
// LogClear.Validate if the designated constraints aren't met.
type LogClearValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogClearValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogClearValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogClearValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogClearValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogClearValidationError) ErrorName() string { return "LogClearValidationError" }

// Error satisfies the builtin error interface
func (e LogClearValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogClear.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogClearValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogClearValidationError{}

// Validate checks the field values on CveAttack with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CveAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CveAttack with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CveAttackMultiError, or nil
// if none found.
func (m *CveAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *CveAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CveName

	// no validation rules for AttackSource

	if len(errors) > 0 {
		return CveAttackMultiError(errors)
	}

	return nil
}

// CveAttackMultiError is an error wrapping multiple validation errors returned
// by CveAttack.ValidateAll() if the designated constraints aren't met.
type CveAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CveAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CveAttackMultiError) AllErrors() []error { return m }

// CveAttackValidationError is the validation error returned by
// CveAttack.Validate if the designated constraints aren't met.
type CveAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CveAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CveAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CveAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CveAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CveAttackValidationError) ErrorName() string { return "CveAttackValidationError" }

// Error satisfies the builtin error interface
func (e CveAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCveAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CveAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CveAttackValidationError{}

// Validate checks the field values on RemoteBugOverflow with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RemoteBugOverflow) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoteBugOverflow with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoteBugOverflowMultiError, or nil if none found.
func (m *RemoteBugOverflow) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoteBugOverflow) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CveName

	if all {
		switch v := interface{}(m.GetNode()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoteBugOverflowValidationError{
					field:  "Node",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoteBugOverflowValidationError{
					field:  "Node",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNode()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoteBugOverflowValidationError{
				field:  "Node",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemoteBugOverflowMultiError(errors)
	}

	return nil
}

// RemoteBugOverflowMultiError is an error wrapping multiple validation errors
// returned by RemoteBugOverflow.ValidateAll() if the designated constraints
// aren't met.
type RemoteBugOverflowMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoteBugOverflowMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoteBugOverflowMultiError) AllErrors() []error { return m }

// RemoteBugOverflowValidationError is the validation error returned by
// RemoteBugOverflow.Validate if the designated constraints aren't met.
type RemoteBugOverflowValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoteBugOverflowValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoteBugOverflowValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoteBugOverflowValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoteBugOverflowValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoteBugOverflowValidationError) ErrorName() string {
	return "RemoteBugOverflowValidationError"
}

// Error satisfies the builtin error interface
func (e RemoteBugOverflowValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoteBugOverflow.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoteBugOverflowValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoteBugOverflowValidationError{}

// Validate checks the field values on PuppetProcessAttack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PuppetProcessAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PuppetProcessAttack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PuppetProcessAttackMultiError, or nil if none found.
func (m *PuppetProcessAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *PuppetProcessAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BaseAddr

	if len(errors) > 0 {
		return PuppetProcessAttackMultiError(errors)
	}

	return nil
}

// PuppetProcessAttackMultiError is an error wrapping multiple validation
// errors returned by PuppetProcessAttack.ValidateAll() if the designated
// constraints aren't met.
type PuppetProcessAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PuppetProcessAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PuppetProcessAttackMultiError) AllErrors() []error { return m }

// PuppetProcessAttackValidationError is the validation error returned by
// PuppetProcessAttack.Validate if the designated constraints aren't met.
type PuppetProcessAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PuppetProcessAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PuppetProcessAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PuppetProcessAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PuppetProcessAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PuppetProcessAttackValidationError) ErrorName() string {
	return "PuppetProcessAttackValidationError"
}

// Error satisfies the builtin error interface
func (e PuppetProcessAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPuppetProcessAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PuppetProcessAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PuppetProcessAttackValidationError{}

// Validate checks the field values on V01PtraceInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *V01PtraceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on V01PtraceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in V01PtraceInfoMultiError, or
// nil if none found.
func (m *V01PtraceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *V01PtraceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, V01PtraceInfoValidationError{
					field:  "ProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, V01PtraceInfoValidationError{
					field:  "ProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return V01PtraceInfoValidationError{
				field:  "ProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Mode

	// no validation rules for Addr

	if len(errors) > 0 {
		return V01PtraceInfoMultiError(errors)
	}

	return nil
}

// V01PtraceInfoMultiError is an error wrapping multiple validation errors
// returned by V01PtraceInfo.ValidateAll() if the designated constraints
// aren't met.
type V01PtraceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m V01PtraceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m V01PtraceInfoMultiError) AllErrors() []error { return m }

// V01PtraceInfoValidationError is the validation error returned by
// V01PtraceInfo.Validate if the designated constraints aren't met.
type V01PtraceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e V01PtraceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e V01PtraceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e V01PtraceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e V01PtraceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e V01PtraceInfoValidationError) ErrorName() string { return "V01PtraceInfoValidationError" }

// Error satisfies the builtin error interface
func (e V01PtraceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sV01PtraceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = V01PtraceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = V01PtraceInfoValidationError{}

// Validate checks the field values on V01KillInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *V01KillInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on V01KillInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in V01KillInfoMultiError, or
// nil if none found.
func (m *V01KillInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *V01KillInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, V01KillInfoValidationError{
					field:  "ProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, V01KillInfoValidationError{
					field:  "ProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return V01KillInfoValidationError{
				field:  "ProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Signal

	if len(errors) > 0 {
		return V01KillInfoMultiError(errors)
	}

	return nil
}

// V01KillInfoMultiError is an error wrapping multiple validation errors
// returned by V01KillInfo.ValidateAll() if the designated constraints aren't met.
type V01KillInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m V01KillInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m V01KillInfoMultiError) AllErrors() []error { return m }

// V01KillInfoValidationError is the validation error returned by
// V01KillInfo.Validate if the designated constraints aren't met.
type V01KillInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e V01KillInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e V01KillInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e V01KillInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e V01KillInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e V01KillInfoValidationError) ErrorName() string { return "V01KillInfoValidationError" }

// Error satisfies the builtin error interface
func (e V01KillInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sV01KillInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = V01KillInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = V01KillInfoValidationError{}

// Validate checks the field values on SetRlimit with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SetRlimit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRlimit with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SetRlimitMultiError, or nil
// if none found.
func (m *SetRlimit) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRlimit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Resource

	if len(errors) > 0 {
		return SetRlimitMultiError(errors)
	}

	return nil
}

// SetRlimitMultiError is an error wrapping multiple validation errors returned
// by SetRlimit.ValidateAll() if the designated constraints aren't met.
type SetRlimitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRlimitMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRlimitMultiError) AllErrors() []error { return m }

// SetRlimitValidationError is the validation error returned by
// SetRlimit.Validate if the designated constraints aren't met.
type SetRlimitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRlimitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRlimitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRlimitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRlimitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRlimitValidationError) ErrorName() string { return "SetRlimitValidationError" }

// Error satisfies the builtin error interface
func (e SetRlimitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRlimit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRlimitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRlimitValidationError{}

// Validate checks the field values on MemActionOtherInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemActionOtherInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemActionOtherInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemActionOtherInfoMultiError, or nil if none found.
func (m *MemActionOtherInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemActionOtherInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RetAddr

	// no validation rules for RetModuleName

	// no validation rules for CollectPointName

	if len(errors) > 0 {
		return MemActionOtherInfoMultiError(errors)
	}

	return nil
}

// MemActionOtherInfoMultiError is an error wrapping multiple validation errors
// returned by MemActionOtherInfo.ValidateAll() if the designated constraints
// aren't met.
type MemActionOtherInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemActionOtherInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemActionOtherInfoMultiError) AllErrors() []error { return m }

// MemActionOtherInfoValidationError is the validation error returned by
// MemActionOtherInfo.Validate if the designated constraints aren't met.
type MemActionOtherInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemActionOtherInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemActionOtherInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemActionOtherInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemActionOtherInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemActionOtherInfoValidationError) ErrorName() string {
	return "MemActionOtherInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemActionOtherInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemActionOtherInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemActionOtherInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemActionOtherInfoValidationError{}

// Validate checks the field values on QueueApcThread with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueueApcThread) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueueApcThread with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QueueApcThreadMultiError,
// or nil if none found.
func (m *QueueApcThread) ValidateAll() error {
	return m.validate(true)
}

func (m *QueueApcThread) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueueApcThreadValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueueApcThreadValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueueApcThreadValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for ApcRoutine

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueueApcThreadValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueueApcThreadValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueueApcThreadValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QueueApcThreadMultiError(errors)
	}

	return nil
}

// QueueApcThreadMultiError is an error wrapping multiple validation errors
// returned by QueueApcThread.ValidateAll() if the designated constraints
// aren't met.
type QueueApcThreadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueueApcThreadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueueApcThreadMultiError) AllErrors() []error { return m }

// QueueApcThreadValidationError is the validation error returned by
// QueueApcThread.Validate if the designated constraints aren't met.
type QueueApcThreadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueueApcThreadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueueApcThreadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueueApcThreadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueueApcThreadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueueApcThreadValidationError) ErrorName() string { return "QueueApcThreadValidationError" }

// Error satisfies the builtin error interface
func (e QueueApcThreadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueueApcThread.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueueApcThreadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueueApcThreadValidationError{}

// Validate checks the field values on SetContextThread with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SetContextThread) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetContextThread with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetContextThreadMultiError, or nil if none found.
func (m *SetContextThread) ValidateAll() error {
	return m.validate(true)
}

func (m *SetContextThread) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetContextThreadValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetContextThreadValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetContextThreadValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SetMode

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SetContextThreadValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SetContextThreadValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SetContextThreadValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SetContextThreadMultiError(errors)
	}

	return nil
}

// SetContextThreadMultiError is an error wrapping multiple validation errors
// returned by SetContextThread.ValidateAll() if the designated constraints
// aren't met.
type SetContextThreadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetContextThreadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetContextThreadMultiError) AllErrors() []error { return m }

// SetContextThreadValidationError is the validation error returned by
// SetContextThread.Validate if the designated constraints aren't met.
type SetContextThreadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetContextThreadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetContextThreadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetContextThreadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetContextThreadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetContextThreadValidationError) ErrorName() string { return "SetContextThreadValidationError" }

// Error satisfies the builtin error interface
func (e SetContextThreadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetContextThread.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetContextThreadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetContextThreadValidationError{}

// Validate checks the field values on ProtectVirtualMem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProtectVirtualMem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProtectVirtualMem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProtectVirtualMemMultiError, or nil if none found.
func (m *ProtectVirtualMem) ValidateAll() error {
	return m.validate(true)
}

func (m *ProtectVirtualMem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProtectVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProtectVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProtectVirtualMemValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for OldProtect

	// no validation rules for NewProtect

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProtectVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProtectVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProtectVirtualMemValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProtectVirtualMemMultiError(errors)
	}

	return nil
}

// ProtectVirtualMemMultiError is an error wrapping multiple validation errors
// returned by ProtectVirtualMem.ValidateAll() if the designated constraints
// aren't met.
type ProtectVirtualMemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProtectVirtualMemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProtectVirtualMemMultiError) AllErrors() []error { return m }

// ProtectVirtualMemValidationError is the validation error returned by
// ProtectVirtualMem.Validate if the designated constraints aren't met.
type ProtectVirtualMemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProtectVirtualMemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProtectVirtualMemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProtectVirtualMemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProtectVirtualMemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProtectVirtualMemValidationError) ErrorName() string {
	return "ProtectVirtualMemValidationError"
}

// Error satisfies the builtin error interface
func (e ProtectVirtualMemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProtectVirtualMem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProtectVirtualMemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProtectVirtualMemValidationError{}

// Validate checks the field values on AllocateVirtualMem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AllocateVirtualMem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AllocateVirtualMem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AllocateVirtualMemMultiError, or nil if none found.
func (m *AllocateVirtualMem) ValidateAll() error {
	return m.validate(true)
}

func (m *AllocateVirtualMem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllocateVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllocateVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllocateVirtualMemValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for Protect

	// no validation rules for AllocType

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AllocateVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AllocateVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AllocateVirtualMemValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AllocateVirtualMemMultiError(errors)
	}

	return nil
}

// AllocateVirtualMemMultiError is an error wrapping multiple validation errors
// returned by AllocateVirtualMem.ValidateAll() if the designated constraints
// aren't met.
type AllocateVirtualMemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AllocateVirtualMemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AllocateVirtualMemMultiError) AllErrors() []error { return m }

// AllocateVirtualMemValidationError is the validation error returned by
// AllocateVirtualMem.Validate if the designated constraints aren't met.
type AllocateVirtualMemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AllocateVirtualMemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AllocateVirtualMemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AllocateVirtualMemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AllocateVirtualMemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AllocateVirtualMemValidationError) ErrorName() string {
	return "AllocateVirtualMemValidationError"
}

// Error satisfies the builtin error interface
func (e AllocateVirtualMemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAllocateVirtualMem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AllocateVirtualMemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AllocateVirtualMemValidationError{}

// Validate checks the field values on WriteVirtualMem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WriteVirtualMem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WriteVirtualMem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WriteVirtualMemMultiError, or nil if none found.
func (m *WriteVirtualMem) ValidateAll() error {
	return m.validate(true)
}

func (m *WriteVirtualMem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WriteVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WriteVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WriteVirtualMemValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for WriteBufAddr

	// no validation rules for WriteLen

	// no validation rules for RiskMode

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WriteVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WriteVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WriteVirtualMemValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WriteVirtualMemMultiError(errors)
	}

	return nil
}

// WriteVirtualMemMultiError is an error wrapping multiple validation errors
// returned by WriteVirtualMem.ValidateAll() if the designated constraints
// aren't met.
type WriteVirtualMemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WriteVirtualMemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WriteVirtualMemMultiError) AllErrors() []error { return m }

// WriteVirtualMemValidationError is the validation error returned by
// WriteVirtualMem.Validate if the designated constraints aren't met.
type WriteVirtualMemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WriteVirtualMemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WriteVirtualMemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WriteVirtualMemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WriteVirtualMemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WriteVirtualMemValidationError) ErrorName() string { return "WriteVirtualMemValidationError" }

// Error satisfies the builtin error interface
func (e WriteVirtualMemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWriteVirtualMem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WriteVirtualMemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WriteVirtualMemValidationError{}

// Validate checks the field values on ReadVirtualMem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ReadVirtualMem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReadVirtualMem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ReadVirtualMemMultiError,
// or nil if none found.
func (m *ReadVirtualMem) ValidateAll() error {
	return m.validate(true)
}

func (m *ReadVirtualMem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReadVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReadVirtualMemValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReadVirtualMemValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for ReadBufAddr

	// no validation rules for ReadLen

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReadVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReadVirtualMemValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReadVirtualMemValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReadVirtualMemMultiError(errors)
	}

	return nil
}

// ReadVirtualMemMultiError is an error wrapping multiple validation errors
// returned by ReadVirtualMem.ValidateAll() if the designated constraints
// aren't met.
type ReadVirtualMemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReadVirtualMemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReadVirtualMemMultiError) AllErrors() []error { return m }

// ReadVirtualMemValidationError is the validation error returned by
// ReadVirtualMem.Validate if the designated constraints aren't met.
type ReadVirtualMemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReadVirtualMemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReadVirtualMemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReadVirtualMemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReadVirtualMemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReadVirtualMemValidationError) ErrorName() string { return "ReadVirtualMemValidationError" }

// Error satisfies the builtin error interface
func (e ReadVirtualMemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReadVirtualMem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReadVirtualMemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReadVirtualMemValidationError{}

// Validate checks the field values on MapViewOfSection with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MapViewOfSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MapViewOfSection with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MapViewOfSectionMultiError, or nil if none found.
func (m *MapViewOfSection) ValidateAll() error {
	return m.validate(true)
}

func (m *MapViewOfSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNodeProcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MapViewOfSectionValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MapViewOfSectionValidationError{
					field:  "NodeProcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNodeProcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MapViewOfSectionValidationError{
				field:  "NodeProcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BaseAddr

	// no validation rules for Protect

	// no validation rules for AllocType

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MapViewOfSectionValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MapViewOfSectionValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MapViewOfSectionValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MapViewOfSectionMultiError(errors)
	}

	return nil
}

// MapViewOfSectionMultiError is an error wrapping multiple validation errors
// returned by MapViewOfSection.ValidateAll() if the designated constraints
// aren't met.
type MapViewOfSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MapViewOfSectionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MapViewOfSectionMultiError) AllErrors() []error { return m }

// MapViewOfSectionValidationError is the validation error returned by
// MapViewOfSection.Validate if the designated constraints aren't met.
type MapViewOfSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MapViewOfSectionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MapViewOfSectionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MapViewOfSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MapViewOfSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MapViewOfSectionValidationError) ErrorName() string { return "MapViewOfSectionValidationError" }

// Error satisfies the builtin error interface
func (e MapViewOfSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMapViewOfSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MapViewOfSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MapViewOfSectionValidationError{}

// Validate checks the field values on WhiteAddBlack with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WhiteAddBlack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhiteAddBlack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WhiteAddBlackMultiError, or
// nil if none found.
func (m *WhiteAddBlack) ValidateAll() error {
	return m.validate(true)
}

func (m *WhiteAddBlack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportId

	if all {
		switch v := interface{}(m.GetDllFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhiteAddBlackValidationError{
					field:  "DllFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhiteAddBlackValidationError{
					field:  "DllFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDllFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhiteAddBlackValidationError{
				field:  "DllFileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WhiteAddBlackMultiError(errors)
	}

	return nil
}

// WhiteAddBlackMultiError is an error wrapping multiple validation errors
// returned by WhiteAddBlack.ValidateAll() if the designated constraints
// aren't met.
type WhiteAddBlackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhiteAddBlackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhiteAddBlackMultiError) AllErrors() []error { return m }

// WhiteAddBlackValidationError is the validation error returned by
// WhiteAddBlack.Validate if the designated constraints aren't met.
type WhiteAddBlackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhiteAddBlackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhiteAddBlackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhiteAddBlackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhiteAddBlackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhiteAddBlackValidationError) ErrorName() string { return "WhiteAddBlackValidationError" }

// Error satisfies the builtin error interface
func (e WhiteAddBlackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhiteAddBlack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhiteAddBlackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhiteAddBlackValidationError{}

// Validate checks the field values on OpenDeviceObject with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OpenDeviceObject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OpenDeviceObject with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OpenDeviceObjectMultiError, or nil if none found.
func (m *OpenDeviceObject) ValidateAll() error {
	return m.validate(true)
}

func (m *OpenDeviceObject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeviceName

	if len(errors) > 0 {
		return OpenDeviceObjectMultiError(errors)
	}

	return nil
}

// OpenDeviceObjectMultiError is an error wrapping multiple validation errors
// returned by OpenDeviceObject.ValidateAll() if the designated constraints
// aren't met.
type OpenDeviceObjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OpenDeviceObjectMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OpenDeviceObjectMultiError) AllErrors() []error { return m }

// OpenDeviceObjectValidationError is the validation error returned by
// OpenDeviceObject.Validate if the designated constraints aren't met.
type OpenDeviceObjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OpenDeviceObjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OpenDeviceObjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OpenDeviceObjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OpenDeviceObjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OpenDeviceObjectValidationError) ErrorName() string { return "OpenDeviceObjectValidationError" }

// Error satisfies the builtin error interface
func (e OpenDeviceObjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOpenDeviceObject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OpenDeviceObjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OpenDeviceObjectValidationError{}

// Validate checks the field values on CreateServiceAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateServiceAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateServiceAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateServiceActionMultiError, or nil if none found.
func (m *CreateServiceAction) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateServiceAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceName

	// no validation rules for ServicePath

	if len(errors) > 0 {
		return CreateServiceActionMultiError(errors)
	}

	return nil
}

// CreateServiceActionMultiError is an error wrapping multiple validation
// errors returned by CreateServiceAction.ValidateAll() if the designated
// constraints aren't met.
type CreateServiceActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateServiceActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateServiceActionMultiError) AllErrors() []error { return m }

// CreateServiceActionValidationError is the validation error returned by
// CreateServiceAction.Validate if the designated constraints aren't met.
type CreateServiceActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateServiceActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateServiceActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateServiceActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateServiceActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateServiceActionValidationError) ErrorName() string {
	return "CreateServiceActionValidationError"
}

// Error satisfies the builtin error interface
func (e CreateServiceActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateServiceAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateServiceActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateServiceActionValidationError{}

// Validate checks the field values on StartServiceAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartServiceAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartServiceAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartServiceActionMultiError, or nil if none found.
func (m *StartServiceAction) ValidateAll() error {
	return m.validate(true)
}

func (m *StartServiceAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ServiceName

	// no validation rules for ServicePath

	if len(errors) > 0 {
		return StartServiceActionMultiError(errors)
	}

	return nil
}

// StartServiceActionMultiError is an error wrapping multiple validation errors
// returned by StartServiceAction.ValidateAll() if the designated constraints
// aren't met.
type StartServiceActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartServiceActionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartServiceActionMultiError) AllErrors() []error { return m }

// StartServiceActionValidationError is the validation error returned by
// StartServiceAction.Validate if the designated constraints aren't met.
type StartServiceActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartServiceActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartServiceActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartServiceActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartServiceActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartServiceActionValidationError) ErrorName() string {
	return "StartServiceActionValidationError"
}

// Error satisfies the builtin error interface
func (e StartServiceActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartServiceAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartServiceActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartServiceActionValidationError{}

// Validate checks the field values on CreateTaskScheduler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTaskScheduler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTaskScheduler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTaskSchedulerMultiError, or nil if none found.
func (m *CreateTaskScheduler) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTaskScheduler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskSchedulerName

	// no validation rules for CreateAuthor

	// no validation rules for Command

	// no validation rules for Arguments

	if len(errors) > 0 {
		return CreateTaskSchedulerMultiError(errors)
	}

	return nil
}

// CreateTaskSchedulerMultiError is an error wrapping multiple validation
// errors returned by CreateTaskScheduler.ValidateAll() if the designated
// constraints aren't met.
type CreateTaskSchedulerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTaskSchedulerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTaskSchedulerMultiError) AllErrors() []error { return m }

// CreateTaskSchedulerValidationError is the validation error returned by
// CreateTaskScheduler.Validate if the designated constraints aren't met.
type CreateTaskSchedulerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTaskSchedulerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTaskSchedulerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTaskSchedulerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTaskSchedulerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTaskSchedulerValidationError) ErrorName() string {
	return "CreateTaskSchedulerValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTaskSchedulerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTaskScheduler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTaskSchedulerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTaskSchedulerValidationError{}

// Validate checks the field values on StartTaskScheduler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartTaskScheduler) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartTaskScheduler with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartTaskSchedulerMultiError, or nil if none found.
func (m *StartTaskScheduler) ValidateAll() error {
	return m.validate(true)
}

func (m *StartTaskScheduler) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskSchedulerName

	// no validation rules for CreateAuthor

	if all {
		switch v := interface{}(m.GetStartProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StartTaskSchedulerValidationError{
					field:  "StartProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StartTaskSchedulerValidationError{
					field:  "StartProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StartTaskSchedulerValidationError{
				field:  "StartProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StartTaskSchedulerMultiError(errors)
	}

	return nil
}

// StartTaskSchedulerMultiError is an error wrapping multiple validation errors
// returned by StartTaskScheduler.ValidateAll() if the designated constraints
// aren't met.
type StartTaskSchedulerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartTaskSchedulerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartTaskSchedulerMultiError) AllErrors() []error { return m }

// StartTaskSchedulerValidationError is the validation error returned by
// StartTaskScheduler.Validate if the designated constraints aren't met.
type StartTaskSchedulerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartTaskSchedulerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartTaskSchedulerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartTaskSchedulerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartTaskSchedulerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartTaskSchedulerValidationError) ErrorName() string {
	return "StartTaskSchedulerValidationError"
}

// Error satisfies the builtin error interface
func (e StartTaskSchedulerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartTaskScheduler.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartTaskSchedulerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartTaskSchedulerValidationError{}

// Validate checks the field values on V01RuleActionItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *V01RuleActionItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on V01RuleActionItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// V01RuleActionItemMultiError, or nil if none found.
func (m *V01RuleActionItem) ValidateAll() error {
	return m.validate(true)
}

func (m *V01RuleActionItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionType

	// no validation rules for TriggerTime

	if all {
		switch v := interface{}(m.GetProcessUnique()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, V01RuleActionItemValidationError{
					field:  "ProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, V01RuleActionItemValidationError{
					field:  "ProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessUnique()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return V01RuleActionItemValidationError{
				field:  "ProcessUnique",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, V01RuleActionItemValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, V01RuleActionItemValidationError{
					field:  "ProcessFileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return V01RuleActionItemValidationError{
				field:  "ProcessFileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.ActionContent.(type) {
	case *V01RuleActionItem_ProcessRelationUpdate:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcessRelationUpdate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessRelationUpdate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessRelationUpdate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcessRelationUpdate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ProcessRelationUpdate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ProcessCreate:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcessCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcessCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ProcessCreate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileCreate:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileCreate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileWrite:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileWrite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileWrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileWrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileWrite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileWrite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileRead:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileRead()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileRead",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileRead",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileRead()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileRead",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileDelete:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileDelete()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileDelete",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileDelete",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileDelete()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileDelete",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileRename:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileRename()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileRename",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileRename",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileRename()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileRename",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_NetConnect:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetConnect()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetConnect",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetConnect",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetConnect()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "NetConnect",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_NetAccept:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetAccept()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetAccept",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetAccept",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetAccept()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "NetAccept",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_NetListen:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetListen()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetListen",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetListen",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetListen()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "NetListen",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RegCreate:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRegCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRegCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RegCreate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptHttp:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptHttp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptHttp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptHttp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptHttp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptHttp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptImageLoad:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptImageLoad()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptImageLoad",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptImageLoad",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptImageLoad()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptImageLoad",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptRunWmicCode:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptRunWmicCode()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptRunWmicCode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptRunWmicCode",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptRunWmicCode()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptRunWmicCode",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptScheduleCreate:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptScheduleCreate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptScheduleCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptScheduleCreate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptScheduleCreate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptScheduleCreate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptGetApiAddr:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptGetApiAddr()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptGetApiAddr",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptGetApiAddr",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptGetApiAddr()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptGetApiAddr",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptWmicWin32Share:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptWmicWin32Share()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicWin32Share",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicWin32Share",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptWmicWin32Share()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptWmicWin32Share",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptWmicTerminateProcess:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptWmicTerminateProcess()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicTerminateProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicTerminateProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptWmicTerminateProcess()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptWmicTerminateProcess",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptWmicRegOper:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptWmicRegOper()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicRegOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicRegOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptWmicRegOper()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptWmicRegOper",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptWmicServiceOper:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptWmicServiceOper()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicServiceOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicServiceOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptWmicServiceOper()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptWmicServiceOper",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptWmicQuery:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptWmicQuery()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicQuery",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptWmicQuery",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptWmicQuery()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptWmicQuery",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptAmsiByAmsiContext:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptAmsiByAmsiContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptAmsiByAmsiContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptAmsiByAmsiContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptAmsiByAmsiContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptAmsiByAmsiContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptAmsiDllHijack:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptAmsiDllHijack()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptAmsiDllHijack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptAmsiDllHijack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptAmsiDllHijack()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptAmsiDllHijack",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ScriptEmail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetScriptEmail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptEmail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ScriptEmail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetScriptEmail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ScriptEmail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_LogClear:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLogClear()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "LogClear",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "LogClear",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLogClear()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "LogClear",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_KillProcessDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKillProcessDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "KillProcessDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "KillProcessDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKillProcessDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "KillProcessDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ProcessInjectDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcessInjectDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessInjectDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessInjectDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcessInjectDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ProcessInjectDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RegDelete:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRegDelete()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegDelete",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegDelete",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRegDelete()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RegDelete",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RegWrite:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRegWrite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegWrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegWrite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRegWrite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RegWrite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RegSetSecurity:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRegSetSecurity()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegSetSecurity",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RegSetSecurity",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRegSetSecurity()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RegSetSecurity",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileHide:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileHide()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileHide",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileHide",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileHide()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileHide",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileReadonly:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileReadonly()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileReadonly",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileReadonly",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileReadonly()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileReadonly",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_LoadRemoteModule:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoadRemoteModule()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "LoadRemoteModule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "LoadRemoteModule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoadRemoteModule()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "LoadRemoteModule",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_WhiteAddBlack:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWhiteAddBlack()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "WhiteAddBlack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "WhiteAddBlack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWhiteAddBlack()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "WhiteAddBlack",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_Cve:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCve()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "Cve",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "Cve",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCve()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "Cve",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RemoteBugOverflow:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRemoteBugOverflow()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RemoteBugOverflow",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RemoteBugOverflow",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRemoteBugOverflow()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RemoteBugOverflow",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_PuppetProcess:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPuppetProcess()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PuppetProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PuppetProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPuppetProcess()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "PuppetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_NetConnectDomain:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetConnectDomain()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetConnectDomain",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetConnectDomain",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetConnectDomain()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "NetConnectDomain",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_NetSnifferContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNetSnifferContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetSnifferContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "NetSnifferContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNetSnifferContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "NetSnifferContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_EnvHijackContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEnvHijackContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "EnvHijackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "EnvHijackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEnvHijackContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "EnvHijackContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_SelfDeleteContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSelfDeleteContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "SelfDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "SelfDeleteContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSelfDeleteContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "SelfDeleteContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_PtraceContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPtraceContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PtraceContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PtraceContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPtraceContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "PtraceContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileLinkContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileLinkContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileLinkContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileLinkContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileLinkContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileLinkContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_CallUsermodehelperContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCallUsermodehelperContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CallUsermodehelperContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CallUsermodehelperContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCallUsermodehelperContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "CallUsermodehelperContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ReverseShellContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReverseShellContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ReverseShellContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ReverseShellContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReverseShellContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ReverseShellContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ProcessExecContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProcessExecContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessExecContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProcessExecContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProcessExecContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ProcessExecContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_RestoreUtimeContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRestoreUtimeContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RestoreUtimeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "RestoreUtimeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRestoreUtimeContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "RestoreUtimeContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileIoctlImmutableContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileIoctlImmutableContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileIoctlImmutableContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileIoctlImmutableContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileIoctlImmutableContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileIoctlImmutableContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_MkdirContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMkdirContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "MkdirContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "MkdirContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMkdirContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "MkdirContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileSymlink:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSymlink()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileSymlink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileSymlink",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSymlink()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileSymlink",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileSetUid:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileSetUid()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileSetUid",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileSetUid",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileSetUid()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileSetUid",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_Bpf:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBpf()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "Bpf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "Bpf",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBpf()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "Bpf",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_PrivilegeEscalation:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrivilegeEscalation()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PrivilegeEscalation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "PrivilegeEscalation",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivilegeEscalation()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "PrivilegeEscalation",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FakeExeFileContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFakeExeFileContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FakeExeFileContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FakeExeFileContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFakeExeFileContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FakeExeFileContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_HideModuleContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHideModuleContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "HideModuleContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "HideModuleContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHideModuleContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "HideModuleContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_KillContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetKillContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "KillContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "KillContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetKillContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "KillContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileModeChangeContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileModeChangeContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileModeChangeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileModeChangeContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileModeChangeContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileModeChangeContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ResoureLimit:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResoureLimit()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ResoureLimit",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ResoureLimit",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResoureLimit()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ResoureLimit",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FilelessAttackContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFilelessAttackContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FilelessAttackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FilelessAttackContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFilelessAttackContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FilelessAttackContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_FileOpenContent:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileOpenContent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileOpenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "FileOpenContent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileOpenContent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "FileOpenContent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_QueueApcDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetQueueApcDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "QueueApcDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "QueueApcDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetQueueApcDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "QueueApcDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_SetContextThreadDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSetContextThreadDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "SetContextThreadDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "SetContextThreadDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSetContextThreadDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "SetContextThreadDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ProtectMemDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProtectMemDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProtectMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ProtectMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProtectMemDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ProtectMemDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_AllocateMemDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAllocateMemDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "AllocateMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "AllocateMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAllocateMemDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "AllocateMemDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_WriteMemDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWriteMemDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "WriteMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "WriteMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWriteMemDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "WriteMemDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_ReadMemDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetReadMemDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ReadMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "ReadMemDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetReadMemDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "ReadMemDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_MapViewSectionDetail:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMapViewSectionDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "MapViewSectionDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "MapViewSectionDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMapViewSectionDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "MapViewSectionDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_DeleteByself:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDeleteByself()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "DeleteByself",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "DeleteByself",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDeleteByself()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "DeleteByself",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_OpenDeviceObject:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOpenDeviceObject()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "OpenDeviceObject",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "OpenDeviceObject",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOpenDeviceObject()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "OpenDeviceObject",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_CreateService:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CreateService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CreateService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "CreateService",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_StartService:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStartService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "StartService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "StartService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStartService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "StartService",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_CreateTaskScheduler:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreateTaskScheduler()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CreateTaskScheduler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "CreateTaskScheduler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreateTaskScheduler()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "CreateTaskScheduler",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *V01RuleActionItem_StartTaskScheduler:
		if v == nil {
			err := V01RuleActionItemValidationError{
				field:  "ActionContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStartTaskScheduler()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "StartTaskScheduler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleActionItemValidationError{
						field:  "StartTaskScheduler",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStartTaskScheduler()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleActionItemValidationError{
					field:  "StartTaskScheduler",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return V01RuleActionItemMultiError(errors)
	}

	return nil
}

// V01RuleActionItemMultiError is an error wrapping multiple validation errors
// returned by V01RuleActionItem.ValidateAll() if the designated constraints
// aren't met.
type V01RuleActionItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m V01RuleActionItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m V01RuleActionItemMultiError) AllErrors() []error { return m }

// V01RuleActionItemValidationError is the validation error returned by
// V01RuleActionItem.Validate if the designated constraints aren't met.
type V01RuleActionItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e V01RuleActionItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e V01RuleActionItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e V01RuleActionItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e V01RuleActionItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e V01RuleActionItemValidationError) ErrorName() string {
	return "V01RuleActionItemValidationError"
}

// Error satisfies the builtin error interface
func (e V01RuleActionItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sV01RuleActionItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = V01RuleActionItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = V01RuleActionItemValidationError{}

// Validate checks the field values on V01RuleInfoMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *V01RuleInfoMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on V01RuleInfoMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// V01RuleInfoMessageMultiError, or nil if none found.
func (m *V01RuleInfoMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *V01RuleInfoMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleName

	// no validation rules for RuleLevel

	// no validation rules for NeedShowOnly0229

	if all {
		switch v := interface{}(m.GetRootProcessUnique()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, V01RuleInfoMessageValidationError{
					field:  "RootProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, V01RuleInfoMessageValidationError{
					field:  "RootProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRootProcessUnique()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return V01RuleInfoMessageValidationError{
				field:  "RootProcessUnique",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetActionList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, V01RuleInfoMessageValidationError{
						field:  fmt.Sprintf("ActionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, V01RuleInfoMessageValidationError{
						field:  fmt.Sprintf("ActionList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return V01RuleInfoMessageValidationError{
					field:  fmt.Sprintf("ActionList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Attack

	// no validation rules for ClientVersion

	if len(errors) > 0 {
		return V01RuleInfoMessageMultiError(errors)
	}

	return nil
}

// V01RuleInfoMessageMultiError is an error wrapping multiple validation errors
// returned by V01RuleInfoMessage.ValidateAll() if the designated constraints
// aren't met.
type V01RuleInfoMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m V01RuleInfoMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m V01RuleInfoMessageMultiError) AllErrors() []error { return m }

// V01RuleInfoMessageValidationError is the validation error returned by
// V01RuleInfoMessage.Validate if the designated constraints aren't met.
type V01RuleInfoMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e V01RuleInfoMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e V01RuleInfoMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e V01RuleInfoMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e V01RuleInfoMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e V01RuleInfoMessageValidationError) ErrorName() string {
	return "V01RuleInfoMessageValidationError"
}

// Error satisfies the builtin error interface
func (e V01RuleInfoMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sV01RuleInfoMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = V01RuleInfoMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = V01RuleInfoMessageValidationError{}

// Validate checks the field values on RuleInfoMessages with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RuleInfoMessages) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RuleInfoMessages with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RuleInfoMessagesMultiError, or nil if none found.
func (m *RuleInfoMessages) ValidateAll() error {
	return m.validate(true)
}

func (m *RuleInfoMessages) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRuleList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RuleInfoMessagesValidationError{
						field:  fmt.Sprintf("RuleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RuleInfoMessagesValidationError{
						field:  fmt.Sprintf("RuleList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RuleInfoMessagesValidationError{
					field:  fmt.Sprintf("RuleList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RuleInfoMessagesMultiError(errors)
	}

	return nil
}

// RuleInfoMessagesMultiError is an error wrapping multiple validation errors
// returned by RuleInfoMessages.ValidateAll() if the designated constraints
// aren't met.
type RuleInfoMessagesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RuleInfoMessagesMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RuleInfoMessagesMultiError) AllErrors() []error { return m }

// RuleInfoMessagesValidationError is the validation error returned by
// RuleInfoMessages.Validate if the designated constraints aren't met.
type RuleInfoMessagesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RuleInfoMessagesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RuleInfoMessagesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RuleInfoMessagesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RuleInfoMessagesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RuleInfoMessagesValidationError) ErrorName() string { return "RuleInfoMessagesValidationError" }

// Error satisfies the builtin error interface
func (e RuleInfoMessagesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRuleInfoMessages.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RuleInfoMessagesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RuleInfoMessagesValidationError{}

// Validate checks the field values on OutreachInfos with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutreachInfos) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachInfos with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutreachInfosMultiError, or
// nil if none found.
func (m *OutreachInfos) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachInfos) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBeans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OutreachInfosValidationError{
						field:  fmt.Sprintf("Beans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OutreachInfosValidationError{
						field:  fmt.Sprintf("Beans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OutreachInfosValidationError{
					field:  fmt.Sprintf("Beans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OutreachInfosMultiError(errors)
	}

	return nil
}

// OutreachInfosMultiError is an error wrapping multiple validation errors
// returned by OutreachInfos.ValidateAll() if the designated constraints
// aren't met.
type OutreachInfosMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachInfosMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachInfosMultiError) AllErrors() []error { return m }

// OutreachInfosValidationError is the validation error returned by
// OutreachInfos.Validate if the designated constraints aren't met.
type OutreachInfosValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachInfosValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachInfosValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachInfosValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachInfosValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachInfosValidationError) ErrorName() string { return "OutreachInfosValidationError" }

// Error satisfies the builtin error interface
func (e OutreachInfosValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachInfos.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachInfosValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachInfosValidationError{}

// Validate checks the field values on OutreachBean with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutreachBean) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachBean with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutreachBeanMultiError, or
// nil if none found.
func (m *OutreachBean) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachBean) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EventType

	// no validation rules for UniqueFlag

	if all {
		switch v := interface{}(m.GetNetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "NetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "NetInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachBeanValidationError{
				field:  "NetInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DiscoverTime

	if all {
		switch v := interface{}(m.GetRootProcessUnique()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "RootProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "RootProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRootProcessUnique()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachBeanValidationError{
				field:  "RootProcessUnique",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurProcessUnique()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "CurProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachBeanValidationError{
					field:  "CurProcessUnique",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurProcessUnique()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachBeanValidationError{
				field:  "CurProcessUnique",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientVersion

	if len(errors) > 0 {
		return OutreachBeanMultiError(errors)
	}

	return nil
}

// OutreachBeanMultiError is an error wrapping multiple validation errors
// returned by OutreachBean.ValidateAll() if the designated constraints aren't met.
type OutreachBeanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachBeanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachBeanMultiError) AllErrors() []error { return m }

// OutreachBeanValidationError is the validation error returned by
// OutreachBean.Validate if the designated constraints aren't met.
type OutreachBeanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachBeanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachBeanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachBeanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachBeanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachBeanValidationError) ErrorName() string { return "OutreachBeanValidationError" }

// Error satisfies the builtin error interface
func (e OutreachBeanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachBean.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachBeanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachBeanValidationError{}

// Validate checks the field values on InternalOutreachInfos with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternalOutreachInfos) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternalOutreachInfos with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InternalOutreachInfosMultiError, or nil if none found.
func (m *InternalOutreachInfos) ValidateAll() error {
	return m.validate(true)
}

func (m *InternalOutreachInfos) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetInterOutreach() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InternalOutreachInfosValidationError{
						field:  fmt.Sprintf("InterOutreach[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InternalOutreachInfosValidationError{
						field:  fmt.Sprintf("InterOutreach[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InternalOutreachInfosValidationError{
					field:  fmt.Sprintf("InterOutreach[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InternalOutreachInfosMultiError(errors)
	}

	return nil
}

// InternalOutreachInfosMultiError is an error wrapping multiple validation
// errors returned by InternalOutreachInfos.ValidateAll() if the designated
// constraints aren't met.
type InternalOutreachInfosMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternalOutreachInfosMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternalOutreachInfosMultiError) AllErrors() []error { return m }

// InternalOutreachInfosValidationError is the validation error returned by
// InternalOutreachInfos.Validate if the designated constraints aren't met.
type InternalOutreachInfosValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternalOutreachInfosValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternalOutreachInfosValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternalOutreachInfosValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternalOutreachInfosValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternalOutreachInfosValidationError) ErrorName() string {
	return "InternalOutreachInfosValidationError"
}

// Error satisfies the builtin error interface
func (e InternalOutreachInfosValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternalOutreachInfos.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternalOutreachInfosValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternalOutreachInfosValidationError{}

// Validate checks the field values on InternalOutreachInfoWithMac with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternalOutreachInfoWithMac) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternalOutreachInfoWithMac with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InternalOutreachInfoWithMacMultiError, or nil if none found.
func (m *InternalOutreachInfoWithMac) ValidateAll() error {
	return m.validate(true)
}

func (m *InternalOutreachInfoWithMac) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineID

	// no validation rules for Beans

	if len(errors) > 0 {
		return InternalOutreachInfoWithMacMultiError(errors)
	}

	return nil
}

// InternalOutreachInfoWithMacMultiError is an error wrapping multiple
// validation errors returned by InternalOutreachInfoWithMac.ValidateAll() if
// the designated constraints aren't met.
type InternalOutreachInfoWithMacMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternalOutreachInfoWithMacMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternalOutreachInfoWithMacMultiError) AllErrors() []error { return m }

// InternalOutreachInfoWithMacValidationError is the validation error returned
// by InternalOutreachInfoWithMac.Validate if the designated constraints
// aren't met.
type InternalOutreachInfoWithMacValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternalOutreachInfoWithMacValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternalOutreachInfoWithMacValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternalOutreachInfoWithMacValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternalOutreachInfoWithMacValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternalOutreachInfoWithMacValidationError) ErrorName() string {
	return "InternalOutreachInfoWithMacValidationError"
}

// Error satisfies the builtin error interface
func (e InternalOutreachInfoWithMacValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternalOutreachInfoWithMac.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternalOutreachInfoWithMacValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternalOutreachInfoWithMacValidationError{}
