// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_net.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskNetInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskNetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskNetInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskNetInfoMultiError, or nil if none found.
func (m *MemProtectRiskNetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskNetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskNetInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskNetInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskNetInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetScanList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskNetInfoValidationError{
						field:  fmt.Sprintf("ScanList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskNetInfoValidationError{
						field:  fmt.Sprintf("ScanList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskNetInfoValidationError{
					field:  fmt.Sprintf("ScanList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskNetInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskNetInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskNetInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskNetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskNetInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskNetInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskNetInfoValidationError is the validation error returned by
// MemProtectRiskNetInfo.Validate if the designated constraints aren't met.
type MemProtectRiskNetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskNetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskNetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskNetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskNetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskNetInfoValidationError) ErrorName() string {
	return "MemProtectRiskNetInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskNetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskNetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskNetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskNetInfoValidationError{}

// Validate checks the field values on PortScan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PortScan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortScan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PortScanMultiError, or nil
// if none found.
func (m *PortScan) ValidateAll() error {
	return m.validate(true)
}

func (m *PortScan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortScanValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortScanValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortScanValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemoteIp

	// no validation rules for Protocol

	if len(errors) > 0 {
		return PortScanMultiError(errors)
	}

	return nil
}

// PortScanMultiError is an error wrapping multiple validation errors returned
// by PortScan.ValidateAll() if the designated constraints aren't met.
type PortScanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortScanMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortScanMultiError) AllErrors() []error { return m }

// PortScanValidationError is the validation error returned by
// PortScan.Validate if the designated constraints aren't met.
type PortScanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortScanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortScanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortScanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortScanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortScanValidationError) ErrorName() string { return "PortScanValidationError" }

// Error satisfies the builtin error interface
func (e PortScanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortScan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortScanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortScanValidationError{}
