// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/license_enable.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求、广播
type LicenseEnableRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip           string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"` // ip，同时在线时，服务端判断machineid是否重复的依据
	Enable       bool   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	RandomKey    string `protobuf:"bytes,3,opt,name=randomKey,proto3" json:"randomKey,omitempty"`                     // 32字节的随机校验码
	ProcductName string `protobuf:"bytes,4,opt,name=procductName,proto3" json:"procductName,omitempty"`               // 企业标识
	Groupid      int64  `protobuf:"varint,5,opt,name=groupid,proto3" json:"groupid,omitempty"`                        // 组id
	ServerAddr   string `protobuf:"bytes,6,opt,name=server_addr,json=serverAddr,proto3" json:"server_addr,omitempty"` // config 中配置的连接 server 端IP或域名
}

func (x *LicenseEnableRequest) Reset() {
	*x = LicenseEnableRequest{}
	mi := &file_agent_license_enable_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LicenseEnableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LicenseEnableRequest) ProtoMessage() {}

func (x *LicenseEnableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_license_enable_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LicenseEnableRequest.ProtoReflect.Descriptor instead.
func (*LicenseEnableRequest) Descriptor() ([]byte, []int) {
	return file_agent_license_enable_proto_rawDescGZIP(), []int{0}
}

func (x *LicenseEnableRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *LicenseEnableRequest) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *LicenseEnableRequest) GetRandomKey() string {
	if x != nil {
		return x.RandomKey
	}
	return ""
}

func (x *LicenseEnableRequest) GetProcductName() string {
	if x != nil {
		return x.ProcductName
	}
	return ""
}

func (x *LicenseEnableRequest) GetGroupid() int64 {
	if x != nil {
		return x.Groupid
	}
	return 0
}

func (x *LicenseEnableRequest) GetServerAddr() string {
	if x != nil {
		return x.ServerAddr
	}
	return ""
}

// 回复
type LicenseEnableResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NeedSave     bool     `protobuf:"varint,1,opt,name=needSave,proto3" json:"needSave,omitempty"` // machineid是否重复，如果重复，machineid为新分配的
	Enable       bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`
	RandomKey    string   `protobuf:"bytes,3,opt,name=randomKey,proto3" json:"randomKey,omitempty"`       // 32字节的随机校验码
	ProcductName string   `protobuf:"bytes,4,opt,name=procductName,proto3" json:"procductName,omitempty"` // 企业标识
	Machineid    []byte   `protobuf:"bytes,5,opt,name=machineid,proto3" json:"machineid,omitempty"`       // machineid, 服务端返回信息
	Products     []string `protobuf:"bytes,6,rep,name=products,proto3" json:"products,omitempty"`         // 安芯主机端子产品列表
	GroupId      int64    `protobuf:"varint,7,opt,name=groupId,proto3" json:"groupId,omitempty"`          // 组id
}

func (x *LicenseEnableResponse) Reset() {
	*x = LicenseEnableResponse{}
	mi := &file_agent_license_enable_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LicenseEnableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LicenseEnableResponse) ProtoMessage() {}

func (x *LicenseEnableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agent_license_enable_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LicenseEnableResponse.ProtoReflect.Descriptor instead.
func (*LicenseEnableResponse) Descriptor() ([]byte, []int) {
	return file_agent_license_enable_proto_rawDescGZIP(), []int{1}
}

func (x *LicenseEnableResponse) GetNeedSave() bool {
	if x != nil {
		return x.NeedSave
	}
	return false
}

func (x *LicenseEnableResponse) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *LicenseEnableResponse) GetRandomKey() string {
	if x != nil {
		return x.RandomKey
	}
	return ""
}

func (x *LicenseEnableResponse) GetProcductName() string {
	if x != nil {
		return x.ProcductName
	}
	return ""
}

func (x *LicenseEnableResponse) GetMachineid() []byte {
	if x != nil {
		return x.Machineid
	}
	return nil
}

func (x *LicenseEnableResponse) GetProducts() []string {
	if x != nil {
		return x.Products
	}
	return nil
}

func (x *LicenseEnableResponse) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

var File_agent_license_enable_proto protoreflect.FileDescriptor

var file_agent_license_enable_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x22, 0xbb, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x4b, 0x65,
	0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x4b,
	0x65, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x64, 0x75,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64,
	0x72, 0x22, 0xe1, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e,
	0x65, 0x65, 0x64, 0x53, 0x61, 0x76, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e,
	0x65, 0x65, 0x64, 0x53, 0x61, 0x76, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x4b, 0x65, 0x79, 0x12, 0x22, 0x0a,
	0x0c, 0x70, 0x72, 0x6f, 0x63, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x69, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x69, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_license_enable_proto_rawDescOnce sync.Once
	file_agent_license_enable_proto_rawDescData = file_agent_license_enable_proto_rawDesc
)

func file_agent_license_enable_proto_rawDescGZIP() []byte {
	file_agent_license_enable_proto_rawDescOnce.Do(func() {
		file_agent_license_enable_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_license_enable_proto_rawDescData)
	})
	return file_agent_license_enable_proto_rawDescData
}

var file_agent_license_enable_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_license_enable_proto_goTypes = []any{
	(*LicenseEnableRequest)(nil),  // 0: agent.LicenseEnableRequest
	(*LicenseEnableResponse)(nil), // 1: agent.LicenseEnableResponse
}
var file_agent_license_enable_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_agent_license_enable_proto_init() }
func file_agent_license_enable_proto_init() {
	if File_agent_license_enable_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_license_enable_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_license_enable_proto_goTypes,
		DependencyIndexes: file_agent_license_enable_proto_depIdxs,
		MessageInfos:      file_agent_license_enable_proto_msgTypes,
	}.Build()
	File_agent_license_enable_proto = out.File
	file_agent_license_enable_proto_rawDesc = nil
	file_agent_license_enable_proto_goTypes = nil
	file_agent_license_enable_proto_depIdxs = nil
}
