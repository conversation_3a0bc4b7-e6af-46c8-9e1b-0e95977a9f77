// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/common.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// eventlog与policy都要用，所以在common里面定义
type FileOperate int32

const (
	FileOperate_FO_FILEOPERATEUNKNOWN FileOperate = 0
	FileOperate_FO_CREATEFILE         FileOperate = 1  // 新建
	FileOperate_FO_READFILE           FileOperate = 2  // 读取
	FileOperate_FO_WRITEFILE          FileOperate = 4  // 写入
	FileOperate_FO_RENAMEFILE         FileOperate = 8  // 改名
	FileOperate_FO_DELETEFILE         FileOperate = 16 // 删除
	FileOperate_FO_EXECUTEFILE        FileOperate = 32 // 执行
	FileOperate_FO_ATTRIBUTEFILE      FileOperate = 64 // 修改属性
)

// Enum value maps for FileOperate.
var (
	FileOperate_name = map[int32]string{
		0:  "FO_FILEOPERATEUNKNOWN",
		1:  "FO_CREATEFILE",
		2:  "FO_READFILE",
		4:  "FO_WRITEFILE",
		8:  "FO_RENAMEFILE",
		16: "FO_DELETEFILE",
		32: "FO_EXECUTEFILE",
		64: "FO_ATTRIBUTEFILE",
	}
	FileOperate_value = map[string]int32{
		"FO_FILEOPERATEUNKNOWN": 0,
		"FO_CREATEFILE":         1,
		"FO_READFILE":           2,
		"FO_WRITEFILE":          4,
		"FO_RENAMEFILE":         8,
		"FO_DELETEFILE":         16,
		"FO_EXECUTEFILE":        32,
		"FO_ATTRIBUTEFILE":      64,
	}
)

func (x FileOperate) Enum() *FileOperate {
	p := new(FileOperate)
	*p = x
	return p
}

func (x FileOperate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileOperate) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[0].Descriptor()
}

func (FileOperate) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[0]
}

func (x FileOperate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileOperate.Descriptor instead.
func (FileOperate) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{0}
}

// IP 协议版本可以用来标识端口IP
type InternetProtocol int32

const (
	InternetProtocol_IP_TCP  InternetProtocol = 0
	InternetProtocol_IP_UDP  InternetProtocol = 1
	InternetProtocol_IP_TCP6 InternetProtocol = 2
	InternetProtocol_IP_UDP6 InternetProtocol = 3
)

// Enum value maps for InternetProtocol.
var (
	InternetProtocol_name = map[int32]string{
		0: "IP_TCP",
		1: "IP_UDP",
		2: "IP_TCP6",
		3: "IP_UDP6",
	}
	InternetProtocol_value = map[string]int32{
		"IP_TCP":  0,
		"IP_UDP":  1,
		"IP_TCP6": 2,
		"IP_UDP6": 3,
	}
)

func (x InternetProtocol) Enum() *InternetProtocol {
	p := new(InternetProtocol)
	*p = x
	return p
}

func (x InternetProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InternetProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[1].Descriptor()
}

func (InternetProtocol) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[1]
}

func (x InternetProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InternetProtocol.Descriptor instead.
func (InternetProtocol) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{1}
}

type NewFileOperate int32

const (
	NewFileOperate_NFO_UNKNOWN     NewFileOperate = 0
	NewFileOperate_NFO_READFILE    NewFileOperate = 1
	NewFileOperate_NFO_WRITEFILE   NewFileOperate = 2
	NewFileOperate_NFO_EXECUTEFILE NewFileOperate = 3
	NewFileOperate_NFO_RENAMEFILE  NewFileOperate = 4
	NewFileOperate_NFO_HIDEFILE    NewFileOperate = 5
	NewFileOperate_NFO_DELETEFILE  NewFileOperate = 6
	NewFileOperate_NFO_CREATEFILE  NewFileOperate = 7
	NewFileOperate_NFO_LINKFILE    NewFileOperate = 8
	NewFileOperate_NFO_CHATTRFILE  NewFileOperate = 9
	NewFileOperate_NFO_CHMODFILE   NewFileOperate = 10
)

// Enum value maps for NewFileOperate.
var (
	NewFileOperate_name = map[int32]string{
		0:  "NFO_UNKNOWN",
		1:  "NFO_READFILE",
		2:  "NFO_WRITEFILE",
		3:  "NFO_EXECUTEFILE",
		4:  "NFO_RENAMEFILE",
		5:  "NFO_HIDEFILE",
		6:  "NFO_DELETEFILE",
		7:  "NFO_CREATEFILE",
		8:  "NFO_LINKFILE",
		9:  "NFO_CHATTRFILE",
		10: "NFO_CHMODFILE",
	}
	NewFileOperate_value = map[string]int32{
		"NFO_UNKNOWN":     0,
		"NFO_READFILE":    1,
		"NFO_WRITEFILE":   2,
		"NFO_EXECUTEFILE": 3,
		"NFO_RENAMEFILE":  4,
		"NFO_HIDEFILE":    5,
		"NFO_DELETEFILE":  6,
		"NFO_CREATEFILE":  7,
		"NFO_LINKFILE":    8,
		"NFO_CHATTRFILE":  9,
		"NFO_CHMODFILE":   10,
	}
)

func (x NewFileOperate) Enum() *NewFileOperate {
	p := new(NewFileOperate)
	*p = x
	return p
}

func (x NewFileOperate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NewFileOperate) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[2].Descriptor()
}

func (NewFileOperate) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[2]
}

func (x NewFileOperate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NewFileOperate.Descriptor instead.
func (NewFileOperate) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{2}
}

type EvidenceType int32

const (
	EvidenceType_EVIDENCE_TYPE_UNKNOWN          EvidenceType = 0
	EvidenceType_EVIDENCE_TYPE_NET              EvidenceType = 1
	EvidenceType_EVIDENCE_TYPE_FILE             EvidenceType = 2
	EvidenceType_EVIDENCE_TYPE_SCRIPT_FILE      EvidenceType = 4
	EvidenceType_EVIDENCE_TYPE_CLUE_MEMORY_DUMP EvidenceType = 8
	EvidenceType_EVIDENCE_TYPE_SYSTEM_LOG       EvidenceType = 16
	EvidenceType_EVIDENCE_TYPE_MEMORY_MINIDUMP  EvidenceType = 32
	EvidenceType_EVIDENCE_TYPE_MEMORY_SEGMENT   EvidenceType = 64
)

// Enum value maps for EvidenceType.
var (
	EvidenceType_name = map[int32]string{
		0:  "EVIDENCE_TYPE_UNKNOWN",
		1:  "EVIDENCE_TYPE_NET",
		2:  "EVIDENCE_TYPE_FILE",
		4:  "EVIDENCE_TYPE_SCRIPT_FILE",
		8:  "EVIDENCE_TYPE_CLUE_MEMORY_DUMP",
		16: "EVIDENCE_TYPE_SYSTEM_LOG",
		32: "EVIDENCE_TYPE_MEMORY_MINIDUMP",
		64: "EVIDENCE_TYPE_MEMORY_SEGMENT",
	}
	EvidenceType_value = map[string]int32{
		"EVIDENCE_TYPE_UNKNOWN":          0,
		"EVIDENCE_TYPE_NET":              1,
		"EVIDENCE_TYPE_FILE":             2,
		"EVIDENCE_TYPE_SCRIPT_FILE":      4,
		"EVIDENCE_TYPE_CLUE_MEMORY_DUMP": 8,
		"EVIDENCE_TYPE_SYSTEM_LOG":       16,
		"EVIDENCE_TYPE_MEMORY_MINIDUMP":  32,
		"EVIDENCE_TYPE_MEMORY_SEGMENT":   64,
	}
)

func (x EvidenceType) Enum() *EvidenceType {
	p := new(EvidenceType)
	*p = x
	return p
}

func (x EvidenceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[3].Descriptor()
}

func (EvidenceType) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[3]
}

func (x EvidenceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceType.Descriptor instead.
func (EvidenceType) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{3}
}

type FileTypeIdent int32

const (
	FileTypeIdent_FILE_TYPE_UNKNOWN     FileTypeIdent = 0
	FileTypeIdent_FILE_TYPE_PHP         FileTypeIdent = 1
	FileTypeIdent_FILE_TYPE_JSP         FileTypeIdent = 2
	FileTypeIdent_FILE_TYPE_ASP         FileTypeIdent = 3
	FileTypeIdent_FILE_TYPE_ASPX        FileTypeIdent = 4
	FileTypeIdent_FILE_TYPE_PYTHON      FileTypeIdent = 5
	FileTypeIdent_FILE_TYPE_PERL        FileTypeIdent = 6
	FileTypeIdent_FILE_TYPE_JPG_PHP     FileTypeIdent = 7
	FileTypeIdent_FILE_TYPE_GIF_PHP     FileTypeIdent = 8
	FileTypeIdent_FILE_TYPE_BMP_PHP     FileTypeIdent = 9
	FileTypeIdent_FILE_TYPE_PNG_PHP     FileTypeIdent = 10
	FileTypeIdent_FILE_TYPE_JP2_PHP     FileTypeIdent = 11
	FileTypeIdent_FILE_TYPE_ICON_PHP    FileTypeIdent = 12
	FileTypeIdent_FILE_TYPE_JPG_JSP     FileTypeIdent = 13
	FileTypeIdent_FILE_TYPE_GIF_JSP     FileTypeIdent = 14
	FileTypeIdent_FILE_TYPE_BMP_JSP     FileTypeIdent = 15
	FileTypeIdent_FILE_TYPE_PNG_JSP     FileTypeIdent = 16
	FileTypeIdent_FILE_TYPE_JP2_JSP     FileTypeIdent = 17
	FileTypeIdent_FILE_TYPE_ICON_JSP    FileTypeIdent = 18
	FileTypeIdent_FILE_TYPE_JPG_ASP     FileTypeIdent = 20
	FileTypeIdent_FILE_TYPE_GIF_ASP     FileTypeIdent = 21
	FileTypeIdent_FILE_TYPE_BMP_ASP     FileTypeIdent = 22
	FileTypeIdent_FILE_TYPE_PNG_ASP     FileTypeIdent = 23
	FileTypeIdent_FILE_TYPE_JP2_ASP     FileTypeIdent = 24
	FileTypeIdent_FILE_TYPE_ICON_ASP    FileTypeIdent = 25
	FileTypeIdent_FILE_TYPE_JPG_ASPX    FileTypeIdent = 26
	FileTypeIdent_FILE_TYPE_GIF_ASPX    FileTypeIdent = 27
	FileTypeIdent_FILE_TYPE_BMP_ASPX    FileTypeIdent = 28
	FileTypeIdent_FILE_TYPE_PNG_ASPX    FileTypeIdent = 29
	FileTypeIdent_FILE_TYPE_JP2_ASPX    FileTypeIdent = 30
	FileTypeIdent_FILE_TYPE_ICON_ASPX   FileTypeIdent = 31
	FileTypeIdent_FILE_TYPE_JPG_PYTHON  FileTypeIdent = 32
	FileTypeIdent_FILE_TYPE_GIF_PYTHON  FileTypeIdent = 33
	FileTypeIdent_FILE_TYPE_BMP_PYTHON  FileTypeIdent = 34
	FileTypeIdent_FILE_TYPE_PNG_PYTHON  FileTypeIdent = 35
	FileTypeIdent_FILE_TYPE_JP2_PYTHON  FileTypeIdent = 36
	FileTypeIdent_FILE_TYPE_ICON_PYTHON FileTypeIdent = 37
	FileTypeIdent_FILE_TYPE_JPG_PERL    FileTypeIdent = 38
	FileTypeIdent_FILE_TYPE_GIF_PERL    FileTypeIdent = 39
	FileTypeIdent_FILE_TYPE_BMP_PERL    FileTypeIdent = 40
	FileTypeIdent_FILE_TYPE_PNG_PERL    FileTypeIdent = 41
	FileTypeIdent_FILE_TYPE_JP2_PERL    FileTypeIdent = 42
	FileTypeIdent_FILE_TYPE_ICON_PERL   FileTypeIdent = 43
	FileTypeIdent_FILE_TYPE_ELF         FileTypeIdent = 44
	FileTypeIdent_FILE_TYPE_SHELL       FileTypeIdent = 45
	FileTypeIdent_FILE_TYPE_PE          FileTypeIdent = 46
	FileTypeIdent_FILE_TYPE_PDF         FileTypeIdent = 47
	FileTypeIdent_FILE_TYPE_DOC         FileTypeIdent = 48
	FileTypeIdent_FILE_TYPE_XLS         FileTypeIdent = 49
	FileTypeIdent_FILE_TYPE_PPT         FileTypeIdent = 50
	FileTypeIdent_FILE_TYPE_DLL         FileTypeIdent = 51
	FileTypeIdent_FILE_TYPE_BAT         FileTypeIdent = 52
	FileTypeIdent_FILE_TYPE_MSI         FileTypeIdent = 53
	FileTypeIdent_FILE_TYPE_ODT         FileTypeIdent = 54
	FileTypeIdent_FILE_TYPE_VB          FileTypeIdent = 55
	FileTypeIdent_FILE_TYPE_JS          FileTypeIdent = 56
	FileTypeIdent_FILE_TYPE_PS          FileTypeIdent = 57 // powershell
	FileTypeIdent_FILE_TYPE_PY          FileTypeIdent = 58
	FileTypeIdent_FILE_TYPE_LNK         FileTypeIdent = 59
	FileTypeIdent_FILE_TYPE_ARCHIVE     FileTypeIdent = 60
)

// Enum value maps for FileTypeIdent.
var (
	FileTypeIdent_name = map[int32]string{
		0:  "FILE_TYPE_UNKNOWN",
		1:  "FILE_TYPE_PHP",
		2:  "FILE_TYPE_JSP",
		3:  "FILE_TYPE_ASP",
		4:  "FILE_TYPE_ASPX",
		5:  "FILE_TYPE_PYTHON",
		6:  "FILE_TYPE_PERL",
		7:  "FILE_TYPE_JPG_PHP",
		8:  "FILE_TYPE_GIF_PHP",
		9:  "FILE_TYPE_BMP_PHP",
		10: "FILE_TYPE_PNG_PHP",
		11: "FILE_TYPE_JP2_PHP",
		12: "FILE_TYPE_ICON_PHP",
		13: "FILE_TYPE_JPG_JSP",
		14: "FILE_TYPE_GIF_JSP",
		15: "FILE_TYPE_BMP_JSP",
		16: "FILE_TYPE_PNG_JSP",
		17: "FILE_TYPE_JP2_JSP",
		18: "FILE_TYPE_ICON_JSP",
		20: "FILE_TYPE_JPG_ASP",
		21: "FILE_TYPE_GIF_ASP",
		22: "FILE_TYPE_BMP_ASP",
		23: "FILE_TYPE_PNG_ASP",
		24: "FILE_TYPE_JP2_ASP",
		25: "FILE_TYPE_ICON_ASP",
		26: "FILE_TYPE_JPG_ASPX",
		27: "FILE_TYPE_GIF_ASPX",
		28: "FILE_TYPE_BMP_ASPX",
		29: "FILE_TYPE_PNG_ASPX",
		30: "FILE_TYPE_JP2_ASPX",
		31: "FILE_TYPE_ICON_ASPX",
		32: "FILE_TYPE_JPG_PYTHON",
		33: "FILE_TYPE_GIF_PYTHON",
		34: "FILE_TYPE_BMP_PYTHON",
		35: "FILE_TYPE_PNG_PYTHON",
		36: "FILE_TYPE_JP2_PYTHON",
		37: "FILE_TYPE_ICON_PYTHON",
		38: "FILE_TYPE_JPG_PERL",
		39: "FILE_TYPE_GIF_PERL",
		40: "FILE_TYPE_BMP_PERL",
		41: "FILE_TYPE_PNG_PERL",
		42: "FILE_TYPE_JP2_PERL",
		43: "FILE_TYPE_ICON_PERL",
		44: "FILE_TYPE_ELF",
		45: "FILE_TYPE_SHELL",
		46: "FILE_TYPE_PE",
		47: "FILE_TYPE_PDF",
		48: "FILE_TYPE_DOC",
		49: "FILE_TYPE_XLS",
		50: "FILE_TYPE_PPT",
		51: "FILE_TYPE_DLL",
		52: "FILE_TYPE_BAT",
		53: "FILE_TYPE_MSI",
		54: "FILE_TYPE_ODT",
		55: "FILE_TYPE_VB",
		56: "FILE_TYPE_JS",
		57: "FILE_TYPE_PS",
		58: "FILE_TYPE_PY",
		59: "FILE_TYPE_LNK",
		60: "FILE_TYPE_ARCHIVE",
	}
	FileTypeIdent_value = map[string]int32{
		"FILE_TYPE_UNKNOWN":     0,
		"FILE_TYPE_PHP":         1,
		"FILE_TYPE_JSP":         2,
		"FILE_TYPE_ASP":         3,
		"FILE_TYPE_ASPX":        4,
		"FILE_TYPE_PYTHON":      5,
		"FILE_TYPE_PERL":        6,
		"FILE_TYPE_JPG_PHP":     7,
		"FILE_TYPE_GIF_PHP":     8,
		"FILE_TYPE_BMP_PHP":     9,
		"FILE_TYPE_PNG_PHP":     10,
		"FILE_TYPE_JP2_PHP":     11,
		"FILE_TYPE_ICON_PHP":    12,
		"FILE_TYPE_JPG_JSP":     13,
		"FILE_TYPE_GIF_JSP":     14,
		"FILE_TYPE_BMP_JSP":     15,
		"FILE_TYPE_PNG_JSP":     16,
		"FILE_TYPE_JP2_JSP":     17,
		"FILE_TYPE_ICON_JSP":    18,
		"FILE_TYPE_JPG_ASP":     20,
		"FILE_TYPE_GIF_ASP":     21,
		"FILE_TYPE_BMP_ASP":     22,
		"FILE_TYPE_PNG_ASP":     23,
		"FILE_TYPE_JP2_ASP":     24,
		"FILE_TYPE_ICON_ASP":    25,
		"FILE_TYPE_JPG_ASPX":    26,
		"FILE_TYPE_GIF_ASPX":    27,
		"FILE_TYPE_BMP_ASPX":    28,
		"FILE_TYPE_PNG_ASPX":    29,
		"FILE_TYPE_JP2_ASPX":    30,
		"FILE_TYPE_ICON_ASPX":   31,
		"FILE_TYPE_JPG_PYTHON":  32,
		"FILE_TYPE_GIF_PYTHON":  33,
		"FILE_TYPE_BMP_PYTHON":  34,
		"FILE_TYPE_PNG_PYTHON":  35,
		"FILE_TYPE_JP2_PYTHON":  36,
		"FILE_TYPE_ICON_PYTHON": 37,
		"FILE_TYPE_JPG_PERL":    38,
		"FILE_TYPE_GIF_PERL":    39,
		"FILE_TYPE_BMP_PERL":    40,
		"FILE_TYPE_PNG_PERL":    41,
		"FILE_TYPE_JP2_PERL":    42,
		"FILE_TYPE_ICON_PERL":   43,
		"FILE_TYPE_ELF":         44,
		"FILE_TYPE_SHELL":       45,
		"FILE_TYPE_PE":          46,
		"FILE_TYPE_PDF":         47,
		"FILE_TYPE_DOC":         48,
		"FILE_TYPE_XLS":         49,
		"FILE_TYPE_PPT":         50,
		"FILE_TYPE_DLL":         51,
		"FILE_TYPE_BAT":         52,
		"FILE_TYPE_MSI":         53,
		"FILE_TYPE_ODT":         54,
		"FILE_TYPE_VB":          55,
		"FILE_TYPE_JS":          56,
		"FILE_TYPE_PS":          57,
		"FILE_TYPE_PY":          58,
		"FILE_TYPE_LNK":         59,
		"FILE_TYPE_ARCHIVE":     60,
	}
)

func (x FileTypeIdent) Enum() *FileTypeIdent {
	p := new(FileTypeIdent)
	*p = x
	return p
}

func (x FileTypeIdent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileTypeIdent) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[4].Descriptor()
}

func (FileTypeIdent) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[4]
}

func (x FileTypeIdent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileTypeIdent.Descriptor instead.
func (FileTypeIdent) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{4}
}

type SignStatus int32

const (
	SignStatus_SS_UnKnown          SignStatus = 0 //证书状态未知
	SignStatus_SS_NoSignature      SignStatus = 1 //无证书
	SignStatus_SS_Trusted          SignStatus = 2 //受信任的证书
	SignStatus_SS_Expired          SignStatus = 3 //证书已过期
	SignStatus_SS_Revoked          SignStatus = 4 //证书已被撤销
	SignStatus_SS_Distrust         SignStatus = 5 //不受信任的证书
	SignStatus_SS_SecuritySettings SignStatus = 6 //由于本地安全策略导致证书未通过验证
	SignStatus_SS_BadSignature     SignStatus = 7 // 证书数字签名未通过验证
)

// Enum value maps for SignStatus.
var (
	SignStatus_name = map[int32]string{
		0: "SS_UnKnown",
		1: "SS_NoSignature",
		2: "SS_Trusted",
		3: "SS_Expired",
		4: "SS_Revoked",
		5: "SS_Distrust",
		6: "SS_SecuritySettings",
		7: "SS_BadSignature",
	}
	SignStatus_value = map[string]int32{
		"SS_UnKnown":          0,
		"SS_NoSignature":      1,
		"SS_Trusted":          2,
		"SS_Expired":          3,
		"SS_Revoked":          4,
		"SS_Distrust":         5,
		"SS_SecuritySettings": 6,
		"SS_BadSignature":     7,
	}
)

func (x SignStatus) Enum() *SignStatus {
	p := new(SignStatus)
	*p = x
	return p
}

func (x SignStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_common_proto_enumTypes[5].Descriptor()
}

func (SignStatus) Type() protoreflect.EnumType {
	return &file_agent_common_proto_enumTypes[5]
}

func (x SignStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignStatus.Descriptor instead.
func (SignStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{5}
}

type ClientID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateTime string `protobuf:"bytes,2,opt,name=dateTime,proto3" json:"dateTime,omitempty"` //事件发生的大致时间
	// v01新增
	ClientTime    uint64 `protobuf:"varint,20,opt,name=clientTime,proto3" json:"clientTime,omitempty"`      // 客户端发现时间
	ClientVersion string `protobuf:"bytes,30,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"` // 客户端版本信息
}

func (x *ClientID) Reset() {
	*x = ClientID{}
	mi := &file_agent_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientID) ProtoMessage() {}

func (x *ClientID) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientID.ProtoReflect.Descriptor instead.
func (*ClientID) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{0}
}

func (x *ClientID) GetDateTime() string {
	if x != nil {
		return x.DateTime
	}
	return ""
}

func (x *ClientID) GetClientTime() uint64 {
	if x != nil {
		return x.ClientTime
	}
	return 0
}

func (x *ClientID) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

// --------------------------------------------------
//
//	风险识别头
//
// --------------------------------------------------
type RiskHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level       uint32 `protobuf:"varint,1,opt,name=Level,proto3" json:"Level,omitempty"`             // 默认为0 风险等级 0：无风险 1：低风险
	Comment     []byte `protobuf:"bytes,2,opt,name=Comment,proto3" json:"Comment,omitempty"`          // 攻击特征描述
	OperateFlag uint32 `protobuf:"varint,3,opt,name=operateFlag,proto3" json:"operateFlag,omitempty"` // 处理标志
	UniqueFlag  []byte `protobuf:"bytes,4,opt,name=uniqueFlag,proto3" json:"uniqueFlag,omitempty"`    // 唯一的处理相关信息
}

func (x *RiskHeader) Reset() {
	*x = RiskHeader{}
	mi := &file_agent_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskHeader) ProtoMessage() {}

func (x *RiskHeader) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskHeader.ProtoReflect.Descriptor instead.
func (*RiskHeader) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{1}
}

func (x *RiskHeader) GetLevel() uint32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RiskHeader) GetComment() []byte {
	if x != nil {
		return x.Comment
	}
	return nil
}

func (x *RiskHeader) GetOperateFlag() uint32 {
	if x != nil {
		return x.OperateFlag
	}
	return 0
}

func (x *RiskHeader) GetUniqueFlag() []byte {
	if x != nil {
		return x.UniqueFlag
	}
	return nil
}

type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PID          uint32 `protobuf:"varint,1,opt,name=PID,proto3" json:"PID,omitempty"`                   // 当前进程ID
	ProcessName  []byte `protobuf:"bytes,2,opt,name=ProcessName,proto3" json:"ProcessName,omitempty"`    // 当前进程名
	FilePath     []byte `protobuf:"bytes,3,opt,name=FilePath,proto3" json:"FilePath,omitempty"`          // 进程路径
	FileSha256   []byte `protobuf:"bytes,4,opt,name=FileSha256,proto3" json:"FileSha256,omitempty"`      // 文件sha256
	FileMd5      []byte `protobuf:"bytes,7,opt,name=FileMd5,proto3" json:"FileMd5,omitempty"`            // 文件md5
	CommandLine  []byte `protobuf:"bytes,5,opt,name=CommandLine,proto3" json:"CommandLine,omitempty"`    // 进程命令行
	IsX86Process uint32 `protobuf:"varint,6,opt,name=IsX86Process,proto3" json:"IsX86Process,omitempty"` // 是否是32位进程
	UserName     []byte `protobuf:"bytes,8,opt,name=UserName,proto3" json:"UserName,omitempty"`          // 进程所属用户
	// linux神甲在不断迭代，所以序号20开始，已防冲突
	CurrentCreateTime  uint64           `protobuf:"varint,20,opt,name=CurrentCreateTime,proto3" json:"CurrentCreateTime,omitempty"`   //进程启动时间 unix时间戳
	PPID               uint32           `protobuf:"varint,21,opt,name=PPID,proto3" json:"PPID,omitempty"`                             // 当前进程父进程ID
	ParentCreateTime   uint64           `protobuf:"varint,22,opt,name=ParentCreateTime,proto3" json:"ParentCreateTime,omitempty"`     //当前进程父进程启动时间 unix时间戳
	FileSha1           []byte           `protobuf:"bytes,23,opt,name=FileSha1,proto3" json:"FileSha1,omitempty"`                      // 文件sha1
	FileCreateTime     uint64           `protobuf:"varint,24,opt,name=FileCreateTime,proto3" json:"FileCreateTime,omitempty"`         //进程文件创建时间 unix时间戳
	FileModifyTime     uint64           `protobuf:"varint,25,opt,name=FileModifyTime,proto3" json:"FileModifyTime,omitempty"`         //进程文件修改时间 unix时间戳
	FileLastAccessTime uint64           `protobuf:"varint,26,opt,name=FileLastAccessTime,proto3" json:"FileLastAccessTime,omitempty"` //进程文件最后访问时间 unix时间戳
	FileSize           uint64           `protobuf:"varint,27,opt,name=FileSize,proto3" json:"FileSize,omitempty"`                     //进程文件大小
	FileCompanyName    []byte           `protobuf:"bytes,28,opt,name=FileCompanyName,proto3" json:"FileCompanyName,omitempty"`        // 进程文件产商
	FileVersion        []byte           `protobuf:"bytes,29,opt,name=FileVersion,proto3" json:"FileVersion,omitempty"`                // 进程文件版本
	AccessToken        []byte           `protobuf:"bytes,30,opt,name=AccessToken,proto3" json:"AccessToken,omitempty"`                // 进程特权
	User               []byte           `protobuf:"bytes,31,opt,name=user,proto3" json:"user,omitempty"`                              //进程所属用户
	SignatureInfo      []*SignatureInfo `protobuf:"bytes,32,rep,name=signatureInfo,proto3" json:"signatureInfo,omitempty"`            // 进程文件签名信息
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	mi := &file_agent_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessInfo) GetPID() uint32 {
	if x != nil {
		return x.PID
	}
	return 0
}

func (x *ProcessInfo) GetProcessName() []byte {
	if x != nil {
		return x.ProcessName
	}
	return nil
}

func (x *ProcessInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *ProcessInfo) GetFileSha256() []byte {
	if x != nil {
		return x.FileSha256
	}
	return nil
}

func (x *ProcessInfo) GetFileMd5() []byte {
	if x != nil {
		return x.FileMd5
	}
	return nil
}

func (x *ProcessInfo) GetCommandLine() []byte {
	if x != nil {
		return x.CommandLine
	}
	return nil
}

func (x *ProcessInfo) GetIsX86Process() uint32 {
	if x != nil {
		return x.IsX86Process
	}
	return 0
}

func (x *ProcessInfo) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *ProcessInfo) GetCurrentCreateTime() uint64 {
	if x != nil {
		return x.CurrentCreateTime
	}
	return 0
}

func (x *ProcessInfo) GetPPID() uint32 {
	if x != nil {
		return x.PPID
	}
	return 0
}

func (x *ProcessInfo) GetParentCreateTime() uint64 {
	if x != nil {
		return x.ParentCreateTime
	}
	return 0
}

func (x *ProcessInfo) GetFileSha1() []byte {
	if x != nil {
		return x.FileSha1
	}
	return nil
}

func (x *ProcessInfo) GetFileCreateTime() uint64 {
	if x != nil {
		return x.FileCreateTime
	}
	return 0
}

func (x *ProcessInfo) GetFileModifyTime() uint64 {
	if x != nil {
		return x.FileModifyTime
	}
	return 0
}

func (x *ProcessInfo) GetFileLastAccessTime() uint64 {
	if x != nil {
		return x.FileLastAccessTime
	}
	return 0
}

func (x *ProcessInfo) GetFileSize() uint64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *ProcessInfo) GetFileCompanyName() []byte {
	if x != nil {
		return x.FileCompanyName
	}
	return nil
}

func (x *ProcessInfo) GetFileVersion() []byte {
	if x != nil {
		return x.FileVersion
	}
	return nil
}

func (x *ProcessInfo) GetAccessToken() []byte {
	if x != nil {
		return x.AccessToken
	}
	return nil
}

func (x *ProcessInfo) GetUser() []byte {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *ProcessInfo) GetSignatureInfo() []*SignatureInfo {
	if x != nil {
		return x.SignatureInfo
	}
	return nil
}

// 网络5元素上报用结构
type NetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port    uint32 `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`      //端口
	Address string `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"` //地址信息
}

func (x *NetInfo) Reset() {
	*x = NetInfo{}
	mi := &file_agent_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetInfo) ProtoMessage() {}

func (x *NetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetInfo.ProtoReflect.Descriptor instead.
func (*NetInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{3}
}

func (x *NetInfo) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *NetInfo) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

type OpFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=FileName,proto3" json:"FileName,omitempty"`
	FilePath string `protobuf:"bytes,2,opt,name=FilePath,proto3" json:"FilePath,omitempty"` //文件路径
	FileMd5  string `protobuf:"bytes,3,opt,name=FileMd5,proto3" json:"FileMd5,omitempty"`
	StMode   string `protobuf:"bytes,4,opt,name=StMode,proto3" json:"StMode,omitempty"`      // 文件权限，ge: rwxr-xr--
	FileSize uint32 `protobuf:"varint,5,opt,name=FileSize,proto3" json:"FileSize,omitempty"` // 文件大小，单位字节
	Atime    string `protobuf:"bytes,6,opt,name=atime,proto3" json:"atime,omitempty"`        //最后一次访问文件时间
	Mtime    string `protobuf:"bytes,7,opt,name=mtime,proto3" json:"mtime,omitempty"`        // 最后一次文件修改时间
	Ctime    string `protobuf:"bytes,8,opt,name=ctime,proto3" json:"ctime,omitempty"`        // 最后一次文件改变时间
}

func (x *OpFileInfo) Reset() {
	*x = OpFileInfo{}
	mi := &file_agent_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpFileInfo) ProtoMessage() {}

func (x *OpFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpFileInfo.ProtoReflect.Descriptor instead.
func (*OpFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{4}
}

func (x *OpFileInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *OpFileInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *OpFileInfo) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *OpFileInfo) GetStMode() string {
	if x != nil {
		return x.StMode
	}
	return ""
}

func (x *OpFileInfo) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *OpFileInfo) GetAtime() string {
	if x != nil {
		return x.Atime
	}
	return ""
}

func (x *OpFileInfo) GetMtime() string {
	if x != nil {
		return x.Mtime
	}
	return ""
}

func (x *OpFileInfo) GetCtime() string {
	if x != nil {
		return x.Ctime
	}
	return ""
}

type SignatureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Serial            []byte     `protobuf:"bytes,1,opt,name=Serial,proto3" json:"Serial,omitempty"`                                         // 签名序列号
	IssuerName        []byte     `protobuf:"bytes,2,opt,name=IssuerName,proto3" json:"IssuerName,omitempty"`                                 // 签名颁发者
	Customer          []byte     `protobuf:"bytes,3,opt,name=Customer,proto3" json:"Customer,omitempty"`                                     // 签名使用者
	Thumbprint        []byte     `protobuf:"bytes,4,opt,name=Thumbprint,proto3" json:"Thumbprint,omitempty"`                                 // 签名指纹
	Result            []byte     `protobuf:"bytes,5,opt,name=Result,proto3" json:"Result,omitempty"`                                         // 签名状态
	Description       []byte     `protobuf:"bytes,6,opt,name=Description,proto3" json:"Description,omitempty"`                               // 签名描述
	NotAfter          []byte     `protobuf:"bytes,7,opt,name=NotAfter,proto3" json:"NotAfter,omitempty"`                                     // 签名有效期起始时间
	NotBefore         []byte     `protobuf:"bytes,8,opt,name=NotBefore,proto3" json:"NotBefore,omitempty"`                                   // 签名有效期到期时间
	SignAlgorithm     []byte     `protobuf:"bytes,9,opt,name=SignAlgorithm,proto3" json:"SignAlgorithm,omitempty"`                           // 签名算法
	SignHashAlgorithm []byte     `protobuf:"bytes,10,opt,name=SignHashAlgorithm,proto3" json:"SignHashAlgorithm,omitempty"`                  // 签名哈希算法
	Version           []byte     `protobuf:"bytes,11,opt,name=Version,proto3" json:"Version,omitempty"`                                      // 签名版本
	SignStatusInfo    SignStatus `protobuf:"varint,12,opt,name=SignStatusInfo,proto3,enum=agent.SignStatus" json:"SignStatusInfo,omitempty"` //签名状态
}

func (x *SignatureInfo) Reset() {
	*x = SignatureInfo{}
	mi := &file_agent_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignatureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignatureInfo) ProtoMessage() {}

func (x *SignatureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignatureInfo.ProtoReflect.Descriptor instead.
func (*SignatureInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{5}
}

func (x *SignatureInfo) GetSerial() []byte {
	if x != nil {
		return x.Serial
	}
	return nil
}

func (x *SignatureInfo) GetIssuerName() []byte {
	if x != nil {
		return x.IssuerName
	}
	return nil
}

func (x *SignatureInfo) GetCustomer() []byte {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *SignatureInfo) GetThumbprint() []byte {
	if x != nil {
		return x.Thumbprint
	}
	return nil
}

func (x *SignatureInfo) GetResult() []byte {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SignatureInfo) GetDescription() []byte {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *SignatureInfo) GetNotAfter() []byte {
	if x != nil {
		return x.NotAfter
	}
	return nil
}

func (x *SignatureInfo) GetNotBefore() []byte {
	if x != nil {
		return x.NotBefore
	}
	return nil
}

func (x *SignatureInfo) GetSignAlgorithm() []byte {
	if x != nil {
		return x.SignAlgorithm
	}
	return nil
}

func (x *SignatureInfo) GetSignHashAlgorithm() []byte {
	if x != nil {
		return x.SignHashAlgorithm
	}
	return nil
}

func (x *SignatureInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *SignatureInfo) GetSignStatusInfo() SignStatus {
	if x != nil {
		return x.SignStatusInfo
	}
	return SignStatus_SS_UnKnown
}

type ReportEvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvidenceType EvidenceType `protobuf:"varint,1,opt,name=evidence_type,json=evidenceType,proto3,enum=agent.EvidenceType" json:"evidence_type,omitempty"` //证据类型
	Uniqueflag   string       `protobuf:"bytes,2,opt,name=uniqueflag,proto3" json:"uniqueflag,omitempty"`                                                  //证据唯一标识
	EvidenceSize uint64       `protobuf:"varint,3,opt,name=evidenceSize,proto3" json:"evidenceSize,omitempty"`                                             //证据大小
	Filepath     string       `protobuf:"bytes,4,opt,name=filepath,proto3" json:"filepath,omitempty"`
	Filename     string       `protobuf:"bytes,5,opt,name=filename,proto3" json:"filename,omitempty"`
	Md5          string       `protobuf:"bytes,6,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256       string       `protobuf:"bytes,7,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Atime        int64        `protobuf:"varint,8,opt,name=atime,proto3" json:"atime,omitempty"`
	Mtime        int64        `protobuf:"varint,9,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Ctime        int64        `protobuf:"varint,10,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Filesize     uint64       `protobuf:"varint,11,opt,name=filesize,proto3" json:"filesize,omitempty"`
}

func (x *ReportEvidenceInfo) Reset() {
	*x = ReportEvidenceInfo{}
	mi := &file_agent_common_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportEvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportEvidenceInfo) ProtoMessage() {}

func (x *ReportEvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportEvidenceInfo.ProtoReflect.Descriptor instead.
func (*ReportEvidenceInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{6}
}

func (x *ReportEvidenceInfo) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_UNKNOWN
}

func (x *ReportEvidenceInfo) GetUniqueflag() string {
	if x != nil {
		return x.Uniqueflag
	}
	return ""
}

func (x *ReportEvidenceInfo) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *ReportEvidenceInfo) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *ReportEvidenceInfo) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *ReportEvidenceInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *ReportEvidenceInfo) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *ReportEvidenceInfo) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *ReportEvidenceInfo) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *ReportEvidenceInfo) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *ReportEvidenceInfo) GetFilesize() uint64 {
	if x != nil {
		return x.Filesize
	}
	return 0
}

type ProcFileDetailInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InternalName     string `protobuf:"bytes,1,opt,name=InternalName,proto3" json:"InternalName,omitempty"`          // 内部名称
	ProductName      string `protobuf:"bytes,2,opt,name=ProductName,proto3" json:"ProductName,omitempty"`            // 产品名称
	CompanyName      string `protobuf:"bytes,3,opt,name=CompanyName,proto3" json:"CompanyName,omitempty"`            // 公司名称
	Copyright        string `protobuf:"bytes,4,opt,name=Copyright,proto3" json:"Copyright,omitempty"`                // 版权
	Version          string `protobuf:"bytes,5,opt,name=Version,proto3" json:"Version,omitempty"`                    // 版本
	Desc             string `protobuf:"bytes,6,opt,name=Desc,proto3" json:"Desc,omitempty"`                          // 描述
	Trademark        string `protobuf:"bytes,7,opt,name=Trademark,proto3" json:"Trademark,omitempty"`                // 商标信息
	BuildInfo        string `protobuf:"bytes,8,opt,name=BuildInfo,proto3" json:"BuildInfo,omitempty"`                // 构建信息
	VerCode          string `protobuf:"bytes,9,opt,name=VerCode,proto3" json:"VerCode,omitempty"`                    // 版本号
	OriginalInfo     string `protobuf:"bytes,10,opt,name=OriginalInfo,proto3" json:"OriginalInfo,omitempty"`         // 原始名称
	SpecialBuildInfo string `protobuf:"bytes,11,opt,name=SpecialBuildInfo,proto3" json:"SpecialBuildInfo,omitempty"` // 特殊构建信息
}

func (x *ProcFileDetailInfo) Reset() {
	*x = ProcFileDetailInfo{}
	mi := &file_agent_common_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcFileDetailInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcFileDetailInfo) ProtoMessage() {}

func (x *ProcFileDetailInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_common_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcFileDetailInfo.ProtoReflect.Descriptor instead.
func (*ProcFileDetailInfo) Descriptor() ([]byte, []int) {
	return file_agent_common_proto_rawDescGZIP(), []int{7}
}

func (x *ProcFileDetailInfo) GetInternalName() string {
	if x != nil {
		return x.InternalName
	}
	return ""
}

func (x *ProcFileDetailInfo) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *ProcFileDetailInfo) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *ProcFileDetailInfo) GetCopyright() string {
	if x != nil {
		return x.Copyright
	}
	return ""
}

func (x *ProcFileDetailInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ProcFileDetailInfo) GetDesc() string {
	if x != nil {
		return x.Desc
	}
	return ""
}

func (x *ProcFileDetailInfo) GetTrademark() string {
	if x != nil {
		return x.Trademark
	}
	return ""
}

func (x *ProcFileDetailInfo) GetBuildInfo() string {
	if x != nil {
		return x.BuildInfo
	}
	return ""
}

func (x *ProcFileDetailInfo) GetVerCode() string {
	if x != nil {
		return x.VerCode
	}
	return ""
}

func (x *ProcFileDetailInfo) GetOriginalInfo() string {
	if x != nil {
		return x.OriginalInfo
	}
	return ""
}

func (x *ProcFileDetailInfo) GetSpecialBuildInfo() string {
	if x != nil {
		return x.SpecialBuildInfo
	}
	return ""
}

var File_agent_common_proto protoreflect.FileDescriptor

var file_agent_common_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0x6c, 0x0a, 0x08, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x7e, 0x0a, 0x0a, 0x52, 0x69, 0x73,
	0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x22, 0xdd, 0x05, 0x0a, 0x0b, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x50, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x50, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x69, 0x6c,
	0x65, 0x4d, 0x64, 0x35, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x46, 0x69, 0x6c, 0x65,
	0x4d, 0x64, 0x35, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69,
	0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x49, 0x73, 0x58,
	0x38, 0x36, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x55, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x11, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x50, 0x49, 0x44, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x50, 0x50, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x10, 0x50, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x10, 0x50, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x12,
	0x26, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0e, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x12, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x61, 0x73, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x46,
	0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x46, 0x69, 0x6c, 0x65,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x3a, 0x0a,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x20,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x37, 0x0a, 0x07, 0x4e, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x22, 0xd4, 0x01, 0x0a, 0x0a, 0x4f, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x69, 0x6c,
	0x65, 0x4d, 0x64, 0x35, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x46, 0x69, 0x6c, 0x65,
	0x4d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xa0, 0x03, 0x0a, 0x0d, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x53,
	0x65, 0x72, 0x69, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x53, 0x65, 0x72,
	0x69, 0x61, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0a, 0x54, 0x68, 0x75, 0x6d, 0x62, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x74,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x4e, 0x6f, 0x74,
	0x41, 0x66, 0x74, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x6f, 0x74, 0x42, 0x65, 0x66, 0x6f,
	0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x4e, 0x6f, 0x74, 0x42, 0x65, 0x66,
	0x6f, 0x72, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x41, 0x6c, 0x67, 0x6f, 0x72,
	0x69, 0x74, 0x68, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x53, 0x69, 0x67, 0x6e,
	0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x69, 0x67,
	0x6e, 0x48, 0x61, 0x73, 0x68, 0x41, 0x6c, 0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x48, 0x61, 0x73, 0x68, 0x41, 0x6c,
	0x67, 0x6f, 0x72, 0x69, 0x74, 0x68, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x39, 0x0a, 0x0e, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x53, 0x69,
	0x67, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd2, 0x02, 0x0a,
	0x12, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x22, 0x0a,
	0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x22, 0xee, 0x02, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x65, 0x73, 0x63,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x44, 0x65, 0x73, 0x63, 0x12, 0x1c, 0x0a, 0x09,
	0x54, 0x72, 0x61, 0x64, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x54, 0x72, 0x61, 0x64, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x75,
	0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x56, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x10, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61,
	0x6c, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x53, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x2a, 0xae, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x4f, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x45, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a,
	0x0d, 0x46, 0x4f, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x46, 0x49, 0x4c, 0x45, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x4f, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x4e, 0x41, 0x4d, 0x45,
	0x46, 0x49, 0x4c, 0x45, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x4f, 0x5f, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x10, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x4f, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x20, 0x12, 0x14, 0x0a,
	0x10, 0x46, 0x4f, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x40, 0x2a, 0x44, 0x0a, 0x10, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x50, 0x5f, 0x54, 0x43,
	0x50, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x50, 0x5f, 0x55, 0x44, 0x50, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x50, 0x5f, 0x54, 0x43, 0x50, 0x36, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x50, 0x5f, 0x55, 0x44, 0x50, 0x36, 0x10, 0x03, 0x2a, 0xe2, 0x01, 0x0a, 0x0e, 0x4e, 0x65,
	0x77, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x0f, 0x0a, 0x0b,
	0x4e, 0x46, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12,
	0x11, 0x0a, 0x0d, 0x4e, 0x46, 0x4f, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x46, 0x49, 0x4c, 0x45,
	0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x46, 0x4f, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x46, 0x4f, 0x5f, 0x52,
	0x45, 0x4e, 0x41, 0x4d, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x4e,
	0x46, 0x4f, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x05, 0x12, 0x12, 0x0a,
	0x0e, 0x4e, 0x46, 0x4f, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x46, 0x49, 0x4c, 0x45, 0x10,
	0x06, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x46, 0x4f, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x46,
	0x49, 0x4c, 0x45, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x46, 0x4f, 0x5f, 0x4c, 0x49, 0x4e,
	0x4b, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x46, 0x4f, 0x5f, 0x43,
	0x48, 0x41, 0x54, 0x54, 0x52, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x4e,
	0x46, 0x4f, 0x5f, 0x43, 0x48, 0x4d, 0x4f, 0x44, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x0a, 0x2a, 0xfe,
	0x01, 0x0a, 0x0c, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x19, 0x0a, 0x15, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x56,
	0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x56, 0x49,
	0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4d,
	0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x10, 0x08, 0x12, 0x1c, 0x0a, 0x18,
	0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x59,
	0x53, 0x54, 0x45, 0x4d, 0x5f, 0x4c, 0x4f, 0x47, 0x10, 0x10, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56,
	0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x4f,
	0x52, 0x59, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x44, 0x55, 0x4d, 0x50, 0x10, 0x20, 0x12, 0x20, 0x0a,
	0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x40, 0x2a,
	0xc1, 0x0a, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x48, 0x50, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x53, 0x50, 0x10, 0x02, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x53, 0x50, 0x10,
	0x03, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41,
	0x53, 0x50, 0x58, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x4c, 0x10, 0x06, 0x12,
	0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x47,
	0x5f, 0x50, 0x48, 0x50, 0x10, 0x07, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x5f, 0x50, 0x48, 0x50, 0x10, 0x08, 0x12, 0x15, 0x0a,
	0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4d, 0x50, 0x5f, 0x50,
	0x48, 0x50, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x50, 0x4e, 0x47, 0x5f, 0x50, 0x48, 0x50, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x32, 0x5f, 0x50, 0x48, 0x50,
	0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x50, 0x48, 0x50, 0x10, 0x0c, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x47, 0x5f, 0x4a, 0x53, 0x50, 0x10,
	0x0d, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47,
	0x49, 0x46, 0x5f, 0x4a, 0x53, 0x50, 0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4d, 0x50, 0x5f, 0x4a, 0x53, 0x50, 0x10, 0x0f, 0x12,
	0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47,
	0x5f, 0x4a, 0x53, 0x50, 0x10, 0x10, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x32, 0x5f, 0x4a, 0x53, 0x50, 0x10, 0x11, 0x12, 0x16, 0x0a,
	0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f,
	0x4a, 0x53, 0x50, 0x10, 0x12, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4a, 0x50, 0x47, 0x5f, 0x41, 0x53, 0x50, 0x10, 0x14, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x5f, 0x41, 0x53,
	0x50, 0x10, 0x15, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x42, 0x4d, 0x50, 0x5f, 0x41, 0x53, 0x50, 0x10, 0x16, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47, 0x5f, 0x41, 0x53, 0x50, 0x10,
	0x17, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a,
	0x50, 0x32, 0x5f, 0x41, 0x53, 0x50, 0x10, 0x18, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x41, 0x53, 0x50, 0x10, 0x19,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50,
	0x47, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10, 0x1a, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10, 0x1b,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4d,
	0x50, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10, 0x1c, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10, 0x1d,
	0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50,
	0x32, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10, 0x1e, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x41, 0x53, 0x50, 0x58, 0x10,
	0x1f, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a,
	0x50, 0x47, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x18, 0x0a, 0x14, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x5f, 0x50, 0x59, 0x54,
	0x48, 0x4f, 0x4e, 0x10, 0x21, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x42, 0x4d, 0x50, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x22, 0x12,
	0x18, 0x0a, 0x14, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47,
	0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x23, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x32, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f,
	0x4e, 0x10, 0x24, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x50, 0x59, 0x54, 0x48, 0x4f, 0x4e, 0x10, 0x25, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x47, 0x5f,
	0x50, 0x45, 0x52, 0x4c, 0x10, 0x26, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x47, 0x49, 0x46, 0x5f, 0x50, 0x45, 0x52, 0x4c, 0x10, 0x27, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4d, 0x50, 0x5f,
	0x50, 0x45, 0x52, 0x4c, 0x10, 0x28, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e, 0x47, 0x5f, 0x50, 0x45, 0x52, 0x4c, 0x10, 0x29, 0x12, 0x16,
	0x0a, 0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50, 0x32, 0x5f,
	0x50, 0x45, 0x52, 0x4c, 0x10, 0x2a, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x52, 0x4c, 0x10, 0x2b, 0x12,
	0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4c, 0x46,
	0x10, 0x2c, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x53, 0x48, 0x45, 0x4c, 0x4c, 0x10, 0x2d, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x10, 0x2e, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x44, 0x46, 0x10, 0x2f, 0x12, 0x11, 0x0a, 0x0d,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x10, 0x30, 0x12,
	0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x4c, 0x53,
	0x10, 0x31, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x50, 0x50, 0x54, 0x10, 0x32, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x44, 0x4c, 0x4c, 0x10, 0x33, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x10, 0x34, 0x12, 0x11, 0x0a, 0x0d, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x53, 0x49, 0x10, 0x35, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x44, 0x54, 0x10,
	0x36, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56,
	0x42, 0x10, 0x37, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x4a, 0x53, 0x10, 0x38, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x53, 0x10, 0x39, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x59, 0x10, 0x3a, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4e, 0x4b, 0x10, 0x3b, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56,
	0x45, 0x10, 0x3c, 0x2a, 0x9f, 0x01, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x53, 0x5f, 0x55, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x53, 0x5f, 0x4e, 0x6f, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x53, 0x5f, 0x54, 0x72, 0x75,
	0x73, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x53, 0x5f, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x64, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x53, 0x5f, 0x52, 0x65, 0x76,
	0x6f, 0x6b, 0x65, 0x64, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x53, 0x5f, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x75, 0x73, 0x74, 0x10, 0x05, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x53, 0x5f, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x10, 0x06,
	0x12, 0x13, 0x0a, 0x0f, 0x53, 0x53, 0x5f, 0x42, 0x61, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x10, 0x07, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_common_proto_rawDescOnce sync.Once
	file_agent_common_proto_rawDescData = file_agent_common_proto_rawDesc
)

func file_agent_common_proto_rawDescGZIP() []byte {
	file_agent_common_proto_rawDescOnce.Do(func() {
		file_agent_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_common_proto_rawDescData)
	})
	return file_agent_common_proto_rawDescData
}

var file_agent_common_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_agent_common_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_agent_common_proto_goTypes = []any{
	(FileOperate)(0),           // 0: agent.FileOperate
	(InternetProtocol)(0),      // 1: agent.InternetProtocol
	(NewFileOperate)(0),        // 2: agent.NewFileOperate
	(EvidenceType)(0),          // 3: agent.EvidenceType
	(FileTypeIdent)(0),         // 4: agent.FileTypeIdent
	(SignStatus)(0),            // 5: agent.SignStatus
	(*ClientID)(nil),           // 6: agent.ClientID
	(*RiskHeader)(nil),         // 7: agent.RiskHeader
	(*ProcessInfo)(nil),        // 8: agent.ProcessInfo
	(*NetInfo)(nil),            // 9: agent.NetInfo
	(*OpFileInfo)(nil),         // 10: agent.OpFileInfo
	(*SignatureInfo)(nil),      // 11: agent.SignatureInfo
	(*ReportEvidenceInfo)(nil), // 12: agent.ReportEvidenceInfo
	(*ProcFileDetailInfo)(nil), // 13: agent.ProcFileDetailInfo
}
var file_agent_common_proto_depIdxs = []int32{
	11, // 0: agent.ProcessInfo.signatureInfo:type_name -> agent.SignatureInfo
	5,  // 1: agent.SignatureInfo.SignStatusInfo:type_name -> agent.SignStatus
	3,  // 2: agent.ReportEvidenceInfo.evidence_type:type_name -> agent.EvidenceType
	3,  // [3:3] is the sub-list for method output_type
	3,  // [3:3] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_agent_common_proto_init() }
func file_agent_common_proto_init() {
	if File_agent_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_common_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_common_proto_goTypes,
		DependencyIndexes: file_agent_common_proto_depIdxs,
		EnumInfos:         file_agent_common_proto_enumTypes,
		MessageInfos:      file_agent_common_proto_msgTypes,
	}.Build()
	File_agent_common_proto = out.File
	file_agent_common_proto_rawDesc = nil
	file_agent_common_proto_goTypes = nil
	file_agent_common_proto_depIdxs = nil
}
