// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_intercept.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------------------------
//
//	文件风险 识别结果
//	对应 g_CmdMemProtectvirusFileInfocd
//
// --------------------------------------------------
type MemProtectAutoInterceptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo          *ClientID            `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	AutoInterceptList []*AutoInterceptInfo `protobuf:"bytes,2,rep,name=autoInterceptList,proto3" json:"autoInterceptList,omitempty"`
}

func (x *MemProtectAutoInterceptInfo) Reset() {
	*x = MemProtectAutoInterceptInfo{}
	mi := &file_agent_risk_intercept_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectAutoInterceptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectAutoInterceptInfo) ProtoMessage() {}

func (x *MemProtectAutoInterceptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_intercept_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectAutoInterceptInfo.ProtoReflect.Descriptor instead.
func (*MemProtectAutoInterceptInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_intercept_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectAutoInterceptInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectAutoInterceptInfo) GetAutoInterceptList() []*AutoInterceptInfo {
	if x != nil {
		return x.AutoInterceptList
	}
	return nil
}

type AutoInterceptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *RiskHeader `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	DetailInfo string      `protobuf:"bytes,2,opt,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"` // 详细信息，json序列化后的字符串
	PolicyId   int64       `protobuf:"varint,3,opt,name=policy_id,json=policyId,proto3" json:"policy_id,omitempty"`      // 命中的策略ID
}

func (x *AutoInterceptInfo) Reset() {
	*x = AutoInterceptInfo{}
	mi := &file_agent_risk_intercept_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoInterceptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoInterceptInfo) ProtoMessage() {}

func (x *AutoInterceptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_intercept_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoInterceptInfo.ProtoReflect.Descriptor instead.
func (*AutoInterceptInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_intercept_proto_rawDescGZIP(), []int{1}
}

func (x *AutoInterceptInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AutoInterceptInfo) GetDetailInfo() string {
	if x != nil {
		return x.DetailInfo
	}
	return ""
}

func (x *AutoInterceptInfo) GetPolicyId() int64 {
	if x != nil {
		return x.PolicyId
	}
	return 0
}

var File_agent_risk_intercept_proto protoreflect.FileDescriptor

var file_agent_risk_intercept_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x92, 0x01, 0x0a, 0x1b, 0x4d, 0x65, 0x6d, 0x50,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x41, 0x75, 0x74, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63,
	0x65, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x46, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x63, 0x65, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x75, 0x74, 0x6f, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x63, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x61, 0x75, 0x74, 0x6f, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x7c, 0x0a, 0x11,
	0x41, 0x75, 0x74, 0x6f, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x29, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x64, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69,
	0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_intercept_proto_rawDescOnce sync.Once
	file_agent_risk_intercept_proto_rawDescData = file_agent_risk_intercept_proto_rawDesc
)

func file_agent_risk_intercept_proto_rawDescGZIP() []byte {
	file_agent_risk_intercept_proto_rawDescOnce.Do(func() {
		file_agent_risk_intercept_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_intercept_proto_rawDescData)
	})
	return file_agent_risk_intercept_proto_rawDescData
}

var file_agent_risk_intercept_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_agent_risk_intercept_proto_goTypes = []any{
	(*MemProtectAutoInterceptInfo)(nil), // 0: agent.MemProtectAutoInterceptInfo
	(*AutoInterceptInfo)(nil),           // 1: agent.AutoInterceptInfo
	(*ClientID)(nil),                    // 2: agent.ClientID
	(*RiskHeader)(nil),                  // 3: agent.RiskHeader
}
var file_agent_risk_intercept_proto_depIdxs = []int32{
	2, // 0: agent.MemProtectAutoInterceptInfo.baseInfo:type_name -> agent.ClientID
	1, // 1: agent.MemProtectAutoInterceptInfo.autoInterceptList:type_name -> agent.AutoInterceptInfo
	3, // 2: agent.AutoInterceptInfo.Header:type_name -> agent.RiskHeader
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_agent_risk_intercept_proto_init() }
func file_agent_risk_intercept_proto_init() {
	if File_agent_risk_intercept_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_intercept_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_intercept_proto_goTypes,
		DependencyIndexes: file_agent_risk_intercept_proto_depIdxs,
		MessageInfos:      file_agent_risk_intercept_proto_msgTypes,
	}.Build()
	File_agent_risk_intercept_proto = out.File
	file_agent_risk_intercept_proto_rawDesc = nil
	file_agent_risk_intercept_proto_goTypes = nil
	file_agent_risk_intercept_proto_depIdxs = nil
}
