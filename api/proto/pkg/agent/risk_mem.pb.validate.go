// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_mem.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskMemInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskMemInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskMemInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskMemInfoMultiError, or nil if none found.
func (m *MemProtectRiskMemInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskMemInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskMemInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskMemInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskMemInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoadRemoteLibList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("LoadRemoteLibList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("LoadRemoteLibList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("LoadRemoteLibList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetVirProtectExStkSpaceList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("VirProtectExStkSpaceList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("VirProtectExStkSpaceList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("VirProtectExStkSpaceList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReturnStackExecList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("ReturnStackExecList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("ReturnStackExecList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("ReturnStackExecList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRemoteThreadInject() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("RemoteThreadInject[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("RemoteThreadInject[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("RemoteThreadInject[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemNoFileAttackList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemNoFileAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemNoFileAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemNoFileAttackList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemHeapSprayList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemHeapSprayList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemHeapSprayList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemHeapSprayList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAnalyzerVirusList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("AnalyzerVirusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("AnalyzerVirusList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("AnalyzerVirusList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemRopList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemRopList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemRopList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemRopList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemLayoutShellCodeList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemLayoutShellCodeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemLayoutShellCodeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemLayoutShellCodeList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemStackPivotList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemStackPivotList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemStackPivotList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemStackPivotList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemRunningShellCodeList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemRunningShellCodeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemRunningShellCodeList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemRunningShellCodeList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemStartProcessList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemStartProcessList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemStartProcessList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemStartProcessList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMemEngineAttackList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemEngineAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("MemEngineAttackList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("MemEngineAttackList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetTunnelRiskList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("TunnelRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("TunnelRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("TunnelRiskList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEtwRiskList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("EtwRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("EtwRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("EtwRiskList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetKernelVulnerableDriverRiskList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("KernelVulnerableDriverRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskMemInfoValidationError{
						field:  fmt.Sprintf("KernelVulnerableDriverRiskList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskMemInfoValidationError{
					field:  fmt.Sprintf("KernelVulnerableDriverRiskList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskMemInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskMemInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskMemInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskMemInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskMemInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskMemInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskMemInfoValidationError is the validation error returned by
// MemProtectRiskMemInfo.Validate if the designated constraints aren't met.
type MemProtectRiskMemInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskMemInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskMemInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskMemInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskMemInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskMemInfoValidationError) ErrorName() string {
	return "MemProtectRiskMemInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskMemInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskMemInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskMemInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskMemInfoValidationError{}

// Validate checks the field values on AnalyzerVirus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AnalyzerVirus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalyzerVirus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AnalyzerVirusMultiError, or
// nil if none found.
func (m *AnalyzerVirus) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalyzerVirus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalyzerVirusValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalyzerVirusValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalyzerVirusValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImagePath

	// no validation rules for VirusName

	// no validation rules for VirusDesc

	// no validation rules for ProcessID

	// no validation rules for ProcessMd5

	if len(errors) > 0 {
		return AnalyzerVirusMultiError(errors)
	}

	return nil
}

// AnalyzerVirusMultiError is an error wrapping multiple validation errors
// returned by AnalyzerVirus.ValidateAll() if the designated constraints
// aren't met.
type AnalyzerVirusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalyzerVirusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalyzerVirusMultiError) AllErrors() []error { return m }

// AnalyzerVirusValidationError is the validation error returned by
// AnalyzerVirus.Validate if the designated constraints aren't met.
type AnalyzerVirusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalyzerVirusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalyzerVirusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalyzerVirusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalyzerVirusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalyzerVirusValidationError) ErrorName() string { return "AnalyzerVirusValidationError" }

// Error satisfies the builtin error interface
func (e AnalyzerVirusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalyzerVirus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalyzerVirusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalyzerVirusValidationError{}

// Validate checks the field values on LoadRemoteLib with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoadRemoteLib) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoadRemoteLib with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoadRemoteLibMultiError, or
// nil if none found.
func (m *LoadRemoteLib) ValidateAll() error {
	return m.validate(true)
}

func (m *LoadRemoteLib) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoadRemoteLibValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoadRemoteLibValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoadRemoteLibValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessID

	// no validation rules for IsX86Process

	// no validation rules for ImagePath

	// no validation rules for RemoteLibPath

	// no validation rules for ProcessMd5

	// no validation rules for ReportID

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoadRemoteLibValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoadRemoteLibValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoadRemoteLibValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoadRemoteLibValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoadRemoteLibValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoadRemoteLibValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	if len(errors) > 0 {
		return LoadRemoteLibMultiError(errors)
	}

	return nil
}

// LoadRemoteLibMultiError is an error wrapping multiple validation errors
// returned by LoadRemoteLib.ValidateAll() if the designated constraints
// aren't met.
type LoadRemoteLibMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoadRemoteLibMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoadRemoteLibMultiError) AllErrors() []error { return m }

// LoadRemoteLibValidationError is the validation error returned by
// LoadRemoteLib.Validate if the designated constraints aren't met.
type LoadRemoteLibValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoadRemoteLibValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoadRemoteLibValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoadRemoteLibValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoadRemoteLibValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoadRemoteLibValidationError) ErrorName() string { return "LoadRemoteLibValidationError" }

// Error satisfies the builtin error interface
func (e LoadRemoteLibValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoadRemoteLib.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoadRemoteLibValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoadRemoteLibValidationError{}

// Validate checks the field values on VirProtectExStkSpace with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VirProtectExStkSpace) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VirProtectExStkSpace with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VirProtectExStkSpaceMultiError, or nil if none found.
func (m *VirProtectExStkSpace) ValidateAll() error {
	return m.validate(true)
}

func (m *VirProtectExStkSpace) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VirProtectExStkSpaceValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VirProtectExStkSpaceValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VirProtectExStkSpaceValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessID

	// no validation rules for IsX86Process

	// no validation rules for ImagePath

	// no validation rules for Address

	// no validation rules for AddrSize

	// no validation rules for MemAttri

	// no validation rules for ThreadID

	// no validation rules for StkBeginAddr

	// no validation rules for StkEndAddr

	// no validation rules for ProcessMd5

	// no validation rules for ReportID

	// no validation rules for HookFuncName

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VirProtectExStkSpaceValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VirProtectExStkSpaceValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VirProtectExStkSpaceValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VirProtectExStkSpaceValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VirProtectExStkSpaceValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VirProtectExStkSpaceValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	if len(errors) > 0 {
		return VirProtectExStkSpaceMultiError(errors)
	}

	return nil
}

// VirProtectExStkSpaceMultiError is an error wrapping multiple validation
// errors returned by VirProtectExStkSpace.ValidateAll() if the designated
// constraints aren't met.
type VirProtectExStkSpaceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VirProtectExStkSpaceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VirProtectExStkSpaceMultiError) AllErrors() []error { return m }

// VirProtectExStkSpaceValidationError is the validation error returned by
// VirProtectExStkSpace.Validate if the designated constraints aren't met.
type VirProtectExStkSpaceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VirProtectExStkSpaceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VirProtectExStkSpaceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VirProtectExStkSpaceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VirProtectExStkSpaceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VirProtectExStkSpaceValidationError) ErrorName() string {
	return "VirProtectExStkSpaceValidationError"
}

// Error satisfies the builtin error interface
func (e VirProtectExStkSpaceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVirProtectExStkSpace.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VirProtectExStkSpaceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VirProtectExStkSpaceValidationError{}

// Validate checks the field values on ReturnStackExec with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReturnStackExec) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReturnStackExec with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReturnStackExecMultiError, or nil if none found.
func (m *ReturnStackExec) ValidateAll() error {
	return m.validate(true)
}

func (m *ReturnStackExec) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReturnStackExecValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReturnStackExecValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReturnStackExecValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessID

	// no validation rules for IsX86Process

	// no validation rules for ImagePath

	// no validation rules for Address

	// no validation rules for ThreadID

	// no validation rules for StkBeginAddr

	// no validation rules for StkEndAddr

	// no validation rules for ProcessMd5

	// no validation rules for ReportID

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReturnStackExecValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReturnStackExecValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReturnStackExecValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReturnStackExecValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReturnStackExecValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReturnStackExecValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	if len(errors) > 0 {
		return ReturnStackExecMultiError(errors)
	}

	return nil
}

// ReturnStackExecMultiError is an error wrapping multiple validation errors
// returned by ReturnStackExec.ValidateAll() if the designated constraints
// aren't met.
type ReturnStackExecMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReturnStackExecMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReturnStackExecMultiError) AllErrors() []error { return m }

// ReturnStackExecValidationError is the validation error returned by
// ReturnStackExec.Validate if the designated constraints aren't met.
type ReturnStackExecValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReturnStackExecValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReturnStackExecValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReturnStackExecValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReturnStackExecValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReturnStackExecValidationError) ErrorName() string { return "ReturnStackExecValidationError" }

// Error satisfies the builtin error interface
func (e ReturnStackExecValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReturnStackExec.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReturnStackExecValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReturnStackExecValidationError{}

// Validate checks the field values on RemoteThread with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RemoteThread) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoteThread with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RemoteThreadMultiError, or
// nil if none found.
func (m *RemoteThread) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoteThread) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoteThreadValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoteThreadValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoteThreadValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NHostPid

	// no validation rules for NGuestPid

	// no validation rules for StrGuestImageName

	// no validation rules for StrHostImageName

	// no validation rules for StrGuestProcessName

	// no validation rules for StrHostProcessName

	// no validation rules for StrHostProcessMd5

	if len(errors) > 0 {
		return RemoteThreadMultiError(errors)
	}

	return nil
}

// RemoteThreadMultiError is an error wrapping multiple validation errors
// returned by RemoteThread.ValidateAll() if the designated constraints aren't met.
type RemoteThreadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoteThreadMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoteThreadMultiError) AllErrors() []error { return m }

// RemoteThreadValidationError is the validation error returned by
// RemoteThread.Validate if the designated constraints aren't met.
type RemoteThreadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoteThreadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoteThreadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoteThreadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoteThreadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoteThreadValidationError) ErrorName() string { return "RemoteThreadValidationError" }

// Error satisfies the builtin error interface
func (e RemoteThreadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoteThread.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoteThreadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoteThreadValidationError{}

// Validate checks the field values on MemNoFileAttack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MemNoFileAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemNoFileAttack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemNoFileAttackMultiError, or nil if none found.
func (m *MemNoFileAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *MemNoFileAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemNoFileAttackValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemNoFileAttackValidationError{
				field:  "SourceProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemNoFileAttackValidationError{
					field:  "TargetProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemNoFileAttackValidationError{
				field:  "TargetProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttackType

	// no validation rules for DocName

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MemStr

	switch v := m.Detail.(type) {
	case *MemNoFileAttack_Schedule:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSchedule()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Schedule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Schedule",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSchedule()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "Schedule",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_FileCopy:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileCopy()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "FileCopy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "FileCopy",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileCopy()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "FileCopy",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_FileMove:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileMove()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "FileMove",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "FileMove",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileMove()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "FileMove",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_Http:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHttp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Http",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Http",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHttp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "Http",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WimCmd:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWimCmd()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WimCmd",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WimCmd",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWimCmd()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WimCmd",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_LoadDll:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoadDll()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "LoadDll",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "LoadDll",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoadDll()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "LoadDll",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_GetAddress:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGetAddress()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "GetAddress",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "GetAddress",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGetAddress()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "GetAddress",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_ListenPort:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetListenPort()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "ListenPort",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "ListenPort",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetListenPort()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "ListenPort",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_SocketCommunication:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSocketCommunication()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "SocketCommunication",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "SocketCommunication",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSocketCommunication()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "SocketCommunication",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_Win32Share:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWin32Share()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Win32Share",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Win32Share",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWin32Share()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "Win32Share",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WebShellJava:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebShellJava()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WebShellJava",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WebShellJava",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebShellJava()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WebShellJava",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WmiPersistentBackdoor:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWmiPersistentBackdoor()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiPersistentBackdoor",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiPersistentBackdoor",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWmiPersistentBackdoor()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WmiPersistentBackdoor",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WebShellJava1:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWebShellJava1()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WebShellJava1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WebShellJava1",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWebShellJava1()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WebShellJava1",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_AmsiByAmsiContext:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAmsiByAmsiContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "AmsiByAmsiContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "AmsiByAmsiContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAmsiByAmsiContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "AmsiByAmsiContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_AmsiDllhijack:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAmsiDllhijack()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "AmsiDllhijack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "AmsiDllhijack",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAmsiDllhijack()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "AmsiDllhijack",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_Email:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Email",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "Email",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "Email",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WmiTerminateProcess:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWmiTerminateProcess()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiTerminateProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiTerminateProcess",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWmiTerminateProcess()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WmiTerminateProcess",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WmiRegOper:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWmiRegOper()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiRegOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiRegOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWmiRegOper()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WmiRegOper",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WmiServiceOper:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWmiServiceOper()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiServiceOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiServiceOper",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWmiServiceOper()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WmiServiceOper",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MemNoFileAttack_WmiQuery:
		if v == nil {
			err := MemNoFileAttackValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWmiQuery()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiQuery",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemNoFileAttackValidationError{
						field:  "WmiQuery",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWmiQuery()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemNoFileAttackValidationError{
					field:  "WmiQuery",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MemNoFileAttackMultiError(errors)
	}

	return nil
}

// MemNoFileAttackMultiError is an error wrapping multiple validation errors
// returned by MemNoFileAttack.ValidateAll() if the designated constraints
// aren't met.
type MemNoFileAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemNoFileAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemNoFileAttackMultiError) AllErrors() []error { return m }

// MemNoFileAttackValidationError is the validation error returned by
// MemNoFileAttack.Validate if the designated constraints aren't met.
type MemNoFileAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemNoFileAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemNoFileAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemNoFileAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemNoFileAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemNoFileAttackValidationError) ErrorName() string { return "MemNoFileAttackValidationError" }

// Error satisfies the builtin error interface
func (e MemNoFileAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemNoFileAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemNoFileAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemNoFileAttackValidationError{}

// Validate checks the field values on MemHeapSpray with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MemHeapSpray) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemHeapSpray with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MemHeapSprayMultiError, or
// nil if none found.
func (m *MemHeapSpray) ValidateAll() error {
	return m.validate(true)
}

func (m *MemHeapSpray) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemHeapSprayValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemHeapSprayValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemHeapSprayValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemHeapSprayValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemHeapSprayValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemHeapSprayValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	// no validation rules for HookFuncName

	// no validation rules for Address

	// no validation rules for AddrSize

	// no validation rules for InvalidInstruction

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemHeapSprayValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemHeapSprayValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemHeapSprayValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemHeapSprayValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemHeapSprayValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemHeapSprayValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for HeapBase

	// no validation rules for CheckBase

	// no validation rules for HeapSize

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	// no validation rules for MemStr

	if len(errors) > 0 {
		return MemHeapSprayMultiError(errors)
	}

	return nil
}

// MemHeapSprayMultiError is an error wrapping multiple validation errors
// returned by MemHeapSpray.ValidateAll() if the designated constraints aren't met.
type MemHeapSprayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemHeapSprayMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemHeapSprayMultiError) AllErrors() []error { return m }

// MemHeapSprayValidationError is the validation error returned by
// MemHeapSpray.Validate if the designated constraints aren't met.
type MemHeapSprayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemHeapSprayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemHeapSprayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemHeapSprayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemHeapSprayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemHeapSprayValidationError) ErrorName() string { return "MemHeapSprayValidationError" }

// Error satisfies the builtin error interface
func (e MemHeapSprayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemHeapSpray.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemHeapSprayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemHeapSprayValidationError{}

// Validate checks the field values on MemRop with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MemRop) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemRop with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MemRopMultiError, or nil if none found.
func (m *MemRop) ValidateAll() error {
	return m.validate(true)
}

func (m *MemRop) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemRopValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemRopValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemRopValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemRopValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemRopValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemRopValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	// no validation rules for RemoteIp

	// no validation rules for HookFuncName

	// no validation rules for CheckContent

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemRopValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemRopValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemRopValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemRopValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemRopValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemRopValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	if len(errors) > 0 {
		return MemRopMultiError(errors)
	}

	return nil
}

// MemRopMultiError is an error wrapping multiple validation errors returned by
// MemRop.ValidateAll() if the designated constraints aren't met.
type MemRopMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemRopMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemRopMultiError) AllErrors() []error { return m }

// MemRopValidationError is the validation error returned by MemRop.Validate if
// the designated constraints aren't met.
type MemRopValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemRopValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemRopValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemRopValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemRopValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemRopValidationError) ErrorName() string { return "MemRopValidationError" }

// Error satisfies the builtin error interface
func (e MemRopValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemRop.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemRopValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemRopValidationError{}

// Validate checks the field values on MemLayoutShellCode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemLayoutShellCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemLayoutShellCode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemLayoutShellCodeMultiError, or nil if none found.
func (m *MemLayoutShellCode) ValidateAll() error {
	return m.validate(true)
}

func (m *MemLayoutShellCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemLayoutShellCodeValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemLayoutShellCodeValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemLayoutShellCodeValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemLayoutShellCodeValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemLayoutShellCodeValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemLayoutShellCodeValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemLayoutShellCodeValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemLayoutShellCodeValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemLayoutShellCodeValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemLayoutShellCodeValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemLayoutShellCodeValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemLayoutShellCodeValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	// no validation rules for MemStr

	if len(errors) > 0 {
		return MemLayoutShellCodeMultiError(errors)
	}

	return nil
}

// MemLayoutShellCodeMultiError is an error wrapping multiple validation errors
// returned by MemLayoutShellCode.ValidateAll() if the designated constraints
// aren't met.
type MemLayoutShellCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemLayoutShellCodeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemLayoutShellCodeMultiError) AllErrors() []error { return m }

// MemLayoutShellCodeValidationError is the validation error returned by
// MemLayoutShellCode.Validate if the designated constraints aren't met.
type MemLayoutShellCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemLayoutShellCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemLayoutShellCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemLayoutShellCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemLayoutShellCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemLayoutShellCodeValidationError) ErrorName() string {
	return "MemLayoutShellCodeValidationError"
}

// Error satisfies the builtin error interface
func (e MemLayoutShellCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemLayoutShellCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemLayoutShellCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemLayoutShellCodeValidationError{}

// Validate checks the field values on MemStackPivot with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MemStackPivot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemStackPivot with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MemStackPivotMultiError, or
// nil if none found.
func (m *MemStackPivot) ValidateAll() error {
	return m.validate(true)
}

func (m *MemStackPivot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemStackPivotValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemStackPivotValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemStackPivotValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemStackPivotValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemStackPivotValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemStackPivotValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	// no validation rules for HookFuncName

	// no validation rules for Address

	// no validation rules for AddrSize

	// no validation rules for MemAttri

	// no validation rules for StkBeginAddr

	// no validation rules for StkEndAddr

	// no validation rules for EspAddress

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemStackPivotValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemStackPivotValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemStackPivotValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemStackPivotValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemStackPivotValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemStackPivotValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OldStackBase

	// no validation rules for NewStackBase

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	// no validation rules for MemStr

	if len(errors) > 0 {
		return MemStackPivotMultiError(errors)
	}

	return nil
}

// MemStackPivotMultiError is an error wrapping multiple validation errors
// returned by MemStackPivot.ValidateAll() if the designated constraints
// aren't met.
type MemStackPivotMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemStackPivotMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemStackPivotMultiError) AllErrors() []error { return m }

// MemStackPivotValidationError is the validation error returned by
// MemStackPivot.Validate if the designated constraints aren't met.
type MemStackPivotValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemStackPivotValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemStackPivotValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemStackPivotValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemStackPivotValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemStackPivotValidationError) ErrorName() string { return "MemStackPivotValidationError" }

// Error satisfies the builtin error interface
func (e MemStackPivotValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemStackPivot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemStackPivotValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemStackPivotValidationError{}

// Validate checks the field values on MemStartProcess with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MemStartProcess) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemStartProcess with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemStartProcessMultiError, or nil if none found.
func (m *MemStartProcess) ValidateAll() error {
	return m.validate(true)
}

func (m *MemStartProcess) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemStartProcessValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemStartProcessValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemStartProcessValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemStartProcessValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemStartProcessValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemStartProcessValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TargetProcessFilePath

	// no validation rules for TargetProcessCommandLine

	if len(errors) > 0 {
		return MemStartProcessMultiError(errors)
	}

	return nil
}

// MemStartProcessMultiError is an error wrapping multiple validation errors
// returned by MemStartProcess.ValidateAll() if the designated constraints
// aren't met.
type MemStartProcessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemStartProcessMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemStartProcessMultiError) AllErrors() []error { return m }

// MemStartProcessValidationError is the validation error returned by
// MemStartProcess.Validate if the designated constraints aren't met.
type MemStartProcessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemStartProcessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemStartProcessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemStartProcessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemStartProcessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemStartProcessValidationError) ErrorName() string { return "MemStartProcessValidationError" }

// Error satisfies the builtin error interface
func (e MemStartProcessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemStartProcess.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemStartProcessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemStartProcessValidationError{}

// Validate checks the field values on MemRunningShellCode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemRunningShellCode) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemRunningShellCode with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemRunningShellCodeMultiError, or nil if none found.
func (m *MemRunningShellCode) ValidateAll() error {
	return m.validate(true)
}

func (m *MemRunningShellCode) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemRunningShellCodeValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemRunningShellCodeValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemRunningShellCodeValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemRunningShellCodeValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemRunningShellCodeValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemRunningShellCodeValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemRunningShellCodeValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemRunningShellCodeValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemRunningShellCodeValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemRunningShellCodeValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemRunningShellCodeValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemRunningShellCodeValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	// no validation rules for MemStr

	if len(errors) > 0 {
		return MemRunningShellCodeMultiError(errors)
	}

	return nil
}

// MemRunningShellCodeMultiError is an error wrapping multiple validation
// errors returned by MemRunningShellCode.ValidateAll() if the designated
// constraints aren't met.
type MemRunningShellCodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemRunningShellCodeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemRunningShellCodeMultiError) AllErrors() []error { return m }

// MemRunningShellCodeValidationError is the validation error returned by
// MemRunningShellCode.Validate if the designated constraints aren't met.
type MemRunningShellCodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemRunningShellCodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemRunningShellCodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemRunningShellCodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemRunningShellCodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemRunningShellCodeValidationError) ErrorName() string {
	return "MemRunningShellCodeValidationError"
}

// Error satisfies the builtin error interface
func (e MemRunningShellCodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemRunningShellCode.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemRunningShellCodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemRunningShellCodeValidationError{}

// Validate checks the field values on MemEngineAttack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MemEngineAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemEngineAttack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemEngineAttackMultiError, or nil if none found.
func (m *MemEngineAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *MemEngineAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemEngineAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemEngineAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemEngineAttackValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemEngineAttackValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemEngineAttackValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemEngineAttackValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemEngineAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemEngineAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemEngineAttackValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemEngineAttackValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemEngineAttackValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemEngineAttackValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RetAddress

	// no validation rules for ModuleName

	// no validation rules for HookName

	// no validation rules for CodeFragment

	if len(errors) > 0 {
		return MemEngineAttackMultiError(errors)
	}

	return nil
}

// MemEngineAttackMultiError is an error wrapping multiple validation errors
// returned by MemEngineAttack.ValidateAll() if the designated constraints
// aren't met.
type MemEngineAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemEngineAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemEngineAttackMultiError) AllErrors() []error { return m }

// MemEngineAttackValidationError is the validation error returned by
// MemEngineAttack.Validate if the designated constraints aren't met.
type MemEngineAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemEngineAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemEngineAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemEngineAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemEngineAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemEngineAttackValidationError) ErrorName() string { return "MemEngineAttackValidationError" }

// Error satisfies the builtin error interface
func (e MemEngineAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemEngineAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemEngineAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemEngineAttackValidationError{}

// Validate checks the field values on NoFileAttackScriptSchedule with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptSchedule with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptScheduleMultiError, or nil if none found.
func (m *NoFileAttackScriptSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	// no validation rules for Args

	if len(errors) > 0 {
		return NoFileAttackScriptScheduleMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptScheduleMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptSchedule.ValidateAll() if
// the designated constraints aren't met.
type NoFileAttackScriptScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptScheduleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptScheduleMultiError) AllErrors() []error { return m }

// NoFileAttackScriptScheduleValidationError is the validation error returned
// by NoFileAttackScriptSchedule.Validate if the designated constraints aren't met.
type NoFileAttackScriptScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptScheduleValidationError) ErrorName() string {
	return "NoFileAttackScriptScheduleValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptScheduleValidationError{}

// Validate checks the field values on NoFileAttackScriptFileCopy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptFileCopy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptFileCopy with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptFileCopyMultiError, or nil if none found.
func (m *NoFileAttackScriptFileCopy) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptFileCopy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SrcPath

	// no validation rules for DestPath

	if len(errors) > 0 {
		return NoFileAttackScriptFileCopyMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptFileCopyMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptFileCopy.ValidateAll() if
// the designated constraints aren't met.
type NoFileAttackScriptFileCopyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptFileCopyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptFileCopyMultiError) AllErrors() []error { return m }

// NoFileAttackScriptFileCopyValidationError is the validation error returned
// by NoFileAttackScriptFileCopy.Validate if the designated constraints aren't met.
type NoFileAttackScriptFileCopyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptFileCopyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptFileCopyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptFileCopyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptFileCopyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptFileCopyValidationError) ErrorName() string {
	return "NoFileAttackScriptFileCopyValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptFileCopyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptFileCopy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptFileCopyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptFileCopyValidationError{}

// Validate checks the field values on NoFileAttackScriptHttp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptHttp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptHttp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptHttpMultiError, or nil if none found.
func (m *NoFileAttackScriptHttp) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptHttp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return NoFileAttackScriptHttpMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptHttpMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptHttp.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptHttpMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptHttpMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptHttpMultiError) AllErrors() []error { return m }

// NoFileAttackScriptHttpValidationError is the validation error returned by
// NoFileAttackScriptHttp.Validate if the designated constraints aren't met.
type NoFileAttackScriptHttpValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptHttpValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptHttpValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptHttpValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptHttpValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptHttpValidationError) ErrorName() string {
	return "NoFileAttackScriptHttpValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptHttpValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptHttp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptHttpValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptHttpValidationError{}

// Validate checks the field values on NoFileAttackScriptWmiCmd with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptWmiCmd) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWmiCmd with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWmiCmdMultiError, or nil if none found.
func (m *NoFileAttackScriptWmiCmd) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWmiCmd) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cmd

	// no validation rules for RemoteIp

	// no validation rules for IsRemote

	if len(errors) > 0 {
		return NoFileAttackScriptWmiCmdMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWmiCmdMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptWmiCmd.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptWmiCmdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWmiCmdMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWmiCmdMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWmiCmdValidationError is the validation error returned by
// NoFileAttackScriptWmiCmd.Validate if the designated constraints aren't met.
type NoFileAttackScriptWmiCmdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWmiCmdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWmiCmdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWmiCmdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWmiCmdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWmiCmdValidationError) ErrorName() string {
	return "NoFileAttackScriptWmiCmdValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWmiCmdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWmiCmd.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWmiCmdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWmiCmdValidationError{}

// Validate checks the field values on NoFileAttackScriptWmiTerminateProcess
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *NoFileAttackScriptWmiTerminateProcess) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWmiTerminateProcess
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWmiTerminateProcessMultiError, or nil if none found.
func (m *NoFileAttackScriptWmiTerminateProcess) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWmiTerminateProcess) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsRemote

	// no validation rules for RemoteIp

	// no validation rules for TargetProcessPath

	if len(errors) > 0 {
		return NoFileAttackScriptWmiTerminateProcessMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWmiTerminateProcessMultiError is an error wrapping
// multiple validation errors returned by
// NoFileAttackScriptWmiTerminateProcess.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptWmiTerminateProcessMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWmiTerminateProcessMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWmiTerminateProcessMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWmiTerminateProcessValidationError is the validation error
// returned by NoFileAttackScriptWmiTerminateProcess.Validate if the
// designated constraints aren't met.
type NoFileAttackScriptWmiTerminateProcessValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWmiTerminateProcessValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWmiTerminateProcessValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWmiTerminateProcessValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWmiTerminateProcessValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWmiTerminateProcessValidationError) ErrorName() string {
	return "NoFileAttackScriptWmiTerminateProcessValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWmiTerminateProcessValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWmiTerminateProcess.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWmiTerminateProcessValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWmiTerminateProcessValidationError{}

// Validate checks the field values on NoFileAttackScriptWmiOperReg with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptWmiOperReg) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWmiOperReg with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWmiOperRegMultiError, or nil if none found.
func (m *NoFileAttackScriptWmiOperReg) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWmiOperReg) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsRemote

	// no validation rules for RemoteIp

	// no validation rules for OperType

	// no validation rules for RegKeyPath

	// no validation rules for RegValueName

	if len(errors) > 0 {
		return NoFileAttackScriptWmiOperRegMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWmiOperRegMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptWmiOperReg.ValidateAll() if
// the designated constraints aren't met.
type NoFileAttackScriptWmiOperRegMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWmiOperRegMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWmiOperRegMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWmiOperRegValidationError is the validation error returned
// by NoFileAttackScriptWmiOperReg.Validate if the designated constraints
// aren't met.
type NoFileAttackScriptWmiOperRegValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWmiOperRegValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWmiOperRegValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWmiOperRegValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWmiOperRegValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWmiOperRegValidationError) ErrorName() string {
	return "NoFileAttackScriptWmiOperRegValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWmiOperRegValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWmiOperReg.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWmiOperRegValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWmiOperRegValidationError{}

// Validate checks the field values on NoFileAttackScriptWmiOperService with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NoFileAttackScriptWmiOperService) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWmiOperService with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWmiOperServiceMultiError, or nil if none found.
func (m *NoFileAttackScriptWmiOperService) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWmiOperService) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsRemote

	// no validation rules for RemoteIp

	// no validation rules for OperType

	// no validation rules for ServiceName

	// no validation rules for ServiceExePath

	if len(errors) > 0 {
		return NoFileAttackScriptWmiOperServiceMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWmiOperServiceMultiError is an error wrapping multiple
// validation errors returned by
// NoFileAttackScriptWmiOperService.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptWmiOperServiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWmiOperServiceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWmiOperServiceMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWmiOperServiceValidationError is the validation error
// returned by NoFileAttackScriptWmiOperService.Validate if the designated
// constraints aren't met.
type NoFileAttackScriptWmiOperServiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWmiOperServiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWmiOperServiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWmiOperServiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWmiOperServiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWmiOperServiceValidationError) ErrorName() string {
	return "NoFileAttackScriptWmiOperServiceValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWmiOperServiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWmiOperService.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWmiOperServiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWmiOperServiceValidationError{}

// Validate checks the field values on NoFileAttackScriptWmiExeQuery with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptWmiExeQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWmiExeQuery with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWmiExeQueryMultiError, or nil if none found.
func (m *NoFileAttackScriptWmiExeQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWmiExeQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsRemote

	// no validation rules for RemoteIp

	// no validation rules for QueryLanguage

	// no validation rules for QueryCmd

	if len(errors) > 0 {
		return NoFileAttackScriptWmiExeQueryMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWmiExeQueryMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptWmiExeQuery.ValidateAll()
// if the designated constraints aren't met.
type NoFileAttackScriptWmiExeQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWmiExeQueryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWmiExeQueryMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWmiExeQueryValidationError is the validation error
// returned by NoFileAttackScriptWmiExeQuery.Validate if the designated
// constraints aren't met.
type NoFileAttackScriptWmiExeQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWmiExeQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWmiExeQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWmiExeQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWmiExeQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWmiExeQueryValidationError) ErrorName() string {
	return "NoFileAttackScriptWmiExeQueryValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWmiExeQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWmiExeQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWmiExeQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWmiExeQueryValidationError{}

// Validate checks the field values on NoFileAttackScriptLoadDll with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptLoadDll) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptLoadDll with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptLoadDllMultiError, or nil if none found.
func (m *NoFileAttackScriptLoadDll) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptLoadDll) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Path

	if len(errors) > 0 {
		return NoFileAttackScriptLoadDllMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptLoadDllMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptLoadDll.ValidateAll() if the
// designated constraints aren't met.
type NoFileAttackScriptLoadDllMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptLoadDllMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptLoadDllMultiError) AllErrors() []error { return m }

// NoFileAttackScriptLoadDllValidationError is the validation error returned by
// NoFileAttackScriptLoadDll.Validate if the designated constraints aren't met.
type NoFileAttackScriptLoadDllValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptLoadDllValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptLoadDllValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptLoadDllValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptLoadDllValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptLoadDllValidationError) ErrorName() string {
	return "NoFileAttackScriptLoadDllValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptLoadDllValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptLoadDll.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptLoadDllValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptLoadDllValidationError{}

// Validate checks the field values on NoFileAttackScriptGetAddress with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptGetAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptGetAddress with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptGetAddressMultiError, or nil if none found.
func (m *NoFileAttackScriptGetAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptGetAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApiName

	if len(errors) > 0 {
		return NoFileAttackScriptGetAddressMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptGetAddressMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptGetAddress.ValidateAll() if
// the designated constraints aren't met.
type NoFileAttackScriptGetAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptGetAddressMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptGetAddressMultiError) AllErrors() []error { return m }

// NoFileAttackScriptGetAddressValidationError is the validation error returned
// by NoFileAttackScriptGetAddress.Validate if the designated constraints
// aren't met.
type NoFileAttackScriptGetAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptGetAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptGetAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptGetAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptGetAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptGetAddressValidationError) ErrorName() string {
	return "NoFileAttackScriptGetAddressValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptGetAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptGetAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptGetAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptGetAddressValidationError{}

// Validate checks the field values on NoFileAttackScriptListen with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptListen) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptListen with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptListenMultiError, or nil if none found.
func (m *NoFileAttackScriptListen) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptListen) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	if len(errors) > 0 {
		return NoFileAttackScriptListenMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptListenMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptListen.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptListenMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptListenMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptListenMultiError) AllErrors() []error { return m }

// NoFileAttackScriptListenValidationError is the validation error returned by
// NoFileAttackScriptListen.Validate if the designated constraints aren't met.
type NoFileAttackScriptListenValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptListenValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptListenValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptListenValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptListenValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptListenValidationError) ErrorName() string {
	return "NoFileAttackScriptListenValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptListenValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptListen.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptListenValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptListenValidationError{}

// Validate checks the field values on NoFileAttackScriptSocket with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptSocket) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptSocket with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptSocketMultiError, or nil if none found.
func (m *NoFileAttackScriptSocket) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptSocket) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	if len(errors) > 0 {
		return NoFileAttackScriptSocketMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptSocketMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptSocket.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptSocketMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptSocketMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptSocketMultiError) AllErrors() []error { return m }

// NoFileAttackScriptSocketValidationError is the validation error returned by
// NoFileAttackScriptSocket.Validate if the designated constraints aren't met.
type NoFileAttackScriptSocketValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptSocketValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptSocketValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptSocketValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptSocketValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptSocketValidationError) ErrorName() string {
	return "NoFileAttackScriptSocketValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptSocketValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptSocket.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptSocketValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptSocketValidationError{}

// Validate checks the field values on NoFileAttackScriptWin32Share with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptWin32Share) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptWin32Share with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptWin32ShareMultiError, or nil if none found.
func (m *NoFileAttackScriptWin32Share) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptWin32Share) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShareName

	// no validation rules for SharePath

	// no validation rules for RemoteIp

	// no validation rules for IsRemote

	if len(errors) > 0 {
		return NoFileAttackScriptWin32ShareMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptWin32ShareMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptWin32Share.ValidateAll() if
// the designated constraints aren't met.
type NoFileAttackScriptWin32ShareMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptWin32ShareMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptWin32ShareMultiError) AllErrors() []error { return m }

// NoFileAttackScriptWin32ShareValidationError is the validation error returned
// by NoFileAttackScriptWin32Share.Validate if the designated constraints
// aren't met.
type NoFileAttackScriptWin32ShareValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptWin32ShareValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptWin32ShareValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptWin32ShareValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptWin32ShareValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptWin32ShareValidationError) ErrorName() string {
	return "NoFileAttackScriptWin32ShareValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptWin32ShareValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptWin32Share.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptWin32ShareValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptWin32ShareValidationError{}

// Validate checks the field values on NoFileAttackWebShellJava with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackWebShellJava) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackWebShellJava with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackWebShellJavaMultiError, or nil if none found.
func (m *NoFileAttackWebShellJava) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackWebShellJava) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WebShellName

	// no validation rules for WebShellDetail

	if len(errors) > 0 {
		return NoFileAttackWebShellJavaMultiError(errors)
	}

	return nil
}

// NoFileAttackWebShellJavaMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackWebShellJava.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackWebShellJavaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackWebShellJavaMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackWebShellJavaMultiError) AllErrors() []error { return m }

// NoFileAttackWebShellJavaValidationError is the validation error returned by
// NoFileAttackWebShellJava.Validate if the designated constraints aren't met.
type NoFileAttackWebShellJavaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackWebShellJavaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackWebShellJavaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackWebShellJavaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackWebShellJavaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackWebShellJavaValidationError) ErrorName() string {
	return "NoFileAttackWebShellJavaValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackWebShellJavaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackWebShellJava.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackWebShellJavaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackWebShellJavaValidationError{}

// Validate checks the field values on NoFileAttackWMIPersistentBackdoor with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NoFileAttackWMIPersistentBackdoor) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackWMIPersistentBackdoor
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NoFileAttackWMIPersistentBackdoorMultiError, or nil if none found.
func (m *NoFileAttackWMIPersistentBackdoor) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackWMIPersistentBackdoor) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ObjName

	// no validation rules for WmiContext

	// no validation rules for RemoteIp

	if len(errors) > 0 {
		return NoFileAttackWMIPersistentBackdoorMultiError(errors)
	}

	return nil
}

// NoFileAttackWMIPersistentBackdoorMultiError is an error wrapping multiple
// validation errors returned by
// NoFileAttackWMIPersistentBackdoor.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackWMIPersistentBackdoorMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackWMIPersistentBackdoorMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackWMIPersistentBackdoorMultiError) AllErrors() []error { return m }

// NoFileAttackWMIPersistentBackdoorValidationError is the validation error
// returned by NoFileAttackWMIPersistentBackdoor.Validate if the designated
// constraints aren't met.
type NoFileAttackWMIPersistentBackdoorValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackWMIPersistentBackdoorValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackWMIPersistentBackdoorValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackWMIPersistentBackdoorValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackWMIPersistentBackdoorValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackWMIPersistentBackdoorValidationError) ErrorName() string {
	return "NoFileAttackWMIPersistentBackdoorValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackWMIPersistentBackdoorValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackWMIPersistentBackdoor.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackWMIPersistentBackdoorValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackWMIPersistentBackdoorValidationError{}

// Validate checks the field values on NoFileAttackWebShellJava1 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackWebShellJava1) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackWebShellJava1 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackWebShellJava1MultiError, or nil if none found.
func (m *NoFileAttackWebShellJava1) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackWebShellJava1) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RemoteIp

	// no validation rules for Infotype

	if len(errors) > 0 {
		return NoFileAttackWebShellJava1MultiError(errors)
	}

	return nil
}

// NoFileAttackWebShellJava1MultiError is an error wrapping multiple validation
// errors returned by NoFileAttackWebShellJava1.ValidateAll() if the
// designated constraints aren't met.
type NoFileAttackWebShellJava1MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackWebShellJava1MultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackWebShellJava1MultiError) AllErrors() []error { return m }

// NoFileAttackWebShellJava1ValidationError is the validation error returned by
// NoFileAttackWebShellJava1.Validate if the designated constraints aren't met.
type NoFileAttackWebShellJava1ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackWebShellJava1ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackWebShellJava1ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackWebShellJava1ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackWebShellJava1ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackWebShellJava1ValidationError) ErrorName() string {
	return "NoFileAttackWebShellJava1ValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackWebShellJava1ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackWebShellJava1.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackWebShellJava1ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackWebShellJava1ValidationError{}

// Validate checks the field values on NoFileAttackScriptAmsiByAmsiContext with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *NoFileAttackScriptAmsiByAmsiContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptAmsiByAmsiContext
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptAmsiByAmsiContextMultiError, or nil if none found.
func (m *NoFileAttackScriptAmsiByAmsiContext) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptAmsiByAmsiContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModifiedValue

	if len(errors) > 0 {
		return NoFileAttackScriptAmsiByAmsiContextMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptAmsiByAmsiContextMultiError is an error wrapping multiple
// validation errors returned by
// NoFileAttackScriptAmsiByAmsiContext.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptAmsiByAmsiContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptAmsiByAmsiContextMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptAmsiByAmsiContextMultiError) AllErrors() []error { return m }

// NoFileAttackScriptAmsiByAmsiContextValidationError is the validation error
// returned by NoFileAttackScriptAmsiByAmsiContext.Validate if the designated
// constraints aren't met.
type NoFileAttackScriptAmsiByAmsiContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) ErrorName() string {
	return "NoFileAttackScriptAmsiByAmsiContextValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptAmsiByAmsiContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptAmsiByAmsiContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptAmsiByAmsiContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptAmsiByAmsiContextValidationError{}

// Validate checks the field values on NoFileAttackScriptAmsiDllHijack with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptAmsiDllHijack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptAmsiDllHijack with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptAmsiDllHijackMultiError, or nil if none found.
func (m *NoFileAttackScriptAmsiDllHijack) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptAmsiDllHijack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DllPath

	if len(errors) > 0 {
		return NoFileAttackScriptAmsiDllHijackMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptAmsiDllHijackMultiError is an error wrapping multiple
// validation errors returned by NoFileAttackScriptAmsiDllHijack.ValidateAll()
// if the designated constraints aren't met.
type NoFileAttackScriptAmsiDllHijackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptAmsiDllHijackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptAmsiDllHijackMultiError) AllErrors() []error { return m }

// NoFileAttackScriptAmsiDllHijackValidationError is the validation error
// returned by NoFileAttackScriptAmsiDllHijack.Validate if the designated
// constraints aren't met.
type NoFileAttackScriptAmsiDllHijackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptAmsiDllHijackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptAmsiDllHijackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptAmsiDllHijackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptAmsiDllHijackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptAmsiDllHijackValidationError) ErrorName() string {
	return "NoFileAttackScriptAmsiDllHijackValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptAmsiDllHijackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptAmsiDllHijack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptAmsiDllHijackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptAmsiDllHijackValidationError{}

// Validate checks the field values on NoFileAttackScriptEmail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NoFileAttackScriptEmail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NoFileAttackScriptEmail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NoFileAttackScriptEmailMultiError, or nil if none found.
func (m *NoFileAttackScriptEmail) ValidateAll() error {
	return m.validate(true)
}

func (m *NoFileAttackScriptEmail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderAddr

	// no validation rules for RecvAddr

	if len(errors) > 0 {
		return NoFileAttackScriptEmailMultiError(errors)
	}

	return nil
}

// NoFileAttackScriptEmailMultiError is an error wrapping multiple validation
// errors returned by NoFileAttackScriptEmail.ValidateAll() if the designated
// constraints aren't met.
type NoFileAttackScriptEmailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NoFileAttackScriptEmailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NoFileAttackScriptEmailMultiError) AllErrors() []error { return m }

// NoFileAttackScriptEmailValidationError is the validation error returned by
// NoFileAttackScriptEmail.Validate if the designated constraints aren't met.
type NoFileAttackScriptEmailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NoFileAttackScriptEmailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NoFileAttackScriptEmailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NoFileAttackScriptEmailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NoFileAttackScriptEmailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NoFileAttackScriptEmailValidationError) ErrorName() string {
	return "NoFileAttackScriptEmailValidationError"
}

// Error satisfies the builtin error interface
func (e NoFileAttackScriptEmailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNoFileAttackScriptEmail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NoFileAttackScriptEmailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NoFileAttackScriptEmailValidationError{}

// Validate checks the field values on EtwRiskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EtwRiskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EtwRiskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EtwRiskInfoMultiError, or
// nil if none found.
func (m *EtwRiskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EtwRiskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EtwRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EtwRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EtwRiskInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTargetProcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EtwRiskInfoValidationError{
					field:  "TargetProcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EtwRiskInfoValidationError{
					field:  "TargetProcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetProcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EtwRiskInfoValidationError{
				field:  "TargetProcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EtwRiskInfoValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EtwRiskInfoValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EtwRiskInfoValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ProcFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for EvidenceSize

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EtwRiskInfoValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EtwRiskInfoValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EtwRiskInfoValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EtwRiskInfoMultiError(errors)
	}

	return nil
}

// EtwRiskInfoMultiError is an error wrapping multiple validation errors
// returned by EtwRiskInfo.ValidateAll() if the designated constraints aren't met.
type EtwRiskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EtwRiskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EtwRiskInfoMultiError) AllErrors() []error { return m }

// EtwRiskInfoValidationError is the validation error returned by
// EtwRiskInfo.Validate if the designated constraints aren't met.
type EtwRiskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EtwRiskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EtwRiskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EtwRiskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EtwRiskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EtwRiskInfoValidationError) ErrorName() string { return "EtwRiskInfoValidationError" }

// Error satisfies the builtin error interface
func (e EtwRiskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEtwRiskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EtwRiskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EtwRiskInfoValidationError{}

// Validate checks the field values on KernelVulnerableDriverRiskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KernelVulnerableDriverRiskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KernelVulnerableDriverRiskInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// KernelVulnerableDriverRiskInfoMultiError, or nil if none found.
func (m *KernelVulnerableDriverRiskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KernelVulnerableDriverRiskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KernelVulnerableDriverRiskInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DriverName

	// no validation rules for DriverPath

	// no validation rules for RiskType

	for idx, item := range m.GetDriverFileList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
						field:  fmt.Sprintf("DriverFileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
						field:  fmt.Sprintf("DriverFileList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KernelVulnerableDriverRiskInfoValidationError{
					field:  fmt.Sprintf("DriverFileList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OriginFileName

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KernelVulnerableDriverRiskInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KernelVulnerableDriverRiskInfoValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KernelVulnerableDriverRiskInfoMultiError(errors)
	}

	return nil
}

// KernelVulnerableDriverRiskInfoMultiError is an error wrapping multiple
// validation errors returned by KernelVulnerableDriverRiskInfo.ValidateAll()
// if the designated constraints aren't met.
type KernelVulnerableDriverRiskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KernelVulnerableDriverRiskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KernelVulnerableDriverRiskInfoMultiError) AllErrors() []error { return m }

// KernelVulnerableDriverRiskInfoValidationError is the validation error
// returned by KernelVulnerableDriverRiskInfo.Validate if the designated
// constraints aren't met.
type KernelVulnerableDriverRiskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KernelVulnerableDriverRiskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KernelVulnerableDriverRiskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KernelVulnerableDriverRiskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KernelVulnerableDriverRiskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KernelVulnerableDriverRiskInfoValidationError) ErrorName() string {
	return "KernelVulnerableDriverRiskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KernelVulnerableDriverRiskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKernelVulnerableDriverRiskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KernelVulnerableDriverRiskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KernelVulnerableDriverRiskInfoValidationError{}

// Validate checks the field values on NullAddressAttack with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NullAddressAttack) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NullAddressAttack with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NullAddressAttackMultiError, or nil if none found.
func (m *NullAddressAttack) ValidateAll() error {
	return m.validate(true)
}

func (m *NullAddressAttack) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NullAddressAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NullAddressAttackValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NullAddressAttackValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HookFuncName

	// no validation rules for Address

	// no validation rules for AddrSize

	// no validation rules for MemAttri

	// no validation rules for FuncFlags

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NullAddressAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NullAddressAttackValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NullAddressAttackValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NullAddressAttackMultiError(errors)
	}

	return nil
}

// NullAddressAttackMultiError is an error wrapping multiple validation errors
// returned by NullAddressAttack.ValidateAll() if the designated constraints
// aren't met.
type NullAddressAttackMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NullAddressAttackMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NullAddressAttackMultiError) AllErrors() []error { return m }

// NullAddressAttackValidationError is the validation error returned by
// NullAddressAttack.Validate if the designated constraints aren't met.
type NullAddressAttackValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NullAddressAttackValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NullAddressAttackValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NullAddressAttackValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NullAddressAttackValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NullAddressAttackValidationError) ErrorName() string {
	return "NullAddressAttackValidationError"
}

// Error satisfies the builtin error interface
func (e NullAddressAttackValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNullAddressAttack.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NullAddressAttackValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NullAddressAttackValidationError{}

// Validate checks the field values on TunnelRiskInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TunnelRiskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TunnelRiskInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TunnelRiskInfoMultiError,
// or nil if none found.
func (m *TunnelRiskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TunnelRiskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TunnelRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TunnelRiskInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TunnelRiskInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TunnelRiskInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TunnelRiskInfoValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TunnelRiskInfoValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReportID

	// no validation rules for SrcIp

	// no validation rules for SrcPort

	// no validation rules for DstIp

	// no validation rules for DstPort

	// no validation rules for TunnelType

	for idx, item := range m.GetProcessInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TunnelRiskInfoValidationError{
					field:  fmt.Sprintf("ProcessInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetReportEvidenceInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TunnelRiskInfoValidationError{
					field:  fmt.Sprintf("ReportEvidenceInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.Detail.(type) {
	case *TunnelRiskInfo_InfoIcmp:
		if v == nil {
			err := TunnelRiskInfoValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInfoIcmp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoIcmp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoIcmp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInfoIcmp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TunnelRiskInfoValidationError{
					field:  "InfoIcmp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TunnelRiskInfo_InfoDns:
		if v == nil {
			err := TunnelRiskInfoValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInfoDns()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoDns",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoDns",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInfoDns()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TunnelRiskInfoValidationError{
					field:  "InfoDns",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *TunnelRiskInfo_InfoTcp:
		if v == nil {
			err := TunnelRiskInfoValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInfoTcp()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoTcp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TunnelRiskInfoValidationError{
						field:  "InfoTcp",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInfoTcp()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TunnelRiskInfoValidationError{
					field:  "InfoTcp",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return TunnelRiskInfoMultiError(errors)
	}

	return nil
}

// TunnelRiskInfoMultiError is an error wrapping multiple validation errors
// returned by TunnelRiskInfo.ValidateAll() if the designated constraints
// aren't met.
type TunnelRiskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TunnelRiskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TunnelRiskInfoMultiError) AllErrors() []error { return m }

// TunnelRiskInfoValidationError is the validation error returned by
// TunnelRiskInfo.Validate if the designated constraints aren't met.
type TunnelRiskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TunnelRiskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TunnelRiskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TunnelRiskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TunnelRiskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TunnelRiskInfoValidationError) ErrorName() string { return "TunnelRiskInfoValidationError" }

// Error satisfies the builtin error interface
func (e TunnelRiskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTunnelRiskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TunnelRiskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TunnelRiskInfoValidationError{}

// Validate checks the field values on TUNNEL_INFO_ICMP with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TUNNEL_INFO_ICMP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TUNNEL_INFO_ICMP with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TUNNEL_INFO_ICMPMultiError, or nil if none found.
func (m *TUNNEL_INFO_ICMP) ValidateAll() error {
	return m.validate(true)
}

func (m *TUNNEL_INFO_ICMP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Code

	// no validation rules for Checksum

	// no validation rules for Other

	// no validation rules for HexData

	// no validation rules for StrData

	// no validation rules for DetectFlag

	if len(errors) > 0 {
		return TUNNEL_INFO_ICMPMultiError(errors)
	}

	return nil
}

// TUNNEL_INFO_ICMPMultiError is an error wrapping multiple validation errors
// returned by TUNNEL_INFO_ICMP.ValidateAll() if the designated constraints
// aren't met.
type TUNNEL_INFO_ICMPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TUNNEL_INFO_ICMPMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TUNNEL_INFO_ICMPMultiError) AllErrors() []error { return m }

// TUNNEL_INFO_ICMPValidationError is the validation error returned by
// TUNNEL_INFO_ICMP.Validate if the designated constraints aren't met.
type TUNNEL_INFO_ICMPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TUNNEL_INFO_ICMPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TUNNEL_INFO_ICMPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TUNNEL_INFO_ICMPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TUNNEL_INFO_ICMPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TUNNEL_INFO_ICMPValidationError) ErrorName() string { return "TUNNEL_INFO_ICMPValidationError" }

// Error satisfies the builtin error interface
func (e TUNNEL_INFO_ICMPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTUNNEL_INFO_ICMP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TUNNEL_INFO_ICMPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TUNNEL_INFO_ICMPValidationError{}

// Validate checks the field values on Query with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Query) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Query with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in QueryMultiError, or nil if none found.
func (m *Query) ValidateAll() error {
	return m.validate(true)
}

func (m *Query) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Class

	if len(errors) > 0 {
		return QueryMultiError(errors)
	}

	return nil
}

// QueryMultiError is an error wrapping multiple validation errors returned by
// Query.ValidateAll() if the designated constraints aren't met.
type QueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryMultiError) AllErrors() []error { return m }

// QueryValidationError is the validation error returned by Query.Validate if
// the designated constraints aren't met.
type QueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryValidationError) ErrorName() string { return "QueryValidationError" }

// Error satisfies the builtin error interface
func (e QueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryValidationError{}

// Validate checks the field values on Answer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Answer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Answer with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AnswerMultiError, or nil if none found.
func (m *Answer) ValidateAll() error {
	return m.validate(true)
}

func (m *Answer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Type

	// no validation rules for Class

	// no validation rules for Ttl

	if len(errors) > 0 {
		return AnswerMultiError(errors)
	}

	return nil
}

// AnswerMultiError is an error wrapping multiple validation errors returned by
// Answer.ValidateAll() if the designated constraints aren't met.
type AnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnswerMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnswerMultiError) AllErrors() []error { return m }

// AnswerValidationError is the validation error returned by Answer.Validate if
// the designated constraints aren't met.
type AnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnswerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnswerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnswerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnswerValidationError) ErrorName() string { return "AnswerValidationError" }

// Error satisfies the builtin error interface
func (e AnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnswerValidationError{}

// Validate checks the field values on TUNNEL_INFO_DNS with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TUNNEL_INFO_DNS) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TUNNEL_INFO_DNS with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TUNNEL_INFO_DNSMultiError, or nil if none found.
func (m *TUNNEL_INFO_DNS) ValidateAll() error {
	return m.validate(true)
}

func (m *TUNNEL_INFO_DNS) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Flags

	// no validation rules for Questions

	// no validation rules for AnswerRrs

	// no validation rules for AuthorityRrs

	// no validation rules for AdditionalRrs

	for idx, item := range m.GetQueries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TUNNEL_INFO_DNSValidationError{
						field:  fmt.Sprintf("Queries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TUNNEL_INFO_DNSValidationError{
						field:  fmt.Sprintf("Queries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TUNNEL_INFO_DNSValidationError{
					field:  fmt.Sprintf("Queries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TUNNEL_INFO_DNSValidationError{
						field:  fmt.Sprintf("Answers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TUNNEL_INFO_DNSValidationError{
						field:  fmt.Sprintf("Answers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TUNNEL_INFO_DNSValidationError{
					field:  fmt.Sprintf("Answers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for HexData

	// no validation rules for StrData

	// no validation rules for DetectFlag

	if len(errors) > 0 {
		return TUNNEL_INFO_DNSMultiError(errors)
	}

	return nil
}

// TUNNEL_INFO_DNSMultiError is an error wrapping multiple validation errors
// returned by TUNNEL_INFO_DNS.ValidateAll() if the designated constraints
// aren't met.
type TUNNEL_INFO_DNSMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TUNNEL_INFO_DNSMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TUNNEL_INFO_DNSMultiError) AllErrors() []error { return m }

// TUNNEL_INFO_DNSValidationError is the validation error returned by
// TUNNEL_INFO_DNS.Validate if the designated constraints aren't met.
type TUNNEL_INFO_DNSValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TUNNEL_INFO_DNSValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TUNNEL_INFO_DNSValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TUNNEL_INFO_DNSValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TUNNEL_INFO_DNSValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TUNNEL_INFO_DNSValidationError) ErrorName() string { return "TUNNEL_INFO_DNSValidationError" }

// Error satisfies the builtin error interface
func (e TUNNEL_INFO_DNSValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTUNNEL_INFO_DNS.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TUNNEL_INFO_DNSValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TUNNEL_INFO_DNSValidationError{}

// Validate checks the field values on TUNNEL_INFO_TCP with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TUNNEL_INFO_TCP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TUNNEL_INFO_TCP with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TUNNEL_INFO_TCPMultiError, or nil if none found.
func (m *TUNNEL_INFO_TCP) ValidateAll() error {
	return m.validate(true)
}

func (m *TUNNEL_INFO_TCP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SrcPort

	// no validation rules for DstPort

	// no validation rules for SeqNum

	// no validation rules for AckNum

	// no validation rules for DataOffset

	// no validation rules for Flags

	// no validation rules for WindowSize

	// no validation rules for Checksum

	// no validation rules for UrgentPtr

	// no validation rules for HexData

	// no validation rules for StrData

	// no validation rules for DetectFlag

	if len(errors) > 0 {
		return TUNNEL_INFO_TCPMultiError(errors)
	}

	return nil
}

// TUNNEL_INFO_TCPMultiError is an error wrapping multiple validation errors
// returned by TUNNEL_INFO_TCP.ValidateAll() if the designated constraints
// aren't met.
type TUNNEL_INFO_TCPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TUNNEL_INFO_TCPMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TUNNEL_INFO_TCPMultiError) AllErrors() []error { return m }

// TUNNEL_INFO_TCPValidationError is the validation error returned by
// TUNNEL_INFO_TCP.Validate if the designated constraints aren't met.
type TUNNEL_INFO_TCPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TUNNEL_INFO_TCPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TUNNEL_INFO_TCPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TUNNEL_INFO_TCPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TUNNEL_INFO_TCPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TUNNEL_INFO_TCPValidationError) ErrorName() string { return "TUNNEL_INFO_TCPValidationError" }

// Error satisfies the builtin error interface
func (e TUNNEL_INFO_TCPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTUNNEL_INFO_TCP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TUNNEL_INFO_TCPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TUNNEL_INFO_TCPValidationError{}
