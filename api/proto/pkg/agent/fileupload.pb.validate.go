// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/fileupload.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FileUploadReq with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileUploadReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileUploadReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileUploadReqMultiError, or
// nil if none found.
func (m *FileUploadReq) ValidateAll() error {
	return m.validate(true)
}

func (m *FileUploadReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UploadType

	// no validation rules for Identifier

	if len(errors) > 0 {
		return FileUploadReqMultiError(errors)
	}

	return nil
}

// FileUploadReqMultiError is an error wrapping multiple validation errors
// returned by FileUploadReq.ValidateAll() if the designated constraints
// aren't met.
type FileUploadReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileUploadReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileUploadReqMultiError) AllErrors() []error { return m }

// FileUploadReqValidationError is the validation error returned by
// FileUploadReq.Validate if the designated constraints aren't met.
type FileUploadReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileUploadReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileUploadReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileUploadReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileUploadReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileUploadReqValidationError) ErrorName() string { return "FileUploadReqValidationError" }

// Error satisfies the builtin error interface
func (e FileUploadReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileUploadReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileUploadReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileUploadReqValidationError{}

// Validate checks the field values on FileUploadResp with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileUploadResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileUploadResp with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileUploadRespMultiError,
// or nil if none found.
func (m *FileUploadResp) ValidateAll() error {
	return m.validate(true)
}

func (m *FileUploadResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UploadType

	// no validation rules for Identifier

	// no validation rules for Url

	if len(errors) > 0 {
		return FileUploadRespMultiError(errors)
	}

	return nil
}

// FileUploadRespMultiError is an error wrapping multiple validation errors
// returned by FileUploadResp.ValidateAll() if the designated constraints
// aren't met.
type FileUploadRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileUploadRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileUploadRespMultiError) AllErrors() []error { return m }

// FileUploadRespValidationError is the validation error returned by
// FileUploadResp.Validate if the designated constraints aren't met.
type FileUploadRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileUploadRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileUploadRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileUploadRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileUploadRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileUploadRespValidationError) ErrorName() string { return "FileUploadRespValidationError" }

// Error satisfies the builtin error interface
func (e FileUploadRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileUploadResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileUploadRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileUploadRespValidationError{}
