// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_system.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 需要与客户端策略类型值保持一致！！！
type RegOperation int32

const (
	RegOperation_rgo_unset   RegOperation = 0
	RegOperation_rgo_create  RegOperation = 131073
	RegOperation_rgo_delete  RegOperation = 131074
	RegOperation_rgo_delete1 RegOperation = 131076
	RegOperation_rgo_write   RegOperation = 131078
)

// Enum value maps for RegOperation.
var (
	RegOperation_name = map[int32]string{
		0:      "rgo_unset",
		131073: "rgo_create",
		131074: "rgo_delete",
		131076: "rgo_delete1",
		131078: "rgo_write",
	}
	RegOperation_value = map[string]int32{
		"rgo_unset":   0,
		"rgo_create":  131073,
		"rgo_delete":  131074,
		"rgo_delete1": 131076,
		"rgo_write":   131078,
	}
)

func (x RegOperation) Enum() *RegOperation {
	p := new(RegOperation)
	*p = x
	return p
}

func (x RegOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RegOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[0].Descriptor()
}

func (RegOperation) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[0]
}

func (x RegOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RegOperation.Descriptor instead.
func (RegOperation) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{0}
}

type KernelRiskType int32

const (
	KernelRiskType_krt_unknown              KernelRiskType = 0
	KernelRiskType_krt_hiddenprocess        KernelRiskType = 1
	KernelRiskType_krt_integrity_failure    KernelRiskType = 2
	KernelRiskType_krt_remote_leak_overflow KernelRiskType = 3
	KernelRiskType_krt_rootkit_hidmod       KernelRiskType = 4
	KernelRiskType_krt_ex_krnlrisk          KernelRiskType = 5
	KernelRiskType_krt_cve                  KernelRiskType = 6
)

// Enum value maps for KernelRiskType.
var (
	KernelRiskType_name = map[int32]string{
		0: "krt_unknown",
		1: "krt_hiddenprocess",
		2: "krt_integrity_failure",
		3: "krt_remote_leak_overflow",
		4: "krt_rootkit_hidmod",
		5: "krt_ex_krnlrisk",
		6: "krt_cve",
	}
	KernelRiskType_value = map[string]int32{
		"krt_unknown":              0,
		"krt_hiddenprocess":        1,
		"krt_integrity_failure":    2,
		"krt_remote_leak_overflow": 3,
		"krt_rootkit_hidmod":       4,
		"krt_ex_krnlrisk":          5,
		"krt_cve":                  6,
	}
)

func (x KernelRiskType) Enum() *KernelRiskType {
	p := new(KernelRiskType)
	*p = x
	return p
}

func (x KernelRiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KernelRiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[1].Descriptor()
}

func (KernelRiskType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[1]
}

func (x KernelRiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KernelRiskType.Descriptor instead.
func (KernelRiskType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{1}
}

type AttackType int32

const (
	AttackType_at_unknown       AttackType = 0
	AttackType_at_golden_ticket AttackType = 1
	AttackType_at_pth           AttackType = 2 // pass the hash
	AttackType_at_worm          AttackType = 3 // 蠕虫风险
	AttackType_at_cve2020_1472  AttackType = 4 // CVE漏洞
	AttackType_at_cve2021_42287 AttackType = 5 // CVE 42287漏洞
)

// Enum value maps for AttackType.
var (
	AttackType_name = map[int32]string{
		0: "at_unknown",
		1: "at_golden_ticket",
		2: "at_pth",
		3: "at_worm",
		4: "at_cve2020_1472",
		5: "at_cve2021_42287",
	}
	AttackType_value = map[string]int32{
		"at_unknown":       0,
		"at_golden_ticket": 1,
		"at_pth":           2,
		"at_worm":          3,
		"at_cve2020_1472":  4,
		"at_cve2021_42287": 5,
	}
)

func (x AttackType) Enum() *AttackType {
	p := new(AttackType)
	*p = x
	return p
}

func (x AttackType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttackType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[2].Descriptor()
}

func (AttackType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[2]
}

func (x AttackType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttackType.Descriptor instead.
func (AttackType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{2}
}

// 域控风险类型
type DomainRiskType int32

const (
	DomainRiskType_dr_unknown                      DomainRiskType = 0  //未知
	DomainRiskType_dr_EventLogClear                DomainRiskType = 1  //1102 日志被清空
	DomainRiskType_dr_EventServiceClose            DomainRiskType = 2  //1100 事件记录服务关闭
	DomainRiskType_dr_LoginWithCredentials         DomainRiskType = 3  //4648 显示凭据登录
	DomainRiskType_dr_CreateDirectoryServiceObject DomainRiskType = 4  //5137 新增组策略
	DomainRiskType_dr_DomainAddPlanningTasks       DomainRiskType = 5  //4698 增加计划任务
	DomainRiskType_dr_DomainAddSystemservice       DomainRiskType = 6  //4697 增加系统服务
	DomainRiskType_dr_PsloggedonHandler            DomainRiskType = 7  //5145 Psloggedon
	DomainRiskType_dr_ModifySensitiveGroup         DomainRiskType = 8  //[4728, 4732, 4756] 修改敏感用户组
	DomainRiskType_dr_CVE_2021_42278               DomainRiskType = 9  // 4741 创建机器账户 && 4781 修改账户名称
	DomainRiskType_dr_AclAbnormalUpdate            DomainRiskType = 10 //5136 ACL异常修改
	DomainRiskType_dr_CVE_2021_42287               DomainRiskType = 11 // 4769 Kerberos 请求服务票证
	DomainRiskType_dr_CheckSensitiveGroupOrUser    DomainRiskType = 12 //4661 查看敏感用户/组
	DomainRiskType_dr_ASREPRoasting                DomainRiskType = 13 //4768 AS-REP Roasting
)

// Enum value maps for DomainRiskType.
var (
	DomainRiskType_name = map[int32]string{
		0:  "dr_unknown",
		1:  "dr_EventLogClear",
		2:  "dr_EventServiceClose",
		3:  "dr_LoginWithCredentials",
		4:  "dr_CreateDirectoryServiceObject",
		5:  "dr_DomainAddPlanningTasks",
		6:  "dr_DomainAddSystemservice",
		7:  "dr_PsloggedonHandler",
		8:  "dr_ModifySensitiveGroup",
		9:  "dr_CVE_2021_42278",
		10: "dr_AclAbnormalUpdate",
		11: "dr_CVE_2021_42287",
		12: "dr_CheckSensitiveGroupOrUser",
		13: "dr_ASREPRoasting",
	}
	DomainRiskType_value = map[string]int32{
		"dr_unknown":                      0,
		"dr_EventLogClear":                1,
		"dr_EventServiceClose":            2,
		"dr_LoginWithCredentials":         3,
		"dr_CreateDirectoryServiceObject": 4,
		"dr_DomainAddPlanningTasks":       5,
		"dr_DomainAddSystemservice":       6,
		"dr_PsloggedonHandler":            7,
		"dr_ModifySensitiveGroup":         8,
		"dr_CVE_2021_42278":               9,
		"dr_AclAbnormalUpdate":            10,
		"dr_CVE_2021_42287":               11,
		"dr_CheckSensitiveGroupOrUser":    12,
		"dr_ASREPRoasting":                13,
	}
)

func (x DomainRiskType) Enum() *DomainRiskType {
	p := new(DomainRiskType)
	*p = x
	return p
}

func (x DomainRiskType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DomainRiskType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[3].Descriptor()
}

func (DomainRiskType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[3]
}

func (x DomainRiskType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DomainRiskType.Descriptor instead.
func (DomainRiskType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{3}
}

type RiskSystemKernelEntry_KrnlRiskSubType int32

const (
	RiskSystemKernelEntry_e_risk_callback_nomod   RiskSystemKernelEntry_KrnlRiskSubType = 0 //上报不属于任何模块的地址 0
	RiskSystemKernelEntry_e_risk_value_not_noraml RiskSystemKernelEntry_KrnlRiskSubType = 1 //上报不正常的值 1
	RiskSystemKernelEntry_e_risk_black_character  RiskSystemKernelEntry_KrnlRiskSubType = 2 //上报命中黑特征 2
	RiskSystemKernelEntry_e_risk_black_mod        RiskSystemKernelEntry_KrnlRiskSubType = 3 //上报黑模块信息 3
	RiskSystemKernelEntry_e_risk_white_mod_tamper RiskSystemKernelEntry_KrnlRiskSubType = 4 //上报白模块(系统模块)的篡改 4
)

// Enum value maps for RiskSystemKernelEntry_KrnlRiskSubType.
var (
	RiskSystemKernelEntry_KrnlRiskSubType_name = map[int32]string{
		0: "e_risk_callback_nomod",
		1: "e_risk_value_not_noraml",
		2: "e_risk_black_character",
		3: "e_risk_black_mod",
		4: "e_risk_white_mod_tamper",
	}
	RiskSystemKernelEntry_KrnlRiskSubType_value = map[string]int32{
		"e_risk_callback_nomod":   0,
		"e_risk_value_not_noraml": 1,
		"e_risk_black_character":  2,
		"e_risk_black_mod":        3,
		"e_risk_white_mod_tamper": 4,
	}
)

func (x RiskSystemKernelEntry_KrnlRiskSubType) Enum() *RiskSystemKernelEntry_KrnlRiskSubType {
	p := new(RiskSystemKernelEntry_KrnlRiskSubType)
	*p = x
	return p
}

func (x RiskSystemKernelEntry_KrnlRiskSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskSystemKernelEntry_KrnlRiskSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[4].Descriptor()
}

func (RiskSystemKernelEntry_KrnlRiskSubType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[4]
}

func (x RiskSystemKernelEntry_KrnlRiskSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskSystemKernelEntry_KrnlRiskSubType.Descriptor instead.
func (RiskSystemKernelEntry_KrnlRiskSubType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{17, 0}
}

type RiskSystemInfoOfAccount_LogonType int32

const (
	RiskSystemInfoOfAccount_lt_windows_remote_auth RiskSystemInfoOfAccount_LogonType = 0 //远程登录
	RiskSystemInfoOfAccount_lt_windows_local_auth  RiskSystemInfoOfAccount_LogonType = 1 //本地登录
)

// Enum value maps for RiskSystemInfoOfAccount_LogonType.
var (
	RiskSystemInfoOfAccount_LogonType_name = map[int32]string{
		0: "lt_windows_remote_auth",
		1: "lt_windows_local_auth",
	}
	RiskSystemInfoOfAccount_LogonType_value = map[string]int32{
		"lt_windows_remote_auth": 0,
		"lt_windows_local_auth":  1,
	}
)

func (x RiskSystemInfoOfAccount_LogonType) Enum() *RiskSystemInfoOfAccount_LogonType {
	p := new(RiskSystemInfoOfAccount_LogonType)
	*p = x
	return p
}

func (x RiskSystemInfoOfAccount_LogonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskSystemInfoOfAccount_LogonType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[5].Descriptor()
}

func (RiskSystemInfoOfAccount_LogonType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[5]
}

func (x RiskSystemInfoOfAccount_LogonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskSystemInfoOfAccount_LogonType.Descriptor instead.
func (RiskSystemInfoOfAccount_LogonType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{19, 0}
}

type RiskSystemInfoOfAccount_AbnormalReason int32

const (
	RiskSystemInfoOfAccount_ar_brute_force RiskSystemInfoOfAccount_AbnormalReason = 0 //暴力破解
	RiskSystemInfoOfAccount_ar_weak_passwd RiskSystemInfoOfAccount_AbnormalReason = 1 //弱密码
	RiskSystemInfoOfAccount_ar_unusual     RiskSystemInfoOfAccount_AbnormalReason = 2 //不常见的登录
)

// Enum value maps for RiskSystemInfoOfAccount_AbnormalReason.
var (
	RiskSystemInfoOfAccount_AbnormalReason_name = map[int32]string{
		0: "ar_brute_force",
		1: "ar_weak_passwd",
		2: "ar_unusual",
	}
	RiskSystemInfoOfAccount_AbnormalReason_value = map[string]int32{
		"ar_brute_force": 0,
		"ar_weak_passwd": 1,
		"ar_unusual":     2,
	}
)

func (x RiskSystemInfoOfAccount_AbnormalReason) Enum() *RiskSystemInfoOfAccount_AbnormalReason {
	p := new(RiskSystemInfoOfAccount_AbnormalReason)
	*p = x
	return p
}

func (x RiskSystemInfoOfAccount_AbnormalReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskSystemInfoOfAccount_AbnormalReason) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[6].Descriptor()
}

func (RiskSystemInfoOfAccount_AbnormalReason) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[6]
}

func (x RiskSystemInfoOfAccount_AbnormalReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskSystemInfoOfAccount_AbnormalReason.Descriptor instead.
func (RiskSystemInfoOfAccount_AbnormalReason) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{19, 1}
}

type DomainRiskCheckSensitiveGroupOrUser_ModifyType int32

const (
	DomainRiskCheckSensitiveGroupOrUser_mt_user  DomainRiskCheckSensitiveGroupOrUser_ModifyType = 0 //用户
	DomainRiskCheckSensitiveGroupOrUser_mt_group DomainRiskCheckSensitiveGroupOrUser_ModifyType = 1 //组
)

// Enum value maps for DomainRiskCheckSensitiveGroupOrUser_ModifyType.
var (
	DomainRiskCheckSensitiveGroupOrUser_ModifyType_name = map[int32]string{
		0: "mt_user",
		1: "mt_group",
	}
	DomainRiskCheckSensitiveGroupOrUser_ModifyType_value = map[string]int32{
		"mt_user":  0,
		"mt_group": 1,
	}
)

func (x DomainRiskCheckSensitiveGroupOrUser_ModifyType) Enum() *DomainRiskCheckSensitiveGroupOrUser_ModifyType {
	p := new(DomainRiskCheckSensitiveGroupOrUser_ModifyType)
	*p = x
	return p
}

func (x DomainRiskCheckSensitiveGroupOrUser_ModifyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DomainRiskCheckSensitiveGroupOrUser_ModifyType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_system_proto_enumTypes[7].Descriptor()
}

func (DomainRiskCheckSensitiveGroupOrUser_ModifyType) Type() protoreflect.EnumType {
	return &file_agent_risk_system_proto_enumTypes[7]
}

func (x DomainRiskCheckSensitiveGroupOrUser_ModifyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DomainRiskCheckSensitiveGroupOrUser_ModifyType.Descriptor instead.
func (DomainRiskCheckSensitiveGroupOrUser_ModifyType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{36, 0}
}

// --------------------------------------------------
//
//	系统风险 识别结果
//
// --------------------------------------------------
type MemProtectRiskSystemInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskSystemInfoOfRegList     []*RiskSystemInfoOfReg     `protobuf:"bytes,1,rep,name=RiskSystemInfoOfRegList,proto3" json:"RiskSystemInfoOfRegList,omitempty"`
	RiskSystemInfoOfAppList     []*RiskSystemInfoOfApp     `protobuf:"bytes,2,rep,name=RiskSystemInfoOfAppList,proto3" json:"RiskSystemInfoOfAppList,omitempty"`
	RiskSystemKernelList        []*RiskSystemKernel        `protobuf:"bytes,3,rep,name=RiskSystemKernelList,proto3" json:"RiskSystemKernelList,omitempty"`
	RiskAttackList              []*RiskAttack              `protobuf:"bytes,4,rep,name=RiskAttackList,proto3" json:"RiskAttackList,omitempty"`
	DirtyCowList                []*DirtyCow                `protobuf:"bytes,5,rep,name=DirtyCowList,proto3" json:"DirtyCowList,omitempty"`
	ProcessPrivilegeList        []*ProcessPrivilege        `protobuf:"bytes,6,rep,name=ProcessPrivilegeList,proto3" json:"ProcessPrivilegeList,omitempty"`
	RiskSystemInfoOfAccountList []*RiskSystemInfoOfAccount `protobuf:"bytes,7,rep,name=RiskSystemInfoOfAccountList,proto3" json:"RiskSystemInfoOfAccountList,omitempty"`
	RiskDomainEventLogList      []*RiskDomainEventLog      `protobuf:"bytes,8,rep,name=RiskDomainEventLogList,proto3" json:"RiskDomainEventLogList,omitempty"`
}

func (x *MemProtectRiskSystemInfo) Reset() {
	*x = MemProtectRiskSystemInfo{}
	mi := &file_agent_risk_system_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskSystemInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskSystemInfo) ProtoMessage() {}

func (x *MemProtectRiskSystemInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskSystemInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskSystemInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskSystemInfo) GetRiskSystemInfoOfRegList() []*RiskSystemInfoOfReg {
	if x != nil {
		return x.RiskSystemInfoOfRegList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetRiskSystemInfoOfAppList() []*RiskSystemInfoOfApp {
	if x != nil {
		return x.RiskSystemInfoOfAppList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetRiskSystemKernelList() []*RiskSystemKernel {
	if x != nil {
		return x.RiskSystemKernelList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetRiskAttackList() []*RiskAttack {
	if x != nil {
		return x.RiskAttackList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetDirtyCowList() []*DirtyCow {
	if x != nil {
		return x.DirtyCowList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetProcessPrivilegeList() []*ProcessPrivilege {
	if x != nil {
		return x.ProcessPrivilegeList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetRiskSystemInfoOfAccountList() []*RiskSystemInfoOfAccount {
	if x != nil {
		return x.RiskSystemInfoOfAccountList
	}
	return nil
}

func (x *MemProtectRiskSystemInfo) GetRiskDomainEventLogList() []*RiskDomainEventLog {
	if x != nil {
		return x.RiskDomainEventLogList
	}
	return nil
}

type MemProtectRiskBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateTime   string       `protobuf:"bytes,1,opt,name=dateTime,proto3" json:"dateTime,omitempty"` // 风险发生的时间
	Header     *RiskHeader  `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	Process    *ProcessInfo `protobuf:"bytes,3,opt,name=Process,proto3" json:"Process,omitempty"`
	ClientTime uint64       `protobuf:"varint,50,opt,name=clientTime,proto3" json:"clientTime,omitempty"` // 客户端发现时间
}

func (x *MemProtectRiskBaseInfo) Reset() {
	*x = MemProtectRiskBaseInfo{}
	mi := &file_agent_risk_system_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskBaseInfo) ProtoMessage() {}

func (x *MemProtectRiskBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskBaseInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskBaseInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{1}
}

func (x *MemProtectRiskBaseInfo) GetDateTime() string {
	if x != nil {
		return x.DateTime
	}
	return ""
}

func (x *MemProtectRiskBaseInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *MemProtectRiskBaseInfo) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *MemProtectRiskBaseInfo) GetClientTime() uint64 {
	if x != nil {
		return x.ClientTime
	}
	return 0
}

// 注册表风险
type RiskSystemInfoOfReg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo *MemProtectRiskBaseInfo `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
	RegPath          []byte                  `protobuf:"bytes,2,opt,name=regPath,proto3" json:"regPath,omitempty"` // 注册表路径
	RegOp            RegOperation            `protobuf:"varint,3,opt,name=regOp,proto3,enum=agent.RegOperation" json:"regOp,omitempty"`
	RegSetValue      []byte                  `protobuf:"bytes,4,opt,name=regSetValue,proto3" json:"regSetValue,omitempty"` // 仅RegOperation为write有效
}

func (x *RiskSystemInfoOfReg) Reset() {
	*x = RiskSystemInfoOfReg{}
	mi := &file_agent_risk_system_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemInfoOfReg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemInfoOfReg) ProtoMessage() {}

func (x *RiskSystemInfoOfReg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemInfoOfReg.ProtoReflect.Descriptor instead.
func (*RiskSystemInfoOfReg) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{2}
}

func (x *RiskSystemInfoOfReg) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

func (x *RiskSystemInfoOfReg) GetRegPath() []byte {
	if x != nil {
		return x.RegPath
	}
	return nil
}

func (x *RiskSystemInfoOfReg) GetRegOp() RegOperation {
	if x != nil {
		return x.RegOp
	}
	return RegOperation_rgo_unset
}

func (x *RiskSystemInfoOfReg) GetRegSetValue() []byte {
	if x != nil {
		return x.RegSetValue
	}
	return nil
}

// 应用风险
type RiskSystemInfoOfApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo      *MemProtectRiskBaseInfo `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
	SubProcessPath        []byte                  `protobuf:"bytes,2,opt,name=subProcessPath,proto3" json:"subProcessPath,omitempty"`               // 子进程路径
	SubProcessFileSha256  []byte                  `protobuf:"bytes,3,opt,name=subProcessFileSha256,proto3" json:"subProcessFileSha256,omitempty"`   // 子进程文件sha256
	SubProcessCommandLine []byte                  `protobuf:"bytes,4,opt,name=subProcessCommandLine,proto3" json:"subProcessCommandLine,omitempty"` // 子进程命令行
}

func (x *RiskSystemInfoOfApp) Reset() {
	*x = RiskSystemInfoOfApp{}
	mi := &file_agent_risk_system_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemInfoOfApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemInfoOfApp) ProtoMessage() {}

func (x *RiskSystemInfoOfApp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemInfoOfApp.ProtoReflect.Descriptor instead.
func (*RiskSystemInfoOfApp) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{3}
}

func (x *RiskSystemInfoOfApp) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

func (x *RiskSystemInfoOfApp) GetSubProcessPath() []byte {
	if x != nil {
		return x.SubProcessPath
	}
	return nil
}

func (x *RiskSystemInfoOfApp) GetSubProcessFileSha256() []byte {
	if x != nil {
		return x.SubProcessFileSha256
	}
	return nil
}

func (x *RiskSystemInfoOfApp) GetSubProcessCommandLine() []byte {
	if x != nil {
		return x.SubProcessCommandLine
	}
	return nil
}

// 内核风险
type RiskSystemKernel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateTime      string         `protobuf:"bytes,1,opt,name=dateTime,proto3" json:"dateTime,omitempty"` // 风险发生的时间
	Header        *RiskHeader    `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`
	RiskType      KernelRiskType `protobuf:"varint,3,opt,name=riskType,proto3,enum=agent.KernelRiskType" json:"riskType,omitempty"`
	ClientVersion string         `protobuf:"bytes,4,opt,name=clientVersion,proto3" json:"clientVersion,omitempty"` //终端版本信息
	// Types that are assignable to Desc:
	//
	//	*RiskSystemKernel_IntegrityFailure
	//	*RiskSystemKernel_Hidemod
	//	*RiskSystemKernel_EntryExs
	//	*RiskSystemKernel_HideProcess
	//	*RiskSystemKernel_Leakinfo
	//	*RiskSystemKernel_CveInfo
	Desc       isRiskSystemKernel_Desc `protobuf_oneof:"desc"`
	ClientTime uint64                  `protobuf:"varint,50,opt,name=clientTime,proto3" json:"clientTime,omitempty"` // 客户端发现时间
}

func (x *RiskSystemKernel) Reset() {
	*x = RiskSystemKernel{}
	mi := &file_agent_risk_system_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemKernel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemKernel) ProtoMessage() {}

func (x *RiskSystemKernel) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemKernel.ProtoReflect.Descriptor instead.
func (*RiskSystemKernel) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{4}
}

func (x *RiskSystemKernel) GetDateTime() string {
	if x != nil {
		return x.DateTime
	}
	return ""
}

func (x *RiskSystemKernel) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RiskSystemKernel) GetRiskType() KernelRiskType {
	if x != nil {
		return x.RiskType
	}
	return KernelRiskType_krt_unknown
}

func (x *RiskSystemKernel) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (m *RiskSystemKernel) GetDesc() isRiskSystemKernel_Desc {
	if m != nil {
		return m.Desc
	}
	return nil
}

func (x *RiskSystemKernel) GetIntegrityFailure() *IntegrityFailure {
	if x, ok := x.GetDesc().(*RiskSystemKernel_IntegrityFailure); ok {
		return x.IntegrityFailure
	}
	return nil
}

func (x *RiskSystemKernel) GetHidemod() *HideMod {
	if x, ok := x.GetDesc().(*RiskSystemKernel_Hidemod); ok {
		return x.Hidemod
	}
	return nil
}

func (x *RiskSystemKernel) GetEntryExs() *RiskSystemKernelExs {
	if x, ok := x.GetDesc().(*RiskSystemKernel_EntryExs); ok {
		return x.EntryExs
	}
	return nil
}

func (x *RiskSystemKernel) GetHideProcess() *ProcessInfo {
	if x, ok := x.GetDesc().(*RiskSystemKernel_HideProcess); ok {
		return x.HideProcess
	}
	return nil
}

func (x *RiskSystemKernel) GetLeakinfo() *LeakOverflowInfo {
	if x, ok := x.GetDesc().(*RiskSystemKernel_Leakinfo); ok {
		return x.Leakinfo
	}
	return nil
}

func (x *RiskSystemKernel) GetCveInfo() *CveInfo {
	if x, ok := x.GetDesc().(*RiskSystemKernel_CveInfo); ok {
		return x.CveInfo
	}
	return nil
}

func (x *RiskSystemKernel) GetClientTime() uint64 {
	if x != nil {
		return x.ClientTime
	}
	return 0
}

type isRiskSystemKernel_Desc interface {
	isRiskSystemKernel_Desc()
}

type RiskSystemKernel_IntegrityFailure struct {
	IntegrityFailure *IntegrityFailure `protobuf:"bytes,23,opt,name=integrityFailure,proto3,oneof"`
}

type RiskSystemKernel_Hidemod struct {
	Hidemod *HideMod `protobuf:"bytes,24,opt,name=hidemod,proto3,oneof"`
}

type RiskSystemKernel_EntryExs struct {
	EntryExs *RiskSystemKernelExs `protobuf:"bytes,25,opt,name=entryExs,proto3,oneof"`
}

type RiskSystemKernel_HideProcess struct {
	HideProcess *ProcessInfo `protobuf:"bytes,26,opt,name=hideProcess,proto3,oneof"`
}

type RiskSystemKernel_Leakinfo struct {
	Leakinfo *LeakOverflowInfo `protobuf:"bytes,27,opt,name=leakinfo,proto3,oneof"`
}

type RiskSystemKernel_CveInfo struct {
	CveInfo *CveInfo `protobuf:"bytes,28,opt,name=cveInfo,proto3,oneof"`
}

func (*RiskSystemKernel_IntegrityFailure) isRiskSystemKernel_Desc() {}

func (*RiskSystemKernel_Hidemod) isRiskSystemKernel_Desc() {}

func (*RiskSystemKernel_EntryExs) isRiskSystemKernel_Desc() {}

func (*RiskSystemKernel_HideProcess) isRiskSystemKernel_Desc() {}

func (*RiskSystemKernel_Leakinfo) isRiskSystemKernel_Desc() {}

func (*RiskSystemKernel_CveInfo) isRiskSystemKernel_Desc() {}

type LeakOverflowInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportID   uint32 `protobuf:"varint,1,opt,name=reportID,proto3" json:"reportID,omitempty"`
	RemoteAddr string `protobuf:"bytes,2,opt,name=remote_addr,json=remoteAddr,proto3" json:"remote_addr,omitempty"`
	Cve        string `protobuf:"bytes,20,opt,name=cve,proto3" json:"cve,omitempty"`
	NetData    []byte `protobuf:"bytes,21,opt,name=net_data,json=netData,proto3" json:"net_data,omitempty"` // 网络数据包
}

func (x *LeakOverflowInfo) Reset() {
	*x = LeakOverflowInfo{}
	mi := &file_agent_risk_system_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeakOverflowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeakOverflowInfo) ProtoMessage() {}

func (x *LeakOverflowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeakOverflowInfo.ProtoReflect.Descriptor instead.
func (*LeakOverflowInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{5}
}

func (x *LeakOverflowInfo) GetReportID() uint32 {
	if x != nil {
		return x.ReportID
	}
	return 0
}

func (x *LeakOverflowInfo) GetRemoteAddr() string {
	if x != nil {
		return x.RemoteAddr
	}
	return ""
}

func (x *LeakOverflowInfo) GetCve() string {
	if x != nil {
		return x.Cve
	}
	return ""
}

func (x *LeakOverflowInfo) GetNetData() []byte {
	if x != nil {
		return x.NetData
	}
	return nil
}

// CVE 风险
type CveInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AttackSource string `protobuf:"bytes,1,opt,name=attack_source,json=attackSource,proto3" json:"attack_source,omitempty"` //攻击源, 可能是或者http://127.0.0.1/1.exe
}

func (x *CveInfo) Reset() {
	*x = CveInfo{}
	mi := &file_agent_risk_system_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CveInfo) ProtoMessage() {}

func (x *CveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CveInfo.ProtoReflect.Descriptor instead.
func (*CveInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{6}
}

func (x *CveInfo) GetAttackSource() string {
	if x != nil {
		return x.AttackSource
	}
	return ""
}

// 攻击风险
type RiskAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo *MemProtectRiskBaseInfo `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
	RiskType         AttackType              `protobuf:"varint,2,opt,name=riskType,proto3,enum=agent.AttackType" json:"riskType,omitempty"`
	HostIP           string                  `protobuf:"bytes,3,opt,name=hostIP,proto3" json:"hostIP,omitempty"`             // PTH使用这个字段
	UserName         string                  `protobuf:"bytes,4,opt,name=userName,proto3" json:"userName,omitempty"`         // 用户名，PTH使用这个字段
	ComputerName     string                  `protobuf:"bytes,5,opt,name=computerName,proto3" json:"computerName,omitempty"` // 机器名, CVE1472使用
}

func (x *RiskAttack) Reset() {
	*x = RiskAttack{}
	mi := &file_agent_risk_system_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAttack) ProtoMessage() {}

func (x *RiskAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAttack.ProtoReflect.Descriptor instead.
func (*RiskAttack) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{7}
}

func (x *RiskAttack) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

func (x *RiskAttack) GetRiskType() AttackType {
	if x != nil {
		return x.RiskType
	}
	return AttackType_at_unknown
}

func (x *RiskAttack) GetHostIP() string {
	if x != nil {
		return x.HostIP
	}
	return ""
}

func (x *RiskAttack) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RiskAttack) GetComputerName() string {
	if x != nil {
		return x.ComputerName
	}
	return ""
}

// 脏牛漏洞
type DirtyCow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo *MemProtectRiskBaseInfo `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
}

func (x *DirtyCow) Reset() {
	*x = DirtyCow{}
	mi := &file_agent_risk_system_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DirtyCow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirtyCow) ProtoMessage() {}

func (x *DirtyCow) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirtyCow.ProtoReflect.Descriptor instead.
func (*DirtyCow) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{8}
}

func (x *DirtyCow) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

// 进程提权
type ProcessPrivilege struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo *MemProtectRiskBaseInfo `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
	Modname          string                  `protobuf:"bytes,2,opt,name=modname,proto3" json:"modname,omitempty"` // 通过哪个模块提的权
}

func (x *ProcessPrivilege) Reset() {
	*x = ProcessPrivilege{}
	mi := &file_agent_risk_system_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessPrivilege) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPrivilege) ProtoMessage() {}

func (x *ProcessPrivilege) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPrivilege.ProtoReflect.Descriptor instead.
func (*ProcessPrivilege) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessPrivilege) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

func (x *ProcessPrivilege) GetModname() string {
	if x != nil {
		return x.Modname
	}
	return ""
}

// 隐藏模块
type HideMod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname string `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"`
}

func (x *HideMod) Reset() {
	*x = HideMod{}
	mi := &file_agent_risk_system_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HideMod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HideMod) ProtoMessage() {}

func (x *HideMod) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HideMod.ProtoReflect.Descriptor instead.
func (*HideMod) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{10}
}

func (x *HideMod) GetModname() string {
	if x != nil {
		return x.Modname
	}
	return ""
}

// 内核完整性校验失败
type IntegrityFailure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname string `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"`
}

func (x *IntegrityFailure) Reset() {
	*x = IntegrityFailure{}
	mi := &file_agent_risk_system_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntegrityFailure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntegrityFailure) ProtoMessage() {}

func (x *IntegrityFailure) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntegrityFailure.ProtoReflect.Descriptor instead.
func (*IntegrityFailure) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{11}
}

func (x *IntegrityFailure) GetModname() string {
	if x != nil {
		return x.Modname
	}
	return ""
}

// 上报不属于任何模块的地址 0
type RiskInfoCallbackNoMod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrCallBackType    string `protobuf:"bytes,1,opt,name=strCallBackType,proto3" json:"strCallBackType,omitempty"`       //异常类型
	StrCallBackSubType string `protobuf:"bytes,2,opt,name=strCallBackSubType,proto3" json:"strCallBackSubType,omitempty"` //异常子类型 (为空则不显示)
	StrCallBackAddr    string `protobuf:"bytes,3,opt,name=strCallBackAddr,proto3" json:"strCallBackAddr,omitempty"`       //地址值
}

func (x *RiskInfoCallbackNoMod) Reset() {
	*x = RiskInfoCallbackNoMod{}
	mi := &file_agent_risk_system_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfoCallbackNoMod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfoCallbackNoMod) ProtoMessage() {}

func (x *RiskInfoCallbackNoMod) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfoCallbackNoMod.ProtoReflect.Descriptor instead.
func (*RiskInfoCallbackNoMod) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{12}
}

func (x *RiskInfoCallbackNoMod) GetStrCallBackType() string {
	if x != nil {
		return x.StrCallBackType
	}
	return ""
}

func (x *RiskInfoCallbackNoMod) GetStrCallBackSubType() string {
	if x != nil {
		return x.StrCallBackSubType
	}
	return ""
}

func (x *RiskInfoCallbackNoMod) GetStrCallBackAddr() string {
	if x != nil {
		return x.StrCallBackAddr
	}
	return ""
}

// 上报不正常的值 1
type RiskInfoValueAbnoraml struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrAbnormalType    string `protobuf:"bytes,1,opt,name=strAbnormalType,proto3" json:"strAbnormalType,omitempty"`       //异常类型
	StrAbnormalSubType string `protobuf:"bytes,2,opt,name=strAbnormalSubType,proto3" json:"strAbnormalSubType,omitempty"` //异常子类型(为空则不显示)
	StrAbnormalAddr    string `protobuf:"bytes,3,opt,name=strAbnormalAddr,proto3" json:"strAbnormalAddr,omitempty"`       //当前异常值
	StrNormalAddr      string `protobuf:"bytes,4,opt,name=strNormalAddr,proto3" json:"strNormalAddr,omitempty"`           //正常值  (为空则不显示)
}

func (x *RiskInfoValueAbnoraml) Reset() {
	*x = RiskInfoValueAbnoraml{}
	mi := &file_agent_risk_system_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfoValueAbnoraml) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfoValueAbnoraml) ProtoMessage() {}

func (x *RiskInfoValueAbnoraml) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfoValueAbnoraml.ProtoReflect.Descriptor instead.
func (*RiskInfoValueAbnoraml) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{13}
}

func (x *RiskInfoValueAbnoraml) GetStrAbnormalType() string {
	if x != nil {
		return x.StrAbnormalType
	}
	return ""
}

func (x *RiskInfoValueAbnoraml) GetStrAbnormalSubType() string {
	if x != nil {
		return x.StrAbnormalSubType
	}
	return ""
}

func (x *RiskInfoValueAbnoraml) GetStrAbnormalAddr() string {
	if x != nil {
		return x.StrAbnormalAddr
	}
	return ""
}

func (x *RiskInfoValueAbnoraml) GetStrNormalAddr() string {
	if x != nil {
		return x.StrNormalAddr
	}
	return ""
}

// 上报命中黑特征 2
type RiskInfoBlackCharacter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrCharacterType    string `protobuf:"bytes,1,opt,name=strCharacterType,proto3" json:"strCharacterType,omitempty"`       //黑特征类型
	StrCharacterSubType string `protobuf:"bytes,2,opt,name=strCharacterSubType,proto3" json:"strCharacterSubType,omitempty"` //黑特征子类型(为空则不显示)
	StrCharacterInfo    string `protobuf:"bytes,3,opt,name=strCharacterInfo,proto3" json:"strCharacterInfo,omitempty"`       //黑特征具体信息
}

func (x *RiskInfoBlackCharacter) Reset() {
	*x = RiskInfoBlackCharacter{}
	mi := &file_agent_risk_system_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfoBlackCharacter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfoBlackCharacter) ProtoMessage() {}

func (x *RiskInfoBlackCharacter) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfoBlackCharacter.ProtoReflect.Descriptor instead.
func (*RiskInfoBlackCharacter) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{14}
}

func (x *RiskInfoBlackCharacter) GetStrCharacterType() string {
	if x != nil {
		return x.StrCharacterType
	}
	return ""
}

func (x *RiskInfoBlackCharacter) GetStrCharacterSubType() string {
	if x != nil {
		return x.StrCharacterSubType
	}
	return ""
}

func (x *RiskInfoBlackCharacter) GetStrCharacterInfo() string {
	if x != nil {
		return x.StrCharacterInfo
	}
	return ""
}

// 上报黑模块信息 3
type RiskInfoBlackMod struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrBlackModName     string `protobuf:"bytes,1,opt,name=strBlackModName,proto3" json:"strBlackModName,omitempty"`         //模块的名称
	StrBlackModFilePath string `protobuf:"bytes,2,opt,name=strBlackModFilePath,proto3" json:"strBlackModFilePath,omitempty"` //模块文件路径
	StrBlackModBaseAddr string `protobuf:"bytes,3,opt,name=strBlackModBaseAddr,proto3" json:"strBlackModBaseAddr,omitempty"` //模块内存映像的起始地址
	StrBlackModSize     string `protobuf:"bytes,4,opt,name=strBlackModSize,proto3" json:"strBlackModSize,omitempty"`         //模块内存映像的大小
	StrBlackDetail      string `protobuf:"bytes,5,opt,name=strBlackDetail,proto3" json:"strBlackDetail,omitempty"`           //详情(命中哪些探测规则),不建议显示
}

func (x *RiskInfoBlackMod) Reset() {
	*x = RiskInfoBlackMod{}
	mi := &file_agent_risk_system_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfoBlackMod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfoBlackMod) ProtoMessage() {}

func (x *RiskInfoBlackMod) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfoBlackMod.ProtoReflect.Descriptor instead.
func (*RiskInfoBlackMod) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{15}
}

func (x *RiskInfoBlackMod) GetStrBlackModName() string {
	if x != nil {
		return x.StrBlackModName
	}
	return ""
}

func (x *RiskInfoBlackMod) GetStrBlackModFilePath() string {
	if x != nil {
		return x.StrBlackModFilePath
	}
	return ""
}

func (x *RiskInfoBlackMod) GetStrBlackModBaseAddr() string {
	if x != nil {
		return x.StrBlackModBaseAddr
	}
	return ""
}

func (x *RiskInfoBlackMod) GetStrBlackModSize() string {
	if x != nil {
		return x.StrBlackModSize
	}
	return ""
}

func (x *RiskInfoBlackMod) GetStrBlackDetail() string {
	if x != nil {
		return x.StrBlackDetail
	}
	return ""
}

// 上报白模块(系统模块)的篡改 4
type RiskInfoWhiteModTamper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StrModName      string `protobuf:"bytes,1,opt,name=strModName,proto3" json:"strModName,omitempty"`           //模块的名称
	StrModBase      string `protobuf:"bytes,2,opt,name=strModBase,proto3" json:"strModBase,omitempty"`           //模块起始地址
	StrTamperDetail string `protobuf:"bytes,3,opt,name=strTamperDetail,proto3" json:"strTamperDetail,omitempty"` //篡改详情
}

func (x *RiskInfoWhiteModTamper) Reset() {
	*x = RiskInfoWhiteModTamper{}
	mi := &file_agent_risk_system_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInfoWhiteModTamper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInfoWhiteModTamper) ProtoMessage() {}

func (x *RiskInfoWhiteModTamper) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInfoWhiteModTamper.ProtoReflect.Descriptor instead.
func (*RiskInfoWhiteModTamper) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{16}
}

func (x *RiskInfoWhiteModTamper) GetStrModName() string {
	if x != nil {
		return x.StrModName
	}
	return ""
}

func (x *RiskInfoWhiteModTamper) GetStrModBase() string {
	if x != nil {
		return x.StrModBase
	}
	return ""
}

func (x *RiskInfoWhiteModTamper) GetStrTamperDetail() string {
	if x != nil {
		return x.StrTamperDetail
	}
	return ""
}

// 子风险信息
type RiskSystemKernelEntry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EKrnlRiskType RiskSystemKernelEntry_KrnlRiskSubType `protobuf:"varint,1,opt,name=eKrnlRiskType,proto3,enum=agent.RiskSystemKernelEntry_KrnlRiskSubType" json:"eKrnlRiskType,omitempty"`
	// Types that are assignable to KrnlRiskEntryDetail:
	//
	//	*RiskSystemKernelEntry_StCallbackNoMod
	//	*RiskSystemKernelEntry_StValueAbnormal
	//	*RiskSystemKernelEntry_StBlackCharacter
	//	*RiskSystemKernelEntry_StBlackMod
	//	*RiskSystemKernelEntry_StWhiteModTamper
	KrnlRiskEntryDetail isRiskSystemKernelEntry_KrnlRiskEntryDetail `protobuf_oneof:"KrnlRiskEntryDetail"`
}

func (x *RiskSystemKernelEntry) Reset() {
	*x = RiskSystemKernelEntry{}
	mi := &file_agent_risk_system_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemKernelEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemKernelEntry) ProtoMessage() {}

func (x *RiskSystemKernelEntry) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemKernelEntry.ProtoReflect.Descriptor instead.
func (*RiskSystemKernelEntry) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{17}
}

func (x *RiskSystemKernelEntry) GetEKrnlRiskType() RiskSystemKernelEntry_KrnlRiskSubType {
	if x != nil {
		return x.EKrnlRiskType
	}
	return RiskSystemKernelEntry_e_risk_callback_nomod
}

func (m *RiskSystemKernelEntry) GetKrnlRiskEntryDetail() isRiskSystemKernelEntry_KrnlRiskEntryDetail {
	if m != nil {
		return m.KrnlRiskEntryDetail
	}
	return nil
}

func (x *RiskSystemKernelEntry) GetStCallbackNoMod() *RiskInfoCallbackNoMod {
	if x, ok := x.GetKrnlRiskEntryDetail().(*RiskSystemKernelEntry_StCallbackNoMod); ok {
		return x.StCallbackNoMod
	}
	return nil
}

func (x *RiskSystemKernelEntry) GetStValueAbnormal() *RiskInfoValueAbnoraml {
	if x, ok := x.GetKrnlRiskEntryDetail().(*RiskSystemKernelEntry_StValueAbnormal); ok {
		return x.StValueAbnormal
	}
	return nil
}

func (x *RiskSystemKernelEntry) GetStBlackCharacter() *RiskInfoBlackCharacter {
	if x, ok := x.GetKrnlRiskEntryDetail().(*RiskSystemKernelEntry_StBlackCharacter); ok {
		return x.StBlackCharacter
	}
	return nil
}

func (x *RiskSystemKernelEntry) GetStBlackMod() *RiskInfoBlackMod {
	if x, ok := x.GetKrnlRiskEntryDetail().(*RiskSystemKernelEntry_StBlackMod); ok {
		return x.StBlackMod
	}
	return nil
}

func (x *RiskSystemKernelEntry) GetStWhiteModTamper() *RiskInfoWhiteModTamper {
	if x, ok := x.GetKrnlRiskEntryDetail().(*RiskSystemKernelEntry_StWhiteModTamper); ok {
		return x.StWhiteModTamper
	}
	return nil
}

type isRiskSystemKernelEntry_KrnlRiskEntryDetail interface {
	isRiskSystemKernelEntry_KrnlRiskEntryDetail()
}

type RiskSystemKernelEntry_StCallbackNoMod struct {
	StCallbackNoMod *RiskInfoCallbackNoMod `protobuf:"bytes,20,opt,name=stCallbackNoMod,proto3,oneof"`
}

type RiskSystemKernelEntry_StValueAbnormal struct {
	StValueAbnormal *RiskInfoValueAbnoraml `protobuf:"bytes,21,opt,name=stValueAbnormal,proto3,oneof"`
}

type RiskSystemKernelEntry_StBlackCharacter struct {
	StBlackCharacter *RiskInfoBlackCharacter `protobuf:"bytes,22,opt,name=stBlackCharacter,proto3,oneof"`
}

type RiskSystemKernelEntry_StBlackMod struct {
	StBlackMod *RiskInfoBlackMod `protobuf:"bytes,23,opt,name=stBlackMod,proto3,oneof"`
}

type RiskSystemKernelEntry_StWhiteModTamper struct {
	StWhiteModTamper *RiskInfoWhiteModTamper `protobuf:"bytes,24,opt,name=stWhiteModTamper,proto3,oneof"`
}

func (*RiskSystemKernelEntry_StCallbackNoMod) isRiskSystemKernelEntry_KrnlRiskEntryDetail() {}

func (*RiskSystemKernelEntry_StValueAbnormal) isRiskSystemKernelEntry_KrnlRiskEntryDetail() {}

func (*RiskSystemKernelEntry_StBlackCharacter) isRiskSystemKernelEntry_KrnlRiskEntryDetail() {}

func (*RiskSystemKernelEntry_StBlackMod) isRiskSystemKernelEntry_KrnlRiskEntryDetail() {}

func (*RiskSystemKernelEntry_StWhiteModTamper) isRiskSystemKernelEntry_KrnlRiskEntryDetail() {}

type RiskSystemKernelExs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskName                  string                   `protobuf:"bytes,1,opt,name=riskName,proto3" json:"riskName,omitempty"`
	RiskSystemKernelEntryList []*RiskSystemKernelEntry `protobuf:"bytes,2,rep,name=RiskSystemKernelEntryList,proto3" json:"RiskSystemKernelEntryList,omitempty"` //风险分条
}

func (x *RiskSystemKernelExs) Reset() {
	*x = RiskSystemKernelExs{}
	mi := &file_agent_risk_system_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemKernelExs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemKernelExs) ProtoMessage() {}

func (x *RiskSystemKernelExs) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemKernelExs.ProtoReflect.Descriptor instead.
func (*RiskSystemKernelExs) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{18}
}

func (x *RiskSystemKernelExs) GetRiskName() string {
	if x != nil {
		return x.RiskName
	}
	return ""
}

func (x *RiskSystemKernelExs) GetRiskSystemKernelEntryList() []*RiskSystemKernelEntry {
	if x != nil {
		return x.RiskSystemKernelEntryList
	}
	return nil
}

// 账户风险
type RiskSystemInfoOfAccount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicPorcessInfo *MemProtectRiskBaseInfo                `protobuf:"bytes,1,opt,name=basicPorcessInfo,proto3" json:"basicPorcessInfo,omitempty"`
	RemoteIp         string                                 `protobuf:"bytes,2,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`                                                                      //远程地址，仅lt_windows_remote_auth时有效
	UserName         string                                 `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`                                                                      //用户名
	IsSuperUser      bool                                   `protobuf:"varint,4,opt,name=isSuperUser,proto3" json:"isSuperUser,omitempty"`                                                                               //是否是管理员账户
	LogonType        RiskSystemInfoOfAccount_LogonType      `protobuf:"varint,5,opt,name=logon_type,json=logonType,proto3,enum=agent.RiskSystemInfoOfAccount_LogonType" json:"logon_type,omitempty"`                     //登录方式
	AbnormalReason   RiskSystemInfoOfAccount_AbnormalReason `protobuf:"varint,6,opt,name=abnormal_reason,json=abnormalReason,proto3,enum=agent.RiskSystemInfoOfAccount_AbnormalReason" json:"abnormal_reason,omitempty"` //异常原因
}

func (x *RiskSystemInfoOfAccount) Reset() {
	*x = RiskSystemInfoOfAccount{}
	mi := &file_agent_risk_system_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskSystemInfoOfAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSystemInfoOfAccount) ProtoMessage() {}

func (x *RiskSystemInfoOfAccount) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSystemInfoOfAccount.ProtoReflect.Descriptor instead.
func (*RiskSystemInfoOfAccount) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{19}
}

func (x *RiskSystemInfoOfAccount) GetBasicPorcessInfo() *MemProtectRiskBaseInfo {
	if x != nil {
		return x.BasicPorcessInfo
	}
	return nil
}

func (x *RiskSystemInfoOfAccount) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *RiskSystemInfoOfAccount) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RiskSystemInfoOfAccount) GetIsSuperUser() bool {
	if x != nil {
		return x.IsSuperUser
	}
	return false
}

func (x *RiskSystemInfoOfAccount) GetLogonType() RiskSystemInfoOfAccount_LogonType {
	if x != nil {
		return x.LogonType
	}
	return RiskSystemInfoOfAccount_lt_windows_remote_auth
}

func (x *RiskSystemInfoOfAccount) GetAbnormalReason() RiskSystemInfoOfAccount_AbnormalReason {
	if x != nil {
		return x.AbnormalReason
	}
	return RiskSystemInfoOfAccount_ar_brute_force
}

// 域控风险字段
type RiskDomainEventLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader    `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`                                 //风险头
	TimeCreated   string         `protobuf:"bytes,2,opt,name=TimeCreated,proto3" json:"TimeCreated,omitempty"`                       //日志创建时间
	ComputerName  string         `protobuf:"bytes,3,opt,name=ComputerName,proto3" json:"ComputerName,omitempty"`                     //计算机名
	UserName      string         `protobuf:"bytes,4,opt,name=UserName,proto3" json:"UserName,omitempty"`                             //用户名
	DomainName    string         `protobuf:"bytes,5,opt,name=DomainName,proto3" json:"DomainName,omitempty"`                         //域名
	EventId       uint32         `protobuf:"varint,6,opt,name=EventId,proto3" json:"EventId,omitempty"`                              //事件类别ID
	EventRecordId uint64         `protobuf:"varint,7,opt,name=EventRecordId,proto3" json:"EventRecordId,omitempty"`                  //日志记录ID
	EventMessage  string         `protobuf:"bytes,8,opt,name=EventMessage,proto3" json:"EventMessage,omitempty"`                     //事件详细数据
	EventXml      string         `protobuf:"bytes,9,opt,name=EventXml,proto3" json:"EventXml,omitempty"`                             //XML数据
	RiskType      DomainRiskType `protobuf:"varint,10,opt,name=RiskType,proto3,enum=agent.DomainRiskType" json:"RiskType,omitempty"` //风险类型
	ProcessSource *ProcessInfo   `protobuf:"bytes,11,opt,name=ProcessSource,proto3" json:"ProcessSource,omitempty"`                  //风险源
	// Types that are assignable to Detail:
	//
	//	*RiskDomainEventLog_Rs_EventLogClear
	//	*RiskDomainEventLog_Rs_LoginWithCredentials
	//	*RiskDomainEventLog_Rs_CreateDirectoryServiceObject
	//	*RiskDomainEventLog_Rs_DomainAddPlanningTasks
	//	*RiskDomainEventLog_Rs_DomainAddSystemservice
	//	*RiskDomainEventLog_Rs_PsloggedonHandler
	//	*RiskDomainEventLog_Rs_ModifySensitiveGroup
	//	*RiskDomainEventLog_Rs_DomainCVE2021_42278
	//	*RiskDomainEventLog_Rs_AclAbnormalUpdate
	//	*RiskDomainEventLog_Rs_DomainCVE2021_42287
	//	*RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser
	//	*RiskDomainEventLog_Rs_ASREPRoasting
	Detail isRiskDomainEventLog_Detail `protobuf_oneof:"detail"`
}

func (x *RiskDomainEventLog) Reset() {
	*x = RiskDomainEventLog{}
	mi := &file_agent_risk_system_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskDomainEventLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskDomainEventLog) ProtoMessage() {}

func (x *RiskDomainEventLog) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskDomainEventLog.ProtoReflect.Descriptor instead.
func (*RiskDomainEventLog) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{20}
}

func (x *RiskDomainEventLog) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RiskDomainEventLog) GetTimeCreated() string {
	if x != nil {
		return x.TimeCreated
	}
	return ""
}

func (x *RiskDomainEventLog) GetComputerName() string {
	if x != nil {
		return x.ComputerName
	}
	return ""
}

func (x *RiskDomainEventLog) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RiskDomainEventLog) GetDomainName() string {
	if x != nil {
		return x.DomainName
	}
	return ""
}

func (x *RiskDomainEventLog) GetEventId() uint32 {
	if x != nil {
		return x.EventId
	}
	return 0
}

func (x *RiskDomainEventLog) GetEventRecordId() uint64 {
	if x != nil {
		return x.EventRecordId
	}
	return 0
}

func (x *RiskDomainEventLog) GetEventMessage() string {
	if x != nil {
		return x.EventMessage
	}
	return ""
}

func (x *RiskDomainEventLog) GetEventXml() string {
	if x != nil {
		return x.EventXml
	}
	return ""
}

func (x *RiskDomainEventLog) GetRiskType() DomainRiskType {
	if x != nil {
		return x.RiskType
	}
	return DomainRiskType_dr_unknown
}

func (x *RiskDomainEventLog) GetProcessSource() *ProcessInfo {
	if x != nil {
		return x.ProcessSource
	}
	return nil
}

func (m *RiskDomainEventLog) GetDetail() isRiskDomainEventLog_Detail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_EventLogClear() *DomainRiskEventLogClear {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_EventLogClear); ok {
		return x.Rs_EventLogClear
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_LoginWithCredentials() *DomainRiskLoginWithCredentials {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_LoginWithCredentials); ok {
		return x.Rs_LoginWithCredentials
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_CreateDirectoryServiceObject() *DomainRiskCreateDirectoryServiceObject {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_CreateDirectoryServiceObject); ok {
		return x.Rs_CreateDirectoryServiceObject
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_DomainAddPlanningTasks() *DomainRiskAddPlanningTasks {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_DomainAddPlanningTasks); ok {
		return x.Rs_DomainAddPlanningTasks
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_DomainAddSystemservice() *DomainRiskAddSystemservice {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_DomainAddSystemservice); ok {
		return x.Rs_DomainAddSystemservice
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_PsloggedonHandler() *DomainRiskPsloggedonHandler {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_PsloggedonHandler); ok {
		return x.Rs_PsloggedonHandler
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_ModifySensitiveGroup() *DomainRiskModifySensitiveGroupHandler {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_ModifySensitiveGroup); ok {
		return x.Rs_ModifySensitiveGroup
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_DomainCVE2021_42278() *DomainRiskCVE2021_42278 {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_DomainCVE2021_42278); ok {
		return x.Rs_DomainCVE2021_42278
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_AclAbnormalUpdate() *DomainRiskAclAbnormalUpdate {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_AclAbnormalUpdate); ok {
		return x.Rs_AclAbnormalUpdate
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_DomainCVE2021_42287() *DomainRiskCVE2021_42287 {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_DomainCVE2021_42287); ok {
		return x.Rs_DomainCVE2021_42287
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_CheckSensitiveGroupOrUser() *DomainRiskCheckSensitiveGroupOrUser {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser); ok {
		return x.Rs_CheckSensitiveGroupOrUser
	}
	return nil
}

func (x *RiskDomainEventLog) GetRs_ASREPRoasting() *DomainRiskASREPRoasting {
	if x, ok := x.GetDetail().(*RiskDomainEventLog_Rs_ASREPRoasting); ok {
		return x.Rs_ASREPRoasting
	}
	return nil
}

type isRiskDomainEventLog_Detail interface {
	isRiskDomainEventLog_Detail()
}

type RiskDomainEventLog_Rs_EventLogClear struct {
	Rs_EventLogClear *DomainRiskEventLogClear `protobuf:"bytes,20,opt,name=rs_EventLogClear,json=rsEventLogClear,proto3,oneof"`
}

type RiskDomainEventLog_Rs_LoginWithCredentials struct {
	Rs_LoginWithCredentials *DomainRiskLoginWithCredentials `protobuf:"bytes,21,opt,name=rs_LoginWithCredentials,json=rsLoginWithCredentials,proto3,oneof"`
}

type RiskDomainEventLog_Rs_CreateDirectoryServiceObject struct {
	Rs_CreateDirectoryServiceObject *DomainRiskCreateDirectoryServiceObject `protobuf:"bytes,22,opt,name=rs_CreateDirectoryServiceObject,json=rsCreateDirectoryServiceObject,proto3,oneof"`
}

type RiskDomainEventLog_Rs_DomainAddPlanningTasks struct {
	Rs_DomainAddPlanningTasks *DomainRiskAddPlanningTasks `protobuf:"bytes,23,opt,name=rs_DomainAddPlanningTasks,json=rsDomainAddPlanningTasks,proto3,oneof"`
}

type RiskDomainEventLog_Rs_DomainAddSystemservice struct {
	Rs_DomainAddSystemservice *DomainRiskAddSystemservice `protobuf:"bytes,24,opt,name=rs_DomainAddSystemservice,json=rsDomainAddSystemservice,proto3,oneof"`
}

type RiskDomainEventLog_Rs_PsloggedonHandler struct {
	Rs_PsloggedonHandler *DomainRiskPsloggedonHandler `protobuf:"bytes,25,opt,name=rs_PsloggedonHandler,json=rsPsloggedonHandler,proto3,oneof"`
}

type RiskDomainEventLog_Rs_ModifySensitiveGroup struct {
	Rs_ModifySensitiveGroup *DomainRiskModifySensitiveGroupHandler `protobuf:"bytes,26,opt,name=rs_ModifySensitiveGroup,json=rsModifySensitiveGroup,proto3,oneof"`
}

type RiskDomainEventLog_Rs_DomainCVE2021_42278 struct {
	Rs_DomainCVE2021_42278 *DomainRiskCVE2021_42278 `protobuf:"bytes,27,opt,name=rs_DomainCVE2021_42278,json=rsDomainCVE202142278,proto3,oneof"`
}

type RiskDomainEventLog_Rs_AclAbnormalUpdate struct {
	Rs_AclAbnormalUpdate *DomainRiskAclAbnormalUpdate `protobuf:"bytes,28,opt,name=rs_AclAbnormalUpdate,json=rsAclAbnormalUpdate,proto3,oneof"`
}

type RiskDomainEventLog_Rs_DomainCVE2021_42287 struct {
	Rs_DomainCVE2021_42287 *DomainRiskCVE2021_42287 `protobuf:"bytes,29,opt,name=rs_DomainCVE2021_42287,json=rsDomainCVE202142287,proto3,oneof"`
}

type RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser struct {
	Rs_CheckSensitiveGroupOrUser *DomainRiskCheckSensitiveGroupOrUser `protobuf:"bytes,30,opt,name=rs_CheckSensitiveGroupOrUser,json=rsCheckSensitiveGroupOrUser,proto3,oneof"`
}

type RiskDomainEventLog_Rs_ASREPRoasting struct {
	Rs_ASREPRoasting *DomainRiskASREPRoasting `protobuf:"bytes,31,opt,name=rs_ASREPRoasting,json=rsASREPRoasting,proto3,oneof"`
}

func (*RiskDomainEventLog_Rs_EventLogClear) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_LoginWithCredentials) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_CreateDirectoryServiceObject) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_DomainAddPlanningTasks) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_DomainAddSystemservice) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_PsloggedonHandler) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_ModifySensitiveGroup) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_DomainCVE2021_42278) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_AclAbnormalUpdate) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_DomainCVE2021_42287) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser) isRiskDomainEventLog_Detail() {}

func (*RiskDomainEventLog_Rs_ASREPRoasting) isRiskDomainEventLog_Detail() {}

// 下面是域控事件日志EventData或者UserData的五大类型
type SubjectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubjectDomainName string `protobuf:"bytes,1,opt,name=SubjectDomainName,proto3" json:"SubjectDomainName,omitempty"`
	SubjectUserName   string `protobuf:"bytes,2,opt,name=SubjectUserName,proto3" json:"SubjectUserName,omitempty"`
	SubjectUserSid    string `protobuf:"bytes,3,opt,name=SubjectUserSid,proto3" json:"SubjectUserSid,omitempty"`
	SubjectLogonId    string `protobuf:"bytes,4,opt,name=SubjectLogonId,proto3" json:"SubjectLogonId,omitempty"`
}

func (x *SubjectType) Reset() {
	*x = SubjectType{}
	mi := &file_agent_risk_system_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubjectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubjectType) ProtoMessage() {}

func (x *SubjectType) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubjectType.ProtoReflect.Descriptor instead.
func (*SubjectType) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{21}
}

func (x *SubjectType) GetSubjectDomainName() string {
	if x != nil {
		return x.SubjectDomainName
	}
	return ""
}

func (x *SubjectType) GetSubjectUserName() string {
	if x != nil {
		return x.SubjectUserName
	}
	return ""
}

func (x *SubjectType) GetSubjectUserSid() string {
	if x != nil {
		return x.SubjectUserSid
	}
	return ""
}

func (x *SubjectType) GetSubjectLogonId() string {
	if x != nil {
		return x.SubjectLogonId
	}
	return ""
}

type TargetType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetDomainName string `protobuf:"bytes,1,opt,name=TargetDomainName,proto3" json:"TargetDomainName,omitempty"`
	TargetUserName   string `protobuf:"bytes,2,opt,name=TargetUserName,proto3" json:"TargetUserName,omitempty"`
	TargetUserSid    string `protobuf:"bytes,3,opt,name=TargetUserSid,proto3" json:"TargetUserSid,omitempty"`
	TargetSid        string `protobuf:"bytes,4,opt,name=TargetSid,proto3" json:"TargetSid,omitempty"`
	TargetLogonId    string `protobuf:"bytes,5,opt,name=TargetLogonId,proto3" json:"TargetLogonId,omitempty"`
	TargetInfo       string `protobuf:"bytes,6,opt,name=TargetInfo,proto3" json:"TargetInfo,omitempty"`
	TargetServerName string `protobuf:"bytes,7,opt,name=TargetServerName,proto3" json:"TargetServerName,omitempty"`
}

func (x *TargetType) Reset() {
	*x = TargetType{}
	mi := &file_agent_risk_system_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TargetType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetType) ProtoMessage() {}

func (x *TargetType) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetType.ProtoReflect.Descriptor instead.
func (*TargetType) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{22}
}

func (x *TargetType) GetTargetDomainName() string {
	if x != nil {
		return x.TargetDomainName
	}
	return ""
}

func (x *TargetType) GetTargetUserName() string {
	if x != nil {
		return x.TargetUserName
	}
	return ""
}

func (x *TargetType) GetTargetUserSid() string {
	if x != nil {
		return x.TargetUserSid
	}
	return ""
}

func (x *TargetType) GetTargetSid() string {
	if x != nil {
		return x.TargetSid
	}
	return ""
}

func (x *TargetType) GetTargetLogonId() string {
	if x != nil {
		return x.TargetLogonId
	}
	return ""
}

func (x *TargetType) GetTargetInfo() string {
	if x != nil {
		return x.TargetInfo
	}
	return ""
}

func (x *TargetType) GetTargetServerName() string {
	if x != nil {
		return x.TargetServerName
	}
	return ""
}

type Sourcetype struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkstationName string `protobuf:"bytes,1,opt,name=WorkstationName,proto3" json:"WorkstationName,omitempty"`
	IpAddress       string `protobuf:"bytes,2,opt,name=IpAddress,proto3" json:"IpAddress,omitempty"`
	IpPort          string `protobuf:"bytes,3,opt,name=IpPort,proto3" json:"IpPort,omitempty"`
}

func (x *Sourcetype) Reset() {
	*x = Sourcetype{}
	mi := &file_agent_risk_system_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sourcetype) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sourcetype) ProtoMessage() {}

func (x *Sourcetype) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sourcetype.ProtoReflect.Descriptor instead.
func (*Sourcetype) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{23}
}

func (x *Sourcetype) GetWorkstationName() string {
	if x != nil {
		return x.WorkstationName
	}
	return ""
}

func (x *Sourcetype) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *Sourcetype) GetIpPort() string {
	if x != nil {
		return x.IpPort
	}
	return ""
}

type TicketType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TicketEncryptionType string `protobuf:"bytes,1,opt,name=TicketEncryptionType,proto3" json:"TicketEncryptionType,omitempty"`
	TicketOptions        string `protobuf:"bytes,2,opt,name=TicketOptions,proto3" json:"TicketOptions,omitempty"`
	Status               string `protobuf:"bytes,3,opt,name=Status,proto3" json:"Status,omitempty"`
}

func (x *TicketType) Reset() {
	*x = TicketType{}
	mi := &file_agent_risk_system_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TicketType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TicketType) ProtoMessage() {}

func (x *TicketType) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TicketType.ProtoReflect.Descriptor instead.
func (*TicketType) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{24}
}

func (x *TicketType) GetTicketEncryptionType() string {
	if x != nil {
		return x.TicketEncryptionType
	}
	return ""
}

func (x *TicketType) GetTicketOptions() string {
	if x != nil {
		return x.TicketOptions
	}
	return ""
}

func (x *TicketType) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ObjectType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ObjectDN     string `protobuf:"bytes,1,opt,name=ObjectDN,proto3" json:"ObjectDN,omitempty"`
	ObjectGUID   string `protobuf:"bytes,2,opt,name=ObjectGUID,proto3" json:"ObjectGUID,omitempty"`
	ObjectClass  string `protobuf:"bytes,3,opt,name=ObjectClass,proto3" json:"ObjectClass,omitempty"`
	ObjectServer string `protobuf:"bytes,4,opt,name=ObjectServer,proto3" json:"ObjectServer,omitempty"`
	ObjectType   string `protobuf:"bytes,5,opt,name=ObjectType,proto3" json:"ObjectType,omitempty"`
	ObjectName   string `protobuf:"bytes,6,opt,name=ObjectName,proto3" json:"ObjectName,omitempty"`
}

func (x *ObjectType) Reset() {
	*x = ObjectType{}
	mi := &file_agent_risk_system_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObjectType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObjectType) ProtoMessage() {}

func (x *ObjectType) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObjectType.ProtoReflect.Descriptor instead.
func (*ObjectType) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{25}
}

func (x *ObjectType) GetObjectDN() string {
	if x != nil {
		return x.ObjectDN
	}
	return ""
}

func (x *ObjectType) GetObjectGUID() string {
	if x != nil {
		return x.ObjectGUID
	}
	return ""
}

func (x *ObjectType) GetObjectClass() string {
	if x != nil {
		return x.ObjectClass
	}
	return ""
}

func (x *ObjectType) GetObjectServer() string {
	if x != nil {
		return x.ObjectServer
	}
	return ""
}

func (x *ObjectType) GetObjectType() string {
	if x != nil {
		return x.ObjectType
	}
	return ""
}

func (x *ObjectType) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

// 1102 日志被清空
type DomainRiskEventLogClear struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrIp   string       `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
}

func (x *DomainRiskEventLogClear) Reset() {
	*x = DomainRiskEventLogClear{}
	mi := &file_agent_risk_system_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskEventLogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskEventLogClear) ProtoMessage() {}

func (x *DomainRiskEventLogClear) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskEventLogClear.ProtoReflect.Descriptor instead.
func (*DomainRiskEventLogClear) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{26}
}

func (x *DomainRiskEventLogClear) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskEventLogClear) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

// 4648 显示凭据登录
type DomainRiskLoginWithCredentials struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Taget   *TargetType  `protobuf:"bytes,1,opt,name=taget,proto3" json:"taget,omitempty"`
	StrIp   string       `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
	Subject *SubjectType `protobuf:"bytes,3,opt,name=subject,proto3" json:"subject,omitempty"`
}

func (x *DomainRiskLoginWithCredentials) Reset() {
	*x = DomainRiskLoginWithCredentials{}
	mi := &file_agent_risk_system_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskLoginWithCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskLoginWithCredentials) ProtoMessage() {}

func (x *DomainRiskLoginWithCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskLoginWithCredentials.ProtoReflect.Descriptor instead.
func (*DomainRiskLoginWithCredentials) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{27}
}

func (x *DomainRiskLoginWithCredentials) GetTaget() *TargetType {
	if x != nil {
		return x.Taget
	}
	return nil
}

func (x *DomainRiskLoginWithCredentials) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

func (x *DomainRiskLoginWithCredentials) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

// 5137 新增组策略
type DomainRiskCreateDirectoryServiceObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrIp   string       `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
}

func (x *DomainRiskCreateDirectoryServiceObject) Reset() {
	*x = DomainRiskCreateDirectoryServiceObject{}
	mi := &file_agent_risk_system_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskCreateDirectoryServiceObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskCreateDirectoryServiceObject) ProtoMessage() {}

func (x *DomainRiskCreateDirectoryServiceObject) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskCreateDirectoryServiceObject.ProtoReflect.Descriptor instead.
func (*DomainRiskCreateDirectoryServiceObject) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{28}
}

func (x *DomainRiskCreateDirectoryServiceObject) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskCreateDirectoryServiceObject) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

// 4698 增加计划任务
type DomainRiskAddPlanningTasks struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject     *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrTaskName string       `protobuf:"bytes,2,opt,name=strTaskName,proto3" json:"strTaskName,omitempty"`
	StrIp       string       `protobuf:"bytes,3,opt,name=strIp,proto3" json:"strIp,omitempty"`
}

func (x *DomainRiskAddPlanningTasks) Reset() {
	*x = DomainRiskAddPlanningTasks{}
	mi := &file_agent_risk_system_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskAddPlanningTasks) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskAddPlanningTasks) ProtoMessage() {}

func (x *DomainRiskAddPlanningTasks) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskAddPlanningTasks.ProtoReflect.Descriptor instead.
func (*DomainRiskAddPlanningTasks) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{29}
}

func (x *DomainRiskAddPlanningTasks) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskAddPlanningTasks) GetStrTaskName() string {
	if x != nil {
		return x.StrTaskName
	}
	return ""
}

func (x *DomainRiskAddPlanningTasks) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

// 4697 增加系统服务
type DomainRiskAddSystemservice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject        *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrServiceName string       `protobuf:"bytes,2,opt,name=strServiceName,proto3" json:"strServiceName,omitempty"`
	StrIp          string       `protobuf:"bytes,3,opt,name=strIp,proto3" json:"strIp,omitempty"`
}

func (x *DomainRiskAddSystemservice) Reset() {
	*x = DomainRiskAddSystemservice{}
	mi := &file_agent_risk_system_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskAddSystemservice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskAddSystemservice) ProtoMessage() {}

func (x *DomainRiskAddSystemservice) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskAddSystemservice.ProtoReflect.Descriptor instead.
func (*DomainRiskAddSystemservice) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{30}
}

func (x *DomainRiskAddSystemservice) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskAddSystemservice) GetStrServiceName() string {
	if x != nil {
		return x.StrServiceName
	}
	return ""
}

func (x *DomainRiskAddSystemservice) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

// 5145 Psloggedon
type DomainRiskPsloggedonHandler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrIp   string       `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
}

func (x *DomainRiskPsloggedonHandler) Reset() {
	*x = DomainRiskPsloggedonHandler{}
	mi := &file_agent_risk_system_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskPsloggedonHandler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskPsloggedonHandler) ProtoMessage() {}

func (x *DomainRiskPsloggedonHandler) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskPsloggedonHandler.ProtoReflect.Descriptor instead.
func (*DomainRiskPsloggedonHandler) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{31}
}

func (x *DomainRiskPsloggedonHandler) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskPsloggedonHandler) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

// [4728, 4732, 4756]  修改敏感用户组
type DomainRiskModifySensitiveGroupHandler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject       *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	Taget         *TargetType  `protobuf:"bytes,2,opt,name=taget,proto3" json:"taget,omitempty"`
	StrIp         string       `protobuf:"bytes,3,opt,name=strIp,proto3" json:"strIp,omitempty"`
	StrMemberName string       `protobuf:"bytes,4,opt,name=strMemberName,proto3" json:"strMemberName,omitempty"`
}

func (x *DomainRiskModifySensitiveGroupHandler) Reset() {
	*x = DomainRiskModifySensitiveGroupHandler{}
	mi := &file_agent_risk_system_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskModifySensitiveGroupHandler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskModifySensitiveGroupHandler) ProtoMessage() {}

func (x *DomainRiskModifySensitiveGroupHandler) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskModifySensitiveGroupHandler.ProtoReflect.Descriptor instead.
func (*DomainRiskModifySensitiveGroupHandler) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{32}
}

func (x *DomainRiskModifySensitiveGroupHandler) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskModifySensitiveGroupHandler) GetTaget() *TargetType {
	if x != nil {
		return x.Taget
	}
	return nil
}

func (x *DomainRiskModifySensitiveGroupHandler) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

func (x *DomainRiskModifySensitiveGroupHandler) GetStrMemberName() string {
	if x != nil {
		return x.StrMemberName
	}
	return ""
}

// 4741 创建机器账户 && 4781 修改账户名称
type DomainRiskCVE2021_42278 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Target            *TargetType  `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Subject           *SubjectType `protobuf:"bytes,2,opt,name=subject,proto3" json:"subject,omitempty"`
	OldTargetUserName string       `protobuf:"bytes,3,opt,name=oldTargetUserName,proto3" json:"oldTargetUserName,omitempty"`
	NewTargetUserName string       `protobuf:"bytes,4,opt,name=newTargetUserName,proto3" json:"newTargetUserName,omitempty"`
}

func (x *DomainRiskCVE2021_42278) Reset() {
	*x = DomainRiskCVE2021_42278{}
	mi := &file_agent_risk_system_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskCVE2021_42278) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskCVE2021_42278) ProtoMessage() {}

func (x *DomainRiskCVE2021_42278) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskCVE2021_42278.ProtoReflect.Descriptor instead.
func (*DomainRiskCVE2021_42278) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{33}
}

func (x *DomainRiskCVE2021_42278) GetTarget() *TargetType {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *DomainRiskCVE2021_42278) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskCVE2021_42278) GetOldTargetUserName() string {
	if x != nil {
		return x.OldTargetUserName
	}
	return ""
}

func (x *DomainRiskCVE2021_42278) GetNewTargetUserName() string {
	if x != nil {
		return x.NewTargetUserName
	}
	return ""
}

// 5136 ACL异常修改
type DomainRiskAclAbnormalUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject *SubjectType `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrIp   string       `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
	Object  *ObjectType  `protobuf:"bytes,3,opt,name=object,proto3" json:"object,omitempty"`
}

func (x *DomainRiskAclAbnormalUpdate) Reset() {
	*x = DomainRiskAclAbnormalUpdate{}
	mi := &file_agent_risk_system_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskAclAbnormalUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskAclAbnormalUpdate) ProtoMessage() {}

func (x *DomainRiskAclAbnormalUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskAclAbnormalUpdate.ProtoReflect.Descriptor instead.
func (*DomainRiskAclAbnormalUpdate) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{34}
}

func (x *DomainRiskAclAbnormalUpdate) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskAclAbnormalUpdate) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

func (x *DomainRiskAclAbnormalUpdate) GetObject() *ObjectType {
	if x != nil {
		return x.Object
	}
	return nil
}

// 4769 Kerberos 请求服务票证
type DomainRiskCVE2021_42287 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Target        *TargetType `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	Family        uint32      `protobuf:"varint,2,opt,name=family,proto3" json:"family,omitempty"` //AF_INET6:23 、 AF_INET:2
	RemoteAddress *NetInfo    `protobuf:"bytes,3,opt,name=remoteAddress,proto3" json:"remoteAddress,omitempty"`
}

func (x *DomainRiskCVE2021_42287) Reset() {
	*x = DomainRiskCVE2021_42287{}
	mi := &file_agent_risk_system_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskCVE2021_42287) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskCVE2021_42287) ProtoMessage() {}

func (x *DomainRiskCVE2021_42287) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskCVE2021_42287.ProtoReflect.Descriptor instead.
func (*DomainRiskCVE2021_42287) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{35}
}

func (x *DomainRiskCVE2021_42287) GetTarget() *TargetType {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *DomainRiskCVE2021_42287) GetFamily() uint32 {
	if x != nil {
		return x.Family
	}
	return 0
}

func (x *DomainRiskCVE2021_42287) GetRemoteAddress() *NetInfo {
	if x != nil {
		return x.RemoteAddress
	}
	return nil
}

// 4661  查看敏感用户/组
type DomainRiskCheckSensitiveGroupOrUser struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Subject       *SubjectType                                   `protobuf:"bytes,1,opt,name=subject,proto3" json:"subject,omitempty"`
	StrIp         string                                         `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
	StrTargetName string                                         `protobuf:"bytes,3,opt,name=strTargetName,proto3" json:"strTargetName,omitempty"`
	Modify        DomainRiskCheckSensitiveGroupOrUser_ModifyType `protobuf:"varint,4,opt,name=modify,proto3,enum=agent.DomainRiskCheckSensitiveGroupOrUser_ModifyType" json:"modify,omitempty"`
}

func (x *DomainRiskCheckSensitiveGroupOrUser) Reset() {
	*x = DomainRiskCheckSensitiveGroupOrUser{}
	mi := &file_agent_risk_system_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskCheckSensitiveGroupOrUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskCheckSensitiveGroupOrUser) ProtoMessage() {}

func (x *DomainRiskCheckSensitiveGroupOrUser) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskCheckSensitiveGroupOrUser.ProtoReflect.Descriptor instead.
func (*DomainRiskCheckSensitiveGroupOrUser) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{36}
}

func (x *DomainRiskCheckSensitiveGroupOrUser) GetSubject() *SubjectType {
	if x != nil {
		return x.Subject
	}
	return nil
}

func (x *DomainRiskCheckSensitiveGroupOrUser) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

func (x *DomainRiskCheckSensitiveGroupOrUser) GetStrTargetName() string {
	if x != nil {
		return x.StrTargetName
	}
	return ""
}

func (x *DomainRiskCheckSensitiveGroupOrUser) GetModify() DomainRiskCheckSensitiveGroupOrUser_ModifyType {
	if x != nil {
		return x.Modify
	}
	return DomainRiskCheckSensitiveGroupOrUser_mt_user
}

// 4768  AS-REP Roasting
type DomainRiskASREPRoasting struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Target    *TargetType `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	StrIp     string      `protobuf:"bytes,2,opt,name=strIp,proto3" json:"strIp,omitempty"`
	MatchTool string      `protobuf:"bytes,3,opt,name=matchTool,proto3" json:"matchTool,omitempty"`
}

func (x *DomainRiskASREPRoasting) Reset() {
	*x = DomainRiskASREPRoasting{}
	mi := &file_agent_risk_system_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainRiskASREPRoasting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainRiskASREPRoasting) ProtoMessage() {}

func (x *DomainRiskASREPRoasting) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_system_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainRiskASREPRoasting.ProtoReflect.Descriptor instead.
func (*DomainRiskASREPRoasting) Descriptor() ([]byte, []int) {
	return file_agent_risk_system_proto_rawDescGZIP(), []int{37}
}

func (x *DomainRiskASREPRoasting) GetTarget() *TargetType {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *DomainRiskASREPRoasting) GetStrIp() string {
	if x != nil {
		return x.StrIp
	}
	return ""
}

func (x *DomainRiskASREPRoasting) GetMatchTool() string {
	if x != nil {
		return x.MatchTool
	}
	return ""
}

var File_agent_risk_system_proto protoreflect.FileDescriptor

var file_agent_risk_system_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x05, 0x0a, 0x18, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x54, 0x0a, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x52, 0x65, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x52, 0x65, 0x67, 0x52, 0x17,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66,
	0x52, 0x65, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x70, 0x70, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f,
	0x66, 0x41, 0x70, 0x70, 0x52, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a,
	0x14, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65,
	0x72, 0x6e, 0x65, 0x6c, 0x52, 0x14, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x52, 0x69,
	0x73, 0x6b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x52, 0x0e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0c, 0x44, 0x69, 0x72, 0x74, 0x79, 0x43, 0x6f,
	0x77, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x69, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x77, 0x52, 0x0c, 0x44, 0x69,
	0x72, 0x74, 0x79, 0x43, 0x6f, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x14, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67,
	0x65, 0x52, 0x14, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c,
	0x65, 0x67, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x60, 0x0a, 0x1b, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x1b, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x52, 0x69, 0x73,
	0x6b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x52, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xad, 0x01, 0x0a,
	0x16, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c,
	0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0a, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc7, 0x01, 0x0a,
	0x13, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f,
	0x66, 0x52, 0x65, 0x67, 0x12, 0x49, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62,
	0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x18, 0x0a, 0x07, 0x72, 0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x72, 0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x12, 0x29, 0x0a, 0x05, 0x72, 0x65, 0x67,
	0x4f, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x72,
	0x65, 0x67, 0x4f, 0x70, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x53, 0x65, 0x74, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x72, 0x65, 0x67, 0x53, 0x65,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xf2, 0x01, 0x0a, 0x13, 0x52, 0x69, 0x73, 0x6b, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x70, 0x70, 0x12, 0x49,
	0x0a, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f,
	0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x75, 0x62,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x32, 0x0a, 0x14, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x14, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x34, 0x0a, 0x15, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x22, 0xa2, 0x04, 0x0a, 0x10,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x45, 0x0a, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x48, 0x00, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x68, 0x69, 0x64, 0x65, 0x6d,
	0x6f, 0x64, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x48, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x48, 0x00, 0x52, 0x07, 0x68, 0x69, 0x64, 0x65,
	0x6d, 0x6f, 0x64, 0x12, 0x38, 0x0a, 0x08, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x73, 0x18,
	0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x45, 0x78,
	0x73, 0x48, 0x00, 0x52, 0x08, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x78, 0x73, 0x12, 0x36, 0x0a,
	0x0b, 0x68, 0x69, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x68, 0x69, 0x64, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x6c, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4c, 0x65, 0x61, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x08, 0x6c, 0x65, 0x61, 0x6b, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x07,
	0x63, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0e, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x07, 0x63, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x32, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63,
	0x22, 0x7c, 0x0a, 0x10, 0x4c, 0x65, 0x61, 0x6b, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x44,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x63, 0x76, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x2e,
	0x0a, 0x07, 0x43, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xde,
	0x01, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x49, 0x0a,
	0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x0a, 0x08, 0x72, 0x69, 0x73, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72,
	0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49,
	0x50, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x50, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x55, 0x0a, 0x08, 0x44, 0x69, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x77, 0x12, 0x49, 0x0a, 0x10, 0x62,
	0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65,
	0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x77, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x62, 0x61,
	0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d,
	0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x23, 0x0a, 0x07, 0x48, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f,
	0x64, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x2c, 0x0a, 0x10, 0x49, 0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74,
	0x79, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0x9b, 0x01, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4e, 0x6f, 0x4d, 0x6f, 0x64, 0x12, 0x28, 0x0a, 0x0f,
	0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61,
	0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c,
	0x6c, 0x42, 0x61, 0x63, 0x6b, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c,
	0x6c, 0x42, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x73, 0x74, 0x72, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x41, 0x64, 0x64, 0x72,
	0x22, 0xc1, 0x01, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x61, 0x6d, 0x6c, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74,
	0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x73, 0x74, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x12, 0x73, 0x74, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x53, 0x75, 0x62,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73,
	0x74, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x12, 0x24,
	0x0a, 0x0d, 0x73, 0x74, 0x72, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x41, 0x64, 0x64, 0x72, 0x22, 0xa2, 0x01, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x74, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x72, 0x43, 0x68,
	0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x73,
	0x74, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x72, 0x43, 0x68, 0x61,
	0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x73, 0x74, 0x72, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x72, 0x43, 0x68, 0x61, 0x72,
	0x61, 0x63, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xf2, 0x01, 0x0a, 0x10, 0x52, 0x69,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x12, 0x28,
	0x0a, 0x0f, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x4d, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x74, 0x72, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d,
	0x6f, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x13, 0x73, 0x74,
	0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x42, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63,
	0x6b, 0x4d, 0x6f, 0x64, 0x42, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x28, 0x0a, 0x0f,
	0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d,
	0x6f, 0x64, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x74, 0x72, 0x42, 0x6c, 0x61,
	0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x74, 0x72, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x82,
	0x01, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x54, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72,
	0x4d, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x74, 0x72, 0x4d, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x74, 0x72,
	0x4d, 0x6f, 0x64, 0x42, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x74, 0x72, 0x4d, 0x6f, 0x64, 0x42, 0x61, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x74, 0x72,
	0x54, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x73, 0x74, 0x72, 0x54, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x22, 0x86, 0x05, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x52, 0x0a,
	0x0d, 0x65, 0x4b, 0x72, 0x6e, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x2e, 0x4b, 0x72, 0x6e, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0d, 0x65, 0x4b, 0x72, 0x6e, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x48, 0x0a, 0x0f, 0x73, 0x74, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4e,
	0x6f, 0x4d, 0x6f, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x4e, 0x6f, 0x4d, 0x6f, 0x64, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x43, 0x61,
	0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4e, 0x6f, 0x4d, 0x6f, 0x64, 0x12, 0x48, 0x0a, 0x0f, 0x73,
	0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x61,
	0x6d, 0x6c, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x41, 0x62, 0x6e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x4b, 0x0a, 0x10, 0x73, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x10, 0x73, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x43, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74,
	0x65, 0x72, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x48,
	0x00, 0x52, 0x0a, 0x73, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4d, 0x6f, 0x64, 0x12, 0x4b, 0x0a,
	0x10, 0x73, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x54, 0x61, 0x6d, 0x70, 0x65,
	0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4d, 0x6f, 0x64,
	0x54, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52, 0x10, 0x73, 0x74, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4d, 0x6f, 0x64, 0x54, 0x61, 0x6d, 0x70, 0x65, 0x72, 0x22, 0x98, 0x01, 0x0a, 0x0f, 0x4b,
	0x72, 0x6e, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x6e, 0x6f, 0x6d, 0x6f, 0x64, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x65, 0x5f, 0x72,
	0x69, 0x73, 0x6b, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x6e, 0x6f, 0x74, 0x5f, 0x6e, 0x6f,
	0x72, 0x61, 0x6d, 0x6c, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x61, 0x63, 0x74, 0x65, 0x72,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x65, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x62, 0x6c, 0x61,
	0x63, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x65, 0x5f, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x5f, 0x74, 0x61, 0x6d,
	0x70, 0x65, 0x72, 0x10, 0x04, 0x42, 0x15, 0x0a, 0x13, 0x4b, 0x72, 0x6e, 0x6c, 0x52, 0x69, 0x73,
	0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x8d, 0x01, 0x0a,
	0x13, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x45, 0x78, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x5a, 0x0a, 0x19, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65,
	0x72, 0x6e, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x19, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4b, 0x65, 0x72,
	0x6e, 0x65, 0x6c, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xef, 0x03, 0x0a,
	0x17, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f,
	0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x69,
	0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x50, 0x6f, 0x72, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70,
	0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x47, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6c,
	0x6f, 0x67, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x0f, 0x61, 0x62, 0x6e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4f, 0x66, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x52, 0x0e, 0x61, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x22, 0x42, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x16, 0x6c, 0x74, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x6c, 0x74, 0x5f,
	0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x10, 0x01, 0x22, 0x48, 0x0a, 0x0e, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x0e, 0x61, 0x72, 0x5f, 0x62, 0x72, 0x75,
	0x74, 0x65, 0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x61, 0x72,
	0x5f, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x64, 0x10, 0x01, 0x12, 0x0e,
	0x0a, 0x0a, 0x61, 0x72, 0x5f, 0x75, 0x6e, 0x75, 0x73, 0x75, 0x61, 0x6c, 0x10, 0x02, 0x22, 0xae,
	0x0c, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x20, 0x0a, 0x0b, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x54, 0x69, 0x6d, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0d, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x58,
	0x6d, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x58,
	0x6d, 0x6c, 0x12, 0x31, 0x0a, 0x08, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x52, 0x69, 0x73,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x4b, 0x0a, 0x10, 0x72, 0x73, 0x5f, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6c,
	0x65, 0x61, 0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x73, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x60, 0x0a, 0x17,
	0x72, 0x73, 0x5f, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x16, 0x72, 0x73, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x78,
	0x0a, 0x1f, 0x72, 0x73, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x1e, 0x72, 0x73, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x60, 0x0a, 0x19, 0x72, 0x73, 0x5f, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x6e, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x64,
	0x64, 0x50, 0x6c, 0x61, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x48, 0x00,
	0x52, 0x18, 0x72, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x60, 0x0a, 0x19, 0x72, 0x73,
	0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x41, 0x64, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x00, 0x52, 0x18, 0x72, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x57, 0x0a, 0x14,
	0x72, 0x73, 0x5f, 0x50, 0x73, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x64, 0x6f, 0x6e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x72, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x73, 0x6c,
	0x6f, 0x67, 0x67, 0x65, 0x64, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x48, 0x00,
	0x52, 0x13, 0x72, 0x73, 0x50, 0x73, 0x6c, 0x6f, 0x67, 0x67, 0x65, 0x64, 0x6f, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x67, 0x0a, 0x17, 0x72, 0x73, 0x5f, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x72, 0x48, 0x00, 0x52, 0x16, 0x72, 0x73, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x56,
	0x0a, 0x16, 0x72, 0x73, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x56, 0x45, 0x32, 0x30,
	0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x37, 0x38, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x56, 0x45, 0x32, 0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x37, 0x38, 0x48, 0x00,
	0x52, 0x14, 0x72, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x56, 0x45, 0x32, 0x30, 0x32,
	0x31, 0x34, 0x32, 0x32, 0x37, 0x38, 0x12, 0x57, 0x0a, 0x14, 0x72, 0x73, 0x5f, 0x41, 0x63, 0x6c,
	0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x63, 0x6c, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x13, 0x72, 0x73, 0x41, 0x63,
	0x6c, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x56, 0x0a, 0x16, 0x72, 0x73, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x56, 0x45, 0x32,
	0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x38, 0x37, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69,
	0x73, 0x6b, 0x43, 0x56, 0x45, 0x32, 0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x38, 0x37, 0x48,
	0x00, 0x52, 0x14, 0x72, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x56, 0x45, 0x32, 0x30,
	0x32, 0x31, 0x34, 0x32, 0x32, 0x38, 0x37, 0x12, 0x6f, 0x0a, 0x1c, 0x72, 0x73, 0x5f, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x48, 0x00, 0x52, 0x1b, 0x72, 0x73, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x4b, 0x0a, 0x10, 0x72, 0x73, 0x5f, 0x41,
	0x53, 0x52, 0x45, 0x50, 0x52, 0x6f, 0x61, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x1f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x53, 0x52, 0x45, 0x50, 0x52, 0x6f, 0x61, 0x73, 0x74, 0x69,
	0x6e, 0x67, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x73, 0x41, 0x53, 0x52, 0x45, 0x50, 0x52, 0x6f, 0x61,
	0x73, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x08, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0xb5, 0x01, 0x0a, 0x0b, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2c, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x53, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x75, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x64, 0x12,
	0x26, 0x0a, 0x0e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x96, 0x02, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x64,
	0x12, 0x1c, 0x0a, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x69, 0x64, 0x12, 0x24,
	0x0a, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x49, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x6f, 0x67,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2a, 0x0a, 0x10, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x6c, 0x0a, 0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x70, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x70, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x22, 0x7e,
	0x0a, 0x0a, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x14,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x24, 0x0a, 0x0d, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xce,
	0x01, 0x0a, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x4e, 0x12, 0x1e, 0x0a, 0x0a, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x47, 0x55, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x47, 0x55, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x4f, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12,
	0x1e, 0x0a, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x5d, 0x0a, 0x17, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x22, 0x8d,
	0x01, 0x0a, 0x1e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x73, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x61, 0x67, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74,
	0x72, 0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70,
	0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x6c,
	0x0a, 0x26, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a,
	0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x22, 0x82, 0x01, 0x0a,
	0x1a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x64, 0x64, 0x50, 0x6c,
	0x61, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x74, 0x72,
	0x54, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x74, 0x72, 0x54, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x72, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49,
	0x70, 0x22, 0x88, 0x01, 0x0a, 0x1a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x41, 0x64, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x73, 0x74, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x22, 0x61, 0x0a, 0x1b,
	0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x50, 0x73, 0x6c, 0x6f, 0x67, 0x67,
	0x65, 0x64, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x73,
	0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72,
	0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x22,
	0xba, 0x01, 0x0a, 0x25, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x27, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x74, 0x61, 0x67, 0x65, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x74, 0x72, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73,
	0x74, 0x72, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xce, 0x01, 0x0a,
	0x17, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x56, 0x45, 0x32, 0x30,
	0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x37, 0x38, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63,
	0x74, 0x12, 0x2c, 0x0a, 0x11, 0x6f, 0x6c, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6f, 0x6c,
	0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2c, 0x0a, 0x11, 0x6e, 0x65, 0x77, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6e, 0x65, 0x77, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8c, 0x01,
	0x0a, 0x1b, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x63, 0x6c, 0x41,
	0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73,
	0x74, 0x72, 0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49,
	0x70, 0x12, 0x29, 0x0a, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x22, 0x92, 0x01, 0x0a,
	0x17, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x56, 0x45, 0x32, 0x30,
	0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x38, 0x37, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x06, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x34, 0x0a, 0x0d, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x87, 0x02, 0x0a, 0x23, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x73, 0x75, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a,
	0x0d, 0x73, 0x74, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x74, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x65, 0x6e, 0x73, 0x69,
	0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x2e,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x22, 0x27, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0b, 0x0a, 0x07, 0x6d, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x6d, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x10, 0x01, 0x22, 0x78, 0x0a, 0x17, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x53, 0x52, 0x45, 0x50, 0x52, 0x6f,
	0x61, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x72, 0x49, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x54, 0x6f, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x54, 0x6f, 0x6f, 0x6c, 0x2a, 0x65, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0d, 0x0a, 0x09, 0x72, 0x67, 0x6f, 0x5f, 0x75, 0x6e, 0x73,
	0x65, 0x74, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0a, 0x72, 0x67, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x10, 0x81, 0x80, 0x08, 0x12, 0x10, 0x0a, 0x0a, 0x72, 0x67, 0x6f, 0x5f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x10, 0x82, 0x80, 0x08, 0x12, 0x11, 0x0a, 0x0b, 0x72, 0x67, 0x6f, 0x5f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x31, 0x10, 0x84, 0x80, 0x08, 0x12, 0x0f, 0x0a, 0x09, 0x72,
	0x67, 0x6f, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x10, 0x86, 0x80, 0x08, 0x2a, 0xab, 0x01, 0x0a,
	0x0e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0f, 0x0a, 0x0b, 0x6b, 0x72, 0x74, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x6b, 0x72, 0x74, 0x5f, 0x68, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x6b, 0x72, 0x74, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65,
	0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x6b, 0x72, 0x74, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x5f, 0x6c, 0x65, 0x61, 0x6b, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x10, 0x03,
	0x12, 0x16, 0x0a, 0x12, 0x6b, 0x72, 0x74, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x6b, 0x69, 0x74, 0x5f,
	0x68, 0x69, 0x64, 0x6d, 0x6f, 0x64, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x6b, 0x72, 0x74, 0x5f,
	0x65, 0x78, 0x5f, 0x6b, 0x72, 0x6e, 0x6c, 0x72, 0x69, 0x73, 0x6b, 0x10, 0x05, 0x12, 0x0b, 0x0a,
	0x07, 0x6b, 0x72, 0x74, 0x5f, 0x63, 0x76, 0x65, 0x10, 0x06, 0x2a, 0x76, 0x0a, 0x0a, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x61, 0x74, 0x5f, 0x75,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x61, 0x74, 0x5f, 0x67,
	0x6f, 0x6c, 0x64, 0x65, 0x6e, 0x5f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x61, 0x74, 0x5f, 0x70, 0x74, 0x68, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x61, 0x74,
	0x5f, 0x77, 0x6f, 0x72, 0x6d, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x61, 0x74, 0x5f, 0x63, 0x76,
	0x65, 0x32, 0x30, 0x32, 0x30, 0x5f, 0x31, 0x34, 0x37, 0x32, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10,
	0x61, 0x74, 0x5f, 0x63, 0x76, 0x65, 0x32, 0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x38, 0x37,
	0x10, 0x05, 0x2a, 0x87, 0x03, 0x0a, 0x0e, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x52, 0x69, 0x73,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x64, 0x72, 0x5f, 0x75, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x64, 0x72, 0x5f, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x64,
	0x72, 0x5f, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x65, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x64, 0x72, 0x5f, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73,
	0x10, 0x03, 0x12, 0x23, 0x0a, 0x1f, 0x64, 0x72, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f,
	0x62, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x64, 0x72, 0x5f, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x54,
	0x61, 0x73, 0x6b, 0x73, 0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x64, 0x72, 0x5f, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x10, 0x06, 0x12, 0x18, 0x0a, 0x14, 0x64, 0x72, 0x5f, 0x50, 0x73, 0x6c, 0x6f,
	0x67, 0x67, 0x65, 0x64, 0x6f, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x72, 0x10, 0x07, 0x12,
	0x1b, 0x0a, 0x17, 0x64, 0x72, 0x5f, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x65, 0x6e, 0x73,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11,
	0x64, 0x72, 0x5f, 0x43, 0x56, 0x45, 0x5f, 0x32, 0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32, 0x37,
	0x38, 0x10, 0x09, 0x12, 0x18, 0x0a, 0x14, 0x64, 0x72, 0x5f, 0x41, 0x63, 0x6c, 0x41, 0x62, 0x6e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x0a, 0x12, 0x15, 0x0a,
	0x11, 0x64, 0x72, 0x5f, 0x43, 0x56, 0x45, 0x5f, 0x32, 0x30, 0x32, 0x31, 0x5f, 0x34, 0x32, 0x32,
	0x38, 0x37, 0x10, 0x0b, 0x12, 0x20, 0x0a, 0x1c, 0x64, 0x72, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4f, 0x72,
	0x55, 0x73, 0x65, 0x72, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10, 0x64, 0x72, 0x5f, 0x41, 0x53, 0x52,
	0x45, 0x50, 0x52, 0x6f, 0x61, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x0d, 0x42, 0x2a, 0x5a, 0x28,
	0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30,
	0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_system_proto_rawDescOnce sync.Once
	file_agent_risk_system_proto_rawDescData = file_agent_risk_system_proto_rawDesc
)

func file_agent_risk_system_proto_rawDescGZIP() []byte {
	file_agent_risk_system_proto_rawDescOnce.Do(func() {
		file_agent_risk_system_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_system_proto_rawDescData)
	})
	return file_agent_risk_system_proto_rawDescData
}

var file_agent_risk_system_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_agent_risk_system_proto_msgTypes = make([]protoimpl.MessageInfo, 38)
var file_agent_risk_system_proto_goTypes = []any{
	(RegOperation)(0),                                   // 0: agent.RegOperation
	(KernelRiskType)(0),                                 // 1: agent.KernelRiskType
	(AttackType)(0),                                     // 2: agent.AttackType
	(DomainRiskType)(0),                                 // 3: agent.DomainRiskType
	(RiskSystemKernelEntry_KrnlRiskSubType)(0),          // 4: agent.RiskSystemKernelEntry.KrnlRiskSubType
	(RiskSystemInfoOfAccount_LogonType)(0),              // 5: agent.RiskSystemInfoOfAccount.LogonType
	(RiskSystemInfoOfAccount_AbnormalReason)(0),         // 6: agent.RiskSystemInfoOfAccount.AbnormalReason
	(DomainRiskCheckSensitiveGroupOrUser_ModifyType)(0), // 7: agent.DomainRiskCheckSensitiveGroupOrUser.ModifyType
	(*MemProtectRiskSystemInfo)(nil),                    // 8: agent.MemProtectRiskSystemInfo
	(*MemProtectRiskBaseInfo)(nil),                      // 9: agent.MemProtectRiskBaseInfo
	(*RiskSystemInfoOfReg)(nil),                         // 10: agent.RiskSystemInfoOfReg
	(*RiskSystemInfoOfApp)(nil),                         // 11: agent.RiskSystemInfoOfApp
	(*RiskSystemKernel)(nil),                            // 12: agent.RiskSystemKernel
	(*LeakOverflowInfo)(nil),                            // 13: agent.LeakOverflowInfo
	(*CveInfo)(nil),                                     // 14: agent.CveInfo
	(*RiskAttack)(nil),                                  // 15: agent.RiskAttack
	(*DirtyCow)(nil),                                    // 16: agent.DirtyCow
	(*ProcessPrivilege)(nil),                            // 17: agent.ProcessPrivilege
	(*HideMod)(nil),                                     // 18: agent.HideMod
	(*IntegrityFailure)(nil),                            // 19: agent.IntegrityFailure
	(*RiskInfoCallbackNoMod)(nil),                       // 20: agent.RiskInfoCallbackNoMod
	(*RiskInfoValueAbnoraml)(nil),                       // 21: agent.RiskInfoValueAbnoraml
	(*RiskInfoBlackCharacter)(nil),                      // 22: agent.RiskInfoBlackCharacter
	(*RiskInfoBlackMod)(nil),                            // 23: agent.RiskInfoBlackMod
	(*RiskInfoWhiteModTamper)(nil),                      // 24: agent.RiskInfoWhiteModTamper
	(*RiskSystemKernelEntry)(nil),                       // 25: agent.RiskSystemKernelEntry
	(*RiskSystemKernelExs)(nil),                         // 26: agent.RiskSystemKernelExs
	(*RiskSystemInfoOfAccount)(nil),                     // 27: agent.RiskSystemInfoOfAccount
	(*RiskDomainEventLog)(nil),                          // 28: agent.RiskDomainEventLog
	(*SubjectType)(nil),                                 // 29: agent.SubjectType
	(*TargetType)(nil),                                  // 30: agent.TargetType
	(*Sourcetype)(nil),                                  // 31: agent.Sourcetype
	(*TicketType)(nil),                                  // 32: agent.TicketType
	(*ObjectType)(nil),                                  // 33: agent.ObjectType
	(*DomainRiskEventLogClear)(nil),                     // 34: agent.DomainRiskEventLogClear
	(*DomainRiskLoginWithCredentials)(nil),              // 35: agent.DomainRiskLoginWithCredentials
	(*DomainRiskCreateDirectoryServiceObject)(nil),      // 36: agent.DomainRiskCreateDirectoryServiceObject
	(*DomainRiskAddPlanningTasks)(nil),                  // 37: agent.DomainRiskAddPlanningTasks
	(*DomainRiskAddSystemservice)(nil),                  // 38: agent.DomainRiskAddSystemservice
	(*DomainRiskPsloggedonHandler)(nil),                 // 39: agent.DomainRiskPsloggedonHandler
	(*DomainRiskModifySensitiveGroupHandler)(nil),       // 40: agent.DomainRiskModifySensitiveGroupHandler
	(*DomainRiskCVE2021_42278)(nil),                     // 41: agent.DomainRiskCVE2021_42278
	(*DomainRiskAclAbnormalUpdate)(nil),                 // 42: agent.DomainRiskAclAbnormalUpdate
	(*DomainRiskCVE2021_42287)(nil),                     // 43: agent.DomainRiskCVE2021_42287
	(*DomainRiskCheckSensitiveGroupOrUser)(nil),         // 44: agent.DomainRiskCheckSensitiveGroupOrUser
	(*DomainRiskASREPRoasting)(nil),                     // 45: agent.DomainRiskASREPRoasting
	(*RiskHeader)(nil),                                  // 46: agent.RiskHeader
	(*ProcessInfo)(nil),                                 // 47: agent.ProcessInfo
	(*NetInfo)(nil),                                     // 48: agent.NetInfo
}
var file_agent_risk_system_proto_depIdxs = []int32{
	10, // 0: agent.MemProtectRiskSystemInfo.RiskSystemInfoOfRegList:type_name -> agent.RiskSystemInfoOfReg
	11, // 1: agent.MemProtectRiskSystemInfo.RiskSystemInfoOfAppList:type_name -> agent.RiskSystemInfoOfApp
	12, // 2: agent.MemProtectRiskSystemInfo.RiskSystemKernelList:type_name -> agent.RiskSystemKernel
	15, // 3: agent.MemProtectRiskSystemInfo.RiskAttackList:type_name -> agent.RiskAttack
	16, // 4: agent.MemProtectRiskSystemInfo.DirtyCowList:type_name -> agent.DirtyCow
	17, // 5: agent.MemProtectRiskSystemInfo.ProcessPrivilegeList:type_name -> agent.ProcessPrivilege
	27, // 6: agent.MemProtectRiskSystemInfo.RiskSystemInfoOfAccountList:type_name -> agent.RiskSystemInfoOfAccount
	28, // 7: agent.MemProtectRiskSystemInfo.RiskDomainEventLogList:type_name -> agent.RiskDomainEventLog
	46, // 8: agent.MemProtectRiskBaseInfo.header:type_name -> agent.RiskHeader
	47, // 9: agent.MemProtectRiskBaseInfo.Process:type_name -> agent.ProcessInfo
	9,  // 10: agent.RiskSystemInfoOfReg.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	0,  // 11: agent.RiskSystemInfoOfReg.regOp:type_name -> agent.RegOperation
	9,  // 12: agent.RiskSystemInfoOfApp.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	46, // 13: agent.RiskSystemKernel.header:type_name -> agent.RiskHeader
	1,  // 14: agent.RiskSystemKernel.riskType:type_name -> agent.KernelRiskType
	19, // 15: agent.RiskSystemKernel.integrityFailure:type_name -> agent.IntegrityFailure
	18, // 16: agent.RiskSystemKernel.hidemod:type_name -> agent.HideMod
	26, // 17: agent.RiskSystemKernel.entryExs:type_name -> agent.RiskSystemKernelExs
	47, // 18: agent.RiskSystemKernel.hideProcess:type_name -> agent.ProcessInfo
	13, // 19: agent.RiskSystemKernel.leakinfo:type_name -> agent.LeakOverflowInfo
	14, // 20: agent.RiskSystemKernel.cveInfo:type_name -> agent.CveInfo
	9,  // 21: agent.RiskAttack.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	2,  // 22: agent.RiskAttack.riskType:type_name -> agent.AttackType
	9,  // 23: agent.DirtyCow.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	9,  // 24: agent.ProcessPrivilege.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	4,  // 25: agent.RiskSystemKernelEntry.eKrnlRiskType:type_name -> agent.RiskSystemKernelEntry.KrnlRiskSubType
	20, // 26: agent.RiskSystemKernelEntry.stCallbackNoMod:type_name -> agent.RiskInfoCallbackNoMod
	21, // 27: agent.RiskSystemKernelEntry.stValueAbnormal:type_name -> agent.RiskInfoValueAbnoraml
	22, // 28: agent.RiskSystemKernelEntry.stBlackCharacter:type_name -> agent.RiskInfoBlackCharacter
	23, // 29: agent.RiskSystemKernelEntry.stBlackMod:type_name -> agent.RiskInfoBlackMod
	24, // 30: agent.RiskSystemKernelEntry.stWhiteModTamper:type_name -> agent.RiskInfoWhiteModTamper
	25, // 31: agent.RiskSystemKernelExs.RiskSystemKernelEntryList:type_name -> agent.RiskSystemKernelEntry
	9,  // 32: agent.RiskSystemInfoOfAccount.basicPorcessInfo:type_name -> agent.MemProtectRiskBaseInfo
	5,  // 33: agent.RiskSystemInfoOfAccount.logon_type:type_name -> agent.RiskSystemInfoOfAccount.LogonType
	6,  // 34: agent.RiskSystemInfoOfAccount.abnormal_reason:type_name -> agent.RiskSystemInfoOfAccount.AbnormalReason
	46, // 35: agent.RiskDomainEventLog.header:type_name -> agent.RiskHeader
	3,  // 36: agent.RiskDomainEventLog.RiskType:type_name -> agent.DomainRiskType
	47, // 37: agent.RiskDomainEventLog.ProcessSource:type_name -> agent.ProcessInfo
	34, // 38: agent.RiskDomainEventLog.rs_EventLogClear:type_name -> agent.DomainRiskEventLogClear
	35, // 39: agent.RiskDomainEventLog.rs_LoginWithCredentials:type_name -> agent.DomainRiskLoginWithCredentials
	36, // 40: agent.RiskDomainEventLog.rs_CreateDirectoryServiceObject:type_name -> agent.DomainRiskCreateDirectoryServiceObject
	37, // 41: agent.RiskDomainEventLog.rs_DomainAddPlanningTasks:type_name -> agent.DomainRiskAddPlanningTasks
	38, // 42: agent.RiskDomainEventLog.rs_DomainAddSystemservice:type_name -> agent.DomainRiskAddSystemservice
	39, // 43: agent.RiskDomainEventLog.rs_PsloggedonHandler:type_name -> agent.DomainRiskPsloggedonHandler
	40, // 44: agent.RiskDomainEventLog.rs_ModifySensitiveGroup:type_name -> agent.DomainRiskModifySensitiveGroupHandler
	41, // 45: agent.RiskDomainEventLog.rs_DomainCVE2021_42278:type_name -> agent.DomainRiskCVE2021_42278
	42, // 46: agent.RiskDomainEventLog.rs_AclAbnormalUpdate:type_name -> agent.DomainRiskAclAbnormalUpdate
	43, // 47: agent.RiskDomainEventLog.rs_DomainCVE2021_42287:type_name -> agent.DomainRiskCVE2021_42287
	44, // 48: agent.RiskDomainEventLog.rs_CheckSensitiveGroupOrUser:type_name -> agent.DomainRiskCheckSensitiveGroupOrUser
	45, // 49: agent.RiskDomainEventLog.rs_ASREPRoasting:type_name -> agent.DomainRiskASREPRoasting
	29, // 50: agent.DomainRiskEventLogClear.subject:type_name -> agent.SubjectType
	30, // 51: agent.DomainRiskLoginWithCredentials.taget:type_name -> agent.TargetType
	29, // 52: agent.DomainRiskLoginWithCredentials.subject:type_name -> agent.SubjectType
	29, // 53: agent.DomainRiskCreateDirectoryServiceObject.subject:type_name -> agent.SubjectType
	29, // 54: agent.DomainRiskAddPlanningTasks.subject:type_name -> agent.SubjectType
	29, // 55: agent.DomainRiskAddSystemservice.subject:type_name -> agent.SubjectType
	29, // 56: agent.DomainRiskPsloggedonHandler.subject:type_name -> agent.SubjectType
	29, // 57: agent.DomainRiskModifySensitiveGroupHandler.subject:type_name -> agent.SubjectType
	30, // 58: agent.DomainRiskModifySensitiveGroupHandler.taget:type_name -> agent.TargetType
	30, // 59: agent.DomainRiskCVE2021_42278.target:type_name -> agent.TargetType
	29, // 60: agent.DomainRiskCVE2021_42278.subject:type_name -> agent.SubjectType
	29, // 61: agent.DomainRiskAclAbnormalUpdate.subject:type_name -> agent.SubjectType
	33, // 62: agent.DomainRiskAclAbnormalUpdate.object:type_name -> agent.ObjectType
	30, // 63: agent.DomainRiskCVE2021_42287.target:type_name -> agent.TargetType
	48, // 64: agent.DomainRiskCVE2021_42287.remoteAddress:type_name -> agent.NetInfo
	29, // 65: agent.DomainRiskCheckSensitiveGroupOrUser.subject:type_name -> agent.SubjectType
	7,  // 66: agent.DomainRiskCheckSensitiveGroupOrUser.modify:type_name -> agent.DomainRiskCheckSensitiveGroupOrUser.ModifyType
	30, // 67: agent.DomainRiskASREPRoasting.target:type_name -> agent.TargetType
	68, // [68:68] is the sub-list for method output_type
	68, // [68:68] is the sub-list for method input_type
	68, // [68:68] is the sub-list for extension type_name
	68, // [68:68] is the sub-list for extension extendee
	0,  // [0:68] is the sub-list for field type_name
}

func init() { file_agent_risk_system_proto_init() }
func file_agent_risk_system_proto_init() {
	if File_agent_risk_system_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_risk_system_proto_msgTypes[4].OneofWrappers = []any{
		(*RiskSystemKernel_IntegrityFailure)(nil),
		(*RiskSystemKernel_Hidemod)(nil),
		(*RiskSystemKernel_EntryExs)(nil),
		(*RiskSystemKernel_HideProcess)(nil),
		(*RiskSystemKernel_Leakinfo)(nil),
		(*RiskSystemKernel_CveInfo)(nil),
	}
	file_agent_risk_system_proto_msgTypes[17].OneofWrappers = []any{
		(*RiskSystemKernelEntry_StCallbackNoMod)(nil),
		(*RiskSystemKernelEntry_StValueAbnormal)(nil),
		(*RiskSystemKernelEntry_StBlackCharacter)(nil),
		(*RiskSystemKernelEntry_StBlackMod)(nil),
		(*RiskSystemKernelEntry_StWhiteModTamper)(nil),
	}
	file_agent_risk_system_proto_msgTypes[20].OneofWrappers = []any{
		(*RiskDomainEventLog_Rs_EventLogClear)(nil),
		(*RiskDomainEventLog_Rs_LoginWithCredentials)(nil),
		(*RiskDomainEventLog_Rs_CreateDirectoryServiceObject)(nil),
		(*RiskDomainEventLog_Rs_DomainAddPlanningTasks)(nil),
		(*RiskDomainEventLog_Rs_DomainAddSystemservice)(nil),
		(*RiskDomainEventLog_Rs_PsloggedonHandler)(nil),
		(*RiskDomainEventLog_Rs_ModifySensitiveGroup)(nil),
		(*RiskDomainEventLog_Rs_DomainCVE2021_42278)(nil),
		(*RiskDomainEventLog_Rs_AclAbnormalUpdate)(nil),
		(*RiskDomainEventLog_Rs_DomainCVE2021_42287)(nil),
		(*RiskDomainEventLog_Rs_CheckSensitiveGroupOrUser)(nil),
		(*RiskDomainEventLog_Rs_ASREPRoasting)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_system_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   38,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_system_proto_goTypes,
		DependencyIndexes: file_agent_risk_system_proto_depIdxs,
		EnumInfos:         file_agent_risk_system_proto_enumTypes,
		MessageInfos:      file_agent_risk_system_proto_msgTypes,
	}.Build()
	File_agent_risk_system_proto = out.File
	file_agent_risk_system_proto_rawDesc = nil
	file_agent_risk_system_proto_goTypes = nil
	file_agent_risk_system_proto_depIdxs = nil
}
