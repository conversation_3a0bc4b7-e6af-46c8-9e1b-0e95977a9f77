// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/command.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 服务端和agent通信命令字
type Command int32

const (
	Command_CMD_UNKNOWN                          Command = 0    // 未定义
	Command_CMD_ASSETS_INFO                      Command = 1    // 资产信息 agent->server->agent
	Command_CMD_RISK_FILE_INFO                   Command = 2    // 文件类风险 agent->server->agent
	Command_CMD_RISK_MEM_INFO                    Command = 3    // 内存类风险 agent->server->agent
	Command_CMD_RISK_PROC_INFO                   Command = 4    // 进程类风险 agent->server->agent
	Command_CMD_RISK_SYS_INFO                    Command = 7    // 系统类风险 agent->server->agent
	Command_CMD_ACDR_RULE_MESSAGE_REPORT         Command = 8    // ACDR 事件消息上报 agent->server->agent
	Command_CMD_ACDR_RULE_ALL_MESSAGE_REPORT     Command = 9    // ACDR 全量消息上报 agent->server->agent
	Command_CMD_HEARTBEAT                        Command = 10   // 心跳消息 agent->server->agent
	Command_CMD_RES_FILE                         Command = 22   // 请求文件响应消息 server->agent
	Command_CMD_UPDATE_RISK_BLACK_WHITE_POLICY   Command = 24   // 下发攻击风险黑白名单策略 server->agent
	Command_CMD_LICENSE_ENABLE                   Command = 27   // 请求license agent->server
	Command_CMD_LICENSE_ENABLE_RES               Command = 28   // 请求license响应 server->agent
	Command_CMD_AGENT_UPGRADE                    Command = 29   // agent升级命令 server->agent
	Command_CMD_AGENT_UPGRADE_RES                Command = 30   // agent升级响应 agent->server->agent
	Command_CMD_AGENT_MIGRATE                    Command = 31   // agent迁移 server->agent
	Command_CMD_AGENT_MIGRATE_RES                Command = 32   // agent迁移结果上报 agent->server->agent
	Command_CMD_MODULE_STATUS                    Command = 36   // 驱动加载状态 agent->server->agent
	Command_CMD_CUSTOM_FILE_BLACK_WHITE_POLICY   Command = 38   // 个性化文件黑白名单 server->agent
	Command_CMD_DIGITAL_SIGNATURE_POLICY         Command = 39   // 数字签名 server->agent
	Command_CMD_ACCOUNT_RISK_CONFIG              Command = 40   // 账户风险策略配置 server->agent
	Command_CMD_RESET_BLUE_SCREEN_COUNT          Command = 42   // 蓝屏避障状态重启 agent
	Command_CMD_NOTICE_UPLOAD_FILE               Command = 43   // 通知上传文件 server->agent
	Command_CMD_UPLOAD_FILE                      Command = 44   // 上传文件 agent->server->agent
	Command_CMD_SERVER_DRIVER_UPGRADE_NOTIFY     Command = 45   // server端下发驱动升级通知
	Command_CMD_AGENT_DRIVER_UPGRADE_REQUEST     Command = 46   // agent上传内核版本
	Command_CMD_AGENT_DRIVER_UPGRADE_RESULT      Command = 47   // agent上报驱动升级结果
	Command_CMD_SET_DRIVER_STATUS                Command = 48   // 驱动开关 server->agent
	Command_CMD_REPORT_DRIVER_STATUS             Command = 49   // 驱动状态上报 agent->server->agent
	Command_CMD_AGENT_UNINSTALL                  Command = 51   // agent卸载命令
	Command_CMD_AGENT_UNINSTALL_RESULT           Command = 52   // agent卸载结果响应
	Command_CMD_HASH_ENGINE_VERSION              Command = 53   // agent上传特征库版本
	Command_CMD_HASH_ENGINE_UPGRADE_RESULT       Command = 54   // agent上报特征库升级结果
	Command_CMD_WEB_SHELL_SCAN_RESULT            Command = 56   // webshell扫描结果
	Command_CMD_WEB_SHELL_SCAN_UPLOAD            Command = 57   // 文件上传
	Command_CMD_MEM_PROTECT_VIRUS_FILE_INFO      Command = 58   // webshell风险上报
	Command_CMD_WEB_SHELL_REVERSE                Command = 59   // webshell预留
	Command_CMD_RASP_DATA_REPORT                 Command = 60   // rasp内存马数据上报
	Command_CMD_RASP_INJECTION_REQUEST           Command = 61   // rasp内存马注入命令请求
	Command_CMD_RASP_INJECTION_RESPONSE          Command = 62   // rasp内存马注入命令回复
	Command_CMD_RASP_UNINSTALL_REQUEST           Command = 63   // rasp内存马卸载命令请求
	Command_CMD_RASP_UNINSTALL_RESPONSE          Command = 64   // rasp内存马卸载命令回复
	Command_CMD_AGENT_INFO_REPORT_NOTIFY         Command = 66   // agent信息上报通知
	Command_CMD_ANTI_VIRUS_POLICY                Command = 67   // 杀毒扫描策略
	Command_CMD_ANTI_VIRUS_RESULT                Command = 68   // 杀毒扫描结果
	Command_CMD_ANTI_VIRUS_UPLOAD                Command = 69   // 杀毒文件上传
	Command_CMD_SHA256_ENGINE_VERSION            Command = 70   // agent上传基线库版本
	Command_CMD_SHA256_ENGINE_UPGRADE_RESULT     Command = 71   // agent上报基线库升级结果
	Command_CMD_FILE_HANDLING_DELETE_FILES       Command = 72   // 通知agent删除威胁文件
	Command_CMD_FILES_REPORT_PROGRESS            Command = 73   // 文件采集进度 agent->server
	Command_CMD_POLICY_NEW_COMMON                Command = 100  // 策略
	Command_CMD_AGENT_ENGINE_LIB_UPGRADE_REQUEST Command = 101  // V01 agent引擎库统一升级请求
	Command_CMD_AGENT_ENGINE_LIB_UPGRADE_RESULT  Command = 102  // V01 agent引擎库统一升级结果
	Command_CMD_FILE_UPLOAD_NOTIFY               Command = 201  // 文件上传通知 server -> agent
	Command_CMD_NOTIFY_UPLOAD_EVIDENCE_FILE      Command = 202  // 通知上传证据文件 server->agent
	Command_CMD_REPORT_OBTAIN_EVIDENCE_RESULT    Command = 203  // 报告取证结果 agent->server
	Command_CMD_UPGRADE_NOTIFY_RESPONSE          Command = 204  // 升级通知应答 agent -> server
	Command_CMD_DELETE_AGENT_FILES_HANDLING      Command = 205  // Agent 文件隔离, 删除, 恢复
	Command_CMD_AGENT_REPORT_OUTREACH            Command = 206  // Agent 上报外连
	Command_CMD_WARNING_REPORT                   Command = 207  // Agent 异常警告信息上报
	Command_CMD_COLLECT_SYS_LOGS                 Command = 209  // 收集 Agent 运行日志 (dump, 运行日志等)
	Command_CMD_ACK                              Command = 1000 // 通用应答
	Command_CMD_PULL                             Command = 1001 // 拉取消息数据
)

// Enum value maps for Command.
var (
	Command_name = map[int32]string{
		0:    "CMD_UNKNOWN",
		1:    "CMD_ASSETS_INFO",
		2:    "CMD_RISK_FILE_INFO",
		3:    "CMD_RISK_MEM_INFO",
		4:    "CMD_RISK_PROC_INFO",
		7:    "CMD_RISK_SYS_INFO",
		8:    "CMD_ACDR_RULE_MESSAGE_REPORT",
		9:    "CMD_ACDR_RULE_ALL_MESSAGE_REPORT",
		10:   "CMD_HEARTBEAT",
		22:   "CMD_RES_FILE",
		24:   "CMD_UPDATE_RISK_BLACK_WHITE_POLICY",
		27:   "CMD_LICENSE_ENABLE",
		28:   "CMD_LICENSE_ENABLE_RES",
		29:   "CMD_AGENT_UPGRADE",
		30:   "CMD_AGENT_UPGRADE_RES",
		31:   "CMD_AGENT_MIGRATE",
		32:   "CMD_AGENT_MIGRATE_RES",
		36:   "CMD_MODULE_STATUS",
		38:   "CMD_CUSTOM_FILE_BLACK_WHITE_POLICY",
		39:   "CMD_DIGITAL_SIGNATURE_POLICY",
		40:   "CMD_ACCOUNT_RISK_CONFIG",
		42:   "CMD_RESET_BLUE_SCREEN_COUNT",
		43:   "CMD_NOTICE_UPLOAD_FILE",
		44:   "CMD_UPLOAD_FILE",
		45:   "CMD_SERVER_DRIVER_UPGRADE_NOTIFY",
		46:   "CMD_AGENT_DRIVER_UPGRADE_REQUEST",
		47:   "CMD_AGENT_DRIVER_UPGRADE_RESULT",
		48:   "CMD_SET_DRIVER_STATUS",
		49:   "CMD_REPORT_DRIVER_STATUS",
		51:   "CMD_AGENT_UNINSTALL",
		52:   "CMD_AGENT_UNINSTALL_RESULT",
		53:   "CMD_HASH_ENGINE_VERSION",
		54:   "CMD_HASH_ENGINE_UPGRADE_RESULT",
		56:   "CMD_WEB_SHELL_SCAN_RESULT",
		57:   "CMD_WEB_SHELL_SCAN_UPLOAD",
		58:   "CMD_MEM_PROTECT_VIRUS_FILE_INFO",
		59:   "CMD_WEB_SHELL_REVERSE",
		60:   "CMD_RASP_DATA_REPORT",
		61:   "CMD_RASP_INJECTION_REQUEST",
		62:   "CMD_RASP_INJECTION_RESPONSE",
		63:   "CMD_RASP_UNINSTALL_REQUEST",
		64:   "CMD_RASP_UNINSTALL_RESPONSE",
		66:   "CMD_AGENT_INFO_REPORT_NOTIFY",
		67:   "CMD_ANTI_VIRUS_POLICY",
		68:   "CMD_ANTI_VIRUS_RESULT",
		69:   "CMD_ANTI_VIRUS_UPLOAD",
		70:   "CMD_SHA256_ENGINE_VERSION",
		71:   "CMD_SHA256_ENGINE_UPGRADE_RESULT",
		72:   "CMD_FILE_HANDLING_DELETE_FILES",
		73:   "CMD_FILES_REPORT_PROGRESS",
		100:  "CMD_POLICY_NEW_COMMON",
		101:  "CMD_AGENT_ENGINE_LIB_UPGRADE_REQUEST",
		102:  "CMD_AGENT_ENGINE_LIB_UPGRADE_RESULT",
		201:  "CMD_FILE_UPLOAD_NOTIFY",
		202:  "CMD_NOTIFY_UPLOAD_EVIDENCE_FILE",
		203:  "CMD_REPORT_OBTAIN_EVIDENCE_RESULT",
		204:  "CMD_UPGRADE_NOTIFY_RESPONSE",
		205:  "CMD_DELETE_AGENT_FILES_HANDLING",
		206:  "CMD_AGENT_REPORT_OUTREACH",
		207:  "CMD_WARNING_REPORT",
		209:  "CMD_COLLECT_SYS_LOGS",
		1000: "CMD_ACK",
		1001: "CMD_PULL",
	}
	Command_value = map[string]int32{
		"CMD_UNKNOWN":                          0,
		"CMD_ASSETS_INFO":                      1,
		"CMD_RISK_FILE_INFO":                   2,
		"CMD_RISK_MEM_INFO":                    3,
		"CMD_RISK_PROC_INFO":                   4,
		"CMD_RISK_SYS_INFO":                    7,
		"CMD_ACDR_RULE_MESSAGE_REPORT":         8,
		"CMD_ACDR_RULE_ALL_MESSAGE_REPORT":     9,
		"CMD_HEARTBEAT":                        10,
		"CMD_RES_FILE":                         22,
		"CMD_UPDATE_RISK_BLACK_WHITE_POLICY":   24,
		"CMD_LICENSE_ENABLE":                   27,
		"CMD_LICENSE_ENABLE_RES":               28,
		"CMD_AGENT_UPGRADE":                    29,
		"CMD_AGENT_UPGRADE_RES":                30,
		"CMD_AGENT_MIGRATE":                    31,
		"CMD_AGENT_MIGRATE_RES":                32,
		"CMD_MODULE_STATUS":                    36,
		"CMD_CUSTOM_FILE_BLACK_WHITE_POLICY":   38,
		"CMD_DIGITAL_SIGNATURE_POLICY":         39,
		"CMD_ACCOUNT_RISK_CONFIG":              40,
		"CMD_RESET_BLUE_SCREEN_COUNT":          42,
		"CMD_NOTICE_UPLOAD_FILE":               43,
		"CMD_UPLOAD_FILE":                      44,
		"CMD_SERVER_DRIVER_UPGRADE_NOTIFY":     45,
		"CMD_AGENT_DRIVER_UPGRADE_REQUEST":     46,
		"CMD_AGENT_DRIVER_UPGRADE_RESULT":      47,
		"CMD_SET_DRIVER_STATUS":                48,
		"CMD_REPORT_DRIVER_STATUS":             49,
		"CMD_AGENT_UNINSTALL":                  51,
		"CMD_AGENT_UNINSTALL_RESULT":           52,
		"CMD_HASH_ENGINE_VERSION":              53,
		"CMD_HASH_ENGINE_UPGRADE_RESULT":       54,
		"CMD_WEB_SHELL_SCAN_RESULT":            56,
		"CMD_WEB_SHELL_SCAN_UPLOAD":            57,
		"CMD_MEM_PROTECT_VIRUS_FILE_INFO":      58,
		"CMD_WEB_SHELL_REVERSE":                59,
		"CMD_RASP_DATA_REPORT":                 60,
		"CMD_RASP_INJECTION_REQUEST":           61,
		"CMD_RASP_INJECTION_RESPONSE":          62,
		"CMD_RASP_UNINSTALL_REQUEST":           63,
		"CMD_RASP_UNINSTALL_RESPONSE":          64,
		"CMD_AGENT_INFO_REPORT_NOTIFY":         66,
		"CMD_ANTI_VIRUS_POLICY":                67,
		"CMD_ANTI_VIRUS_RESULT":                68,
		"CMD_ANTI_VIRUS_UPLOAD":                69,
		"CMD_SHA256_ENGINE_VERSION":            70,
		"CMD_SHA256_ENGINE_UPGRADE_RESULT":     71,
		"CMD_FILE_HANDLING_DELETE_FILES":       72,
		"CMD_FILES_REPORT_PROGRESS":            73,
		"CMD_POLICY_NEW_COMMON":                100,
		"CMD_AGENT_ENGINE_LIB_UPGRADE_REQUEST": 101,
		"CMD_AGENT_ENGINE_LIB_UPGRADE_RESULT":  102,
		"CMD_FILE_UPLOAD_NOTIFY":               201,
		"CMD_NOTIFY_UPLOAD_EVIDENCE_FILE":      202,
		"CMD_REPORT_OBTAIN_EVIDENCE_RESULT":    203,
		"CMD_UPGRADE_NOTIFY_RESPONSE":          204,
		"CMD_DELETE_AGENT_FILES_HANDLING":      205,
		"CMD_AGENT_REPORT_OUTREACH":            206,
		"CMD_WARNING_REPORT":                   207,
		"CMD_COLLECT_SYS_LOGS":                 209,
		"CMD_ACK":                              1000,
		"CMD_PULL":                             1001,
	}
)

func (x Command) Enum() *Command {
	p := new(Command)
	*p = x
	return p
}

func (x Command) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Command) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_command_proto_enumTypes[0].Descriptor()
}

func (Command) Type() protoreflect.EnumType {
	return &file_agent_command_proto_enumTypes[0]
}

func (x Command) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Command.Descriptor instead.
func (Command) EnumDescriptor() ([]byte, []int) {
	return file_agent_command_proto_rawDescGZIP(), []int{0}
}

var File_agent_command_proto protoreflect.FileDescriptor

var file_agent_command_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2a, 0xd5, 0x0e, 0x0a,
	0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x4d, 0x44, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4d, 0x44,
	0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x01, 0x12, 0x16,
	0x0a, 0x12, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x49, 0x4e, 0x46, 0x4f, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x49,
	0x53, 0x4b, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x03, 0x12, 0x16, 0x0a,
	0x12, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x5f, 0x49,
	0x4e, 0x46, 0x4f, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x49, 0x53,
	0x4b, 0x5f, 0x53, 0x59, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x07, 0x12, 0x20, 0x0a, 0x1c,
	0x43, 0x4d, 0x44, 0x5f, 0x41, 0x43, 0x44, 0x52, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f, 0x4d, 0x45,
	0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x08, 0x12, 0x24,
	0x0a, 0x20, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x43, 0x44, 0x52, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x4d, 0x45, 0x53, 0x53, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4d, 0x44, 0x5f, 0x48, 0x45, 0x41, 0x52,
	0x54, 0x42, 0x45, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x4d, 0x44, 0x5f, 0x52,
	0x45, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x16, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x4d, 0x44,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x42, 0x4c, 0x41,
	0x43, 0x4b, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10,
	0x18, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4d, 0x44, 0x5f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45,
	0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x1b, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4d, 0x44,
	0x5f, 0x4c, 0x49, 0x43, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f,
	0x52, 0x45, 0x53, 0x10, 0x1c, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x1d, 0x12, 0x19, 0x0a, 0x15,
	0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44,
	0x45, 0x5f, 0x52, 0x45, 0x53, 0x10, 0x1e, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4d, 0x44, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x47, 0x52, 0x41, 0x54, 0x45, 0x10, 0x1f, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x47, 0x52,
	0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x10, 0x20, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4d, 0x44,
	0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x24,
	0x12, 0x26, 0x0a, 0x22, 0x43, 0x4d, 0x44, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x5f,
	0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x26, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f,
	0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x10, 0x27, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x4d,
	0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x43,
	0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x28, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x52,
	0x45, 0x53, 0x45, 0x54, 0x5f, 0x42, 0x4c, 0x55, 0x45, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x2a, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f,
	0x4e, 0x4f, 0x54, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x10, 0x2b, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4d, 0x44, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x2c, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4d, 0x44,
	0x5f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x55,
	0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x2d, 0x12,
	0x24, 0x0a, 0x20, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x44, 0x52, 0x49,
	0x56, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x10, 0x2e, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44,
	0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x2f, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4d,
	0x44, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x30, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x10, 0x31, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x10, 0x33, 0x12, 0x1e, 0x0a, 0x1a,
	0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x34, 0x12, 0x1b, 0x0a, 0x17,
	0x43, 0x4d, 0x44, 0x5f, 0x48, 0x41, 0x53, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x35, 0x12, 0x22, 0x0a, 0x1e, 0x43, 0x4d, 0x44,
	0x5f, 0x48, 0x41, 0x53, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x55, 0x50, 0x47,
	0x52, 0x41, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x36, 0x12, 0x1d, 0x0a,
	0x19, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x53,
	0x43, 0x41, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x38, 0x12, 0x1d, 0x0a, 0x19,
	0x43, 0x4d, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f, 0x53, 0x43,
	0x41, 0x4e, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x39, 0x12, 0x23, 0x0a, 0x1f, 0x43,
	0x4d, 0x44, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x56,
	0x49, 0x52, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x3a,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x5f, 0x53, 0x48, 0x45, 0x4c,
	0x4c, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x10, 0x3b, 0x12, 0x18, 0x0a, 0x14, 0x43,
	0x4d, 0x44, 0x5f, 0x52, 0x41, 0x53, 0x50, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x10, 0x3c, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x53,
	0x50, 0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x10, 0x3d, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41, 0x53,
	0x50, 0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x50,
	0x4f, 0x4e, 0x53, 0x45, 0x10, 0x3e, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41,
	0x53, 0x50, 0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x10, 0x3f, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x41,
	0x53, 0x50, 0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x45, 0x53,
	0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0x40, 0x12, 0x20, 0x0a, 0x1c, 0x43, 0x4d, 0x44, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0x42, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4d, 0x44,
	0x5f, 0x41, 0x4e, 0x54, 0x49, 0x5f, 0x56, 0x49, 0x52, 0x55, 0x53, 0x5f, 0x50, 0x4f, 0x4c, 0x49,
	0x43, 0x59, 0x10, 0x43, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x4e, 0x54, 0x49,
	0x5f, 0x56, 0x49, 0x52, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x44, 0x12,
	0x19, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x4e, 0x54, 0x49, 0x5f, 0x56, 0x49, 0x52, 0x55,
	0x53, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x45, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4d,
	0x44, 0x5f, 0x53, 0x48, 0x41, 0x32, 0x35, 0x36, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f,
	0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x46, 0x12, 0x24, 0x0a, 0x20, 0x43, 0x4d, 0x44,
	0x5f, 0x53, 0x48, 0x41, 0x32, 0x35, 0x36, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x55,
	0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x47, 0x12,
	0x22, 0x0a, 0x1e, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x48, 0x41, 0x4e, 0x44,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x53, 0x10, 0x48, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x49, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59,
	0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x10, 0x64, 0x12, 0x28, 0x0a,
	0x24, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e,
	0x45, 0x5f, 0x4c, 0x49, 0x42, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x52, 0x45,
	0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x4d, 0x44, 0x5f, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x4c, 0x49, 0x42, 0x5f,
	0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0x66,
	0x12, 0x1b, 0x0a, 0x16, 0x43, 0x4d, 0x44, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x50, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x10, 0xc9, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x59, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x10, 0xca, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x43, 0x4d, 0x44, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x4f, 0x42, 0x54, 0x41, 0x49, 0x4e, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x10, 0xcb, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x43,
	0x4d, 0x44, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x59, 0x5f, 0x52, 0x45, 0x53, 0x50, 0x4f, 0x4e, 0x53, 0x45, 0x10, 0xcc, 0x01, 0x12, 0x24, 0x0a,
	0x1f, 0x43, 0x4d, 0x44, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x41, 0x47, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47,
	0x10, 0xcd, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x43, 0x4d, 0x44, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4f, 0x55, 0x54, 0x52, 0x45, 0x41, 0x43, 0x48,
	0x10, 0xce, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x43, 0x4d, 0x44, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49,
	0x4e, 0x47, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0xcf, 0x01, 0x12, 0x19, 0x0a, 0x14,
	0x43, 0x4d, 0x44, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x59, 0x53, 0x5f,
	0x4c, 0x4f, 0x47, 0x53, 0x10, 0xd1, 0x01, 0x12, 0x0c, 0x0a, 0x07, 0x43, 0x4d, 0x44, 0x5f, 0x41,
	0x43, 0x4b, 0x10, 0xe8, 0x07, 0x12, 0x0d, 0x0a, 0x08, 0x43, 0x4d, 0x44, 0x5f, 0x50, 0x55, 0x4c,
	0x4c, 0x10, 0xe9, 0x07, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69,
	0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_command_proto_rawDescOnce sync.Once
	file_agent_command_proto_rawDescData = file_agent_command_proto_rawDesc
)

func file_agent_command_proto_rawDescGZIP() []byte {
	file_agent_command_proto_rawDescOnce.Do(func() {
		file_agent_command_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_command_proto_rawDescData)
	})
	return file_agent_command_proto_rawDescData
}

var file_agent_command_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_agent_command_proto_goTypes = []any{
	(Command)(0), // 0: agent.Command
}
var file_agent_command_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_agent_command_proto_init() }
func file_agent_command_proto_init() {
	if File_agent_command_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_command_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_command_proto_goTypes,
		DependencyIndexes: file_agent_command_proto_depIdxs,
		EnumInfos:         file_agent_command_proto_enumTypes,
	}.Build()
	File_agent_command_proto = out.File
	file_agent_command_proto_rawDesc = nil
	file_agent_command_proto_goTypes = nil
	file_agent_command_proto_depIdxs = nil
}
