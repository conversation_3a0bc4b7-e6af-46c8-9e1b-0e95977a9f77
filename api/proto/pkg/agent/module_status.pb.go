// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/module_status.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModuleReportType int32

const (
	ModuleReportType_DEFENSE          ModuleReportType = 0   // Windows
	ModuleReportType_MONITOR          ModuleReportType = 1   // Windows
	ModuleReportType_BEHAVIOR         ModuleReportType = 2   // Windows
	ModuleReportType_AXBININJ         ModuleReportType = 3   // Windows
	ModuleReportType_AXKRNLDEC        ModuleReportType = 4   // Windows
	ModuleReportType_MEM_SERVICE      ModuleReportType = 100 // linux
	ModuleReportType_MEM_NGAV_SERVICE ModuleReportType = 101 // linux
	ModuleReportType_MEM_NGAV_HOOK    ModuleReportType = 102 // linux
)

// Enum value maps for ModuleReportType.
var (
	ModuleReportType_name = map[int32]string{
		0:   "DEFENSE",
		1:   "MONITOR",
		2:   "BEHAVIOR",
		3:   "AXBININJ",
		4:   "AXKRNLDEC",
		100: "MEM_SERVICE",
		101: "MEM_NGAV_SERVICE",
		102: "MEM_NGAV_HOOK",
	}
	ModuleReportType_value = map[string]int32{
		"DEFENSE":          0,
		"MONITOR":          1,
		"BEHAVIOR":         2,
		"AXBININJ":         3,
		"AXKRNLDEC":        4,
		"MEM_SERVICE":      100,
		"MEM_NGAV_SERVICE": 101,
		"MEM_NGAV_HOOK":    102,
	}
)

func (x ModuleReportType) Enum() *ModuleReportType {
	p := new(ModuleReportType)
	*p = x
	return p
}

func (x ModuleReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[0].Descriptor()
}

func (ModuleReportType) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[0]
}

func (x ModuleReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleReportType.Descriptor instead.
func (ModuleReportType) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{0}
}

type ModuleLoadingStatus int32

const (
	ModuleLoadingStatus_MLS_UNKNOW       ModuleLoadingStatus = 0 //未知
	ModuleLoadingStatus_MLS_LOAD_SUCCESS ModuleLoadingStatus = 1 //驱动加载成功
	ModuleLoadingStatus_MLS_LOAD_FAILED  ModuleLoadingStatus = 2 //驱动加载失败
)

// Enum value maps for ModuleLoadingStatus.
var (
	ModuleLoadingStatus_name = map[int32]string{
		0: "MLS_UNKNOW",
		1: "MLS_LOAD_SUCCESS",
		2: "MLS_LOAD_FAILED",
	}
	ModuleLoadingStatus_value = map[string]int32{
		"MLS_UNKNOW":       0,
		"MLS_LOAD_SUCCESS": 1,
		"MLS_LOAD_FAILED":  2,
	}
)

func (x ModuleLoadingStatus) Enum() *ModuleLoadingStatus {
	p := new(ModuleLoadingStatus)
	*p = x
	return p
}

func (x ModuleLoadingStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleLoadingStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[1].Descriptor()
}

func (ModuleLoadingStatus) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[1]
}

func (x ModuleLoadingStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleLoadingStatus.Descriptor instead.
func (ModuleLoadingStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{1}
}

type ModuleConflictStatus int32

const (
	ModuleConflictStatus_MCS_UNKNOW   ModuleConflictStatus = 0
	ModuleConflictStatus_MCS_Conflict ModuleConflictStatus = 1 //模块有冲突
	ModuleConflictStatus_MCS_Normal   ModuleConflictStatus = 2 //模块正常，没有冲突
)

// Enum value maps for ModuleConflictStatus.
var (
	ModuleConflictStatus_name = map[int32]string{
		0: "MCS_UNKNOW",
		1: "MCS_Conflict",
		2: "MCS_Normal",
	}
	ModuleConflictStatus_value = map[string]int32{
		"MCS_UNKNOW":   0,
		"MCS_Conflict": 1,
		"MCS_Normal":   2,
	}
)

func (x ModuleConflictStatus) Enum() *ModuleConflictStatus {
	p := new(ModuleConflictStatus)
	*p = x
	return p
}

func (x ModuleConflictStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleConflictStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[2].Descriptor()
}

func (ModuleConflictStatus) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[2]
}

func (x ModuleConflictStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleConflictStatus.Descriptor instead.
func (ModuleConflictStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{2}
}

type ModuleEnableStatus int32

const (
	ModuleEnableStatus_MES_UNKNOW   ModuleEnableStatus = 0
	ModuleEnableStatus_MES_Enabled  ModuleEnableStatus = 1 //驱动正常
	ModuleEnableStatus_MES_Disabled ModuleEnableStatus = 2 //驱动暂停
)

// Enum value maps for ModuleEnableStatus.
var (
	ModuleEnableStatus_name = map[int32]string{
		0: "MES_UNKNOW",
		1: "MES_Enabled",
		2: "MES_Disabled",
	}
	ModuleEnableStatus_value = map[string]int32{
		"MES_UNKNOW":   0,
		"MES_Enabled":  1,
		"MES_Disabled": 2,
	}
)

func (x ModuleEnableStatus) Enum() *ModuleEnableStatus {
	p := new(ModuleEnableStatus)
	*p = x
	return p
}

func (x ModuleEnableStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleEnableStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[3].Descriptor()
}

func (ModuleEnableStatus) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[3]
}

func (x ModuleEnableStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleEnableStatus.Descriptor instead.
func (ModuleEnableStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{3}
}

type Status int32

const (
	Status_STATUS_UNKNOW    Status = 0
	Status_NOT_INJECTED     Status = 1 //未注入
	Status_INJECTED         Status = 2 //已注入
	Status_IN_INJECTION     Status = 3 //注入中
	Status_INJECTION_FAILED Status = 4 //注入失败
	Status_UNINSTALLED      Status = 5 //已卸载
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNKNOW",
		1: "NOT_INJECTED",
		2: "INJECTED",
		3: "IN_INJECTION",
		4: "INJECTION_FAILED",
		5: "UNINSTALLED",
	}
	Status_value = map[string]int32{
		"STATUS_UNKNOW":    0,
		"NOT_INJECTED":     1,
		"INJECTED":         2,
		"IN_INJECTION":     3,
		"INJECTION_FAILED": 4,
		"UNINSTALLED":      5,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[4].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[4]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{4}
}

type ProcessType int32

const (
	ProcessType_PROCESS_TYPE_UNKNOW ProcessType = 0
	ProcessType_JAVA                ProcessType = 1 //Java
	ProcessType_PHP                 ProcessType = 2 //PHP
)

// Enum value maps for ProcessType.
var (
	ProcessType_name = map[int32]string{
		0: "PROCESS_TYPE_UNKNOW",
		1: "JAVA",
		2: "PHP",
	}
	ProcessType_value = map[string]int32{
		"PROCESS_TYPE_UNKNOW": 0,
		"JAVA":                1,
		"PHP":                 2,
	}
)

func (x ProcessType) Enum() *ProcessType {
	p := new(ProcessType)
	*p = x
	return p
}

func (x ProcessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProcessType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[5].Descriptor()
}

func (ProcessType) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[5]
}

func (x ProcessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProcessType.Descriptor instead.
func (ProcessType) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{5}
}

// 内存马注入状态异常信息
type InjectErrorNum int32

const (
	InjectErrorNum_NULL_ERROR                   InjectErrorNum = 0
	InjectErrorNum_SYMBOL_LINK_ERROR            InjectErrorNum = 1  // Error querying process symbolic link  查询进程符号链接错误
	InjectErrorNum_PROCESS_STATUS_ERROR         InjectErrorNum = 2  // Error querying process status 查询进程状态错误
	InjectErrorNum_USERNAME_ERROR               InjectErrorNum = 3  // Error querying username by effective uid 按有效uid查询用户名时出错
	InjectErrorNum_INJECTION_COMMAND_ERROR      InjectErrorNum = 4  // Failed to execute injection command 执行注入命令失败
	InjectErrorNum_OPEN_JAVA_POLICYFILE_ERROR   InjectErrorNum = 5  // Failed to open Java switch policy file 打开java开关策略文件失败
	InjectErrorNum_CHMOD_JAVA_POLICYFILE_ERROR  InjectErrorNum = 6  // Failed to change the permission of Java switch policy file 更改java开关策略文件权限失败
	InjectErrorNum_DELETE_FILE_ERROR            InjectErrorNum = 7  // Delete file exception 删除文件异常
	InjectErrorNum_EXISTS_FILE_ERROR            InjectErrorNum = 8  // Determine whether there is an error in the file 判断文件是否存在出错
	InjectErrorNum_EXECUTE_RESTART_ERROR        InjectErrorNum = 9  // Failed to execute restart command 执行重启命令失败
	InjectErrorNum_NOTFOUND_LIB_ERROR           InjectErrorNum = 10 // No library files found 没有找到安装的库文件
	InjectErrorNum_EXCEPTION_ERROR              InjectErrorNum = 11 // Client exception 客户端异常
	InjectErrorNum_NOTFOUND_INI_FILE_ERROR      InjectErrorNum = 12 // Not found php.ini file   查询不到php.ini文件
	InjectErrorNum_GLIBC_NOT_FOUND_ERROR        InjectErrorNum = 13 // Not found glibc    查询不到glibc版本
	InjectErrorNum_GLIBC_NOT_SUPPORT_ERROR      InjectErrorNum = 14 // Not support current glibc version  不支持当前主机的glibc版本
	InjectErrorNum_RETURN_RESULT_OVERTIME_ERROR InjectErrorNum = 15 // Return inject result timeout 返回注入结果超时
	InjectErrorNum_SEMANAGE_NOT_FOUND_ERROR     InjectErrorNum = 16 // 缺少semanage命令，无法直接注入
	InjectErrorNum_LIBSTDCPLUS_SUPPORT_ERROR    InjectErrorNum = 17 // 当前的libstdc++版本过低
	InjectErrorNum_SELINUX_NOT_SUPPORT_ERROR    InjectErrorNum = 18 // 不支持selinux环境注入
)

// Enum value maps for InjectErrorNum.
var (
	InjectErrorNum_name = map[int32]string{
		0:  "NULL_ERROR",
		1:  "SYMBOL_LINK_ERROR",
		2:  "PROCESS_STATUS_ERROR",
		3:  "USERNAME_ERROR",
		4:  "INJECTION_COMMAND_ERROR",
		5:  "OPEN_JAVA_POLICYFILE_ERROR",
		6:  "CHMOD_JAVA_POLICYFILE_ERROR",
		7:  "DELETE_FILE_ERROR",
		8:  "EXISTS_FILE_ERROR",
		9:  "EXECUTE_RESTART_ERROR",
		10: "NOTFOUND_LIB_ERROR",
		11: "EXCEPTION_ERROR",
		12: "NOTFOUND_INI_FILE_ERROR",
		13: "GLIBC_NOT_FOUND_ERROR",
		14: "GLIBC_NOT_SUPPORT_ERROR",
		15: "RETURN_RESULT_OVERTIME_ERROR",
		16: "SEMANAGE_NOT_FOUND_ERROR",
		17: "LIBSTDCPLUS_SUPPORT_ERROR",
		18: "SELINUX_NOT_SUPPORT_ERROR",
	}
	InjectErrorNum_value = map[string]int32{
		"NULL_ERROR":                   0,
		"SYMBOL_LINK_ERROR":            1,
		"PROCESS_STATUS_ERROR":         2,
		"USERNAME_ERROR":               3,
		"INJECTION_COMMAND_ERROR":      4,
		"OPEN_JAVA_POLICYFILE_ERROR":   5,
		"CHMOD_JAVA_POLICYFILE_ERROR":  6,
		"DELETE_FILE_ERROR":            7,
		"EXISTS_FILE_ERROR":            8,
		"EXECUTE_RESTART_ERROR":        9,
		"NOTFOUND_LIB_ERROR":           10,
		"EXCEPTION_ERROR":              11,
		"NOTFOUND_INI_FILE_ERROR":      12,
		"GLIBC_NOT_FOUND_ERROR":        13,
		"GLIBC_NOT_SUPPORT_ERROR":      14,
		"RETURN_RESULT_OVERTIME_ERROR": 15,
		"SEMANAGE_NOT_FOUND_ERROR":     16,
		"LIBSTDCPLUS_SUPPORT_ERROR":    17,
		"SELINUX_NOT_SUPPORT_ERROR":    18,
	}
)

func (x InjectErrorNum) Enum() *InjectErrorNum {
	p := new(InjectErrorNum)
	*p = x
	return p
}

func (x InjectErrorNum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InjectErrorNum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[6].Descriptor()
}

func (InjectErrorNum) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[6]
}

func (x InjectErrorNum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InjectErrorNum.Descriptor instead.
func (InjectErrorNum) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{6}
}

// NGAV采集器运行状态
type NgavCollectorRunningStatus int32

const (
	NgavCollectorRunningStatus_NCS_TYPE_UNKNOW NgavCollectorRunningStatus = 0
	NgavCollectorRunningStatus_NCS_RUNNING     NgavCollectorRunningStatus = 1 //运行中
	NgavCollectorRunningStatus_NCS_SUSPEND     NgavCollectorRunningStatus = 2 //暂停
	NgavCollectorRunningStatus_NCS_ABNORMAL    NgavCollectorRunningStatus = 3 //异常
)

// Enum value maps for NgavCollectorRunningStatus.
var (
	NgavCollectorRunningStatus_name = map[int32]string{
		0: "NCS_TYPE_UNKNOW",
		1: "NCS_RUNNING",
		2: "NCS_SUSPEND",
		3: "NCS_ABNORMAL",
	}
	NgavCollectorRunningStatus_value = map[string]int32{
		"NCS_TYPE_UNKNOW": 0,
		"NCS_RUNNING":     1,
		"NCS_SUSPEND":     2,
		"NCS_ABNORMAL":    3,
	}
)

func (x NgavCollectorRunningStatus) Enum() *NgavCollectorRunningStatus {
	p := new(NgavCollectorRunningStatus)
	*p = x
	return p
}

func (x NgavCollectorRunningStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NgavCollectorRunningStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[7].Descriptor()
}

func (NgavCollectorRunningStatus) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[7]
}

func (x NgavCollectorRunningStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NgavCollectorRunningStatus.Descriptor instead.
func (NgavCollectorRunningStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{7}
}

type WarningReportType int32

const (
	WarningReportType_Unknow               WarningReportType = 0  // 未知
	WarningReportType_ServiceInjectTimeOut WarningReportType = 1  // 服务重复注入崩溃
	WarningReportType_TimeOutCheck         WarningReportType = 2  // 模块超时
	WarningReportType_AgentCrash           WarningReportType = 3  // Agent崩溃
	WarningReportType_OSCrash              WarningReportType = 4  // 系统崩溃
	WarningReportType_UnexpectShutdown     WarningReportType = 5  // 意外关机
	WarningReportType_ManyHandle           WarningReportType = 6  // 忘记关闭句柄
	WarningReportType_ManyMemory           WarningReportType = 7  // 内存泄漏
	WarningReportType_InjectDllCrash       WarningReportType = 8  // 注入程序崩溃
	WarningReportType_InjectDllCrashFound  WarningReportType = 9  // 注入程序崩溃文件发现
	WarningReportType_ReportSuppression    WarningReportType = 10 // 误报压制
	WarningReportType_TriggerSelfDowngrade WarningReportType = 11 // agent 触发自降级通知  (+ 神甲1700版本新增)
	WarningReportType_ResourceOverLimit    WarningReportType = 12 // agent 资源超过限制（+ 神甲1700版本新增）
	WarningReportType_RestoreSelfDowngrade WarningReportType = 13 // agent 恢复自降级通知 (+ 神甲1700版本新增）
	WarningReportType_DriverAbnormal       WarningReportType = 14 // 驱动异常
	WarningReportType_HandleNoClose        WarningReportType = 15 // 句柄未关闭(oplock)
	WarningReportType_AgentNotOK           WarningReportType = 16 // Agent状态异常
)

// Enum value maps for WarningReportType.
var (
	WarningReportType_name = map[int32]string{
		0:  "Unknow",
		1:  "ServiceInjectTimeOut",
		2:  "TimeOutCheck",
		3:  "AgentCrash",
		4:  "OSCrash",
		5:  "UnexpectShutdown",
		6:  "ManyHandle",
		7:  "ManyMemory",
		8:  "InjectDllCrash",
		9:  "InjectDllCrashFound",
		10: "ReportSuppression",
		11: "TriggerSelfDowngrade",
		12: "ResourceOverLimit",
		13: "RestoreSelfDowngrade",
		14: "DriverAbnormal",
		15: "HandleNoClose",
		16: "AgentNotOK",
	}
	WarningReportType_value = map[string]int32{
		"Unknow":               0,
		"ServiceInjectTimeOut": 1,
		"TimeOutCheck":         2,
		"AgentCrash":           3,
		"OSCrash":              4,
		"UnexpectShutdown":     5,
		"ManyHandle":           6,
		"ManyMemory":           7,
		"InjectDllCrash":       8,
		"InjectDllCrashFound":  9,
		"ReportSuppression":    10,
		"TriggerSelfDowngrade": 11,
		"ResourceOverLimit":    12,
		"RestoreSelfDowngrade": 13,
		"DriverAbnormal":       14,
		"HandleNoClose":        15,
		"AgentNotOK":           16,
	}
)

func (x WarningReportType) Enum() *WarningReportType {
	p := new(WarningReportType)
	*p = x
	return p
}

func (x WarningReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WarningReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[8].Descriptor()
}

func (WarningReportType) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[8]
}

func (x WarningReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WarningReportType.Descriptor instead.
func (WarningReportType) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{8}
}

type DriverAbnormalType int32

const (
	DriverAbnormalType_DAT_Unknow             DriverAbnormalType = 0 // 未知
	DriverAbnormalType_DAT_KernelBehavorTrace DriverAbnormalType = 1 // 内核行为跟踪异常
)

// Enum value maps for DriverAbnormalType.
var (
	DriverAbnormalType_name = map[int32]string{
		0: "DAT_Unknow",
		1: "DAT_KernelBehavorTrace",
	}
	DriverAbnormalType_value = map[string]int32{
		"DAT_Unknow":             0,
		"DAT_KernelBehavorTrace": 1,
	}
)

func (x DriverAbnormalType) Enum() *DriverAbnormalType {
	p := new(DriverAbnormalType)
	*p = x
	return p
}

func (x DriverAbnormalType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DriverAbnormalType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[9].Descriptor()
}

func (DriverAbnormalType) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[9]
}

func (x DriverAbnormalType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DriverAbnormalType.Descriptor instead.
func (DriverAbnormalType) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{9}
}

type CollectLogsType_Enum int32

const (
	CollectLogsType_Unknown      CollectLogsType_Enum = 0
	CollectLogsType_SysDump      CollectLogsType_Enum = 1 // 系统 dump 日志
	CollectLogsType_AgentDump    CollectLogsType_Enum = 2 // Agent dump 日志
	CollectLogsType_SysEvent     CollectLogsType_Enum = 3 // 系统事件日志
	CollectLogsType_AgentRunning CollectLogsType_Enum = 4 // 系统运行日志
)

// Enum value maps for CollectLogsType_Enum.
var (
	CollectLogsType_Enum_name = map[int32]string{
		0: "Unknown",
		1: "SysDump",
		2: "AgentDump",
		3: "SysEvent",
		4: "AgentRunning",
	}
	CollectLogsType_Enum_value = map[string]int32{
		"Unknown":      0,
		"SysDump":      1,
		"AgentDump":    2,
		"SysEvent":     3,
		"AgentRunning": 4,
	}
)

func (x CollectLogsType_Enum) Enum() *CollectLogsType_Enum {
	p := new(CollectLogsType_Enum)
	*p = x
	return p
}

func (x CollectLogsType_Enum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CollectLogsType_Enum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_module_status_proto_enumTypes[10].Descriptor()
}

func (CollectLogsType_Enum) Type() protoreflect.EnumType {
	return &file_agent_module_status_proto_enumTypes[10]
}

func (x CollectLogsType_Enum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CollectLogsType_Enum.Descriptor instead.
func (CollectLogsType_Enum) EnumDescriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{28, 0}
}

// --------------------------------------------------
//
//	运行状态上报
//	对应 g_CmdMemProtectStatusReport
//
// --------------------------------------------------
type MemProtectModulesStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleStatus               []*ModuleStatus      `protobuf:"bytes,1,rep,name=moduleStatus,proto3" json:"moduleStatus,omitempty"`
	AgentStatus                []*AgentStatus       `protobuf:"bytes,2,rep,name=agentStatus,proto3" json:"agentStatus,omitempty"`
	IsBlueCountOverLimit       bool                 `protobuf:"varint,3,opt,name=IsBlueCountOverLimit,proto3" json:"IsBlueCountOverLimit,omitempty"`
	AgentCrashStatus           *AgentCrashStatus    `protobuf:"bytes,4,opt,name=agentCrashStatus,proto3" json:"agentCrashStatus,omitempty"`
	Conflict                   ModuleConflictStatus `protobuf:"varint,5,opt,name=conflict,proto3,enum=agent.ModuleConflictStatus" json:"conflict,omitempty"`     // 总的冲突状态, 有一个驱动冲突，结果是冲突
	IsAgentResOverLimitRestart bool                 `protobuf:"varint,6,opt,name=IsAgentResOverLimitRestart,proto3" json:"IsAgentResOverLimitRestart,omitempty"` // agent资源超限重启
	WarningReport              []*WarningReport     `protobuf:"bytes,7,rep,name=warningReport,proto3" json:"warningReport,omitempty"`
}

func (x *MemProtectModulesStatus) Reset() {
	*x = MemProtectModulesStatus{}
	mi := &file_agent_module_status_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectModulesStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectModulesStatus) ProtoMessage() {}

func (x *MemProtectModulesStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectModulesStatus.ProtoReflect.Descriptor instead.
func (*MemProtectModulesStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectModulesStatus) GetModuleStatus() []*ModuleStatus {
	if x != nil {
		return x.ModuleStatus
	}
	return nil
}

func (x *MemProtectModulesStatus) GetAgentStatus() []*AgentStatus {
	if x != nil {
		return x.AgentStatus
	}
	return nil
}

func (x *MemProtectModulesStatus) GetIsBlueCountOverLimit() bool {
	if x != nil {
		return x.IsBlueCountOverLimit
	}
	return false
}

func (x *MemProtectModulesStatus) GetAgentCrashStatus() *AgentCrashStatus {
	if x != nil {
		return x.AgentCrashStatus
	}
	return nil
}

func (x *MemProtectModulesStatus) GetConflict() ModuleConflictStatus {
	if x != nil {
		return x.Conflict
	}
	return ModuleConflictStatus_MCS_UNKNOW
}

func (x *MemProtectModulesStatus) GetIsAgentResOverLimitRestart() bool {
	if x != nil {
		return x.IsAgentResOverLimitRestart
	}
	return false
}

func (x *MemProtectModulesStatus) GetWarningReport() []*WarningReport {
	if x != nil {
		return x.WarningReport
	}
	return nil
}

type ModuleStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleType          ModuleReportType    `protobuf:"varint,1,opt,name=moduleType,proto3,enum=agent.ModuleReportType" json:"moduleType,omitempty"`
	IsRun               bool                `protobuf:"varint,2,opt,name=isRun,proto3" json:"isRun,omitempty"` // 这个先不动 现在这个和loadingstatus是一样的
	StopTime            string              `protobuf:"bytes,3,opt,name=stopTime,proto3" json:"stopTime,omitempty"`
	ModuleLoadingStatus ModuleLoadingStatus `protobuf:"varint,4,opt,name=moduleLoadingStatus,proto3,enum=agent.ModuleLoadingStatus" json:"moduleLoadingStatus,omitempty"` // 2表示驱动加载失败
	ModuleEnableStatus  ModuleEnableStatus  `protobuf:"varint,5,opt,name=moduleEnableStatus,proto3,enum=agent.ModuleEnableStatus" json:"moduleEnableStatus,omitempty"`
}

func (x *ModuleStatus) Reset() {
	*x = ModuleStatus{}
	mi := &file_agent_module_status_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModuleStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleStatus) ProtoMessage() {}

func (x *ModuleStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleStatus.ProtoReflect.Descriptor instead.
func (*ModuleStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{1}
}

func (x *ModuleStatus) GetModuleType() ModuleReportType {
	if x != nil {
		return x.ModuleType
	}
	return ModuleReportType_DEFENSE
}

func (x *ModuleStatus) GetIsRun() bool {
	if x != nil {
		return x.IsRun
	}
	return false
}

func (x *ModuleStatus) GetStopTime() string {
	if x != nil {
		return x.StopTime
	}
	return ""
}

func (x *ModuleStatus) GetModuleLoadingStatus() ModuleLoadingStatus {
	if x != nil {
		return x.ModuleLoadingStatus
	}
	return ModuleLoadingStatus_MLS_UNKNOW
}

func (x *ModuleStatus) GetModuleEnableStatus() ModuleEnableStatus {
	if x != nil {
		return x.ModuleEnableStatus
	}
	return ModuleEnableStatus_MES_UNKNOW
}

// Agent 完整性校验
type AgentStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"` // 名称
	Status   bool   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`    // 校验结果
}

func (x *AgentStatus) Reset() {
	*x = AgentStatus{}
	mi := &file_agent_module_status_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentStatus) ProtoMessage() {}

func (x *AgentStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentStatus.ProtoReflect.Descriptor instead.
func (*AgentStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{2}
}

func (x *AgentStatus) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *AgentStatus) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// Agent 状态上报
type AgentCrashStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StopTime string `protobuf:"bytes,1,opt,name=stopTime,proto3" json:"stopTime,omitempty"` // crash时间
}

func (x *AgentCrashStatus) Reset() {
	*x = AgentCrashStatus{}
	mi := &file_agent_module_status_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentCrashStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentCrashStatus) ProtoMessage() {}

func (x *AgentCrashStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentCrashStatus.ProtoReflect.Descriptor instead.
func (*AgentCrashStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{3}
}

func (x *AgentCrashStatus) GetStopTime() string {
	if x != nil {
		return x.StopTime
	}
	return ""
}

// agent->server
type InjectionStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName                   string      `protobuf:"bytes,1,opt,name=processName,proto3" json:"processName,omitempty"` //usr/local/PHP-7.4/sbin/php-fpm
	Status                        Status      `protobuf:"varint,2,opt,name=status,proto3,enum=agent.Status" json:"status,omitempty"`
	MiddlewareVersion             string      `protobuf:"bytes,3,opt,name=middlewareVersion,proto3" json:"middlewareVersion,omitempty"` //nginx/1.12.2
	Ppid                          uint32      `protobuf:"varint,4,opt,name=ppid,proto3" json:"ppid,omitempty"`                          //父进程PID
	Pids                          []uint32    `protobuf:"varint,5,rep,packed,name=pids,proto3" json:"pids,omitempty"`                   //子进程PID
	ProcessApplication            ProcessType `protobuf:"varint,6,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
	ApplicationVersion            string      `protobuf:"bytes,7,opt,name=applicationVersion,proto3" json:"applicationVersion,omitempty"`                        //ProcessType版本信息
	InjectionUnixTimeMsecFrom1970 uint64      `protobuf:"varint,8,opt,name=injectionUnixTimeMsecFrom1970,proto3" json:"injectionUnixTimeMsecFrom1970,omitempty"` //毫秒时间戳
	Pid                           uint32      `protobuf:"varint,9,opt,name=pid,proto3" json:"pid,omitempty"`                                                     //当前进程的PID
	ErrorNum                      uint32      `protobuf:"varint,10,opt,name=errorNum,proto3" json:"errorNum,omitempty"`                                          //错误描述
}

func (x *InjectionStatus) Reset() {
	*x = InjectionStatus{}
	mi := &file_agent_module_status_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectionStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectionStatus) ProtoMessage() {}

func (x *InjectionStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectionStatus.ProtoReflect.Descriptor instead.
func (*InjectionStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{4}
}

func (x *InjectionStatus) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *InjectionStatus) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNKNOW
}

func (x *InjectionStatus) GetMiddlewareVersion() string {
	if x != nil {
		return x.MiddlewareVersion
	}
	return ""
}

func (x *InjectionStatus) GetPpid() uint32 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *InjectionStatus) GetPids() []uint32 {
	if x != nil {
		return x.Pids
	}
	return nil
}

func (x *InjectionStatus) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

func (x *InjectionStatus) GetApplicationVersion() string {
	if x != nil {
		return x.ApplicationVersion
	}
	return ""
}

func (x *InjectionStatus) GetInjectionUnixTimeMsecFrom1970() uint64 {
	if x != nil {
		return x.InjectionUnixTimeMsecFrom1970
	}
	return 0
}

func (x *InjectionStatus) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *InjectionStatus) GetErrorNum() uint32 {
	if x != nil {
		return x.ErrorNum
	}
	return 0
}

type InjectionStatusList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items              []*InjectionStatus `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	ProcessApplication ProcessType        `protobuf:"varint,2,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
}

func (x *InjectionStatusList) Reset() {
	*x = InjectionStatusList{}
	mi := &file_agent_module_status_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectionStatusList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectionStatusList) ProtoMessage() {}

func (x *InjectionStatusList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectionStatusList.ProtoReflect.Descriptor instead.
func (*InjectionStatusList) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{5}
}

func (x *InjectionStatusList) GetItems() []*InjectionStatus {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *InjectionStatusList) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

type InjectionStatusMsgBody struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*InjectionStatusList `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *InjectionStatusMsgBody) Reset() {
	*x = InjectionStatusMsgBody{}
	mi := &file_agent_module_status_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectionStatusMsgBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectionStatusMsgBody) ProtoMessage() {}

func (x *InjectionStatusMsgBody) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectionStatusMsgBody.ProtoReflect.Descriptor instead.
func (*InjectionStatusMsgBody) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{6}
}

func (x *InjectionStatusMsgBody) GetItems() []*InjectionStatusList {
	if x != nil {
		return x.Items
	}
	return nil
}

// server<->agent
type InjectionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName        string      `protobuf:"bytes,1,opt,name=processName,proto3" json:"processName,omitempty"`
	Pid                uint32      `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"` //当前进程的PID
	ProcessApplication ProcessType `protobuf:"varint,3,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
}

func (x *InjectionRequest) Reset() {
	*x = InjectionRequest{}
	mi := &file_agent_module_status_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectionRequest) ProtoMessage() {}

func (x *InjectionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectionRequest.ProtoReflect.Descriptor instead.
func (*InjectionRequest) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{7}
}

func (x *InjectionRequest) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *InjectionRequest) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *InjectionRequest) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

type InjectionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             uint32      `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	ProcessName        string      `protobuf:"bytes,2,opt,name=processName,proto3" json:"processName,omitempty"`
	Pid                uint32      `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty"` //当前进程的PID
	ProcessApplication ProcessType `protobuf:"varint,4,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
}

func (x *InjectionResponse) Reset() {
	*x = InjectionResponse{}
	mi := &file_agent_module_status_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectionResponse) ProtoMessage() {}

func (x *InjectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectionResponse.ProtoReflect.Descriptor instead.
func (*InjectionResponse) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{8}
}

func (x *InjectionResponse) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *InjectionResponse) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *InjectionResponse) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *InjectionResponse) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

// server<->agent
type UnInstallRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessName        string      `protobuf:"bytes,1,opt,name=processName,proto3" json:"processName,omitempty"`
	Pid                uint32      `protobuf:"varint,2,opt,name=pid,proto3" json:"pid,omitempty"` //当前进程的PID
	ProcessApplication ProcessType `protobuf:"varint,3,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
}

func (x *UnInstallRequest) Reset() {
	*x = UnInstallRequest{}
	mi := &file_agent_module_status_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnInstallRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnInstallRequest) ProtoMessage() {}

func (x *UnInstallRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnInstallRequest.ProtoReflect.Descriptor instead.
func (*UnInstallRequest) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{9}
}

func (x *UnInstallRequest) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *UnInstallRequest) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *UnInstallRequest) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

type UnInstallResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             uint32      `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	ProcessName        string      `protobuf:"bytes,2,opt,name=processName,proto3" json:"processName,omitempty"`
	Pid                uint32      `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty"` //当前进程的PID
	ProcessApplication ProcessType `protobuf:"varint,4,opt,name=processApplication,proto3,enum=agent.ProcessType" json:"processApplication,omitempty"`
}

func (x *UnInstallResponse) Reset() {
	*x = UnInstallResponse{}
	mi := &file_agent_module_status_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnInstallResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnInstallResponse) ProtoMessage() {}

func (x *UnInstallResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnInstallResponse.ProtoReflect.Descriptor instead.
func (*UnInstallResponse) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{10}
}

func (x *UnInstallResponse) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UnInstallResponse) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *UnInstallResponse) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *UnInstallResponse) GetProcessApplication() ProcessType {
	if x != nil {
		return x.ProcessApplication
	}
	return ProcessType_PROCESS_TYPE_UNKNOW
}

// NGAV采集器状态
type NgavColletorStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectorType NGAVCollectorType          `protobuf:"varint,1,opt,name=collector_type,json=collectorType,proto3,enum=agent.NGAVCollectorType" json:"collector_type,omitempty"`
	RunningStatus NgavCollectorRunningStatus `protobuf:"varint,2,opt,name=running_status,json=runningStatus,proto3,enum=agent.NgavCollectorRunningStatus" json:"running_status,omitempty"`
}

func (x *NgavColletorStatus) Reset() {
	*x = NgavColletorStatus{}
	mi := &file_agent_module_status_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NgavColletorStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NgavColletorStatus) ProtoMessage() {}

func (x *NgavColletorStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NgavColletorStatus.ProtoReflect.Descriptor instead.
func (*NgavColletorStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{11}
}

func (x *NgavColletorStatus) GetCollectorType() NGAVCollectorType {
	if x != nil {
		return x.CollectorType
	}
	return NGAVCollectorType_NCT_UNKNOWN
}

func (x *NgavColletorStatus) GetRunningStatus() NgavCollectorRunningStatus {
	if x != nil {
		return x.RunningStatus
	}
	return NgavCollectorRunningStatus_NCS_TYPE_UNKNOW
}

// NGAV采集器状态报告
type NgavColletorStatusReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Reports []*NgavColletorStatus `protobuf:"bytes,1,rep,name=reports,proto3" json:"reports,omitempty"`
}

func (x *NgavColletorStatusReport) Reset() {
	*x = NgavColletorStatusReport{}
	mi := &file_agent_module_status_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NgavColletorStatusReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NgavColletorStatusReport) ProtoMessage() {}

func (x *NgavColletorStatusReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NgavColletorStatusReport.ProtoReflect.Descriptor instead.
func (*NgavColletorStatusReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{12}
}

func (x *NgavColletorStatusReport) GetReports() []*NgavColletorStatus {
	if x != nil {
		return x.Reports
	}
	return nil
}

type DriverRunningStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleReportType ModuleReportType `protobuf:"varint,1,opt,name=moduleReportType,proto3,enum=agent.ModuleReportType" json:"moduleReportType,omitempty"` // 驱动种类定义
	Enable           int32            `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`                                                 // 驱动开关状态
}

func (x *DriverRunningStatus) Reset() {
	*x = DriverRunningStatus{}
	mi := &file_agent_module_status_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DriverRunningStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverRunningStatus) ProtoMessage() {}

func (x *DriverRunningStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverRunningStatus.ProtoReflect.Descriptor instead.
func (*DriverRunningStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{13}
}

func (x *DriverRunningStatus) GetModuleReportType() ModuleReportType {
	if x != nil {
		return x.ModuleReportType
	}
	return ModuleReportType_DEFENSE
}

func (x *DriverRunningStatus) GetEnable() int32 {
	if x != nil {
		return x.Enable
	}
	return 0
}

type SetDriverStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable       bool             `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	SwitchPlugin SwitchStatusEnum `protobuf:"varint,2,opt,name=switchPlugin,proto3,enum=agent.SwitchStatusEnum" json:"switchPlugin,omitempty"` // >=1404 插件开关控制
	SwitchDriver SwitchStatusEnum `protobuf:"varint,3,opt,name=switchDriver,proto3,enum=agent.SwitchStatusEnum" json:"switchDriver,omitempty"` // >=1404 驱动开关控制
	// v01增加管控各个驱动，服务端可以管控5个驱动运行状态
	DriverRunningStatus []*DriverRunningStatus `protobuf:"bytes,20,rep,name=driverRunningStatus,proto3" json:"driverRunningStatus,omitempty"`
}

func (x *SetDriverStatus) Reset() {
	*x = SetDriverStatus{}
	mi := &file_agent_module_status_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetDriverStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetDriverStatus) ProtoMessage() {}

func (x *SetDriverStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetDriverStatus.ProtoReflect.Descriptor instead.
func (*SetDriverStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{14}
}

func (x *SetDriverStatus) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *SetDriverStatus) GetSwitchPlugin() SwitchStatusEnum {
	if x != nil {
		return x.SwitchPlugin
	}
	return SwitchStatusEnum_NULL_STATUS
}

func (x *SetDriverStatus) GetSwitchDriver() SwitchStatusEnum {
	if x != nil {
		return x.SwitchDriver
	}
	return SwitchStatusEnum_NULL_STATUS
}

func (x *SetDriverStatus) GetDriverRunningStatus() []*DriverRunningStatus {
	if x != nil {
		return x.DriverRunningStatus
	}
	return nil
}

type ReportDriverStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// v01增加管控各个驱动，5个驱动上报各自的运行状态
	DriverRunningStatus []*DriverRunningStatus `protobuf:"bytes,20,rep,name=driverRunningStatus,proto3" json:"driverRunningStatus,omitempty"`
}

func (x *ReportDriverStatus) Reset() {
	*x = ReportDriverStatus{}
	mi := &file_agent_module_status_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportDriverStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportDriverStatus) ProtoMessage() {}

func (x *ReportDriverStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportDriverStatus.ProtoReflect.Descriptor instead.
func (*ReportDriverStatus) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{15}
}

func (x *ReportDriverStatus) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *ReportDriverStatus) GetDriverRunningStatus() []*DriverRunningStatus {
	if x != nil {
		return x.DriverRunningStatus
	}
	return nil
}

type MemProtectWarningReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WarningReport []*WarningReport `protobuf:"bytes,1,rep,name=warningReport,proto3" json:"warningReport,omitempty"`
}

func (x *MemProtectWarningReport) Reset() {
	*x = MemProtectWarningReport{}
	mi := &file_agent_module_status_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectWarningReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectWarningReport) ProtoMessage() {}

func (x *MemProtectWarningReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectWarningReport.ProtoReflect.Descriptor instead.
func (*MemProtectWarningReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{16}
}

func (x *MemProtectWarningReport) GetWarningReport() []*WarningReport {
	if x != nil {
		return x.WarningReport
	}
	return nil
}

type TimeOutCheckReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModuleName  string `protobuf:"bytes,1,opt,name=moduleName,proto3" json:"moduleName,omitempty"`    // 模块名称
	TimeOutTime int64  `protobuf:"varint,2,opt,name=timeOutTime,proto3" json:"timeOutTime,omitempty"` // 超时时间
}

func (x *TimeOutCheckReport) Reset() {
	*x = TimeOutCheckReport{}
	mi := &file_agent_module_status_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeOutCheckReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeOutCheckReport) ProtoMessage() {}

func (x *TimeOutCheckReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeOutCheckReport.ProtoReflect.Descriptor instead.
func (*TimeOutCheckReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{17}
}

func (x *TimeOutCheckReport) GetModuleName() string {
	if x != nil {
		return x.ModuleName
	}
	return ""
}

func (x *TimeOutCheckReport) GetTimeOutTime() int64 {
	if x != nil {
		return x.TimeOutTime
	}
	return 0
}

type ServiceInjectTimeOuthReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=serviceName,proto3" json:"serviceName,omitempty"` // 服务名称
}

func (x *ServiceInjectTimeOuthReport) Reset() {
	*x = ServiceInjectTimeOuthReport{}
	mi := &file_agent_module_status_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInjectTimeOuthReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInjectTimeOuthReport) ProtoMessage() {}

func (x *ServiceInjectTimeOuthReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInjectTimeOuthReport.ProtoReflect.Descriptor instead.
func (*ServiceInjectTimeOuthReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{18}
}

func (x *ServiceInjectTimeOuthReport) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type UnexpectShutdownReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstallAgentTime uint64 `protobuf:"varint,1,opt,name=installAgentTime,proto3" json:"installAgentTime,omitempty"` // 首次安装时间
	BeforeNum        uint64 `protobuf:"varint,2,opt,name=beforeNum,proto3" json:"beforeNum,omitempty"`               // 安装agent前意外关机次数
	RecentlyNum      uint64 `protobuf:"varint,3,opt,name=recentlyNum,proto3" json:"recentlyNum,omitempty"`           // 最近7天意外关机次数
}

func (x *UnexpectShutdownReport) Reset() {
	*x = UnexpectShutdownReport{}
	mi := &file_agent_module_status_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnexpectShutdownReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnexpectShutdownReport) ProtoMessage() {}

func (x *UnexpectShutdownReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnexpectShutdownReport.ProtoReflect.Descriptor instead.
func (*UnexpectShutdownReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{19}
}

func (x *UnexpectShutdownReport) GetInstallAgentTime() uint64 {
	if x != nil {
		return x.InstallAgentTime
	}
	return 0
}

func (x *UnexpectShutdownReport) GetBeforeNum() uint64 {
	if x != nil {
		return x.BeforeNum
	}
	return 0
}

func (x *UnexpectShutdownReport) GetRecentlyNum() uint64 {
	if x != nil {
		return x.RecentlyNum
	}
	return 0
}

type InjectCrashReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"` // 应用名称
}

func (x *InjectCrashReport) Reset() {
	*x = InjectCrashReport{}
	mi := &file_agent_module_status_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectCrashReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectCrashReport) ProtoMessage() {}

func (x *InjectCrashReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectCrashReport.ProtoReflect.Descriptor instead.
func (*InjectCrashReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{20}
}

func (x *InjectCrashReport) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type InjectDllCrashFoundReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,1,opt,name=fileName,proto3" json:"fileName,omitempty"` // 应用名称
}

func (x *InjectDllCrashFoundReport) Reset() {
	*x = InjectDllCrashFoundReport{}
	mi := &file_agent_module_status_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InjectDllCrashFoundReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InjectDllCrashFoundReport) ProtoMessage() {}

func (x *InjectDllCrashFoundReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InjectDllCrashFoundReport.ProtoReflect.Descriptor instead.
func (*InjectDllCrashFoundReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{21}
}

func (x *InjectDllCrashFoundReport) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type ReportSuppressionReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`    // 压制的检测项名称
	Count uint64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"` // 检测次数
}

func (x *ReportSuppressionReport) Reset() {
	*x = ReportSuppressionReport{}
	mi := &file_agent_module_status_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportSuppressionReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSuppressionReport) ProtoMessage() {}

func (x *ReportSuppressionReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSuppressionReport.ProtoReflect.Descriptor instead.
func (*ReportSuppressionReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{22}
}

func (x *ReportSuppressionReport) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ReportSuppressionReport) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ResourceOverLimitReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TriggerTime uint64 `protobuf:"varint,1,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"` // 资源超限时间（发生OOM的时间）
}

func (x *ResourceOverLimitReport) Reset() {
	*x = ResourceOverLimitReport{}
	mi := &file_agent_module_status_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceOverLimitReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceOverLimitReport) ProtoMessage() {}

func (x *ResourceOverLimitReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceOverLimitReport.ProtoReflect.Descriptor instead.
func (*ResourceOverLimitReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{23}
}

func (x *ResourceOverLimitReport) GetTriggerTime() uint64 {
	if x != nil {
		return x.TriggerTime
	}
	return 0
}

type DriverAbnormalReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type DriverAbnormalType `protobuf:"varint,1,opt,name=type,proto3,enum=agent.DriverAbnormalType" json:"type,omitempty"`
}

func (x *DriverAbnormalReport) Reset() {
	*x = DriverAbnormalReport{}
	mi := &file_agent_module_status_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DriverAbnormalReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverAbnormalReport) ProtoMessage() {}

func (x *DriverAbnormalReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverAbnormalReport.ProtoReflect.Descriptor instead.
func (*DriverAbnormalReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{24}
}

func (x *DriverAbnormalReport) GetType() DriverAbnormalType {
	if x != nil {
		return x.Type
	}
	return DriverAbnormalType_DAT_Unknow
}

type WarningReport struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportType WarningReportType `protobuf:"varint,1,opt,name=reportType,proto3,enum=agent.WarningReportType" json:"reportType,omitempty"`
	TimeStamp  uint64            `protobuf:"varint,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"` // 系统时间戳/文件时间戳/日志时间戳
	// Types that are assignable to ReportData:
	//
	//	*WarningReport_TimeOutCheckReport
	//	*WarningReport_ServiceInjectTimeOuthReport
	//	*WarningReport_UnexpectShutdownReport
	//	*WarningReport_InjectCrashReport
	//	*WarningReport_InjectDllCrashFoundReport
	//	*WarningReport_ReportSuppressionReport
	//	*WarningReport_ResOverLimitReport
	//	*WarningReport_DriverAbnormalReport
	ReportData isWarningReport_ReportData `protobuf_oneof:"reportData"`
}

func (x *WarningReport) Reset() {
	*x = WarningReport{}
	mi := &file_agent_module_status_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WarningReport) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WarningReport) ProtoMessage() {}

func (x *WarningReport) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WarningReport.ProtoReflect.Descriptor instead.
func (*WarningReport) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{25}
}

func (x *WarningReport) GetReportType() WarningReportType {
	if x != nil {
		return x.ReportType
	}
	return WarningReportType_Unknow
}

func (x *WarningReport) GetTimeStamp() uint64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (m *WarningReport) GetReportData() isWarningReport_ReportData {
	if m != nil {
		return m.ReportData
	}
	return nil
}

func (x *WarningReport) GetTimeOutCheckReport() *TimeOutCheckReport {
	if x, ok := x.GetReportData().(*WarningReport_TimeOutCheckReport); ok {
		return x.TimeOutCheckReport
	}
	return nil
}

func (x *WarningReport) GetServiceInjectTimeOuthReport() *ServiceInjectTimeOuthReport {
	if x, ok := x.GetReportData().(*WarningReport_ServiceInjectTimeOuthReport); ok {
		return x.ServiceInjectTimeOuthReport
	}
	return nil
}

func (x *WarningReport) GetUnexpectShutdownReport() *UnexpectShutdownReport {
	if x, ok := x.GetReportData().(*WarningReport_UnexpectShutdownReport); ok {
		return x.UnexpectShutdownReport
	}
	return nil
}

func (x *WarningReport) GetInjectCrashReport() *InjectCrashReport {
	if x, ok := x.GetReportData().(*WarningReport_InjectCrashReport); ok {
		return x.InjectCrashReport
	}
	return nil
}

func (x *WarningReport) GetInjectDllCrashFoundReport() *InjectDllCrashFoundReport {
	if x, ok := x.GetReportData().(*WarningReport_InjectDllCrashFoundReport); ok {
		return x.InjectDllCrashFoundReport
	}
	return nil
}

func (x *WarningReport) GetReportSuppressionReport() *ReportSuppressionReport {
	if x, ok := x.GetReportData().(*WarningReport_ReportSuppressionReport); ok {
		return x.ReportSuppressionReport
	}
	return nil
}

func (x *WarningReport) GetResOverLimitReport() *ResourceOverLimitReport {
	if x, ok := x.GetReportData().(*WarningReport_ResOverLimitReport); ok {
		return x.ResOverLimitReport
	}
	return nil
}

func (x *WarningReport) GetDriverAbnormalReport() *DriverAbnormalReport {
	if x, ok := x.GetReportData().(*WarningReport_DriverAbnormalReport); ok {
		return x.DriverAbnormalReport
	}
	return nil
}

type isWarningReport_ReportData interface {
	isWarningReport_ReportData()
}

type WarningReport_TimeOutCheckReport struct {
	TimeOutCheckReport *TimeOutCheckReport `protobuf:"bytes,3,opt,name=timeOutCheckReport,proto3,oneof"`
}

type WarningReport_ServiceInjectTimeOuthReport struct {
	ServiceInjectTimeOuthReport *ServiceInjectTimeOuthReport `protobuf:"bytes,4,opt,name=serviceInjectTimeOuthReport,proto3,oneof"`
}

type WarningReport_UnexpectShutdownReport struct {
	UnexpectShutdownReport *UnexpectShutdownReport `protobuf:"bytes,5,opt,name=UnexpectShutdownReport,proto3,oneof"`
}

type WarningReport_InjectCrashReport struct {
	InjectCrashReport *InjectCrashReport `protobuf:"bytes,6,opt,name=injectCrashReport,proto3,oneof"`
}

type WarningReport_InjectDllCrashFoundReport struct {
	InjectDllCrashFoundReport *InjectDllCrashFoundReport `protobuf:"bytes,7,opt,name=injectDllCrashFoundReport,proto3,oneof"`
}

type WarningReport_ReportSuppressionReport struct {
	ReportSuppressionReport *ReportSuppressionReport `protobuf:"bytes,8,opt,name=reportSuppressionReport,proto3,oneof"`
}

type WarningReport_ResOverLimitReport struct {
	ResOverLimitReport *ResourceOverLimitReport `protobuf:"bytes,9,opt,name=resOverLimitReport,proto3,oneof"` //（+ 神甲1700版本新增）
}

type WarningReport_DriverAbnormalReport struct {
	DriverAbnormalReport *DriverAbnormalReport `protobuf:"bytes,10,opt,name=driverAbnormalReport,proto3,oneof"`
}

func (*WarningReport_TimeOutCheckReport) isWarningReport_ReportData() {}

func (*WarningReport_ServiceInjectTimeOuthReport) isWarningReport_ReportData() {}

func (*WarningReport_UnexpectShutdownReport) isWarningReport_ReportData() {}

func (*WarningReport_InjectCrashReport) isWarningReport_ReportData() {}

func (*WarningReport_InjectDllCrashFoundReport) isWarningReport_ReportData() {}

func (*WarningReport_ReportSuppressionReport) isWarningReport_ReportData() {}

func (*WarningReport_ResOverLimitReport) isWarningReport_ReportData() {}

func (*WarningReport_DriverAbnormalReport) isWarningReport_ReportData() {}

type NoticeUploadFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname        string           `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"`
	IgnoreWarnings []*IgnoreWarning `protobuf:"bytes,2,rep,name=IgnoreWarnings,proto3" json:"IgnoreWarnings,omitempty"`
}

func (x *NoticeUploadFile) Reset() {
	*x = NoticeUploadFile{}
	mi := &file_agent_module_status_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeUploadFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeUploadFile) ProtoMessage() {}

func (x *NoticeUploadFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeUploadFile.ProtoReflect.Descriptor instead.
func (*NoticeUploadFile) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{26}
}

func (x *NoticeUploadFile) GetModname() string {
	if x != nil {
		return x.Modname
	}
	return ""
}

func (x *NoticeUploadFile) GetIgnoreWarnings() []*IgnoreWarning {
	if x != nil {
		return x.IgnoreWarnings
	}
	return nil
}

type IgnoreWarning struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WarningType WarningReportType `protobuf:"varint,1,opt,name=WarningType,proto3,enum=agent.WarningReportType" json:"WarningType,omitempty"` // 异常警告类型
	TimeStamp   uint64            `protobuf:"varint,2,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`                                  // 系统时间戳
}

func (x *IgnoreWarning) Reset() {
	*x = IgnoreWarning{}
	mi := &file_agent_module_status_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IgnoreWarning) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreWarning) ProtoMessage() {}

func (x *IgnoreWarning) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreWarning.ProtoReflect.Descriptor instead.
func (*IgnoreWarning) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{27}
}

func (x *IgnoreWarning) GetWarningType() WarningReportType {
	if x != nil {
		return x.WarningType
	}
	return WarningReportType_Unknow
}

func (x *IgnoreWarning) GetTimeStamp() uint64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

type CollectLogsType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CollectLogsType) Reset() {
	*x = CollectLogsType{}
	mi := &file_agent_module_status_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectLogsType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectLogsType) ProtoMessage() {}

func (x *CollectLogsType) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectLogsType.ProtoReflect.Descriptor instead.
func (*CollectLogsType) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{28}
}

// CollectAndUploadLogs 取代原 NoticeUploadFile
type CollectAndUploadLogs struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type           CollectLogsType_Enum `protobuf:"varint,1,opt,name=type,proto3,enum=agent.CollectLogsType_Enum" json:"type,omitempty"`
	IgnoreWarnings []*IgnoreWarning     `protobuf:"bytes,2,rep,name=ignore_warnings,json=ignoreWarnings,proto3" json:"ignore_warnings,omitempty"`
	UploadUri      string               `protobuf:"bytes,3,opt,name=upload_uri,json=uploadUri,proto3" json:"upload_uri,omitempty"` // 上传 uri, 形如 "https://ip:port/path?upload_key=xxx&task_id=xxx"
}

func (x *CollectAndUploadLogs) Reset() {
	*x = CollectAndUploadLogs{}
	mi := &file_agent_module_status_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CollectAndUploadLogs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectAndUploadLogs) ProtoMessage() {}

func (x *CollectAndUploadLogs) ProtoReflect() protoreflect.Message {
	mi := &file_agent_module_status_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectAndUploadLogs.ProtoReflect.Descriptor instead.
func (*CollectAndUploadLogs) Descriptor() ([]byte, []int) {
	return file_agent_module_status_proto_rawDescGZIP(), []int{29}
}

func (x *CollectAndUploadLogs) GetType() CollectLogsType_Enum {
	if x != nil {
		return x.Type
	}
	return CollectLogsType_Unknown
}

func (x *CollectAndUploadLogs) GetIgnoreWarnings() []*IgnoreWarning {
	if x != nil {
		return x.IgnoreWarnings
	}
	return nil
}

func (x *CollectAndUploadLogs) GetUploadUri() string {
	if x != nil {
		return x.UploadUri
	}
	return ""
}

var File_agent_module_status_proto protoreflect.FileDescriptor

var file_agent_module_status_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x03, 0x0a, 0x17, 0x4d, 0x65, 0x6d, 0x50, 0x72,
	0x6f, 0x74, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x32, 0x0a, 0x14, 0x49, 0x73, 0x42, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x49, 0x73, 0x42, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x76, 0x65, 0x72,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x43, 0x0a, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x72,
	0x61, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x61,
	0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x43,
	0x72, 0x61, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x0a, 0x08, 0x63, 0x6f,
	0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c,
	0x69, 0x63, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x66, 0x6c,
	0x69, 0x63, 0x74, 0x12, 0x3e, 0x0a, 0x1a, 0x49, 0x73, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x49, 0x73, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x52, 0x0d, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22,
	0x92, 0x02, 0x0a, 0x0c, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x37, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x73, 0x52,
	0x75, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x52, 0x75, 0x6e, 0x12,
	0x1a, 0x0a, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x73, 0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x13, 0x6d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4c, 0x6f, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x49, 0x0a, 0x12, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x12, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x41, 0x0a, 0x0b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x2e, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x72, 0x61, 0x73, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73,
	0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x74, 0x6f, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x98, 0x03, 0x0a, 0x0f, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x04, 0x70, 0x70, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x69, 0x64, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x69, 0x64, 0x73, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44,
	0x0a, 0x1d, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x78, 0x54,
	0x69, 0x6d, 0x65, 0x4d, 0x73, 0x65, 0x63, 0x46, 0x72, 0x6f, 0x6d, 0x31, 0x39, 0x37, 0x30, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x1d, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x65, 0x63, 0x46, 0x72, 0x6f, 0x6d,
	0x31, 0x39, 0x37, 0x30, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4e,
	0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4e,
	0x75, 0x6d, 0x22, 0x87, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x16,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d,
	0x73, 0x67, 0x42, 0x6f, 0x64, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x8a, 0x01, 0x0a, 0x10, 0x49, 0x6e, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x8a, 0x01, 0x0a, 0x10,
	0x55, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x55, 0x6e, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x42, 0x0a, 0x12, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x9f,
	0x01, 0x0a, 0x12, 0x4e, 0x67, 0x61, 0x76, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x74, 0x6f, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x47, 0x41, 0x56, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x67, 0x61, 0x76, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0d, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x4f, 0x0a, 0x18, 0x4e, 0x67, 0x61, 0x76, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x74, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x33, 0x0a, 0x07,
	0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x67, 0x61, 0x76, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x74,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x07, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x73, 0x22, 0x72, 0x0a, 0x13, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0xf1, 0x01, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x75, 0x67, 0x69,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x75, 0x6d,
	0x52, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x50, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x12, 0x3b,
	0x0a, 0x0c, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0c, 0x73,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x13, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x13, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e,
	0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x12, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4c, 0x0a, 0x13, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x14,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x13, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a, 0x17, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x0d, 0x77,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x56, 0x0a, 0x12,
	0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x3f, 0x0a, 0x1b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x68, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x84, 0x01, 0x0a, 0x16, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x53, 0x68, 0x75, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x2a, 0x0a, 0x10, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x10, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x62, 0x65, 0x66, 0x6f, 0x72, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65,
	0x63, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x6c, 0x79, 0x4e, 0x75, 0x6d, 0x22, 0x2f, 0x0a, 0x11,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x37, 0x0a,
	0x19, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6c, 0x6c, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46,
	0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x43, 0x0a, 0x17, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3c, 0x0a, 0x17, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x74, 0x72,
	0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x45, 0x0a, 0x14, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x2d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x62,
	0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0xb0, 0x06, 0x0a, 0x0d, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x38, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x4b, 0x0a, 0x12, 0x74, 0x69,
	0x6d, 0x65, 0x4f, 0x75, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x48, 0x00, 0x52, 0x12, 0x74, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x66, 0x0a, 0x1b, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x68,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x48, 0x00, 0x52, 0x1b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x57, 0x0a, 0x16, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x53, 0x68, 0x75, 0x74, 0x64,
	0x6f, 0x77, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x53, 0x68, 0x75, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00,
	0x52, 0x16, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x53, 0x68, 0x75, 0x74, 0x64, 0x6f,
	0x77, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x48, 0x0a, 0x11, 0x69, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52,
	0x11, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x43, 0x72, 0x61, 0x73, 0x68, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x60, 0x0a, 0x19, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6c, 0x6c, 0x43,
	0x72, 0x61, 0x73, 0x68, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e,
	0x6a, 0x65, 0x63, 0x74, 0x44, 0x6c, 0x6c, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x6f, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52, 0x19, 0x69, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x44, 0x6c, 0x6c, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x5a, 0x0a, 0x17, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52, 0x17, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x12, 0x50, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4f, 0x76, 0x65,
	0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52, 0x12,
	0x72, 0x65, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x51, 0x0a, 0x14, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x62, 0x6e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41,
	0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x48, 0x00, 0x52,
	0x14, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x52,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x6a, 0x0a, 0x10, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x57, 0x61, 0x72, 0x6e, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52,
	0x0e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x22,
	0x69, 0x0a, 0x0d, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x3a, 0x0a, 0x0b, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x61,
	0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x62, 0x0a, 0x0f, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x4f, 0x0a,
	0x04, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x79, 0x73, 0x44, 0x75, 0x6d, 0x70, 0x10, 0x01, 0x12,
	0x0d, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x75, 0x6d, 0x70, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x53, 0x79, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x10, 0x04, 0x22, 0xa5,
	0x01, 0x0a, 0x14, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x41, 0x6e, 0x64, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f,
	0x6c, 0x6c, 0x65, 0x63, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x6e,
	0x75, 0x6d, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0f, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x5f, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x57, 0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x0e, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x57,
	0x61, 0x72, 0x6e, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x69, 0x2a, 0x91, 0x01, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44,
	0x45, 0x46, 0x45, 0x4e, 0x53, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4d, 0x4f, 0x4e, 0x49,
	0x54, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x42, 0x45, 0x48, 0x41, 0x56, 0x49, 0x4f,
	0x52, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x58, 0x42, 0x49, 0x4e, 0x49, 0x4e, 0x4a, 0x10,
	0x03, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x58, 0x4b, 0x52, 0x4e, 0x4c, 0x44, 0x45, 0x43, 0x10, 0x04,
	0x12, 0x0f, 0x0a, 0x0b, 0x4d, 0x45, 0x4d, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10,
	0x64, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x45, 0x4d, 0x5f, 0x4e, 0x47, 0x41, 0x56, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x45, 0x4d, 0x5f, 0x4e,
	0x47, 0x41, 0x56, 0x5f, 0x48, 0x4f, 0x4f, 0x4b, 0x10, 0x66, 0x2a, 0x50, 0x0a, 0x13, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x4c, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x4c, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x4d, 0x4c, 0x53, 0x5f, 0x4c,
	0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x2a, 0x48, 0x0a, 0x14,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x43, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x43, 0x53, 0x5f, 0x43, 0x6f, 0x6e, 0x66,
	0x6c, 0x69, 0x63, 0x74, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x43, 0x53, 0x5f, 0x4e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x10, 0x02, 0x2a, 0x47, 0x0a, 0x12, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a,
	0x4d, 0x45, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b,
	0x4d, 0x45, 0x53, 0x5f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x01, 0x12, 0x10, 0x0a,
	0x0c, 0x4d, 0x45, 0x53, 0x5f, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x10, 0x02, 0x2a,
	0x74, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c,
	0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x12, 0x14,
	0x0a, 0x10, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c,
	0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0x39, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x10, 0x00, 0x12, 0x08, 0x0a,
	0x04, 0x4a, 0x41, 0x56, 0x41, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x48, 0x50, 0x10, 0x02,
	0x2a, 0x8c, 0x04, 0x0a, 0x0e, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x4e, 0x75, 0x6d, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x55, 0x4c, 0x4c, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x53, 0x45, 0x52, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4e, 0x4a, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x41, 0x4e, 0x44, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x4a, 0x41,
	0x56, 0x41, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x43, 0x48, 0x4d, 0x4f, 0x44, 0x5f, 0x4a,
	0x41, 0x56, 0x41, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x15, 0x0a,
	0x11, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x08, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x5f,
	0x52, 0x45, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x09, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x4f, 0x54, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x42, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x45, 0x58, 0x43, 0x45, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17,
	0x4e, 0x4f, 0x54, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x49, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x47, 0x4c, 0x49,
	0x42, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x0d, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x4c, 0x49, 0x42, 0x43, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x0e, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x0f, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x45, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10,
	0x10, 0x12, 0x1d, 0x0a, 0x19, 0x4c, 0x49, 0x42, 0x53, 0x54, 0x44, 0x43, 0x50, 0x4c, 0x55, 0x53,
	0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x11,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x4c, 0x49, 0x4e, 0x55, 0x58, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x12, 0x2a,
	0x65, 0x0a, 0x1a, 0x4e, 0x67, 0x61, 0x76, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x0a,
	0x0f, 0x4e, 0x43, 0x53, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x43, 0x53, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x43, 0x53, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x43, 0x53, 0x5f, 0x41, 0x42, 0x4e, 0x4f,
	0x52, 0x4d, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0xe4, 0x02, 0x0a, 0x11, 0x57, 0x61, 0x72, 0x6e, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x69, 0x6d, 0x65, 0x4f, 0x75, 0x74, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x72, 0x61,
	0x73, 0x68, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x53, 0x43, 0x72, 0x61, 0x73, 0x68, 0x10,
	0x04, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x6e, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x53, 0x68, 0x75,
	0x74, 0x64, 0x6f, 0x77, 0x6e, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x61, 0x6e, 0x79, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x61, 0x6e, 0x79, 0x4d,
	0x65, 0x6d, 0x6f, 0x72, 0x79, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x49, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x44, 0x6c, 0x6c, 0x43, 0x72, 0x61, 0x73, 0x68, 0x10, 0x08, 0x12, 0x17, 0x0a, 0x13, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x6c, 0x6c, 0x43, 0x72, 0x61, 0x73, 0x68, 0x46, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x10, 0x0a, 0x12, 0x18, 0x0a, 0x14, 0x54,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x44, 0x6f, 0x77, 0x6e, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x10, 0x0b, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4f, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x10, 0x0c, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x65, 0x6c, 0x66, 0x44, 0x6f, 0x77, 0x6e, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x0e, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x4e, 0x6f, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x0f, 0x12, 0x0e, 0x0a,
	0x0a, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x74, 0x4f, 0x4b, 0x10, 0x10, 0x2a, 0x40, 0x0a,
	0x12, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x41, 0x54, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x42, 0x65, 0x68, 0x61, 0x76, 0x6f, 0x72, 0x54, 0x72, 0x61, 0x63, 0x65, 0x10, 0x01, 0x42,
	0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_agent_module_status_proto_rawDescOnce sync.Once
	file_agent_module_status_proto_rawDescData = file_agent_module_status_proto_rawDesc
)

func file_agent_module_status_proto_rawDescGZIP() []byte {
	file_agent_module_status_proto_rawDescOnce.Do(func() {
		file_agent_module_status_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_module_status_proto_rawDescData)
	})
	return file_agent_module_status_proto_rawDescData
}

var file_agent_module_status_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_agent_module_status_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_agent_module_status_proto_goTypes = []any{
	(ModuleReportType)(0),               // 0: agent.ModuleReportType
	(ModuleLoadingStatus)(0),            // 1: agent.ModuleLoadingStatus
	(ModuleConflictStatus)(0),           // 2: agent.ModuleConflictStatus
	(ModuleEnableStatus)(0),             // 3: agent.ModuleEnableStatus
	(Status)(0),                         // 4: agent.Status
	(ProcessType)(0),                    // 5: agent.ProcessType
	(InjectErrorNum)(0),                 // 6: agent.InjectErrorNum
	(NgavCollectorRunningStatus)(0),     // 7: agent.NgavCollectorRunningStatus
	(WarningReportType)(0),              // 8: agent.WarningReportType
	(DriverAbnormalType)(0),             // 9: agent.DriverAbnormalType
	(CollectLogsType_Enum)(0),           // 10: agent.CollectLogsType.Enum
	(*MemProtectModulesStatus)(nil),     // 11: agent.MemProtectModulesStatus
	(*ModuleStatus)(nil),                // 12: agent.ModuleStatus
	(*AgentStatus)(nil),                 // 13: agent.AgentStatus
	(*AgentCrashStatus)(nil),            // 14: agent.AgentCrashStatus
	(*InjectionStatus)(nil),             // 15: agent.InjectionStatus
	(*InjectionStatusList)(nil),         // 16: agent.InjectionStatusList
	(*InjectionStatusMsgBody)(nil),      // 17: agent.InjectionStatusMsgBody
	(*InjectionRequest)(nil),            // 18: agent.InjectionRequest
	(*InjectionResponse)(nil),           // 19: agent.InjectionResponse
	(*UnInstallRequest)(nil),            // 20: agent.UnInstallRequest
	(*UnInstallResponse)(nil),           // 21: agent.UnInstallResponse
	(*NgavColletorStatus)(nil),          // 22: agent.NgavColletorStatus
	(*NgavColletorStatusReport)(nil),    // 23: agent.NgavColletorStatusReport
	(*DriverRunningStatus)(nil),         // 24: agent.DriverRunningStatus
	(*SetDriverStatus)(nil),             // 25: agent.SetDriverStatus
	(*ReportDriverStatus)(nil),          // 26: agent.ReportDriverStatus
	(*MemProtectWarningReport)(nil),     // 27: agent.MemProtectWarningReport
	(*TimeOutCheckReport)(nil),          // 28: agent.TimeOutCheckReport
	(*ServiceInjectTimeOuthReport)(nil), // 29: agent.ServiceInjectTimeOuthReport
	(*UnexpectShutdownReport)(nil),      // 30: agent.UnexpectShutdownReport
	(*InjectCrashReport)(nil),           // 31: agent.InjectCrashReport
	(*InjectDllCrashFoundReport)(nil),   // 32: agent.InjectDllCrashFoundReport
	(*ReportSuppressionReport)(nil),     // 33: agent.ReportSuppressionReport
	(*ResourceOverLimitReport)(nil),     // 34: agent.ResourceOverLimitReport
	(*DriverAbnormalReport)(nil),        // 35: agent.DriverAbnormalReport
	(*WarningReport)(nil),               // 36: agent.WarningReport
	(*NoticeUploadFile)(nil),            // 37: agent.NoticeUploadFile
	(*IgnoreWarning)(nil),               // 38: agent.IgnoreWarning
	(*CollectLogsType)(nil),             // 39: agent.CollectLogsType
	(*CollectAndUploadLogs)(nil),        // 40: agent.CollectAndUploadLogs
	(NGAVCollectorType)(0),              // 41: agent.NGAVCollectorType
	(SwitchStatusEnum)(0),               // 42: agent.SwitchStatusEnum
}
var file_agent_module_status_proto_depIdxs = []int32{
	12, // 0: agent.MemProtectModulesStatus.moduleStatus:type_name -> agent.ModuleStatus
	13, // 1: agent.MemProtectModulesStatus.agentStatus:type_name -> agent.AgentStatus
	14, // 2: agent.MemProtectModulesStatus.agentCrashStatus:type_name -> agent.AgentCrashStatus
	2,  // 3: agent.MemProtectModulesStatus.conflict:type_name -> agent.ModuleConflictStatus
	36, // 4: agent.MemProtectModulesStatus.warningReport:type_name -> agent.WarningReport
	0,  // 5: agent.ModuleStatus.moduleType:type_name -> agent.ModuleReportType
	1,  // 6: agent.ModuleStatus.moduleLoadingStatus:type_name -> agent.ModuleLoadingStatus
	3,  // 7: agent.ModuleStatus.moduleEnableStatus:type_name -> agent.ModuleEnableStatus
	4,  // 8: agent.InjectionStatus.status:type_name -> agent.Status
	5,  // 9: agent.InjectionStatus.processApplication:type_name -> agent.ProcessType
	15, // 10: agent.InjectionStatusList.items:type_name -> agent.InjectionStatus
	5,  // 11: agent.InjectionStatusList.processApplication:type_name -> agent.ProcessType
	16, // 12: agent.InjectionStatusMsgBody.items:type_name -> agent.InjectionStatusList
	5,  // 13: agent.InjectionRequest.processApplication:type_name -> agent.ProcessType
	5,  // 14: agent.InjectionResponse.processApplication:type_name -> agent.ProcessType
	5,  // 15: agent.UnInstallRequest.processApplication:type_name -> agent.ProcessType
	5,  // 16: agent.UnInstallResponse.processApplication:type_name -> agent.ProcessType
	41, // 17: agent.NgavColletorStatus.collector_type:type_name -> agent.NGAVCollectorType
	7,  // 18: agent.NgavColletorStatus.running_status:type_name -> agent.NgavCollectorRunningStatus
	22, // 19: agent.NgavColletorStatusReport.reports:type_name -> agent.NgavColletorStatus
	0,  // 20: agent.DriverRunningStatus.moduleReportType:type_name -> agent.ModuleReportType
	42, // 21: agent.SetDriverStatus.switchPlugin:type_name -> agent.SwitchStatusEnum
	42, // 22: agent.SetDriverStatus.switchDriver:type_name -> agent.SwitchStatusEnum
	24, // 23: agent.SetDriverStatus.driverRunningStatus:type_name -> agent.DriverRunningStatus
	24, // 24: agent.ReportDriverStatus.driverRunningStatus:type_name -> agent.DriverRunningStatus
	36, // 25: agent.MemProtectWarningReport.warningReport:type_name -> agent.WarningReport
	9,  // 26: agent.DriverAbnormalReport.type:type_name -> agent.DriverAbnormalType
	8,  // 27: agent.WarningReport.reportType:type_name -> agent.WarningReportType
	28, // 28: agent.WarningReport.timeOutCheckReport:type_name -> agent.TimeOutCheckReport
	29, // 29: agent.WarningReport.serviceInjectTimeOuthReport:type_name -> agent.ServiceInjectTimeOuthReport
	30, // 30: agent.WarningReport.UnexpectShutdownReport:type_name -> agent.UnexpectShutdownReport
	31, // 31: agent.WarningReport.injectCrashReport:type_name -> agent.InjectCrashReport
	32, // 32: agent.WarningReport.injectDllCrashFoundReport:type_name -> agent.InjectDllCrashFoundReport
	33, // 33: agent.WarningReport.reportSuppressionReport:type_name -> agent.ReportSuppressionReport
	34, // 34: agent.WarningReport.resOverLimitReport:type_name -> agent.ResourceOverLimitReport
	35, // 35: agent.WarningReport.driverAbnormalReport:type_name -> agent.DriverAbnormalReport
	38, // 36: agent.NoticeUploadFile.IgnoreWarnings:type_name -> agent.IgnoreWarning
	8,  // 37: agent.IgnoreWarning.WarningType:type_name -> agent.WarningReportType
	10, // 38: agent.CollectAndUploadLogs.type:type_name -> agent.CollectLogsType.Enum
	38, // 39: agent.CollectAndUploadLogs.ignore_warnings:type_name -> agent.IgnoreWarning
	40, // [40:40] is the sub-list for method output_type
	40, // [40:40] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_agent_module_status_proto_init() }
func file_agent_module_status_proto_init() {
	if File_agent_module_status_proto != nil {
		return
	}
	file_agent_policy_proto_init()
	file_agent_module_status_proto_msgTypes[25].OneofWrappers = []any{
		(*WarningReport_TimeOutCheckReport)(nil),
		(*WarningReport_ServiceInjectTimeOuthReport)(nil),
		(*WarningReport_UnexpectShutdownReport)(nil),
		(*WarningReport_InjectCrashReport)(nil),
		(*WarningReport_InjectDllCrashFoundReport)(nil),
		(*WarningReport_ReportSuppressionReport)(nil),
		(*WarningReport_ResOverLimitReport)(nil),
		(*WarningReport_DriverAbnormalReport)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_module_status_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_module_status_proto_goTypes,
		DependencyIndexes: file_agent_module_status_proto_depIdxs,
		EnumInfos:         file_agent_module_status_proto_enumTypes,
		MessageInfos:      file_agent_module_status_proto_msgTypes,
	}.Build()
	File_agent_module_status_proto = out.File
	file_agent_module_status_proto_rawDesc = nil
	file_agent_module_status_proto_goTypes = nil
	file_agent_module_status_proto_depIdxs = nil
}
