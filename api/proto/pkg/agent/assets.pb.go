//说明：
// 1、所有内存和文件大小单位都是字节
// 2、百分比单位都是float,如：36.37%,上传36.37

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/assets.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MemProtectAssetsInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo        *ClientID             `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	HostInfoList    []*HostInformation    `protobuf:"bytes,2,rep,name=hostInfoList,proto3" json:"hostInfoList,omitempty"`
	AccountInfoList []*AccountInformation `protobuf:"bytes,3,rep,name=accountInfoList,proto3" json:"accountInfoList,omitempty"`
	EnvInfoList     []*EnvInformation     `protobuf:"bytes,4,rep,name=envInfoList,proto3" json:"envInfoList,omitempty"`
	KernelInfoList  []*KernelInformation  `protobuf:"bytes,5,rep,name=kernelInfoList,proto3" json:"kernelInfoList,omitempty"`
	ServiceInfoList []*ServiceInformation `protobuf:"bytes,6,rep,name=serviceInfoList,proto3" json:"serviceInfoList,omitempty"`
	ProcInfoList    []*ProcInformation    `protobuf:"bytes,7,rep,name=procInfoList,proto3" json:"procInfoList,omitempty"`
	// 通过如下方式检测主机是否为域控服务器 ：setting.has_usualLogonThreshold_case() == MemProtect::MemProtectAssetsInfo::HAS_DOMAININFO_NOT_SET;
	//
	// Types that are assignable to HasDomainInfo:
	//
	//	*MemProtectAssetsInfo_DomainInfo
	HasDomainInfo isMemProtectAssetsInfo_HasDomainInfo `protobuf_oneof:"has_domainInfo"`
	// 后面的都放oneof里面
	//
	// Types that are assignable to HasWebMiddleware:
	//
	//	*MemProtectAssetsInfo_MiddlewareList
	HasWebMiddleware isMemProtectAssetsInfo_HasWebMiddleware `protobuf_oneof:"has_web_middleware"`
	// web站点信息
	//
	// Types that are assignable to HasWebSiteInfo:
	//
	//	*MemProtectAssetsInfo_WebSiteInfoList
	HasWebSiteInfo isMemProtectAssetsInfo_HasWebSiteInfo `protobuf_oneof:"has_web_site_info"`
	// Types that are assignable to HasWebFrame:
	//
	//	*MemProtectAssetsInfo_WebFrameList
	HasWebFrame isMemProtectAssetsInfo_HasWebFrame `protobuf_oneof:"has_web_frame"`
	// 软件应用信息
	//
	// Types that are assignable to HasHostApplication:
	//
	//	*MemProtectAssetsInfo_HostApplicationList
	HasHostApplication isMemProtectAssetsInfo_HasHostApplication `protobuf_oneof:"has_host_application"`
	// 安装包信息
	//
	// Types that are assignable to HasPackageInfo:
	//
	//	*MemProtectAssetsInfo_PackageInfoList
	HasPackageInfo isMemProtectAssetsInfo_HasPackageInfo `protobuf_oneof:"has_package_info"`
	// python包信息
	//
	// Types that are assignable to HasPythonPackageInfo:
	//
	//	*MemProtectAssetsInfo_PythonPackageInfoList
	HasPythonPackageInfo isMemProtectAssetsInfo_HasPythonPackageInfo `protobuf_oneof:"has_python_package_info"`
	// Web应用信息
	//
	// Types that are assignable to HasWebApp:
	//
	//	*MemProtectAssetsInfo_WebAppList
	HasWebApp isMemProtectAssetsInfo_HasWebApp `protobuf_oneof:"has_web_app"`
	// npm包信息
	//
	// Types that are assignable to HasNpmInfo:
	//
	//	*MemProtectAssetsInfo_NpmInfoList
	HasNpmInfo isMemProtectAssetsInfo_HasNpmInfo `protobuf_oneof:"has_npm_info"`
	// 网卡及流量信息
	//
	// Types that are assignable to HasEthInfo:
	//
	//	*MemProtectAssetsInfo_EthInfoList
	HasEthInfo isMemProtectAssetsInfo_HasEthInfo `protobuf_oneof:"has_eth_info"`
	// 资源占用情况
	//
	// Types that are assignable to HasResourceInfo:
	//
	//	*MemProtectAssetsInfo_ResourceUtilInfo
	HasResourceInfo isMemProtectAssetsInfo_HasResourceInfo `protobuf_oneof:"has_resource_info"`
	// 计划任务信息
	//
	// Types that are assignable to HasJobTaskInfo:
	//
	//	*MemProtectAssetsInfo_JobTaskInfoList
	HasJobTaskInfo isMemProtectAssetsInfo_HasJobTaskInfo `protobuf_oneof:"has_job_task_info"`
	// 应用弱口令
	//
	// Types that are assignable to HasAppWeakPwdInfo:
	//
	//	*MemProtectAssetsInfo_AppWeakPwdInfoList
	HasAppWeakPwdInfo isMemProtectAssetsInfo_HasAppWeakPwdInfo `protobuf_oneof:"has_app_weak_pwd_info"`
}

func (x *MemProtectAssetsInfo) Reset() {
	*x = MemProtectAssetsInfo{}
	mi := &file_agent_assets_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectAssetsInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectAssetsInfo) ProtoMessage() {}

func (x *MemProtectAssetsInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectAssetsInfo.ProtoReflect.Descriptor instead.
func (*MemProtectAssetsInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectAssetsInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetHostInfoList() []*HostInformation {
	if x != nil {
		return x.HostInfoList
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetAccountInfoList() []*AccountInformation {
	if x != nil {
		return x.AccountInfoList
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetEnvInfoList() []*EnvInformation {
	if x != nil {
		return x.EnvInfoList
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetKernelInfoList() []*KernelInformation {
	if x != nil {
		return x.KernelInfoList
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetServiceInfoList() []*ServiceInformation {
	if x != nil {
		return x.ServiceInfoList
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetProcInfoList() []*ProcInformation {
	if x != nil {
		return x.ProcInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasDomainInfo() isMemProtectAssetsInfo_HasDomainInfo {
	if m != nil {
		return m.HasDomainInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetDomainInfo() *DomainInfo {
	if x, ok := x.GetHasDomainInfo().(*MemProtectAssetsInfo_DomainInfo); ok {
		return x.DomainInfo
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasWebMiddleware() isMemProtectAssetsInfo_HasWebMiddleware {
	if m != nil {
		return m.HasWebMiddleware
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetMiddlewareList() *WebMiddlewareList {
	if x, ok := x.GetHasWebMiddleware().(*MemProtectAssetsInfo_MiddlewareList); ok {
		return x.MiddlewareList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasWebSiteInfo() isMemProtectAssetsInfo_HasWebSiteInfo {
	if m != nil {
		return m.HasWebSiteInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetWebSiteInfoList() *WebSiteInfoList {
	if x, ok := x.GetHasWebSiteInfo().(*MemProtectAssetsInfo_WebSiteInfoList); ok {
		return x.WebSiteInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasWebFrame() isMemProtectAssetsInfo_HasWebFrame {
	if m != nil {
		return m.HasWebFrame
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetWebFrameList() *WebFrameList {
	if x, ok := x.GetHasWebFrame().(*MemProtectAssetsInfo_WebFrameList); ok {
		return x.WebFrameList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasHostApplication() isMemProtectAssetsInfo_HasHostApplication {
	if m != nil {
		return m.HasHostApplication
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetHostApplicationList() *HostApplicationList {
	if x, ok := x.GetHasHostApplication().(*MemProtectAssetsInfo_HostApplicationList); ok {
		return x.HostApplicationList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasPackageInfo() isMemProtectAssetsInfo_HasPackageInfo {
	if m != nil {
		return m.HasPackageInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetPackageInfoList() *PackageInfoList {
	if x, ok := x.GetHasPackageInfo().(*MemProtectAssetsInfo_PackageInfoList); ok {
		return x.PackageInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasPythonPackageInfo() isMemProtectAssetsInfo_HasPythonPackageInfo {
	if m != nil {
		return m.HasPythonPackageInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetPythonPackageInfoList() *PythonPackageInfoList {
	if x, ok := x.GetHasPythonPackageInfo().(*MemProtectAssetsInfo_PythonPackageInfoList); ok {
		return x.PythonPackageInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasWebApp() isMemProtectAssetsInfo_HasWebApp {
	if m != nil {
		return m.HasWebApp
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetWebAppList() *WebAppList {
	if x, ok := x.GetHasWebApp().(*MemProtectAssetsInfo_WebAppList); ok {
		return x.WebAppList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasNpmInfo() isMemProtectAssetsInfo_HasNpmInfo {
	if m != nil {
		return m.HasNpmInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetNpmInfoList() *NpmPackageInfoList {
	if x, ok := x.GetHasNpmInfo().(*MemProtectAssetsInfo_NpmInfoList); ok {
		return x.NpmInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasEthInfo() isMemProtectAssetsInfo_HasEthInfo {
	if m != nil {
		return m.HasEthInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetEthInfoList() *EtherNetList {
	if x, ok := x.GetHasEthInfo().(*MemProtectAssetsInfo_EthInfoList); ok {
		return x.EthInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasResourceInfo() isMemProtectAssetsInfo_HasResourceInfo {
	if m != nil {
		return m.HasResourceInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetResourceUtilInfo() *ResourceUtilization {
	if x, ok := x.GetHasResourceInfo().(*MemProtectAssetsInfo_ResourceUtilInfo); ok {
		return x.ResourceUtilInfo
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasJobTaskInfo() isMemProtectAssetsInfo_HasJobTaskInfo {
	if m != nil {
		return m.HasJobTaskInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetJobTaskInfoList() *JobTaskInfoList {
	if x, ok := x.GetHasJobTaskInfo().(*MemProtectAssetsInfo_JobTaskInfoList); ok {
		return x.JobTaskInfoList
	}
	return nil
}

func (m *MemProtectAssetsInfo) GetHasAppWeakPwdInfo() isMemProtectAssetsInfo_HasAppWeakPwdInfo {
	if m != nil {
		return m.HasAppWeakPwdInfo
	}
	return nil
}

func (x *MemProtectAssetsInfo) GetAppWeakPwdInfoList() *AppWeakPwdInfoList {
	if x, ok := x.GetHasAppWeakPwdInfo().(*MemProtectAssetsInfo_AppWeakPwdInfoList); ok {
		return x.AppWeakPwdInfoList
	}
	return nil
}

type isMemProtectAssetsInfo_HasDomainInfo interface {
	isMemProtectAssetsInfo_HasDomainInfo()
}

type MemProtectAssetsInfo_DomainInfo struct {
	DomainInfo *DomainInfo `protobuf:"bytes,8,opt,name=domainInfo,proto3,oneof"`
}

func (*MemProtectAssetsInfo_DomainInfo) isMemProtectAssetsInfo_HasDomainInfo() {}

type isMemProtectAssetsInfo_HasWebMiddleware interface {
	isMemProtectAssetsInfo_HasWebMiddleware()
}

type MemProtectAssetsInfo_MiddlewareList struct {
	MiddlewareList *WebMiddlewareList `protobuf:"bytes,9,opt,name=middleware_list,json=middlewareList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_MiddlewareList) isMemProtectAssetsInfo_HasWebMiddleware() {}

type isMemProtectAssetsInfo_HasWebSiteInfo interface {
	isMemProtectAssetsInfo_HasWebSiteInfo()
}

type MemProtectAssetsInfo_WebSiteInfoList struct {
	WebSiteInfoList *WebSiteInfoList `protobuf:"bytes,10,opt,name=web_site_info_list,json=webSiteInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_WebSiteInfoList) isMemProtectAssetsInfo_HasWebSiteInfo() {}

type isMemProtectAssetsInfo_HasWebFrame interface {
	isMemProtectAssetsInfo_HasWebFrame()
}

type MemProtectAssetsInfo_WebFrameList struct {
	WebFrameList *WebFrameList `protobuf:"bytes,11,opt,name=web_frame_list,json=webFrameList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_WebFrameList) isMemProtectAssetsInfo_HasWebFrame() {}

type isMemProtectAssetsInfo_HasHostApplication interface {
	isMemProtectAssetsInfo_HasHostApplication()
}

type MemProtectAssetsInfo_HostApplicationList struct {
	HostApplicationList *HostApplicationList `protobuf:"bytes,12,opt,name=host_application_list,json=hostApplicationList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_HostApplicationList) isMemProtectAssetsInfo_HasHostApplication() {}

type isMemProtectAssetsInfo_HasPackageInfo interface {
	isMemProtectAssetsInfo_HasPackageInfo()
}

type MemProtectAssetsInfo_PackageInfoList struct {
	PackageInfoList *PackageInfoList `protobuf:"bytes,13,opt,name=package_info_list,json=packageInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_PackageInfoList) isMemProtectAssetsInfo_HasPackageInfo() {}

type isMemProtectAssetsInfo_HasPythonPackageInfo interface {
	isMemProtectAssetsInfo_HasPythonPackageInfo()
}

type MemProtectAssetsInfo_PythonPackageInfoList struct {
	PythonPackageInfoList *PythonPackageInfoList `protobuf:"bytes,14,opt,name=python_package_info_list,json=pythonPackageInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_PythonPackageInfoList) isMemProtectAssetsInfo_HasPythonPackageInfo() {}

type isMemProtectAssetsInfo_HasWebApp interface {
	isMemProtectAssetsInfo_HasWebApp()
}

type MemProtectAssetsInfo_WebAppList struct {
	WebAppList *WebAppList `protobuf:"bytes,15,opt,name=web_app_list,json=webAppList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_WebAppList) isMemProtectAssetsInfo_HasWebApp() {}

type isMemProtectAssetsInfo_HasNpmInfo interface {
	isMemProtectAssetsInfo_HasNpmInfo()
}

type MemProtectAssetsInfo_NpmInfoList struct {
	NpmInfoList *NpmPackageInfoList `protobuf:"bytes,16,opt,name=npm_info_list,json=npmInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_NpmInfoList) isMemProtectAssetsInfo_HasNpmInfo() {}

type isMemProtectAssetsInfo_HasEthInfo interface {
	isMemProtectAssetsInfo_HasEthInfo()
}

type MemProtectAssetsInfo_EthInfoList struct {
	EthInfoList *EtherNetList `protobuf:"bytes,17,opt,name=eth_info_list,json=ethInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_EthInfoList) isMemProtectAssetsInfo_HasEthInfo() {}

type isMemProtectAssetsInfo_HasResourceInfo interface {
	isMemProtectAssetsInfo_HasResourceInfo()
}

type MemProtectAssetsInfo_ResourceUtilInfo struct {
	ResourceUtilInfo *ResourceUtilization `protobuf:"bytes,18,opt,name=resource_util_info,json=resourceUtilInfo,proto3,oneof"`
}

func (*MemProtectAssetsInfo_ResourceUtilInfo) isMemProtectAssetsInfo_HasResourceInfo() {}

type isMemProtectAssetsInfo_HasJobTaskInfo interface {
	isMemProtectAssetsInfo_HasJobTaskInfo()
}

type MemProtectAssetsInfo_JobTaskInfoList struct {
	JobTaskInfoList *JobTaskInfoList `protobuf:"bytes,19,opt,name=job_task_info_list,json=jobTaskInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_JobTaskInfoList) isMemProtectAssetsInfo_HasJobTaskInfo() {}

type isMemProtectAssetsInfo_HasAppWeakPwdInfo interface {
	isMemProtectAssetsInfo_HasAppWeakPwdInfo()
}

type MemProtectAssetsInfo_AppWeakPwdInfoList struct {
	AppWeakPwdInfoList *AppWeakPwdInfoList `protobuf:"bytes,20,opt,name=app_weak_pwd_info_list,json=appWeakPwdInfoList,proto3,oneof"`
}

func (*MemProtectAssetsInfo_AppWeakPwdInfoList) isMemProtectAssetsInfo_HasAppWeakPwdInfo() {}

// 资产管理：主机信息
type HostInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OsInfo          []byte  `protobuf:"bytes,1,opt,name=osInfo,proto3" json:"osInfo,omitempty"`                      //操作系统名称
	ComputerName    []byte  `protobuf:"bytes,2,opt,name=computerName,proto3" json:"computerName,omitempty"`          //计算机名称
	UserName        []byte  `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName,omitempty"`                  //所有用户名,如: root,test1,test2
	IEVersion       string  `protobuf:"bytes,4,opt,name=IEVersion,proto3" json:"IEVersion,omitempty"`                //IE版本
	IP              string  `protobuf:"bytes,5,opt,name=IP,proto3" json:"IP,omitempty"`                              //ip地址
	MAC             string  `protobuf:"bytes,6,opt,name=MAC,proto3" json:"MAC,omitempty"`                            //mac地址
	IPMASK          string  `protobuf:"bytes,7,opt,name=IPMASK,proto3" json:"IPMASK,omitempty"`                      //子网掩码
	Gateway         []byte  `protobuf:"bytes,8,opt,name=gateway,proto3" json:"gateway,omitempty"`                    //网关地址
	MemSize         uint64  `protobuf:"varint,9,opt,name=memSize,proto3" json:"memSize,omitempty"`                   //内存大小 字节
	TCPPorts        []byte  `protobuf:"bytes,10,opt,name=TCPPorts,proto3" json:"TCPPorts,omitempty"`                 //主机处于监听状态的TCP端口,如：23,80,443
	UDPPorts        []byte  `protobuf:"bytes,11,opt,name=UDPPorts,proto3" json:"UDPPorts,omitempty"`                 //主机处于监听状态的UDP端口,如：23,80,443
	CpuUtilization  float32 `protobuf:"fixed32,12,opt,name=cpuUtilization,proto3" json:"cpuUtilization,omitempty"`   //CPU使用率
	MemUtilization  float32 `protobuf:"fixed32,13,opt,name=memUtilization,proto3" json:"memUtilization,omitempty"`   //内存使用率
	DiskUtilization float32 `protobuf:"fixed32,14,opt,name=diskUtilization,proto3" json:"diskUtilization,omitempty"` //磁盘使用率
	AgentVer        []byte  `protobuf:"bytes,15,opt,name=agentVer,proto3" json:"agentVer,omitempty"`                 //agent版本名
	VersionCode     []byte  `protobuf:"bytes,16,opt,name=versionCode,proto3" json:"versionCode,omitempty"`           //agent版本号(1209未用)
	AgentKernelVer  []byte  `protobuf:"bytes,17,opt,name=agentKernelVer,proto3" json:"agentKernelVer,omitempty"`     //agent安装的驱动信息，仅linux
	LogonUserName   []byte  `protobuf:"bytes,18,opt,name=logonUserName,proto3" json:"logonUserName,omitempty"`       //当前登录的账户，如admin,testadmin
	// 1209 新增
	Arch                       string          `protobuf:"bytes,19,opt,name=arch,proto3" json:"arch,omitempty"`                                   // 架构
	CpuCores                   uint32          `protobuf:"varint,20,opt,name=cpuCores,proto3" json:"cpuCores,omitempty"`                          // CPU核数
	DiskSize                   uint64          `protobuf:"varint,21,opt,name=diskSize,proto3" json:"diskSize,omitempty"`                          // 磁盘大小
	JdkVersion                 string          `protobuf:"bytes,22,opt,name=jdkVersion,proto3" json:"jdkVersion,omitempty"`                       // JDK 版本
	WebMiddlewareVersion       string          `protobuf:"bytes,23,opt,name=webMiddlewareVersion,proto3" json:"webMiddlewareVersion,omitempty"`   // web中间件版本,|分隔 eg: tomcat-5.0.1|weblogic-1.0.1
	DriverLibVersion           string          `protobuf:"bytes,24,opt,name=driverLibVersion,proto3" json:"driverLibVersion,omitempty"`           // 驱动版本号
	BehaviorLibVersion         string          `protobuf:"bytes,25,opt,name=behaviorLibVersion,proto3" json:"behaviorLibVersion,omitempty"`       // 行为库版本
	EnvLibVersion              string          `protobuf:"bytes,26,opt,name=envLibVersion,proto3" json:"envLibVersion,omitempty"`                 // 环境库版本
	AgentJdkVersion            string          `protobuf:"bytes,27,opt,name=agentJdkVersion,proto3" json:"agentJdkVersion,omitempty"`             // JDK版本（Agent环境）
	LinuxOSInfoLong            string          `protobuf:"bytes,28,opt,name=linuxOSInfoLong,proto3" json:"linuxOSInfoLong,omitempty"`             // linux 系统信息长串，仅linux
	LinuxOSInfoShort           string          `protobuf:"bytes,29,opt,name=linuxOSInfoShort,proto3" json:"linuxOSInfoShort,omitempty"`           // linux 系统信息短串，仅linux
	RecentlyLogonUserName      []byte          `protobuf:"bytes,30,opt,name=recentlyLogonUserName,proto3" json:"recentlyLogonUserName,omitempty"` // 最近登录的账号，如admin,testadmin
	HashEngineVersion          string          `protobuf:"bytes,31,opt,name=hashEngineVersion,proto3" json:"hashEngineVersion,omitempty"`
	Sha256EngineVersion        string          `protobuf:"bytes,32,opt,name=sha256EngineVersion,proto3" json:"sha256EngineVersion,omitempty"`
	NgavLibVersion             string          `protobuf:"bytes,33,opt,name=ngavLibVersion,proto3" json:"ngavLibVersion,omitempty"`                         // ngav 行为规则的版本号
	HostIpPolicyType           int32           `protobuf:"varint,34,opt,name=hostIpPolicyType,proto3" json:"hostIpPolicyType,omitempty"`                    // 上报主机ip的策略类型， 1 默认， 2 自定义优先 ， 3 网卡优先
	AgentInstallTime           int64           `protobuf:"varint,35,opt,name=agentInstallTime,proto3" json:"agentInstallTime,omitempty"`                    // agent 首次安装时间
	DriverInstallTime          int64           `protobuf:"varint,36,opt,name=driverInstallTime,proto3" json:"driverInstallTime,omitempty"`                  // 驱动 首次安装时间
	EthInfoList                []*EtherNetInfo `protobuf:"bytes,37,rep,name=ethInfoList,proto3" json:"ethInfoList,omitempty"`                               // 所有网卡信息
	IsResourceUpdate           bool            `protobuf:"varint,38,opt,name=isResourceUpdate,proto3" json:"isResourceUpdate,omitempty"`                    // 主机资源信息是否需要更新
	RaspRuleVersion            string          `protobuf:"bytes,39,opt,name=raspRuleVersion,proto3" json:"raspRuleVersion,omitempty"`                       // RASP规则库的版本号
	BaselineLibVersion         string          `protobuf:"bytes,40,opt,name=baselineLibVersion,proto3" json:"baselineLibVersion,omitempty"`                 // 基线规则规则的版本号
	BaselineOS                 string          `protobuf:"bytes,41,opt,name=baselineOS,proto3" json:"baselineOS,omitempty"`                                 // 基线检查兼容平台
	DomainWhiteLibVersion      string          `protobuf:"bytes,42,opt,name=domainWhiteLibVersion,proto3" json:"domainWhiteLibVersion,omitempty"`           // 域名白名单规则的版本号
	FileSignComWhiteLibVersion string          `protobuf:"bytes,43,opt,name=fileSignComWhiteLibVersion,proto3" json:"fileSignComWhiteLibVersion,omitempty"` // 文件签名公司白名单规则的版本号
	OsInfoDisplay              []byte          `protobuf:"bytes,44,opt,name=osInfoDisplay,proto3" json:"osInfoDisplay,omitempty"`                           //操作系统名称，展示名称，仅windows
	DomainUserName             []byte          `protobuf:"bytes,45,opt,name=domainUserName,proto3" json:"domainUserName,omitempty"`                         //域的账户
	FileDriverBlockLibVersion  string          `protobuf:"bytes,46,opt,name=fileDriverBlockLibVersion,proto3" json:"fileDriverBlockLibVersion,omitempty"`   // 驱动黑库版本号
	LogonUserFlag              []bool          `protobuf:"varint,47,rep,packed,name=logonUserFlag,proto3" json:"logonUserFlag,omitempty"`                   // 当前登陆用户是否为域用户列表,true 是，false 不是
	WinOSInfoShort             string          `protobuf:"bytes,48,opt,name=winOSInfoShort,proto3" json:"winOSInfoShort,omitempty"`                         // windows 系统信息短串，仅windows
	ProxyIpPort                string          `protobuf:"bytes,49,opt,name=proxyIpPort,proto3" json:"proxyIpPort,omitempty"`                               // 代理信息 ip:port
}

func (x *HostInformation) Reset() {
	*x = HostInformation{}
	mi := &file_agent_assets_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostInformation) ProtoMessage() {}

func (x *HostInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostInformation.ProtoReflect.Descriptor instead.
func (*HostInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{1}
}

func (x *HostInformation) GetOsInfo() []byte {
	if x != nil {
		return x.OsInfo
	}
	return nil
}

func (x *HostInformation) GetComputerName() []byte {
	if x != nil {
		return x.ComputerName
	}
	return nil
}

func (x *HostInformation) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *HostInformation) GetIEVersion() string {
	if x != nil {
		return x.IEVersion
	}
	return ""
}

func (x *HostInformation) GetIP() string {
	if x != nil {
		return x.IP
	}
	return ""
}

func (x *HostInformation) GetMAC() string {
	if x != nil {
		return x.MAC
	}
	return ""
}

func (x *HostInformation) GetIPMASK() string {
	if x != nil {
		return x.IPMASK
	}
	return ""
}

func (x *HostInformation) GetGateway() []byte {
	if x != nil {
		return x.Gateway
	}
	return nil
}

func (x *HostInformation) GetMemSize() uint64 {
	if x != nil {
		return x.MemSize
	}
	return 0
}

func (x *HostInformation) GetTCPPorts() []byte {
	if x != nil {
		return x.TCPPorts
	}
	return nil
}

func (x *HostInformation) GetUDPPorts() []byte {
	if x != nil {
		return x.UDPPorts
	}
	return nil
}

func (x *HostInformation) GetCpuUtilization() float32 {
	if x != nil {
		return x.CpuUtilization
	}
	return 0
}

func (x *HostInformation) GetMemUtilization() float32 {
	if x != nil {
		return x.MemUtilization
	}
	return 0
}

func (x *HostInformation) GetDiskUtilization() float32 {
	if x != nil {
		return x.DiskUtilization
	}
	return 0
}

func (x *HostInformation) GetAgentVer() []byte {
	if x != nil {
		return x.AgentVer
	}
	return nil
}

func (x *HostInformation) GetVersionCode() []byte {
	if x != nil {
		return x.VersionCode
	}
	return nil
}

func (x *HostInformation) GetAgentKernelVer() []byte {
	if x != nil {
		return x.AgentKernelVer
	}
	return nil
}

func (x *HostInformation) GetLogonUserName() []byte {
	if x != nil {
		return x.LogonUserName
	}
	return nil
}

func (x *HostInformation) GetArch() string {
	if x != nil {
		return x.Arch
	}
	return ""
}

func (x *HostInformation) GetCpuCores() uint32 {
	if x != nil {
		return x.CpuCores
	}
	return 0
}

func (x *HostInformation) GetDiskSize() uint64 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *HostInformation) GetJdkVersion() string {
	if x != nil {
		return x.JdkVersion
	}
	return ""
}

func (x *HostInformation) GetWebMiddlewareVersion() string {
	if x != nil {
		return x.WebMiddlewareVersion
	}
	return ""
}

func (x *HostInformation) GetDriverLibVersion() string {
	if x != nil {
		return x.DriverLibVersion
	}
	return ""
}

func (x *HostInformation) GetBehaviorLibVersion() string {
	if x != nil {
		return x.BehaviorLibVersion
	}
	return ""
}

func (x *HostInformation) GetEnvLibVersion() string {
	if x != nil {
		return x.EnvLibVersion
	}
	return ""
}

func (x *HostInformation) GetAgentJdkVersion() string {
	if x != nil {
		return x.AgentJdkVersion
	}
	return ""
}

func (x *HostInformation) GetLinuxOSInfoLong() string {
	if x != nil {
		return x.LinuxOSInfoLong
	}
	return ""
}

func (x *HostInformation) GetLinuxOSInfoShort() string {
	if x != nil {
		return x.LinuxOSInfoShort
	}
	return ""
}

func (x *HostInformation) GetRecentlyLogonUserName() []byte {
	if x != nil {
		return x.RecentlyLogonUserName
	}
	return nil
}

func (x *HostInformation) GetHashEngineVersion() string {
	if x != nil {
		return x.HashEngineVersion
	}
	return ""
}

func (x *HostInformation) GetSha256EngineVersion() string {
	if x != nil {
		return x.Sha256EngineVersion
	}
	return ""
}

func (x *HostInformation) GetNgavLibVersion() string {
	if x != nil {
		return x.NgavLibVersion
	}
	return ""
}

func (x *HostInformation) GetHostIpPolicyType() int32 {
	if x != nil {
		return x.HostIpPolicyType
	}
	return 0
}

func (x *HostInformation) GetAgentInstallTime() int64 {
	if x != nil {
		return x.AgentInstallTime
	}
	return 0
}

func (x *HostInformation) GetDriverInstallTime() int64 {
	if x != nil {
		return x.DriverInstallTime
	}
	return 0
}

func (x *HostInformation) GetEthInfoList() []*EtherNetInfo {
	if x != nil {
		return x.EthInfoList
	}
	return nil
}

func (x *HostInformation) GetIsResourceUpdate() bool {
	if x != nil {
		return x.IsResourceUpdate
	}
	return false
}

func (x *HostInformation) GetRaspRuleVersion() string {
	if x != nil {
		return x.RaspRuleVersion
	}
	return ""
}

func (x *HostInformation) GetBaselineLibVersion() string {
	if x != nil {
		return x.BaselineLibVersion
	}
	return ""
}

func (x *HostInformation) GetBaselineOS() string {
	if x != nil {
		return x.BaselineOS
	}
	return ""
}

func (x *HostInformation) GetDomainWhiteLibVersion() string {
	if x != nil {
		return x.DomainWhiteLibVersion
	}
	return ""
}

func (x *HostInformation) GetFileSignComWhiteLibVersion() string {
	if x != nil {
		return x.FileSignComWhiteLibVersion
	}
	return ""
}

func (x *HostInformation) GetOsInfoDisplay() []byte {
	if x != nil {
		return x.OsInfoDisplay
	}
	return nil
}

func (x *HostInformation) GetDomainUserName() []byte {
	if x != nil {
		return x.DomainUserName
	}
	return nil
}

func (x *HostInformation) GetFileDriverBlockLibVersion() string {
	if x != nil {
		return x.FileDriverBlockLibVersion
	}
	return ""
}

func (x *HostInformation) GetLogonUserFlag() []bool {
	if x != nil {
		return x.LogonUserFlag
	}
	return nil
}

func (x *HostInformation) GetWinOSInfoShort() string {
	if x != nil {
		return x.WinOSInfoShort
	}
	return ""
}

func (x *HostInformation) GetProxyIpPort() string {
	if x != nil {
		return x.ProxyIpPort
	}
	return ""
}

// 网卡及流量信息
type EtherNetList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostIpPolicyType int32           `protobuf:"varint,1,opt,name=hostIpPolicyType,proto3" json:"hostIpPolicyType,omitempty"` // 上报主机ip的策略类型, 1 默认 2 自定义优先 3 网卡优先
	HostIp           string          `protobuf:"bytes,2,opt,name=host_ip,json=hostIp,proto3" json:"host_ip,omitempty"`        // 通过策略指定显示的主机ip
	EthInfoList      []*EtherNetInfo `protobuf:"bytes,3,rep,name=ethInfoList,proto3" json:"ethInfoList,omitempty"`            // 所有网卡信息
}

func (x *EtherNetList) Reset() {
	*x = EtherNetList{}
	mi := &file_agent_assets_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EtherNetList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EtherNetList) ProtoMessage() {}

func (x *EtherNetList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EtherNetList.ProtoReflect.Descriptor instead.
func (*EtherNetList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{2}
}

func (x *EtherNetList) GetHostIpPolicyType() int32 {
	if x != nil {
		return x.HostIpPolicyType
	}
	return 0
}

func (x *EtherNetList) GetHostIp() string {
	if x != nil {
		return x.HostIp
	}
	return ""
}

func (x *EtherNetList) GetEthInfoList() []*EtherNetInfo {
	if x != nil {
		return x.EthInfoList
	}
	return nil
}

// 主机资源占用率
type ResourceUtilization struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CollectionTime  int64   `protobuf:"varint,1,opt,name=collection_time,json=collectionTime,proto3" json:"collection_time,omitempty"`     //采集时间
	CpuUtilization  float32 `protobuf:"fixed32,2,opt,name=cpu_utilization,json=cpuUtilization,proto3" json:"cpu_utilization,omitempty"`    //CPU使用率
	MemUtilization  float32 `protobuf:"fixed32,3,opt,name=mem_utilization,json=memUtilization,proto3" json:"mem_utilization,omitempty"`    //内存使用率
	DiskUtilization float32 `protobuf:"fixed32,4,opt,name=disk_utilization,json=diskUtilization,proto3" json:"disk_utilization,omitempty"` //磁盘使用率
	// 资源占用告警策略
	CpuAlarmEnable  bool    `protobuf:"varint,5,opt,name=cpu_alarm_enable,json=cpuAlarmEnable,proto3" json:"cpu_alarm_enable,omitempty"`    //cpu告警开关
	CpuThreshold    float32 `protobuf:"fixed32,6,opt,name=cpu_threshold,json=cpuThreshold,proto3" json:"cpu_threshold,omitempty"`           //cpu告警阈值
	MemAlarmEnable  bool    `protobuf:"varint,7,opt,name=mem_alarm_enable,json=memAlarmEnable,proto3" json:"mem_alarm_enable,omitempty"`    //内存告警开关
	MemThreshold    float32 `protobuf:"fixed32,8,opt,name=mem_threshold,json=memThreshold,proto3" json:"mem_threshold,omitempty"`           //内存告警阈值
	DiskAlarmEnable bool    `protobuf:"varint,9,opt,name=disk_alarm_enable,json=diskAlarmEnable,proto3" json:"disk_alarm_enable,omitempty"` //磁盘告警开关
	DiskThreshold   float32 `protobuf:"fixed32,10,opt,name=disk_threshold,json=diskThreshold,proto3" json:"disk_threshold,omitempty"`       //磁盘告警阈值
}

func (x *ResourceUtilization) Reset() {
	*x = ResourceUtilization{}
	mi := &file_agent_assets_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceUtilization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceUtilization) ProtoMessage() {}

func (x *ResourceUtilization) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceUtilization.ProtoReflect.Descriptor instead.
func (*ResourceUtilization) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{3}
}

func (x *ResourceUtilization) GetCollectionTime() int64 {
	if x != nil {
		return x.CollectionTime
	}
	return 0
}

func (x *ResourceUtilization) GetCpuUtilization() float32 {
	if x != nil {
		return x.CpuUtilization
	}
	return 0
}

func (x *ResourceUtilization) GetMemUtilization() float32 {
	if x != nil {
		return x.MemUtilization
	}
	return 0
}

func (x *ResourceUtilization) GetDiskUtilization() float32 {
	if x != nil {
		return x.DiskUtilization
	}
	return 0
}

func (x *ResourceUtilization) GetCpuAlarmEnable() bool {
	if x != nil {
		return x.CpuAlarmEnable
	}
	return false
}

func (x *ResourceUtilization) GetCpuThreshold() float32 {
	if x != nil {
		return x.CpuThreshold
	}
	return 0
}

func (x *ResourceUtilization) GetMemAlarmEnable() bool {
	if x != nil {
		return x.MemAlarmEnable
	}
	return false
}

func (x *ResourceUtilization) GetMemThreshold() float32 {
	if x != nil {
		return x.MemThreshold
	}
	return 0
}

func (x *ResourceUtilization) GetDiskAlarmEnable() bool {
	if x != nil {
		return x.DiskAlarmEnable
	}
	return false
}

func (x *ResourceUtilization) GetDiskThreshold() float32 {
	if x != nil {
		return x.DiskThreshold
	}
	return 0
}

type ResourceAlarmList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlarmList []*ResourceAlarm `protobuf:"bytes,1,rep,name=alarm_list,json=alarmList,proto3" json:"alarm_list,omitempty"`
}

func (x *ResourceAlarmList) Reset() {
	*x = ResourceAlarmList{}
	mi := &file_agent_assets_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceAlarmList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAlarmList) ProtoMessage() {}

func (x *ResourceAlarmList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAlarmList.ProtoReflect.Descriptor instead.
func (*ResourceAlarmList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{4}
}

func (x *ResourceAlarmList) GetAlarmList() []*ResourceAlarm {
	if x != nil {
		return x.AlarmList
	}
	return nil
}

// 资源使用率告警
type ResourceAlarm struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResourceType    uint32  `protobuf:"varint,1,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`          // 资源类型 1 CPU 2 内存 3 磁盘
	AlarmTime       int64   `protobuf:"varint,2,opt,name=alarm_time,json=alarmTime,proto3" json:"alarm_time,omitempty"`                   // 告警时间
	Utilization     float32 `protobuf:"fixed32,3,opt,name=utilization,proto3" json:"utilization,omitempty"`                               // 资源使用率
	PolicyThreshold uint32  `protobuf:"varint,4,opt,name=policy_threshold,json=policyThreshold,proto3" json:"policy_threshold,omitempty"` // 策略阈值
}

func (x *ResourceAlarm) Reset() {
	*x = ResourceAlarm{}
	mi := &file_agent_assets_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceAlarm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceAlarm) ProtoMessage() {}

func (x *ResourceAlarm) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceAlarm.ProtoReflect.Descriptor instead.
func (*ResourceAlarm) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{5}
}

func (x *ResourceAlarm) GetResourceType() uint32 {
	if x != nil {
		return x.ResourceType
	}
	return 0
}

func (x *ResourceAlarm) GetAlarmTime() int64 {
	if x != nil {
		return x.AlarmTime
	}
	return 0
}

func (x *ResourceAlarm) GetUtilization() float32 {
	if x != nil {
		return x.Utilization
	}
	return 0
}

func (x *ResourceAlarm) GetPolicyThreshold() uint32 {
	if x != nil {
		return x.PolicyThreshold
	}
	return 0
}

// 资产管理：账户信息,不一定包含登录信息
type AccountInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName           []byte `protobuf:"bytes,1,opt,name=userName,proto3" json:"userName,omitempty"`                      //账户名
	UserId             []byte `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`                          //账户ID
	GroupId            []byte `protobuf:"bytes,3,opt,name=groupId,proto3" json:"groupId,omitempty"`                        //所属组ID
	State              bool   `protobuf:"varint,4,opt,name=state,proto3" json:"state,omitempty"`                           //账户所属状态：“启用” “禁用”
	ExpireDate         []byte `protobuf:"bytes,5,opt,name=expireDate,proto3" json:"expireDate,omitempty"`                  //密码过期时间
	IsSuperUser        bool   `protobuf:"varint,6,opt,name=isSuperUser,proto3" json:"isSuperUser,omitempty"`               //管理员权限root
	LoginPort          []byte `protobuf:"bytes,7,opt,name=loginPort,proto3" json:"loginPort,omitempty"`                    //登录终端类型
	LoginIP            []byte `protobuf:"bytes,8,opt,name=loginIP,proto3" json:"loginIP,omitempty"`                        //登录IP
	LoginTime          []byte `protobuf:"bytes,9,opt,name=loginTime,proto3" json:"loginTime,omitempty"`                    //最近一次登录时间
	PasswordChangeTime []byte `protobuf:"bytes,10,opt,name=passwordChangeTime,proto3" json:"passwordChangeTime,omitempty"` //密码最后修改时间（格式：2019-10-15）
	WeakPassword       bool   `protobuf:"varint,11,opt,name=weakPassword,proto3" json:"weakPassword,omitempty"`            //是否弱口令 not implement
	Interactive        bool   `protobuf:"varint,12,opt,name=interactive,proto3" json:"interactive,omitempty"`              //是否允许交互登录（仅Linux）
	HomePath           []byte `protobuf:"bytes,13,opt,name=homePath,proto3" json:"homePath,omitempty"`                     //账户home目录
	IsClone            bool   `protobuf:"varint,14,opt,name=isClone,proto3" json:"isClone,omitempty"`                      //克隆账户（仅win）
	IsHidden           bool   `protobuf:"varint,15,opt,name=isHidden,proto3" json:"isHidden,omitempty"`                    //隐藏账户（仅win）
	AccountExpireDate  []byte `protobuf:"bytes,16,opt,name=accountExpireDate,proto3" json:"accountExpireDate,omitempty"`   //账号过期时间
	// 1209 新增
	Shell           string `protobuf:"bytes,17,opt,name=shell,proto3" json:"shell,omitempty"`                      //登录终端类型
	Description     string `protobuf:"bytes,18,opt,name=description,proto3" json:"description,omitempty"`          //账号描述
	IsDomainAccount bool   `protobuf:"varint,19,opt,name=isDomainAccount,proto3" json:"isDomainAccount,omitempty"` //是否是域账号
	// 1215 新增
	WeakpwdRegex   []byte                 `protobuf:"bytes,20,opt,name=weakpwdRegex,proto3" json:"weakpwdRegex,omitempty"`     // 弱密码命中的正则，如果命中的是字典，将上报为空
	WeakpwdType    int32                  `protobuf:"varint,21,opt,name=weakpwdType,proto3" json:"weakpwdType,omitempty"`      // 1 正则 2 字典 3 内置
	Weakpwdhistory []*WeakPasswordhistory `protobuf:"bytes,22,rep,name=weakpwdhistory,proto3" json:"weakpwdhistory,omitempty"` // 历史登录信息
}

func (x *AccountInformation) Reset() {
	*x = AccountInformation{}
	mi := &file_agent_assets_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInformation) ProtoMessage() {}

func (x *AccountInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInformation.ProtoReflect.Descriptor instead.
func (*AccountInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{6}
}

func (x *AccountInformation) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *AccountInformation) GetUserId() []byte {
	if x != nil {
		return x.UserId
	}
	return nil
}

func (x *AccountInformation) GetGroupId() []byte {
	if x != nil {
		return x.GroupId
	}
	return nil
}

func (x *AccountInformation) GetState() bool {
	if x != nil {
		return x.State
	}
	return false
}

func (x *AccountInformation) GetExpireDate() []byte {
	if x != nil {
		return x.ExpireDate
	}
	return nil
}

func (x *AccountInformation) GetIsSuperUser() bool {
	if x != nil {
		return x.IsSuperUser
	}
	return false
}

func (x *AccountInformation) GetLoginPort() []byte {
	if x != nil {
		return x.LoginPort
	}
	return nil
}

func (x *AccountInformation) GetLoginIP() []byte {
	if x != nil {
		return x.LoginIP
	}
	return nil
}

func (x *AccountInformation) GetLoginTime() []byte {
	if x != nil {
		return x.LoginTime
	}
	return nil
}

func (x *AccountInformation) GetPasswordChangeTime() []byte {
	if x != nil {
		return x.PasswordChangeTime
	}
	return nil
}

func (x *AccountInformation) GetWeakPassword() bool {
	if x != nil {
		return x.WeakPassword
	}
	return false
}

func (x *AccountInformation) GetInteractive() bool {
	if x != nil {
		return x.Interactive
	}
	return false
}

func (x *AccountInformation) GetHomePath() []byte {
	if x != nil {
		return x.HomePath
	}
	return nil
}

func (x *AccountInformation) GetIsClone() bool {
	if x != nil {
		return x.IsClone
	}
	return false
}

func (x *AccountInformation) GetIsHidden() bool {
	if x != nil {
		return x.IsHidden
	}
	return false
}

func (x *AccountInformation) GetAccountExpireDate() []byte {
	if x != nil {
		return x.AccountExpireDate
	}
	return nil
}

func (x *AccountInformation) GetShell() string {
	if x != nil {
		return x.Shell
	}
	return ""
}

func (x *AccountInformation) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AccountInformation) GetIsDomainAccount() bool {
	if x != nil {
		return x.IsDomainAccount
	}
	return false
}

func (x *AccountInformation) GetWeakpwdRegex() []byte {
	if x != nil {
		return x.WeakpwdRegex
	}
	return nil
}

func (x *AccountInformation) GetWeakpwdType() int32 {
	if x != nil {
		return x.WeakpwdType
	}
	return 0
}

func (x *AccountInformation) GetWeakpwdhistory() []*WeakPasswordhistory {
	if x != nil {
		return x.Weakpwdhistory
	}
	return nil
}

type WeakPasswordhistory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip         string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	LoginTimes int64  `protobuf:"varint,2,opt,name=login_times,json=loginTimes,proto3" json:"login_times,omitempty"`
}

func (x *WeakPasswordhistory) Reset() {
	*x = WeakPasswordhistory{}
	mi := &file_agent_assets_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeakPasswordhistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeakPasswordhistory) ProtoMessage() {}

func (x *WeakPasswordhistory) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeakPasswordhistory.ProtoReflect.Descriptor instead.
func (*WeakPasswordhistory) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{7}
}

func (x *WeakPasswordhistory) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *WeakPasswordhistory) GetLoginTimes() int64 {
	if x != nil {
		return x.LoginTimes
	}
	return 0
}

// 资产管理：环境变量信息
type EnvInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`   //环境变量名称
	Value []byte `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"` //环境变量值
}

func (x *EnvInformation) Reset() {
	*x = EnvInformation{}
	mi := &file_agent_assets_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvInformation) ProtoMessage() {}

func (x *EnvInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvInformation.ProtoReflect.Descriptor instead.
func (*EnvInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{8}
}

func (x *EnvInformation) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *EnvInformation) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

// 资产管理：内核模块信息
type KernelInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`               //内核模块名称
	Path        []byte `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`               //文件路径
	License     []byte `protobuf:"bytes,3,opt,name=license,proto3" json:"license,omitempty"`         //文件版权信息
	Signature   []byte `protobuf:"bytes,4,opt,name=signature,proto3" json:"signature,omitempty"`     //签名信息
	Description []byte `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"` //描述
	Services    []byte `protobuf:"bytes,6,opt,name=services,proto3" json:"services,omitempty"`       //对应服务名
	Loaded      bool   `protobuf:"varint,7,opt,name=loaded,proto3" json:"loaded,omitempty"`          //是否加载
	RiskLevel   uint32 `protobuf:"varint,8,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`    //存在风险的模块，根据签名等判断
	ImageSize   uint32 `protobuf:"varint,9,opt,name=imageSize,proto3" json:"imageSize,omitempty"`    //内核镜像大小
	Sha256      []byte `protobuf:"bytes,10,opt,name=sha256,proto3" json:"sha256,omitempty"`          // 文件sha256
	Company     []byte `protobuf:"bytes,11,opt,name=company,proto3" json:"company,omitempty"`        //公司名
}

func (x *KernelInformation) Reset() {
	*x = KernelInformation{}
	mi := &file_agent_assets_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KernelInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KernelInformation) ProtoMessage() {}

func (x *KernelInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KernelInformation.ProtoReflect.Descriptor instead.
func (*KernelInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{9}
}

func (x *KernelInformation) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *KernelInformation) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *KernelInformation) GetLicense() []byte {
	if x != nil {
		return x.License
	}
	return nil
}

func (x *KernelInformation) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *KernelInformation) GetDescription() []byte {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *KernelInformation) GetServices() []byte {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *KernelInformation) GetLoaded() bool {
	if x != nil {
		return x.Loaded
	}
	return false
}

func (x *KernelInformation) GetRiskLevel() uint32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *KernelInformation) GetImageSize() uint32 {
	if x != nil {
		return x.ImageSize
	}
	return 0
}

func (x *KernelInformation) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

func (x *KernelInformation) GetCompany() []byte {
	if x != nil {
		return x.Company
	}
	return nil
}

// 资产管理：启动服务信息
type ServiceInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` //服务名称
	Path []byte `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"` //服务启动路径
	// 状态：运行状态
	//
	//	1:服务未运行 2:服务正在启动 3:服务正在停止 4:服务正在运行
	//	5:服务即将继续 6:服务即将暂停 7:服务已暂停
	State       []byte `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	StartTime   []byte `protobuf:"bytes,4,opt,name=startTime,proto3" json:"startTime,omitempty"`      //服务启动时间 2019-10-15 12:50:23
	StartType   []byte `protobuf:"bytes,5,opt,name=startType,proto3" json:"startType,omitempty"`      //启动类型 win特有
	Company     []byte `protobuf:"bytes,6,opt,name=company,proto3" json:"company,omitempty"`          //文件厂商 win特有
	DllPath     []byte `protobuf:"bytes,7,opt,name=dllPath,proto3" json:"dllPath,omitempty"`          //动态链接库 win特有
	DllCompany  []byte `protobuf:"bytes,8,opt,name=dllCompany,proto3" json:"dllCompany,omitempty"`    //动态链接库文件厂商 win特有
	Sha256      []byte `protobuf:"bytes,9,opt,name=sha256,proto3" json:"sha256,omitempty"`            //文件SHA256
	Sha256Dll   []byte `protobuf:"bytes,10,opt,name=sha256Dll,proto3" json:"sha256Dll,omitempty"`     //dll文件SHA256 win特有
	ProcessID   uint32 `protobuf:"varint,11,opt,name=processID,proto3" json:"processID,omitempty"`    //运行状态进程ID
	Description []byte `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"` //描述
}

func (x *ServiceInformation) Reset() {
	*x = ServiceInformation{}
	mi := &file_agent_assets_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceInformation) ProtoMessage() {}

func (x *ServiceInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceInformation.ProtoReflect.Descriptor instead.
func (*ServiceInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{10}
}

func (x *ServiceInformation) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ServiceInformation) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *ServiceInformation) GetState() []byte {
	if x != nil {
		return x.State
	}
	return nil
}

func (x *ServiceInformation) GetStartTime() []byte {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ServiceInformation) GetStartType() []byte {
	if x != nil {
		return x.StartType
	}
	return nil
}

func (x *ServiceInformation) GetCompany() []byte {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *ServiceInformation) GetDllPath() []byte {
	if x != nil {
		return x.DllPath
	}
	return nil
}

func (x *ServiceInformation) GetDllCompany() []byte {
	if x != nil {
		return x.DllCompany
	}
	return nil
}

func (x *ServiceInformation) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

func (x *ServiceInformation) GetSha256Dll() []byte {
	if x != nil {
		return x.Sha256Dll
	}
	return nil
}

func (x *ServiceInformation) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *ServiceInformation) GetDescription() []byte {
	if x != nil {
		return x.Description
	}
	return nil
}

// 资产管理：进程信息
type ProcInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name           []byte  `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                        //进程名
	Path           []byte  `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`                        //文件路径
	Type           []byte  `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                        //进程分类 not implement
	UserName       []byte  `protobuf:"bytes,4,opt,name=userName,proto3" json:"userName,omitempty"`                //运行用户
	ThreadNum      uint32  `protobuf:"varint,5,opt,name=threadNum,proto3" json:"threadNum,omitempty"`             //线程数
	MemRss         uint64  `protobuf:"varint,6,opt,name=memRss,proto3" json:"memRss,omitempty"`                   //占用主机内存大小（字节）
	MemVss         uint64  `protobuf:"varint,7,opt,name=memVss,proto3" json:"memVss,omitempty"`                   //虚拟内存大小    （字节）
	IoRead         uint64  `protobuf:"varint,8,opt,name=ioRead,proto3" json:"ioRead,omitempty"`                   //该进程IO读取大小（字节）
	IoWrite        uint64  `protobuf:"varint,9,opt,name=ioWrite,proto3" json:"ioWrite,omitempty"`                 //该进程IO写入大小（字节）
	CpuUtilization float32 `protobuf:"fixed32,10,opt,name=cpuUtilization,proto3" json:"cpuUtilization,omitempty"` //CPU使用率%
	StartTime      []byte  `protobuf:"bytes,11,opt,name=startTime,proto3" json:"startTime,omitempty"`             //启动时间 格式：2019-10-15 12:50:23
	ProcessID      uint32  `protobuf:"varint,12,opt,name=processID,proto3" json:"processID,omitempty"`            //进程PID
	CommandInfo    []byte  `protobuf:"bytes,13,opt,name=commandInfo,proto3" json:"commandInfo,omitempty"`         //命令行参数
	Description    []byte  `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`         //描述
	// 1209 新增
	MemUtilization float32            `protobuf:"fixed32,15,opt,name=memUtilization,proto3" json:"memUtilization,omitempty"` // 内存使用率%
	Ports          []*PortInformation `protobuf:"bytes,16,rep,name=Ports,proto3" json:"Ports,omitempty"`                     // 进程打开的端口信息
}

func (x *ProcInformation) Reset() {
	*x = ProcInformation{}
	mi := &file_agent_assets_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcInformation) ProtoMessage() {}

func (x *ProcInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcInformation.ProtoReflect.Descriptor instead.
func (*ProcInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{11}
}

func (x *ProcInformation) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *ProcInformation) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *ProcInformation) GetType() []byte {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *ProcInformation) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *ProcInformation) GetThreadNum() uint32 {
	if x != nil {
		return x.ThreadNum
	}
	return 0
}

func (x *ProcInformation) GetMemRss() uint64 {
	if x != nil {
		return x.MemRss
	}
	return 0
}

func (x *ProcInformation) GetMemVss() uint64 {
	if x != nil {
		return x.MemVss
	}
	return 0
}

func (x *ProcInformation) GetIoRead() uint64 {
	if x != nil {
		return x.IoRead
	}
	return 0
}

func (x *ProcInformation) GetIoWrite() uint64 {
	if x != nil {
		return x.IoWrite
	}
	return 0
}

func (x *ProcInformation) GetCpuUtilization() float32 {
	if x != nil {
		return x.CpuUtilization
	}
	return 0
}

func (x *ProcInformation) GetStartTime() []byte {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ProcInformation) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *ProcInformation) GetCommandInfo() []byte {
	if x != nil {
		return x.CommandInfo
	}
	return nil
}

func (x *ProcInformation) GetDescription() []byte {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *ProcInformation) GetMemUtilization() float32 {
	if x != nil {
		return x.MemUtilization
	}
	return 0
}

func (x *ProcInformation) GetPorts() []*PortInformation {
	if x != nil {
		return x.Ports
	}
	return nil
}

type PortInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port     uint32 `protobuf:"varint,1,opt,name=Port,proto3" json:"Port,omitempty"`        // 端口号
	Protocol string `protobuf:"bytes,2,opt,name=Protocol,proto3" json:"Protocol,omitempty"` // 协议（TCP,UDP)
}

func (x *PortInformation) Reset() {
	*x = PortInformation{}
	mi := &file_agent_assets_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortInformation) ProtoMessage() {}

func (x *PortInformation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortInformation.ProtoReflect.Descriptor instead.
func (*PortInformation) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{12}
}

func (x *PortInformation) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *PortInformation) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 域控资产
type DomainInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseDN           string          `protobuf:"bytes,1,opt,name=baseDN,proto3" json:"baseDN,omitempty"`                     // 域节点名称
	DomainName       string          `protobuf:"bytes,2,opt,name=domainName,proto3" json:"domainName,omitempty"`             // 域名
	Dns              string          `protobuf:"bytes,3,opt,name=dns,proto3" json:"dns,omitempty"`                           // DNS服务器地址
	NetBIOS          string          `protobuf:"bytes,4,opt,name=netBIOS,proto3" json:"netBIOS,omitempty"`                   // NetBIOS名称(计算机名)
	NetShareInfoList []*NetShareInfo `protobuf:"bytes,5,rep,name=netShareInfoList,proto3" json:"netShareInfoList,omitempty"` // 共享文件夹列表
}

func (x *DomainInfo) Reset() {
	*x = DomainInfo{}
	mi := &file_agent_assets_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainInfo) ProtoMessage() {}

func (x *DomainInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainInfo.ProtoReflect.Descriptor instead.
func (*DomainInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{13}
}

func (x *DomainInfo) GetBaseDN() string {
	if x != nil {
		return x.BaseDN
	}
	return ""
}

func (x *DomainInfo) GetDomainName() string {
	if x != nil {
		return x.DomainName
	}
	return ""
}

func (x *DomainInfo) GetDns() string {
	if x != nil {
		return x.Dns
	}
	return ""
}

func (x *DomainInfo) GetNetBIOS() string {
	if x != nil {
		return x.NetBIOS
	}
	return ""
}

func (x *DomainInfo) GetNetShareInfoList() []*NetShareInfo {
	if x != nil {
		return x.NetShareInfoList
	}
	return nil
}

// 共享文件夹
type NetShareInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"` // 文件夹名
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"` // 路径
}

func (x *NetShareInfo) Reset() {
	*x = NetShareInfo{}
	mi := &file_agent_assets_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetShareInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetShareInfo) ProtoMessage() {}

func (x *NetShareInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetShareInfo.ProtoReflect.Descriptor instead.
func (*NetShareInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{14}
}

func (x *NetShareInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NetShareInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

// Jar包信息
type JarInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                         // jar包名称
	Summary  []byte `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`                   // 类型描述
	IsExec   bool   `protobuf:"varint,3,opt,name=is_exec,json=isExec,proto3" json:"is_exec,omitempty"`      // 是否可执行
	Version  []byte `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`                   // 版本号
	Path     []byte `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                         // 绝对路径
	BusiName []byte `protobuf:"bytes,6,opt,name=busi_name,json=busiName,proto3" json:"busi_name,omitempty"` // 所属服务
	Pid      []byte `protobuf:"bytes,7,opt,name=pid,proto3" json:"pid,omitempty"`                           // 进程id
}

func (x *JarInfo) Reset() {
	*x = JarInfo{}
	mi := &file_agent_assets_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JarInfo) ProtoMessage() {}

func (x *JarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JarInfo.ProtoReflect.Descriptor instead.
func (*JarInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{15}
}

func (x *JarInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *JarInfo) GetSummary() []byte {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *JarInfo) GetIsExec() bool {
	if x != nil {
		return x.IsExec
	}
	return false
}

func (x *JarInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *JarInfo) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *JarInfo) GetBusiName() []byte {
	if x != nil {
		return x.BusiName
	}
	return nil
}

func (x *JarInfo) GetPid() []byte {
	if x != nil {
		return x.Pid
	}
	return nil
}

// web中间件链表
type WebMiddlewareList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebMiddleware []*WebMiddleware `protobuf:"bytes,1,rep,name=web_middleware,json=webMiddleware,proto3" json:"web_middleware,omitempty"`
}

func (x *WebMiddlewareList) Reset() {
	*x = WebMiddlewareList{}
	mi := &file_agent_assets_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebMiddlewareList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebMiddlewareList) ProtoMessage() {}

func (x *WebMiddlewareList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebMiddlewareList.ProtoReflect.Descriptor instead.
func (*WebMiddlewareList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{16}
}

func (x *WebMiddlewareList) GetWebMiddleware() []*WebMiddleware {
	if x != nil {
		return x.WebMiddleware
	}
	return nil
}

// web中间件 (仅Linux)
type WebMiddleware struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebName           []byte     `protobuf:"bytes,1,opt,name=web_name,json=webName,proto3" json:"web_name,omitempty"`                               //web服务名
	WebVersion        []byte     `protobuf:"bytes,2,opt,name=web_version,json=webVersion,proto3" json:"web_version,omitempty"`                      //应用版本
	BinaryPath        []byte     `protobuf:"bytes,3,opt,name=binary_path,json=binaryPath,proto3" json:"binary_path,omitempty"`                      //二进制路径
	StartUser         []byte     `protobuf:"bytes,4,opt,name=start_user,json=startUser,proto3" json:"start_user,omitempty"`                         //启动用户
	ConfigurationPath []byte     `protobuf:"bytes,5,opt,name=configuration_path,json=configurationPath,proto3" json:"configuration_path,omitempty"` //配置文件路径
	ProcessId         uint32     `protobuf:"varint,6,opt,name=process_id,json=processId,proto3" json:"process_id,omitempty"`                        //进程PID
	JarInfoList       []*JarInfo `protobuf:"bytes,7,rep,name=jar_info_list,json=jarInfoList,proto3" json:"jar_info_list,omitempty"`                 //关联的jar
}

func (x *WebMiddleware) Reset() {
	*x = WebMiddleware{}
	mi := &file_agent_assets_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebMiddleware) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebMiddleware) ProtoMessage() {}

func (x *WebMiddleware) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebMiddleware.ProtoReflect.Descriptor instead.
func (*WebMiddleware) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{17}
}

func (x *WebMiddleware) GetWebName() []byte {
	if x != nil {
		return x.WebName
	}
	return nil
}

func (x *WebMiddleware) GetWebVersion() []byte {
	if x != nil {
		return x.WebVersion
	}
	return nil
}

func (x *WebMiddleware) GetBinaryPath() []byte {
	if x != nil {
		return x.BinaryPath
	}
	return nil
}

func (x *WebMiddleware) GetStartUser() []byte {
	if x != nil {
		return x.StartUser
	}
	return nil
}

func (x *WebMiddleware) GetConfigurationPath() []byte {
	if x != nil {
		return x.ConfigurationPath
	}
	return nil
}

func (x *WebMiddleware) GetProcessId() uint32 {
	if x != nil {
		return x.ProcessId
	}
	return 0
}

func (x *WebMiddleware) GetJarInfoList() []*JarInfo {
	if x != nil {
		return x.JarInfoList
	}
	return nil
}

type WebSiteAppInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VirtualPath []byte `protobuf:"bytes,1,opt,name=virtual_path,json=virtualPath,proto3" json:"virtual_path,omitempty"` //虚拟路径
	RealPath    []byte `protobuf:"bytes,2,opt,name=real_path,json=realPath,proto3" json:"real_path,omitempty"`          //真实路径
	Author      []byte `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`                              //所属用户
	Group       []byte `protobuf:"bytes,4,opt,name=group,proto3" json:"group,omitempty"`                                //所属组
	Authority   []byte `protobuf:"bytes,5,opt,name=authority,proto3" json:"authority,omitempty"`                        //文件权限
}

func (x *WebSiteAppInfo) Reset() {
	*x = WebSiteAppInfo{}
	mi := &file_agent_assets_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebSiteAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSiteAppInfo) ProtoMessage() {}

func (x *WebSiteAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSiteAppInfo.ProtoReflect.Descriptor instead.
func (*WebSiteAppInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{18}
}

func (x *WebSiteAppInfo) GetVirtualPath() []byte {
	if x != nil {
		return x.VirtualPath
	}
	return nil
}

func (x *WebSiteAppInfo) GetRealPath() []byte {
	if x != nil {
		return x.RealPath
	}
	return nil
}

func (x *WebSiteAppInfo) GetAuthor() []byte {
	if x != nil {
		return x.Author
	}
	return nil
}

func (x *WebSiteAppInfo) GetGroup() []byte {
	if x != nil {
		return x.Group
	}
	return nil
}

func (x *WebSiteAppInfo) GetAuthority() []byte {
	if x != nil {
		return x.Authority
	}
	return nil
}

type WebSiteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip               []byte            `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`                                                       //ip
	Port             []byte            `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`                                                   //端口号
	Protocol         []byte            `protobuf:"bytes,3,opt,name=protocol,proto3" json:"protocol,omitempty"`                                           //协议类型
	User             []byte            `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`                                                   //用户
	ServerName       []byte            `protobuf:"bytes,5,opt,name=server_name,json=serverName,proto3" json:"server_name,omitempty"`                     //所属服务
	WarDir           []byte            `protobuf:"bytes,6,opt,name=war_dir,json=warDir,proto3" json:"war_dir,omitempty"`                                 //war包部署总目录
	RootDir          []byte            `protobuf:"bytes,7,opt,name=root_dir,json=rootDir,proto3" json:"root_dir,omitempty"`                              //主目录
	RootDirAuthority []byte            `protobuf:"bytes,8,opt,name=root_dir_authority,json=rootDirAuthority,proto3" json:"root_dir_authority,omitempty"` //主目录权限
	SiteCount        uint32            `protobuf:"varint,9,opt,name=site_count,json=siteCount,proto3" json:"site_count,omitempty"`                       //站点总数
	WebSiteApps      []*WebSiteAppInfo `protobuf:"bytes,10,rep,name=web_site_apps,json=webSiteApps,proto3" json:"web_site_apps,omitempty"`               //安装的APP
}

func (x *WebSiteInfo) Reset() {
	*x = WebSiteInfo{}
	mi := &file_agent_assets_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebSiteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSiteInfo) ProtoMessage() {}

func (x *WebSiteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSiteInfo.ProtoReflect.Descriptor instead.
func (*WebSiteInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{19}
}

func (x *WebSiteInfo) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *WebSiteInfo) GetPort() []byte {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *WebSiteInfo) GetProtocol() []byte {
	if x != nil {
		return x.Protocol
	}
	return nil
}

func (x *WebSiteInfo) GetUser() []byte {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *WebSiteInfo) GetServerName() []byte {
	if x != nil {
		return x.ServerName
	}
	return nil
}

func (x *WebSiteInfo) GetWarDir() []byte {
	if x != nil {
		return x.WarDir
	}
	return nil
}

func (x *WebSiteInfo) GetRootDir() []byte {
	if x != nil {
		return x.RootDir
	}
	return nil
}

func (x *WebSiteInfo) GetRootDirAuthority() []byte {
	if x != nil {
		return x.RootDirAuthority
	}
	return nil
}

func (x *WebSiteInfo) GetSiteCount() uint32 {
	if x != nil {
		return x.SiteCount
	}
	return 0
}

func (x *WebSiteInfo) GetWebSiteApps() []*WebSiteAppInfo {
	if x != nil {
		return x.WebSiteApps
	}
	return nil
}

type WebSiteInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebSiteInfo []*WebSiteInfo `protobuf:"bytes,1,rep,name=web_site_info,json=webSiteInfo,proto3" json:"web_site_info,omitempty"`
}

func (x *WebSiteInfoList) Reset() {
	*x = WebSiteInfoList{}
	mi := &file_agent_assets_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebSiteInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSiteInfoList) ProtoMessage() {}

func (x *WebSiteInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSiteInfoList.ProtoReflect.Descriptor instead.
func (*WebSiteInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{20}
}

func (x *WebSiteInfoList) GetWebSiteInfo() []*WebSiteInfo {
	if x != nil {
		return x.WebSiteInfo
	}
	return nil
}

// web框架
type WebFrame struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FrameName     []byte     `protobuf:"bytes,1,opt,name=frame_name,json=frameName,proto3" json:"frame_name,omitempty"`             //框架名称
	FrameVersion  []byte     `protobuf:"bytes,2,opt,name=frame_version,json=frameVersion,proto3" json:"frame_version,omitempty"`    //框架版本
	WebServer     []byte     `protobuf:"bytes,3,opt,name=web_server,json=webServer,proto3" json:"web_server,omitempty"`             //服务类型
	WebPath       []byte     `protobuf:"bytes,4,opt,name=web_path,json=webPath,proto3" json:"web_path,omitempty"`                   //应用路径
	FrameLanguage []byte     `protobuf:"bytes,5,opt,name=frame_language,json=frameLanguage,proto3" json:"frame_language,omitempty"` //框架语言
	JarInfoList   []*JarInfo `protobuf:"bytes,6,rep,name=jar_info_list,json=jarInfoList,proto3" json:"jar_info_list,omitempty"`     //关联的jar
}

func (x *WebFrame) Reset() {
	*x = WebFrame{}
	mi := &file_agent_assets_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebFrame) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebFrame) ProtoMessage() {}

func (x *WebFrame) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebFrame.ProtoReflect.Descriptor instead.
func (*WebFrame) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{21}
}

func (x *WebFrame) GetFrameName() []byte {
	if x != nil {
		return x.FrameName
	}
	return nil
}

func (x *WebFrame) GetFrameVersion() []byte {
	if x != nil {
		return x.FrameVersion
	}
	return nil
}

func (x *WebFrame) GetWebServer() []byte {
	if x != nil {
		return x.WebServer
	}
	return nil
}

func (x *WebFrame) GetWebPath() []byte {
	if x != nil {
		return x.WebPath
	}
	return nil
}

func (x *WebFrame) GetFrameLanguage() []byte {
	if x != nil {
		return x.FrameLanguage
	}
	return nil
}

func (x *WebFrame) GetJarInfoList() []*JarInfo {
	if x != nil {
		return x.JarInfoList
	}
	return nil
}

type WebFrameList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebFrameInfo []*WebFrame `protobuf:"bytes,1,rep,name=web_frame_info,json=webFrameInfo,proto3" json:"web_frame_info,omitempty"`
}

func (x *WebFrameList) Reset() {
	*x = WebFrameList{}
	mi := &file_agent_assets_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebFrameList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebFrameList) ProtoMessage() {}

func (x *WebFrameList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebFrameList.ProtoReflect.Descriptor instead.
func (*WebFrameList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{22}
}

func (x *WebFrameList) GetWebFrameInfo() []*WebFrame {
	if x != nil {
		return x.WebFrameInfo
	}
	return nil
}

// 软件应用关联进程信息
type RelateProcInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name      []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                            //关联进程名
	Version   []byte `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                      //关联进程版本
	ProcessId []byte `protobuf:"bytes,3,opt,name=process_id,json=processId,proto3" json:"process_id,omitempty"` //PID
	StartTime []byte `protobuf:"bytes,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` //启动时间
	Path      []byte `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                            //进程路径
}

func (x *RelateProcInfo) Reset() {
	*x = RelateProcInfo{}
	mi := &file_agent_assets_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelateProcInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelateProcInfo) ProtoMessage() {}

func (x *RelateProcInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelateProcInfo.ProtoReflect.Descriptor instead.
func (*RelateProcInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{23}
}

func (x *RelateProcInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *RelateProcInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *RelateProcInfo) GetProcessId() []byte {
	if x != nil {
		return x.ProcessId
	}
	return nil
}

func (x *RelateProcInfo) GetStartTime() []byte {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *RelateProcInfo) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

type HostApplicationInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        []byte            `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                    //应用名称
	Type        int32             `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`                                   //应用类别 1 普通应用 2 web应用 3 系统应用
	Version     []byte            `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                              //版本
	User        []byte            `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`                                    //启动用户
	Path        []byte            `protobuf:"bytes,5,opt,name=path,proto3" json:"path,omitempty"`                                    //二进制路径
	ConfigPath  []byte            `protobuf:"bytes,6,opt,name=config_path,json=configPath,proto3" json:"config_path,omitempty"`      //配置文件路径
	ProcNum     uint64            `protobuf:"varint,7,opt,name=proc_num,json=procNum,proto3" json:"proc_num,omitempty"`              //关联进程数
	AppProcList []*RelateProcInfo `protobuf:"bytes,8,rep,name=app_proc_list,json=appProcList,proto3" json:"app_proc_list,omitempty"` //具体关联进程信息
	JarInfoList []*JarInfo        `protobuf:"bytes,9,rep,name=jar_info_list,json=jarInfoList,proto3" json:"jar_info_list,omitempty"` //关联的jar
}

func (x *HostApplicationInfo) Reset() {
	*x = HostApplicationInfo{}
	mi := &file_agent_assets_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostApplicationInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostApplicationInfo) ProtoMessage() {}

func (x *HostApplicationInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostApplicationInfo.ProtoReflect.Descriptor instead.
func (*HostApplicationInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{24}
}

func (x *HostApplicationInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *HostApplicationInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *HostApplicationInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *HostApplicationInfo) GetUser() []byte {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *HostApplicationInfo) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *HostApplicationInfo) GetConfigPath() []byte {
	if x != nil {
		return x.ConfigPath
	}
	return nil
}

func (x *HostApplicationInfo) GetProcNum() uint64 {
	if x != nil {
		return x.ProcNum
	}
	return 0
}

func (x *HostApplicationInfo) GetAppProcList() []*RelateProcInfo {
	if x != nil {
		return x.AppProcList
	}
	return nil
}

func (x *HostApplicationInfo) GetJarInfoList() []*JarInfo {
	if x != nil {
		return x.JarInfoList
	}
	return nil
}

type HostApplicationList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostAppList []*HostApplicationInfo `protobuf:"bytes,1,rep,name=host_app_list,json=hostAppList,proto3" json:"host_app_list,omitempty"`
}

func (x *HostApplicationList) Reset() {
	*x = HostApplicationList{}
	mi := &file_agent_assets_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostApplicationList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostApplicationList) ProtoMessage() {}

func (x *HostApplicationList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostApplicationList.ProtoReflect.Descriptor instead.
func (*HostApplicationList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{25}
}

func (x *HostApplicationList) GetHostAppList() []*HostApplicationInfo {
	if x != nil {
		return x.HostAppList
	}
	return nil
}

// 安装包信息
type PackageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name         []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                      //安装包名称
	Summary      []byte `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`                                //总述
	Version      []byte `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`                                //版本
	Release      []byte `protobuf:"bytes,4,opt,name=release,proto3" json:"release,omitempty"`                                //发行号
	PackageType  []byte `protobuf:"bytes,5,opt,name=package_type,json=packageType,proto3" json:"package_type,omitempty"`     //安装包类型;
	Vendor       []byte `protobuf:"bytes,6,opt,name=vendor,proto3" json:"vendor,omitempty"`                                  //安装包发行厂商
	Architecture []byte `protobuf:"bytes,7,opt,name=architecture,proto3" json:"architecture,omitempty"`                      //架构信息
	SizeInKb     uint64 `protobuf:"varint,8,opt,name=size_in_kb,json=sizeInKb,proto3" json:"size_in_kb,omitempty"`           //安装包大小（kb）
	CreationTime uint64 `protobuf:"varint,9,opt,name=creation_time,json=creationTime,proto3" json:"creation_time,omitempty"` //安装时间
}

func (x *PackageInfo) Reset() {
	*x = PackageInfo{}
	mi := &file_agent_assets_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageInfo) ProtoMessage() {}

func (x *PackageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageInfo.ProtoReflect.Descriptor instead.
func (*PackageInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{26}
}

func (x *PackageInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *PackageInfo) GetSummary() []byte {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *PackageInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *PackageInfo) GetRelease() []byte {
	if x != nil {
		return x.Release
	}
	return nil
}

func (x *PackageInfo) GetPackageType() []byte {
	if x != nil {
		return x.PackageType
	}
	return nil
}

func (x *PackageInfo) GetVendor() []byte {
	if x != nil {
		return x.Vendor
	}
	return nil
}

func (x *PackageInfo) GetArchitecture() []byte {
	if x != nil {
		return x.Architecture
	}
	return nil
}

func (x *PackageInfo) GetSizeInKb() uint64 {
	if x != nil {
		return x.SizeInKb
	}
	return 0
}

func (x *PackageInfo) GetCreationTime() uint64 {
	if x != nil {
		return x.CreationTime
	}
	return 0
}

type PackageInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PackageInfo []*PackageInfo `protobuf:"bytes,1,rep,name=package_info,json=packageInfo,proto3" json:"package_info,omitempty"`
}

func (x *PackageInfoList) Reset() {
	*x = PackageInfoList{}
	mi := &file_agent_assets_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageInfoList) ProtoMessage() {}

func (x *PackageInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageInfoList.ProtoReflect.Descriptor instead.
func (*PackageInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{27}
}

func (x *PackageInfoList) GetPackageInfo() []*PackageInfo {
	if x != nil {
		return x.PackageInfo
	}
	return nil
}

// Python包信息
type PythonPackageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                        // Python包名称
	Version       []byte `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`                                  // Python包版本
	Dir           []byte `protobuf:"bytes,3,opt,name=dir,proto3" json:"dir,omitempty"`                                          // Python包所在目录
	PythonVersion []byte `protobuf:"bytes,4,opt,name=python_version,json=pythonVersion,proto3" json:"python_version,omitempty"` // Python包所属Python版本
}

func (x *PythonPackageInfo) Reset() {
	*x = PythonPackageInfo{}
	mi := &file_agent_assets_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PythonPackageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PythonPackageInfo) ProtoMessage() {}

func (x *PythonPackageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PythonPackageInfo.ProtoReflect.Descriptor instead.
func (*PythonPackageInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{28}
}

func (x *PythonPackageInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *PythonPackageInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *PythonPackageInfo) GetDir() []byte {
	if x != nil {
		return x.Dir
	}
	return nil
}

func (x *PythonPackageInfo) GetPythonVersion() []byte {
	if x != nil {
		return x.PythonVersion
	}
	return nil
}

type PythonPackageInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PythonPackageInfo []*PythonPackageInfo `protobuf:"bytes,1,rep,name=python_package_info,json=pythonPackageInfo,proto3" json:"python_package_info,omitempty"`
}

func (x *PythonPackageInfoList) Reset() {
	*x = PythonPackageInfoList{}
	mi := &file_agent_assets_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PythonPackageInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PythonPackageInfoList) ProtoMessage() {}

func (x *PythonPackageInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PythonPackageInfoList.ProtoReflect.Descriptor instead.
func (*PythonPackageInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{29}
}

func (x *PythonPackageInfoList) GetPythonPackageInfo() []*PythonPackageInfo {
	if x != nil {
		return x.PythonPackageInfo
	}
	return nil
}

// web应用
type WebApp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName       []byte `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                   //Web应用名称
	AppVersion    []byte `protobuf:"bytes,2,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`          //Web应用版本
	WebServer     []byte `protobuf:"bytes,3,opt,name=web_server,json=webServer,proto3" json:"web_server,omitempty"`             //服务类型
	WebPath       []byte `protobuf:"bytes,4,opt,name=web_path,json=webPath,proto3" json:"web_path,omitempty"`                   //应用路径
	FrameLanguage []byte `protobuf:"bytes,5,opt,name=frame_language,json=frameLanguage,proto3" json:"frame_language,omitempty"` //框架语言
}

func (x *WebApp) Reset() {
	*x = WebApp{}
	mi := &file_agent_assets_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebApp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebApp) ProtoMessage() {}

func (x *WebApp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebApp.ProtoReflect.Descriptor instead.
func (*WebApp) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{30}
}

func (x *WebApp) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

func (x *WebApp) GetAppVersion() []byte {
	if x != nil {
		return x.AppVersion
	}
	return nil
}

func (x *WebApp) GetWebServer() []byte {
	if x != nil {
		return x.WebServer
	}
	return nil
}

func (x *WebApp) GetWebPath() []byte {
	if x != nil {
		return x.WebPath
	}
	return nil
}

func (x *WebApp) GetFrameLanguage() []byte {
	if x != nil {
		return x.FrameLanguage
	}
	return nil
}

type WebAppList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WebAppInfo []*WebApp `protobuf:"bytes,1,rep,name=web_app_info,json=webAppInfo,proto3" json:"web_app_info,omitempty"`
}

func (x *WebAppList) Reset() {
	*x = WebAppList{}
	mi := &file_agent_assets_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebAppList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebAppList) ProtoMessage() {}

func (x *WebAppList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebAppList.ProtoReflect.Descriptor instead.
func (*WebAppList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{31}
}

func (x *WebAppList) GetWebAppInfo() []*WebApp {
	if x != nil {
		return x.WebAppInfo
	}
	return nil
}

// 网卡信息
type EtherNetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EthName        string   `protobuf:"bytes,1,opt,name=eth_name,json=ethName,proto3" json:"eth_name,omitempty"`                        // 网卡名称
	EthMac         string   `protobuf:"bytes,2,opt,name=eth_mac,json=ethMac,proto3" json:"eth_mac,omitempty"`                           // mac地址
	EthIpv4List    []string `protobuf:"bytes,3,rep,name=eth_ipv4_list,json=ethIpv4List,proto3" json:"eth_ipv4_list,omitempty"`          // ethIpv4
	EthIpv6List    []string `protobuf:"bytes,4,rep,name=eth_ipv6_list,json=ethIpv6List,proto3" json:"eth_ipv6_list,omitempty"`          // ethIpv6
	EthGatewayList []string `protobuf:"bytes,5,rep,name=eth_gateway_list,json=ethGatewayList,proto3" json:"eth_gateway_list,omitempty"` // 网关地址
	DnsList        []string `protobuf:"bytes,6,rep,name=dns_list,json=dnsList,proto3" json:"dns_list,omitempty"`                        // DNS
	EthLinkState   uint32   `protobuf:"varint,7,opt,name=eth_link_state,json=ethLinkState,proto3" json:"eth_link_state,omitempty"`      // 网卡连接状态 0 未连接 1 连接
	EthMaskList    []string `protobuf:"bytes,8,rep,name=eth_mask_list,json=ethMaskList,proto3" json:"eth_mask_list,omitempty"`          // 子网掩码
	EthSpeed       string   `protobuf:"bytes,9,opt,name=eth_speed,json=ethSpeed,proto3" json:"eth_speed,omitempty"`                     // 网卡速度
	EthBcastList   []string `protobuf:"bytes,10,rep,name=eth_bcast_list,json=ethBcastList,proto3" json:"eth_bcast_list,omitempty"`      // 广播地址
	// 0125新增 网卡流量信息
	UpFlowBytes   uint64 `protobuf:"varint,11,opt,name=up_flow_bytes,json=upFlowBytes,proto3" json:"up_flow_bytes,omitempty"`       // 上行流量
	DownFlowBytes uint64 `protobuf:"varint,12,opt,name=down_flow_bytes,json=downFlowBytes,proto3" json:"down_flow_bytes,omitempty"` // 下行流量
	FlowTime      int64  `protobuf:"varint,13,opt,name=flow_time,json=flowTime,proto3" json:"flow_time,omitempty"`                  // 采集时间（不是真正的上报时间)
}

func (x *EtherNetInfo) Reset() {
	*x = EtherNetInfo{}
	mi := &file_agent_assets_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EtherNetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EtherNetInfo) ProtoMessage() {}

func (x *EtherNetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EtherNetInfo.ProtoReflect.Descriptor instead.
func (*EtherNetInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{32}
}

func (x *EtherNetInfo) GetEthName() string {
	if x != nil {
		return x.EthName
	}
	return ""
}

func (x *EtherNetInfo) GetEthMac() string {
	if x != nil {
		return x.EthMac
	}
	return ""
}

func (x *EtherNetInfo) GetEthIpv4List() []string {
	if x != nil {
		return x.EthIpv4List
	}
	return nil
}

func (x *EtherNetInfo) GetEthIpv6List() []string {
	if x != nil {
		return x.EthIpv6List
	}
	return nil
}

func (x *EtherNetInfo) GetEthGatewayList() []string {
	if x != nil {
		return x.EthGatewayList
	}
	return nil
}

func (x *EtherNetInfo) GetDnsList() []string {
	if x != nil {
		return x.DnsList
	}
	return nil
}

func (x *EtherNetInfo) GetEthLinkState() uint32 {
	if x != nil {
		return x.EthLinkState
	}
	return 0
}

func (x *EtherNetInfo) GetEthMaskList() []string {
	if x != nil {
		return x.EthMaskList
	}
	return nil
}

func (x *EtherNetInfo) GetEthSpeed() string {
	if x != nil {
		return x.EthSpeed
	}
	return ""
}

func (x *EtherNetInfo) GetEthBcastList() []string {
	if x != nil {
		return x.EthBcastList
	}
	return nil
}

func (x *EtherNetInfo) GetUpFlowBytes() uint64 {
	if x != nil {
		return x.UpFlowBytes
	}
	return 0
}

func (x *EtherNetInfo) GetDownFlowBytes() uint64 {
	if x != nil {
		return x.DownFlowBytes
	}
	return 0
}

func (x *EtherNetInfo) GetFlowTime() int64 {
	if x != nil {
		return x.FlowTime
	}
	return 0
}

// Npm包信息
type NpmPackageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`       //npm名称
	Version []byte `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"` //npm包版本号
	Path    []byte `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`       //npm包所在绝对路径
	Scope   []byte `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`     //npm包所在作用域范围
	Pid     []byte `protobuf:"bytes,5,opt,name=pid,proto3" json:"pid,omitempty"`         //npm包应用pid
	Command []byte `protobuf:"bytes,6,opt,name=command,proto3" json:"command,omitempty"` //npm包应用操作命令
}

func (x *NpmPackageInfo) Reset() {
	*x = NpmPackageInfo{}
	mi := &file_agent_assets_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NpmPackageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpmPackageInfo) ProtoMessage() {}

func (x *NpmPackageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpmPackageInfo.ProtoReflect.Descriptor instead.
func (*NpmPackageInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{33}
}

func (x *NpmPackageInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *NpmPackageInfo) GetVersion() []byte {
	if x != nil {
		return x.Version
	}
	return nil
}

func (x *NpmPackageInfo) GetPath() []byte {
	if x != nil {
		return x.Path
	}
	return nil
}

func (x *NpmPackageInfo) GetScope() []byte {
	if x != nil {
		return x.Scope
	}
	return nil
}

func (x *NpmPackageInfo) GetPid() []byte {
	if x != nil {
		return x.Pid
	}
	return nil
}

func (x *NpmPackageInfo) GetCommand() []byte {
	if x != nil {
		return x.Command
	}
	return nil
}

// Npm包信息列表
type NpmPackageInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NpmPackageInfo []*NpmPackageInfo `protobuf:"bytes,1,rep,name=npm_package_info,json=npmPackageInfo,proto3" json:"npm_package_info,omitempty"`
}

func (x *NpmPackageInfoList) Reset() {
	*x = NpmPackageInfoList{}
	mi := &file_agent_assets_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NpmPackageInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NpmPackageInfoList) ProtoMessage() {}

func (x *NpmPackageInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NpmPackageInfoList.ProtoReflect.Descriptor instead.
func (*NpmPackageInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{34}
}

func (x *NpmPackageInfoList) GetNpmPackageInfo() []*NpmPackageInfo {
	if x != nil {
		return x.NpmPackageInfo
	}
	return nil
}

// 计划任务信息列表
type JobTaskInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	JobTaskInfo []*JobTaskInfo `protobuf:"bytes,1,rep,name=job_task_info,json=jobTaskInfo,proto3" json:"job_task_info,omitempty"`
}

func (x *JobTaskInfoList) Reset() {
	*x = JobTaskInfoList{}
	mi := &file_agent_assets_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobTaskInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTaskInfoList) ProtoMessage() {}

func (x *JobTaskInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTaskInfoList.ProtoReflect.Descriptor instead.
func (*JobTaskInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{35}
}

func (x *JobTaskInfoList) GetJobTaskInfo() []*JobTaskInfo {
	if x != nil {
		return x.JobTaskInfo
	}
	return nil
}

// 计划任务信息
type JobTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                          // 计划任务名
	Type     []byte `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                          // 任务类型
	IsEnable bool   `protobuf:"varint,3,opt,name=is_enable,json=isEnable,proto3" json:"is_enable,omitempty"` // 启用状态 启用/未启用
	ExecTime []byte `protobuf:"bytes,4,opt,name=exec_time,json=execTime,proto3" json:"exec_time,omitempty"`  // 执行时间
	ExecUser []byte `protobuf:"bytes,5,opt,name=exec_user,json=execUser,proto3" json:"exec_user,omitempty"`  // 执行用户
	EtcPath  []byte `protobuf:"bytes,6,opt,name=etc_path,json=etcPath,proto3" json:"etc_path,omitempty"`     // 配置文件路径
}

func (x *JobTaskInfo) Reset() {
	*x = JobTaskInfo{}
	mi := &file_agent_assets_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobTaskInfo) ProtoMessage() {}

func (x *JobTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobTaskInfo.ProtoReflect.Descriptor instead.
func (*JobTaskInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{36}
}

func (x *JobTaskInfo) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *JobTaskInfo) GetType() []byte {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *JobTaskInfo) GetIsEnable() bool {
	if x != nil {
		return x.IsEnable
	}
	return false
}

func (x *JobTaskInfo) GetExecTime() []byte {
	if x != nil {
		return x.ExecTime
	}
	return nil
}

func (x *JobTaskInfo) GetExecUser() []byte {
	if x != nil {
		return x.ExecUser
	}
	return nil
}

func (x *JobTaskInfo) GetEtcPath() []byte {
	if x != nil {
		return x.EtcPath
	}
	return nil
}

// 应用弱口令
type AppWeakPwdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppName          []byte `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                                // 应用名
	UserName         []byte `protobuf:"bytes,2,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`                             // 账户名
	WeakPwdType      int32  `protobuf:"varint,3,opt,name=weak_pwd_type,json=weakPwdType,proto3" json:"weak_pwd_type,omitempty"`                 // 1 正则 2 字典 3 内置
	WeakPwdScanTime  int64  `protobuf:"varint,4,opt,name=weak_pwd_scan_time,json=weakPwdScanTime,proto3" json:"weak_pwd_scan_time,omitempty"`   // 应用弱密码扫描时间
	WeakPwdRegexRule []byte `protobuf:"bytes,5,opt,name=weak_pwd_regex_rule,json=weakPwdRegexRule,proto3" json:"weak_pwd_regex_rule,omitempty"` // 正则内容
}

func (x *AppWeakPwdInfo) Reset() {
	*x = AppWeakPwdInfo{}
	mi := &file_agent_assets_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppWeakPwdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppWeakPwdInfo) ProtoMessage() {}

func (x *AppWeakPwdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppWeakPwdInfo.ProtoReflect.Descriptor instead.
func (*AppWeakPwdInfo) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{37}
}

func (x *AppWeakPwdInfo) GetAppName() []byte {
	if x != nil {
		return x.AppName
	}
	return nil
}

func (x *AppWeakPwdInfo) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *AppWeakPwdInfo) GetWeakPwdType() int32 {
	if x != nil {
		return x.WeakPwdType
	}
	return 0
}

func (x *AppWeakPwdInfo) GetWeakPwdScanTime() int64 {
	if x != nil {
		return x.WeakPwdScanTime
	}
	return 0
}

func (x *AppWeakPwdInfo) GetWeakPwdRegexRule() []byte {
	if x != nil {
		return x.WeakPwdRegexRule
	}
	return nil
}

type AppWeakPwdInfoList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AppWeakPwdInfoList []*AppWeakPwdInfo `protobuf:"bytes,1,rep,name=app_weak_pwd_info_list,json=appWeakPwdInfoList,proto3" json:"app_weak_pwd_info_list,omitempty"` // 应用弱密码信息列表
}

func (x *AppWeakPwdInfoList) Reset() {
	*x = AppWeakPwdInfoList{}
	mi := &file_agent_assets_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppWeakPwdInfoList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppWeakPwdInfoList) ProtoMessage() {}

func (x *AppWeakPwdInfoList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_assets_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppWeakPwdInfoList.ProtoReflect.Descriptor instead.
func (*AppWeakPwdInfoList) Descriptor() ([]byte, []int) {
	return file_agent_assets_proto_rawDescGZIP(), []int{38}
}

func (x *AppWeakPwdInfoList) GetAppWeakPwdInfoList() []*AppWeakPwdInfo {
	if x != nil {
		return x.AppWeakPwdInfoList
	}
	return nil
}

var File_agent_assets_proto protoreflect.FileDescriptor

var file_agent_assets_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xcd, 0x0c, 0x0a, 0x14, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x65, 0x6e, 0x76, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x45, 0x6e, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x65, 0x6e, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x40, 0x0a, 0x0e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0e, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x0f, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x62, 0x4d, 0x69, 0x64, 0x64,
	0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x01, 0x52, 0x0e, 0x6d, 0x69,
	0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x45, 0x0a, 0x12,
	0x77, 0x65, 0x62, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x57, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x02, 0x52, 0x0f, 0x77, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x62, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x48, 0x03, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x50, 0x0a, 0x15, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x04, 0x52, 0x13, 0x68,
	0x6f, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x44, 0x0a, 0x11, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x05, 0x52, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x57, 0x0a, 0x18, 0x70, 0x79, 0x74, 0x68,
	0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x06, 0x52, 0x15, 0x70, 0x79, 0x74, 0x68,
	0x6f, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x35, 0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x07, 0x52, 0x0a, 0x77, 0x65,
	0x62, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x0d, 0x6e, 0x70, 0x6d, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x70, 0x6d, 0x50, 0x61, 0x63, 0x6b, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x08, 0x52, 0x0b, 0x6e, 0x70,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0d, 0x65, 0x74, 0x68,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x45, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x65,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x09, 0x52, 0x0b, 0x65, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x12, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x75, 0x74, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x0a, 0x52, 0x10,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x74, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x45, 0x0a, 0x12, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x0b, 0x52, 0x0f, 0x6a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x5f, 0x77,
	0x65, 0x61, 0x6b, 0x5f, 0x70, 0x77, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x70, 0x70, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x48, 0x0c, 0x52, 0x12, 0x61, 0x70, 0x70, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x68, 0x61, 0x73, 0x5f,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x14, 0x0a, 0x12, 0x68, 0x61,
	0x73, 0x5f, 0x77, 0x65, 0x62, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65,
	0x42, 0x13, 0x0a, 0x11, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x69, 0x74, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0f, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x65, 0x62,
	0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x68, 0x61, 0x73, 0x5f, 0x68, 0x6f,
	0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x12,
	0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x42, 0x19, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e,
	0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0d, 0x0a,
	0x0b, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x65, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x42, 0x0e, 0x0a, 0x0c,
	0x68, 0x61, 0x73, 0x5f, 0x6e, 0x70, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x0e, 0x0a, 0x0c,
	0x68, 0x61, 0x73, 0x5f, 0x65, 0x74, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11,
	0x68, 0x61, 0x73, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x42, 0x13, 0x0a, 0x11, 0x68, 0x61, 0x73, 0x5f, 0x6a, 0x6f, 0x62, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x17, 0x0a, 0x15, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x70, 0x77, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22,
	0xe8, 0x0e, 0x0a, 0x0f, 0x48, 0x6f, 0x73, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x06, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x63,
	0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x49,
	0x45, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x49, 0x45, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x50, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x50, 0x12, 0x10, 0x0a, 0x03, 0x4d, 0x41, 0x43,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4d, 0x41, 0x43, 0x12, 0x16, 0x0a, 0x06, 0x49,
	0x50, 0x4d, 0x41, 0x53, 0x4b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x49, 0x50, 0x4d,
	0x41, 0x53, 0x4b, 0x12, 0x18, 0x0a, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07,
	0x6d, 0x65, 0x6d, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x43, 0x50, 0x50, 0x6f,
	0x72, 0x74, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x54, 0x43, 0x50, 0x50, 0x6f,
	0x72, 0x74, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x44, 0x50, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x55, 0x44, 0x50, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x12,
	0x26, 0x0a, 0x0e, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x55, 0x74,
	0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x02, 0x52,
	0x0e, 0x6d, 0x65, 0x6d, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x28, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x74,
	0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x56, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x56, 0x65, 0x72, 0x12,
	0x24, 0x0a, 0x0d, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x63, 0x68, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x70, 0x75,
	0x43, 0x6f, 0x72, 0x65, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x70, 0x75,
	0x43, 0x6f, 0x72, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6a, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6a, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x32, 0x0a, 0x14, 0x77, 0x65, 0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x77, 0x65, 0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4c,
	0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x4c, 0x69, 0x62,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x65, 0x6e, 0x76, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x6e, 0x76, 0x4c, 0x69, 0x62,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x4a, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4a, 0x64, 0x6b, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x6f, 0x6e, 0x67, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x75,
	0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x10, 0x6c,
	0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x72, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x6c, 0x79, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x1e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x15, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x6c, 0x79,
	0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x11, 0x68, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x68, 0x61, 0x73, 0x68, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x13, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a,
	0x0e, 0x6e, 0x67, 0x61, 0x76, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x67, 0x61, 0x76, 0x4c, 0x69, 0x62, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x22, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x10, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x2a, 0x0a, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x11, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x0b, 0x65,
	0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x65, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x28,
	0x0a, 0x0f, 0x72, 0x61, 0x73, 0x70, 0x52, 0x75, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x61, 0x73, 0x70, 0x52, 0x75, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12, 0x62, 0x61, 0x73, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x28,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x62, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4c, 0x69,
	0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x62, 0x61, 0x73, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x53, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x61,
	0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x53, 0x12, 0x34, 0x0a, 0x15, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3e,
	0x0a, 0x1a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x1a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24,
	0x0a, 0x0d, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x18,
	0x2c, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x6f, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x12, 0x26, 0x0a, 0x0e, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x55, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x19,
	0x66, 0x69, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x4c,
	0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x2e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x19, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x6c, 0x6f,
	0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x2f, 0x20, 0x03, 0x28,
	0x08, 0x52, 0x0d, 0x6c, 0x6f, 0x67, 0x6f, 0x6e, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6c, 0x61, 0x67,
	0x12, 0x26, 0x0a, 0x0e, 0x77, 0x69, 0x6e, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f,
	0x72, 0x74, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x77, 0x69, 0x6e, 0x4f, 0x53, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x78,
	0x79, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x78, 0x79, 0x49, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x22, 0x8a, 0x01, 0x0a, 0x0c, 0x45,
	0x74, 0x68, 0x65, 0x72, 0x4e, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2a, 0x0a, 0x10, 0x68,
	0x6f, 0x73, 0x74, 0x49, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x6f, 0x73, 0x74, 0x5f,
	0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f, 0x73, 0x74, 0x49, 0x70,
	0x12, 0x35, 0x0a, 0x0b, 0x65, 0x74, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x74,
	0x68, 0x65, 0x72, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x65, 0x74, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xac, 0x03, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x27, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x63, 0x70, 0x75, 0x5f,
	0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x0e, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x65, 0x6d, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6d, 0x65, 0x6d, 0x55,
	0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69,
	0x73, 0x6b, 0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x6b, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x70, 0x75, 0x5f, 0x61, 0x6c, 0x61,
	0x72, 0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x63, 0x70, 0x75, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x23, 0x0a, 0x0d, 0x63, 0x70, 0x75, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x63, 0x70, 0x75, 0x54, 0x68, 0x72, 0x65, 0x73,
	0x68, 0x6f, 0x6c, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x6d, 0x65, 0x6d, 0x5f, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x6d, 0x65, 0x6d, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x65, 0x6d, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x6d, 0x65, 0x6d, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x6c, 0x61, 0x72,
	0x6d, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x64, 0x69, 0x73, 0x6b, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c,
	0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x6b, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x22, 0x48, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x41, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0a, 0x61,
	0x6c, 0x61, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x41, 0x6c, 0x61, 0x72, 0x6d, 0x52, 0x09, 0x61, 0x6c, 0x61, 0x72, 0x6d, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0xa0, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x41, 0x6c, 0x61,
	0x72, 0x6d, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x6c, 0x61, 0x72, 0x6d,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x6c, 0x61,
	0x72, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0b, 0x75, 0x74, 0x69,
	0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68,
	0x6f, 0x6c, 0x64, 0x22, 0xf2, 0x05, 0x0a, 0x12, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x18,
	0x0a, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1e,
	0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72,
	0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x50, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x50, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x12, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x12, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x77, 0x65,
	0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x68, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x68, 0x6f, 0x6d, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x43, 0x6c,
	0x6f, 0x6e, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x43, 0x6c, 0x6f,
	0x6e, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x73, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x12, 0x2c,
	0x0a, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x68, 0x65, 0x6c, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x68, 0x65,
	0x6c, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69,
	0x73, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77, 0x64, 0x52, 0x65, 0x67, 0x65, 0x78, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77, 0x64, 0x52, 0x65, 0x67,
	0x65, 0x78, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x42, 0x0a, 0x0e, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77, 0x64, 0x68,
	0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x16, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x52, 0x0e, 0x77, 0x65, 0x61, 0x6b, 0x70, 0x77,
	0x64, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x46, 0x0a, 0x13, 0x77, 0x65, 0x61, 0x6b,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x68, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x22, 0x3a, 0x0a, 0x0e, 0x45, 0x6e, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xb7, 0x02, 0x0a,
	0x11, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x69,
	0x63, 0x65, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6c, 0x69, 0x63,
	0x65, 0x6e, 0x73, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x22, 0xd8, 0x02, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61,
	0x6e, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x64, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x64,
	0x6c, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x64, 0x6c, 0x6c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x44, 0x6c, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x44, 0x6c,
	0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xe7, 0x03, 0x0a, 0x0f, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x6d,
	0x65, 0x6d, 0x52, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x65, 0x6d,
	0x52, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x56, 0x73, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x56, 0x73, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6f, 0x52, 0x65, 0x61, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x69, 0x6f, 0x52,
	0x65, 0x61, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6f, 0x57, 0x72, 0x69, 0x74, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x69, 0x6f, 0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x63, 0x70, 0x75, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x44,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x44, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x55, 0x74, 0x69, 0x6c,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0e, 0x6d,
	0x65, 0x6d, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a,
	0x05, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x05, 0x50, 0x6f, 0x72, 0x74, 0x73, 0x22, 0x41, 0x0a, 0x0f, 0x50,
	0x6f, 0x72, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0xb1,
	0x01, 0x0a, 0x0a, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x61, 0x73, 0x65, 0x44, 0x4e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x61, 0x73, 0x65, 0x44, 0x4e, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x64, 0x6e, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x42, 0x49,
	0x4f, 0x53, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x42, 0x49, 0x4f,
	0x53, 0x12, 0x3f, 0x0a, 0x10, 0x6e, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x10, 0x6e, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x36, 0x0a, 0x0c, 0x4e, 0x65, 0x74, 0x53, 0x68, 0x61, 0x72, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xad, 0x01, 0x0a, 0x07, 0x4a,
	0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x45, 0x78, 0x65, 0x63, 0x12, 0x18, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x75, 0x73, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x62, 0x75, 0x73, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x70, 0x69, 0x64, 0x22, 0x50, 0x0a, 0x11, 0x57, 0x65,
	0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x3b, 0x0a, 0x0e, 0x77, 0x65, 0x62, 0x5f, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x57, 0x65, 0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x52, 0x0d, 0x77,
	0x65, 0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x22, 0x8d, 0x02, 0x0a,
	0x0d, 0x57, 0x65, 0x62, 0x4d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x77, 0x61, 0x72, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x77, 0x65, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x77, 0x65, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x62,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x77, 0x65, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x69,
	0x6e, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x62, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x11, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x32, 0x0a, 0x0d, 0x6a, 0x61, 0x72, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x6a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x9c, 0x01, 0x0a,
	0x0e, 0x57, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x21, 0x0a, 0x0c, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x61, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x06, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x09, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0xbe, 0x02, 0x0a, 0x0b,
	0x57, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x75,
	0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x77, 0x61, 0x72, 0x5f, 0x64, 0x69, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x06, 0x77, 0x61, 0x72, 0x44, 0x69, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x6f, 0x6f,
	0x74, 0x5f, 0x64, 0x69, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x6f, 0x6f,
	0x74, 0x44, 0x69, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x64, 0x69, 0x72,
	0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x10, 0x72, 0x6f, 0x6f, 0x74, 0x44, 0x69, 0x72, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x73, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x39, 0x0a, 0x0d, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x61, 0x70,
	0x70, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x57, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x77, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x41, 0x70, 0x70, 0x73, 0x22, 0x49, 0x0a, 0x0f,
	0x57, 0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x36, 0x0a, 0x0d, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x65, 0x62, 0x53, 0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x77, 0x65, 0x62, 0x53,
	0x69, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe3, 0x01, 0x0a, 0x08, 0x57, 0x65, 0x62, 0x46,
	0x72, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x62, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x77, 0x65,
	0x62, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x65, 0x62, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x77, 0x65, 0x62, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67,
	0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x66, 0x72, 0x61, 0x6d,
	0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x32, 0x0a, 0x0d, 0x6a, 0x61, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0b, 0x6a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x45, 0x0a,
	0x0c, 0x57, 0x65, 0x62, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x35, 0x0a,
	0x0e, 0x77, 0x65, 0x62, 0x5f, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65,
	0x62, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x77, 0x65, 0x62, 0x46, 0x72, 0x61, 0x6d, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x90, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0xaa, 0x02, 0x0a, 0x13, 0x48, 0x6f, 0x73, 0x74,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x63, 0x4e, 0x75, 0x6d, 0x12, 0x39, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x50, 0x72, 0x6f, 0x63, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x32, 0x0a, 0x0d, 0x6a, 0x61, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73,
	0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x6a, 0x61, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x55, 0x0a, 0x13, 0x48, 0x6f, 0x73, 0x74, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x68,
	0x6f, 0x73, 0x74, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x6f, 0x73, 0x74, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b,
	0x68, 0x6f, 0x73, 0x74, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x91, 0x02, 0x0a, 0x0b,
	0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x72, 0x63, 0x68,
	0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c,
	0x61, 0x72, 0x63, 0x68, 0x69, 0x74, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1c, 0x0a, 0x0a,
	0x73, 0x69, 0x7a, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6b, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x73, 0x69, 0x7a, 0x65, 0x49, 0x6e, 0x4b, 0x62, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x48, 0x0a, 0x0f, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x7a, 0x0a, 0x11, 0x50, 0x79, 0x74,
	0x68, 0x6f, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x64, 0x69, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x61, 0x0a, 0x15, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48,
	0x0a, 0x13, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xa5, 0x01, 0x0a, 0x06, 0x57, 0x65, 0x62,
	0x41, 0x70, 0x70, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x61, 0x70, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x70, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x77, 0x65, 0x62, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x09, 0x77, 0x65, 0x62, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x77, 0x65, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x77, 0x65, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x72, 0x61,
	0x6d, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0d, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x22, 0x3d, 0x0a, 0x0a, 0x57, 0x65, 0x62, 0x41, 0x70, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x65, 0x62,
	0x41, 0x70, 0x70, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0xc5, 0x03, 0x0a, 0x0c, 0x65, 0x74, 0x68, 0x65, 0x72, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x74, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x74, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x65,
	0x74, 0x68, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x74,
	0x68, 0x4d, 0x61, 0x63, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x74, 0x68, 0x5f, 0x69, 0x70, 0x76, 0x34,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x74, 0x68,
	0x49, 0x70, 0x76, 0x34, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x74, 0x68, 0x5f,
	0x69, 0x70, 0x76, 0x36, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x65, 0x74, 0x68, 0x49, 0x70, 0x76, 0x36, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10,
	0x65, 0x74, 0x68, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x74, 0x68, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x6e, 0x73, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x64, 0x6e, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x65, 0x74, 0x68, 0x4c, 0x69,
	0x6e, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x65, 0x74, 0x68, 0x5f, 0x6d,
	0x61, 0x73, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x65, 0x74, 0x68, 0x4d, 0x61, 0x73, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x65,
	0x74, 0x68, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x65, 0x74, 0x68, 0x53, 0x70, 0x65, 0x65, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x65, 0x74, 0x68, 0x5f,
	0x62, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0c, 0x65, 0x74, 0x68, 0x42, 0x63, 0x61, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x0d, 0x75, 0x70, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x62, 0x79, 0x74, 0x65, 0x73, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x75, 0x70, 0x46, 0x6c, 0x6f, 0x77, 0x42, 0x79, 0x74,
	0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x62, 0x79, 0x74, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x64, 0x6f, 0x77,
	0x6e, 0x46, 0x6c, 0x6f, 0x77, 0x42, 0x79, 0x74, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66,
	0x6c, 0x6f, 0x77, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x94, 0x01, 0x0a, 0x0e, 0x4e, 0x70, 0x6d, 0x50,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x73, 0x63, 0x6f,
	0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x22, 0x55,
	0x0a, 0x12, 0x4e, 0x70, 0x6d, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x10, 0x6e, 0x70, 0x6d, 0x5f, 0x70, 0x61, 0x63, 0x6b,
	0x61, 0x67, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x70, 0x6d, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x6e, 0x70, 0x6d, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x49, 0x0a, 0x0f, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0d, 0x6a, 0x6f, 0x62, 0x5f,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x6a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0xa7, 0x01, 0x0a, 0x0b, 0x4a, 0x6f, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x65, 0x63, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x65, 0x78, 0x65, 0x63, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x65, 0x74, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x07, 0x65, 0x74, 0x63, 0x50, 0x61, 0x74, 0x68, 0x22, 0xc8, 0x01, 0x0a, 0x0e, 0x41,
	0x70, 0x70, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x70, 0x77,
	0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x77, 0x65,
	0x61, 0x6b, 0x50, 0x77, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x77, 0x65, 0x61,
	0x6b, 0x5f, 0x70, 0x77, 0x64, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x53, 0x63,
	0x61, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x13, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x70,
	0x77, 0x64, 0x5f, 0x72, 0x65, 0x67, 0x65, 0x78, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x10, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x52, 0x65, 0x67, 0x65,
	0x78, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x5f, 0x0a, 0x12, 0x41, 0x70, 0x70, 0x57, 0x65, 0x61, 0x6b,
	0x50, 0x77, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x16, 0x61,
	0x70, 0x70, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x5f, 0x70, 0x77, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x12, 0x61, 0x70, 0x70, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x77, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_assets_proto_rawDescOnce sync.Once
	file_agent_assets_proto_rawDescData = file_agent_assets_proto_rawDesc
)

func file_agent_assets_proto_rawDescGZIP() []byte {
	file_agent_assets_proto_rawDescOnce.Do(func() {
		file_agent_assets_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_assets_proto_rawDescData)
	})
	return file_agent_assets_proto_rawDescData
}

var file_agent_assets_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_agent_assets_proto_goTypes = []any{
	(*MemProtectAssetsInfo)(nil),  // 0: agent.MemProtectAssetsInfo
	(*HostInformation)(nil),       // 1: agent.HostInformation
	(*EtherNetList)(nil),          // 2: agent.EtherNetList
	(*ResourceUtilization)(nil),   // 3: agent.ResourceUtilization
	(*ResourceAlarmList)(nil),     // 4: agent.ResourceAlarmList
	(*ResourceAlarm)(nil),         // 5: agent.ResourceAlarm
	(*AccountInformation)(nil),    // 6: agent.AccountInformation
	(*WeakPasswordhistory)(nil),   // 7: agent.weakPasswordhistory
	(*EnvInformation)(nil),        // 8: agent.EnvInformation
	(*KernelInformation)(nil),     // 9: agent.KernelInformation
	(*ServiceInformation)(nil),    // 10: agent.ServiceInformation
	(*ProcInformation)(nil),       // 11: agent.ProcInformation
	(*PortInformation)(nil),       // 12: agent.PortInformation
	(*DomainInfo)(nil),            // 13: agent.DomainInfo
	(*NetShareInfo)(nil),          // 14: agent.NetShareInfo
	(*JarInfo)(nil),               // 15: agent.JarInfo
	(*WebMiddlewareList)(nil),     // 16: agent.WebMiddlewareList
	(*WebMiddleware)(nil),         // 17: agent.WebMiddleware
	(*WebSiteAppInfo)(nil),        // 18: agent.WebSiteAppInfo
	(*WebSiteInfo)(nil),           // 19: agent.WebSiteInfo
	(*WebSiteInfoList)(nil),       // 20: agent.WebSiteInfoList
	(*WebFrame)(nil),              // 21: agent.WebFrame
	(*WebFrameList)(nil),          // 22: agent.WebFrameList
	(*RelateProcInfo)(nil),        // 23: agent.RelateProcInfo
	(*HostApplicationInfo)(nil),   // 24: agent.HostApplicationInfo
	(*HostApplicationList)(nil),   // 25: agent.HostApplicationList
	(*PackageInfo)(nil),           // 26: agent.PackageInfo
	(*PackageInfoList)(nil),       // 27: agent.PackageInfoList
	(*PythonPackageInfo)(nil),     // 28: agent.PythonPackageInfo
	(*PythonPackageInfoList)(nil), // 29: agent.PythonPackageInfoList
	(*WebApp)(nil),                // 30: agent.WebApp
	(*WebAppList)(nil),            // 31: agent.WebAppList
	(*EtherNetInfo)(nil),          // 32: agent.etherNetInfo
	(*NpmPackageInfo)(nil),        // 33: agent.NpmPackageInfo
	(*NpmPackageInfoList)(nil),    // 34: agent.NpmPackageInfoList
	(*JobTaskInfoList)(nil),       // 35: agent.JobTaskInfoList
	(*JobTaskInfo)(nil),           // 36: agent.JobTaskInfo
	(*AppWeakPwdInfo)(nil),        // 37: agent.AppWeakPwdInfo
	(*AppWeakPwdInfoList)(nil),    // 38: agent.AppWeakPwdInfoList
	(*ClientID)(nil),              // 39: agent.ClientID
}
var file_agent_assets_proto_depIdxs = []int32{
	39, // 0: agent.MemProtectAssetsInfo.baseInfo:type_name -> agent.ClientID
	1,  // 1: agent.MemProtectAssetsInfo.hostInfoList:type_name -> agent.HostInformation
	6,  // 2: agent.MemProtectAssetsInfo.accountInfoList:type_name -> agent.AccountInformation
	8,  // 3: agent.MemProtectAssetsInfo.envInfoList:type_name -> agent.EnvInformation
	9,  // 4: agent.MemProtectAssetsInfo.kernelInfoList:type_name -> agent.KernelInformation
	10, // 5: agent.MemProtectAssetsInfo.serviceInfoList:type_name -> agent.ServiceInformation
	11, // 6: agent.MemProtectAssetsInfo.procInfoList:type_name -> agent.ProcInformation
	13, // 7: agent.MemProtectAssetsInfo.domainInfo:type_name -> agent.DomainInfo
	16, // 8: agent.MemProtectAssetsInfo.middleware_list:type_name -> agent.WebMiddlewareList
	20, // 9: agent.MemProtectAssetsInfo.web_site_info_list:type_name -> agent.WebSiteInfoList
	22, // 10: agent.MemProtectAssetsInfo.web_frame_list:type_name -> agent.WebFrameList
	25, // 11: agent.MemProtectAssetsInfo.host_application_list:type_name -> agent.HostApplicationList
	27, // 12: agent.MemProtectAssetsInfo.package_info_list:type_name -> agent.PackageInfoList
	29, // 13: agent.MemProtectAssetsInfo.python_package_info_list:type_name -> agent.PythonPackageInfoList
	31, // 14: agent.MemProtectAssetsInfo.web_app_list:type_name -> agent.WebAppList
	34, // 15: agent.MemProtectAssetsInfo.npm_info_list:type_name -> agent.NpmPackageInfoList
	2,  // 16: agent.MemProtectAssetsInfo.eth_info_list:type_name -> agent.EtherNetList
	3,  // 17: agent.MemProtectAssetsInfo.resource_util_info:type_name -> agent.ResourceUtilization
	35, // 18: agent.MemProtectAssetsInfo.job_task_info_list:type_name -> agent.JobTaskInfoList
	38, // 19: agent.MemProtectAssetsInfo.app_weak_pwd_info_list:type_name -> agent.AppWeakPwdInfoList
	32, // 20: agent.HostInformation.ethInfoList:type_name -> agent.etherNetInfo
	32, // 21: agent.EtherNetList.ethInfoList:type_name -> agent.etherNetInfo
	5,  // 22: agent.ResourceAlarmList.alarm_list:type_name -> agent.ResourceAlarm
	7,  // 23: agent.AccountInformation.weakpwdhistory:type_name -> agent.weakPasswordhistory
	12, // 24: agent.ProcInformation.Ports:type_name -> agent.PortInformation
	14, // 25: agent.DomainInfo.netShareInfoList:type_name -> agent.NetShareInfo
	17, // 26: agent.WebMiddlewareList.web_middleware:type_name -> agent.WebMiddleware
	15, // 27: agent.WebMiddleware.jar_info_list:type_name -> agent.JarInfo
	18, // 28: agent.WebSiteInfo.web_site_apps:type_name -> agent.WebSiteAppInfo
	19, // 29: agent.WebSiteInfoList.web_site_info:type_name -> agent.WebSiteInfo
	15, // 30: agent.WebFrame.jar_info_list:type_name -> agent.JarInfo
	21, // 31: agent.WebFrameList.web_frame_info:type_name -> agent.WebFrame
	23, // 32: agent.HostApplicationInfo.app_proc_list:type_name -> agent.RelateProcInfo
	15, // 33: agent.HostApplicationInfo.jar_info_list:type_name -> agent.JarInfo
	24, // 34: agent.HostApplicationList.host_app_list:type_name -> agent.HostApplicationInfo
	26, // 35: agent.PackageInfoList.package_info:type_name -> agent.PackageInfo
	28, // 36: agent.PythonPackageInfoList.python_package_info:type_name -> agent.PythonPackageInfo
	30, // 37: agent.WebAppList.web_app_info:type_name -> agent.WebApp
	33, // 38: agent.NpmPackageInfoList.npm_package_info:type_name -> agent.NpmPackageInfo
	36, // 39: agent.JobTaskInfoList.job_task_info:type_name -> agent.JobTaskInfo
	37, // 40: agent.AppWeakPwdInfoList.app_weak_pwd_info_list:type_name -> agent.AppWeakPwdInfo
	41, // [41:41] is the sub-list for method output_type
	41, // [41:41] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_agent_assets_proto_init() }
func file_agent_assets_proto_init() {
	if File_agent_assets_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_assets_proto_msgTypes[0].OneofWrappers = []any{
		(*MemProtectAssetsInfo_DomainInfo)(nil),
		(*MemProtectAssetsInfo_MiddlewareList)(nil),
		(*MemProtectAssetsInfo_WebSiteInfoList)(nil),
		(*MemProtectAssetsInfo_WebFrameList)(nil),
		(*MemProtectAssetsInfo_HostApplicationList)(nil),
		(*MemProtectAssetsInfo_PackageInfoList)(nil),
		(*MemProtectAssetsInfo_PythonPackageInfoList)(nil),
		(*MemProtectAssetsInfo_WebAppList)(nil),
		(*MemProtectAssetsInfo_NpmInfoList)(nil),
		(*MemProtectAssetsInfo_EthInfoList)(nil),
		(*MemProtectAssetsInfo_ResourceUtilInfo)(nil),
		(*MemProtectAssetsInfo_JobTaskInfoList)(nil),
		(*MemProtectAssetsInfo_AppWeakPwdInfoList)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_assets_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_assets_proto_goTypes,
		DependencyIndexes: file_agent_assets_proto_depIdxs,
		MessageInfos:      file_agent_assets_proto_msgTypes,
	}.Build()
	File_agent_assets_proto = out.File
	file_agent_assets_proto_rawDesc = nil
	file_agent_assets_proto_goTypes = nil
	file_agent_assets_proto_depIdxs = nil
}
