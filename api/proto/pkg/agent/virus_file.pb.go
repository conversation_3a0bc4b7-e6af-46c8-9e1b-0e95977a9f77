// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/virus_file.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 风险类型
type VirusType int32

const (
	VirusType_DETECT_UNKNOWN           VirusType = 0
	VirusType_DETECT_WSS_FILE          VirusType = 1  // 弃用(终端弃用)
	VirusType_DETECT_ANTI_FILE         VirusType = 2  // 黑hash
	VirusType_DETECT_OFFICE_FISHING    VirusType = 3  // 办公软件钓鱼
	VirusType_DETECT_EMAIL_FISHING     VirusType = 4  // 邮件钓鱼
	VirusType_DETECT_SIGN_BLACK_LIB    VirusType = 5  // 弃用
	VirusType_DETECT_DYN_LIB_HACK      VirusType = 6  // 弃用,dll劫持
	VirusType_DETECT_SL                VirusType = 7  // 0415弃用
	VirusType_DETECT_BOT_NET           VirusType = 8  // 僵尸网络
	VirusType_DETECT_TROJAN            VirusType = 9  // 木马
	VirusType_DETECT_SPYWARE           VirusType = 10 // 间谍软件
	VirusType_DETECT_REMOTE_CONTROL    VirusType = 11 // 远控
	VirusType_DETECT_BACKDOOR          VirusType = 12 // 后门软件
	VirusType_DETECT_DATA_INTERCEPTION VirusType = 13 // 数据窃取
	VirusType_DETECT_EXP_VULN          VirusType = 14 // 漏洞利用
	VirusType_DETECT_EXTORTION         VirusType = 15 // 勒索
	VirusType_DETECT_MINING_VIRUS      VirusType = 16 // 挖矿病毒
	VirusType_DETECT_WORM              VirusType = 17 // 蠕虫
	VirusType_DETECT_APT               VirusType = 18 // APT
	VirusType_DETECT_VIRUS             VirusType = 19 // 病毒
	VirusType_DETECT_HACK_TOOL         VirusType = 20 // 黑客工具
	VirusType_DETECT_USB_PHISHING      VirusType = 21 // USB钓鱼
)

// Enum value maps for VirusType.
var (
	VirusType_name = map[int32]string{
		0:  "DETECT_UNKNOWN",
		1:  "DETECT_WSS_FILE",
		2:  "DETECT_ANTI_FILE",
		3:  "DETECT_OFFICE_FISHING",
		4:  "DETECT_EMAIL_FISHING",
		5:  "DETECT_SIGN_BLACK_LIB",
		6:  "DETECT_DYN_LIB_HACK",
		7:  "DETECT_SL",
		8:  "DETECT_BOT_NET",
		9:  "DETECT_TROJAN",
		10: "DETECT_SPYWARE",
		11: "DETECT_REMOTE_CONTROL",
		12: "DETECT_BACKDOOR",
		13: "DETECT_DATA_INTERCEPTION",
		14: "DETECT_EXP_VULN",
		15: "DETECT_EXTORTION",
		16: "DETECT_MINING_VIRUS",
		17: "DETECT_WORM",
		18: "DETECT_APT",
		19: "DETECT_VIRUS",
		20: "DETECT_HACK_TOOL",
		21: "DETECT_USB_PHISHING",
	}
	VirusType_value = map[string]int32{
		"DETECT_UNKNOWN":           0,
		"DETECT_WSS_FILE":          1,
		"DETECT_ANTI_FILE":         2,
		"DETECT_OFFICE_FISHING":    3,
		"DETECT_EMAIL_FISHING":     4,
		"DETECT_SIGN_BLACK_LIB":    5,
		"DETECT_DYN_LIB_HACK":      6,
		"DETECT_SL":                7,
		"DETECT_BOT_NET":           8,
		"DETECT_TROJAN":            9,
		"DETECT_SPYWARE":           10,
		"DETECT_REMOTE_CONTROL":    11,
		"DETECT_BACKDOOR":          12,
		"DETECT_DATA_INTERCEPTION": 13,
		"DETECT_EXP_VULN":          14,
		"DETECT_EXTORTION":         15,
		"DETECT_MINING_VIRUS":      16,
		"DETECT_WORM":              17,
		"DETECT_APT":               18,
		"DETECT_VIRUS":             19,
		"DETECT_HACK_TOOL":         20,
		"DETECT_USB_PHISHING":      21,
	}
)

func (x VirusType) Enum() *VirusType {
	p := new(VirusType)
	*p = x
	return p
}

func (x VirusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VirusType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_virus_file_proto_enumTypes[0].Descriptor()
}

func (VirusType) Type() protoreflect.EnumType {
	return &file_agent_virus_file_proto_enumTypes[0]
}

func (x VirusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VirusType.Descriptor instead.
func (VirusType) EnumDescriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{0}
}

type MainType int32

const (
	MainType_MT_UNKNOWN     MainType = 0
	MainType_MT_LNK         MainType = 1
	MainType_MT_URL         MainType = 2
	MainType_MT_MACRO       MainType = 3
	MainType_MT_DDE         MainType = 4
	MainType_MT_OLE         MainType = 5
	MainType_MT_HTA         MainType = 6
	MainType_MT_CHM         MainType = 7
	MainType_MT_RLO         MainType = 8
	MainType_MT_RAR         MainType = 9
	MainType_MT_FILE_PATH   MainType = 10
	MainType_MT_PE          MainType = 11
	MainType_MT_VBE         MainType = 12
	MainType_MT_EML         MainType = 13
	MainType_MT_FILE_BINDLE MainType = 14
)

// Enum value maps for MainType.
var (
	MainType_name = map[int32]string{
		0:  "MT_UNKNOWN",
		1:  "MT_LNK",
		2:  "MT_URL",
		3:  "MT_MACRO",
		4:  "MT_DDE",
		5:  "MT_OLE",
		6:  "MT_HTA",
		7:  "MT_CHM",
		8:  "MT_RLO",
		9:  "MT_RAR",
		10: "MT_FILE_PATH",
		11: "MT_PE",
		12: "MT_VBE",
		13: "MT_EML",
		14: "MT_FILE_BINDLE",
	}
	MainType_value = map[string]int32{
		"MT_UNKNOWN":     0,
		"MT_LNK":         1,
		"MT_URL":         2,
		"MT_MACRO":       3,
		"MT_DDE":         4,
		"MT_OLE":         5,
		"MT_HTA":         6,
		"MT_CHM":         7,
		"MT_RLO":         8,
		"MT_RAR":         9,
		"MT_FILE_PATH":   10,
		"MT_PE":          11,
		"MT_VBE":         12,
		"MT_EML":         13,
		"MT_FILE_BINDLE": 14,
	}
)

func (x MainType) Enum() *MainType {
	p := new(MainType)
	*p = x
	return p
}

func (x MainType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MainType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_virus_file_proto_enumTypes[1].Descriptor()
}

func (MainType) Type() protoreflect.EnumType {
	return &file_agent_virus_file_proto_enumTypes[1]
}

func (x MainType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MainType.Descriptor instead.
func (MainType) EnumDescriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{1}
}

type SubType int32

const (
	SubType_SUBT_UNKNOWN SubType = 0
	// MT_LNK
	SubType_SUBT_LIK_BIG_SIZE   SubType = 1
	SubType_SUBT_ICON_REPLACE   SubType = 2
	SubType_SUBT_CMD_LINE_EXCEP SubType = 3
	// MT_FILE_PATH
	SubType_SUBT_DOUBLE_SUFFIX      SubType = 10
	SubType_SUBT_TOO_LONG_PATH      SubType = 11
	SubType_SUBT_TEMPTATION_CONTENT SubType = 12
)

// Enum value maps for SubType.
var (
	SubType_name = map[int32]string{
		0:  "SUBT_UNKNOWN",
		1:  "SUBT_LIK_BIG_SIZE",
		2:  "SUBT_ICON_REPLACE",
		3:  "SUBT_CMD_LINE_EXCEP",
		10: "SUBT_DOUBLE_SUFFIX",
		11: "SUBT_TOO_LONG_PATH",
		12: "SUBT_TEMPTATION_CONTENT",
	}
	SubType_value = map[string]int32{
		"SUBT_UNKNOWN":            0,
		"SUBT_LIK_BIG_SIZE":       1,
		"SUBT_ICON_REPLACE":       2,
		"SUBT_CMD_LINE_EXCEP":     3,
		"SUBT_DOUBLE_SUFFIX":      10,
		"SUBT_TOO_LONG_PATH":      11,
		"SUBT_TEMPTATION_CONTENT": 12,
	}
)

func (x SubType) Enum() *SubType {
	p := new(SubType)
	*p = x
	return p
}

func (x SubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_virus_file_proto_enumTypes[2].Descriptor()
}

func (SubType) Type() protoreflect.EnumType {
	return &file_agent_virus_file_proto_enumTypes[2]
}

func (x SubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubType.Descriptor instead.
func (SubType) EnumDescriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{2}
}

type SourceProcessType int32

const (
	SourceProcessType_SPT_UNKNOWN    SourceProcessType = 0
	SourceProcessType_SPT_WEIXIN     SourceProcessType = 1  // 微信
	SourceProcessType_SPT_WXWORK     SourceProcessType = 2  // 企业微信
	SourceProcessType_SPT_DINGDING   SourceProcessType = 3  // 钉钉
	SourceProcessType_SPT_WELINK     SourceProcessType = 4  // 华为WeLink
	SourceProcessType_SPT_FOXMIAL    SourceProcessType = 5  // Foxmail
	SourceProcessType_SPT_MAILMASTER SourceProcessType = 6  // 网易邮件大师
	SourceProcessType_SPT_OUTLOOK    SourceProcessType = 7  // Outlook
	SourceProcessType_SPT_CHANGYOU   SourceProcessType = 8  // 畅邮
	SourceProcessType_SPT_COREMAIL   SourceProcessType = 9  // CoreMail
	SourceProcessType_SPT_QWMAIL     SourceProcessType = 10 // 企微邮箱
	SourceProcessType_SPT_BOSS       SourceProcessType = 11 // Boss直聘
	SourceProcessType_SPT_FEISHU     SourceProcessType = 12 // 飞书
	SourceProcessType_SPT_LANXIN     SourceProcessType = 13 // 蓝信
	SourceProcessType_SPT_UDISK      SourceProcessType = 14 // U盘
	SourceProcessType_SPT_WEMAIL     SourceProcessType = 15 // WeMail
)

// Enum value maps for SourceProcessType.
var (
	SourceProcessType_name = map[int32]string{
		0:  "SPT_UNKNOWN",
		1:  "SPT_WEIXIN",
		2:  "SPT_WXWORK",
		3:  "SPT_DINGDING",
		4:  "SPT_WELINK",
		5:  "SPT_FOXMIAL",
		6:  "SPT_MAILMASTER",
		7:  "SPT_OUTLOOK",
		8:  "SPT_CHANGYOU",
		9:  "SPT_COREMAIL",
		10: "SPT_QWMAIL",
		11: "SPT_BOSS",
		12: "SPT_FEISHU",
		13: "SPT_LANXIN",
		14: "SPT_UDISK",
		15: "SPT_WEMAIL",
	}
	SourceProcessType_value = map[string]int32{
		"SPT_UNKNOWN":    0,
		"SPT_WEIXIN":     1,
		"SPT_WXWORK":     2,
		"SPT_DINGDING":   3,
		"SPT_WELINK":     4,
		"SPT_FOXMIAL":    5,
		"SPT_MAILMASTER": 6,
		"SPT_OUTLOOK":    7,
		"SPT_CHANGYOU":   8,
		"SPT_COREMAIL":   9,
		"SPT_QWMAIL":     10,
		"SPT_BOSS":       11,
		"SPT_FEISHU":     12,
		"SPT_LANXIN":     13,
		"SPT_UDISK":      14,
		"SPT_WEMAIL":     15,
	}
)

func (x SourceProcessType) Enum() *SourceProcessType {
	p := new(SourceProcessType)
	*p = x
	return p
}

func (x SourceProcessType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceProcessType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_virus_file_proto_enumTypes[3].Descriptor()
}

func (SourceProcessType) Type() protoreflect.EnumType {
	return &file_agent_virus_file_proto_enumTypes[3]
}

func (x SourceProcessType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceProcessType.Descriptor instead.
func (SourceProcessType) EnumDescriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{3}
}

// --------------------------------------------------
//
//	文件风险 识别结果
//	对应 g_CmdMemProtectvirusFileInfocd
//
// --------------------------------------------------
type MemProtectVirusFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo  *ClientID        `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	VirusList []*VirusSendInfo `protobuf:"bytes,2,rep,name=virus_list,json=virusList,proto3" json:"virus_list,omitempty"`
}

func (x *MemProtectVirusFileInfo) Reset() {
	*x = MemProtectVirusFileInfo{}
	mi := &file_agent_virus_file_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectVirusFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectVirusFileInfo) ProtoMessage() {}

func (x *MemProtectVirusFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectVirusFileInfo.ProtoReflect.Descriptor instead.
func (*MemProtectVirusFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectVirusFileInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectVirusFileInfo) GetVirusList() []*VirusSendInfo {
	if x != nil {
		return x.VirusList
	}
	return nil
}

type ReportSLInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleName     string `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`             // 规则名称
	MatchDetails string `protobuf:"bytes,2,opt,name=match_details,json=matchDetails,proto3" json:"match_details,omitempty"` // 详细信息
}

func (x *ReportSLInfo) Reset() {
	*x = ReportSLInfo{}
	mi := &file_agent_virus_file_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportSLInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportSLInfo) ProtoMessage() {}

func (x *ReportSLInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportSLInfo.ProtoReflect.Descriptor instead.
func (*ReportSLInfo) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{1}
}

func (x *ReportSLInfo) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *ReportSLInfo) GetMatchDetails() string {
	if x != nil {
		return x.MatchDetails
	}
	return ""
}

// 病毒扫描 目前只有webshell 静态扫描结果上报
type VirusSendInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Filepath     string      `protobuf:"bytes,2,opt,name=filepath,proto3" json:"filepath,omitempty"`                               // 文件路径
	Sha256       string      `protobuf:"bytes,3,opt,name=sha256,proto3" json:"sha256,omitempty"`                                   // 文件sha256
	RiskType     int32       `protobuf:"varint,4,opt,name=risk_type,json=riskType,proto3" json:"risk_type,omitempty"`              // 风险类型 3:办公软件钓鱼 4:邮件钓鱼
	VirusName    string      `protobuf:"bytes,5,opt,name=virus_name,json=virusName,proto3" json:"virus_name,omitempty"`            // v01不需要
	Md5          string      `protobuf:"bytes,6,opt,name=md5,proto3" json:"md5,omitempty"`                                         // 文件md5
	Atime        int64       `protobuf:"varint,7,opt,name=atime,proto3" json:"atime,omitempty"`                                    // 访问文件时间
	Mtime        int64       `protobuf:"varint,8,opt,name=mtime,proto3" json:"mtime,omitempty"`                                    // 文件修改时间
	Ctime        int64       `protobuf:"varint,9,opt,name=ctime,proto3" json:"ctime,omitempty"`                                    // 文件创建时间
	StMode       string      `protobuf:"bytes,10,opt,name=st_mode,json=stMode,proto3" json:"st_mode,omitempty"`                    // 文件权限
	FileSize     uint32      `protobuf:"varint,11,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`             // 文件大小，单位字节
	DetectSource uint32      `protobuf:"varint,12,opt,name=detect_source,json=detectSource,proto3" json:"detect_source,omitempty"` // 1 是agent告警检测到图片后门，2 是server检测结果上报
	// v01不需要
	DetectKind uint32 `protobuf:"varint,13,opt,name=detect_kind,json=detectKind,proto3" json:"detect_kind,omitempty"` // 1.webshell  2.virus
	// v01不需要
	MalwareType         string               `protobuf:"bytes,14,opt,name=malware_type,json=malwareType,proto3" json:"malware_type,omitempty"`                           // v01不需要
	AccessTime          uint64               `protobuf:"varint,30,opt,name=access_time,json=accessTime,proto3" json:"access_time,omitempty"`                             // 访问文件时间
	ModifyTime          uint64               `protobuf:"varint,31,opt,name=modify_time,json=modifyTime,proto3" json:"modify_time,omitempty"`                             // 文件修改时间
	CreateTime          uint64               `protobuf:"varint,32,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                             // 文件创建时间
	Filename            string               `protobuf:"bytes,33,opt,name=filename,proto3" json:"filename,omitempty"`                                                    // 文件名
	Sha1                string               `protobuf:"bytes,34,opt,name=sha1,proto3" json:"sha1,omitempty"`                                                            // 文件sha1
	FileVersion         string               `protobuf:"bytes,35,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`                           // 文件版本
	FileVendor          []byte               `protobuf:"bytes,36,opt,name=file_vendor,json=fileVendor,proto3" json:"file_vendor,omitempty"`                              // 文件厂商c
	SignatureInfo       []*SignatureInfo     `protobuf:"bytes,37,rep,name=signatureInfo,proto3" json:"signatureInfo,omitempty"`                                          // 进程文件签名信息
	FileType            FileTypeIdent        `protobuf:"varint,38,opt,name=file_type,json=fileType,proto3,enum=agent.FileTypeIdent" json:"file_type,omitempty"`          // 文件类型(php ,asp,jsp,...)
	ReportMaliciousInfo *ReportMaliciousInfo `protobuf:"bytes,39,opt,name=report_malicious_info,json=reportMaliciousInfo,proto3" json:"report_malicious_info,omitempty"` // 病毒扫描结果上报信息
	ReportSlInfo        *ReportSLInfo        `protobuf:"bytes,40,opt,name=report_sl_info,json=reportSlInfo,proto3" json:"report_sl_info,omitempty"`                      // 扫雷结果上报信息
	SlDetail            string               `protobuf:"bytes,41,opt,name=sl_detail,json=slDetail,proto3" json:"sl_detail,omitempty"`                                    // 扫雷详细信息
	FileSourceList      string               `protobuf:"bytes,42,opt,name=file_source_list,json=fileSourceList,proto3" json:"file_source_list,omitempty"`
	FileSourceSoftware  SourceProcessType    `protobuf:"varint,43,opt,name=file_source_software,json=fileSourceSoftware,proto3,enum=agent.SourceProcessType" json:"file_source_software,omitempty"` // 文件来源软件
	FileScore           int32                `protobuf:"varint,44,opt,name=file_score,json=fileScore,proto3" json:"file_score,omitempty"`                                                           // 文件分数用于计算置信度,分数越大越可疑
}

func (x *VirusSendInfo) Reset() {
	*x = VirusSendInfo{}
	mi := &file_agent_virus_file_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusSendInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusSendInfo) ProtoMessage() {}

func (x *VirusSendInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusSendInfo.ProtoReflect.Descriptor instead.
func (*VirusSendInfo) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{2}
}

func (x *VirusSendInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VirusSendInfo) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *VirusSendInfo) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *VirusSendInfo) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *VirusSendInfo) GetVirusName() string {
	if x != nil {
		return x.VirusName
	}
	return ""
}

func (x *VirusSendInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *VirusSendInfo) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *VirusSendInfo) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *VirusSendInfo) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *VirusSendInfo) GetStMode() string {
	if x != nil {
		return x.StMode
	}
	return ""
}

func (x *VirusSendInfo) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *VirusSendInfo) GetDetectSource() uint32 {
	if x != nil {
		return x.DetectSource
	}
	return 0
}

func (x *VirusSendInfo) GetDetectKind() uint32 {
	if x != nil {
		return x.DetectKind
	}
	return 0
}

func (x *VirusSendInfo) GetMalwareType() string {
	if x != nil {
		return x.MalwareType
	}
	return ""
}

func (x *VirusSendInfo) GetAccessTime() uint64 {
	if x != nil {
		return x.AccessTime
	}
	return 0
}

func (x *VirusSendInfo) GetModifyTime() uint64 {
	if x != nil {
		return x.ModifyTime
	}
	return 0
}

func (x *VirusSendInfo) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *VirusSendInfo) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *VirusSendInfo) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *VirusSendInfo) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *VirusSendInfo) GetFileVendor() []byte {
	if x != nil {
		return x.FileVendor
	}
	return nil
}

func (x *VirusSendInfo) GetSignatureInfo() []*SignatureInfo {
	if x != nil {
		return x.SignatureInfo
	}
	return nil
}

func (x *VirusSendInfo) GetFileType() FileTypeIdent {
	if x != nil {
		return x.FileType
	}
	return FileTypeIdent_FILE_TYPE_UNKNOWN
}

func (x *VirusSendInfo) GetReportMaliciousInfo() *ReportMaliciousInfo {
	if x != nil {
		return x.ReportMaliciousInfo
	}
	return nil
}

func (x *VirusSendInfo) GetReportSlInfo() *ReportSLInfo {
	if x != nil {
		return x.ReportSlInfo
	}
	return nil
}

func (x *VirusSendInfo) GetSlDetail() string {
	if x != nil {
		return x.SlDetail
	}
	return ""
}

func (x *VirusSendInfo) GetFileSourceList() string {
	if x != nil {
		return x.FileSourceList
	}
	return ""
}

func (x *VirusSendInfo) GetFileSourceSoftware() SourceProcessType {
	if x != nil {
		return x.FileSourceSoftware
	}
	return SourceProcessType_SPT_UNKNOWN
}

func (x *VirusSendInfo) GetFileScore() int32 {
	if x != nil {
		return x.FileScore
	}
	return 0
}

type VirusRecvInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256 string `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`
}

func (x *VirusRecvInfo) Reset() {
	*x = VirusRecvInfo{}
	mi := &file_agent_virus_file_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusRecvInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusRecvInfo) ProtoMessage() {}

func (x *VirusRecvInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusRecvInfo.ProtoReflect.Descriptor instead.
func (*VirusRecvInfo) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{3}
}

func (x *VirusRecvInfo) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

type DdeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DdeCode []string `protobuf:"bytes,1,rep,name=dde_code,json=ddeCode,proto3" json:"dde_code,omitempty"` // dde code
}

func (x *DdeMessage) Reset() {
	*x = DdeMessage{}
	mi := &file_agent_virus_file_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DdeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DdeMessage) ProtoMessage() {}

func (x *DdeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DdeMessage.ProtoReflect.Descriptor instead.
func (*DdeMessage) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{4}
}

func (x *DdeMessage) GetDdeCode() []string {
	if x != nil {
		return x.DdeCode
	}
	return nil
}

type VbaMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VbaCode []string `protobuf:"bytes,1,rep,name=vba_code,json=vbaCode,proto3" json:"vba_code,omitempty"` // vba code
}

func (x *VbaMessage) Reset() {
	*x = VbaMessage{}
	mi := &file_agent_virus_file_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VbaMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VbaMessage) ProtoMessage() {}

func (x *VbaMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VbaMessage.ProtoReflect.Descriptor instead.
func (*VbaMessage) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{5}
}

func (x *VbaMessage) GetVbaCode() []string {
	if x != nil {
		return x.VbaCode
	}
	return nil
}

type LnkMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetPath string `protobuf:"bytes,1,opt,name=target_path,json=targetPath,proto3" json:"target_path,omitempty"` // lnk目标路径
	WorkingDir string `protobuf:"bytes,2,opt,name=working_dir,json=workingDir,proto3" json:"working_dir,omitempty"` // 工作目录
	CmdLine    string `protobuf:"bytes,3,opt,name=cmd_line,json=cmdLine,proto3" json:"cmd_line,omitempty"`          // 命令行参数
	IconPath   string `protobuf:"bytes,4,opt,name=icon_path,json=iconPath,proto3" json:"icon_path,omitempty"`       // 图标路径
}

func (x *LnkMessage) Reset() {
	*x = LnkMessage{}
	mi := &file_agent_virus_file_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LnkMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LnkMessage) ProtoMessage() {}

func (x *LnkMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LnkMessage.ProtoReflect.Descriptor instead.
func (*LnkMessage) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{6}
}

func (x *LnkMessage) GetTargetPath() string {
	if x != nil {
		return x.TargetPath
	}
	return ""
}

func (x *LnkMessage) GetWorkingDir() string {
	if x != nil {
		return x.WorkingDir
	}
	return ""
}

func (x *LnkMessage) GetCmdLine() string {
	if x != nil {
		return x.CmdLine
	}
	return ""
}

func (x *LnkMessage) GetIconPath() string {
	if x != nil {
		return x.IconPath
	}
	return ""
}

type UrlMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"` // url地址
}

func (x *UrlMessage) Reset() {
	*x = UrlMessage{}
	mi := &file_agent_virus_file_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UrlMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UrlMessage) ProtoMessage() {}

func (x *UrlMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UrlMessage.ProtoReflect.Descriptor instead.
func (*UrlMessage) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{7}
}

func (x *UrlMessage) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ReportMaliciousInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MainType []MainType  `protobuf:"varint,1,rep,packed,name=main_type,json=mainType,proto3,enum=agent.MainType" json:"main_type,omitempty"` // 主类型
	SubType  []SubType   `protobuf:"varint,2,rep,packed,name=sub_type,json=subType,proto3,enum=agent.SubType" json:"sub_type,omitempty"`     // 子类型
	DdeInfo  *DdeMessage `protobuf:"bytes,3,opt,name=dde_info,json=ddeInfo,proto3" json:"dde_info,omitempty"`                                // 恶意dde信息
	VbaInfo  *VbaMessage `protobuf:"bytes,4,opt,name=vba_info,json=vbaInfo,proto3" json:"vba_info,omitempty"`                                // 恶意vba信息
	LnkInfo  *LnkMessage `protobuf:"bytes,5,opt,name=lnk_info,json=lnkInfo,proto3" json:"lnk_info,omitempty"`                                // 恶意lnk信息
	UrlInfo  *UrlMessage `protobuf:"bytes,6,opt,name=url_info,json=urlInfo,proto3" json:"url_info,omitempty"`                                // 恶意url信息
}

func (x *ReportMaliciousInfo) Reset() {
	*x = ReportMaliciousInfo{}
	mi := &file_agent_virus_file_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportMaliciousInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportMaliciousInfo) ProtoMessage() {}

func (x *ReportMaliciousInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_virus_file_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportMaliciousInfo.ProtoReflect.Descriptor instead.
func (*ReportMaliciousInfo) Descriptor() ([]byte, []int) {
	return file_agent_virus_file_proto_rawDescGZIP(), []int{8}
}

func (x *ReportMaliciousInfo) GetMainType() []MainType {
	if x != nil {
		return x.MainType
	}
	return nil
}

func (x *ReportMaliciousInfo) GetSubType() []SubType {
	if x != nil {
		return x.SubType
	}
	return nil
}

func (x *ReportMaliciousInfo) GetDdeInfo() *DdeMessage {
	if x != nil {
		return x.DdeInfo
	}
	return nil
}

func (x *ReportMaliciousInfo) GetVbaInfo() *VbaMessage {
	if x != nil {
		return x.VbaInfo
	}
	return nil
}

func (x *ReportMaliciousInfo) GetLnkInfo() *LnkMessage {
	if x != nil {
		return x.LnkInfo
	}
	return nil
}

func (x *ReportMaliciousInfo) GetUrlInfo() *UrlMessage {
	if x != nil {
		return x.UrlInfo
	}
	return nil
}

var File_agent_virus_file_proto protoreflect.FileDescriptor

var file_agent_virus_file_proto_rawDesc = []byte{
	0x0a, 0x16, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a,
	0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x17, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x56, 0x69, 0x72, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b,
	0x0a, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x44, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x0a, 0x76,
	0x69, 0x72, 0x75, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x69, 0x72, 0x75, 0x73, 0x53, 0x65, 0x6e,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x50, 0x0a, 0x0c, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x4c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x22, 0xa0, 0x08, 0x0a, 0x0d, 0x56, 0x69, 0x72, 0x75, 0x73, 0x53, 0x65, 0x6e, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64,
	0x35, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x0c, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f,
	0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x66,
	0x69, 0x6c, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0d, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x25, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4e, 0x0a, 0x15, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x13, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x6c, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39, 0x0a, 0x0e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x73, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53,
	0x4c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x2a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x14, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x6f, 0x66, 0x74, 0x77, 0x61,
	0x72, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x12, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x6f,
	0x66, 0x74, 0x77, 0x61, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x63, 0x6f, 0x72, 0x65, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x63, 0x6f, 0x72, 0x65, 0x22, 0x27, 0x0a, 0x0d, 0x56, 0x69, 0x72, 0x75, 0x73, 0x52, 0x65,
	0x63, 0x76, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22, 0x27,
	0x0a, 0x0a, 0x44, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x64, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07,
	0x64, 0x64, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x27, 0x0a, 0x0a, 0x56, 0x62, 0x61, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x62, 0x61, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x76, 0x62, 0x61, 0x43, 0x6f, 0x64, 0x65,
	0x22, 0x86, 0x01, 0x0a, 0x0a, 0x4c, 0x6e, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x69, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6d, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6d, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x63, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x22, 0x1e, 0x0a, 0x0a, 0x55, 0x72, 0x6c,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xa6, 0x02, 0x0a, 0x13, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x4d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2c, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x69,
	0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x29, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x08, 0x64, 0x64,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x07, 0x64, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x08, 0x76, 0x62, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x56, 0x62, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x76,
	0x62, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x08, 0x6c, 0x6e, 0x6b, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4c, 0x6e, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6c, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x08, 0x75, 0x72, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55,
	0x72, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x75, 0x72, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x2a, 0xf1, 0x03, 0x0a, 0x09, 0x56, 0x69, 0x72, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x57,
	0x53, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x54,
	0x45, 0x43, 0x54, 0x5f, 0x41, 0x4e, 0x54, 0x49, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12,
	0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x49, 0x43, 0x45,
	0x5f, 0x46, 0x49, 0x53, 0x48, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45,
	0x54, 0x45, 0x43, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x5f, 0x46, 0x49, 0x53, 0x48, 0x49,
	0x4e, 0x47, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x53,
	0x49, 0x47, 0x4e, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x5f, 0x4c, 0x49, 0x42, 0x10, 0x05, 0x12,
	0x17, 0x0a, 0x13, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x44, 0x59, 0x4e, 0x5f, 0x4c, 0x49,
	0x42, 0x5f, 0x48, 0x41, 0x43, 0x4b, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x45, 0x54, 0x45,
	0x43, 0x54, 0x5f, 0x53, 0x4c, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x5f, 0x42, 0x4f, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x10, 0x08, 0x12, 0x11, 0x0a, 0x0d, 0x44,
	0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x54, 0x52, 0x4f, 0x4a, 0x41, 0x4e, 0x10, 0x09, 0x12, 0x12,
	0x0a, 0x0e, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x53, 0x50, 0x59, 0x57, 0x41, 0x52, 0x45,
	0x10, 0x0a, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x52, 0x45, 0x4d,
	0x4f, 0x54, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x10, 0x0b, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x44, 0x4f, 0x4f, 0x52,
	0x10, 0x0c, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x44, 0x41, 0x54,
	0x41, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x43, 0x45, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d,
	0x12, 0x13, 0x0a, 0x0f, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x45, 0x58, 0x50, 0x5f, 0x56,
	0x55, 0x4c, 0x4e, 0x10, 0x0e, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f,
	0x45, 0x58, 0x54, 0x4f, 0x52, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0f, 0x12, 0x17, 0x0a, 0x13, 0x44,
	0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x56, 0x49, 0x52,
	0x55, 0x53, 0x10, 0x10, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x57,
	0x4f, 0x52, 0x4d, 0x10, 0x11, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f,
	0x41, 0x50, 0x54, 0x10, 0x12, 0x12, 0x10, 0x0a, 0x0c, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f,
	0x56, 0x49, 0x52, 0x55, 0x53, 0x10, 0x13, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x54, 0x45, 0x43,
	0x54, 0x5f, 0x48, 0x41, 0x43, 0x4b, 0x5f, 0x54, 0x4f, 0x4f, 0x4c, 0x10, 0x14, 0x12, 0x17, 0x0a,
	0x13, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x55, 0x53, 0x42, 0x5f, 0x50, 0x48, 0x49, 0x53,
	0x48, 0x49, 0x4e, 0x47, 0x10, 0x15, 0x2a, 0xd1, 0x01, 0x0a, 0x08, 0x4d, 0x61, 0x69, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4d, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x4c, 0x4e, 0x4b, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x4d,
	0x54, 0x5f, 0x4d, 0x41, 0x43, 0x52, 0x4f, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f,
	0x44, 0x44, 0x45, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x4f, 0x4c, 0x45, 0x10,
	0x05, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x48, 0x54, 0x41, 0x10, 0x06, 0x12, 0x0a, 0x0a,
	0x06, 0x4d, 0x54, 0x5f, 0x43, 0x48, 0x4d, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f,
	0x52, 0x4c, 0x4f, 0x10, 0x08, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x52, 0x41, 0x52, 0x10,
	0x09, 0x12, 0x10, 0x0a, 0x0c, 0x4d, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x54,
	0x48, 0x10, 0x0a, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x54, 0x5f, 0x50, 0x45, 0x10, 0x0b, 0x12, 0x0a,
	0x0a, 0x06, 0x4d, 0x54, 0x5f, 0x56, 0x42, 0x45, 0x10, 0x0c, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x54,
	0x5f, 0x45, 0x4d, 0x4c, 0x10, 0x0d, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x54, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x42, 0x49, 0x4e, 0x44, 0x4c, 0x45, 0x10, 0x0e, 0x2a, 0xaf, 0x01, 0x0a, 0x07, 0x53,
	0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x55, 0x42, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x55, 0x42, 0x54,
	0x5f, 0x4c, 0x49, 0x4b, 0x5f, 0x42, 0x49, 0x47, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x53, 0x55, 0x42, 0x54, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x50,
	0x4c, 0x41, 0x43, 0x45, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x55, 0x42, 0x54, 0x5f, 0x43,
	0x4d, 0x44, 0x5f, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x50, 0x10, 0x03, 0x12,
	0x16, 0x0a, 0x12, 0x53, 0x55, 0x42, 0x54, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x5f, 0x53,
	0x55, 0x46, 0x46, 0x49, 0x58, 0x10, 0x0a, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x55, 0x42, 0x54, 0x5f,
	0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x4f, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x54, 0x48, 0x10, 0x0b, 0x12,
	0x1b, 0x0a, 0x17, 0x53, 0x55, 0x42, 0x54, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x0c, 0x2a, 0x9d, 0x02, 0x0a,
	0x11, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x50, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x50, 0x54, 0x5f, 0x57, 0x45, 0x49, 0x58, 0x49,
	0x4e, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x50, 0x54, 0x5f, 0x57, 0x58, 0x57, 0x4f, 0x52,
	0x4b, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x50, 0x54, 0x5f, 0x44, 0x49, 0x4e, 0x47, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x50, 0x54, 0x5f, 0x57, 0x45, 0x4c,
	0x49, 0x4e, 0x4b, 0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x50, 0x54, 0x5f, 0x46, 0x4f, 0x58,
	0x4d, 0x49, 0x41, 0x4c, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x50, 0x54, 0x5f, 0x4d, 0x41,
	0x49, 0x4c, 0x4d, 0x41, 0x53, 0x54, 0x45, 0x52, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x50,
	0x54, 0x5f, 0x4f, 0x55, 0x54, 0x4c, 0x4f, 0x4f, 0x4b, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x53,
	0x50, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47, 0x59, 0x4f, 0x55, 0x10, 0x08, 0x12, 0x10, 0x0a,
	0x0c, 0x53, 0x50, 0x54, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x09, 0x12,
	0x0e, 0x0a, 0x0a, 0x53, 0x50, 0x54, 0x5f, 0x51, 0x57, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x0a, 0x12,
	0x0c, 0x0a, 0x08, 0x53, 0x50, 0x54, 0x5f, 0x42, 0x4f, 0x53, 0x53, 0x10, 0x0b, 0x12, 0x0e, 0x0a,
	0x0a, 0x53, 0x50, 0x54, 0x5f, 0x46, 0x45, 0x49, 0x53, 0x48, 0x55, 0x10, 0x0c, 0x12, 0x0e, 0x0a,
	0x0a, 0x53, 0x50, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x58, 0x49, 0x4e, 0x10, 0x0d, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x50, 0x54, 0x5f, 0x55, 0x44, 0x49, 0x53, 0x4b, 0x10, 0x0e, 0x12, 0x0e, 0x0a, 0x0a,
	0x53, 0x50, 0x54, 0x5f, 0x57, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x0f, 0x42, 0x2a, 0x5a, 0x28,
	0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30,
	0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_virus_file_proto_rawDescOnce sync.Once
	file_agent_virus_file_proto_rawDescData = file_agent_virus_file_proto_rawDesc
)

func file_agent_virus_file_proto_rawDescGZIP() []byte {
	file_agent_virus_file_proto_rawDescOnce.Do(func() {
		file_agent_virus_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_virus_file_proto_rawDescData)
	})
	return file_agent_virus_file_proto_rawDescData
}

var file_agent_virus_file_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_agent_virus_file_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_agent_virus_file_proto_goTypes = []any{
	(VirusType)(0),                  // 0: agent.VirusType
	(MainType)(0),                   // 1: agent.MainType
	(SubType)(0),                    // 2: agent.SubType
	(SourceProcessType)(0),          // 3: agent.SourceProcessType
	(*MemProtectVirusFileInfo)(nil), // 4: agent.MemProtectVirusFileInfo
	(*ReportSLInfo)(nil),            // 5: agent.ReportSLInfo
	(*VirusSendInfo)(nil),           // 6: agent.VirusSendInfo
	(*VirusRecvInfo)(nil),           // 7: agent.VirusRecvInfo
	(*DdeMessage)(nil),              // 8: agent.DdeMessage
	(*VbaMessage)(nil),              // 9: agent.VbaMessage
	(*LnkMessage)(nil),              // 10: agent.LnkMessage
	(*UrlMessage)(nil),              // 11: agent.UrlMessage
	(*ReportMaliciousInfo)(nil),     // 12: agent.ReportMaliciousInfo
	(*ClientID)(nil),                // 13: agent.ClientID
	(*RiskHeader)(nil),              // 14: agent.RiskHeader
	(*SignatureInfo)(nil),           // 15: agent.SignatureInfo
	(FileTypeIdent)(0),              // 16: agent.FileTypeIdent
}
var file_agent_virus_file_proto_depIdxs = []int32{
	13, // 0: agent.MemProtectVirusFileInfo.baseInfo:type_name -> agent.ClientID
	6,  // 1: agent.MemProtectVirusFileInfo.virus_list:type_name -> agent.VirusSendInfo
	14, // 2: agent.VirusSendInfo.header:type_name -> agent.RiskHeader
	15, // 3: agent.VirusSendInfo.signatureInfo:type_name -> agent.SignatureInfo
	16, // 4: agent.VirusSendInfo.file_type:type_name -> agent.FileTypeIdent
	12, // 5: agent.VirusSendInfo.report_malicious_info:type_name -> agent.ReportMaliciousInfo
	5,  // 6: agent.VirusSendInfo.report_sl_info:type_name -> agent.ReportSLInfo
	3,  // 7: agent.VirusSendInfo.file_source_software:type_name -> agent.SourceProcessType
	1,  // 8: agent.ReportMaliciousInfo.main_type:type_name -> agent.MainType
	2,  // 9: agent.ReportMaliciousInfo.sub_type:type_name -> agent.SubType
	8,  // 10: agent.ReportMaliciousInfo.dde_info:type_name -> agent.DdeMessage
	9,  // 11: agent.ReportMaliciousInfo.vba_info:type_name -> agent.VbaMessage
	10, // 12: agent.ReportMaliciousInfo.lnk_info:type_name -> agent.LnkMessage
	11, // 13: agent.ReportMaliciousInfo.url_info:type_name -> agent.UrlMessage
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_agent_virus_file_proto_init() }
func file_agent_virus_file_proto_init() {
	if File_agent_virus_file_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_virus_file_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_virus_file_proto_goTypes,
		DependencyIndexes: file_agent_virus_file_proto_depIdxs,
		EnumInfos:         file_agent_virus_file_proto_enumTypes,
		MessageInfos:      file_agent_virus_file_proto_msgTypes,
	}.Build()
	File_agent_virus_file_proto = out.File
	file_agent_virus_file_proto_rawDesc = nil
	file_agent_virus_file_proto_goTypes = nil
	file_agent_virus_file_proto_depIdxs = nil
}
