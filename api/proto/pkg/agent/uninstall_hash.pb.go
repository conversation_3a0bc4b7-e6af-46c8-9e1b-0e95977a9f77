// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/uninstall_hash.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 请求、回复、广播
type UnInstallHash struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CheckEnable bool   `protobuf:"varint,1,opt,name=checkEnable,proto3" json:"checkEnable,omitempty"`
	Hash        []byte `protobuf:"bytes,2,opt,name=hash,proto3" json:"hash,omitempty"`
}

func (x *UnInstallHash) Reset() {
	*x = UnInstallHash{}
	mi := &file_agent_uninstall_hash_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnInstallHash) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnInstallHash) ProtoMessage() {}

func (x *UnInstallHash) ProtoReflect() protoreflect.Message {
	mi := &file_agent_uninstall_hash_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnInstallHash.ProtoReflect.Descriptor instead.
func (*UnInstallHash) Descriptor() ([]byte, []int) {
	return file_agent_uninstall_hash_proto_rawDescGZIP(), []int{0}
}

func (x *UnInstallHash) GetCheckEnable() bool {
	if x != nil {
		return x.CheckEnable
	}
	return false
}

func (x *UnInstallHash) GetHash() []byte {
	if x != nil {
		return x.Hash
	}
	return nil
}

var File_agent_uninstall_hash_proto protoreflect.FileDescriptor

var file_agent_uninstall_hash_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x22, 0x45, 0x0a, 0x0d, 0x55, 0x6e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x48, 0x61, 0x73, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69,
	0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_uninstall_hash_proto_rawDescOnce sync.Once
	file_agent_uninstall_hash_proto_rawDescData = file_agent_uninstall_hash_proto_rawDesc
)

func file_agent_uninstall_hash_proto_rawDescGZIP() []byte {
	file_agent_uninstall_hash_proto_rawDescOnce.Do(func() {
		file_agent_uninstall_hash_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_uninstall_hash_proto_rawDescData)
	})
	return file_agent_uninstall_hash_proto_rawDescData
}

var file_agent_uninstall_hash_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_agent_uninstall_hash_proto_goTypes = []any{
	(*UnInstallHash)(nil), // 0: agent.UnInstallHash
}
var file_agent_uninstall_hash_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_agent_uninstall_hash_proto_init() }
func file_agent_uninstall_hash_proto_init() {
	if File_agent_uninstall_hash_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_uninstall_hash_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_uninstall_hash_proto_goTypes,
		DependencyIndexes: file_agent_uninstall_hash_proto_depIdxs,
		MessageInfos:      file_agent_uninstall_hash_proto_msgTypes,
	}.Build()
	File_agent_uninstall_hash_proto = out.File
	file_agent_uninstall_hash_proto_rawDesc = nil
	file_agent_uninstall_hash_proto_goTypes = nil
	file_agent_uninstall_hash_proto_depIdxs = nil
}
