// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/acdr.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EnumResourceType int32

const (
	EnumResourceType_RLIMIT_CPU        EnumResourceType = 0
	EnumResourceType_RLIMIT_FSIZE      EnumResourceType = 1
	EnumResourceType_RLIMIT_DATA       EnumResourceType = 2
	EnumResourceType_RLIMIT_STACK      EnumResourceType = 3
	EnumResourceType_RLIMIT_CORE       EnumResourceType = 4
	EnumResourceType_RLIMIT_RSS        EnumResourceType = 5
	EnumResourceType_RLIMIT_NOFILE     EnumResourceType = 7
	EnumResourceType_RLIMIT_AS         EnumResourceType = 9
	EnumResourceType_RLIMIT_NPROC      EnumResourceType = 6
	EnumResourceType_RLIMIT_MEMLOCK    EnumResourceType = 8
	EnumResourceType_RLIMIT_LOCKS      EnumResourceType = 10
	EnumResourceType_RLIMIT_SIGPENDING EnumResourceType = 11
	EnumResourceType_RLIMIT_MSGQUEUE   EnumResourceType = 12
	EnumResourceType_RLIMIT_NICE       EnumResourceType = 13
	EnumResourceType_RLIMIT_RTPRIO     EnumResourceType = 14
	EnumResourceType_RLIMIT_RTTIME     EnumResourceType = 15
	EnumResourceType_RLIMIT_NLIMITS    EnumResourceType = 16
)

// Enum value maps for EnumResourceType.
var (
	EnumResourceType_name = map[int32]string{
		0:  "RLIMIT_CPU",
		1:  "RLIMIT_FSIZE",
		2:  "RLIMIT_DATA",
		3:  "RLIMIT_STACK",
		4:  "RLIMIT_CORE",
		5:  "RLIMIT_RSS",
		7:  "RLIMIT_NOFILE",
		9:  "RLIMIT_AS",
		6:  "RLIMIT_NPROC",
		8:  "RLIMIT_MEMLOCK",
		10: "RLIMIT_LOCKS",
		11: "RLIMIT_SIGPENDING",
		12: "RLIMIT_MSGQUEUE",
		13: "RLIMIT_NICE",
		14: "RLIMIT_RTPRIO",
		15: "RLIMIT_RTTIME",
		16: "RLIMIT_NLIMITS",
	}
	EnumResourceType_value = map[string]int32{
		"RLIMIT_CPU":        0,
		"RLIMIT_FSIZE":      1,
		"RLIMIT_DATA":       2,
		"RLIMIT_STACK":      3,
		"RLIMIT_CORE":       4,
		"RLIMIT_RSS":        5,
		"RLIMIT_NOFILE":     7,
		"RLIMIT_AS":         9,
		"RLIMIT_NPROC":      6,
		"RLIMIT_MEMLOCK":    8,
		"RLIMIT_LOCKS":      10,
		"RLIMIT_SIGPENDING": 11,
		"RLIMIT_MSGQUEUE":   12,
		"RLIMIT_NICE":       13,
		"RLIMIT_RTPRIO":     14,
		"RLIMIT_RTTIME":     15,
		"RLIMIT_NLIMITS":    16,
	}
)

func (x EnumResourceType) Enum() *EnumResourceType {
	p := new(EnumResourceType)
	*p = x
	return p
}

func (x EnumResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EnumResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[0].Descriptor()
}

func (EnumResourceType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[0]
}

func (x EnumResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EnumResourceType.Descriptor instead.
func (EnumResourceType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{0}
}

type HandleMode int32

const (
	HandleMode_HM_NOT_SET                   HandleMode = 0  //不设置 控制模式
	HandleMode_HM_TERMINATE_PROCESS         HandleMode = 1  //结束进程
	HandleMode_HM_TERMINATE_THREAD          HandleMode = 2  //结束线程
	HandleMode_HM_RESTORE_EFFECT            HandleMode = 4  //恢复行为造成的影响
	HandleMode_HM_RESTORE_ALL_EFFECT        HandleMode = 8  //恢复所有行为造成的影响
	HandleMode_HM_REFUSE_REQUEST            HandleMode = 16 //拦截请求
	HandleMode_HM_ONLY_MONITOR              HandleMode = 32 //仅上报
	HandleMode_HM_NO_EFFECT_EXTERNAL_SWITCH HandleMode = 64 //不受外部开关影响
)

// Enum value maps for HandleMode.
var (
	HandleMode_name = map[int32]string{
		0:  "HM_NOT_SET",
		1:  "HM_TERMINATE_PROCESS",
		2:  "HM_TERMINATE_THREAD",
		4:  "HM_RESTORE_EFFECT",
		8:  "HM_RESTORE_ALL_EFFECT",
		16: "HM_REFUSE_REQUEST",
		32: "HM_ONLY_MONITOR",
		64: "HM_NO_EFFECT_EXTERNAL_SWITCH",
	}
	HandleMode_value = map[string]int32{
		"HM_NOT_SET":                   0,
		"HM_TERMINATE_PROCESS":         1,
		"HM_TERMINATE_THREAD":          2,
		"HM_RESTORE_EFFECT":            4,
		"HM_RESTORE_ALL_EFFECT":        8,
		"HM_REFUSE_REQUEST":            16,
		"HM_ONLY_MONITOR":              32,
		"HM_NO_EFFECT_EXTERNAL_SWITCH": 64,
	}
)

func (x HandleMode) Enum() *HandleMode {
	p := new(HandleMode)
	*p = x
	return p
}

func (x HandleMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HandleMode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[1].Descriptor()
}

func (HandleMode) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[1]
}

func (x HandleMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HandleMode.Descriptor instead.
func (HandleMode) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{1}
}

type ActionType int32

const (
	ActionType_AT_DEFAULT              ActionType = 0
	ActionType_AT_UNKNOWN              ActionType = 1001 //未知行为
	ActionType_AT_FILE_CREATE          ActionType = 1002 //文件创建
	ActionType_AT_FILE_OPEN            ActionType = 1003 //文件打开
	ActionType_AT_FILE_WRITE           ActionType = 1004 //文件写
	ActionType_AT_FILE_RENAME          ActionType = 1005 //文件重命名
	ActionType_AT_FILE_DELETE          ActionType = 1006 //文件删除
	ActionType_AT_FILE_LINK            ActionType = 1007 //创建文件连接
	ActionType_AT_FILE_IOCTL_IMMUTABLE ActionType = 1008 //通过ioctl操作文件
	ActionType_AT_FILE_RESTORE_UTIME   ActionType = 1009 //恢复文件时间属性
	ActionType_AT_MAKEDIR              ActionType = 1010 //创建目录
	ActionType_AT_FILE_SYMLINK         ActionType = 1011 //创建符号连接
	ActionType_AT_FILE_SET_UID         ActionType = 1012 //修改文件uid
	ActionType_AT_NET_CONNECT          ActionType = 1100 //连接网络
	ActionType_AT_NET_ACCEPT           ActionType = 1101 //X
	ActionType_AT_NET_LISTEN           ActionType = 1102 //X
	ActionType_AT_NET_SEND             ActionType = 1103 //X
	ActionType_AT_NET_RECV             ActionType = 1104 //X
	ActionType_AT_PROCESS_CREATE       ActionType = 1105 //X
	ActionType_AT_THREAD_CREATE        ActionType = 1106 //X
	ActionType_AT_LOAD_IMAGE           ActionType = 1107 //X
	ActionType_AT_PROCESS_INFO         ActionType = 1108 //X
	ActionType_AT_FILELESS_ATTACK      ActionType = 1109 //无文件攻击
	ActionType_AT_NET_SNIFFER          ActionType = 1110 //网络抓包行为
	ActionType_AT_ENV_HIJACK           ActionType = 1111 //环境变量劫持
	ActionType_AT_SELF_DELETE          ActionType = 1112 //自删除
	ActionType_AT_CALL_USERMODEHELPER  ActionType = 1114 //内核执行用户态进程。
	ActionType_AT_REVERSE_SHELL        ActionType = 1115 //反弹shell
	ActionType_AT_PROCESS_EXEC         ActionType = 1116 //exec
	ActionType_AT_PTRACE               ActionType = 1117 //读写进程内存
	ActionType_AT_BPF                  ActionType = 1118 //bpf技术
	ActionType_AT_PRIVILEGE_ESCALATION ActionType = 1119 //进程提权
	ActionType_AT_FAKE_EXE_FILE        ActionType = 1120 //伪装进程执行路径
	ActionType_AT_HIDE_MODULE          ActionType = 1121 //隐藏模块
	ActionType_AT_KILL                 ActionType = 1122 //kill信号
)

// Enum value maps for ActionType.
var (
	ActionType_name = map[int32]string{
		0:    "AT_DEFAULT",
		1001: "AT_UNKNOWN",
		1002: "AT_FILE_CREATE",
		1003: "AT_FILE_OPEN",
		1004: "AT_FILE_WRITE",
		1005: "AT_FILE_RENAME",
		1006: "AT_FILE_DELETE",
		1007: "AT_FILE_LINK",
		1008: "AT_FILE_IOCTL_IMMUTABLE",
		1009: "AT_FILE_RESTORE_UTIME",
		1010: "AT_MAKEDIR",
		1011: "AT_FILE_SYMLINK",
		1012: "AT_FILE_SET_UID",
		1100: "AT_NET_CONNECT",
		1101: "AT_NET_ACCEPT",
		1102: "AT_NET_LISTEN",
		1103: "AT_NET_SEND",
		1104: "AT_NET_RECV",
		1105: "AT_PROCESS_CREATE",
		1106: "AT_THREAD_CREATE",
		1107: "AT_LOAD_IMAGE",
		1108: "AT_PROCESS_INFO",
		1109: "AT_FILELESS_ATTACK",
		1110: "AT_NET_SNIFFER",
		1111: "AT_ENV_HIJACK",
		1112: "AT_SELF_DELETE",
		1114: "AT_CALL_USERMODEHELPER",
		1115: "AT_REVERSE_SHELL",
		1116: "AT_PROCESS_EXEC",
		1117: "AT_PTRACE",
		1118: "AT_BPF",
		1119: "AT_PRIVILEGE_ESCALATION",
		1120: "AT_FAKE_EXE_FILE",
		1121: "AT_HIDE_MODULE",
		1122: "AT_KILL",
	}
	ActionType_value = map[string]int32{
		"AT_DEFAULT":              0,
		"AT_UNKNOWN":              1001,
		"AT_FILE_CREATE":          1002,
		"AT_FILE_OPEN":            1003,
		"AT_FILE_WRITE":           1004,
		"AT_FILE_RENAME":          1005,
		"AT_FILE_DELETE":          1006,
		"AT_FILE_LINK":            1007,
		"AT_FILE_IOCTL_IMMUTABLE": 1008,
		"AT_FILE_RESTORE_UTIME":   1009,
		"AT_MAKEDIR":              1010,
		"AT_FILE_SYMLINK":         1011,
		"AT_FILE_SET_UID":         1012,
		"AT_NET_CONNECT":          1100,
		"AT_NET_ACCEPT":           1101,
		"AT_NET_LISTEN":           1102,
		"AT_NET_SEND":             1103,
		"AT_NET_RECV":             1104,
		"AT_PROCESS_CREATE":       1105,
		"AT_THREAD_CREATE":        1106,
		"AT_LOAD_IMAGE":           1107,
		"AT_PROCESS_INFO":         1108,
		"AT_FILELESS_ATTACK":      1109,
		"AT_NET_SNIFFER":          1110,
		"AT_ENV_HIJACK":           1111,
		"AT_SELF_DELETE":          1112,
		"AT_CALL_USERMODEHELPER":  1114,
		"AT_REVERSE_SHELL":        1115,
		"AT_PROCESS_EXEC":         1116,
		"AT_PTRACE":               1117,
		"AT_BPF":                  1118,
		"AT_PRIVILEGE_ESCALATION": 1119,
		"AT_FAKE_EXE_FILE":        1120,
		"AT_HIDE_MODULE":          1121,
		"AT_KILL":                 1122,
	}
)

func (x ActionType) Enum() *ActionType {
	p := new(ActionType)
	*p = x
	return p
}

func (x ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[2].Descriptor()
}

func (ActionType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[2]
}

func (x ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActionType.Descriptor instead.
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{2}
}

type NetProto int32

const (
	NetProto_NP_DEFAULT     NetProto = 0
	NetProto_NF_IPPROTO_TCP NetProto = 6
	NetProto_NF_IPPROTO_UDP NetProto = 17
)

// Enum value maps for NetProto.
var (
	NetProto_name = map[int32]string{
		0:  "NP_DEFAULT",
		6:  "NF_IPPROTO_TCP",
		17: "NF_IPPROTO_UDP",
	}
	NetProto_value = map[string]int32{
		"NP_DEFAULT":     0,
		"NF_IPPROTO_TCP": 6,
		"NF_IPPROTO_UDP": 17,
	}
)

func (x NetProto) Enum() *NetProto {
	p := new(NetProto)
	*p = x
	return p
}

func (x NetProto) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetProto) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[3].Descriptor()
}

func (NetProto) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[3]
}

func (x NetProto) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetProto.Descriptor instead.
func (NetProto) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{3}
}

type NetFamilies int32

const (
	NetFamilies_NF_DEFAULT  NetFamilies = 0
	NetFamilies_NF_AF_INET  NetFamilies = 2  //ipv4
	NetFamilies_NF_AF_INET6 NetFamilies = 10 //ipv6
)

// Enum value maps for NetFamilies.
var (
	NetFamilies_name = map[int32]string{
		0:  "NF_DEFAULT",
		2:  "NF_AF_INET",
		10: "NF_AF_INET6",
	}
	NetFamilies_value = map[string]int32{
		"NF_DEFAULT":  0,
		"NF_AF_INET":  2,
		"NF_AF_INET6": 10,
	}
)

func (x NetFamilies) Enum() *NetFamilies {
	p := new(NetFamilies)
	*p = x
	return p
}

func (x NetFamilies) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetFamilies) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[4].Descriptor()
}

func (NetFamilies) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[4]
}

func (x NetFamilies) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetFamilies.Descriptor instead.
func (NetFamilies) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{4}
}

type StatusType int32

const (
	StatusType_ST_UNKNOWN     StatusType = 0 //未知状态
	StatusType_ST_EXIST       StatusType = 1 //存在
	StatusType_ST_NONEXIST    StatusType = 2 //不存在
	StatusType_ST_QUERYFAILED StatusType = 3 //查询失败
)

// Enum value maps for StatusType.
var (
	StatusType_name = map[int32]string{
		0: "ST_UNKNOWN",
		1: "ST_EXIST",
		2: "ST_NONEXIST",
		3: "ST_QUERYFAILED",
	}
	StatusType_value = map[string]int32{
		"ST_UNKNOWN":     0,
		"ST_EXIST":       1,
		"ST_NONEXIST":    2,
		"ST_QUERYFAILED": 3,
	}
)

func (x StatusType) Enum() *StatusType {
	p := new(StatusType)
	*p = x
	return p
}

func (x StatusType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[5].Descriptor()
}

func (StatusType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[5]
}

func (x StatusType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusType.Descriptor instead.
func (StatusType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{5}
}

type ProtectType int32

const (
	ProtectType_PT_PAGE_UNDEFINED         ProtectType = 0
	ProtectType_PT_PAGE_NOACCESS          ProtectType = 1 // 无法访问
	ProtectType_PT_PAGE_READONLY          ProtectType = 2 // 只读
	ProtectType_PT_PAGE_READWRITE         ProtectType = 3 // 读写
	ProtectType_PT_PAGE_EXECUTE           ProtectType = 4 // 执行
	ProtectType_PT_PAGE_EXECUTE_READ      ProtectType = 5 // 执行和读取
	ProtectType_PT_PAGE_EXECUTE_READWRITE ProtectType = 6 // 执行, 读取和写入
	ProtectType_PT_PAGE_EXECUTE_WRITECOPY ProtectType = 7 // 执行和写入, 写时复制
)

// Enum value maps for ProtectType.
var (
	ProtectType_name = map[int32]string{
		0: "PT_PAGE_UNDEFINED",
		1: "PT_PAGE_NOACCESS",
		2: "PT_PAGE_READONLY",
		3: "PT_PAGE_READWRITE",
		4: "PT_PAGE_EXECUTE",
		5: "PT_PAGE_EXECUTE_READ",
		6: "PT_PAGE_EXECUTE_READWRITE",
		7: "PT_PAGE_EXECUTE_WRITECOPY",
	}
	ProtectType_value = map[string]int32{
		"PT_PAGE_UNDEFINED":         0,
		"PT_PAGE_NOACCESS":          1,
		"PT_PAGE_READONLY":          2,
		"PT_PAGE_READWRITE":         3,
		"PT_PAGE_EXECUTE":           4,
		"PT_PAGE_EXECUTE_READ":      5,
		"PT_PAGE_EXECUTE_READWRITE": 6,
		"PT_PAGE_EXECUTE_WRITECOPY": 7,
	}
)

func (x ProtectType) Enum() *ProtectType {
	p := new(ProtectType)
	*p = x
	return p
}

func (x ProtectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProtectType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[6].Descriptor()
}

func (ProtectType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[6]
}

func (x ProtectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProtectType.Descriptor instead.
func (ProtectType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{6}
}

type AllocationType int32

const (
	AllocationType_AT_MEM_UNDEFINED AllocationType = 0
	AllocationType_AT_MEM_COMMIT    AllocationType = 1 // 分配内存并将内容初始化为零
	AllocationType_AT_MEM_RESERVE   AllocationType = 2 // 仅为内存保留地址空间, 不实际分配物理内存
	AllocationType_AT_MEM_RESET     AllocationType = 3 // 重置已分配的页面
)

// Enum value maps for AllocationType.
var (
	AllocationType_name = map[int32]string{
		0: "AT_MEM_UNDEFINED",
		1: "AT_MEM_COMMIT",
		2: "AT_MEM_RESERVE",
		3: "AT_MEM_RESET",
	}
	AllocationType_value = map[string]int32{
		"AT_MEM_UNDEFINED": 0,
		"AT_MEM_COMMIT":    1,
		"AT_MEM_RESERVE":   2,
		"AT_MEM_RESET":     3,
	}
)

func (x AllocationType) Enum() *AllocationType {
	p := new(AllocationType)
	*p = x
	return p
}

func (x AllocationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AllocationType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[7].Descriptor()
}

func (AllocationType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[7]
}

func (x AllocationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AllocationType.Descriptor instead.
func (AllocationType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{7}
}

type WriteMemRiskMode int32

const (
	WriteMemRiskMode_WMRM_DEFAULT WriteMemRiskMode = 0 // 在默认位置写
	WriteMemRiskMode_WMRM_PEB     WriteMemRiskMode = 1 // 在 Peb 位置写
)

// Enum value maps for WriteMemRiskMode.
var (
	WriteMemRiskMode_name = map[int32]string{
		0: "WMRM_DEFAULT",
		1: "WMRM_PEB",
	}
	WriteMemRiskMode_value = map[string]int32{
		"WMRM_DEFAULT": 0,
		"WMRM_PEB":     1,
	}
)

func (x WriteMemRiskMode) Enum() *WriteMemRiskMode {
	p := new(WriteMemRiskMode)
	*p = x
	return p
}

func (x WriteMemRiskMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WriteMemRiskMode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[8].Descriptor()
}

func (WriteMemRiskMode) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[8]
}

func (x WriteMemRiskMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WriteMemRiskMode.Descriptor instead.
func (WriteMemRiskMode) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{8}
}

type V01ActionType int32

const (
	V01ActionType_V01AT_DEFAULT                     V01ActionType = 0
	V01ActionType_V01AT_PROCESS_CREATE              V01ActionType = 1
	V01ActionType_V01AT_PROCESS_EXIT                V01ActionType = 2
	V01ActionType_V01AT_SCRIPT_SCHEDULE_CREATE      V01ActionType = 3    // 脚本方式创建计划任务， 路径：%s 参数：%s
	V01ActionType_V01AT_SCRIPT_GET_API_ADDR         V01ActionType = 11   // 脚本方式获取API地址 api名称：%s
	V01ActionType_V01AT_SCRIPT_WIN32_SHARE          V01ActionType = 16   // 脚本方式共享文件，共享名称：%s 路径：%s
	V01ActionType_V01AT_WMI_TERMINATE_PROCESS       V01ActionType = 30   // wmi方式结束进程 pid:%d 进程路径：%s
	V01ActionType_V01AT_WMI_REG_OPER                V01ActionType = 31   // wmi方式操作注册表, 操作方式：%s， 操作路径：%s， 设置值：%s，
	V01ActionType_V01AT_WMI_SERVICE_OPER            V01ActionType = 32   // wmi方式操作服务， 服务名：%s，操作方式：%s 服务路径：%s
	V01ActionType_V01AT_WMI_QUERY                   V01ActionType = 33   // 调用wmi查询命令，语言方式：%s， 查询命令：%s
	V01ActionType_V01AT_SCRIPT_AMSI_BY_INIT_FAILED  V01ActionType = 34   // 无结构体，过AMSI保护-通过设置InitFailed字段
	V01ActionType_V01AT_SCRIPT_AMSI_BY_AMSI_CONTEXT V01ActionType = 35   // 过AMSI保护-通过修改AMSICONTEXT结构体
	V01ActionType_V01AT_SCRIPT_AMSI_DLLHIJACK       V01ActionType = 36   // 通过AMSI方式Dll劫持,Dll名称%s
	V01ActionType_V01AT_SCRIPT_KEY_LOGGER           V01ActionType = 37   // 无结构体，脚本方式进行键盘记录
	V01ActionType_V01AT_SCRIPT_SCREEN_SHOT          V01ActionType = 38   // 无结构体，脚本方式进行屏幕截图
	V01ActionType_V01AT_SCRIPT_EMAIL                V01ActionType = 39   // 脚本方式发送邮件，发送者：%s ,接收者 %s
	V01ActionType_V01AT_LOG_CLEAR                   V01ActionType = 40   // 清空日志文件%s
	V01ActionType_V01AT_QUEUE_APC                   V01ActionType = 41   // 向进程的APC队列投递任务
	V01ActionType_V01AT_SET_CONTEXT_THREAD          V01ActionType = 42   // 设置线程上下文
	V01ActionType_V01AT_KILL_PROCESS                V01ActionType = 43   // 杀死进程
	V01ActionType_V01AT_PROCESS_INJECT              V01ActionType = 44   // 进程注入
	V01ActionType_V01AT_FILE_HIDE                   V01ActionType = 45   // 文件隐藏
	V01ActionType_V01AT_FILE_READONLY               V01ActionType = 46   // 文件只读
	V01ActionType_V01AT_READ_MEM_DETAIL             V01ActionType = 50   // 读虚拟内存
	V01ActionType_V01AT_WRITE_MEM_DETAIL            V01ActionType = 51   // 写虚拟内存
	V01ActionType_V01AT_ALLOC_MEM_DETAIL            V01ActionType = 52   // 虚拟内存分配
	V01ActionType_V01AT_PROTECT_MEM_DETAIL          V01ActionType = 58   // 更改虚拟内存的保护
	V01ActionType_V01AT_MAP_VIEW_SECTION_DETAIL     V01ActionType = 59   // 虚拟内存映射
	V01ActionType_V01AT_DELETE_BYSELF               V01ActionType = 60   // 进程自删除 (windows) 文件节点
	V01ActionType_V01AT_MEM_HEAP_SPRAY              V01ActionType = 501  // 堆喷射
	V01ActionType_V01AT_MEM_ROP                     V01ActionType = 502  // ROP攻击
	V01ActionType_V01AT_LOAD_REMOTE_MODULE          V01ActionType = 503  // 加载远程模块
	V01ActionType_V01AT_MEM_LAYOUT_SHELL_CODE       V01ActionType = 504  // 布局shellcode
	V01ActionType_V01AT_MEM_EXEC_SHELL_CODE         V01ActionType = 505  // 执行shellcode
	V01ActionType_V01AT_MEM_ENGINE_ATTACK           V01ActionType = 506  // 引擎攻击
	V01ActionType_V01AT_STACK_ATTRIBUTE_ATTACK      V01ActionType = 507  // 栈属性攻击
	V01ActionType_V01AT_STACK_CODE_EXEC             V01ActionType = 508  // 栈代码执行
	V01ActionType_V01AT_MEM_STACK_OVERTRUN          V01ActionType = 509  // 栈翻转
	V01ActionType_V01AT_CVE_ATTACK                  V01ActionType = 601  // cve
	V01ActionType_V01AT_REMOTE_BUG_OVERFLOW         V01ActionType = 602  // 远程漏洞溢出
	V01ActionType_V01AT_PUPPET_PROCESS              V01ActionType = 603  // 傀儡进程
	V01ActionType_V01AT_WHITE_ADD_BLACK             V01ActionType = 604  // 白加黑
	V01ActionType_V01AT_SIGN_LEN_ABNORMAL           V01ActionType = 605  // 签名长度异常
	V01ActionType_V01AT_OPEN_DEVICE_OBJECT          V01ActionType = 610  // 打开恶意驱动设备对象
	V01ActionType_V01AT_CREATE_SERVICE              V01ActionType = 611  // 创建服务
	V01ActionType_V01AT_START_SERVICE               V01ActionType = 612  // 启动服务
	V01ActionType_V01AT_CREATE_SCHEDULER            V01ActionType = 613  // 创建计划任务
	V01ActionType_V01AT_START_SCHEDULER             V01ActionType = 614  // 启动计划任务
	V01ActionType_V01AT_UNKNOWN                     V01ActionType = 1001 // 未知行为
	V01ActionType_V01AT_FILE_CREATE                 V01ActionType = 1002 // 文件创建
	V01ActionType_V01AT_FILE_READ                   V01ActionType = 1003 // 文件读取
	V01ActionType_V01AT_FILE_WRITE                  V01ActionType = 1004 // 文件写
	V01ActionType_V01AT_FILE_RENAME                 V01ActionType = 1005 // 文件重命名
	V01ActionType_V01AT_FILE_DELETE                 V01ActionType = 1006 // 文件删除
	V01ActionType_V01AT_FILE_LINK                   V01ActionType = 1007 // 创建文件连接
	V01ActionType_V01AT_ICTL_IMMUTABLE              V01ActionType = 1008 // 通过ioctl操作文件
	V01ActionType_V01AT_RESTORE_UTIME               V01ActionType = 1009 // 恢复文件时间属性
	V01ActionType_V01AT_MAKEDIR                     V01ActionType = 1010 // 创建目录
	V01ActionType_V01AT_SYMLINK                     V01ActionType = 1011 // 创建符号链接
	V01ActionType_V01AT_SET_UID                     V01ActionType = 1012 // 修改文件uid
	V01ActionType_V01AT_MODE_CHANGE                 V01ActionType = 1014 // 修改文件权限
	V01ActionType_V01AT_SCRIPT_IMAGE_LOAD           V01ActionType = 1100 // 脚本方式加载dll，路径：%s
	V01ActionType_V01AT_SCRIPT_RUN_WMIC_CODE        V01ActionType = 1101 // 脚本方式执行wmic命令，命令：%s
	V01ActionType_V01AT_FILELESS_ATTACK             V01ActionType = 1109 //无文件攻击
	V01ActionType_V01AT_NET_SNIFFER                 V01ActionType = 1110 //网络抓包行为
	V01ActionType_V01AT_ENV_HIJACK                  V01ActionType = 1111 //环境变量劫持
	V01ActionType_V01AT_SELF_DELETE                 V01ActionType = 1112 // 自删除 (linux) 只能查到文件路径, 没有完整文件节点
	V01ActionType_V01AT_FILE_OPEN                   V01ActionType = 1113 //文件打开
	V01ActionType_V01AT_CALL_USERMODEHELPER         V01ActionType = 1114 //内核执行用户态进程。
	V01ActionType_V01AT_REVERSE_SHELL               V01ActionType = 1115 //反弹shell
	V01ActionType_V01AT_PROCESS_EXEC                V01ActionType = 1116 //exec
	V01ActionType_V01AT_PTRACE                      V01ActionType = 1117 //读写进程内存
	V01ActionType_V01AT_BPF                         V01ActionType = 1118 //bpf技术
	V01ActionType_V01AT_PRIVILEGE_ESCALATION        V01ActionType = 1119 //令牌提升
	V01ActionType_V01AT_FAKE_EXE_FILE               V01ActionType = 1120 //伪装进程执行路径
	V01ActionType_V01AT_HIDE_MODULE                 V01ActionType = 1121 //隐藏模块
	V01ActionType_V01AT_KILL                        V01ActionType = 1122 //kill信号
	V01ActionType_V01AT_SETRLIMIT                   V01ActionType = 1123 //设置资源限制
	V01ActionType_V01AT_NET_ACCEPT                  V01ActionType = 1200
	V01ActionType_V01AT_NET_CONNECT                 V01ActionType = 1201
	V01ActionType_V01AT_NET_LISTEN                  V01ActionType = 1202 // 端口监听，监听地址：%s 监听端口 ：%d
	V01ActionType_V01AT_REG_CREATE                  V01ActionType = 1203 // 注册表创建
	V01ActionType_V01AT_PROCESS_PROMOTED_PRIVILEGE  V01ActionType = 1204 // 无结构体，当前进程（process_unique）提权
	V01ActionType_V01AT_SCRIPT_HTTP                 V01ActionType = 1205 // 脚本方式发起http请求, url：%s
	V01ActionType_V01AT_REG_DELETE                  V01ActionType = 1206 // 注册表删除
	V01ActionType_V01AT_REG_WRITE                   V01ActionType = 1207 // 注册表写入
	V01ActionType_V01AT_REG_SET_SECURITY            V01ActionType = 1208 // 设置注册表权限
	V01ActionType_V01AT_NET_CONNECT_DOMAIN          V01ActionType = 1209 // 通过域名创建网络连接
)

// Enum value maps for V01ActionType.
var (
	V01ActionType_name = map[int32]string{
		0:    "V01AT_DEFAULT",
		1:    "V01AT_PROCESS_CREATE",
		2:    "V01AT_PROCESS_EXIT",
		3:    "V01AT_SCRIPT_SCHEDULE_CREATE",
		11:   "V01AT_SCRIPT_GET_API_ADDR",
		16:   "V01AT_SCRIPT_WIN32_SHARE",
		30:   "V01AT_WMI_TERMINATE_PROCESS",
		31:   "V01AT_WMI_REG_OPER",
		32:   "V01AT_WMI_SERVICE_OPER",
		33:   "V01AT_WMI_QUERY",
		34:   "V01AT_SCRIPT_AMSI_BY_INIT_FAILED",
		35:   "V01AT_SCRIPT_AMSI_BY_AMSI_CONTEXT",
		36:   "V01AT_SCRIPT_AMSI_DLLHIJACK",
		37:   "V01AT_SCRIPT_KEY_LOGGER",
		38:   "V01AT_SCRIPT_SCREEN_SHOT",
		39:   "V01AT_SCRIPT_EMAIL",
		40:   "V01AT_LOG_CLEAR",
		41:   "V01AT_QUEUE_APC",
		42:   "V01AT_SET_CONTEXT_THREAD",
		43:   "V01AT_KILL_PROCESS",
		44:   "V01AT_PROCESS_INJECT",
		45:   "V01AT_FILE_HIDE",
		46:   "V01AT_FILE_READONLY",
		50:   "V01AT_READ_MEM_DETAIL",
		51:   "V01AT_WRITE_MEM_DETAIL",
		52:   "V01AT_ALLOC_MEM_DETAIL",
		58:   "V01AT_PROTECT_MEM_DETAIL",
		59:   "V01AT_MAP_VIEW_SECTION_DETAIL",
		60:   "V01AT_DELETE_BYSELF",
		501:  "V01AT_MEM_HEAP_SPRAY",
		502:  "V01AT_MEM_ROP",
		503:  "V01AT_LOAD_REMOTE_MODULE",
		504:  "V01AT_MEM_LAYOUT_SHELL_CODE",
		505:  "V01AT_MEM_EXEC_SHELL_CODE",
		506:  "V01AT_MEM_ENGINE_ATTACK",
		507:  "V01AT_STACK_ATTRIBUTE_ATTACK",
		508:  "V01AT_STACK_CODE_EXEC",
		509:  "V01AT_MEM_STACK_OVERTRUN",
		601:  "V01AT_CVE_ATTACK",
		602:  "V01AT_REMOTE_BUG_OVERFLOW",
		603:  "V01AT_PUPPET_PROCESS",
		604:  "V01AT_WHITE_ADD_BLACK",
		605:  "V01AT_SIGN_LEN_ABNORMAL",
		610:  "V01AT_OPEN_DEVICE_OBJECT",
		611:  "V01AT_CREATE_SERVICE",
		612:  "V01AT_START_SERVICE",
		613:  "V01AT_CREATE_SCHEDULER",
		614:  "V01AT_START_SCHEDULER",
		1001: "V01AT_UNKNOWN",
		1002: "V01AT_FILE_CREATE",
		1003: "V01AT_FILE_READ",
		1004: "V01AT_FILE_WRITE",
		1005: "V01AT_FILE_RENAME",
		1006: "V01AT_FILE_DELETE",
		1007: "V01AT_FILE_LINK",
		1008: "V01AT_ICTL_IMMUTABLE",
		1009: "V01AT_RESTORE_UTIME",
		1010: "V01AT_MAKEDIR",
		1011: "V01AT_SYMLINK",
		1012: "V01AT_SET_UID",
		1014: "V01AT_MODE_CHANGE",
		1100: "V01AT_SCRIPT_IMAGE_LOAD",
		1101: "V01AT_SCRIPT_RUN_WMIC_CODE",
		1109: "V01AT_FILELESS_ATTACK",
		1110: "V01AT_NET_SNIFFER",
		1111: "V01AT_ENV_HIJACK",
		1112: "V01AT_SELF_DELETE",
		1113: "V01AT_FILE_OPEN",
		1114: "V01AT_CALL_USERMODEHELPER",
		1115: "V01AT_REVERSE_SHELL",
		1116: "V01AT_PROCESS_EXEC",
		1117: "V01AT_PTRACE",
		1118: "V01AT_BPF",
		1119: "V01AT_PRIVILEGE_ESCALATION",
		1120: "V01AT_FAKE_EXE_FILE",
		1121: "V01AT_HIDE_MODULE",
		1122: "V01AT_KILL",
		1123: "V01AT_SETRLIMIT",
		1200: "V01AT_NET_ACCEPT",
		1201: "V01AT_NET_CONNECT",
		1202: "V01AT_NET_LISTEN",
		1203: "V01AT_REG_CREATE",
		1204: "V01AT_PROCESS_PROMOTED_PRIVILEGE",
		1205: "V01AT_SCRIPT_HTTP",
		1206: "V01AT_REG_DELETE",
		1207: "V01AT_REG_WRITE",
		1208: "V01AT_REG_SET_SECURITY",
		1209: "V01AT_NET_CONNECT_DOMAIN",
	}
	V01ActionType_value = map[string]int32{
		"V01AT_DEFAULT":                     0,
		"V01AT_PROCESS_CREATE":              1,
		"V01AT_PROCESS_EXIT":                2,
		"V01AT_SCRIPT_SCHEDULE_CREATE":      3,
		"V01AT_SCRIPT_GET_API_ADDR":         11,
		"V01AT_SCRIPT_WIN32_SHARE":          16,
		"V01AT_WMI_TERMINATE_PROCESS":       30,
		"V01AT_WMI_REG_OPER":                31,
		"V01AT_WMI_SERVICE_OPER":            32,
		"V01AT_WMI_QUERY":                   33,
		"V01AT_SCRIPT_AMSI_BY_INIT_FAILED":  34,
		"V01AT_SCRIPT_AMSI_BY_AMSI_CONTEXT": 35,
		"V01AT_SCRIPT_AMSI_DLLHIJACK":       36,
		"V01AT_SCRIPT_KEY_LOGGER":           37,
		"V01AT_SCRIPT_SCREEN_SHOT":          38,
		"V01AT_SCRIPT_EMAIL":                39,
		"V01AT_LOG_CLEAR":                   40,
		"V01AT_QUEUE_APC":                   41,
		"V01AT_SET_CONTEXT_THREAD":          42,
		"V01AT_KILL_PROCESS":                43,
		"V01AT_PROCESS_INJECT":              44,
		"V01AT_FILE_HIDE":                   45,
		"V01AT_FILE_READONLY":               46,
		"V01AT_READ_MEM_DETAIL":             50,
		"V01AT_WRITE_MEM_DETAIL":            51,
		"V01AT_ALLOC_MEM_DETAIL":            52,
		"V01AT_PROTECT_MEM_DETAIL":          58,
		"V01AT_MAP_VIEW_SECTION_DETAIL":     59,
		"V01AT_DELETE_BYSELF":               60,
		"V01AT_MEM_HEAP_SPRAY":              501,
		"V01AT_MEM_ROP":                     502,
		"V01AT_LOAD_REMOTE_MODULE":          503,
		"V01AT_MEM_LAYOUT_SHELL_CODE":       504,
		"V01AT_MEM_EXEC_SHELL_CODE":         505,
		"V01AT_MEM_ENGINE_ATTACK":           506,
		"V01AT_STACK_ATTRIBUTE_ATTACK":      507,
		"V01AT_STACK_CODE_EXEC":             508,
		"V01AT_MEM_STACK_OVERTRUN":          509,
		"V01AT_CVE_ATTACK":                  601,
		"V01AT_REMOTE_BUG_OVERFLOW":         602,
		"V01AT_PUPPET_PROCESS":              603,
		"V01AT_WHITE_ADD_BLACK":             604,
		"V01AT_SIGN_LEN_ABNORMAL":           605,
		"V01AT_OPEN_DEVICE_OBJECT":          610,
		"V01AT_CREATE_SERVICE":              611,
		"V01AT_START_SERVICE":               612,
		"V01AT_CREATE_SCHEDULER":            613,
		"V01AT_START_SCHEDULER":             614,
		"V01AT_UNKNOWN":                     1001,
		"V01AT_FILE_CREATE":                 1002,
		"V01AT_FILE_READ":                   1003,
		"V01AT_FILE_WRITE":                  1004,
		"V01AT_FILE_RENAME":                 1005,
		"V01AT_FILE_DELETE":                 1006,
		"V01AT_FILE_LINK":                   1007,
		"V01AT_ICTL_IMMUTABLE":              1008,
		"V01AT_RESTORE_UTIME":               1009,
		"V01AT_MAKEDIR":                     1010,
		"V01AT_SYMLINK":                     1011,
		"V01AT_SET_UID":                     1012,
		"V01AT_MODE_CHANGE":                 1014,
		"V01AT_SCRIPT_IMAGE_LOAD":           1100,
		"V01AT_SCRIPT_RUN_WMIC_CODE":        1101,
		"V01AT_FILELESS_ATTACK":             1109,
		"V01AT_NET_SNIFFER":                 1110,
		"V01AT_ENV_HIJACK":                  1111,
		"V01AT_SELF_DELETE":                 1112,
		"V01AT_FILE_OPEN":                   1113,
		"V01AT_CALL_USERMODEHELPER":         1114,
		"V01AT_REVERSE_SHELL":               1115,
		"V01AT_PROCESS_EXEC":                1116,
		"V01AT_PTRACE":                      1117,
		"V01AT_BPF":                         1118,
		"V01AT_PRIVILEGE_ESCALATION":        1119,
		"V01AT_FAKE_EXE_FILE":               1120,
		"V01AT_HIDE_MODULE":                 1121,
		"V01AT_KILL":                        1122,
		"V01AT_SETRLIMIT":                   1123,
		"V01AT_NET_ACCEPT":                  1200,
		"V01AT_NET_CONNECT":                 1201,
		"V01AT_NET_LISTEN":                  1202,
		"V01AT_REG_CREATE":                  1203,
		"V01AT_PROCESS_PROMOTED_PRIVILEGE":  1204,
		"V01AT_SCRIPT_HTTP":                 1205,
		"V01AT_REG_DELETE":                  1206,
		"V01AT_REG_WRITE":                   1207,
		"V01AT_REG_SET_SECURITY":            1208,
		"V01AT_NET_CONNECT_DOMAIN":          1209,
	}
)

func (x V01ActionType) Enum() *V01ActionType {
	p := new(V01ActionType)
	*p = x
	return p
}

func (x V01ActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (V01ActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[9].Descriptor()
}

func (V01ActionType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[9]
}

func (x V01ActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use V01ActionType.Descriptor instead.
func (V01ActionType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{9}
}

type WhiteAddBlack_ReportType int32

const (
	WhiteAddBlack_UNKNOWN     WhiteAddBlack_ReportType = 0
	WhiteAddBlack_NO_CERT_NUM WhiteAddBlack_ReportType = 1 // 加载的无签名 DLL 数量大于 0 小于等于 2
	WhiteAddBlack_DLL_HIDE    WhiteAddBlack_ReportType = 2 // 加载的无签名 DLL 文件为隐藏文件
	WhiteAddBlack_SAME_TIME   WhiteAddBlack_ReportType = 3 // 加载的无签名 DLL 文件与可执行程序时间戳相同
	WhiteAddBlack_OLD_TIME    WhiteAddBlack_ReportType = 4 // 加载的无签名 DLL 文件时间戳在可执行文件之后
	WhiteAddBlack_EXE_DIR_NUM WhiteAddBlack_ReportType = 5 // 加载无签名 DLL 的可执行文件同级目录下文件数量小等于 5
)

// Enum value maps for WhiteAddBlack_ReportType.
var (
	WhiteAddBlack_ReportType_name = map[int32]string{
		0: "UNKNOWN",
		1: "NO_CERT_NUM",
		2: "DLL_HIDE",
		3: "SAME_TIME",
		4: "OLD_TIME",
		5: "EXE_DIR_NUM",
	}
	WhiteAddBlack_ReportType_value = map[string]int32{
		"UNKNOWN":     0,
		"NO_CERT_NUM": 1,
		"DLL_HIDE":    2,
		"SAME_TIME":   3,
		"OLD_TIME":    4,
		"EXE_DIR_NUM": 5,
	}
)

func (x WhiteAddBlack_ReportType) Enum() *WhiteAddBlack_ReportType {
	p := new(WhiteAddBlack_ReportType)
	*p = x
	return p
}

func (x WhiteAddBlack_ReportType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WhiteAddBlack_ReportType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_acdr_proto_enumTypes[10].Descriptor()
}

func (WhiteAddBlack_ReportType) Type() protoreflect.EnumType {
	return &file_agent_acdr_proto_enumTypes[10]
}

func (x WhiteAddBlack_ReportType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WhiteAddBlack_ReportType.Descriptor instead.
func (WhiteAddBlack_ReportType) EnumDescriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{83, 0}
}

type RuleInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateTime           *ClientID          `protobuf:"bytes,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`                                    //事件发生的大致时间
	Header             *RiskHeader        `protobuf:"bytes,2,opt,name=header,proto3" json:"header,omitempty"`                                                        //风险头
	RuleName           []byte             `protobuf:"bytes,3,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`                                    //规则名
	RiskLevel          uint32             `protobuf:"varint,4,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`                                //风险等级
	HandleMode         HandleMode         `protobuf:"varint,5,opt,name=handle_mode,json=handleMode,proto3,enum=agent.HandleMode" json:"handle_mode,omitempty"`       //控制模式（结束进程、拦截、仅上报等）
	Score              uint32             `protobuf:"varint,6,opt,name=score,proto3" json:"score,omitempty"`                                                         //分数
	RuleActionList     []*RuleActionItem  `protobuf:"bytes,7,rep,name=rule_action_list,json=ruleActionList,proto3" json:"rule_action_list,omitempty"`                //规则行为数组
	ProcessInfoList    []*NgavProcessInfo `protobuf:"bytes,8,rep,name=process_info_list,json=processInfoList,proto3" json:"process_info_list,omitempty"`             //进程信息数组，仅第一层数组有数据，嵌套结构无该信息。
	NotShowProcessInfo bool               `protobuf:"varint,9,opt,name=not_show_process_info,json=notShowProcessInfo,proto3" json:"not_show_process_info,omitempty"` //是否展示进程信息，但进程之间的关系依然存在RuleActionItem中
}

func (x *RuleInfoMessage) Reset() {
	*x = RuleInfoMessage{}
	mi := &file_agent_acdr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfoMessage) ProtoMessage() {}

func (x *RuleInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfoMessage.ProtoReflect.Descriptor instead.
func (*RuleInfoMessage) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{0}
}

func (x *RuleInfoMessage) GetDateTime() *ClientID {
	if x != nil {
		return x.DateTime
	}
	return nil
}

func (x *RuleInfoMessage) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RuleInfoMessage) GetRuleName() []byte {
	if x != nil {
		return x.RuleName
	}
	return nil
}

func (x *RuleInfoMessage) GetRiskLevel() uint32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *RuleInfoMessage) GetHandleMode() HandleMode {
	if x != nil {
		return x.HandleMode
	}
	return HandleMode_HM_NOT_SET
}

func (x *RuleInfoMessage) GetScore() uint32 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *RuleInfoMessage) GetRuleActionList() []*RuleActionItem {
	if x != nil {
		return x.RuleActionList
	}
	return nil
}

func (x *RuleInfoMessage) GetProcessInfoList() []*NgavProcessInfo {
	if x != nil {
		return x.ProcessInfoList
	}
	return nil
}

func (x *RuleInfoMessage) GetNotShowProcessInfo() bool {
	if x != nil {
		return x.NotShowProcessInfo
	}
	return false
}

type NgavProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid                  uint32 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                                                                 //进程id
	Ppid                 uint32 `protobuf:"varint,2,opt,name=ppid,proto3" json:"ppid,omitempty"`                                                               //父进程id
	StartTime            uint64 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                                    //进程启动时间
	ExitTime             uint64 `protobuf:"varint,4,opt,name=exit_time,json=exitTime,proto3" json:"exit_time,omitempty"`                                       //进程退出时间
	ProcessPath          []byte `protobuf:"bytes,6,opt,name=process_path,json=processPath,proto3" json:"process_path,omitempty"`                               //进程路径
	Command              []byte `protobuf:"bytes,7,opt,name=command,proto3" json:"command,omitempty"`                                                          //命名行
	IsExist              bool   `protobuf:"varint,8,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`                                          //进程是否存在
	FileMd5              []byte `protobuf:"bytes,9,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`                                           //文件MD5
	User                 []byte `protobuf:"bytes,10,opt,name=user,proto3" json:"user,omitempty"`                                                               //进程所属用户
	FileAccessPermission []byte `protobuf:"bytes,11,opt,name=file_access_permission,json=fileAccessPermission,proto3" json:"file_access_permission,omitempty"` //文件访问权限
	FileSize             uint32 `protobuf:"varint,12,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                                      //文件大小
	FileMtime            []byte `protobuf:"bytes,13,opt,name=file_mtime,json=fileMtime,proto3" json:"file_mtime,omitempty"`                                    //文件最后一次修改时间
	FileCtime            []byte `protobuf:"bytes,14,opt,name=file_ctime,json=fileCtime,proto3" json:"file_ctime,omitempty"`                                    //文件最后一次属性改变时间
	FileAtime            []byte `protobuf:"bytes,15,opt,name=file_atime,json=fileAtime,proto3" json:"file_atime,omitempty"`                                    //文件最后一次访问时间
	IsKilled             bool   `protobuf:"varint,16,opt,name=is_killed,json=isKilled,proto3" json:"is_killed,omitempty"`                                      //进程是否被杀死
	PpidStartTime        uint64 `protobuf:"varint,17,opt,name=ppid_start_time,json=ppidStartTime,proto3" json:"ppid_start_time,omitempty"`                     //父进程的启动时间
}

func (x *NgavProcessInfo) Reset() {
	*x = NgavProcessInfo{}
	mi := &file_agent_acdr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NgavProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NgavProcessInfo) ProtoMessage() {}

func (x *NgavProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NgavProcessInfo.ProtoReflect.Descriptor instead.
func (*NgavProcessInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{1}
}

func (x *NgavProcessInfo) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *NgavProcessInfo) GetPpid() uint32 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *NgavProcessInfo) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *NgavProcessInfo) GetExitTime() uint64 {
	if x != nil {
		return x.ExitTime
	}
	return 0
}

func (x *NgavProcessInfo) GetProcessPath() []byte {
	if x != nil {
		return x.ProcessPath
	}
	return nil
}

func (x *NgavProcessInfo) GetCommand() []byte {
	if x != nil {
		return x.Command
	}
	return nil
}

func (x *NgavProcessInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

func (x *NgavProcessInfo) GetFileMd5() []byte {
	if x != nil {
		return x.FileMd5
	}
	return nil
}

func (x *NgavProcessInfo) GetUser() []byte {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *NgavProcessInfo) GetFileAccessPermission() []byte {
	if x != nil {
		return x.FileAccessPermission
	}
	return nil
}

func (x *NgavProcessInfo) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *NgavProcessInfo) GetFileMtime() []byte {
	if x != nil {
		return x.FileMtime
	}
	return nil
}

func (x *NgavProcessInfo) GetFileCtime() []byte {
	if x != nil {
		return x.FileCtime
	}
	return nil
}

func (x *NgavProcessInfo) GetFileAtime() []byte {
	if x != nil {
		return x.FileAtime
	}
	return nil
}

func (x *NgavProcessInfo) GetIsKilled() bool {
	if x != nil {
		return x.IsKilled
	}
	return false
}

func (x *NgavProcessInfo) GetPpidStartTime() uint64 {
	if x != nil {
		return x.PpidStartTime
	}
	return 0
}

type FileCreateContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //文件创建路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *FileCreateContentInfo) Reset() {
	*x = FileCreateContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileCreateContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileCreateContentInfo) ProtoMessage() {}

func (x *FileCreateContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileCreateContentInfo.ProtoReflect.Descriptor instead.
func (*FileCreateContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{2}
}

func (x *FileCreateContentInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileCreateContentInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type FileOpenContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //文件打开路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *FileOpenContentInfo) Reset() {
	*x = FileOpenContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileOpenContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileOpenContentInfo) ProtoMessage() {}

func (x *FileOpenContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileOpenContentInfo.ProtoReflect.Descriptor instead.
func (*FileOpenContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{3}
}

func (x *FileOpenContentInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileOpenContentInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type FileWriteContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //写文件路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *FileWriteContentInfo) Reset() {
	*x = FileWriteContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileWriteContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileWriteContentInfo) ProtoMessage() {}

func (x *FileWriteContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileWriteContentInfo.ProtoReflect.Descriptor instead.
func (*FileWriteContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{4}
}

func (x *FileWriteContentInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileWriteContentInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type FileRenameContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePathSrc  []byte `protobuf:"bytes,1,opt,name=file_path_src,json=filePathSrc,proto3" json:"file_path_src,omitempty"`    //文件重命名原路径
	FilePathDest []byte `protobuf:"bytes,2,opt,name=file_path_dest,json=filePathDest,proto3" json:"file_path_dest,omitempty"` //文件重命名目标路径
	IsSrcExist   bool   `protobuf:"varint,3,opt,name=is_src_exist,json=isSrcExist,proto3" json:"is_src_exist,omitempty"`      //原文件是否存在
	IsDestExist  bool   `protobuf:"varint,4,opt,name=is_dest_exist,json=isDestExist,proto3" json:"is_dest_exist,omitempty"`   //目标文件是否存在
}

func (x *FileRenameContentInfo) Reset() {
	*x = FileRenameContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileRenameContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileRenameContentInfo) ProtoMessage() {}

func (x *FileRenameContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileRenameContentInfo.ProtoReflect.Descriptor instead.
func (*FileRenameContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{5}
}

func (x *FileRenameContentInfo) GetFilePathSrc() []byte {
	if x != nil {
		return x.FilePathSrc
	}
	return nil
}

func (x *FileRenameContentInfo) GetFilePathDest() []byte {
	if x != nil {
		return x.FilePathDest
	}
	return nil
}

func (x *FileRenameContentInfo) GetIsSrcExist() bool {
	if x != nil {
		return x.IsSrcExist
	}
	return false
}

func (x *FileRenameContentInfo) GetIsDestExist() bool {
	if x != nil {
		return x.IsDestExist
	}
	return false
}

type FileDeleteContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //文件删除路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *FileDeleteContentInfo) Reset() {
	*x = FileDeleteContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDeleteContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDeleteContentInfo) ProtoMessage() {}

func (x *FileDeleteContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDeleteContentInfo.ProtoReflect.Descriptor instead.
func (*FileDeleteContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{6}
}

func (x *FileDeleteContentInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileDeleteContentInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type FileModeChangeContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileInfo   *NodeFileInfo `protobuf:"bytes,1,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
	ChangeInfo []byte        `protobuf:"bytes,2,opt,name=change_info,json=changeInfo,proto3" json:"change_info,omitempty"` //改变信息
}

func (x *FileModeChangeContentInfo) Reset() {
	*x = FileModeChangeContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileModeChangeContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileModeChangeContentInfo) ProtoMessage() {}

func (x *FileModeChangeContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileModeChangeContentInfo.ProtoReflect.Descriptor instead.
func (*FileModeChangeContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{7}
}

func (x *FileModeChangeContentInfo) GetFileInfo() *NodeFileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *FileModeChangeContentInfo) GetChangeInfo() []byte {
	if x != nil {
		return x.ChangeInfo
	}
	return nil
}

type FileIoctlImmutableContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //文件路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *FileIoctlImmutableContentInfo) Reset() {
	*x = FileIoctlImmutableContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileIoctlImmutableContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileIoctlImmutableContentInfo) ProtoMessage() {}

func (x *FileIoctlImmutableContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileIoctlImmutableContentInfo.ProtoReflect.Descriptor instead.
func (*FileIoctlImmutableContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{8}
}

func (x *FileIoctlImmutableContentInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileIoctlImmutableContentInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type Mkdir struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //目录路径
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *Mkdir) Reset() {
	*x = Mkdir{}
	mi := &file_agent_acdr_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Mkdir) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Mkdir) ProtoMessage() {}

func (x *Mkdir) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Mkdir.ProtoReflect.Descriptor instead.
func (*Mkdir) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{9}
}

func (x *Mkdir) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *Mkdir) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type FileSymlink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileInfoSrc  *NodeFileInfo `protobuf:"bytes,1,opt,name=file_info_src,json=fileInfoSrc,proto3" json:"file_info_src,omitempty"`
	FileInfoDest *NodeFileInfo `protobuf:"bytes,2,opt,name=file_info_dest,json=fileInfoDest,proto3" json:"file_info_dest,omitempty"`
}

func (x *FileSymlink) Reset() {
	*x = FileSymlink{}
	mi := &file_agent_acdr_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSymlink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSymlink) ProtoMessage() {}

func (x *FileSymlink) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSymlink.ProtoReflect.Descriptor instead.
func (*FileSymlink) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{10}
}

func (x *FileSymlink) GetFileInfoSrc() *NodeFileInfo {
	if x != nil {
		return x.FileInfoSrc
	}
	return nil
}

func (x *FileSymlink) GetFileInfoDest() *NodeFileInfo {
	if x != nil {
		return x.FileInfoDest
	}
	return nil
}

type FileSetUid struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //文件路径
}

func (x *FileSetUid) Reset() {
	*x = FileSetUid{}
	mi := &file_agent_acdr_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSetUid) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSetUid) ProtoMessage() {}

func (x *FileSetUid) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSetUid.ProtoReflect.Descriptor instead.
func (*FileSetUid) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{11}
}

func (x *FileSetUid) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

type NetContentBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalIp       []byte      `protobuf:"bytes,1,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`
	LocalPort     uint32      `protobuf:"varint,2,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`
	RemoteIp      []byte      `protobuf:"bytes,3,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`
	RemotePort    uint32      `protobuf:"varint,4,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`
	AddressFamily NetFamilies `protobuf:"varint,5,opt,name=address_family,json=addressFamily,proto3,enum=agent.NetFamilies" json:"address_family,omitempty"`
	Protocol      NetProto    `protobuf:"varint,6,opt,name=protocol,proto3,enum=agent.NetProto" json:"protocol,omitempty"`
}

func (x *NetContentBaseInfo) Reset() {
	*x = NetContentBaseInfo{}
	mi := &file_agent_acdr_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetContentBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetContentBaseInfo) ProtoMessage() {}

func (x *NetContentBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetContentBaseInfo.ProtoReflect.Descriptor instead.
func (*NetContentBaseInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{12}
}

func (x *NetContentBaseInfo) GetLocalIp() []byte {
	if x != nil {
		return x.LocalIp
	}
	return nil
}

func (x *NetContentBaseInfo) GetLocalPort() uint32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

func (x *NetContentBaseInfo) GetRemoteIp() []byte {
	if x != nil {
		return x.RemoteIp
	}
	return nil
}

func (x *NetContentBaseInfo) GetRemotePort() uint32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *NetContentBaseInfo) GetAddressFamily() NetFamilies {
	if x != nil {
		return x.AddressFamily
	}
	return NetFamilies_NF_DEFAULT
}

func (x *NetContentBaseInfo) GetProtocol() NetProto {
	if x != nil {
		return x.Protocol
	}
	return NetProto_NP_DEFAULT
}

type NetConnectContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
	IsNetworkConnected bool                `protobuf:"varint,2,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *NetConnectContentInfo) Reset() {
	*x = NetConnectContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetConnectContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetConnectContentInfo) ProtoMessage() {}

func (x *NetConnectContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetConnectContentInfo.ProtoReflect.Descriptor instead.
func (*NetConnectContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{13}
}

func (x *NetConnectContentInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

func (x *NetConnectContentInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type NetAcceptContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
	IsNetworkConnected bool                `protobuf:"varint,2,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *NetAcceptContentInfo) Reset() {
	*x = NetAcceptContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetAcceptContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetAcceptContentInfo) ProtoMessage() {}

func (x *NetAcceptContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetAcceptContentInfo.ProtoReflect.Descriptor instead.
func (*NetAcceptContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{14}
}

func (x *NetAcceptContentInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

func (x *NetAcceptContentInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type NetListenContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
	IsNetworkConnected bool                `protobuf:"varint,2,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *NetListenContentInfo) Reset() {
	*x = NetListenContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetListenContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetListenContentInfo) ProtoMessage() {}

func (x *NetListenContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetListenContentInfo.ProtoReflect.Descriptor instead.
func (*NetListenContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{15}
}

func (x *NetListenContentInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

func (x *NetListenContentInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type NetSendContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
	DirectionIn        bool                `protobuf:"varint,2,opt,name=direction_in,json=directionIn,proto3" json:"direction_in,omitempty"`
	IsNetworkConnected bool                `protobuf:"varint,3,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *NetSendContentInfo) Reset() {
	*x = NetSendContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetSendContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetSendContentInfo) ProtoMessage() {}

func (x *NetSendContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetSendContentInfo.ProtoReflect.Descriptor instead.
func (*NetSendContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{16}
}

func (x *NetSendContentInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

func (x *NetSendContentInfo) GetDirectionIn() bool {
	if x != nil {
		return x.DirectionIn
	}
	return false
}

func (x *NetSendContentInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type NetRecvContentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
	DirectionIn        bool                `protobuf:"varint,2,opt,name=direction_in,json=directionIn,proto3" json:"direction_in,omitempty"`
	IsNetworkConnected bool                `protobuf:"varint,3,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *NetRecvContentInfo) Reset() {
	*x = NetRecvContentInfo{}
	mi := &file_agent_acdr_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetRecvContentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetRecvContentInfo) ProtoMessage() {}

func (x *NetRecvContentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetRecvContentInfo.ProtoReflect.Descriptor instead.
func (*NetRecvContentInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{17}
}

func (x *NetRecvContentInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

func (x *NetRecvContentInfo) GetDirectionIn() bool {
	if x != nil {
		return x.DirectionIn
	}
	return false
}

func (x *NetRecvContentInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type FilelessAttackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"` //文件是否存在
}

func (x *FilelessAttackInfo) Reset() {
	*x = FilelessAttackInfo{}
	mi := &file_agent_acdr_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilelessAttackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilelessAttackInfo) ProtoMessage() {}

func (x *FilelessAttackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilelessAttackInfo.ProtoReflect.Descriptor instead.
func (*FilelessAttackInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{18}
}

func (x *FilelessAttackInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FilelessAttackInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type NetSnifferInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Devname []byte `protobuf:"bytes,1,opt,name=devname,proto3" json:"devname,omitempty"`
}

func (x *NetSnifferInfo) Reset() {
	*x = NetSnifferInfo{}
	mi := &file_agent_acdr_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetSnifferInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetSnifferInfo) ProtoMessage() {}

func (x *NetSnifferInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetSnifferInfo.ProtoReflect.Descriptor instead.
func (*NetSnifferInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{19}
}

func (x *NetSnifferInfo) GetDevname() []byte {
	if x != nil {
		return x.Devname
	}
	return nil
}

type EnvHijackInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EnvName []byte `protobuf:"bytes,1,opt,name=env_name,json=envName,proto3" json:"env_name,omitempty"`
}

func (x *EnvHijackInfo) Reset() {
	*x = EnvHijackInfo{}
	mi := &file_agent_acdr_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvHijackInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvHijackInfo) ProtoMessage() {}

func (x *EnvHijackInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvHijackInfo.ProtoReflect.Descriptor instead.
func (*EnvHijackInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{20}
}

func (x *EnvHijackInfo) GetEnvName() []byte {
	if x != nil {
		return x.EnvName
	}
	return nil
}

type SelfDeleteInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"` //文件是否存在
}

func (x *SelfDeleteInfo) Reset() {
	*x = SelfDeleteInfo{}
	mi := &file_agent_acdr_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SelfDeleteInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelfDeleteInfo) ProtoMessage() {}

func (x *SelfDeleteInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelfDeleteInfo.ProtoReflect.Descriptor instead.
func (*SelfDeleteInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{21}
}

func (x *SelfDeleteInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *SelfDeleteInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type PtraceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TracerPid       uint32 `protobuf:"varint,1,opt,name=tracer_pid,json=tracerPid,proto3" json:"tracer_pid,omitempty"`
	TracerStartTime uint64 `protobuf:"varint,2,opt,name=tracer_start_time,json=tracerStartTime,proto3" json:"tracer_start_time,omitempty"`
	TraceePid       uint32 `protobuf:"varint,3,opt,name=tracee_pid,json=traceePid,proto3" json:"tracee_pid,omitempty"`
	TraceeStartTime uint64 `protobuf:"varint,4,opt,name=tracee_start_time,json=traceeStartTime,proto3" json:"tracee_start_time,omitempty"`
	TraceePath      []byte `protobuf:"bytes,5,opt,name=tracee_path,json=traceePath,proto3" json:"tracee_path,omitempty"`
	Mode            uint32 `protobuf:"varint,6,opt,name=mode,proto3" json:"mode,omitempty"`
	Addr            uint64 `protobuf:"varint,7,opt,name=addr,proto3" json:"addr,omitempty"`
}

func (x *PtraceInfo) Reset() {
	*x = PtraceInfo{}
	mi := &file_agent_acdr_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PtraceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PtraceInfo) ProtoMessage() {}

func (x *PtraceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PtraceInfo.ProtoReflect.Descriptor instead.
func (*PtraceInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{22}
}

func (x *PtraceInfo) GetTracerPid() uint32 {
	if x != nil {
		return x.TracerPid
	}
	return 0
}

func (x *PtraceInfo) GetTracerStartTime() uint64 {
	if x != nil {
		return x.TracerStartTime
	}
	return 0
}

func (x *PtraceInfo) GetTraceePid() uint32 {
	if x != nil {
		return x.TraceePid
	}
	return 0
}

func (x *PtraceInfo) GetTraceeStartTime() uint64 {
	if x != nil {
		return x.TraceeStartTime
	}
	return 0
}

func (x *PtraceInfo) GetTraceePath() []byte {
	if x != nil {
		return x.TraceePath
	}
	return nil
}

func (x *PtraceInfo) GetMode() uint32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *PtraceInfo) GetAddr() uint64 {
	if x != nil {
		return x.Addr
	}
	return 0
}

type FileLinkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePathSrc  []byte `protobuf:"bytes,1,opt,name=file_path_src,json=filePathSrc,proto3" json:"file_path_src,omitempty"`
	FilePathDest []byte `protobuf:"bytes,2,opt,name=file_path_dest,json=filePathDest,proto3" json:"file_path_dest,omitempty"`
	IsSrcExist   bool   `protobuf:"varint,3,opt,name=is_src_exist,json=isSrcExist,proto3" json:"is_src_exist,omitempty"`    //原文件是否存在
	IsDestExist  bool   `protobuf:"varint,4,opt,name=is_dest_exist,json=isDestExist,proto3" json:"is_dest_exist,omitempty"` //目标文件是否存在
}

func (x *FileLinkInfo) Reset() {
	*x = FileLinkInfo{}
	mi := &file_agent_acdr_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileLinkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileLinkInfo) ProtoMessage() {}

func (x *FileLinkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileLinkInfo.ProtoReflect.Descriptor instead.
func (*FileLinkInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{23}
}

func (x *FileLinkInfo) GetFilePathSrc() []byte {
	if x != nil {
		return x.FilePathSrc
	}
	return nil
}

func (x *FileLinkInfo) GetFilePathDest() []byte {
	if x != nil {
		return x.FilePathDest
	}
	return nil
}

func (x *FileLinkInfo) GetIsSrcExist() bool {
	if x != nil {
		return x.IsSrcExist
	}
	return false
}

func (x *FileLinkInfo) GetIsDestExist() bool {
	if x != nil {
		return x.IsDestExist
	}
	return false
}

type CallUsermodehelperInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname []byte `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"`
}

func (x *CallUsermodehelperInfo) Reset() {
	*x = CallUsermodehelperInfo{}
	mi := &file_agent_acdr_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CallUsermodehelperInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallUsermodehelperInfo) ProtoMessage() {}

func (x *CallUsermodehelperInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallUsermodehelperInfo.ProtoReflect.Descriptor instead.
func (*CallUsermodehelperInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{24}
}

func (x *CallUsermodehelperInfo) GetModname() []byte {
	if x != nil {
		return x.Modname
	}
	return nil
}

type ReverseShellInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InPid              uint32      `protobuf:"varint,1,opt,name=in_pid,json=inPid,proto3" json:"in_pid,omitempty"`
	InIp               []byte      `protobuf:"bytes,2,opt,name=in_ip,json=inIp,proto3" json:"in_ip,omitempty"` //本地IP
	InPort             uint32      `protobuf:"varint,3,opt,name=in_port,json=inPort,proto3" json:"in_port,omitempty"`
	InAddrFamily       NetFamilies `protobuf:"varint,4,opt,name=in_addr_family,json=inAddrFamily,proto3,enum=agent.NetFamilies" json:"in_addr_family,omitempty"`
	InProto            NetProto    `protobuf:"varint,5,opt,name=in_proto,json=inProto,proto3,enum=agent.NetProto" json:"in_proto,omitempty"`
	InStartTime        uint64      `protobuf:"varint,6,opt,name=in_start_time,json=inStartTime,proto3" json:"in_start_time,omitempty"`
	OutPid             uint32      `protobuf:"varint,7,opt,name=out_pid,json=outPid,proto3" json:"out_pid,omitempty"`
	OutIp              []byte      `protobuf:"bytes,8,opt,name=out_ip,json=outIp,proto3" json:"out_ip,omitempty"` //远端ip
	OutPort            uint32      `protobuf:"varint,9,opt,name=out_port,json=outPort,proto3" json:"out_port,omitempty"`
	OutAddrFamily      NetFamilies `protobuf:"varint,10,opt,name=out_addr_family,json=outAddrFamily,proto3,enum=agent.NetFamilies" json:"out_addr_family,omitempty"`
	OutProto           NetProto    `protobuf:"varint,11,opt,name=out_proto,json=outProto,proto3,enum=agent.NetProto" json:"out_proto,omitempty"`
	OutStartTime       uint64      `protobuf:"varint,12,opt,name=out_start_time,json=outStartTime,proto3" json:"out_start_time,omitempty"`
	IsNetworkConnected bool        `protobuf:"varint,13,opt,name=is_network_connected,json=isNetworkConnected,proto3" json:"is_network_connected,omitempty"` //网络是否连接
}

func (x *ReverseShellInfo) Reset() {
	*x = ReverseShellInfo{}
	mi := &file_agent_acdr_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReverseShellInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReverseShellInfo) ProtoMessage() {}

func (x *ReverseShellInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReverseShellInfo.ProtoReflect.Descriptor instead.
func (*ReverseShellInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{25}
}

func (x *ReverseShellInfo) GetInPid() uint32 {
	if x != nil {
		return x.InPid
	}
	return 0
}

func (x *ReverseShellInfo) GetInIp() []byte {
	if x != nil {
		return x.InIp
	}
	return nil
}

func (x *ReverseShellInfo) GetInPort() uint32 {
	if x != nil {
		return x.InPort
	}
	return 0
}

func (x *ReverseShellInfo) GetInAddrFamily() NetFamilies {
	if x != nil {
		return x.InAddrFamily
	}
	return NetFamilies_NF_DEFAULT
}

func (x *ReverseShellInfo) GetInProto() NetProto {
	if x != nil {
		return x.InProto
	}
	return NetProto_NP_DEFAULT
}

func (x *ReverseShellInfo) GetInStartTime() uint64 {
	if x != nil {
		return x.InStartTime
	}
	return 0
}

func (x *ReverseShellInfo) GetOutPid() uint32 {
	if x != nil {
		return x.OutPid
	}
	return 0
}

func (x *ReverseShellInfo) GetOutIp() []byte {
	if x != nil {
		return x.OutIp
	}
	return nil
}

func (x *ReverseShellInfo) GetOutPort() uint32 {
	if x != nil {
		return x.OutPort
	}
	return 0
}

func (x *ReverseShellInfo) GetOutAddrFamily() NetFamilies {
	if x != nil {
		return x.OutAddrFamily
	}
	return NetFamilies_NF_DEFAULT
}

func (x *ReverseShellInfo) GetOutProto() NetProto {
	if x != nil {
		return x.OutProto
	}
	return NetProto_NP_DEFAULT
}

func (x *ReverseShellInfo) GetOutStartTime() uint64 {
	if x != nil {
		return x.OutStartTime
	}
	return 0
}

func (x *ReverseShellInfo) GetIsNetworkConnected() bool {
	if x != nil {
		return x.IsNetworkConnected
	}
	return false
}

type ProcessExecInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exepath []byte `protobuf:"bytes,1,opt,name=exepath,proto3" json:"exepath,omitempty"`
}

func (x *ProcessExecInfo) Reset() {
	*x = ProcessExecInfo{}
	mi := &file_agent_acdr_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessExecInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessExecInfo) ProtoMessage() {}

func (x *ProcessExecInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessExecInfo.ProtoReflect.Descriptor instead.
func (*ProcessExecInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{26}
}

func (x *ProcessExecInfo) GetExepath() []byte {
	if x != nil {
		return x.Exepath
	}
	return nil
}

type PrivilegeEscalation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrivilegePath    []byte `protobuf:"bytes,1,opt,name=privilege_path,json=privilegePath,proto3" json:"privilege_path,omitempty"`          //被提升进程的路径
	PrivilegeCmdline []byte `protobuf:"bytes,2,opt,name=privilege_cmdline,json=privilegeCmdline,proto3" json:"privilege_cmdline,omitempty"` //被提升进程的命令行
}

func (x *PrivilegeEscalation) Reset() {
	*x = PrivilegeEscalation{}
	mi := &file_agent_acdr_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivilegeEscalation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivilegeEscalation) ProtoMessage() {}

func (x *PrivilegeEscalation) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivilegeEscalation.ProtoReflect.Descriptor instead.
func (*PrivilegeEscalation) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{27}
}

func (x *PrivilegeEscalation) GetPrivilegePath() []byte {
	if x != nil {
		return x.PrivilegePath
	}
	return nil
}

func (x *PrivilegeEscalation) GetPrivilegeCmdline() []byte {
	if x != nil {
		return x.PrivilegeCmdline
	}
	return nil
}

type RestoreUtime struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"` //被恢复时间的文件名
	IsExist  bool   `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`   //文件是否存在
}

func (x *RestoreUtime) Reset() {
	*x = RestoreUtime{}
	mi := &file_agent_acdr_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreUtime) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreUtime) ProtoMessage() {}

func (x *RestoreUtime) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreUtime.ProtoReflect.Descriptor instead.
func (*RestoreUtime) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{28}
}

func (x *RestoreUtime) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *RestoreUtime) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type Bpf struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TypeName []byte `protobuf:"bytes,1,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"` //hook类型名
}

func (x *Bpf) Reset() {
	*x = Bpf{}
	mi := &file_agent_acdr_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Bpf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Bpf) ProtoMessage() {}

func (x *Bpf) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Bpf.ProtoReflect.Descriptor instead.
func (*Bpf) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{29}
}

func (x *Bpf) GetTypeName() []byte {
	if x != nil {
		return x.TypeName
	}
	return nil
}

type FakeExeFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FakePid     uint32 `protobuf:"varint,1,opt,name=fake_pid,json=fakePid,proto3" json:"fake_pid,omitempty"`            //伪装进程pid
	StartTime   uint64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`      //进程启动时间
	FakeExepath []byte `protobuf:"bytes,3,opt,name=fake_exepath,json=fakeExepath,proto3" json:"fake_exepath,omitempty"` //伪装后的路径
	Exepath     []byte `protobuf:"bytes,4,opt,name=exepath,proto3" json:"exepath,omitempty"`                            //真实路径
	IsExeExist  bool   `protobuf:"varint,5,opt,name=is_exe_exist,json=isExeExist,proto3" json:"is_exe_exist,omitempty"` //真实路径文件是否存在
}

func (x *FakeExeFileInfo) Reset() {
	*x = FakeExeFileInfo{}
	mi := &file_agent_acdr_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FakeExeFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FakeExeFileInfo) ProtoMessage() {}

func (x *FakeExeFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FakeExeFileInfo.ProtoReflect.Descriptor instead.
func (*FakeExeFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{30}
}

func (x *FakeExeFileInfo) GetFakePid() uint32 {
	if x != nil {
		return x.FakePid
	}
	return 0
}

func (x *FakeExeFileInfo) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *FakeExeFileInfo) GetFakeExepath() []byte {
	if x != nil {
		return x.FakeExepath
	}
	return nil
}

func (x *FakeExeFileInfo) GetExepath() []byte {
	if x != nil {
		return x.Exepath
	}
	return nil
}

func (x *FakeExeFileInfo) GetIsExeExist() bool {
	if x != nil {
		return x.IsExeExist
	}
	return false
}

type HideModuleInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname []byte `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"` //模块名
}

func (x *HideModuleInfo) Reset() {
	*x = HideModuleInfo{}
	mi := &file_agent_acdr_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HideModuleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HideModuleInfo) ProtoMessage() {}

func (x *HideModuleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HideModuleInfo.ProtoReflect.Descriptor instead.
func (*HideModuleInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{31}
}

func (x *HideModuleInfo) GetModname() []byte {
	if x != nil {
		return x.Modname
	}
	return nil
}

type KillInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetPid  uint32 `protobuf:"varint,1,opt,name=target_pid,json=targetPid,proto3" json:"target_pid,omitempty"`   //信号发送的目标进程
	StartTime  uint64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`   //目标进程启动时间
	Signal     uint32 `protobuf:"varint,3,opt,name=signal,proto3" json:"signal,omitempty"`                          //信号
	TargetPath []byte `protobuf:"bytes,4,opt,name=target_path,json=targetPath,proto3" json:"target_path,omitempty"` //信号发送的目标进程的路径
	IsExist    bool   `protobuf:"varint,5,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`         //文件是否存在
}

func (x *KillInfo) Reset() {
	*x = KillInfo{}
	mi := &file_agent_acdr_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KillInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KillInfo) ProtoMessage() {}

func (x *KillInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KillInfo.ProtoReflect.Descriptor instead.
func (*KillInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{32}
}

func (x *KillInfo) GetTargetPid() uint32 {
	if x != nil {
		return x.TargetPid
	}
	return 0
}

func (x *KillInfo) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *KillInfo) GetSignal() uint32 {
	if x != nil {
		return x.Signal
	}
	return 0
}

func (x *KillInfo) GetTargetPath() []byte {
	if x != nil {
		return x.TargetPath
	}
	return nil
}

func (x *KillInfo) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

type RuleActionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionType  ActionType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=agent.ActionType" json:"action_type,omitempty"` //行为类型
	TriggerTime uint64     `protobuf:"varint,2,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"`                    //触发该行为的时间
	Pid         uint32     `protobuf:"varint,3,opt,name=pid,proto3" json:"pid,omitempty"`                                                       //进程pid
	IsRule      bool       `protobuf:"varint,4,opt,name=is_rule,json=isRule,proto3" json:"is_rule,omitempty"`                                   //是否是规则，为true时，只有oneof字段有效，oneof指向rule_msg。为false时oneof类型和action_type对应，所有字段均有效。
	StartTime   uint64     `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                          //进程的启动时间
	IsRefused   bool       `protobuf:"varint,6,opt,name=is_refused,json=isRefused,proto3" json:"is_refused,omitempty"`                          //该行为是否被拦截
	// Types that are assignable to ActionContent:
	//
	//	*RuleActionItem_RuleMsg
	//	*RuleActionItem_FileCreateContent
	//	*RuleActionItem_FileOpenContent
	//	*RuleActionItem_FileWriteContent
	//	*RuleActionItem_FileRenameContent
	//	*RuleActionItem_FileDeleteContent
	//	*RuleActionItem_NetConnectContent
	//	*RuleActionItem_NetAcceptContent
	//	*RuleActionItem_NetListenContent
	//	*RuleActionItem_NetSendContent
	//	*RuleActionItem_NetRecvContent
	//	*RuleActionItem_FilelessAttackContent
	//	*RuleActionItem_NetSnifferContent
	//	*RuleActionItem_EnvHijackContent
	//	*RuleActionItem_SelfDeleteContent
	//	*RuleActionItem_PtraceContent
	//	*RuleActionItem_FileLinkContent
	//	*RuleActionItem_CallUsermodehelperContent
	//	*RuleActionItem_ReverseShellContent
	//	*RuleActionItem_ProcessExecContent
	//	*RuleActionItem_RestoreUtimeContent
	//	*RuleActionItem_FileIoctlImmutableContent
	//	*RuleActionItem_MkdirContent
	//	*RuleActionItem_FileSymlink
	//	*RuleActionItem_FileSetUid
	//	*RuleActionItem_Bpf
	//	*RuleActionItem_PrivilegeEscalation
	//	*RuleActionItem_FakeExeFileContent
	//	*RuleActionItem_HideModuleContent
	//	*RuleActionItem_KillContent
	ActionContent isRuleActionItem_ActionContent `protobuf_oneof:"ActionContent"`
}

func (x *RuleActionItem) Reset() {
	*x = RuleActionItem{}
	mi := &file_agent_acdr_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleActionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleActionItem) ProtoMessage() {}

func (x *RuleActionItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleActionItem.ProtoReflect.Descriptor instead.
func (*RuleActionItem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{33}
}

func (x *RuleActionItem) GetActionType() ActionType {
	if x != nil {
		return x.ActionType
	}
	return ActionType_AT_DEFAULT
}

func (x *RuleActionItem) GetTriggerTime() uint64 {
	if x != nil {
		return x.TriggerTime
	}
	return 0
}

func (x *RuleActionItem) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *RuleActionItem) GetIsRule() bool {
	if x != nil {
		return x.IsRule
	}
	return false
}

func (x *RuleActionItem) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *RuleActionItem) GetIsRefused() bool {
	if x != nil {
		return x.IsRefused
	}
	return false
}

func (m *RuleActionItem) GetActionContent() isRuleActionItem_ActionContent {
	if m != nil {
		return m.ActionContent
	}
	return nil
}

func (x *RuleActionItem) GetRuleMsg() *RuleInfoMessage {
	if x, ok := x.GetActionContent().(*RuleActionItem_RuleMsg); ok {
		return x.RuleMsg
	}
	return nil
}

func (x *RuleActionItem) GetFileCreateContent() *FileCreateContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileCreateContent); ok {
		return x.FileCreateContent
	}
	return nil
}

func (x *RuleActionItem) GetFileOpenContent() *FileOpenContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileOpenContent); ok {
		return x.FileOpenContent
	}
	return nil
}

func (x *RuleActionItem) GetFileWriteContent() *FileWriteContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileWriteContent); ok {
		return x.FileWriteContent
	}
	return nil
}

func (x *RuleActionItem) GetFileRenameContent() *FileRenameContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileRenameContent); ok {
		return x.FileRenameContent
	}
	return nil
}

func (x *RuleActionItem) GetFileDeleteContent() *FileDeleteContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileDeleteContent); ok {
		return x.FileDeleteContent
	}
	return nil
}

func (x *RuleActionItem) GetNetConnectContent() *NetConnectContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetConnectContent); ok {
		return x.NetConnectContent
	}
	return nil
}

func (x *RuleActionItem) GetNetAcceptContent() *NetAcceptContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetAcceptContent); ok {
		return x.NetAcceptContent
	}
	return nil
}

func (x *RuleActionItem) GetNetListenContent() *NetListenContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetListenContent); ok {
		return x.NetListenContent
	}
	return nil
}

func (x *RuleActionItem) GetNetSendContent() *NetSendContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetSendContent); ok {
		return x.NetSendContent
	}
	return nil
}

func (x *RuleActionItem) GetNetRecvContent() *NetRecvContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetRecvContent); ok {
		return x.NetRecvContent
	}
	return nil
}

func (x *RuleActionItem) GetFilelessAttackContent() *FilelessAttackInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FilelessAttackContent); ok {
		return x.FilelessAttackContent
	}
	return nil
}

func (x *RuleActionItem) GetNetSnifferContent() *NetSnifferInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_NetSnifferContent); ok {
		return x.NetSnifferContent
	}
	return nil
}

func (x *RuleActionItem) GetEnvHijackContent() *EnvHijackInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_EnvHijackContent); ok {
		return x.EnvHijackContent
	}
	return nil
}

func (x *RuleActionItem) GetSelfDeleteContent() *SelfDeleteInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_SelfDeleteContent); ok {
		return x.SelfDeleteContent
	}
	return nil
}

func (x *RuleActionItem) GetPtraceContent() *PtraceInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_PtraceContent); ok {
		return x.PtraceContent
	}
	return nil
}

func (x *RuleActionItem) GetFileLinkContent() *FileLinkInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileLinkContent); ok {
		return x.FileLinkContent
	}
	return nil
}

func (x *RuleActionItem) GetCallUsermodehelperContent() *CallUsermodehelperInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_CallUsermodehelperContent); ok {
		return x.CallUsermodehelperContent
	}
	return nil
}

func (x *RuleActionItem) GetReverseShellContent() *ReverseShellInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_ReverseShellContent); ok {
		return x.ReverseShellContent
	}
	return nil
}

func (x *RuleActionItem) GetProcessExecContent() *ProcessExecInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_ProcessExecContent); ok {
		return x.ProcessExecContent
	}
	return nil
}

func (x *RuleActionItem) GetRestoreUtimeContent() *RestoreUtime {
	if x, ok := x.GetActionContent().(*RuleActionItem_RestoreUtimeContent); ok {
		return x.RestoreUtimeContent
	}
	return nil
}

func (x *RuleActionItem) GetFileIoctlImmutableContent() *FileIoctlImmutableContentInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileIoctlImmutableContent); ok {
		return x.FileIoctlImmutableContent
	}
	return nil
}

func (x *RuleActionItem) GetMkdirContent() *Mkdir {
	if x, ok := x.GetActionContent().(*RuleActionItem_MkdirContent); ok {
		return x.MkdirContent
	}
	return nil
}

func (x *RuleActionItem) GetFileSymlink() *FileSymlink {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileSymlink); ok {
		return x.FileSymlink
	}
	return nil
}

func (x *RuleActionItem) GetFileSetUid() *FileSetUid {
	if x, ok := x.GetActionContent().(*RuleActionItem_FileSetUid); ok {
		return x.FileSetUid
	}
	return nil
}

func (x *RuleActionItem) GetBpf() *Bpf {
	if x, ok := x.GetActionContent().(*RuleActionItem_Bpf); ok {
		return x.Bpf
	}
	return nil
}

func (x *RuleActionItem) GetPrivilegeEscalation() *PrivilegeEscalation {
	if x, ok := x.GetActionContent().(*RuleActionItem_PrivilegeEscalation); ok {
		return x.PrivilegeEscalation
	}
	return nil
}

func (x *RuleActionItem) GetFakeExeFileContent() *FakeExeFileInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_FakeExeFileContent); ok {
		return x.FakeExeFileContent
	}
	return nil
}

func (x *RuleActionItem) GetHideModuleContent() *HideModuleInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_HideModuleContent); ok {
		return x.HideModuleContent
	}
	return nil
}

func (x *RuleActionItem) GetKillContent() *KillInfo {
	if x, ok := x.GetActionContent().(*RuleActionItem_KillContent); ok {
		return x.KillContent
	}
	return nil
}

type isRuleActionItem_ActionContent interface {
	isRuleActionItem_ActionContent()
}

type RuleActionItem_RuleMsg struct {
	RuleMsg *RuleInfoMessage `protobuf:"bytes,9,opt,name=rule_msg,json=ruleMsg,proto3,oneof"`
}

type RuleActionItem_FileCreateContent struct {
	FileCreateContent *FileCreateContentInfo `protobuf:"bytes,10,opt,name=file_create_content,json=fileCreateContent,proto3,oneof"`
}

type RuleActionItem_FileOpenContent struct {
	FileOpenContent *FileOpenContentInfo `protobuf:"bytes,11,opt,name=file_open_content,json=fileOpenContent,proto3,oneof"`
}

type RuleActionItem_FileWriteContent struct {
	FileWriteContent *FileWriteContentInfo `protobuf:"bytes,12,opt,name=file_write_content,json=fileWriteContent,proto3,oneof"`
}

type RuleActionItem_FileRenameContent struct {
	FileRenameContent *FileRenameContentInfo `protobuf:"bytes,13,opt,name=file_rename_content,json=fileRenameContent,proto3,oneof"`
}

type RuleActionItem_FileDeleteContent struct {
	FileDeleteContent *FileDeleteContentInfo `protobuf:"bytes,14,opt,name=file_delete_content,json=fileDeleteContent,proto3,oneof"`
}

type RuleActionItem_NetConnectContent struct {
	NetConnectContent *NetConnectContentInfo `protobuf:"bytes,15,opt,name=net_connect_content,json=netConnectContent,proto3,oneof"`
}

type RuleActionItem_NetAcceptContent struct {
	NetAcceptContent *NetAcceptContentInfo `protobuf:"bytes,16,opt,name=net_accept_content,json=netAcceptContent,proto3,oneof"`
}

type RuleActionItem_NetListenContent struct {
	NetListenContent *NetListenContentInfo `protobuf:"bytes,17,opt,name=net_listen_content,json=netListenContent,proto3,oneof"`
}

type RuleActionItem_NetSendContent struct {
	NetSendContent *NetSendContentInfo `protobuf:"bytes,18,opt,name=net_send_content,json=netSendContent,proto3,oneof"`
}

type RuleActionItem_NetRecvContent struct {
	NetRecvContent *NetRecvContentInfo `protobuf:"bytes,19,opt,name=net_recv_content,json=netRecvContent,proto3,oneof"`
}

type RuleActionItem_FilelessAttackContent struct {
	FilelessAttackContent *FilelessAttackInfo `protobuf:"bytes,20,opt,name=fileless_attack_content,json=filelessAttackContent,proto3,oneof"`
}

type RuleActionItem_NetSnifferContent struct {
	NetSnifferContent *NetSnifferInfo `protobuf:"bytes,21,opt,name=net_sniffer_content,json=netSnifferContent,proto3,oneof"`
}

type RuleActionItem_EnvHijackContent struct {
	EnvHijackContent *EnvHijackInfo `protobuf:"bytes,22,opt,name=env_hijack_content,json=envHijackContent,proto3,oneof"`
}

type RuleActionItem_SelfDeleteContent struct {
	SelfDeleteContent *SelfDeleteInfo `protobuf:"bytes,23,opt,name=self_delete_content,json=selfDeleteContent,proto3,oneof"`
}

type RuleActionItem_PtraceContent struct {
	PtraceContent *PtraceInfo `protobuf:"bytes,24,opt,name=ptrace_content,json=ptraceContent,proto3,oneof"`
}

type RuleActionItem_FileLinkContent struct {
	FileLinkContent *FileLinkInfo `protobuf:"bytes,25,opt,name=file_link_content,json=fileLinkContent,proto3,oneof"`
}

type RuleActionItem_CallUsermodehelperContent struct {
	CallUsermodehelperContent *CallUsermodehelperInfo `protobuf:"bytes,26,opt,name=call_usermodehelper_content,json=callUsermodehelperContent,proto3,oneof"`
}

type RuleActionItem_ReverseShellContent struct {
	ReverseShellContent *ReverseShellInfo `protobuf:"bytes,27,opt,name=reverse_shell_content,json=reverseShellContent,proto3,oneof"`
}

type RuleActionItem_ProcessExecContent struct {
	ProcessExecContent *ProcessExecInfo `protobuf:"bytes,28,opt,name=process_exec_content,json=processExecContent,proto3,oneof"`
}

type RuleActionItem_RestoreUtimeContent struct {
	RestoreUtimeContent *RestoreUtime `protobuf:"bytes,29,opt,name=restore_utime_content,json=restoreUtimeContent,proto3,oneof"`
}

type RuleActionItem_FileIoctlImmutableContent struct {
	FileIoctlImmutableContent *FileIoctlImmutableContentInfo `protobuf:"bytes,30,opt,name=file_ioctl_immutable_content,json=fileIoctlImmutableContent,proto3,oneof"`
}

type RuleActionItem_MkdirContent struct {
	MkdirContent *Mkdir `protobuf:"bytes,31,opt,name=mkdir_content,json=mkdirContent,proto3,oneof"`
}

type RuleActionItem_FileSymlink struct {
	FileSymlink *FileSymlink `protobuf:"bytes,32,opt,name=file_symlink,json=fileSymlink,proto3,oneof"`
}

type RuleActionItem_FileSetUid struct {
	FileSetUid *FileSetUid `protobuf:"bytes,33,opt,name=file_set_uid,json=fileSetUid,proto3,oneof"`
}

type RuleActionItem_Bpf struct {
	Bpf *Bpf `protobuf:"bytes,34,opt,name=bpf,proto3,oneof"`
}

type RuleActionItem_PrivilegeEscalation struct {
	PrivilegeEscalation *PrivilegeEscalation `protobuf:"bytes,35,opt,name=privilege_escalation,json=privilegeEscalation,proto3,oneof"`
}

type RuleActionItem_FakeExeFileContent struct {
	FakeExeFileContent *FakeExeFileInfo `protobuf:"bytes,36,opt,name=fake_exe_file_content,json=fakeExeFileContent,proto3,oneof"`
}

type RuleActionItem_HideModuleContent struct {
	HideModuleContent *HideModuleInfo `protobuf:"bytes,37,opt,name=hide_module_content,json=hideModuleContent,proto3,oneof"`
}

type RuleActionItem_KillContent struct {
	KillContent *KillInfo `protobuf:"bytes,38,opt,name=kill_content,json=killContent,proto3,oneof"`
}

func (*RuleActionItem_RuleMsg) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileCreateContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileOpenContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileWriteContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileRenameContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileDeleteContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetConnectContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetAcceptContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetListenContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetSendContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetRecvContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FilelessAttackContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_NetSnifferContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_EnvHijackContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_SelfDeleteContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_PtraceContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileLinkContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_CallUsermodehelperContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_ReverseShellContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_ProcessExecContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_RestoreUtimeContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileIoctlImmutableContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_MkdirContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileSymlink) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FileSetUid) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_Bpf) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_PrivilegeEscalation) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_FakeExeFileContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_HideModuleContent) isRuleActionItem_ActionContent() {}

func (*RuleActionItem_KillContent) isRuleActionItem_ActionContent() {}

type StatusProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid       uint32 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	StartTime uint64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
}

func (x *StatusProcessInfo) Reset() {
	*x = StatusProcessInfo{}
	mi := &file_agent_acdr_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusProcessInfo) ProtoMessage() {}

func (x *StatusProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusProcessInfo.ProtoReflect.Descriptor instead.
func (*StatusProcessInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{34}
}

func (x *StatusProcessInfo) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *StatusProcessInfo) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type StatusFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath []byte `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
}

func (x *StatusFileInfo) Reset() {
	*x = StatusFileInfo{}
	mi := &file_agent_acdr_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusFileInfo) ProtoMessage() {}

func (x *StatusFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusFileInfo.ProtoReflect.Descriptor instead.
func (*StatusFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{35}
}

func (x *StatusFileInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

type StatusNetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NetContentBaseInfo *NetContentBaseInfo `protobuf:"bytes,1,opt,name=net_content_base_info,json=netContentBaseInfo,proto3" json:"net_content_base_info,omitempty"`
}

func (x *StatusNetInfo) Reset() {
	*x = StatusNetInfo{}
	mi := &file_agent_acdr_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusNetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusNetInfo) ProtoMessage() {}

func (x *StatusNetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusNetInfo.ProtoReflect.Descriptor instead.
func (*StatusNetInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{36}
}

func (x *StatusNetInfo) GetNetContentBaseInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetContentBaseInfo
	}
	return nil
}

type RiskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to StatusContext:
	//
	//	*RiskStatus_ProcInfo
	//	*RiskStatus_NetInfo
	//	*RiskStatus_FileInfo
	StatusContext isRiskStatus_StatusContext `protobuf_oneof:"StatusContext"`
}

func (x *RiskStatus) Reset() {
	*x = RiskStatus{}
	mi := &file_agent_acdr_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskStatus) ProtoMessage() {}

func (x *RiskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskStatus.ProtoReflect.Descriptor instead.
func (*RiskStatus) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{37}
}

func (m *RiskStatus) GetStatusContext() isRiskStatus_StatusContext {
	if m != nil {
		return m.StatusContext
	}
	return nil
}

func (x *RiskStatus) GetProcInfo() *StatusProcessInfo {
	if x, ok := x.GetStatusContext().(*RiskStatus_ProcInfo); ok {
		return x.ProcInfo
	}
	return nil
}

func (x *RiskStatus) GetNetInfo() *StatusNetInfo {
	if x, ok := x.GetStatusContext().(*RiskStatus_NetInfo); ok {
		return x.NetInfo
	}
	return nil
}

func (x *RiskStatus) GetFileInfo() *StatusFileInfo {
	if x, ok := x.GetStatusContext().(*RiskStatus_FileInfo); ok {
		return x.FileInfo
	}
	return nil
}

type isRiskStatus_StatusContext interface {
	isRiskStatus_StatusContext()
}

type RiskStatus_ProcInfo struct {
	ProcInfo *StatusProcessInfo `protobuf:"bytes,1,opt,name=proc_info,json=procInfo,proto3,oneof"`
}

type RiskStatus_NetInfo struct {
	NetInfo *StatusNetInfo `protobuf:"bytes,2,opt,name=net_info,json=netInfo,proto3,oneof"`
}

type RiskStatus_FileInfo struct {
	FileInfo *StatusFileInfo `protobuf:"bytes,3,opt,name=file_info,json=fileInfo,proto3,oneof"`
}

func (*RiskStatus_ProcInfo) isRiskStatus_StatusContext() {}

func (*RiskStatus_NetInfo) isRiskStatus_StatusContext() {}

func (*RiskStatus_FileInfo) isRiskStatus_StatusContext() {}

type RiskStatusListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             uint64        `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RiskStatusList []*RiskStatus `protobuf:"bytes,2,rep,name=risk_status_list,json=riskStatusList,proto3" json:"risk_status_list,omitempty"`
}

func (x *RiskStatusListReq) Reset() {
	*x = RiskStatusListReq{}
	mi := &file_agent_acdr_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskStatusListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskStatusListReq) ProtoMessage() {}

func (x *RiskStatusListReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskStatusListReq.ProtoReflect.Descriptor instead.
func (*RiskStatusListReq) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{38}
}

func (x *RiskStatusListReq) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RiskStatusListReq) GetRiskStatusList() []*RiskStatus {
	if x != nil {
		return x.RiskStatusList
	}
	return nil
}

type RiskStatusListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id         uint64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StatusType []StatusType `protobuf:"varint,2,rep,packed,name=status_type,json=statusType,proto3,enum=agent.StatusType" json:"status_type,omitempty"` //状态类型
}

func (x *RiskStatusListResp) Reset() {
	*x = RiskStatusListResp{}
	mi := &file_agent_acdr_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskStatusListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskStatusListResp) ProtoMessage() {}

func (x *RiskStatusListResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskStatusListResp.ProtoReflect.Descriptor instead.
func (*RiskStatusListResp) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{39}
}

func (x *RiskStatusListResp) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RiskStatusListResp) GetStatusType() []StatusType {
	if x != nil {
		return x.StatusType
	}
	return nil
}

// IP 节点
type NodeAddrInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Port          uint32      `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
	Ip            []byte      `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	AddressFamily NetFamilies `protobuf:"varint,3,opt,name=address_family,json=addressFamily,proto3,enum=agent.NetFamilies" json:"address_family,omitempty"`
	Protocol      NetProto    `protobuf:"varint,4,opt,name=protocol,proto3,enum=agent.NetProto" json:"protocol,omitempty"`
}

func (x *NodeAddrInfo) Reset() {
	*x = NodeAddrInfo{}
	mi := &file_agent_acdr_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeAddrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeAddrInfo) ProtoMessage() {}

func (x *NodeAddrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeAddrInfo.ProtoReflect.Descriptor instead.
func (*NodeAddrInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{40}
}

func (x *NodeAddrInfo) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *NodeAddrInfo) GetIp() []byte {
	if x != nil {
		return x.Ip
	}
	return nil
}

func (x *NodeAddrInfo) GetAddressFamily() NetFamilies {
	if x != nil {
		return x.AddressFamily
	}
	return NetFamilies_NF_DEFAULT
}

func (x *NodeAddrInfo) GetProtocol() NetProto {
	if x != nil {
		return x.Protocol
	}
	return NetProto_NP_DEFAULT
}

type ConnectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Remote *NodeAddrInfo `protobuf:"bytes,1,opt,name=remote,proto3" json:"remote,omitempty"`
	Local  *NodeAddrInfo `protobuf:"bytes,2,opt,name=local,proto3" json:"local,omitempty"`
}

func (x *ConnectionInfo) Reset() {
	*x = ConnectionInfo{}
	mi := &file_agent_acdr_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionInfo) ProtoMessage() {}

func (x *ConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionInfo.ProtoReflect.Descriptor instead.
func (*ConnectionInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{41}
}

func (x *ConnectionInfo) GetRemote() *NodeAddrInfo {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *ConnectionInfo) GetLocal() *NodeAddrInfo {
	if x != nil {
		return x.Local
	}
	return nil
}

type DomainConnectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Remote *NodeRemoteUrl `protobuf:"bytes,1,opt,name=remote,proto3" json:"remote,omitempty"`
	Local  *NodeAddrInfo  `protobuf:"bytes,2,opt,name=local,proto3" json:"local,omitempty"`
}

func (x *DomainConnectionInfo) Reset() {
	*x = DomainConnectionInfo{}
	mi := &file_agent_acdr_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DomainConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DomainConnectionInfo) ProtoMessage() {}

func (x *DomainConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DomainConnectionInfo.ProtoReflect.Descriptor instead.
func (*DomainConnectionInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{42}
}

func (x *DomainConnectionInfo) GetRemote() *NodeRemoteUrl {
	if x != nil {
		return x.Remote
	}
	return nil
}

func (x *DomainConnectionInfo) GetLocal() *NodeAddrInfo {
	if x != nil {
		return x.Local
	}
	return nil
}

// 文件 节点
type NodeFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath             string              `protobuf:"bytes,1,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                                       // 文件创建路径
	FileSha256           []byte              `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`                                 // 文件sha256
	FileSize             uint32              `protobuf:"varint,3,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                                      // 文件大小
	FileMtime            uint64              `protobuf:"varint,4,opt,name=file_mtime,json=fileMtime,proto3" json:"file_mtime,omitempty"`                                   // 文件最后一次修改时间
	FileCtime            uint64              `protobuf:"varint,5,opt,name=file_ctime,json=fileCtime,proto3" json:"file_ctime,omitempty"`                                   // 文件最后一次属性改变时间
	FileAtime            uint64              `protobuf:"varint,6,opt,name=file_atime,json=fileAtime,proto3" json:"file_atime,omitempty"`                                   // 文件最后一次访问时间
	FileAccessPermission []byte              `protobuf:"bytes,7,opt,name=file_access_permission,json=fileAccessPermission,proto3" json:"file_access_permission,omitempty"` // 文件访问权限
	FileMd5              []byte              `protobuf:"bytes,8,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`                                          // 文件md5
	DetailInfo           *ProcFileDetailInfo `protobuf:"bytes,9,opt,name=detail_info,json=detailInfo,proto3" json:"detail_info,omitempty"`                                 // 详细信息
	SignatureInfo        []*SignatureInfo    `protobuf:"bytes,10,rep,name=signatureInfo,proto3" json:"signatureInfo,omitempty"`                                            // 进程文件签名信息
}

func (x *NodeFileInfo) Reset() {
	*x = NodeFileInfo{}
	mi := &file_agent_acdr_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeFileInfo) ProtoMessage() {}

func (x *NodeFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeFileInfo.ProtoReflect.Descriptor instead.
func (*NodeFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{43}
}

func (x *NodeFileInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *NodeFileInfo) GetFileSha256() []byte {
	if x != nil {
		return x.FileSha256
	}
	return nil
}

func (x *NodeFileInfo) GetFileSize() uint32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *NodeFileInfo) GetFileMtime() uint64 {
	if x != nil {
		return x.FileMtime
	}
	return 0
}

func (x *NodeFileInfo) GetFileCtime() uint64 {
	if x != nil {
		return x.FileCtime
	}
	return 0
}

func (x *NodeFileInfo) GetFileAtime() uint64 {
	if x != nil {
		return x.FileAtime
	}
	return 0
}

func (x *NodeFileInfo) GetFileAccessPermission() []byte {
	if x != nil {
		return x.FileAccessPermission
	}
	return nil
}

func (x *NodeFileInfo) GetFileMd5() []byte {
	if x != nil {
		return x.FileMd5
	}
	return nil
}

func (x *NodeFileInfo) GetDetailInfo() *ProcFileDetailInfo {
	if x != nil {
		return x.DetailInfo
	}
	return nil
}

func (x *NodeFileInfo) GetSignatureInfo() []*SignatureInfo {
	if x != nil {
		return x.SignatureInfo
	}
	return nil
}

// URL 节点
type NodeRemoteUrl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *NodeRemoteUrl) Reset() {
	*x = NodeRemoteUrl{}
	mi := &file_agent_acdr_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeRemoteUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeRemoteUrl) ProtoMessage() {}

func (x *NodeRemoteUrl) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeRemoteUrl.ProtoReflect.Descriptor instead.
func (*NodeRemoteUrl) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{44}
}

func (x *NodeRemoteUrl) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type AcdrProcessUnique struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid       uint32 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                              // 进程id
	StartTime uint64 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 进程启动时间
}

func (x *AcdrProcessUnique) Reset() {
	*x = AcdrProcessUnique{}
	mi := &file_agent_acdr_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcdrProcessUnique) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcdrProcessUnique) ProtoMessage() {}

func (x *AcdrProcessUnique) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcdrProcessUnique.ProtoReflect.Descriptor instead.
func (*AcdrProcessUnique) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{45}
}

func (x *AcdrProcessUnique) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *AcdrProcessUnique) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

type AcdrProcessSimple struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid             uint32        `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                                                 // 进程id
	StartTime       int64         `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                    // 进程启动时间，单位：linux毫秒
	UserName        []byte        `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`                        // 进程执行用户
	Command         []byte        `protobuf:"bytes,4,opt,name=command,proto3" json:"command,omitempty"`                                          // 命令行
	ProcessFileInfo *FileBaseInfo `protobuf:"bytes,5,opt,name=process_file_info,json=processFileInfo,proto3" json:"process_file_info,omitempty"` // 进程文件信息
	Euid            uint64        `protobuf:"varint,6,opt,name=euid,proto3" json:"euid,omitempty"`                                               // 进程执行用户 euid
}

func (x *AcdrProcessSimple) Reset() {
	*x = AcdrProcessSimple{}
	mi := &file_agent_acdr_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcdrProcessSimple) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcdrProcessSimple) ProtoMessage() {}

func (x *AcdrProcessSimple) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcdrProcessSimple.ProtoReflect.Descriptor instead.
func (*AcdrProcessSimple) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{46}
}

func (x *AcdrProcessSimple) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *AcdrProcessSimple) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *AcdrProcessSimple) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

func (x *AcdrProcessSimple) GetCommand() []byte {
	if x != nil {
		return x.Command
	}
	return nil
}

func (x *AcdrProcessSimple) GetProcessFileInfo() *FileBaseInfo {
	if x != nil {
		return x.ProcessFileInfo
	}
	return nil
}

func (x *AcdrProcessSimple) GetEuid() uint64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

type FileBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5       []byte `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`                               // md5
	Sha256    []byte `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                         // sha256
	FileName  []byte `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`     // 文件名
	FilePath  []byte `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`     // 文件路径
	FileSize  int64  `protobuf:"varint,5,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`    // 文件大小
	FileAtime int64  `protobuf:"varint,6,opt,name=file_atime,json=fileAtime,proto3" json:"file_atime,omitempty"` // 最后一次访问时间，单位：linux毫秒
	FileMtime int64  `protobuf:"varint,7,opt,name=file_mtime,json=fileMtime,proto3" json:"file_mtime,omitempty"` // 最后一次修改时间，单位：linux毫秒
	FileCtime int64  `protobuf:"varint,8,opt,name=file_ctime,json=fileCtime,proto3" json:"file_ctime,omitempty"` // 创建时间，单位：linux毫秒
}

func (x *FileBaseInfo) Reset() {
	*x = FileBaseInfo{}
	mi := &file_agent_acdr_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileBaseInfo) ProtoMessage() {}

func (x *FileBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileBaseInfo.ProtoReflect.Descriptor instead.
func (*FileBaseInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{47}
}

func (x *FileBaseInfo) GetMd5() []byte {
	if x != nil {
		return x.Md5
	}
	return nil
}

func (x *FileBaseInfo) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

func (x *FileBaseInfo) GetFileName() []byte {
	if x != nil {
		return x.FileName
	}
	return nil
}

func (x *FileBaseInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileBaseInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileBaseInfo) GetFileAtime() int64 {
	if x != nil {
		return x.FileAtime
	}
	return 0
}

func (x *FileBaseInfo) GetFileMtime() int64 {
	if x != nil {
		return x.FileMtime
	}
	return 0
}

func (x *FileBaseInfo) GetFileCtime() int64 {
	if x != nil {
		return x.FileCtime
	}
	return 0
}

// 进程 节点
type NodeProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UniqueChild     *AcdrProcessUnique `protobuf:"bytes,1,opt,name=unique_child,json=uniqueChild,proto3" json:"unique_child,omitempty"`               // 子进程标识
	IsRootProcess   bool               `protobuf:"varint,2,opt,name=is_root_process,json=isRootProcess,proto3" json:"is_root_process,omitempty"`      // 是否是根进程
	Command         string             `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`                                          // 命令行
	ProcessFileInfo *NodeFileInfo      `protobuf:"bytes,4,opt,name=process_file_info,json=processFileInfo,proto3" json:"process_file_info,omitempty"` // 进程对应文件信息
	UserName        string             `protobuf:"bytes,5,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`                        // 进程执行用户
	Euid            uint64             `protobuf:"varint,6,opt,name=euid,proto3" json:"euid,omitempty"`                                               // 进程执行用户 euid
	ExecSrc         *AcdrProcessUnique `protobuf:"bytes,7,opt,name=exec_src,json=execSrc,proto3" json:"exec_src,omitempty"`                           // exec 的源进程, 非 exec 则置零
}

func (x *NodeProcessInfo) Reset() {
	*x = NodeProcessInfo{}
	mi := &file_agent_acdr_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeProcessInfo) ProtoMessage() {}

func (x *NodeProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeProcessInfo.ProtoReflect.Descriptor instead.
func (*NodeProcessInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{48}
}

func (x *NodeProcessInfo) GetUniqueChild() *AcdrProcessUnique {
	if x != nil {
		return x.UniqueChild
	}
	return nil
}

func (x *NodeProcessInfo) GetIsRootProcess() bool {
	if x != nil {
		return x.IsRootProcess
	}
	return false
}

func (x *NodeProcessInfo) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *NodeProcessInfo) GetProcessFileInfo() *NodeFileInfo {
	if x != nil {
		return x.ProcessFileInfo
	}
	return nil
}

func (x *NodeProcessInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *NodeProcessInfo) GetEuid() uint64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *NodeProcessInfo) GetExecSrc() *AcdrProcessUnique {
	if x != nil {
		return x.ExecSrc
	}
	return nil
}

// 父子进程关系更新
type AcdrProcessParrentRelationUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HardRelation   bool               `protobuf:"varint,1,opt,name=hard_relation,json=hardRelation,proto3" json:"hard_relation,omitempty"` //true表示强关系，false表示弱干关系
	ProcessParrent *AcdrProcessUnique `protobuf:"bytes,2,opt,name=process_parrent,json=processParrent,proto3" json:"process_parrent,omitempty"`
	ProcessChiled  *AcdrProcessUnique `protobuf:"bytes,3,opt,name=process_chiled,json=processChiled,proto3" json:"process_chiled,omitempty"`
}

func (x *AcdrProcessParrentRelationUpdate) Reset() {
	*x = AcdrProcessParrentRelationUpdate{}
	mi := &file_agent_acdr_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AcdrProcessParrentRelationUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcdrProcessParrentRelationUpdate) ProtoMessage() {}

func (x *AcdrProcessParrentRelationUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcdrProcessParrentRelationUpdate.ProtoReflect.Descriptor instead.
func (*AcdrProcessParrentRelationUpdate) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{49}
}

func (x *AcdrProcessParrentRelationUpdate) GetHardRelation() bool {
	if x != nil {
		return x.HardRelation
	}
	return false
}

func (x *AcdrProcessParrentRelationUpdate) GetProcessParrent() *AcdrProcessUnique {
	if x != nil {
		return x.ProcessParrent
	}
	return nil
}

func (x *AcdrProcessParrentRelationUpdate) GetProcessChiled() *AcdrProcessUnique {
	if x != nil {
		return x.ProcessChiled
	}
	return nil
}

type FileRename struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From *NodeFileInfo `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To   *NodeFileInfo `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
}

func (x *FileRename) Reset() {
	*x = FileRename{}
	mi := &file_agent_acdr_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileRename) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileRename) ProtoMessage() {}

func (x *FileRename) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileRename.ProtoReflect.Descriptor instead.
func (*FileRename) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{50}
}

func (x *FileRename) GetFrom() *NodeFileInfo {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *FileRename) GetTo() *NodeFileInfo {
	if x != nil {
		return x.To
	}
	return nil
}

type RegCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegPath string `protobuf:"bytes,1,opt,name=reg_path,json=regPath,proto3" json:"reg_path,omitempty"`
}

func (x *RegCreate) Reset() {
	*x = RegCreate{}
	mi := &file_agent_acdr_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegCreate) ProtoMessage() {}

func (x *RegCreate) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegCreate.ProtoReflect.Descriptor instead.
func (*RegCreate) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{51}
}

func (x *RegCreate) GetRegPath() string {
	if x != nil {
		return x.RegPath
	}
	return ""
}

type RegDelete struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegPath string `protobuf:"bytes,1,opt,name=reg_path,json=regPath,proto3" json:"reg_path,omitempty"`
}

func (x *RegDelete) Reset() {
	*x = RegDelete{}
	mi := &file_agent_acdr_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegDelete) ProtoMessage() {}

func (x *RegDelete) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegDelete.ProtoReflect.Descriptor instead.
func (*RegDelete) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{52}
}

func (x *RegDelete) GetRegPath() string {
	if x != nil {
		return x.RegPath
	}
	return ""
}

type RegWrite struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegPath    string `protobuf:"bytes,1,opt,name=reg_path,json=regPath,proto3" json:"reg_path,omitempty"`
	WriteValue string `protobuf:"bytes,2,opt,name=write_value,json=writeValue,proto3" json:"write_value,omitempty"`
}

func (x *RegWrite) Reset() {
	*x = RegWrite{}
	mi := &file_agent_acdr_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegWrite) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegWrite) ProtoMessage() {}

func (x *RegWrite) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegWrite.ProtoReflect.Descriptor instead.
func (*RegWrite) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{53}
}

func (x *RegWrite) GetRegPath() string {
	if x != nil {
		return x.RegPath
	}
	return ""
}

func (x *RegWrite) GetWriteValue() string {
	if x != nil {
		return x.WriteValue
	}
	return ""
}

type RegSetSecurity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegPath string `protobuf:"bytes,1,opt,name=reg_path,json=regPath,proto3" json:"reg_path,omitempty"`
}

func (x *RegSetSecurity) Reset() {
	*x = RegSetSecurity{}
	mi := &file_agent_acdr_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegSetSecurity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegSetSecurity) ProtoMessage() {}

func (x *RegSetSecurity) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegSetSecurity.ProtoReflect.Descriptor instead.
func (*RegSetSecurity) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{54}
}

func (x *RegSetSecurity) GetRegPath() string {
	if x != nil {
		return x.RegPath
	}
	return ""
}

type ScriptHttp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url   string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	IsGet bool   `protobuf:"varint,2,opt,name=is_get,json=isGet,proto3" json:"is_get,omitempty"` //是否是GET请求
}

func (x *ScriptHttp) Reset() {
	*x = ScriptHttp{}
	mi := &file_agent_acdr_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptHttp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptHttp) ProtoMessage() {}

func (x *ScriptHttp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptHttp.ProtoReflect.Descriptor instead.
func (*ScriptHttp) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{55}
}

func (x *ScriptHttp) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ScriptHttp) GetIsGet() bool {
	if x != nil {
		return x.IsGet
	}
	return false
}

type ScriptImageLoad struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImagePath string `protobuf:"bytes,1,opt,name=image_path,json=imagePath,proto3" json:"image_path,omitempty"`
}

func (x *ScriptImageLoad) Reset() {
	*x = ScriptImageLoad{}
	mi := &file_agent_acdr_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptImageLoad) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptImageLoad) ProtoMessage() {}

func (x *ScriptImageLoad) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptImageLoad.ProtoReflect.Descriptor instead.
func (*ScriptImageLoad) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{56}
}

func (x *ScriptImageLoad) GetImagePath() string {
	if x != nil {
		return x.ImagePath
	}
	return ""
}

type ScriptRunWmicCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Cmd      string `protobuf:"bytes,1,opt,name=cmd,proto3" json:"cmd,omitempty"`
	IsRemote bool   `protobuf:"varint,2,opt,name=is_remote,json=isRemote,proto3" json:"is_remote,omitempty"` //是否是GET请求
}

func (x *ScriptRunWmicCode) Reset() {
	*x = ScriptRunWmicCode{}
	mi := &file_agent_acdr_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptRunWmicCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptRunWmicCode) ProtoMessage() {}

func (x *ScriptRunWmicCode) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptRunWmicCode.ProtoReflect.Descriptor instead.
func (*ScriptRunWmicCode) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{57}
}

func (x *ScriptRunWmicCode) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *ScriptRunWmicCode) GetIsRemote() bool {
	if x != nil {
		return x.IsRemote
	}
	return false
}

type ScriptScheduleCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path string `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
	Args string `protobuf:"bytes,2,opt,name=args,proto3" json:"args,omitempty"`
}

func (x *ScriptScheduleCreate) Reset() {
	*x = ScriptScheduleCreate{}
	mi := &file_agent_acdr_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptScheduleCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptScheduleCreate) ProtoMessage() {}

func (x *ScriptScheduleCreate) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptScheduleCreate.ProtoReflect.Descriptor instead.
func (*ScriptScheduleCreate) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{58}
}

func (x *ScriptScheduleCreate) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ScriptScheduleCreate) GetArgs() string {
	if x != nil {
		return x.Args
	}
	return ""
}

type ScriptGetApiAddr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ApiName string `protobuf:"bytes,1,opt,name=api_name,json=apiName,proto3" json:"api_name,omitempty"`
}

func (x *ScriptGetApiAddr) Reset() {
	*x = ScriptGetApiAddr{}
	mi := &file_agent_acdr_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptGetApiAddr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptGetApiAddr) ProtoMessage() {}

func (x *ScriptGetApiAddr) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptGetApiAddr.ProtoReflect.Descriptor instead.
func (*ScriptGetApiAddr) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{59}
}

func (x *ScriptGetApiAddr) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

type ScriptWmicWin32Share struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShareName string `protobuf:"bytes,1,opt,name=share_name,json=shareName,proto3" json:"share_name,omitempty"`
	SharePath string `protobuf:"bytes,2,opt,name=share_path,json=sharePath,proto3" json:"share_path,omitempty"`
}

func (x *ScriptWmicWin32Share) Reset() {
	*x = ScriptWmicWin32Share{}
	mi := &file_agent_acdr_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptWmicWin32Share) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptWmicWin32Share) ProtoMessage() {}

func (x *ScriptWmicWin32Share) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptWmicWin32Share.ProtoReflect.Descriptor instead.
func (*ScriptWmicWin32Share) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{60}
}

func (x *ScriptWmicWin32Share) GetShareName() string {
	if x != nil {
		return x.ShareName
	}
	return ""
}

func (x *ScriptWmicWin32Share) GetSharePath() string {
	if x != nil {
		return x.SharePath
	}
	return ""
}

type ScriptWmicTerminateProcess struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Proc *NodeProcessInfo `protobuf:"bytes,1,opt,name=proc,proto3" json:"proc,omitempty"`
}

func (x *ScriptWmicTerminateProcess) Reset() {
	*x = ScriptWmicTerminateProcess{}
	mi := &file_agent_acdr_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptWmicTerminateProcess) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptWmicTerminateProcess) ProtoMessage() {}

func (x *ScriptWmicTerminateProcess) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptWmicTerminateProcess.ProtoReflect.Descriptor instead.
func (*ScriptWmicTerminateProcess) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{61}
}

func (x *ScriptWmicTerminateProcess) GetProc() *NodeProcessInfo {
	if x != nil {
		return x.Proc
	}
	return nil
}

type ScriptWmicRegOper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperType  string `protobuf:"bytes,1,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	KeyPath   string `protobuf:"bytes,2,opt,name=key_path,json=keyPath,proto3" json:"key_path,omitempty"`
	ValuePath string `protobuf:"bytes,3,opt,name=value_path,json=valuePath,proto3" json:"value_path,omitempty"`
}

func (x *ScriptWmicRegOper) Reset() {
	*x = ScriptWmicRegOper{}
	mi := &file_agent_acdr_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptWmicRegOper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptWmicRegOper) ProtoMessage() {}

func (x *ScriptWmicRegOper) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptWmicRegOper.ProtoReflect.Descriptor instead.
func (*ScriptWmicRegOper) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{62}
}

func (x *ScriptWmicRegOper) GetOperType() string {
	if x != nil {
		return x.OperType
	}
	return ""
}

func (x *ScriptWmicRegOper) GetKeyPath() string {
	if x != nil {
		return x.KeyPath
	}
	return ""
}

func (x *ScriptWmicRegOper) GetValuePath() string {
	if x != nil {
		return x.ValuePath
	}
	return ""
}

type ScriptWmicServiceOper struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OperType       string `protobuf:"bytes,1,opt,name=oper_type,json=operType,proto3" json:"oper_type,omitempty"`
	ServiceName    string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceExePath string `protobuf:"bytes,3,opt,name=service_exe_path,json=serviceExePath,proto3" json:"service_exe_path,omitempty"`
}

func (x *ScriptWmicServiceOper) Reset() {
	*x = ScriptWmicServiceOper{}
	mi := &file_agent_acdr_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptWmicServiceOper) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptWmicServiceOper) ProtoMessage() {}

func (x *ScriptWmicServiceOper) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptWmicServiceOper.ProtoReflect.Descriptor instead.
func (*ScriptWmicServiceOper) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{63}
}

func (x *ScriptWmicServiceOper) GetOperType() string {
	if x != nil {
		return x.OperType
	}
	return ""
}

func (x *ScriptWmicServiceOper) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ScriptWmicServiceOper) GetServiceExePath() string {
	if x != nil {
		return x.ServiceExePath
	}
	return ""
}

type ScriptWmicQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QueryLanguage string `protobuf:"bytes,1,opt,name=query_language,json=queryLanguage,proto3" json:"query_language,omitempty"`
	QueryCmd      string `protobuf:"bytes,2,opt,name=query_cmd,json=queryCmd,proto3" json:"query_cmd,omitempty"` // 是否是GET请求
}

func (x *ScriptWmicQuery) Reset() {
	*x = ScriptWmicQuery{}
	mi := &file_agent_acdr_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptWmicQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptWmicQuery) ProtoMessage() {}

func (x *ScriptWmicQuery) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptWmicQuery.ProtoReflect.Descriptor instead.
func (*ScriptWmicQuery) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{64}
}

func (x *ScriptWmicQuery) GetQueryLanguage() string {
	if x != nil {
		return x.QueryLanguage
	}
	return ""
}

func (x *ScriptWmicQuery) GetQueryCmd() string {
	if x != nil {
		return x.QueryCmd
	}
	return ""
}

type ScriptAmsiByAmsiContext struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ScriptAmsiByAmsiContext) Reset() {
	*x = ScriptAmsiByAmsiContext{}
	mi := &file_agent_acdr_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptAmsiByAmsiContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptAmsiByAmsiContext) ProtoMessage() {}

func (x *ScriptAmsiByAmsiContext) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptAmsiByAmsiContext.ProtoReflect.Descriptor instead.
func (*ScriptAmsiByAmsiContext) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{65}
}

func (x *ScriptAmsiByAmsiContext) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type ScriptAmsiDllHijack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Path *NodeFileInfo `protobuf:"bytes,1,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *ScriptAmsiDllHijack) Reset() {
	*x = ScriptAmsiDllHijack{}
	mi := &file_agent_acdr_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptAmsiDllHijack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptAmsiDllHijack) ProtoMessage() {}

func (x *ScriptAmsiDllHijack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptAmsiDllHijack.ProtoReflect.Descriptor instead.
func (*ScriptAmsiDllHijack) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{66}
}

func (x *ScriptAmsiDllHijack) GetPath() *NodeFileInfo {
	if x != nil {
		return x.Path
	}
	return nil
}

type ScriptEmail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderAddr string `protobuf:"bytes,1,opt,name=sender_addr,json=senderAddr,proto3" json:"sender_addr,omitempty"`
	RecvAddr   string `protobuf:"bytes,2,opt,name=recv_addr,json=recvAddr,proto3" json:"recv_addr,omitempty"`
}

func (x *ScriptEmail) Reset() {
	*x = ScriptEmail{}
	mi := &file_agent_acdr_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScriptEmail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScriptEmail) ProtoMessage() {}

func (x *ScriptEmail) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScriptEmail.ProtoReflect.Descriptor instead.
func (*ScriptEmail) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{67}
}

func (x *ScriptEmail) GetSenderAddr() string {
	if x != nil {
		return x.SenderAddr
	}
	return ""
}

func (x *ScriptEmail) GetRecvAddr() string {
	if x != nil {
		return x.RecvAddr
	}
	return ""
}

type LogClear struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LogFile string `protobuf:"bytes,1,opt,name=log_file,json=logFile,proto3" json:"log_file,omitempty"`
}

func (x *LogClear) Reset() {
	*x = LogClear{}
	mi := &file_agent_acdr_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogClear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogClear) ProtoMessage() {}

func (x *LogClear) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogClear.ProtoReflect.Descriptor instead.
func (*LogClear) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{68}
}

func (x *LogClear) GetLogFile() string {
	if x != nil {
		return x.LogFile
	}
	return ""
}

type CveAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CveName      string `protobuf:"bytes,1,opt,name=cve_name,json=cveName,proto3" json:"cve_name,omitempty"`
	AttackSource string `protobuf:"bytes,2,opt,name=attack_source,json=attackSource,proto3" json:"attack_source,omitempty"` //攻击源，可能是ip或者http:\\127.0.0.1\1.exe
}

func (x *CveAttack) Reset() {
	*x = CveAttack{}
	mi := &file_agent_acdr_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CveAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CveAttack) ProtoMessage() {}

func (x *CveAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CveAttack.ProtoReflect.Descriptor instead.
func (*CveAttack) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{69}
}

func (x *CveAttack) GetCveName() string {
	if x != nil {
		return x.CveName
	}
	return ""
}

func (x *CveAttack) GetAttackSource() string {
	if x != nil {
		return x.AttackSource
	}
	return ""
}

type RemoteBugOverflow struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CveName string        `protobuf:"bytes,1,opt,name=cve_name,json=cveName,proto3" json:"cve_name,omitempty"` // 漏洞CVE名称
	Node    *NodeAddrInfo `protobuf:"bytes,2,opt,name=node,proto3" json:"node,omitempty"`                      // 攻击端信息
}

func (x *RemoteBugOverflow) Reset() {
	*x = RemoteBugOverflow{}
	mi := &file_agent_acdr_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoteBugOverflow) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoteBugOverflow) ProtoMessage() {}

func (x *RemoteBugOverflow) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoteBugOverflow.ProtoReflect.Descriptor instead.
func (*RemoteBugOverflow) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{70}
}

func (x *RemoteBugOverflow) GetCveName() string {
	if x != nil {
		return x.CveName
	}
	return ""
}

func (x *RemoteBugOverflow) GetNode() *NodeAddrInfo {
	if x != nil {
		return x.Node
	}
	return nil
}

type PuppetProcessAttack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseAddr uint64 `protobuf:"varint,1,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"` // 傀儡基地址
}

func (x *PuppetProcessAttack) Reset() {
	*x = PuppetProcessAttack{}
	mi := &file_agent_acdr_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PuppetProcessAttack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PuppetProcessAttack) ProtoMessage() {}

func (x *PuppetProcessAttack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PuppetProcessAttack.ProtoReflect.Descriptor instead.
func (*PuppetProcessAttack) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{71}
}

func (x *PuppetProcessAttack) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

type V01PtraceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcInfo *NodeProcessInfo `protobuf:"bytes,1,opt,name=proc_info,json=procInfo,proto3" json:"proc_info,omitempty"`
	Mode     uint32           `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"` // 注入模式: 0: 读; 1: 写
	Addr     uint64           `protobuf:"varint,3,opt,name=addr,proto3" json:"addr,omitempty"`
}

func (x *V01PtraceInfo) Reset() {
	*x = V01PtraceInfo{}
	mi := &file_agent_acdr_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V01PtraceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V01PtraceInfo) ProtoMessage() {}

func (x *V01PtraceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V01PtraceInfo.ProtoReflect.Descriptor instead.
func (*V01PtraceInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{72}
}

func (x *V01PtraceInfo) GetProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.ProcInfo
	}
	return nil
}

func (x *V01PtraceInfo) GetMode() uint32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *V01PtraceInfo) GetAddr() uint64 {
	if x != nil {
		return x.Addr
	}
	return 0
}

type V01KillInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcInfo *NodeProcessInfo `protobuf:"bytes,1,opt,name=proc_info,json=procInfo,proto3" json:"proc_info,omitempty"`
	Signal   uint32           `protobuf:"varint,2,opt,name=signal,proto3" json:"signal,omitempty"` //信号
}

func (x *V01KillInfo) Reset() {
	*x = V01KillInfo{}
	mi := &file_agent_acdr_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V01KillInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V01KillInfo) ProtoMessage() {}

func (x *V01KillInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V01KillInfo.ProtoReflect.Descriptor instead.
func (*V01KillInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{73}
}

func (x *V01KillInfo) GetProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.ProcInfo
	}
	return nil
}

func (x *V01KillInfo) GetSignal() uint32 {
	if x != nil {
		return x.Signal
	}
	return 0
}

type SetRlimit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Resource EnumResourceType `protobuf:"varint,1,opt,name=resource,proto3,enum=agent.EnumResourceType" json:"resource,omitempty"` //资源类型
}

func (x *SetRlimit) Reset() {
	*x = SetRlimit{}
	mi := &file_agent_acdr_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetRlimit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRlimit) ProtoMessage() {}

func (x *SetRlimit) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRlimit.ProtoReflect.Descriptor instead.
func (*SetRlimit) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{74}
}

func (x *SetRlimit) GetResource() EnumResourceType {
	if x != nil {
		return x.Resource
	}
	return EnumResourceType_RLIMIT_CPU
}

type MemActionOtherInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RetAddr          uint64 `protobuf:"varint,1,opt,name=ret_addr,json=retAddr,proto3" json:"ret_addr,omitempty"`                             // 返回指令所在地址
	RetModuleName    string `protobuf:"bytes,2,opt,name=ret_module_name,json=retModuleName,proto3" json:"ret_module_name,omitempty"`          // 返回指令所在模块
	CollectPointName string `protobuf:"bytes,3,opt,name=collect_point_name,json=collectPointName,proto3" json:"collect_point_name,omitempty"` // 检查点名称
}

func (x *MemActionOtherInfo) Reset() {
	*x = MemActionOtherInfo{}
	mi := &file_agent_acdr_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemActionOtherInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemActionOtherInfo) ProtoMessage() {}

func (x *MemActionOtherInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemActionOtherInfo.ProtoReflect.Descriptor instead.
func (*MemActionOtherInfo) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{75}
}

func (x *MemActionOtherInfo) GetRetAddr() uint64 {
	if x != nil {
		return x.RetAddr
	}
	return 0
}

func (x *MemActionOtherInfo) GetRetModuleName() string {
	if x != nil {
		return x.RetModuleName
	}
	return ""
}

func (x *MemActionOtherInfo) GetCollectPointName() string {
	if x != nil {
		return x.CollectPointName
	}
	return ""
}

type QueueApcThread struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	ApcRoutine   uint64              `protobuf:"varint,3,opt,name=apc_routine,json=apcRoutine,proto3" json:"apc_routine,omitempty"` //apc地址
	Info         *MemActionOtherInfo `protobuf:"bytes,4,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *QueueApcThread) Reset() {
	*x = QueueApcThread{}
	mi := &file_agent_acdr_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueueApcThread) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueueApcThread) ProtoMessage() {}

func (x *QueueApcThread) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueueApcThread.ProtoReflect.Descriptor instead.
func (*QueueApcThread) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{76}
}

func (x *QueueApcThread) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *QueueApcThread) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *QueueApcThread) GetApcRoutine() uint64 {
	if x != nil {
		return x.ApcRoutine
	}
	return 0
}

func (x *QueueApcThread) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type SetContextThread struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	SetMode      uint32              `protobuf:"varint,2,opt,name=set_mode,json=setMode,proto3" json:"set_mode,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *SetContextThread) Reset() {
	*x = SetContextThread{}
	mi := &file_agent_acdr_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetContextThread) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetContextThread) ProtoMessage() {}

func (x *SetContextThread) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetContextThread.ProtoReflect.Descriptor instead.
func (*SetContextThread) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{77}
}

func (x *SetContextThread) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *SetContextThread) GetSetMode() uint32 {
	if x != nil {
		return x.SetMode
	}
	return 0
}

func (x *SetContextThread) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type ProtectVirtualMem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	OldProtect   ProtectType         `protobuf:"varint,3,opt,name=old_protect,json=oldProtect,proto3,enum=agent.ProtectType" json:"old_protect,omitempty"`
	NewProtect   ProtectType         `protobuf:"varint,4,opt,name=new_protect,json=newProtect,proto3,enum=agent.ProtectType" json:"new_protect,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *ProtectVirtualMem) Reset() {
	*x = ProtectVirtualMem{}
	mi := &file_agent_acdr_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProtectVirtualMem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProtectVirtualMem) ProtoMessage() {}

func (x *ProtectVirtualMem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProtectVirtualMem.ProtoReflect.Descriptor instead.
func (*ProtectVirtualMem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{78}
}

func (x *ProtectVirtualMem) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *ProtectVirtualMem) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *ProtectVirtualMem) GetOldProtect() ProtectType {
	if x != nil {
		return x.OldProtect
	}
	return ProtectType_PT_PAGE_UNDEFINED
}

func (x *ProtectVirtualMem) GetNewProtect() ProtectType {
	if x != nil {
		return x.NewProtect
	}
	return ProtectType_PT_PAGE_UNDEFINED
}

func (x *ProtectVirtualMem) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type AllocateVirtualMem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	Protect      ProtectType         `protobuf:"varint,3,opt,name=protect,proto3,enum=agent.ProtectType" json:"protect,omitempty"`
	AllocType    AllocationType      `protobuf:"varint,4,opt,name=alloc_type,json=allocType,proto3,enum=agent.AllocationType" json:"alloc_type,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *AllocateVirtualMem) Reset() {
	*x = AllocateVirtualMem{}
	mi := &file_agent_acdr_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AllocateVirtualMem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AllocateVirtualMem) ProtoMessage() {}

func (x *AllocateVirtualMem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AllocateVirtualMem.ProtoReflect.Descriptor instead.
func (*AllocateVirtualMem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{79}
}

func (x *AllocateVirtualMem) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *AllocateVirtualMem) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *AllocateVirtualMem) GetProtect() ProtectType {
	if x != nil {
		return x.Protect
	}
	return ProtectType_PT_PAGE_UNDEFINED
}

func (x *AllocateVirtualMem) GetAllocType() AllocationType {
	if x != nil {
		return x.AllocType
	}
	return AllocationType_AT_MEM_UNDEFINED
}

func (x *AllocateVirtualMem) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type WriteVirtualMem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	WriteBufAddr uint64              `protobuf:"varint,3,opt,name=write_buf_addr,json=writeBufAddr,proto3" json:"write_buf_addr,omitempty"`
	WriteLen     uint32              `protobuf:"varint,4,opt,name=write_len,json=writeLen,proto3" json:"write_len,omitempty"`
	RiskMode     WriteMemRiskMode    `protobuf:"varint,5,opt,name=risk_mode,json=riskMode,proto3,enum=agent.WriteMemRiskMode" json:"risk_mode,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,6,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *WriteVirtualMem) Reset() {
	*x = WriteVirtualMem{}
	mi := &file_agent_acdr_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WriteVirtualMem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteVirtualMem) ProtoMessage() {}

func (x *WriteVirtualMem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteVirtualMem.ProtoReflect.Descriptor instead.
func (*WriteVirtualMem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{80}
}

func (x *WriteVirtualMem) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *WriteVirtualMem) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *WriteVirtualMem) GetWriteBufAddr() uint64 {
	if x != nil {
		return x.WriteBufAddr
	}
	return 0
}

func (x *WriteVirtualMem) GetWriteLen() uint32 {
	if x != nil {
		return x.WriteLen
	}
	return 0
}

func (x *WriteVirtualMem) GetRiskMode() WriteMemRiskMode {
	if x != nil {
		return x.RiskMode
	}
	return WriteMemRiskMode_WMRM_DEFAULT
}

func (x *WriteVirtualMem) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type ReadVirtualMem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	ReadBufAddr  uint64              `protobuf:"varint,3,opt,name=read_buf_addr,json=readBufAddr,proto3" json:"read_buf_addr,omitempty"`
	ReadLen      uint32              `protobuf:"varint,4,opt,name=read_len,json=readLen,proto3" json:"read_len,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *ReadVirtualMem) Reset() {
	*x = ReadVirtualMem{}
	mi := &file_agent_acdr_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadVirtualMem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadVirtualMem) ProtoMessage() {}

func (x *ReadVirtualMem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadVirtualMem.ProtoReflect.Descriptor instead.
func (*ReadVirtualMem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{81}
}

func (x *ReadVirtualMem) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *ReadVirtualMem) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *ReadVirtualMem) GetReadBufAddr() uint64 {
	if x != nil {
		return x.ReadBufAddr
	}
	return 0
}

func (x *ReadVirtualMem) GetReadLen() uint32 {
	if x != nil {
		return x.ReadLen
	}
	return 0
}

func (x *ReadVirtualMem) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type MapViewOfSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeProcInfo *NodeProcessInfo    `protobuf:"bytes,1,opt,name=node_proc_info,json=nodeProcInfo,proto3" json:"node_proc_info,omitempty"`
	BaseAddr     uint64              `protobuf:"varint,2,opt,name=base_addr,json=baseAddr,proto3" json:"base_addr,omitempty"`
	Protect      ProtectType         `protobuf:"varint,3,opt,name=protect,proto3,enum=agent.ProtectType" json:"protect,omitempty"`
	AllocType    AllocationType      `protobuf:"varint,4,opt,name=alloc_type,json=allocType,proto3,enum=agent.AllocationType" json:"alloc_type,omitempty"`
	Info         *MemActionOtherInfo `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`
}

func (x *MapViewOfSection) Reset() {
	*x = MapViewOfSection{}
	mi := &file_agent_acdr_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MapViewOfSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MapViewOfSection) ProtoMessage() {}

func (x *MapViewOfSection) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MapViewOfSection.ProtoReflect.Descriptor instead.
func (*MapViewOfSection) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{82}
}

func (x *MapViewOfSection) GetNodeProcInfo() *NodeProcessInfo {
	if x != nil {
		return x.NodeProcInfo
	}
	return nil
}

func (x *MapViewOfSection) GetBaseAddr() uint64 {
	if x != nil {
		return x.BaseAddr
	}
	return 0
}

func (x *MapViewOfSection) GetProtect() ProtectType {
	if x != nil {
		return x.Protect
	}
	return ProtectType_PT_PAGE_UNDEFINED
}

func (x *MapViewOfSection) GetAllocType() AllocationType {
	if x != nil {
		return x.AllocType
	}
	return AllocationType_AT_MEM_UNDEFINED
}

func (x *MapViewOfSection) GetInfo() *MemActionOtherInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type WhiteAddBlack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportId    WhiteAddBlack_ReportType `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3,enum=agent.WhiteAddBlack_ReportType" json:"report_id,omitempty"` // 异常点编号
	DllFileInfo *NodeFileInfo            `protobuf:"bytes,2,opt,name=dll_file_info,json=dllFileInfo,proto3" json:"dll_file_info,omitempty"`                           // DLL 文件信息
}

func (x *WhiteAddBlack) Reset() {
	*x = WhiteAddBlack{}
	mi := &file_agent_acdr_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhiteAddBlack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhiteAddBlack) ProtoMessage() {}

func (x *WhiteAddBlack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhiteAddBlack.ProtoReflect.Descriptor instead.
func (*WhiteAddBlack) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{83}
}

func (x *WhiteAddBlack) GetReportId() WhiteAddBlack_ReportType {
	if x != nil {
		return x.ReportId
	}
	return WhiteAddBlack_UNKNOWN
}

func (x *WhiteAddBlack) GetDllFileInfo() *NodeFileInfo {
	if x != nil {
		return x.DllFileInfo
	}
	return nil
}

type OpenDeviceObject struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeviceName string `protobuf:"bytes,1,opt,name=device_name,json=deviceName,proto3" json:"device_name,omitempty"`
}

func (x *OpenDeviceObject) Reset() {
	*x = OpenDeviceObject{}
	mi := &file_agent_acdr_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenDeviceObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenDeviceObject) ProtoMessage() {}

func (x *OpenDeviceObject) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenDeviceObject.ProtoReflect.Descriptor instead.
func (*OpenDeviceObject) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{84}
}

func (x *OpenDeviceObject) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

type CreateServiceAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServicePath string `protobuf:"bytes,2,opt,name=service_path,json=servicePath,proto3" json:"service_path,omitempty"` // 服务二进制文件路径, 路径包含自动启动服务的参数
}

func (x *CreateServiceAction) Reset() {
	*x = CreateServiceAction{}
	mi := &file_agent_acdr_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceAction) ProtoMessage() {}

func (x *CreateServiceAction) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceAction.ProtoReflect.Descriptor instead.
func (*CreateServiceAction) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{85}
}

func (x *CreateServiceAction) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CreateServiceAction) GetServicePath() string {
	if x != nil {
		return x.ServicePath
	}
	return ""
}

type StartServiceAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServiceName string `protobuf:"bytes,1,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServicePath string `protobuf:"bytes,2,opt,name=service_path,json=servicePath,proto3" json:"service_path,omitempty"` // 服务二进制文件路径, 路径包含自动启动服务的参数
}

func (x *StartServiceAction) Reset() {
	*x = StartServiceAction{}
	mi := &file_agent_acdr_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartServiceAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartServiceAction) ProtoMessage() {}

func (x *StartServiceAction) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartServiceAction.ProtoReflect.Descriptor instead.
func (*StartServiceAction) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{86}
}

func (x *StartServiceAction) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *StartServiceAction) GetServicePath() string {
	if x != nil {
		return x.ServicePath
	}
	return ""
}

type CreateTaskScheduler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskSchedulerName string `protobuf:"bytes,1,opt,name=task_scheduler_name,json=taskSchedulerName,proto3" json:"task_scheduler_name,omitempty"`
	CreateAuthor      string `protobuf:"bytes,2,opt,name=create_author,json=createAuthor,proto3" json:"create_author,omitempty"` // 创建计划任务的用户, 主机名+用户, 例: DESKTOP-FVPU73C\ anxinkejiadmin
	Command           string `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"`                               // 计划任务执行的命令行
	Arguments         string `protobuf:"bytes,4,opt,name=arguments,proto3" json:"arguments,omitempty"`                           // 计划任务执行参数
}

func (x *CreateTaskScheduler) Reset() {
	*x = CreateTaskScheduler{}
	mi := &file_agent_acdr_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTaskScheduler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskScheduler) ProtoMessage() {}

func (x *CreateTaskScheduler) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskScheduler.ProtoReflect.Descriptor instead.
func (*CreateTaskScheduler) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{87}
}

func (x *CreateTaskScheduler) GetTaskSchedulerName() string {
	if x != nil {
		return x.TaskSchedulerName
	}
	return ""
}

func (x *CreateTaskScheduler) GetCreateAuthor() string {
	if x != nil {
		return x.CreateAuthor
	}
	return ""
}

func (x *CreateTaskScheduler) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *CreateTaskScheduler) GetArguments() string {
	if x != nil {
		return x.Arguments
	}
	return ""
}

type StartTaskScheduler struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskSchedulerName string           `protobuf:"bytes,1,opt,name=task_scheduler_name,json=taskSchedulerName,proto3" json:"task_scheduler_name,omitempty"`
	CreateAuthor      string           `protobuf:"bytes,2,opt,name=create_author,json=createAuthor,proto3" json:"create_author,omitempty"` // 创建计划任务的用户, 主机名+用户, 例: DESKTOP-FVPU73C\ anxinkejiadmin
	StartProcess      *NodeProcessInfo `protobuf:"bytes,3,opt,name=start_process,json=startProcess,proto3" json:"start_process,omitempty"`
}

func (x *StartTaskScheduler) Reset() {
	*x = StartTaskScheduler{}
	mi := &file_agent_acdr_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartTaskScheduler) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartTaskScheduler) ProtoMessage() {}

func (x *StartTaskScheduler) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartTaskScheduler.ProtoReflect.Descriptor instead.
func (*StartTaskScheduler) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{88}
}

func (x *StartTaskScheduler) GetTaskSchedulerName() string {
	if x != nil {
		return x.TaskSchedulerName
	}
	return ""
}

func (x *StartTaskScheduler) GetCreateAuthor() string {
	if x != nil {
		return x.CreateAuthor
	}
	return ""
}

func (x *StartTaskScheduler) GetStartProcess() *NodeProcessInfo {
	if x != nil {
		return x.StartProcess
	}
	return nil
}

// 行为信息
type V01RuleActionItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActionType      V01ActionType      `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=agent.V01ActionType" json:"action_type,omitempty"` // 行为类型
	TriggerTime     uint64             `protobuf:"varint,2,opt,name=trigger_time,json=triggerTime,proto3" json:"trigger_time,omitempty"`                       // 触发该行为的时间
	ProcessUnique   *AcdrProcessUnique `protobuf:"bytes,3,opt,name=process_unique,json=processUnique,proto3" json:"process_unique,omitempty"`                  // 进程唯一信息
	ProcessFileInfo *NodeFileInfo      `protobuf:"bytes,4,opt,name=process_file_info,json=processFileInfo,proto3" json:"process_file_info,omitempty"`          // 进程文件信息, 仅在线索上报中有值
	// Types that are assignable to ActionContent:
	//
	//	*V01RuleActionItem_ProcessRelationUpdate
	//	*V01RuleActionItem_ProcessCreate
	//	*V01RuleActionItem_FileCreate
	//	*V01RuleActionItem_FileWrite
	//	*V01RuleActionItem_FileRead
	//	*V01RuleActionItem_FileDelete
	//	*V01RuleActionItem_FileRename
	//	*V01RuleActionItem_NetConnect
	//	*V01RuleActionItem_NetAccept
	//	*V01RuleActionItem_NetListen
	//	*V01RuleActionItem_RegCreate
	//	*V01RuleActionItem_ScriptHttp
	//	*V01RuleActionItem_ScriptImageLoad
	//	*V01RuleActionItem_ScriptRunWmicCode
	//	*V01RuleActionItem_ScriptScheduleCreate
	//	*V01RuleActionItem_ScriptGetApiAddr
	//	*V01RuleActionItem_ScriptWmicWin32Share
	//	*V01RuleActionItem_ScriptWmicTerminateProcess
	//	*V01RuleActionItem_ScriptWmicRegOper
	//	*V01RuleActionItem_ScriptWmicServiceOper
	//	*V01RuleActionItem_ScriptWmicQuery
	//	*V01RuleActionItem_ScriptAmsiByAmsiContext
	//	*V01RuleActionItem_ScriptAmsiDllHijack
	//	*V01RuleActionItem_ScriptEmail
	//	*V01RuleActionItem_LogClear
	//	*V01RuleActionItem_KillProcessDetail
	//	*V01RuleActionItem_ProcessInjectDetail
	//	*V01RuleActionItem_RegDelete
	//	*V01RuleActionItem_RegWrite
	//	*V01RuleActionItem_RegSetSecurity
	//	*V01RuleActionItem_FileHide
	//	*V01RuleActionItem_FileReadonly
	//	*V01RuleActionItem_LoadRemoteModule
	//	*V01RuleActionItem_WhiteAddBlack
	//	*V01RuleActionItem_Cve
	//	*V01RuleActionItem_RemoteBugOverflow
	//	*V01RuleActionItem_PuppetProcess
	//	*V01RuleActionItem_NetConnectDomain
	//	*V01RuleActionItem_NetSnifferContent
	//	*V01RuleActionItem_EnvHijackContent
	//	*V01RuleActionItem_SelfDeleteContent
	//	*V01RuleActionItem_PtraceContent
	//	*V01RuleActionItem_FileLinkContent
	//	*V01RuleActionItem_CallUsermodehelperContent
	//	*V01RuleActionItem_ReverseShellContent
	//	*V01RuleActionItem_ProcessExecContent
	//	*V01RuleActionItem_RestoreUtimeContent
	//	*V01RuleActionItem_FileIoctlImmutableContent
	//	*V01RuleActionItem_MkdirContent
	//	*V01RuleActionItem_FileSymlink
	//	*V01RuleActionItem_FileSetUid
	//	*V01RuleActionItem_Bpf
	//	*V01RuleActionItem_PrivilegeEscalation
	//	*V01RuleActionItem_FakeExeFileContent
	//	*V01RuleActionItem_HideModuleContent
	//	*V01RuleActionItem_KillContent
	//	*V01RuleActionItem_FileModeChangeContent
	//	*V01RuleActionItem_ResoureLimit
	//	*V01RuleActionItem_FilelessAttackContent
	//	*V01RuleActionItem_FileOpenContent
	//	*V01RuleActionItem_QueueApcDetail
	//	*V01RuleActionItem_SetContextThreadDetail
	//	*V01RuleActionItem_ProtectMemDetail
	//	*V01RuleActionItem_AllocateMemDetail
	//	*V01RuleActionItem_WriteMemDetail
	//	*V01RuleActionItem_ReadMemDetail
	//	*V01RuleActionItem_MapViewSectionDetail
	//	*V01RuleActionItem_DeleteByself
	//	*V01RuleActionItem_OpenDeviceObject
	//	*V01RuleActionItem_CreateService
	//	*V01RuleActionItem_StartService
	//	*V01RuleActionItem_CreateTaskScheduler
	//	*V01RuleActionItem_StartTaskScheduler
	ActionContent isV01RuleActionItem_ActionContent `protobuf_oneof:"ActionContent"`
}

func (x *V01RuleActionItem) Reset() {
	*x = V01RuleActionItem{}
	mi := &file_agent_acdr_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V01RuleActionItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V01RuleActionItem) ProtoMessage() {}

func (x *V01RuleActionItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V01RuleActionItem.ProtoReflect.Descriptor instead.
func (*V01RuleActionItem) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{89}
}

func (x *V01RuleActionItem) GetActionType() V01ActionType {
	if x != nil {
		return x.ActionType
	}
	return V01ActionType_V01AT_DEFAULT
}

func (x *V01RuleActionItem) GetTriggerTime() uint64 {
	if x != nil {
		return x.TriggerTime
	}
	return 0
}

func (x *V01RuleActionItem) GetProcessUnique() *AcdrProcessUnique {
	if x != nil {
		return x.ProcessUnique
	}
	return nil
}

func (x *V01RuleActionItem) GetProcessFileInfo() *NodeFileInfo {
	if x != nil {
		return x.ProcessFileInfo
	}
	return nil
}

func (m *V01RuleActionItem) GetActionContent() isV01RuleActionItem_ActionContent {
	if m != nil {
		return m.ActionContent
	}
	return nil
}

func (x *V01RuleActionItem) GetProcessRelationUpdate() *AcdrProcessParrentRelationUpdate {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ProcessRelationUpdate); ok {
		return x.ProcessRelationUpdate
	}
	return nil
}

func (x *V01RuleActionItem) GetProcessCreate() *NodeProcessInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ProcessCreate); ok {
		return x.ProcessCreate
	}
	return nil
}

func (x *V01RuleActionItem) GetFileCreate() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileCreate); ok {
		return x.FileCreate
	}
	return nil
}

func (x *V01RuleActionItem) GetFileWrite() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileWrite); ok {
		return x.FileWrite
	}
	return nil
}

func (x *V01RuleActionItem) GetFileRead() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileRead); ok {
		return x.FileRead
	}
	return nil
}

func (x *V01RuleActionItem) GetFileDelete() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileDelete); ok {
		return x.FileDelete
	}
	return nil
}

func (x *V01RuleActionItem) GetFileRename() *FileRename {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileRename); ok {
		return x.FileRename
	}
	return nil
}

func (x *V01RuleActionItem) GetNetConnect() *ConnectionInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_NetConnect); ok {
		return x.NetConnect
	}
	return nil
}

func (x *V01RuleActionItem) GetNetAccept() *ConnectionInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_NetAccept); ok {
		return x.NetAccept
	}
	return nil
}

func (x *V01RuleActionItem) GetNetListen() *NodeAddrInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_NetListen); ok {
		return x.NetListen
	}
	return nil
}

func (x *V01RuleActionItem) GetRegCreate() *RegCreate {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RegCreate); ok {
		return x.RegCreate
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptHttp() *ScriptHttp {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptHttp); ok {
		return x.ScriptHttp
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptImageLoad() *ScriptImageLoad {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptImageLoad); ok {
		return x.ScriptImageLoad
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptRunWmicCode() *ScriptRunWmicCode {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptRunWmicCode); ok {
		return x.ScriptRunWmicCode
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptScheduleCreate() *ScriptScheduleCreate {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptScheduleCreate); ok {
		return x.ScriptScheduleCreate
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptGetApiAddr() *ScriptGetApiAddr {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptGetApiAddr); ok {
		return x.ScriptGetApiAddr
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptWmicWin32Share() *ScriptWmicWin32Share {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptWmicWin32Share); ok {
		return x.ScriptWmicWin32Share
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptWmicTerminateProcess() *ScriptWmicTerminateProcess {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptWmicTerminateProcess); ok {
		return x.ScriptWmicTerminateProcess
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptWmicRegOper() *ScriptWmicRegOper {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptWmicRegOper); ok {
		return x.ScriptWmicRegOper
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptWmicServiceOper() *ScriptWmicServiceOper {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptWmicServiceOper); ok {
		return x.ScriptWmicServiceOper
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptWmicQuery() *ScriptWmicQuery {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptWmicQuery); ok {
		return x.ScriptWmicQuery
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptAmsiByAmsiContext() *ScriptAmsiByAmsiContext {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptAmsiByAmsiContext); ok {
		return x.ScriptAmsiByAmsiContext
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptAmsiDllHijack() *ScriptAmsiDllHijack {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptAmsiDllHijack); ok {
		return x.ScriptAmsiDllHijack
	}
	return nil
}

func (x *V01RuleActionItem) GetScriptEmail() *ScriptEmail {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ScriptEmail); ok {
		return x.ScriptEmail
	}
	return nil
}

func (x *V01RuleActionItem) GetLogClear() *LogClear {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_LogClear); ok {
		return x.LogClear
	}
	return nil
}

func (x *V01RuleActionItem) GetKillProcessDetail() *NodeProcessInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_KillProcessDetail); ok {
		return x.KillProcessDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetProcessInjectDetail() *NodeProcessInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ProcessInjectDetail); ok {
		return x.ProcessInjectDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetRegDelete() *RegDelete {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RegDelete); ok {
		return x.RegDelete
	}
	return nil
}

func (x *V01RuleActionItem) GetRegWrite() *RegWrite {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RegWrite); ok {
		return x.RegWrite
	}
	return nil
}

func (x *V01RuleActionItem) GetRegSetSecurity() *RegSetSecurity {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RegSetSecurity); ok {
		return x.RegSetSecurity
	}
	return nil
}

func (x *V01RuleActionItem) GetFileHide() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileHide); ok {
		return x.FileHide
	}
	return nil
}

func (x *V01RuleActionItem) GetFileReadonly() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileReadonly); ok {
		return x.FileReadonly
	}
	return nil
}

func (x *V01RuleActionItem) GetLoadRemoteModule() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_LoadRemoteModule); ok {
		return x.LoadRemoteModule
	}
	return nil
}

func (x *V01RuleActionItem) GetWhiteAddBlack() *WhiteAddBlack {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_WhiteAddBlack); ok {
		return x.WhiteAddBlack
	}
	return nil
}

func (x *V01RuleActionItem) GetCve() *CveAttack {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_Cve); ok {
		return x.Cve
	}
	return nil
}

func (x *V01RuleActionItem) GetRemoteBugOverflow() *RemoteBugOverflow {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RemoteBugOverflow); ok {
		return x.RemoteBugOverflow
	}
	return nil
}

func (x *V01RuleActionItem) GetPuppetProcess() *PuppetProcessAttack {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_PuppetProcess); ok {
		return x.PuppetProcess
	}
	return nil
}

func (x *V01RuleActionItem) GetNetConnectDomain() *DomainConnectionInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_NetConnectDomain); ok {
		return x.NetConnectDomain
	}
	return nil
}

func (x *V01RuleActionItem) GetNetSnifferContent() *NetSnifferInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_NetSnifferContent); ok {
		return x.NetSnifferContent
	}
	return nil
}

func (x *V01RuleActionItem) GetEnvHijackContent() *EnvHijackInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_EnvHijackContent); ok {
		return x.EnvHijackContent
	}
	return nil
}

func (x *V01RuleActionItem) GetSelfDeleteContent() *SelfDeleteInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_SelfDeleteContent); ok {
		return x.SelfDeleteContent
	}
	return nil
}

func (x *V01RuleActionItem) GetPtraceContent() *V01PtraceInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_PtraceContent); ok {
		return x.PtraceContent
	}
	return nil
}

func (x *V01RuleActionItem) GetFileLinkContent() *FileLinkInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileLinkContent); ok {
		return x.FileLinkContent
	}
	return nil
}

func (x *V01RuleActionItem) GetCallUsermodehelperContent() *CallUsermodehelperInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_CallUsermodehelperContent); ok {
		return x.CallUsermodehelperContent
	}
	return nil
}

func (x *V01RuleActionItem) GetReverseShellContent() *ReverseShellInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ReverseShellContent); ok {
		return x.ReverseShellContent
	}
	return nil
}

func (x *V01RuleActionItem) GetProcessExecContent() *NodeProcessInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ProcessExecContent); ok {
		return x.ProcessExecContent
	}
	return nil
}

func (x *V01RuleActionItem) GetRestoreUtimeContent() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_RestoreUtimeContent); ok {
		return x.RestoreUtimeContent
	}
	return nil
}

func (x *V01RuleActionItem) GetFileIoctlImmutableContent() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileIoctlImmutableContent); ok {
		return x.FileIoctlImmutableContent
	}
	return nil
}

func (x *V01RuleActionItem) GetMkdirContent() *Mkdir {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_MkdirContent); ok {
		return x.MkdirContent
	}
	return nil
}

func (x *V01RuleActionItem) GetFileSymlink() *FileSymlink {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileSymlink); ok {
		return x.FileSymlink
	}
	return nil
}

func (x *V01RuleActionItem) GetFileSetUid() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileSetUid); ok {
		return x.FileSetUid
	}
	return nil
}

func (x *V01RuleActionItem) GetBpf() *Bpf {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_Bpf); ok {
		return x.Bpf
	}
	return nil
}

func (x *V01RuleActionItem) GetPrivilegeEscalation() *NodeProcessInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_PrivilegeEscalation); ok {
		return x.PrivilegeEscalation
	}
	return nil
}

func (x *V01RuleActionItem) GetFakeExeFileContent() *FakeExeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FakeExeFileContent); ok {
		return x.FakeExeFileContent
	}
	return nil
}

func (x *V01RuleActionItem) GetHideModuleContent() *HideModuleInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_HideModuleContent); ok {
		return x.HideModuleContent
	}
	return nil
}

func (x *V01RuleActionItem) GetKillContent() *V01KillInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_KillContent); ok {
		return x.KillContent
	}
	return nil
}

func (x *V01RuleActionItem) GetFileModeChangeContent() *FileModeChangeContentInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileModeChangeContent); ok {
		return x.FileModeChangeContent
	}
	return nil
}

func (x *V01RuleActionItem) GetResoureLimit() *SetRlimit {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ResoureLimit); ok {
		return x.ResoureLimit
	}
	return nil
}

func (x *V01RuleActionItem) GetFilelessAttackContent() *FilelessAttackInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FilelessAttackContent); ok {
		return x.FilelessAttackContent
	}
	return nil
}

func (x *V01RuleActionItem) GetFileOpenContent() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_FileOpenContent); ok {
		return x.FileOpenContent
	}
	return nil
}

func (x *V01RuleActionItem) GetQueueApcDetail() *QueueApcThread {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_QueueApcDetail); ok {
		return x.QueueApcDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetSetContextThreadDetail() *SetContextThread {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_SetContextThreadDetail); ok {
		return x.SetContextThreadDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetProtectMemDetail() *ProtectVirtualMem {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ProtectMemDetail); ok {
		return x.ProtectMemDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetAllocateMemDetail() *AllocateVirtualMem {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_AllocateMemDetail); ok {
		return x.AllocateMemDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetWriteMemDetail() *WriteVirtualMem {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_WriteMemDetail); ok {
		return x.WriteMemDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetReadMemDetail() *ReadVirtualMem {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_ReadMemDetail); ok {
		return x.ReadMemDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetMapViewSectionDetail() *MapViewOfSection {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_MapViewSectionDetail); ok {
		return x.MapViewSectionDetail
	}
	return nil
}

func (x *V01RuleActionItem) GetDeleteByself() *NodeFileInfo {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_DeleteByself); ok {
		return x.DeleteByself
	}
	return nil
}

func (x *V01RuleActionItem) GetOpenDeviceObject() *OpenDeviceObject {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_OpenDeviceObject); ok {
		return x.OpenDeviceObject
	}
	return nil
}

func (x *V01RuleActionItem) GetCreateService() *CreateServiceAction {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_CreateService); ok {
		return x.CreateService
	}
	return nil
}

func (x *V01RuleActionItem) GetStartService() *StartServiceAction {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_StartService); ok {
		return x.StartService
	}
	return nil
}

func (x *V01RuleActionItem) GetCreateTaskScheduler() *CreateTaskScheduler {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_CreateTaskScheduler); ok {
		return x.CreateTaskScheduler
	}
	return nil
}

func (x *V01RuleActionItem) GetStartTaskScheduler() *StartTaskScheduler {
	if x, ok := x.GetActionContent().(*V01RuleActionItem_StartTaskScheduler); ok {
		return x.StartTaskScheduler
	}
	return nil
}

type isV01RuleActionItem_ActionContent interface {
	isV01RuleActionItem_ActionContent()
}

type V01RuleActionItem_ProcessRelationUpdate struct {
	ProcessRelationUpdate *AcdrProcessParrentRelationUpdate `protobuf:"bytes,10,opt,name=process_relation_update,json=processRelationUpdate,proto3,oneof"`
}

type V01RuleActionItem_ProcessCreate struct {
	ProcessCreate *NodeProcessInfo `protobuf:"bytes,11,opt,name=process_create,json=processCreate,proto3,oneof"`
}

type V01RuleActionItem_FileCreate struct {
	FileCreate *NodeFileInfo `protobuf:"bytes,12,opt,name=file_create,json=fileCreate,proto3,oneof"`
}

type V01RuleActionItem_FileWrite struct {
	FileWrite *NodeFileInfo `protobuf:"bytes,13,opt,name=file_write,json=fileWrite,proto3,oneof"`
}

type V01RuleActionItem_FileRead struct {
	FileRead *NodeFileInfo `protobuf:"bytes,14,opt,name=file_read,json=fileRead,proto3,oneof"`
}

type V01RuleActionItem_FileDelete struct {
	FileDelete *NodeFileInfo `protobuf:"bytes,15,opt,name=file_delete,json=fileDelete,proto3,oneof"`
}

type V01RuleActionItem_FileRename struct {
	FileRename *FileRename `protobuf:"bytes,16,opt,name=file_rename,json=fileRename,proto3,oneof"`
}

type V01RuleActionItem_NetConnect struct {
	NetConnect *ConnectionInfo `protobuf:"bytes,17,opt,name=net_connect,json=netConnect,proto3,oneof"`
}

type V01RuleActionItem_NetAccept struct {
	NetAccept *ConnectionInfo `protobuf:"bytes,18,opt,name=net_accept,json=netAccept,proto3,oneof"`
}

type V01RuleActionItem_NetListen struct {
	NetListen *NodeAddrInfo `protobuf:"bytes,19,opt,name=net_listen,json=netListen,proto3,oneof"`
}

type V01RuleActionItem_RegCreate struct {
	// 常规行为点，直接和进程关联即可
	RegCreate *RegCreate `protobuf:"bytes,20,opt,name=reg_create,json=regCreate,proto3,oneof"`
}

type V01RuleActionItem_ScriptHttp struct {
	ScriptHttp *ScriptHttp `protobuf:"bytes,22,opt,name=script_http,json=scriptHttp,proto3,oneof"`
}

type V01RuleActionItem_ScriptImageLoad struct {
	ScriptImageLoad *ScriptImageLoad `protobuf:"bytes,23,opt,name=script_image_load,json=scriptImageLoad,proto3,oneof"`
}

type V01RuleActionItem_ScriptRunWmicCode struct {
	ScriptRunWmicCode *ScriptRunWmicCode `protobuf:"bytes,26,opt,name=script_run_wmic_code,json=scriptRunWmicCode,proto3,oneof"`
}

type V01RuleActionItem_ScriptScheduleCreate struct {
	ScriptScheduleCreate *ScriptScheduleCreate `protobuf:"bytes,27,opt,name=script_schedule_create,json=scriptScheduleCreate,proto3,oneof"`
}

type V01RuleActionItem_ScriptGetApiAddr struct {
	ScriptGetApiAddr *ScriptGetApiAddr `protobuf:"bytes,24,opt,name=script_get_api_addr,json=scriptGetApiAddr,proto3,oneof"`
}

type V01RuleActionItem_ScriptWmicWin32Share struct {
	ScriptWmicWin32Share *ScriptWmicWin32Share `protobuf:"bytes,25,opt,name=script_wmic_win32_share,json=scriptWmicWin32Share,proto3,oneof"`
}

type V01RuleActionItem_ScriptWmicTerminateProcess struct {
	ScriptWmicTerminateProcess *ScriptWmicTerminateProcess `protobuf:"bytes,28,opt,name=script_wmic_terminate_process,json=scriptWmicTerminateProcess,proto3,oneof"`
}

type V01RuleActionItem_ScriptWmicRegOper struct {
	ScriptWmicRegOper *ScriptWmicRegOper `protobuf:"bytes,29,opt,name=script_wmic_reg_oper,json=scriptWmicRegOper,proto3,oneof"`
}

type V01RuleActionItem_ScriptWmicServiceOper struct {
	ScriptWmicServiceOper *ScriptWmicServiceOper `protobuf:"bytes,30,opt,name=script_wmic_service_oper,json=scriptWmicServiceOper,proto3,oneof"`
}

type V01RuleActionItem_ScriptWmicQuery struct {
	ScriptWmicQuery *ScriptWmicQuery `protobuf:"bytes,31,opt,name=script_wmic_query,json=scriptWmicQuery,proto3,oneof"`
}

type V01RuleActionItem_ScriptAmsiByAmsiContext struct {
	ScriptAmsiByAmsiContext *ScriptAmsiByAmsiContext `protobuf:"bytes,32,opt,name=script_amsi_by_amsi_context,json=scriptAmsiByAmsiContext,proto3,oneof"`
}

type V01RuleActionItem_ScriptAmsiDllHijack struct {
	ScriptAmsiDllHijack *ScriptAmsiDllHijack `protobuf:"bytes,33,opt,name=script_amsi_dll_hijack,json=scriptAmsiDllHijack,proto3,oneof"`
}

type V01RuleActionItem_ScriptEmail struct {
	ScriptEmail *ScriptEmail `protobuf:"bytes,34,opt,name=script_email,json=scriptEmail,proto3,oneof"`
}

type V01RuleActionItem_LogClear struct {
	LogClear *LogClear `protobuf:"bytes,35,opt,name=log_clear,json=logClear,proto3,oneof"`
}

type V01RuleActionItem_KillProcessDetail struct {
	// NodeProcessInfo queue_apc_detail = 36;          // moved to 81
	// NodeProcessInfo set_context_thread_detail = 37; // moved to 82
	KillProcessDetail *NodeProcessInfo `protobuf:"bytes,38,opt,name=kill_process_detail,json=killProcessDetail,proto3,oneof"`
}

type V01RuleActionItem_ProcessInjectDetail struct {
	ProcessInjectDetail *NodeProcessInfo `protobuf:"bytes,39,opt,name=process_inject_detail,json=processInjectDetail,proto3,oneof"`
}

type V01RuleActionItem_RegDelete struct {
	RegDelete *RegDelete `protobuf:"bytes,40,opt,name=reg_delete,json=regDelete,proto3,oneof"`
}

type V01RuleActionItem_RegWrite struct {
	RegWrite *RegWrite `protobuf:"bytes,41,opt,name=reg_write,json=regWrite,proto3,oneof"`
}

type V01RuleActionItem_RegSetSecurity struct {
	RegSetSecurity *RegSetSecurity `protobuf:"bytes,42,opt,name=reg_set_security,json=regSetSecurity,proto3,oneof"`
}

type V01RuleActionItem_FileHide struct {
	FileHide *NodeFileInfo `protobuf:"bytes,43,opt,name=file_hide,json=fileHide,proto3,oneof"`
}

type V01RuleActionItem_FileReadonly struct {
	FileReadonly *NodeFileInfo `protobuf:"bytes,44,opt,name=file_readonly,json=fileReadonly,proto3,oneof"`
}

type V01RuleActionItem_LoadRemoteModule struct {
	LoadRemoteModule *NodeFileInfo `protobuf:"bytes,47,opt,name=load_remote_module,json=loadRemoteModule,proto3,oneof"`
}

type V01RuleActionItem_WhiteAddBlack struct {
	WhiteAddBlack *WhiteAddBlack `protobuf:"bytes,48,opt,name=white_add_black,json=whiteAddBlack,proto3,oneof"` // 白加黑
}

type V01RuleActionItem_Cve struct {
	Cve *CveAttack `protobuf:"bytes,55,opt,name=cve,proto3,oneof"`
}

type V01RuleActionItem_RemoteBugOverflow struct {
	RemoteBugOverflow *RemoteBugOverflow `protobuf:"bytes,56,opt,name=remote_bug_overflow,json=remoteBugOverflow,proto3,oneof"`
}

type V01RuleActionItem_PuppetProcess struct {
	PuppetProcess *PuppetProcessAttack `protobuf:"bytes,57,opt,name=puppet_process,json=puppetProcess,proto3,oneof"`
}

type V01RuleActionItem_NetConnectDomain struct {
	NetConnectDomain *DomainConnectionInfo `protobuf:"bytes,58,opt,name=net_connect_domain,json=netConnectDomain,proto3,oneof"`
}

type V01RuleActionItem_NetSnifferContent struct {
	NetSnifferContent *NetSnifferInfo `protobuf:"bytes,59,opt,name=net_sniffer_content,json=netSnifferContent,proto3,oneof"`
}

type V01RuleActionItem_EnvHijackContent struct {
	EnvHijackContent *EnvHijackInfo `protobuf:"bytes,60,opt,name=env_hijack_content,json=envHijackContent,proto3,oneof"`
}

type V01RuleActionItem_SelfDeleteContent struct {
	SelfDeleteContent *SelfDeleteInfo `protobuf:"bytes,61,opt,name=self_delete_content,json=selfDeleteContent,proto3,oneof"`
}

type V01RuleActionItem_PtraceContent struct {
	PtraceContent *V01PtraceInfo `protobuf:"bytes,62,opt,name=ptrace_content,json=ptraceContent,proto3,oneof"`
}

type V01RuleActionItem_FileLinkContent struct {
	FileLinkContent *FileLinkInfo `protobuf:"bytes,63,opt,name=file_link_content,json=fileLinkContent,proto3,oneof"`
}

type V01RuleActionItem_CallUsermodehelperContent struct {
	CallUsermodehelperContent *CallUsermodehelperInfo `protobuf:"bytes,64,opt,name=call_usermodehelper_content,json=callUsermodehelperContent,proto3,oneof"`
}

type V01RuleActionItem_ReverseShellContent struct {
	ReverseShellContent *ReverseShellInfo `protobuf:"bytes,65,opt,name=reverse_shell_content,json=reverseShellContent,proto3,oneof"`
}

type V01RuleActionItem_ProcessExecContent struct {
	ProcessExecContent *NodeProcessInfo `protobuf:"bytes,66,opt,name=process_exec_content,json=processExecContent,proto3,oneof"`
}

type V01RuleActionItem_RestoreUtimeContent struct {
	RestoreUtimeContent *NodeFileInfo `protobuf:"bytes,67,opt,name=restore_utime_content,json=restoreUtimeContent,proto3,oneof"`
}

type V01RuleActionItem_FileIoctlImmutableContent struct {
	FileIoctlImmutableContent *NodeFileInfo `protobuf:"bytes,68,opt,name=file_ioctl_immutable_content,json=fileIoctlImmutableContent,proto3,oneof"`
}

type V01RuleActionItem_MkdirContent struct {
	MkdirContent *Mkdir `protobuf:"bytes,69,opt,name=mkdir_content,json=mkdirContent,proto3,oneof"`
}

type V01RuleActionItem_FileSymlink struct {
	FileSymlink *FileSymlink `protobuf:"bytes,70,opt,name=file_symlink,json=fileSymlink,proto3,oneof"`
}

type V01RuleActionItem_FileSetUid struct {
	FileSetUid *NodeFileInfo `protobuf:"bytes,71,opt,name=file_set_uid,json=fileSetUid,proto3,oneof"`
}

type V01RuleActionItem_Bpf struct {
	Bpf *Bpf `protobuf:"bytes,72,opt,name=bpf,proto3,oneof"`
}

type V01RuleActionItem_PrivilegeEscalation struct {
	PrivilegeEscalation *NodeProcessInfo `protobuf:"bytes,73,opt,name=privilege_escalation,json=privilegeEscalation,proto3,oneof"`
}

type V01RuleActionItem_FakeExeFileContent struct {
	FakeExeFileContent *FakeExeFileInfo `protobuf:"bytes,74,opt,name=fake_exe_file_content,json=fakeExeFileContent,proto3,oneof"`
}

type V01RuleActionItem_HideModuleContent struct {
	HideModuleContent *HideModuleInfo `protobuf:"bytes,75,opt,name=hide_module_content,json=hideModuleContent,proto3,oneof"`
}

type V01RuleActionItem_KillContent struct {
	KillContent *V01KillInfo `protobuf:"bytes,76,opt,name=kill_content,json=killContent,proto3,oneof"`
}

type V01RuleActionItem_FileModeChangeContent struct {
	FileModeChangeContent *FileModeChangeContentInfo `protobuf:"bytes,77,opt,name=file_mode_change_content,json=fileModeChangeContent,proto3,oneof"`
}

type V01RuleActionItem_ResoureLimit struct {
	ResoureLimit *SetRlimit `protobuf:"bytes,78,opt,name=resoure_limit,json=resoureLimit,proto3,oneof"`
}

type V01RuleActionItem_FilelessAttackContent struct {
	FilelessAttackContent *FilelessAttackInfo `protobuf:"bytes,79,opt,name=fileless_attack_content,json=filelessAttackContent,proto3,oneof"`
}

type V01RuleActionItem_FileOpenContent struct {
	FileOpenContent *NodeFileInfo `protobuf:"bytes,80,opt,name=file_open_content,json=fileOpenContent,proto3,oneof"`
}

type V01RuleActionItem_QueueApcDetail struct {
	QueueApcDetail *QueueApcThread `protobuf:"bytes,81,opt,name=queue_apc_detail,json=queueApcDetail,proto3,oneof"`
}

type V01RuleActionItem_SetContextThreadDetail struct {
	SetContextThreadDetail *SetContextThread `protobuf:"bytes,82,opt,name=set_context_thread_detail,json=setContextThreadDetail,proto3,oneof"`
}

type V01RuleActionItem_ProtectMemDetail struct {
	ProtectMemDetail *ProtectVirtualMem `protobuf:"bytes,83,opt,name=protect_mem_detail,json=protectMemDetail,proto3,oneof"`
}

type V01RuleActionItem_AllocateMemDetail struct {
	AllocateMemDetail *AllocateVirtualMem `protobuf:"bytes,84,opt,name=allocate_mem_detail,json=allocateMemDetail,proto3,oneof"`
}

type V01RuleActionItem_WriteMemDetail struct {
	WriteMemDetail *WriteVirtualMem `protobuf:"bytes,85,opt,name=write_mem_detail,json=writeMemDetail,proto3,oneof"`
}

type V01RuleActionItem_ReadMemDetail struct {
	ReadMemDetail *ReadVirtualMem `protobuf:"bytes,86,opt,name=read_mem_detail,json=readMemDetail,proto3,oneof"`
}

type V01RuleActionItem_MapViewSectionDetail struct {
	MapViewSectionDetail *MapViewOfSection `protobuf:"bytes,87,opt,name=map_view_section_detail,json=mapViewSectionDetail,proto3,oneof"`
}

type V01RuleActionItem_DeleteByself struct {
	DeleteByself *NodeFileInfo `protobuf:"bytes,88,opt,name=delete_byself,json=deleteByself,proto3,oneof"`
}

type V01RuleActionItem_OpenDeviceObject struct {
	OpenDeviceObject *OpenDeviceObject `protobuf:"bytes,89,opt,name=open_device_object,json=openDeviceObject,proto3,oneof"`
}

type V01RuleActionItem_CreateService struct {
	CreateService *CreateServiceAction `protobuf:"bytes,90,opt,name=create_service,json=createService,proto3,oneof"`
}

type V01RuleActionItem_StartService struct {
	StartService *StartServiceAction `protobuf:"bytes,91,opt,name=start_service,json=startService,proto3,oneof"`
}

type V01RuleActionItem_CreateTaskScheduler struct {
	CreateTaskScheduler *CreateTaskScheduler `protobuf:"bytes,92,opt,name=create_task_scheduler,json=createTaskScheduler,proto3,oneof"`
}

type V01RuleActionItem_StartTaskScheduler struct {
	StartTaskScheduler *StartTaskScheduler `protobuf:"bytes,93,opt,name=start_task_scheduler,json=startTaskScheduler,proto3,oneof"`
}

func (*V01RuleActionItem_ProcessRelationUpdate) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ProcessCreate) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileCreate) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileWrite) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileRead) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileDelete) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileRename) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_NetConnect) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_NetAccept) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_NetListen) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RegCreate) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptHttp) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptImageLoad) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptRunWmicCode) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptScheduleCreate) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptGetApiAddr) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptWmicWin32Share) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptWmicTerminateProcess) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptWmicRegOper) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptWmicServiceOper) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptWmicQuery) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptAmsiByAmsiContext) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptAmsiDllHijack) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ScriptEmail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_LogClear) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_KillProcessDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ProcessInjectDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RegDelete) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RegWrite) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RegSetSecurity) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileHide) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileReadonly) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_LoadRemoteModule) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_WhiteAddBlack) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_Cve) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RemoteBugOverflow) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_PuppetProcess) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_NetConnectDomain) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_NetSnifferContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_EnvHijackContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_SelfDeleteContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_PtraceContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileLinkContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_CallUsermodehelperContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ReverseShellContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ProcessExecContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_RestoreUtimeContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileIoctlImmutableContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_MkdirContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileSymlink) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileSetUid) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_Bpf) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_PrivilegeEscalation) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FakeExeFileContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_HideModuleContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_KillContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileModeChangeContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ResoureLimit) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FilelessAttackContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_FileOpenContent) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_QueueApcDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_SetContextThreadDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ProtectMemDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_AllocateMemDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_WriteMemDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_ReadMemDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_MapViewSectionDetail) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_DeleteByself) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_OpenDeviceObject) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_CreateService) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_StartService) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_CreateTaskScheduler) isV01RuleActionItem_ActionContent() {}

func (*V01RuleActionItem_StartTaskScheduler) isV01RuleActionItem_ActionContent() {}

// 行为集合
type V01RuleInfoMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleName          string               `protobuf:"bytes,1,opt,name=rule_name,json=ruleName,proto3" json:"rule_name,omitempty"`                              // 规则名 eg:del_self、create_process、autorun_create
	RuleLevel         uint32               `protobuf:"varint,2,opt,name=rule_level,json=ruleLevel,proto3" json:"rule_level,omitempty"`                          // 风险等级
	NeedShowOnly0229  bool                 `protobuf:"varint,3,opt,name=need_show_only0229,json=needShowOnly0229,proto3" json:"need_show_only0229,omitempty"`   // 是否需要展示攻击，0229后由server自己决定
	RootProcessUnique *AcdrProcessUnique   `protobuf:"bytes,4,opt,name=root_process_unique,json=rootProcessUnique,proto3" json:"root_process_unique,omitempty"` // 根进程唯一信息
	ActionList        []*V01RuleActionItem `protobuf:"bytes,5,rep,name=action_list,json=actionList,proto3" json:"action_list,omitempty"`
	Attack            string               `protobuf:"bytes,6,opt,name=attack,proto3" json:"attack,omitempty"`                                    // ATT&CK 逗号分隔, "TA0002,T1059.001,TA0002,T1059.002"
	ClientVersion     string               `protobuf:"bytes,7,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"` // 终端版本信息
}

func (x *V01RuleInfoMessage) Reset() {
	*x = V01RuleInfoMessage{}
	mi := &file_agent_acdr_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *V01RuleInfoMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*V01RuleInfoMessage) ProtoMessage() {}

func (x *V01RuleInfoMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use V01RuleInfoMessage.ProtoReflect.Descriptor instead.
func (*V01RuleInfoMessage) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{90}
}

func (x *V01RuleInfoMessage) GetRuleName() string {
	if x != nil {
		return x.RuleName
	}
	return ""
}

func (x *V01RuleInfoMessage) GetRuleLevel() uint32 {
	if x != nil {
		return x.RuleLevel
	}
	return 0
}

func (x *V01RuleInfoMessage) GetNeedShowOnly0229() bool {
	if x != nil {
		return x.NeedShowOnly0229
	}
	return false
}

func (x *V01RuleInfoMessage) GetRootProcessUnique() *AcdrProcessUnique {
	if x != nil {
		return x.RootProcessUnique
	}
	return nil
}

func (x *V01RuleInfoMessage) GetActionList() []*V01RuleActionItem {
	if x != nil {
		return x.ActionList
	}
	return nil
}

func (x *V01RuleInfoMessage) GetAttack() string {
	if x != nil {
		return x.Attack
	}
	return ""
}

func (x *V01RuleInfoMessage) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

// 规则集合 (最大100条？)
type RuleInfoMessages struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleList []*V01RuleInfoMessage `protobuf:"bytes,1,rep,name=rule_list,json=ruleList,proto3" json:"rule_list,omitempty"`
}

func (x *RuleInfoMessages) Reset() {
	*x = RuleInfoMessages{}
	mi := &file_agent_acdr_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RuleInfoMessages) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuleInfoMessages) ProtoMessage() {}

func (x *RuleInfoMessages) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuleInfoMessages.ProtoReflect.Descriptor instead.
func (*RuleInfoMessages) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{91}
}

func (x *RuleInfoMessages) GetRuleList() []*V01RuleInfoMessage {
	if x != nil {
		return x.RuleList
	}
	return nil
}

// 上报外联信息
type OutreachInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Beans []*OutreachBean `protobuf:"bytes,1,rep,name=Beans,proto3" json:"Beans,omitempty"` // 外联信息列表
}

func (x *OutreachInfos) Reset() {
	*x = OutreachInfos{}
	mi := &file_agent_acdr_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachInfos) ProtoMessage() {}

func (x *OutreachInfos) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachInfos.ProtoReflect.Descriptor instead.
func (*OutreachInfos) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{92}
}

func (x *OutreachInfos) GetBeans() []*OutreachBean {
	if x != nil {
		return x.Beans
	}
	return nil
}

// 外联五元组、进程信息
type OutreachBean struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EventType         uint32              `protobuf:"varint,1,opt,name=EventType,proto3" json:"EventType,omitempty"`                // 外联类型枚举 1:ip 2:域名
	UniqueFlag        []byte              `protobuf:"bytes,2,opt,name=UniqueFlag,proto3" json:"UniqueFlag,omitempty"`               // 外联唯一标识
	NetInfo           *NetContentBaseInfo `protobuf:"bytes,3,opt,name=NetInfo,proto3" json:"NetInfo,omitempty"`                     // 五元组
	DiscoverTime      int64               `protobuf:"varint,4,opt,name=DiscoverTime,proto3" json:"DiscoverTime,omitempty"`          // 外联信息发现时间，单位：linux毫秒
	RootProcessUnique *AcdrProcessUnique  `protobuf:"bytes,5,opt,name=RootProcessUnique,proto3" json:"RootProcessUnique,omitempty"` // 根进程
	CurProcessUnique  *AcdrProcessSimple  `protobuf:"bytes,6,opt,name=CurProcessUnique,proto3" json:"CurProcessUnique,omitempty"`   // 当前进程
	ClientVersion     string              `protobuf:"bytes,7,opt,name=ClientVersion,proto3" json:"ClientVersion,omitempty"`         // 终端版本信息
}

func (x *OutreachBean) Reset() {
	*x = OutreachBean{}
	mi := &file_agent_acdr_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachBean) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachBean) ProtoMessage() {}

func (x *OutreachBean) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachBean.ProtoReflect.Descriptor instead.
func (*OutreachBean) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{93}
}

func (x *OutreachBean) GetEventType() uint32 {
	if x != nil {
		return x.EventType
	}
	return 0
}

func (x *OutreachBean) GetUniqueFlag() []byte {
	if x != nil {
		return x.UniqueFlag
	}
	return nil
}

func (x *OutreachBean) GetNetInfo() *NetContentBaseInfo {
	if x != nil {
		return x.NetInfo
	}
	return nil
}

func (x *OutreachBean) GetDiscoverTime() int64 {
	if x != nil {
		return x.DiscoverTime
	}
	return 0
}

func (x *OutreachBean) GetRootProcessUnique() *AcdrProcessUnique {
	if x != nil {
		return x.RootProcessUnique
	}
	return nil
}

func (x *OutreachBean) GetCurProcessUnique() *AcdrProcessSimple {
	if x != nil {
		return x.CurProcessUnique
	}
	return nil
}

func (x *OutreachBean) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

type InternalOutreachInfos struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InterOutreach []*InternalOutreachInfoWithMac `protobuf:"bytes,1,rep,name=InterOutreach,proto3" json:"InterOutreach,omitempty"`
}

func (x *InternalOutreachInfos) Reset() {
	*x = InternalOutreachInfos{}
	mi := &file_agent_acdr_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalOutreachInfos) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalOutreachInfos) ProtoMessage() {}

func (x *InternalOutreachInfos) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalOutreachInfos.ProtoReflect.Descriptor instead.
func (*InternalOutreachInfos) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{94}
}

func (x *InternalOutreachInfos) GetInterOutreach() []*InternalOutreachInfoWithMac {
	if x != nil {
		return x.InterOutreach
	}
	return nil
}

type InternalOutreachInfoWithMac struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineID []byte `protobuf:"bytes,1,opt,name=MachineID,proto3" json:"MachineID,omitempty"` // 主机ID
	Beans     []byte `protobuf:"bytes,2,opt,name=Beans,proto3" json:"Beans,omitempty"`         // 外联信息
}

func (x *InternalOutreachInfoWithMac) Reset() {
	*x = InternalOutreachInfoWithMac{}
	mi := &file_agent_acdr_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalOutreachInfoWithMac) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalOutreachInfoWithMac) ProtoMessage() {}

func (x *InternalOutreachInfoWithMac) ProtoReflect() protoreflect.Message {
	mi := &file_agent_acdr_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalOutreachInfoWithMac.ProtoReflect.Descriptor instead.
func (*InternalOutreachInfoWithMac) Descriptor() ([]byte, []int) {
	return file_agent_acdr_proto_rawDescGZIP(), []int{95}
}

func (x *InternalOutreachInfoWithMac) GetMachineID() []byte {
	if x != nil {
		return x.MachineID
	}
	return nil
}

func (x *InternalOutreachInfoWithMac) GetBeans() []byte {
	if x != nil {
		return x.Beans
	}
	return nil
}

var File_agent_acdr_proto protoreflect.FileDescriptor

var file_agent_acdr_proto_rawDesc = []byte{
	0x0a, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x64, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa8, 0x03,
	0x0a, 0x0f, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x2c, 0x0a, 0x09, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x72,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x32, 0x0a, 0x0b, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0a,
	0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x3f, 0x0a, 0x10, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0e, 0x72, 0x75, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x42, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x67, 0x61, 0x76, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x15, 0x6e, 0x6f, 0x74, 0x5f, 0x73, 0x68, 0x6f,
	0x77, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x6e, 0x6f, 0x74, 0x53, 0x68, 0x6f, 0x77, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xef, 0x03, 0x0a, 0x0f, 0x4e, 0x67, 0x61,
	0x76, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x70,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x65, 0x78, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69,
	0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64,
	0x35, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x14, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x4d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x41, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x6b, 0x69, 0x6c, 0x6c,
	0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x4b, 0x69, 0x6c, 0x6c,
	0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x70, 0x69, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x70, 0x70, 0x69,
	0x64, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x4f, 0x0a, 0x15, 0x46, 0x69,
	0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x4d, 0x0a, 0x13, 0x46,
	0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x4e, 0x0a, 0x14, 0x46, 0x69,
	0x6c, 0x65, 0x57, 0x72, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x15, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x53, 0x72, 0x63, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x72, 0x63, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x73, 0x74, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x22, 0x4f, 0x0a, 0x15, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x6e, 0x0a, 0x19, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x30, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x57, 0x0a, 0x1d, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6f, 0x63,
	0x74, 0x6c, 0x49, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x3f,
	0x0a, 0x05, 0x4d, 0x6b, 0x64, 0x69, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22,
	0x81, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x37, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73, 0x72, 0x63,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x72, 0x63, 0x12, 0x39, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x44,
	0x65, 0x73, 0x74, 0x22, 0x29, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x55, 0x69,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0xf4,
	0x01, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69,
	0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x70,
	0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x39, 0x0a,
	0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65,
	0x74, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x2b, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x97, 0x01, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a,
	0x14, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22,
	0x96, 0x01, 0x0a, 0x14, 0x4e, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x14, 0x4e, 0x65, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69,
	0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x22, 0xb7, 0x01, 0x0a, 0x12, 0x4e, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x64, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0xb7, 0x01, 0x0a, 0x12,
	0x4e, 0x65, 0x74, 0x52, 0x65, 0x63, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x12, 0x69, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x43, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x4c, 0x0a, 0x12, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x65, 0x73,
	0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x22, 0x2a, 0x0a, 0x0e, 0x4e, 0x65, 0x74, 0x53, 0x6e, 0x69, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x76, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x64, 0x65, 0x76, 0x6e, 0x61, 0x6d, 0x65, 0x22,
	0x2a, 0x0a, 0x0d, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x76, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x07, 0x65, 0x6e, 0x76, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x48, 0x0a, 0x0e, 0x53,
	0x65, 0x6c, 0x66, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xeb, 0x01, 0x0a, 0x0a, 0x50, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x5f, 0x70,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72,
	0x50, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f,
	0x74, 0x72, 0x61, 0x63, 0x65, 0x72, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x50, 0x69, 0x64, 0x12, 0x2a,
	0x0a, 0x11, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x74, 0x72, 0x61, 0x63, 0x65, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x61,
	0x64, 0x64, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x53, 0x72, 0x63, 0x12, 0x24, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x44, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x73, 0x72, 0x63, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x72, 0x63, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x69, 0x73,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x44, 0x65, 0x73, 0x74, 0x45,
	0x78, 0x69, 0x73, 0x74, 0x22, 0x32, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72,
	0x6d, 0x6f, 0x64, 0x65, 0x68, 0x65, 0x6c, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xee, 0x03, 0x0a, 0x10, 0x52, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x65, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x15, 0x0a,
	0x06, 0x69, 0x6e, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x69,
	0x6e, 0x50, 0x69, 0x64, 0x12, 0x13, 0x0a, 0x05, 0x69, 0x6e, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x69, 0x6e, 0x49, 0x70, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x6e, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x69, 0x6e, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x69, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x5f, 0x66, 0x61,
	0x6d, 0x69, 0x6c, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x52, 0x0c,
	0x69, 0x6e, 0x41, 0x64, 0x64, 0x72, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x08,
	0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52,
	0x07, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x22, 0x0a, 0x0d, 0x69, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07,
	0x6f, 0x75, 0x74, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x6f,
	0x75, 0x74, 0x50, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x5f, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x6f, 0x75, 0x74, 0x49, 0x70, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x75, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x6f, 0x75, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x3a, 0x0a, 0x0f, 0x6f, 0x75, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x5f, 0x66, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x46, 0x61, 0x6d, 0x69,
	0x6c, 0x69, 0x65, 0x73, 0x52, 0x0d, 0x6f, 0x75, 0x74, 0x41, 0x64, 0x64, 0x72, 0x46, 0x61, 0x6d,
	0x69, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x09, 0x6f, 0x75, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x08, 0x6f, 0x75, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x69, 0x73, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x22, 0x2b, 0x0a, 0x0f, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x65, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x65,
	0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x22, 0x69, 0x0a, 0x13, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c,
	0x65, 0x67, 0x65, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a,
	0x0e, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67,
	0x65, 0x5f, 0x63, 0x6d, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x10, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x43, 0x6d, 0x64, 0x6c, 0x69, 0x6e,
	0x65, 0x22, 0x46, 0x0a, 0x0c, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19,
	0x0a, 0x08, 0x69, 0x73, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x69, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x22, 0x0a, 0x03, 0x42, 0x70, 0x66,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x74, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xaa, 0x01,
	0x0a, 0x0f, 0x46, 0x61, 0x6b, 0x65, 0x45, 0x78, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x61, 0x6b, 0x65, 0x5f, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x61, 0x6b, 0x65, 0x50, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66,
	0x61, 0x6b, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x66, 0x61, 0x6b, 0x65, 0x45, 0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x18,
	0x0a, 0x07, 0x65, 0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x07, 0x65, 0x78, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x65,
	0x78, 0x65, 0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x45, 0x78, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0x2a, 0x0a, 0x0e, 0x48, 0x69,
	0x64, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x6d, 0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x6d,
	0x6f, 0x64, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9c, 0x01, 0x0a, 0x08, 0x4b, 0x69, 0x6c, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73,
	0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xd3, 0x12, 0x0a, 0x0e, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x32, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x73, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x72, 0x65, 0x66, 0x75, 0x73, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x5f, 0x6d, 0x73, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x48, 0x00, 0x52, 0x07, 0x72, 0x75, 0x6c, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x4e, 0x0a,
	0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x48, 0x0a,
	0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x6e,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x12, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x57, 0x72, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x13, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x12, 0x6e, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52,
	0x10, 0x6e, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x4b, 0x0a, 0x12, 0x6e, 0x65, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10, 0x6e, 0x65,
	0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x45,
	0x0a, 0x10, 0x6e, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4e, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x10, 0x6e, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x63,
	0x76, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x52, 0x65, 0x63, 0x76, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0e, 0x6e, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x76, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x17,
	0x66, 0x69, 0x6c, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x65,
	0x6c, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x47, 0x0a, 0x13, 0x6e, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x69, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x53, 0x6e, 0x69, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x53, 0x6e, 0x69, 0x66,
	0x66, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x12, 0x65, 0x6e,
	0x76, 0x5f, 0x68, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x45,
	0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10,
	0x65, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x47, 0x0a, 0x13, 0x73, 0x65, 0x6c, 0x66, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c, 0x66, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x73, 0x65, 0x6c, 0x66, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x0e, 0x70, 0x74, 0x72,
	0x61, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x74, 0x72, 0x61, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x74, 0x72, 0x61, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e,
	0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x5f, 0x0a, 0x1b, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x6d, 0x6f, 0x64, 0x65, 0x68, 0x65, 0x6c, 0x70, 0x65, 0x72, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x6d, 0x6f,
	0x64, 0x65, 0x68, 0x65, 0x6c, 0x70, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x19,
	0x63, 0x61, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x6d, 0x6f, 0x64, 0x65, 0x68, 0x65, 0x6c, 0x70,
	0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x15, 0x72, 0x65, 0x76,
	0x65, 0x72, 0x73, 0x65, 0x5f, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x00, 0x52, 0x13, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x53, 0x68, 0x65, 0x6c,
	0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4a, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x65, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x65, 0x63, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x15, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x75, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x55, 0x74, 0x69, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x13, 0x72, 0x65, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x55, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x67, 0x0a, 0x1c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6f, 0x63, 0x74, 0x6c, 0x5f, 0x69, 0x6d,
	0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18,
	0x1e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6f, 0x63, 0x74, 0x6c, 0x49, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x19, 0x66,
	0x69, 0x6c, 0x65, 0x49, 0x6f, 0x63, 0x74, 0x6c, 0x49, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a, 0x0d, 0x6d, 0x6b, 0x64, 0x69,
	0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6b, 0x64, 0x69, 0x72, 0x48, 0x00, 0x52,
	0x0c, 0x6d, 0x6b, 0x64, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a,
	0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x20, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x35, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x55, 0x69, 0x64, 0x48,
	0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x55, 0x69, 0x64, 0x12, 0x1e, 0x0a,
	0x03, 0x62, 0x70, 0x66, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x42, 0x70, 0x66, 0x48, 0x00, 0x52, 0x03, 0x62, 0x70, 0x66, 0x12, 0x4f, 0x0a,
	0x14, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x5f, 0x65, 0x73, 0x63, 0x61, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x45, 0x73, 0x63,
	0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x13, 0x70, 0x72, 0x69, 0x76, 0x69,
	0x6c, 0x65, 0x67, 0x65, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b,
	0x0a, 0x15, 0x66, 0x61, 0x6b, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x45, 0x78, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x66, 0x61, 0x6b, 0x65, 0x45, 0x78, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x13, 0x68,
	0x69, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x48, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x11, 0x68, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x0c, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x6b,
	0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x0f, 0x0a, 0x0d, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x44, 0x0a, 0x11, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70,
	0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x2d, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x22, 0x5d, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x4c, 0x0a, 0x15, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x12, 0x6e, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0xbf, 0x01, 0x0a, 0x0a, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31, 0x0a, 0x08, 0x6e, 0x65, 0x74, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x0f, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x22, 0x60, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3b, 0x0a, 0x10, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x58, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0x9a, 0x01,
	0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x39, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x61,
	0x6d, 0x69, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x69, 0x65, 0x73, 0x52, 0x0d,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x46, 0x61, 0x6d, 0x69, 0x6c, 0x79, 0x12, 0x2b, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x68, 0x0a, 0x0e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x6c,
	0x6f, 0x63, 0x61, 0x6c, 0x22, 0x6f, 0x0a, 0x14, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x06,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x55,
	0x72, 0x6c, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x05, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x22, 0x8f, 0x03, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x74, 0x69, 0x6d, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x74, 0x69, 0x6d, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x16, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x65,
	0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x14,
	0x66, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12,
	0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0d, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0a, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x21, 0x0a, 0x0d, 0x4e, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x44, 0x0a, 0x11, 0x41, 0x63,
	0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x22, 0xd0, 0x01, 0x0a, 0x11, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x3f,
	0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x12, 0x0a, 0x04, 0x65, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x65,
	0x75, 0x69, 0x64, 0x22, 0xec, 0x01, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x61, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x41,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x74, 0x69,
	0x6d, 0x65, 0x22, 0xb7, 0x02, 0x0a, 0x0f, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0c, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x43, 0x68,
	0x69, 0x6c, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73,
	0x52, 0x6f, 0x6f, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x3f, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x75, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64, 0x12, 0x33, 0x0a, 0x08, 0x65, 0x78, 0x65, 0x63, 0x5f,
	0x73, 0x72, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x52, 0x07, 0x65, 0x78, 0x65, 0x63, 0x53, 0x72, 0x63, 0x22, 0xcb, 0x01, 0x0a,
	0x20, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x68, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x68, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x41, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x5f, 0x70, 0x61, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x50, 0x61, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x0d, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x43, 0x68, 0x69, 0x6c, 0x65, 0x64, 0x22, 0x5a, 0x0a, 0x0a, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x12, 0x23, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x02, 0x74, 0x6f, 0x22, 0x26, 0x0a, 0x09, 0x52, 0x65, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x22, 0x26,
	0x0a, 0x09, 0x52, 0x65, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x65, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x22, 0x46, 0x0a, 0x08, 0x52, 0x65, 0x67, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x77, 0x72, 0x69, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x2b,
	0x0a, 0x0e, 0x52, 0x65, 0x67, 0x53, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x67, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x67, 0x50, 0x61, 0x74, 0x68, 0x22, 0x35, 0x0a, 0x0a, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x48, 0x74, 0x74, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x69, 0x73, 0x47,
	0x65, 0x74, 0x22, 0x30, 0x0a, 0x0f, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x22, 0x42, 0x0a, 0x11, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x75,
	0x6e, 0x57, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x73, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x22, 0x3e, 0x0a, 0x14, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x22, 0x2d, 0x0a, 0x10, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x47, 0x65, 0x74, 0x41, 0x70, 0x69, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x54, 0x0a, 0x14, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x57, 0x6d, 0x69, 0x63, 0x57, 0x69, 0x6e, 0x33, 0x32, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12,
	0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x48, 0x0a,
	0x1a, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2a, 0x0a, 0x04, 0x70,
	0x72, 0x6f, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x70, 0x72, 0x6f, 0x63, 0x22, 0x6a, 0x0a, 0x11, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x57, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x6f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6f, 0x70, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x65, 0x79,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6b, 0x65, 0x79,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x22, 0x81, 0x01, 0x0a, 0x15, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d,
	0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x12, 0x1b, 0x0a,
	0x09, 0x6f, 0x70, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x45, 0x78, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x55, 0x0a, 0x0f, 0x53, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x57, 0x6d, 0x69, 0x63, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x5f, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x63, 0x6d, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x72, 0x79, 0x43, 0x6d, 0x64, 0x22, 0x2f,
	0x0a, 0x17, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x41, 0x6d, 0x73, 0x69, 0x42, 0x79, 0x41, 0x6d,
	0x73, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22,
	0x3e, 0x0a, 0x13, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x41, 0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c,
	0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22,
	0x4b, 0x0a, 0x0b, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1f,
	0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x76, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x63, 0x76, 0x41, 0x64, 0x64, 0x72, 0x22, 0x25, 0x0a, 0x08,
	0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x46,
	0x69, 0x6c, 0x65, 0x22, 0x4b, 0x0a, 0x09, 0x43, 0x76, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x76, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x76, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x57, 0x0a, 0x11, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x42, 0x75, 0x67, 0x4f, 0x76, 0x65,
	0x72, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x76, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x76, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x27, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x22, 0x32, 0x0a, 0x13, 0x50, 0x75, 0x70,
	0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x22, 0x6c, 0x0a,
	0x0d, 0x56, 0x30, 0x31, 0x50, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33,
	0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x64, 0x64, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x61, 0x64, 0x64, 0x72, 0x22, 0x5a, 0x0a, 0x0b, 0x56,
	0x30, 0x31, 0x4b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x09, 0x70, 0x72,
	0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x06, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x22, 0x40, 0x0a, 0x09, 0x53, 0x65, 0x74, 0x52, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x12, 0x4d, 0x65,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x07, 0x72, 0x65, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x72,
	0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x63, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xbb, 0x01, 0x0a, 0x0e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x70, 0x63, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x70, 0x63, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x61, 0x70, 0x63, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x65,
	0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22,
	0x9a, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x64, 0x12, 0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x73, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x87, 0x02, 0x0a,
	0x11, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d,
	0x65, 0x6d, 0x12, 0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x33, 0x0a,
	0x0b, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f, 0x6c, 0x64, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x12, 0x33, 0x0a, 0x0b, 0x6e, 0x65, 0x77, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6e, 0x65, 0x77,
	0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65,
	0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x82, 0x02, 0x0a, 0x12, 0x41, 0x6c, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x65, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x12, 0x3c, 0x0a,
	0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6e,
	0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62,
	0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08,
	0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x04,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x74, 0x68, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x94, 0x02, 0x0a, 0x0f,
	0x57, 0x72, 0x69, 0x74, 0x65, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x12,
	0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a,
	0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x77, 0x72,
	0x69, 0x74, 0x65, 0x5f, 0x62, 0x75, 0x66, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x0c, 0x77, 0x72, 0x69, 0x74, 0x65, 0x42, 0x75, 0x66, 0x41, 0x64, 0x64, 0x72,
	0x12, 0x1b, 0x0a, 0x09, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x65, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x08, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x65, 0x6e, 0x12, 0x34, 0x0a,
	0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4d, 0x65,
	0x6d, 0x52, 0x69, 0x73, 0x6b, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e,
	0x66, 0x6f, 0x22, 0xd9, 0x01, 0x0a, 0x0e, 0x52, 0x65, 0x61, 0x64, 0x56, 0x69, 0x72, 0x74, 0x75,
	0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x12, 0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72,
	0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72,
	0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x62, 0x75, 0x66, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x72, 0x65, 0x61, 0x64, 0x42, 0x75, 0x66,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6c, 0x65, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x72, 0x65, 0x61, 0x64, 0x4c, 0x65, 0x6e, 0x12,
	0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x80,
	0x02, 0x0a, 0x10, 0x4d, 0x61, 0x70, 0x56, 0x69, 0x65, 0x77, 0x4f, 0x66, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x41, 0x64, 0x64, 0x72, 0x12, 0x2c,
	0x0a, 0x07, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x12, 0x34, 0x0a, 0x0a,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x65, 0x6d, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x22, 0xee, 0x01, 0x0a, 0x0d, 0x57, 0x68, 0x69, 0x74, 0x65, 0x41, 0x64, 0x64, 0x42, 0x6c,
	0x61, 0x63, 0x6b, 0x12, 0x3c, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x41, 0x64, 0x64, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x2e, 0x52, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x64, 0x12, 0x37, 0x0a, 0x0d, 0x64, 0x6c, 0x6c, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x64,
	0x6c, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x66, 0x0a, 0x0a, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x5f, 0x43, 0x45, 0x52, 0x54,
	0x5f, 0x4e, 0x55, 0x4d, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x4c, 0x4c, 0x5f, 0x48, 0x49,
	0x44, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x41, 0x4d, 0x45, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x4c, 0x44, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10,
	0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x58, 0x45, 0x5f, 0x44, 0x49, 0x52, 0x5f, 0x4e, 0x55, 0x4d,
	0x10, 0x05, 0x22, 0x33, 0x0a, 0x10, 0x4f, 0x70, 0x65, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5b, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x50, 0x61, 0x74, 0x68, 0x22, 0x5a, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x22, 0xa2, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x72, 0x67, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x72, 0x67, 0x75,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xa6, 0x01, 0x0a, 0x12, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x13,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x61, 0x73, 0x6b, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x6f, 0x72, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x68, 0x6f,
	0x72, 0x12, 0x3b, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x22, 0xb2,
	0x29, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x52, 0x75, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x35, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x56, 0x30, 0x31, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74,
	0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3f,
	0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12,
	0x3f, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x61, 0x0a, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x72, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x15, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0x36, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x0a,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f,
	0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x61, 0x64, 0x12, 0x36, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x34,
	0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x6e, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x0a, 0x6e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x12, 0x36,
	0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x09, 0x6e, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x12, 0x34, 0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x64, 0x64, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x09, 0x6e, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x12, 0x31, 0x0a, 0x0a,
	0x72, 0x65, 0x67, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x10, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72, 0x65, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0x34, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x68, 0x74, 0x74, 0x70, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x48, 0x74, 0x74, 0x70, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x48, 0x74, 0x74, 0x70, 0x12, 0x44, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x12, 0x4b, 0x0a, 0x14, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x72, 0x75, 0x6e, 0x5f, 0x77, 0x6d, 0x69, 0x63, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x75, 0x6e, 0x57, 0x6d, 0x69, 0x63, 0x43,
	0x6f, 0x64, 0x65, 0x48, 0x00, 0x52, 0x11, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x75, 0x6e,
	0x57, 0x6d, 0x69, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x53, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x14, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a,
	0x13, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x70, 0x69, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x47, 0x65, 0x74, 0x41, 0x70, 0x69, 0x41,
	0x64, 0x64, 0x72, 0x48, 0x00, 0x52, 0x10, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x47, 0x65, 0x74,
	0x41, 0x70, 0x69, 0x41, 0x64, 0x64, 0x72, 0x12, 0x54, 0x0a, 0x17, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x5f, 0x77, 0x6d, 0x69, 0x63, 0x5f, 0x77, 0x69, 0x6e, 0x33, 0x32, 0x5f, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x57, 0x69, 0x6e, 0x33, 0x32,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x48, 0x00, 0x52, 0x14, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57,
	0x6d, 0x69, 0x63, 0x57, 0x69, 0x6e, 0x33, 0x32, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x66, 0x0a,
	0x1d, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x77, 0x6d, 0x69, 0x63, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x1c,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x57, 0x6d, 0x69, 0x63, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x4b, 0x0a, 0x14, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f,
	0x77, 0x6d, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x67, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x1d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x11, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x52, 0x65, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x12, 0x57, 0x0a, 0x18, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x77, 0x6d, 0x69,
	0x63, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x18, 0x1e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x15, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x11, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x77, 0x6d, 0x69, 0x63, 0x5f, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x18, 0x1f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x51, 0x75, 0x65, 0x72, 0x79, 0x48, 0x00,
	0x52, 0x0f, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x57, 0x6d, 0x69, 0x63, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x5e, 0x0a, 0x1b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x61, 0x6d, 0x73, 0x69,
	0x5f, 0x62, 0x79, 0x5f, 0x61, 0x6d, 0x73, 0x69, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x41, 0x6d, 0x73, 0x69, 0x42, 0x79, 0x41, 0x6d, 0x73, 0x69, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x48, 0x00, 0x52, 0x17, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x41, 0x6d, 0x73, 0x69, 0x42, 0x79, 0x41, 0x6d, 0x73, 0x69, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x12, 0x51, 0x0a, 0x16, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x61, 0x6d, 0x73, 0x69,
	0x5f, 0x64, 0x6c, 0x6c, 0x5f, 0x68, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x41, 0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x48, 0x00, 0x52,
	0x13, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x41, 0x6d, 0x73, 0x69, 0x44, 0x6c, 0x6c, 0x48, 0x69,
	0x6a, 0x61, 0x63, 0x6b, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x22, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x48, 0x00,
	0x52, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a,
	0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x18, 0x23, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61,
	0x72, 0x48, 0x00, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x12, 0x48, 0x0a,
	0x13, 0x6b, 0x69, 0x6c, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x18, 0x26, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x6b, 0x69, 0x6c, 0x6c, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4c, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x69, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x27, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x13, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x31, 0x0a, 0x0a, 0x72, 0x65, 0x67, 0x5f, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x65, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x72,
	0x65, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x72, 0x65, 0x67, 0x5f,
	0x77, 0x72, 0x69, 0x74, 0x65, 0x18, 0x29, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x57, 0x72, 0x69, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08,
	0x72, 0x65, 0x67, 0x57, 0x72, 0x69, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x10, 0x72, 0x65, 0x67, 0x5f,
	0x73, 0x65, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x53, 0x65,
	0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0e, 0x72, 0x65, 0x67,
	0x53, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x32, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x68, 0x69, 0x64, 0x65, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x48, 0x69, 0x64, 0x65, 0x12,
	0x3a, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79,
	0x18, 0x2c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0c, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x12, 0x6c,
	0x6f, 0x61, 0x64, 0x5f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x10,
	0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x12, 0x3e, 0x0a, 0x0f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x5f, 0x62, 0x6c,
	0x61, 0x63, 0x6b, 0x18, 0x30, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x41, 0x64, 0x64, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x48,
	0x00, 0x52, 0x0d, 0x77, 0x68, 0x69, 0x74, 0x65, 0x41, 0x64, 0x64, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x12, 0x24, 0x0a, 0x03, 0x63, 0x76, 0x65, 0x18, 0x37, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x76, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x48,
	0x00, 0x52, 0x03, 0x63, 0x76, 0x65, 0x12, 0x4a, 0x0a, 0x13, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x5f, 0x62, 0x75, 0x67, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x38, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x6d, 0x6f,
	0x74, 0x65, 0x42, 0x75, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x48, 0x00, 0x52,
	0x11, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x42, 0x75, 0x67, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c,
	0x6f, 0x77, 0x12, 0x43, 0x0a, 0x0e, 0x70, 0x75, 0x70, 0x70, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x18, 0x39, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x75, 0x70, 0x70, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x4b, 0x0a, 0x12, 0x6e, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x3a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x10, 0x6e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x44, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x12, 0x47, 0x0a, 0x13, 0x6e, 0x65, 0x74, 0x5f, 0x73, 0x6e, 0x69, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x3b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x53, 0x6e, 0x69,
	0x66, 0x66, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x6e, 0x65, 0x74, 0x53,
	0x6e, 0x69, 0x66, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x44, 0x0a,
	0x12, 0x65, 0x6e, 0x76, 0x5f, 0x68, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x48,
	0x00, 0x52, 0x10, 0x65, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x13, 0x73, 0x65, 0x6c, 0x66, 0x5f, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x6c, 0x66, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x73, 0x65, 0x6c, 0x66, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x0e,
	0x70, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x3e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31,
	0x50, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0d, 0x70, 0x74,
	0x72, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x11, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x3f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x5f,
	0x0a, 0x1b, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6d, 0x6f, 0x64, 0x65, 0x68,
	0x65, 0x6c, 0x70, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x40, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c,
	0x55, 0x73, 0x65, 0x72, 0x6d, 0x6f, 0x64, 0x65, 0x68, 0x65, 0x6c, 0x70, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x19, 0x63, 0x61, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x6d, 0x6f,
	0x64, 0x65, 0x68, 0x65, 0x6c, 0x70, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x4d, 0x0a, 0x15, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x5f, 0x73, 0x68, 0x65, 0x6c, 0x6c,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x41, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x65, 0x53, 0x68,
	0x65, 0x6c, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x13, 0x72, 0x65, 0x76, 0x65, 0x72,
	0x73, 0x65, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x4a,
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x42, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45,
	0x78, 0x65, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x15, 0x72, 0x65,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x75, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x18, 0x43, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00,
	0x52, 0x13, 0x72, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x55, 0x74, 0x69, 0x6d, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x56, 0x0a, 0x1c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6f,
	0x63, 0x74, 0x6c, 0x5f, 0x69, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x44, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x48, 0x00, 0x52, 0x19, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6f, 0x63, 0x74, 0x6c, 0x49, 0x6d, 0x6d,
	0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x33, 0x0a,
	0x0d, 0x6d, 0x6b, 0x64, 0x69, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x45,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6b, 0x64,
	0x69, 0x72, 0x48, 0x00, 0x52, 0x0c, 0x6d, 0x6b, 0x64, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x79, 0x6d, 0x6c, 0x69,
	0x6e, 0x6b, 0x18, 0x46, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x48, 0x00, 0x52, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x79, 0x6d, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x37, 0x0a, 0x0c, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x5f, 0x75, 0x69, 0x64, 0x18, 0x47, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x65,
	0x74, 0x55, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x03, 0x62, 0x70, 0x66, 0x18, 0x48, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x70, 0x66, 0x48, 0x00, 0x52,
	0x03, 0x62, 0x70, 0x66, 0x12, 0x4b, 0x0a, 0x14, 0x70, 0x72, 0x69, 0x76, 0x69, 0x6c, 0x65, 0x67,
	0x65, 0x5f, 0x65, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x49, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x13, 0x70, 0x72,
	0x69, 0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x4b, 0x0a, 0x15, 0x66, 0x61, 0x6b, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x61, 0x6b, 0x65, 0x45, 0x78, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x66, 0x61, 0x6b, 0x65,
	0x45, 0x78, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x47,
	0x0a, 0x13, 0x68, 0x69, 0x64, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x4b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x48, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x68, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0c, 0x6b, 0x69, 0x6c, 0x6c, 0x5f,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x4c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x4b, 0x69, 0x6c, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x00, 0x52, 0x0b, 0x6b, 0x69, 0x6c, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x12, 0x5b, 0x0a, 0x18, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x4d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4d,
	0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x4e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x53, 0x0a, 0x17, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x65,
	0x73, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x48, 0x00, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x6c, 0x65, 0x73, 0x73, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x11, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x50, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x41,
	0x0a, 0x10, 0x71, 0x75, 0x65, 0x75, 0x65, 0x5f, 0x61, 0x70, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x51, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x51, 0x75, 0x65, 0x75, 0x65, 0x41, 0x70, 0x63, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x48,
	0x00, 0x52, 0x0e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x41, 0x70, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x54, 0x0a, 0x19, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x52,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x48, 0x00, 0x52,
	0x16, 0x73, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x5f, 0x6d, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x53, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x48, 0x00, 0x52,
	0x10, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x4b, 0x0a, 0x13, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65,
	0x6d, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x54, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x56,
	0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x11, 0x61, 0x6c, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x42,
	0x0a, 0x10, 0x77, 0x72, 0x69, 0x74, 0x65, 0x5f, 0x6d, 0x65, 0x6d, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x18, 0x55, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d, 0x65, 0x6d,
	0x48, 0x00, 0x52, 0x0e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x3f, 0x0a, 0x0f, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x56, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x61, 0x64, 0x56, 0x69, 0x72, 0x74, 0x75, 0x61, 0x6c, 0x4d,
	0x65, 0x6d, 0x48, 0x00, 0x52, 0x0d, 0x72, 0x65, 0x61, 0x64, 0x4d, 0x65, 0x6d, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x50, 0x0a, 0x17, 0x6d, 0x61, 0x70, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x57,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x61, 0x70,
	0x56, 0x69, 0x65, 0x77, 0x4f, 0x66, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x14, 0x6d, 0x61, 0x70, 0x56, 0x69, 0x65, 0x77, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x3a, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x5f,
	0x62, 0x79, 0x73, 0x65, 0x6c, 0x66, 0x18, 0x58, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x79, 0x73, 0x65, 0x6c,
	0x66, 0x12, 0x47, 0x0a, 0x12, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x59, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x5a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00,
	0x52, 0x0d, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12,
	0x40, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x18, 0x5b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x50, 0x0a, 0x15, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x5c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x48, 0x00, 0x52, 0x13,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x5d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x48, 0x00, 0x52, 0x12,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x72, 0x42, 0x0f, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x22, 0xc2, 0x02, 0x0a, 0x12, 0x56, 0x30, 0x31, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x75,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72,
	0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x75, 0x6c,
	0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x12, 0x6e, 0x65, 0x65, 0x64, 0x5f, 0x73,
	0x68, 0x6f, 0x77, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x30, 0x32, 0x32, 0x39, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x6e, 0x65, 0x65, 0x64, 0x53, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x6c, 0x79,
	0x30, 0x32, 0x32, 0x39, 0x12, 0x48, 0x0a, 0x13, 0x72, 0x6f, 0x6f, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x5f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x11, 0x72, 0x6f, 0x6f,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x39,
	0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x52,
	0x75, 0x6c, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4a, 0x0a, 0x10, 0x52, 0x75, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12, 0x36, 0x0a, 0x09,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x52, 0x75, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x22, 0x3a, 0x0a, 0x0d, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x42, 0x65, 0x61, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x42, 0x65, 0x61, 0x6e, 0x52, 0x05, 0x42, 0x65, 0x61, 0x6e, 0x73,
	0x22, 0xd9, 0x02, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x42, 0x65, 0x61,
	0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x0a, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12,
	0x33, 0x0a, 0x07, 0x4e, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x4e, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x0a, 0x0c, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x76, 0x65, 0x72,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x44, 0x69, 0x73, 0x63,
	0x6f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x11, 0x52, 0x6f, 0x6f, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x52, 0x11, 0x52,
	0x6f, 0x6f, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x12, 0x44, 0x0a, 0x10, 0x43, 0x75, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x55, 0x6e,
	0x69, 0x71, 0x75, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x63, 0x64, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x53, 0x69,
	0x6d, 0x70, 0x6c, 0x65, 0x52, 0x10, 0x43, 0x75, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x55, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x61, 0x0a, 0x15,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x48, 0x0a, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x63,
	0x52, 0x0d, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x22,
	0x51, 0x0a, 0x1b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x57, 0x69, 0x74, 0x68, 0x4d, 0x61, 0x63, 0x12, 0x1c,
	0x0a, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x09, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05,
	0x42, 0x65, 0x61, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x42, 0x65, 0x61,
	0x6e, 0x73, 0x2a, 0xc9, 0x02, 0x0a, 0x10, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x43, 0x50, 0x55, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x46, 0x53, 0x49, 0x5a, 0x45, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x03, 0x12, 0x0f, 0x0a, 0x0b,
	0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x04, 0x12, 0x0e, 0x0a,
	0x0a, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x52, 0x53, 0x53, 0x10, 0x05, 0x12, 0x11, 0x0a,
	0x0d, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4e, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x07,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x41, 0x53, 0x10, 0x09, 0x12,
	0x10, 0x0a, 0x0c, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4e, 0x50, 0x52, 0x4f, 0x43, 0x10,
	0x06, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x4c,
	0x4f, 0x43, 0x4b, 0x10, 0x08, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f,
	0x4c, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x53, 0x49, 0x47, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x13,
	0x0a, 0x0f, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4d, 0x53, 0x47, 0x51, 0x55, 0x45, 0x55,
	0x45, 0x10, 0x0c, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4e, 0x49,
	0x43, 0x45, 0x10, 0x0d, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x52,
	0x54, 0x50, 0x52, 0x49, 0x4f, 0x10, 0x0e, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x5f, 0x52, 0x54, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x0f, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x4c,
	0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4e, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x53, 0x10, 0x10, 0x2a, 0xcf,
	0x01, 0x0a, 0x0a, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a,
	0x0a, 0x48, 0x4d, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x48, 0x4d, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x48, 0x4d, 0x5f, 0x54, 0x45,
	0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x44, 0x10, 0x02,
	0x12, 0x15, 0x0a, 0x11, 0x48, 0x4d, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x45,
	0x46, 0x46, 0x45, 0x43, 0x54, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x48, 0x4d, 0x5f, 0x52, 0x45,
	0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54,
	0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x48, 0x4d, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x53, 0x45, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x10, 0x12, 0x13, 0x0a, 0x0f, 0x48, 0x4d, 0x5f,
	0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x20, 0x12, 0x20,
	0x0a, 0x1c, 0x48, 0x4d, 0x5f, 0x4e, 0x4f, 0x5f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x45,
	0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x10, 0x40,
	0x2a, 0xed, 0x05, 0x0a, 0x0a, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x0e, 0x0a, 0x0a, 0x41, 0x54, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0a, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xe9, 0x07,
	0x12, 0x13, 0x0a, 0x0e, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x10, 0xea, 0x07, 0x12, 0x11, 0x0a, 0x0c, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0xeb, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0xec, 0x07, 0x12, 0x13, 0x0a, 0x0e,
	0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0xed,
	0x07, 0x12, 0x13, 0x0a, 0x0e, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x10, 0xee, 0x07, 0x12, 0x11, 0x0a, 0x0c, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0xef, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x41, 0x54, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x49, 0x4f, 0x43, 0x54, 0x4c, 0x5f, 0x49, 0x4d, 0x4d, 0x55, 0x54,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0xf0, 0x07, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x54, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4d, 0x45,
	0x10, 0xf1, 0x07, 0x12, 0x0f, 0x0a, 0x0a, 0x41, 0x54, 0x5f, 0x4d, 0x41, 0x4b, 0x45, 0x44, 0x49,
	0x52, 0x10, 0xf2, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x53, 0x59, 0x4d, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0xf3, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x49, 0x44, 0x10, 0xf4, 0x07,
	0x12, 0x13, 0x0a, 0x0e, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45,
	0x43, 0x54, 0x10, 0xcc, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f,
	0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x10, 0xcd, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x5f,
	0x4e, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x10, 0xce, 0x08, 0x12, 0x10, 0x0a,
	0x0b, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x10, 0xcf, 0x08, 0x12,
	0x10, 0x0a, 0x0b, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x43, 0x56, 0x10, 0xd0,
	0x08, 0x12, 0x16, 0x0a, 0x11, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0xd1, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x41, 0x54, 0x5f,
	0x54, 0x48, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0xd2, 0x08,
	0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x49, 0x4d, 0x41, 0x47,
	0x45, 0x10, 0xd3, 0x08, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0xd4, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x41, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b,
	0x10, 0xd5, 0x08, 0x12, 0x13, 0x0a, 0x0e, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x53, 0x4e,
	0x49, 0x46, 0x46, 0x45, 0x52, 0x10, 0xd6, 0x08, 0x12, 0x12, 0x0a, 0x0d, 0x41, 0x54, 0x5f, 0x45,
	0x4e, 0x56, 0x5f, 0x48, 0x49, 0x4a, 0x41, 0x43, 0x4b, 0x10, 0xd7, 0x08, 0x12, 0x13, 0x0a, 0x0e,
	0x41, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0xd8,
	0x08, 0x12, 0x1b, 0x0a, 0x16, 0x41, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x4d, 0x4f, 0x44, 0x45, 0x48, 0x45, 0x4c, 0x50, 0x45, 0x52, 0x10, 0xda, 0x08, 0x12, 0x15,
	0x0a, 0x10, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x5f, 0x53, 0x48, 0x45,
	0x4c, 0x4c, 0x10, 0xdb, 0x08, 0x12, 0x14, 0x0a, 0x0f, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x10, 0xdc, 0x08, 0x12, 0x0e, 0x0a, 0x09, 0x41,
	0x54, 0x5f, 0x50, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10, 0xdd, 0x08, 0x12, 0x0b, 0x0a, 0x06, 0x41,
	0x54, 0x5f, 0x42, 0x50, 0x46, 0x10, 0xde, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x41, 0x54, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x49, 0x4c, 0x45, 0x47, 0x45, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0xdf, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x41, 0x54, 0x5f, 0x46, 0x41, 0x4b,
	0x45, 0x5f, 0x45, 0x58, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xe0, 0x08, 0x12, 0x13, 0x0a,
	0x0e, 0x41, 0x54, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x55, 0x4c, 0x45, 0x10,
	0xe1, 0x08, 0x12, 0x0c, 0x0a, 0x07, 0x41, 0x54, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x10, 0xe2, 0x08,
	0x2a, 0x42, 0x0a, 0x08, 0x4e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x0a, 0x0a,
	0x4e, 0x50, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x4e, 0x46, 0x5f, 0x49, 0x50, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x5f, 0x54, 0x43, 0x50, 0x10, 0x06,
	0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x46, 0x5f, 0x49, 0x50, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x5f, 0x55,
	0x44, 0x50, 0x10, 0x11, 0x2a, 0x3e, 0x0a, 0x0b, 0x4e, 0x65, 0x74, 0x46, 0x61, 0x6d, 0x69, 0x6c,
	0x69, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x46, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c,
	0x54, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x46, 0x5f, 0x41, 0x46, 0x5f, 0x49, 0x4e, 0x45,
	0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x46, 0x5f, 0x41, 0x46, 0x5f, 0x49, 0x4e, 0x45,
	0x54, 0x36, 0x10, 0x0a, 0x2a, 0x4f, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x01,
	0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x4e, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10,
	0x02, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x54, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x03, 0x2a, 0xd4, 0x01, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45,
	0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x4e, 0x4f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x44, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x54, 0x5f, 0x50,
	0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0x03, 0x12,
	0x13, 0x0a, 0x0f, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55,
	0x54, 0x45, 0x10, 0x04, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f,
	0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x10, 0x05, 0x12, 0x1d,
	0x0a, 0x19, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x57, 0x52, 0x49, 0x54, 0x45, 0x10, 0x06, 0x12, 0x1d, 0x0a,
	0x19, 0x50, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45,
	0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x43, 0x4f, 0x50, 0x59, 0x10, 0x07, 0x2a, 0x5f, 0x0a, 0x0e,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x41, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x43,
	0x4f, 0x4d, 0x4d, 0x49, 0x54, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x54, 0x5f, 0x4d, 0x45,
	0x4d, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x52, 0x56, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x41,
	0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x54, 0x10, 0x03, 0x2a, 0x32, 0x0a,
	0x10, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4d, 0x65, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x4d, 0x6f, 0x64,
	0x65, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x4d, 0x52, 0x4d, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c,
	0x54, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x4d, 0x52, 0x4d, 0x5f, 0x50, 0x45, 0x42, 0x10,
	0x01, 0x2a, 0xad, 0x12, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x44, 0x45, 0x46,
	0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01,
	0x12, 0x16, 0x0a, 0x12, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x5f, 0x45, 0x58, 0x49, 0x54, 0x10, 0x02, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c,
	0x45, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x41,
	0x50, 0x49, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x10, 0x0b, 0x12, 0x1c, 0x0a, 0x18, 0x56, 0x30, 0x31,
	0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x57, 0x49, 0x4e, 0x33, 0x32, 0x5f,
	0x53, 0x48, 0x41, 0x52, 0x45, 0x10, 0x10, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x57, 0x4d, 0x49, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x50,
	0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1e, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x57, 0x4d, 0x49, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x1f,
	0x12, 0x1a, 0x0a, 0x16, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x57, 0x4d, 0x49, 0x5f, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x10, 0x20, 0x12, 0x13, 0x0a, 0x0f,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x57, 0x4d, 0x49, 0x5f, 0x51, 0x55, 0x45, 0x52, 0x59, 0x10,
	0x21, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50,
	0x54, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f, 0x42, 0x59, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x22, 0x12, 0x25, 0x0a, 0x21, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x41, 0x4d, 0x53, 0x49, 0x5f, 0x42, 0x59, 0x5f,
	0x41, 0x4d, 0x53, 0x49, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x23, 0x12, 0x1f,
	0x0a, 0x1b, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x41,
	0x4d, 0x53, 0x49, 0x5f, 0x44, 0x4c, 0x4c, 0x48, 0x49, 0x4a, 0x41, 0x43, 0x4b, 0x10, 0x24, 0x12,
	0x1b, 0x0a, 0x17, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f,
	0x4b, 0x45, 0x59, 0x5f, 0x4c, 0x4f, 0x47, 0x47, 0x45, 0x52, 0x10, 0x25, 0x12, 0x1c, 0x0a, 0x18,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x54, 0x10, 0x26, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x10, 0x27, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4c, 0x4f, 0x47, 0x5f,
	0x43, 0x4c, 0x45, 0x41, 0x52, 0x10, 0x28, 0x12, 0x13, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x51, 0x55, 0x45, 0x55, 0x45, 0x5f, 0x41, 0x50, 0x43, 0x10, 0x29, 0x12, 0x1c, 0x0a, 0x18,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58,
	0x54, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x41, 0x44, 0x10, 0x2a, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x4b, 0x49, 0x4c, 0x4c, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x2b, 0x12, 0x18, 0x0a, 0x14, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x49, 0x4e, 0x4a, 0x45, 0x43, 0x54, 0x10, 0x2c, 0x12, 0x13, 0x0a, 0x0f,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x10,
	0x2d, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x44, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x2e, 0x12, 0x19, 0x0a, 0x15, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x10, 0x32, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x57,
	0x52, 0x49, 0x54, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10,
	0x33, 0x12, 0x1a, 0x0a, 0x16, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x41, 0x4c, 0x4c, 0x4f, 0x43,
	0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0x34, 0x12, 0x1c, 0x0a,
	0x18, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x4d,
	0x45, 0x4d, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0x3a, 0x12, 0x21, 0x0a, 0x1d, 0x56,
	0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d, 0x41, 0x50, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x10, 0x3b, 0x12, 0x17,
	0x0a, 0x13, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x42,
	0x59, 0x53, 0x45, 0x4c, 0x46, 0x10, 0x3c, 0x12, 0x19, 0x0a, 0x14, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x48, 0x45, 0x41, 0x50, 0x5f, 0x53, 0x50, 0x52, 0x41, 0x59, 0x10,
	0xf5, 0x03, 0x12, 0x12, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x5f,
	0x52, 0x4f, 0x50, 0x10, 0xf6, 0x03, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x55,
	0x4c, 0x45, 0x10, 0xf7, 0x03, 0x12, 0x20, 0x0a, 0x1b, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d,
	0x45, 0x4d, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0xf8, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x5f, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x5f,
	0x43, 0x4f, 0x44, 0x45, 0x10, 0xf9, 0x03, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x4d, 0x45, 0x4d, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x41, 0x54, 0x54, 0x41,
	0x43, 0x4b, 0x10, 0xfa, 0x03, 0x12, 0x21, 0x0a, 0x1c, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x43, 0x4b, 0x5f, 0x41, 0x54, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x45, 0x5f, 0x41,
	0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xfb, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x43, 0x4b, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x10, 0xfc, 0x03, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d, 0x45,
	0x4d, 0x5f, 0x53, 0x54, 0x41, 0x43, 0x4b, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x54, 0x52, 0x55, 0x4e,
	0x10, 0xfd, 0x03, 0x12, 0x15, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x43, 0x56, 0x45,
	0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xd9, 0x04, 0x12, 0x1e, 0x0a, 0x19, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x4d, 0x4f, 0x54, 0x45, 0x5f, 0x42, 0x55, 0x47, 0x5f, 0x4f,
	0x56, 0x45, 0x52, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xda, 0x04, 0x12, 0x19, 0x0a, 0x14, 0x56, 0x30,
	0x31, 0x41, 0x54, 0x5f, 0x50, 0x55, 0x50, 0x50, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x10, 0xdb, 0x04, 0x12, 0x1a, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x57,
	0x48, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0xdc,
	0x04, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f,
	0x4c, 0x45, 0x4e, 0x5f, 0x41, 0x42, 0x4e, 0x4f, 0x52, 0x4d, 0x41, 0x4c, 0x10, 0xdd, 0x04, 0x12,
	0x1d, 0x0a, 0x18, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x44, 0x45,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x10, 0xe2, 0x04, 0x12, 0x19,
	0x0a, 0x14, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0xe3, 0x04, 0x12, 0x18, 0x0a, 0x13, 0x56, 0x30, 0x31,
	0x41, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45,
	0x10, 0xe4, 0x04, 0x12, 0x1b, 0x0a, 0x16, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x52, 0x10, 0xe5, 0x04,
	0x12, 0x1a, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f,
	0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x52, 0x10, 0xe6, 0x04, 0x12, 0x12, 0x0a, 0x0d,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0xe9, 0x07,
	0x12, 0x16, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0xea, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x10, 0xeb, 0x07, 0x12, 0x15,
	0x0a, 0x10, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x57, 0x52, 0x49,
	0x54, 0x45, 0x10, 0xec, 0x07, 0x12, 0x16, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0xed, 0x07, 0x12, 0x16, 0x0a,
	0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x10, 0xee, 0x07, 0x12, 0x14, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0xef, 0x07, 0x12, 0x19, 0x0a, 0x14, 0x56,
	0x30, 0x31, 0x41, 0x54, 0x5f, 0x49, 0x43, 0x54, 0x4c, 0x5f, 0x49, 0x4d, 0x4d, 0x55, 0x54, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0xf0, 0x07, 0x12, 0x18, 0x0a, 0x13, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x5f, 0x55, 0x54, 0x49, 0x4d, 0x45, 0x10, 0xf1, 0x07,
	0x12, 0x12, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d, 0x41, 0x4b, 0x45, 0x44, 0x49,
	0x52, 0x10, 0xf2, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x59,
	0x4d, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0xf3, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x49, 0x44, 0x10, 0xf4, 0x07, 0x12, 0x16, 0x0a, 0x11,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x4e, 0x47,
	0x45, 0x10, 0xf6, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43,
	0x52, 0x49, 0x50, 0x54, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x10,
	0xcc, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49,
	0x50, 0x54, 0x5f, 0x52, 0x55, 0x4e, 0x5f, 0x57, 0x4d, 0x49, 0x43, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0xcd, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x4c, 0x45, 0x53, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10, 0xd5, 0x08, 0x12,
	0x16, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x53, 0x4e, 0x49,
	0x46, 0x46, 0x45, 0x52, 0x10, 0xd6, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x41, 0x54,
	0x5f, 0x45, 0x4e, 0x56, 0x5f, 0x48, 0x49, 0x4a, 0x41, 0x43, 0x4b, 0x10, 0xd7, 0x08, 0x12, 0x16,
	0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x10, 0xd8, 0x08, 0x12, 0x14, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0xd9, 0x08, 0x12, 0x1e, 0x0a, 0x19,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x4d,
	0x4f, 0x44, 0x45, 0x48, 0x45, 0x4c, 0x50, 0x45, 0x52, 0x10, 0xda, 0x08, 0x12, 0x18, 0x0a, 0x13,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x5f, 0x53, 0x48,
	0x45, 0x4c, 0x4c, 0x10, 0xdb, 0x08, 0x12, 0x17, 0x0a, 0x12, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x10, 0xdc, 0x08, 0x12,
	0x11, 0x0a, 0x0c, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x50, 0x54, 0x52, 0x41, 0x43, 0x45, 0x10,
	0xdd, 0x08, 0x12, 0x0e, 0x0a, 0x09, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x42, 0x50, 0x46, 0x10,
	0xde, 0x08, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x56,
	0x49, 0x4c, 0x45, 0x47, 0x45, 0x5f, 0x45, 0x53, 0x43, 0x41, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0xdf, 0x08, 0x12, 0x18, 0x0a, 0x13, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x46, 0x41, 0x4b,
	0x45, 0x5f, 0x45, 0x58, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0xe0, 0x08, 0x12, 0x16, 0x0a,
	0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x48, 0x49, 0x44, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x55,
	0x4c, 0x45, 0x10, 0xe1, 0x08, 0x12, 0x0f, 0x0a, 0x0a, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4b,
	0x49, 0x4c, 0x4c, 0x10, 0xe2, 0x08, 0x12, 0x14, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f,
	0x53, 0x45, 0x54, 0x52, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0xe3, 0x08, 0x12, 0x15, 0x0a, 0x10,
	0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54,
	0x10, 0xb0, 0x09, 0x12, 0x16, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54,
	0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x10, 0xb1, 0x09, 0x12, 0x15, 0x0a, 0x10, 0x56,
	0x30, 0x31, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x10,
	0xb2, 0x09, 0x12, 0x15, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0xb3, 0x09, 0x12, 0x25, 0x0a, 0x20, 0x56, 0x30, 0x31,
	0x41, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x4f,
	0x54, 0x45, 0x44, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x49, 0x4c, 0x45, 0x47, 0x45, 0x10, 0xb4, 0x09,
	0x12, 0x16, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54,
	0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0xb5, 0x09, 0x12, 0x15, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x41,
	0x54, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0xb6, 0x09, 0x12,
	0x14, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x52, 0x45, 0x47, 0x5f, 0x57, 0x52, 0x49,
	0x54, 0x45, 0x10, 0xb7, 0x09, 0x12, 0x1b, 0x0a, 0x16, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x52,
	0x45, 0x47, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x10,
	0xb8, 0x09, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x30, 0x31, 0x41, 0x54, 0x5f, 0x4e, 0x45, 0x54, 0x5f,
	0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0xb9,
	0x09, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76,
	0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_acdr_proto_rawDescOnce sync.Once
	file_agent_acdr_proto_rawDescData = file_agent_acdr_proto_rawDesc
)

func file_agent_acdr_proto_rawDescGZIP() []byte {
	file_agent_acdr_proto_rawDescOnce.Do(func() {
		file_agent_acdr_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_acdr_proto_rawDescData)
	})
	return file_agent_acdr_proto_rawDescData
}

var file_agent_acdr_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_agent_acdr_proto_msgTypes = make([]protoimpl.MessageInfo, 96)
var file_agent_acdr_proto_goTypes = []any{
	(EnumResourceType)(0),                    // 0: agent.EnumResourceType
	(HandleMode)(0),                          // 1: agent.HandleMode
	(ActionType)(0),                          // 2: agent.ActionType
	(NetProto)(0),                            // 3: agent.NetProto
	(NetFamilies)(0),                         // 4: agent.NetFamilies
	(StatusType)(0),                          // 5: agent.StatusType
	(ProtectType)(0),                         // 6: agent.ProtectType
	(AllocationType)(0),                      // 7: agent.AllocationType
	(WriteMemRiskMode)(0),                    // 8: agent.WriteMemRiskMode
	(V01ActionType)(0),                       // 9: agent.V01ActionType
	(WhiteAddBlack_ReportType)(0),            // 10: agent.WhiteAddBlack.ReportType
	(*RuleInfoMessage)(nil),                  // 11: agent.RuleInfoMessage
	(*NgavProcessInfo)(nil),                  // 12: agent.NgavProcessInfo
	(*FileCreateContentInfo)(nil),            // 13: agent.FileCreateContentInfo
	(*FileOpenContentInfo)(nil),              // 14: agent.FileOpenContentInfo
	(*FileWriteContentInfo)(nil),             // 15: agent.FileWriteContentInfo
	(*FileRenameContentInfo)(nil),            // 16: agent.FileRenameContentInfo
	(*FileDeleteContentInfo)(nil),            // 17: agent.FileDeleteContentInfo
	(*FileModeChangeContentInfo)(nil),        // 18: agent.FileModeChangeContentInfo
	(*FileIoctlImmutableContentInfo)(nil),    // 19: agent.FileIoctlImmutableContentInfo
	(*Mkdir)(nil),                            // 20: agent.Mkdir
	(*FileSymlink)(nil),                      // 21: agent.FileSymlink
	(*FileSetUid)(nil),                       // 22: agent.FileSetUid
	(*NetContentBaseInfo)(nil),               // 23: agent.NetContentBaseInfo
	(*NetConnectContentInfo)(nil),            // 24: agent.NetConnectContentInfo
	(*NetAcceptContentInfo)(nil),             // 25: agent.NetAcceptContentInfo
	(*NetListenContentInfo)(nil),             // 26: agent.NetListenContentInfo
	(*NetSendContentInfo)(nil),               // 27: agent.NetSendContentInfo
	(*NetRecvContentInfo)(nil),               // 28: agent.NetRecvContentInfo
	(*FilelessAttackInfo)(nil),               // 29: agent.FilelessAttackInfo
	(*NetSnifferInfo)(nil),                   // 30: agent.NetSnifferInfo
	(*EnvHijackInfo)(nil),                    // 31: agent.EnvHijackInfo
	(*SelfDeleteInfo)(nil),                   // 32: agent.SelfDeleteInfo
	(*PtraceInfo)(nil),                       // 33: agent.PtraceInfo
	(*FileLinkInfo)(nil),                     // 34: agent.FileLinkInfo
	(*CallUsermodehelperInfo)(nil),           // 35: agent.CallUsermodehelperInfo
	(*ReverseShellInfo)(nil),                 // 36: agent.ReverseShellInfo
	(*ProcessExecInfo)(nil),                  // 37: agent.ProcessExecInfo
	(*PrivilegeEscalation)(nil),              // 38: agent.PrivilegeEscalation
	(*RestoreUtime)(nil),                     // 39: agent.RestoreUtime
	(*Bpf)(nil),                              // 40: agent.Bpf
	(*FakeExeFileInfo)(nil),                  // 41: agent.FakeExeFileInfo
	(*HideModuleInfo)(nil),                   // 42: agent.HideModuleInfo
	(*KillInfo)(nil),                         // 43: agent.KillInfo
	(*RuleActionItem)(nil),                   // 44: agent.RuleActionItem
	(*StatusProcessInfo)(nil),                // 45: agent.StatusProcessInfo
	(*StatusFileInfo)(nil),                   // 46: agent.StatusFileInfo
	(*StatusNetInfo)(nil),                    // 47: agent.StatusNetInfo
	(*RiskStatus)(nil),                       // 48: agent.RiskStatus
	(*RiskStatusListReq)(nil),                // 49: agent.RiskStatusListReq
	(*RiskStatusListResp)(nil),               // 50: agent.RiskStatusListResp
	(*NodeAddrInfo)(nil),                     // 51: agent.NodeAddrInfo
	(*ConnectionInfo)(nil),                   // 52: agent.ConnectionInfo
	(*DomainConnectionInfo)(nil),             // 53: agent.DomainConnectionInfo
	(*NodeFileInfo)(nil),                     // 54: agent.NodeFileInfo
	(*NodeRemoteUrl)(nil),                    // 55: agent.NodeRemoteUrl
	(*AcdrProcessUnique)(nil),                // 56: agent.AcdrProcessUnique
	(*AcdrProcessSimple)(nil),                // 57: agent.AcdrProcessSimple
	(*FileBaseInfo)(nil),                     // 58: agent.FileBaseInfo
	(*NodeProcessInfo)(nil),                  // 59: agent.NodeProcessInfo
	(*AcdrProcessParrentRelationUpdate)(nil), // 60: agent.AcdrProcessParrentRelationUpdate
	(*FileRename)(nil),                       // 61: agent.FileRename
	(*RegCreate)(nil),                        // 62: agent.RegCreate
	(*RegDelete)(nil),                        // 63: agent.RegDelete
	(*RegWrite)(nil),                         // 64: agent.RegWrite
	(*RegSetSecurity)(nil),                   // 65: agent.RegSetSecurity
	(*ScriptHttp)(nil),                       // 66: agent.ScriptHttp
	(*ScriptImageLoad)(nil),                  // 67: agent.ScriptImageLoad
	(*ScriptRunWmicCode)(nil),                // 68: agent.ScriptRunWmicCode
	(*ScriptScheduleCreate)(nil),             // 69: agent.ScriptScheduleCreate
	(*ScriptGetApiAddr)(nil),                 // 70: agent.ScriptGetApiAddr
	(*ScriptWmicWin32Share)(nil),             // 71: agent.ScriptWmicWin32Share
	(*ScriptWmicTerminateProcess)(nil),       // 72: agent.ScriptWmicTerminateProcess
	(*ScriptWmicRegOper)(nil),                // 73: agent.ScriptWmicRegOper
	(*ScriptWmicServiceOper)(nil),            // 74: agent.ScriptWmicServiceOper
	(*ScriptWmicQuery)(nil),                  // 75: agent.ScriptWmicQuery
	(*ScriptAmsiByAmsiContext)(nil),          // 76: agent.ScriptAmsiByAmsiContext
	(*ScriptAmsiDllHijack)(nil),              // 77: agent.ScriptAmsiDllHijack
	(*ScriptEmail)(nil),                      // 78: agent.ScriptEmail
	(*LogClear)(nil),                         // 79: agent.LogClear
	(*CveAttack)(nil),                        // 80: agent.CveAttack
	(*RemoteBugOverflow)(nil),                // 81: agent.RemoteBugOverflow
	(*PuppetProcessAttack)(nil),              // 82: agent.PuppetProcessAttack
	(*V01PtraceInfo)(nil),                    // 83: agent.V01PtraceInfo
	(*V01KillInfo)(nil),                      // 84: agent.V01KillInfo
	(*SetRlimit)(nil),                        // 85: agent.SetRlimit
	(*MemActionOtherInfo)(nil),               // 86: agent.MemActionOtherInfo
	(*QueueApcThread)(nil),                   // 87: agent.QueueApcThread
	(*SetContextThread)(nil),                 // 88: agent.SetContextThread
	(*ProtectVirtualMem)(nil),                // 89: agent.ProtectVirtualMem
	(*AllocateVirtualMem)(nil),               // 90: agent.AllocateVirtualMem
	(*WriteVirtualMem)(nil),                  // 91: agent.WriteVirtualMem
	(*ReadVirtualMem)(nil),                   // 92: agent.ReadVirtualMem
	(*MapViewOfSection)(nil),                 // 93: agent.MapViewOfSection
	(*WhiteAddBlack)(nil),                    // 94: agent.WhiteAddBlack
	(*OpenDeviceObject)(nil),                 // 95: agent.OpenDeviceObject
	(*CreateServiceAction)(nil),              // 96: agent.CreateServiceAction
	(*StartServiceAction)(nil),               // 97: agent.StartServiceAction
	(*CreateTaskScheduler)(nil),              // 98: agent.CreateTaskScheduler
	(*StartTaskScheduler)(nil),               // 99: agent.StartTaskScheduler
	(*V01RuleActionItem)(nil),                // 100: agent.V01RuleActionItem
	(*V01RuleInfoMessage)(nil),               // 101: agent.V01RuleInfoMessage
	(*RuleInfoMessages)(nil),                 // 102: agent.RuleInfoMessages
	(*OutreachInfos)(nil),                    // 103: agent.OutreachInfos
	(*OutreachBean)(nil),                     // 104: agent.OutreachBean
	(*InternalOutreachInfos)(nil),            // 105: agent.InternalOutreachInfos
	(*InternalOutreachInfoWithMac)(nil),      // 106: agent.InternalOutreachInfoWithMac
	(*ClientID)(nil),                         // 107: agent.ClientID
	(*RiskHeader)(nil),                       // 108: agent.RiskHeader
	(*ProcFileDetailInfo)(nil),               // 109: agent.ProcFileDetailInfo
	(*SignatureInfo)(nil),                    // 110: agent.SignatureInfo
}
var file_agent_acdr_proto_depIdxs = []int32{
	107, // 0: agent.RuleInfoMessage.date_time:type_name -> agent.ClientID
	108, // 1: agent.RuleInfoMessage.header:type_name -> agent.RiskHeader
	1,   // 2: agent.RuleInfoMessage.handle_mode:type_name -> agent.HandleMode
	44,  // 3: agent.RuleInfoMessage.rule_action_list:type_name -> agent.RuleActionItem
	12,  // 4: agent.RuleInfoMessage.process_info_list:type_name -> agent.NgavProcessInfo
	54,  // 5: agent.FileModeChangeContentInfo.file_info:type_name -> agent.NodeFileInfo
	54,  // 6: agent.FileSymlink.file_info_src:type_name -> agent.NodeFileInfo
	54,  // 7: agent.FileSymlink.file_info_dest:type_name -> agent.NodeFileInfo
	4,   // 8: agent.NetContentBaseInfo.address_family:type_name -> agent.NetFamilies
	3,   // 9: agent.NetContentBaseInfo.protocol:type_name -> agent.NetProto
	23,  // 10: agent.NetConnectContentInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	23,  // 11: agent.NetAcceptContentInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	23,  // 12: agent.NetListenContentInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	23,  // 13: agent.NetSendContentInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	23,  // 14: agent.NetRecvContentInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	4,   // 15: agent.ReverseShellInfo.in_addr_family:type_name -> agent.NetFamilies
	3,   // 16: agent.ReverseShellInfo.in_proto:type_name -> agent.NetProto
	4,   // 17: agent.ReverseShellInfo.out_addr_family:type_name -> agent.NetFamilies
	3,   // 18: agent.ReverseShellInfo.out_proto:type_name -> agent.NetProto
	2,   // 19: agent.RuleActionItem.action_type:type_name -> agent.ActionType
	11,  // 20: agent.RuleActionItem.rule_msg:type_name -> agent.RuleInfoMessage
	13,  // 21: agent.RuleActionItem.file_create_content:type_name -> agent.FileCreateContentInfo
	14,  // 22: agent.RuleActionItem.file_open_content:type_name -> agent.FileOpenContentInfo
	15,  // 23: agent.RuleActionItem.file_write_content:type_name -> agent.FileWriteContentInfo
	16,  // 24: agent.RuleActionItem.file_rename_content:type_name -> agent.FileRenameContentInfo
	17,  // 25: agent.RuleActionItem.file_delete_content:type_name -> agent.FileDeleteContentInfo
	24,  // 26: agent.RuleActionItem.net_connect_content:type_name -> agent.NetConnectContentInfo
	25,  // 27: agent.RuleActionItem.net_accept_content:type_name -> agent.NetAcceptContentInfo
	26,  // 28: agent.RuleActionItem.net_listen_content:type_name -> agent.NetListenContentInfo
	27,  // 29: agent.RuleActionItem.net_send_content:type_name -> agent.NetSendContentInfo
	28,  // 30: agent.RuleActionItem.net_recv_content:type_name -> agent.NetRecvContentInfo
	29,  // 31: agent.RuleActionItem.fileless_attack_content:type_name -> agent.FilelessAttackInfo
	30,  // 32: agent.RuleActionItem.net_sniffer_content:type_name -> agent.NetSnifferInfo
	31,  // 33: agent.RuleActionItem.env_hijack_content:type_name -> agent.EnvHijackInfo
	32,  // 34: agent.RuleActionItem.self_delete_content:type_name -> agent.SelfDeleteInfo
	33,  // 35: agent.RuleActionItem.ptrace_content:type_name -> agent.PtraceInfo
	34,  // 36: agent.RuleActionItem.file_link_content:type_name -> agent.FileLinkInfo
	35,  // 37: agent.RuleActionItem.call_usermodehelper_content:type_name -> agent.CallUsermodehelperInfo
	36,  // 38: agent.RuleActionItem.reverse_shell_content:type_name -> agent.ReverseShellInfo
	37,  // 39: agent.RuleActionItem.process_exec_content:type_name -> agent.ProcessExecInfo
	39,  // 40: agent.RuleActionItem.restore_utime_content:type_name -> agent.RestoreUtime
	19,  // 41: agent.RuleActionItem.file_ioctl_immutable_content:type_name -> agent.FileIoctlImmutableContentInfo
	20,  // 42: agent.RuleActionItem.mkdir_content:type_name -> agent.Mkdir
	21,  // 43: agent.RuleActionItem.file_symlink:type_name -> agent.FileSymlink
	22,  // 44: agent.RuleActionItem.file_set_uid:type_name -> agent.FileSetUid
	40,  // 45: agent.RuleActionItem.bpf:type_name -> agent.Bpf
	38,  // 46: agent.RuleActionItem.privilege_escalation:type_name -> agent.PrivilegeEscalation
	41,  // 47: agent.RuleActionItem.fake_exe_file_content:type_name -> agent.FakeExeFileInfo
	42,  // 48: agent.RuleActionItem.hide_module_content:type_name -> agent.HideModuleInfo
	43,  // 49: agent.RuleActionItem.kill_content:type_name -> agent.KillInfo
	23,  // 50: agent.StatusNetInfo.net_content_base_info:type_name -> agent.NetContentBaseInfo
	45,  // 51: agent.RiskStatus.proc_info:type_name -> agent.StatusProcessInfo
	47,  // 52: agent.RiskStatus.net_info:type_name -> agent.StatusNetInfo
	46,  // 53: agent.RiskStatus.file_info:type_name -> agent.StatusFileInfo
	48,  // 54: agent.RiskStatusListReq.risk_status_list:type_name -> agent.RiskStatus
	5,   // 55: agent.RiskStatusListResp.status_type:type_name -> agent.StatusType
	4,   // 56: agent.NodeAddrInfo.address_family:type_name -> agent.NetFamilies
	3,   // 57: agent.NodeAddrInfo.protocol:type_name -> agent.NetProto
	51,  // 58: agent.ConnectionInfo.remote:type_name -> agent.NodeAddrInfo
	51,  // 59: agent.ConnectionInfo.local:type_name -> agent.NodeAddrInfo
	55,  // 60: agent.DomainConnectionInfo.remote:type_name -> agent.NodeRemoteUrl
	51,  // 61: agent.DomainConnectionInfo.local:type_name -> agent.NodeAddrInfo
	109, // 62: agent.NodeFileInfo.detail_info:type_name -> agent.ProcFileDetailInfo
	110, // 63: agent.NodeFileInfo.signatureInfo:type_name -> agent.SignatureInfo
	58,  // 64: agent.AcdrProcessSimple.process_file_info:type_name -> agent.FileBaseInfo
	56,  // 65: agent.NodeProcessInfo.unique_child:type_name -> agent.AcdrProcessUnique
	54,  // 66: agent.NodeProcessInfo.process_file_info:type_name -> agent.NodeFileInfo
	56,  // 67: agent.NodeProcessInfo.exec_src:type_name -> agent.AcdrProcessUnique
	56,  // 68: agent.AcdrProcessParrentRelationUpdate.process_parrent:type_name -> agent.AcdrProcessUnique
	56,  // 69: agent.AcdrProcessParrentRelationUpdate.process_chiled:type_name -> agent.AcdrProcessUnique
	54,  // 70: agent.FileRename.from:type_name -> agent.NodeFileInfo
	54,  // 71: agent.FileRename.to:type_name -> agent.NodeFileInfo
	59,  // 72: agent.ScriptWmicTerminateProcess.proc:type_name -> agent.NodeProcessInfo
	54,  // 73: agent.ScriptAmsiDllHijack.path:type_name -> agent.NodeFileInfo
	51,  // 74: agent.RemoteBugOverflow.node:type_name -> agent.NodeAddrInfo
	59,  // 75: agent.V01PtraceInfo.proc_info:type_name -> agent.NodeProcessInfo
	59,  // 76: agent.V01KillInfo.proc_info:type_name -> agent.NodeProcessInfo
	0,   // 77: agent.SetRlimit.resource:type_name -> agent.EnumResourceType
	59,  // 78: agent.QueueApcThread.node_proc_info:type_name -> agent.NodeProcessInfo
	86,  // 79: agent.QueueApcThread.info:type_name -> agent.MemActionOtherInfo
	59,  // 80: agent.SetContextThread.node_proc_info:type_name -> agent.NodeProcessInfo
	86,  // 81: agent.SetContextThread.info:type_name -> agent.MemActionOtherInfo
	59,  // 82: agent.ProtectVirtualMem.node_proc_info:type_name -> agent.NodeProcessInfo
	6,   // 83: agent.ProtectVirtualMem.old_protect:type_name -> agent.ProtectType
	6,   // 84: agent.ProtectVirtualMem.new_protect:type_name -> agent.ProtectType
	86,  // 85: agent.ProtectVirtualMem.info:type_name -> agent.MemActionOtherInfo
	59,  // 86: agent.AllocateVirtualMem.node_proc_info:type_name -> agent.NodeProcessInfo
	6,   // 87: agent.AllocateVirtualMem.protect:type_name -> agent.ProtectType
	7,   // 88: agent.AllocateVirtualMem.alloc_type:type_name -> agent.AllocationType
	86,  // 89: agent.AllocateVirtualMem.info:type_name -> agent.MemActionOtherInfo
	59,  // 90: agent.WriteVirtualMem.node_proc_info:type_name -> agent.NodeProcessInfo
	8,   // 91: agent.WriteVirtualMem.risk_mode:type_name -> agent.WriteMemRiskMode
	86,  // 92: agent.WriteVirtualMem.info:type_name -> agent.MemActionOtherInfo
	59,  // 93: agent.ReadVirtualMem.node_proc_info:type_name -> agent.NodeProcessInfo
	86,  // 94: agent.ReadVirtualMem.info:type_name -> agent.MemActionOtherInfo
	59,  // 95: agent.MapViewOfSection.node_proc_info:type_name -> agent.NodeProcessInfo
	6,   // 96: agent.MapViewOfSection.protect:type_name -> agent.ProtectType
	7,   // 97: agent.MapViewOfSection.alloc_type:type_name -> agent.AllocationType
	86,  // 98: agent.MapViewOfSection.info:type_name -> agent.MemActionOtherInfo
	10,  // 99: agent.WhiteAddBlack.report_id:type_name -> agent.WhiteAddBlack.ReportType
	54,  // 100: agent.WhiteAddBlack.dll_file_info:type_name -> agent.NodeFileInfo
	59,  // 101: agent.StartTaskScheduler.start_process:type_name -> agent.NodeProcessInfo
	9,   // 102: agent.V01RuleActionItem.action_type:type_name -> agent.V01ActionType
	56,  // 103: agent.V01RuleActionItem.process_unique:type_name -> agent.AcdrProcessUnique
	54,  // 104: agent.V01RuleActionItem.process_file_info:type_name -> agent.NodeFileInfo
	60,  // 105: agent.V01RuleActionItem.process_relation_update:type_name -> agent.AcdrProcessParrentRelationUpdate
	59,  // 106: agent.V01RuleActionItem.process_create:type_name -> agent.NodeProcessInfo
	54,  // 107: agent.V01RuleActionItem.file_create:type_name -> agent.NodeFileInfo
	54,  // 108: agent.V01RuleActionItem.file_write:type_name -> agent.NodeFileInfo
	54,  // 109: agent.V01RuleActionItem.file_read:type_name -> agent.NodeFileInfo
	54,  // 110: agent.V01RuleActionItem.file_delete:type_name -> agent.NodeFileInfo
	61,  // 111: agent.V01RuleActionItem.file_rename:type_name -> agent.FileRename
	52,  // 112: agent.V01RuleActionItem.net_connect:type_name -> agent.ConnectionInfo
	52,  // 113: agent.V01RuleActionItem.net_accept:type_name -> agent.ConnectionInfo
	51,  // 114: agent.V01RuleActionItem.net_listen:type_name -> agent.NodeAddrInfo
	62,  // 115: agent.V01RuleActionItem.reg_create:type_name -> agent.RegCreate
	66,  // 116: agent.V01RuleActionItem.script_http:type_name -> agent.ScriptHttp
	67,  // 117: agent.V01RuleActionItem.script_image_load:type_name -> agent.ScriptImageLoad
	68,  // 118: agent.V01RuleActionItem.script_run_wmic_code:type_name -> agent.ScriptRunWmicCode
	69,  // 119: agent.V01RuleActionItem.script_schedule_create:type_name -> agent.ScriptScheduleCreate
	70,  // 120: agent.V01RuleActionItem.script_get_api_addr:type_name -> agent.ScriptGetApiAddr
	71,  // 121: agent.V01RuleActionItem.script_wmic_win32_share:type_name -> agent.ScriptWmicWin32Share
	72,  // 122: agent.V01RuleActionItem.script_wmic_terminate_process:type_name -> agent.ScriptWmicTerminateProcess
	73,  // 123: agent.V01RuleActionItem.script_wmic_reg_oper:type_name -> agent.ScriptWmicRegOper
	74,  // 124: agent.V01RuleActionItem.script_wmic_service_oper:type_name -> agent.ScriptWmicServiceOper
	75,  // 125: agent.V01RuleActionItem.script_wmic_query:type_name -> agent.ScriptWmicQuery
	76,  // 126: agent.V01RuleActionItem.script_amsi_by_amsi_context:type_name -> agent.ScriptAmsiByAmsiContext
	77,  // 127: agent.V01RuleActionItem.script_amsi_dll_hijack:type_name -> agent.ScriptAmsiDllHijack
	78,  // 128: agent.V01RuleActionItem.script_email:type_name -> agent.ScriptEmail
	79,  // 129: agent.V01RuleActionItem.log_clear:type_name -> agent.LogClear
	59,  // 130: agent.V01RuleActionItem.kill_process_detail:type_name -> agent.NodeProcessInfo
	59,  // 131: agent.V01RuleActionItem.process_inject_detail:type_name -> agent.NodeProcessInfo
	63,  // 132: agent.V01RuleActionItem.reg_delete:type_name -> agent.RegDelete
	64,  // 133: agent.V01RuleActionItem.reg_write:type_name -> agent.RegWrite
	65,  // 134: agent.V01RuleActionItem.reg_set_security:type_name -> agent.RegSetSecurity
	54,  // 135: agent.V01RuleActionItem.file_hide:type_name -> agent.NodeFileInfo
	54,  // 136: agent.V01RuleActionItem.file_readonly:type_name -> agent.NodeFileInfo
	54,  // 137: agent.V01RuleActionItem.load_remote_module:type_name -> agent.NodeFileInfo
	94,  // 138: agent.V01RuleActionItem.white_add_black:type_name -> agent.WhiteAddBlack
	80,  // 139: agent.V01RuleActionItem.cve:type_name -> agent.CveAttack
	81,  // 140: agent.V01RuleActionItem.remote_bug_overflow:type_name -> agent.RemoteBugOverflow
	82,  // 141: agent.V01RuleActionItem.puppet_process:type_name -> agent.PuppetProcessAttack
	53,  // 142: agent.V01RuleActionItem.net_connect_domain:type_name -> agent.DomainConnectionInfo
	30,  // 143: agent.V01RuleActionItem.net_sniffer_content:type_name -> agent.NetSnifferInfo
	31,  // 144: agent.V01RuleActionItem.env_hijack_content:type_name -> agent.EnvHijackInfo
	32,  // 145: agent.V01RuleActionItem.self_delete_content:type_name -> agent.SelfDeleteInfo
	83,  // 146: agent.V01RuleActionItem.ptrace_content:type_name -> agent.V01PtraceInfo
	34,  // 147: agent.V01RuleActionItem.file_link_content:type_name -> agent.FileLinkInfo
	35,  // 148: agent.V01RuleActionItem.call_usermodehelper_content:type_name -> agent.CallUsermodehelperInfo
	36,  // 149: agent.V01RuleActionItem.reverse_shell_content:type_name -> agent.ReverseShellInfo
	59,  // 150: agent.V01RuleActionItem.process_exec_content:type_name -> agent.NodeProcessInfo
	54,  // 151: agent.V01RuleActionItem.restore_utime_content:type_name -> agent.NodeFileInfo
	54,  // 152: agent.V01RuleActionItem.file_ioctl_immutable_content:type_name -> agent.NodeFileInfo
	20,  // 153: agent.V01RuleActionItem.mkdir_content:type_name -> agent.Mkdir
	21,  // 154: agent.V01RuleActionItem.file_symlink:type_name -> agent.FileSymlink
	54,  // 155: agent.V01RuleActionItem.file_set_uid:type_name -> agent.NodeFileInfo
	40,  // 156: agent.V01RuleActionItem.bpf:type_name -> agent.Bpf
	59,  // 157: agent.V01RuleActionItem.privilege_escalation:type_name -> agent.NodeProcessInfo
	41,  // 158: agent.V01RuleActionItem.fake_exe_file_content:type_name -> agent.FakeExeFileInfo
	42,  // 159: agent.V01RuleActionItem.hide_module_content:type_name -> agent.HideModuleInfo
	84,  // 160: agent.V01RuleActionItem.kill_content:type_name -> agent.V01KillInfo
	18,  // 161: agent.V01RuleActionItem.file_mode_change_content:type_name -> agent.FileModeChangeContentInfo
	85,  // 162: agent.V01RuleActionItem.resoure_limit:type_name -> agent.SetRlimit
	29,  // 163: agent.V01RuleActionItem.fileless_attack_content:type_name -> agent.FilelessAttackInfo
	54,  // 164: agent.V01RuleActionItem.file_open_content:type_name -> agent.NodeFileInfo
	87,  // 165: agent.V01RuleActionItem.queue_apc_detail:type_name -> agent.QueueApcThread
	88,  // 166: agent.V01RuleActionItem.set_context_thread_detail:type_name -> agent.SetContextThread
	89,  // 167: agent.V01RuleActionItem.protect_mem_detail:type_name -> agent.ProtectVirtualMem
	90,  // 168: agent.V01RuleActionItem.allocate_mem_detail:type_name -> agent.AllocateVirtualMem
	91,  // 169: agent.V01RuleActionItem.write_mem_detail:type_name -> agent.WriteVirtualMem
	92,  // 170: agent.V01RuleActionItem.read_mem_detail:type_name -> agent.ReadVirtualMem
	93,  // 171: agent.V01RuleActionItem.map_view_section_detail:type_name -> agent.MapViewOfSection
	54,  // 172: agent.V01RuleActionItem.delete_byself:type_name -> agent.NodeFileInfo
	95,  // 173: agent.V01RuleActionItem.open_device_object:type_name -> agent.OpenDeviceObject
	96,  // 174: agent.V01RuleActionItem.create_service:type_name -> agent.CreateServiceAction
	97,  // 175: agent.V01RuleActionItem.start_service:type_name -> agent.StartServiceAction
	98,  // 176: agent.V01RuleActionItem.create_task_scheduler:type_name -> agent.CreateTaskScheduler
	99,  // 177: agent.V01RuleActionItem.start_task_scheduler:type_name -> agent.StartTaskScheduler
	56,  // 178: agent.V01RuleInfoMessage.root_process_unique:type_name -> agent.AcdrProcessUnique
	100, // 179: agent.V01RuleInfoMessage.action_list:type_name -> agent.V01RuleActionItem
	101, // 180: agent.RuleInfoMessages.rule_list:type_name -> agent.V01RuleInfoMessage
	104, // 181: agent.OutreachInfos.Beans:type_name -> agent.OutreachBean
	23,  // 182: agent.OutreachBean.NetInfo:type_name -> agent.NetContentBaseInfo
	56,  // 183: agent.OutreachBean.RootProcessUnique:type_name -> agent.AcdrProcessUnique
	57,  // 184: agent.OutreachBean.CurProcessUnique:type_name -> agent.AcdrProcessSimple
	106, // 185: agent.InternalOutreachInfos.InterOutreach:type_name -> agent.InternalOutreachInfoWithMac
	186, // [186:186] is the sub-list for method output_type
	186, // [186:186] is the sub-list for method input_type
	186, // [186:186] is the sub-list for extension type_name
	186, // [186:186] is the sub-list for extension extendee
	0,   // [0:186] is the sub-list for field type_name
}

func init() { file_agent_acdr_proto_init() }
func file_agent_acdr_proto_init() {
	if File_agent_acdr_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_acdr_proto_msgTypes[33].OneofWrappers = []any{
		(*RuleActionItem_RuleMsg)(nil),
		(*RuleActionItem_FileCreateContent)(nil),
		(*RuleActionItem_FileOpenContent)(nil),
		(*RuleActionItem_FileWriteContent)(nil),
		(*RuleActionItem_FileRenameContent)(nil),
		(*RuleActionItem_FileDeleteContent)(nil),
		(*RuleActionItem_NetConnectContent)(nil),
		(*RuleActionItem_NetAcceptContent)(nil),
		(*RuleActionItem_NetListenContent)(nil),
		(*RuleActionItem_NetSendContent)(nil),
		(*RuleActionItem_NetRecvContent)(nil),
		(*RuleActionItem_FilelessAttackContent)(nil),
		(*RuleActionItem_NetSnifferContent)(nil),
		(*RuleActionItem_EnvHijackContent)(nil),
		(*RuleActionItem_SelfDeleteContent)(nil),
		(*RuleActionItem_PtraceContent)(nil),
		(*RuleActionItem_FileLinkContent)(nil),
		(*RuleActionItem_CallUsermodehelperContent)(nil),
		(*RuleActionItem_ReverseShellContent)(nil),
		(*RuleActionItem_ProcessExecContent)(nil),
		(*RuleActionItem_RestoreUtimeContent)(nil),
		(*RuleActionItem_FileIoctlImmutableContent)(nil),
		(*RuleActionItem_MkdirContent)(nil),
		(*RuleActionItem_FileSymlink)(nil),
		(*RuleActionItem_FileSetUid)(nil),
		(*RuleActionItem_Bpf)(nil),
		(*RuleActionItem_PrivilegeEscalation)(nil),
		(*RuleActionItem_FakeExeFileContent)(nil),
		(*RuleActionItem_HideModuleContent)(nil),
		(*RuleActionItem_KillContent)(nil),
	}
	file_agent_acdr_proto_msgTypes[37].OneofWrappers = []any{
		(*RiskStatus_ProcInfo)(nil),
		(*RiskStatus_NetInfo)(nil),
		(*RiskStatus_FileInfo)(nil),
	}
	file_agent_acdr_proto_msgTypes[89].OneofWrappers = []any{
		(*V01RuleActionItem_ProcessRelationUpdate)(nil),
		(*V01RuleActionItem_ProcessCreate)(nil),
		(*V01RuleActionItem_FileCreate)(nil),
		(*V01RuleActionItem_FileWrite)(nil),
		(*V01RuleActionItem_FileRead)(nil),
		(*V01RuleActionItem_FileDelete)(nil),
		(*V01RuleActionItem_FileRename)(nil),
		(*V01RuleActionItem_NetConnect)(nil),
		(*V01RuleActionItem_NetAccept)(nil),
		(*V01RuleActionItem_NetListen)(nil),
		(*V01RuleActionItem_RegCreate)(nil),
		(*V01RuleActionItem_ScriptHttp)(nil),
		(*V01RuleActionItem_ScriptImageLoad)(nil),
		(*V01RuleActionItem_ScriptRunWmicCode)(nil),
		(*V01RuleActionItem_ScriptScheduleCreate)(nil),
		(*V01RuleActionItem_ScriptGetApiAddr)(nil),
		(*V01RuleActionItem_ScriptWmicWin32Share)(nil),
		(*V01RuleActionItem_ScriptWmicTerminateProcess)(nil),
		(*V01RuleActionItem_ScriptWmicRegOper)(nil),
		(*V01RuleActionItem_ScriptWmicServiceOper)(nil),
		(*V01RuleActionItem_ScriptWmicQuery)(nil),
		(*V01RuleActionItem_ScriptAmsiByAmsiContext)(nil),
		(*V01RuleActionItem_ScriptAmsiDllHijack)(nil),
		(*V01RuleActionItem_ScriptEmail)(nil),
		(*V01RuleActionItem_LogClear)(nil),
		(*V01RuleActionItem_KillProcessDetail)(nil),
		(*V01RuleActionItem_ProcessInjectDetail)(nil),
		(*V01RuleActionItem_RegDelete)(nil),
		(*V01RuleActionItem_RegWrite)(nil),
		(*V01RuleActionItem_RegSetSecurity)(nil),
		(*V01RuleActionItem_FileHide)(nil),
		(*V01RuleActionItem_FileReadonly)(nil),
		(*V01RuleActionItem_LoadRemoteModule)(nil),
		(*V01RuleActionItem_WhiteAddBlack)(nil),
		(*V01RuleActionItem_Cve)(nil),
		(*V01RuleActionItem_RemoteBugOverflow)(nil),
		(*V01RuleActionItem_PuppetProcess)(nil),
		(*V01RuleActionItem_NetConnectDomain)(nil),
		(*V01RuleActionItem_NetSnifferContent)(nil),
		(*V01RuleActionItem_EnvHijackContent)(nil),
		(*V01RuleActionItem_SelfDeleteContent)(nil),
		(*V01RuleActionItem_PtraceContent)(nil),
		(*V01RuleActionItem_FileLinkContent)(nil),
		(*V01RuleActionItem_CallUsermodehelperContent)(nil),
		(*V01RuleActionItem_ReverseShellContent)(nil),
		(*V01RuleActionItem_ProcessExecContent)(nil),
		(*V01RuleActionItem_RestoreUtimeContent)(nil),
		(*V01RuleActionItem_FileIoctlImmutableContent)(nil),
		(*V01RuleActionItem_MkdirContent)(nil),
		(*V01RuleActionItem_FileSymlink)(nil),
		(*V01RuleActionItem_FileSetUid)(nil),
		(*V01RuleActionItem_Bpf)(nil),
		(*V01RuleActionItem_PrivilegeEscalation)(nil),
		(*V01RuleActionItem_FakeExeFileContent)(nil),
		(*V01RuleActionItem_HideModuleContent)(nil),
		(*V01RuleActionItem_KillContent)(nil),
		(*V01RuleActionItem_FileModeChangeContent)(nil),
		(*V01RuleActionItem_ResoureLimit)(nil),
		(*V01RuleActionItem_FilelessAttackContent)(nil),
		(*V01RuleActionItem_FileOpenContent)(nil),
		(*V01RuleActionItem_QueueApcDetail)(nil),
		(*V01RuleActionItem_SetContextThreadDetail)(nil),
		(*V01RuleActionItem_ProtectMemDetail)(nil),
		(*V01RuleActionItem_AllocateMemDetail)(nil),
		(*V01RuleActionItem_WriteMemDetail)(nil),
		(*V01RuleActionItem_ReadMemDetail)(nil),
		(*V01RuleActionItem_MapViewSectionDetail)(nil),
		(*V01RuleActionItem_DeleteByself)(nil),
		(*V01RuleActionItem_OpenDeviceObject)(nil),
		(*V01RuleActionItem_CreateService)(nil),
		(*V01RuleActionItem_StartService)(nil),
		(*V01RuleActionItem_CreateTaskScheduler)(nil),
		(*V01RuleActionItem_StartTaskScheduler)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_acdr_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   96,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_acdr_proto_goTypes,
		DependencyIndexes: file_agent_acdr_proto_depIdxs,
		EnumInfos:         file_agent_acdr_proto_enumTypes,
		MessageInfos:      file_agent_acdr_proto_msgTypes,
	}.Build()
	File_agent_acdr_proto = out.File
	file_agent_acdr_proto_rawDesc = nil
	file_agent_acdr_proto_goTypes = nil
	file_agent_acdr_proto_depIdxs = nil
}
