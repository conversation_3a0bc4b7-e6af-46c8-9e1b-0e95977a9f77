// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/upgrade.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AgentDownloadWay int32

const (
	AgentDownloadWay_ADW_UNKNOWN AgentDownloadWay = 0
	AgentDownloadWay_ADW_HTTP    AgentDownloadWay = 1
	AgentDownloadWay_ADW_SOCKET  AgentDownloadWay = 2
)

// Enum value maps for AgentDownloadWay.
var (
	AgentDownloadWay_name = map[int32]string{
		0: "ADW_UNKNOWN",
		1: "ADW_HTTP",
		2: "ADW_SOCKET",
	}
	AgentDownloadWay_value = map[string]int32{
		"ADW_UNKNOWN": 0,
		"ADW_HTTP":    1,
		"ADW_SOCKET":  2,
	}
)

func (x AgentDownloadWay) Enum() *AgentDownloadWay {
	p := new(AgentDownloadWay)
	*p = x
	return p
}

func (x AgentDownloadWay) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentDownloadWay) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[0].Descriptor()
}

func (AgentDownloadWay) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[0]
}

func (x AgentDownloadWay) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentDownloadWay.Descriptor instead.
func (AgentDownloadWay) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{0}
}

type AgentUpgradeErrorCode int32

const (
	AgentUpgradeErrorCode_AUEC_OK                       AgentUpgradeErrorCode = 0  // 成功
	AgentUpgradeErrorCode_AUEC_DOWNLOAD_FAILED          AgentUpgradeErrorCode = 1  // 下载失败
	AgentUpgradeErrorCode_AUEC_INTEGRITY_CHECK_FAILED   AgentUpgradeErrorCode = 2  // 完整性校验失败
	AgentUpgradeErrorCode_AUEC_UNZIP_FAILED             AgentUpgradeErrorCode = 3  // 解压缩失败
	AgentUpgradeErrorCode_AUEC_FILE_OPEN_FAILED         AgentUpgradeErrorCode = 4  // 下载目标文件文件打开失败
	AgentUpgradeErrorCode_AUEC_UPGRADING                AgentUpgradeErrorCode = 5  // 正在升级
	AgentUpgradeErrorCode_AUEC_VERSION_NOT_NEW          AgentUpgradeErrorCode = 6  // Agent版本更新，不需要更新
	AgentUpgradeErrorCode_AUEC_COPY_UPGRADE_FILE_FAILED AgentUpgradeErrorCode = 7  // 拷贝升级文件到临时文件夹失败
	AgentUpgradeErrorCode_AUEC_OTHER_ERROR              AgentUpgradeErrorCode = 8  // 拷贝升级文件到临时文件夹失败，正常应该用不到
	AgentUpgradeErrorCode_AUEC_CHECK_SIGN_FAILED        AgentUpgradeErrorCode = 9  // 签名校验失败
	AgentUpgradeErrorCode_AUEC_SAME_VERSION             AgentUpgradeErrorCode = 10 // 版本相等
)

// Enum value maps for AgentUpgradeErrorCode.
var (
	AgentUpgradeErrorCode_name = map[int32]string{
		0:  "AUEC_OK",
		1:  "AUEC_DOWNLOAD_FAILED",
		2:  "AUEC_INTEGRITY_CHECK_FAILED",
		3:  "AUEC_UNZIP_FAILED",
		4:  "AUEC_FILE_OPEN_FAILED",
		5:  "AUEC_UPGRADING",
		6:  "AUEC_VERSION_NOT_NEW",
		7:  "AUEC_COPY_UPGRADE_FILE_FAILED",
		8:  "AUEC_OTHER_ERROR",
		9:  "AUEC_CHECK_SIGN_FAILED",
		10: "AUEC_SAME_VERSION",
	}
	AgentUpgradeErrorCode_value = map[string]int32{
		"AUEC_OK":                       0,
		"AUEC_DOWNLOAD_FAILED":          1,
		"AUEC_INTEGRITY_CHECK_FAILED":   2,
		"AUEC_UNZIP_FAILED":             3,
		"AUEC_FILE_OPEN_FAILED":         4,
		"AUEC_UPGRADING":                5,
		"AUEC_VERSION_NOT_NEW":          6,
		"AUEC_COPY_UPGRADE_FILE_FAILED": 7,
		"AUEC_OTHER_ERROR":              8,
		"AUEC_CHECK_SIGN_FAILED":        9,
		"AUEC_SAME_VERSION":             10,
	}
)

func (x AgentUpgradeErrorCode) Enum() *AgentUpgradeErrorCode {
	p := new(AgentUpgradeErrorCode)
	*p = x
	return p
}

func (x AgentUpgradeErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentUpgradeErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[1].Descriptor()
}

func (AgentUpgradeErrorCode) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[1]
}

func (x AgentUpgradeErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentUpgradeErrorCode.Descriptor instead.
func (AgentUpgradeErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{1}
}

type DriverUpgradeErrorCode int32

const (
	DriverUpgradeErrorCode_DUEC_OK                     DriverUpgradeErrorCode = 0  // 成功
	DriverUpgradeErrorCode_DUEC_DOWNLOAD_FAILED        DriverUpgradeErrorCode = 1  // 下载失败
	DriverUpgradeErrorCode_DUEC_INTEGRITY_CHECK_FAILED DriverUpgradeErrorCode = 2  // 完整性校验失败
	DriverUpgradeErrorCode_DUEC_UNZIP_FAILED           DriverUpgradeErrorCode = 3  // 解压缩失败
	DriverUpgradeErrorCode_DUEC_FILE_OPEN_FAILED       DriverUpgradeErrorCode = 4  // 下载目标文件文件打开失败
	DriverUpgradeErrorCode_DUEC_UPGRADING              DriverUpgradeErrorCode = 5  // 正在升级
	DriverUpgradeErrorCode_DUEC_CHECK_SIGN_FAILED      DriverUpgradeErrorCode = 6  // 签名校验失败
	DriverUpgradeErrorCode_DUEC_OTHER_ERROR            DriverUpgradeErrorCode = 7  // 正常应该用不到
	DriverUpgradeErrorCode_DUEC_RENAME_FAILED          DriverUpgradeErrorCode = 8  // 文件重命名失败
	DriverUpgradeErrorCode_DUEC_REMMOD_OLD_FAILED      DriverUpgradeErrorCode = 9  // 卸载旧的驱动失败
	DriverUpgradeErrorCode_DUEC_INSMOD_NEW_FAILED      DriverUpgradeErrorCode = 10 // 安装新的驱动失败
)

// Enum value maps for DriverUpgradeErrorCode.
var (
	DriverUpgradeErrorCode_name = map[int32]string{
		0:  "DUEC_OK",
		1:  "DUEC_DOWNLOAD_FAILED",
		2:  "DUEC_INTEGRITY_CHECK_FAILED",
		3:  "DUEC_UNZIP_FAILED",
		4:  "DUEC_FILE_OPEN_FAILED",
		5:  "DUEC_UPGRADING",
		6:  "DUEC_CHECK_SIGN_FAILED",
		7:  "DUEC_OTHER_ERROR",
		8:  "DUEC_RENAME_FAILED",
		9:  "DUEC_REMMOD_OLD_FAILED",
		10: "DUEC_INSMOD_NEW_FAILED",
	}
	DriverUpgradeErrorCode_value = map[string]int32{
		"DUEC_OK":                     0,
		"DUEC_DOWNLOAD_FAILED":        1,
		"DUEC_INTEGRITY_CHECK_FAILED": 2,
		"DUEC_UNZIP_FAILED":           3,
		"DUEC_FILE_OPEN_FAILED":       4,
		"DUEC_UPGRADING":              5,
		"DUEC_CHECK_SIGN_FAILED":      6,
		"DUEC_OTHER_ERROR":            7,
		"DUEC_RENAME_FAILED":          8,
		"DUEC_REMMOD_OLD_FAILED":      9,
		"DUEC_INSMOD_NEW_FAILED":      10,
	}
)

func (x DriverUpgradeErrorCode) Enum() *DriverUpgradeErrorCode {
	p := new(DriverUpgradeErrorCode)
	*p = x
	return p
}

func (x DriverUpgradeErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DriverUpgradeErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[2].Descriptor()
}

func (DriverUpgradeErrorCode) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[2]
}

func (x DriverUpgradeErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DriverUpgradeErrorCode.Descriptor instead.
func (DriverUpgradeErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{2}
}

type HashEngineFileUpgradeErrorCode int32

const (
	HashEngineFileUpgradeErrorCode_HEFUEC_OK                     HashEngineFileUpgradeErrorCode = 0 // 成功
	HashEngineFileUpgradeErrorCode_HEFUEC_DOWNLOAD_FAILED        HashEngineFileUpgradeErrorCode = 1 // 下载失败
	HashEngineFileUpgradeErrorCode_HEFUEC_INTEGRITY_CHECK_FAILED HashEngineFileUpgradeErrorCode = 2 // 完整性校验失败
	HashEngineFileUpgradeErrorCode_HEFUEC_UNZIP_FAILED           HashEngineFileUpgradeErrorCode = 3 // 解压缩失败
	HashEngineFileUpgradeErrorCode_HEFUEC_FILE_OPEN_FAILED       HashEngineFileUpgradeErrorCode = 4 // 下载目标文件文件打开失败
	HashEngineFileUpgradeErrorCode_HEFUEC_UPGRADING              HashEngineFileUpgradeErrorCode = 5 // 正在升级
	HashEngineFileUpgradeErrorCode_HEFUEC_CHECK_SIGN_FAILED      HashEngineFileUpgradeErrorCode = 6 // 签名校验失败
	HashEngineFileUpgradeErrorCode_HEFUEC_OTHER_ERROR            HashEngineFileUpgradeErrorCode = 7 // 正常应该用不到
	HashEngineFileUpgradeErrorCode_HEFUEC_VERSION_MISMATCH       HashEngineFileUpgradeErrorCode = 8 // 版本不匹配
)

// Enum value maps for HashEngineFileUpgradeErrorCode.
var (
	HashEngineFileUpgradeErrorCode_name = map[int32]string{
		0: "HEFUEC_OK",
		1: "HEFUEC_DOWNLOAD_FAILED",
		2: "HEFUEC_INTEGRITY_CHECK_FAILED",
		3: "HEFUEC_UNZIP_FAILED",
		4: "HEFUEC_FILE_OPEN_FAILED",
		5: "HEFUEC_UPGRADING",
		6: "HEFUEC_CHECK_SIGN_FAILED",
		7: "HEFUEC_OTHER_ERROR",
		8: "HEFUEC_VERSION_MISMATCH",
	}
	HashEngineFileUpgradeErrorCode_value = map[string]int32{
		"HEFUEC_OK":                     0,
		"HEFUEC_DOWNLOAD_FAILED":        1,
		"HEFUEC_INTEGRITY_CHECK_FAILED": 2,
		"HEFUEC_UNZIP_FAILED":           3,
		"HEFUEC_FILE_OPEN_FAILED":       4,
		"HEFUEC_UPGRADING":              5,
		"HEFUEC_CHECK_SIGN_FAILED":      6,
		"HEFUEC_OTHER_ERROR":            7,
		"HEFUEC_VERSION_MISMATCH":       8,
	}
)

func (x HashEngineFileUpgradeErrorCode) Enum() *HashEngineFileUpgradeErrorCode {
	p := new(HashEngineFileUpgradeErrorCode)
	*p = x
	return p
}

func (x HashEngineFileUpgradeErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HashEngineFileUpgradeErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[3].Descriptor()
}

func (HashEngineFileUpgradeErrorCode) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[3]
}

func (x HashEngineFileUpgradeErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HashEngineFileUpgradeErrorCode.Descriptor instead.
func (HashEngineFileUpgradeErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{3}
}

// 升级类别
type UpgradeKind int32

const (
	UpgradeKind_UK_UNDEFINED         UpgradeKind = 0
	UpgradeKind_UK_AGENT             UpgradeKind = 1
	UpgradeKind_UK_DRIVER            UpgradeKind = 2
	UpgradeKind_UK_FILE_BLACKLIB     UpgradeKind = 3
	UpgradeKind_UK_FILE_WHITELIB     UpgradeKind = 4
	UpgradeKind_UK_DOMAIN_WHITELIB   UpgradeKind = 5
	UpgradeKind_UK_FILESIGN_WHITELIB UpgradeKind = 6
	UpgradeKind_UK_DRIVER_BLACKLIB   UpgradeKind = 7
	UpgradeKind_UK_ACDR_LIB          UpgradeKind = 8
	UpgradeKind_UK_ATTACK_WHITELIB   UpgradeKind = 9
	UpgradeKind_UK_FILESIGN_BLACKLIB UpgradeKind = 10
	UpgradeKind_UK_SL_FILELib        UpgradeKind = 11
)

// Enum value maps for UpgradeKind.
var (
	UpgradeKind_name = map[int32]string{
		0:  "UK_UNDEFINED",
		1:  "UK_AGENT",
		2:  "UK_DRIVER",
		3:  "UK_FILE_BLACKLIB",
		4:  "UK_FILE_WHITELIB",
		5:  "UK_DOMAIN_WHITELIB",
		6:  "UK_FILESIGN_WHITELIB",
		7:  "UK_DRIVER_BLACKLIB",
		8:  "UK_ACDR_LIB",
		9:  "UK_ATTACK_WHITELIB",
		10: "UK_FILESIGN_BLACKLIB",
		11: "UK_SL_FILELib",
	}
	UpgradeKind_value = map[string]int32{
		"UK_UNDEFINED":         0,
		"UK_AGENT":             1,
		"UK_DRIVER":            2,
		"UK_FILE_BLACKLIB":     3,
		"UK_FILE_WHITELIB":     4,
		"UK_DOMAIN_WHITELIB":   5,
		"UK_FILESIGN_WHITELIB": 6,
		"UK_DRIVER_BLACKLIB":   7,
		"UK_ACDR_LIB":          8,
		"UK_ATTACK_WHITELIB":   9,
		"UK_FILESIGN_BLACKLIB": 10,
		"UK_SL_FILELib":        11,
	}
)

func (x UpgradeKind) Enum() *UpgradeKind {
	p := new(UpgradeKind)
	*p = x
	return p
}

func (x UpgradeKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeKind) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[4].Descriptor()
}

func (UpgradeKind) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[4]
}

func (x UpgradeKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeKind.Descriptor instead.
func (UpgradeKind) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{4}
}

type UpgradeNotifyResponeCode int32

const (
	UpgradeNotifyResponeCode_UNRC_OK           UpgradeNotifyResponeCode = 0
	UpgradeNotifyResponeCode_UNRC_SAME_VERSION UpgradeNotifyResponeCode = 1 // 版本相等
	UpgradeNotifyResponeCode_UNRC_NOT_SUPPORT  UpgradeNotifyResponeCode = 2 // 不支持
	UpgradeNotifyResponeCode_UNRC_OTHER_ERROR  UpgradeNotifyResponeCode = 3 // 其它错误
)

// Enum value maps for UpgradeNotifyResponeCode.
var (
	UpgradeNotifyResponeCode_name = map[int32]string{
		0: "UNRC_OK",
		1: "UNRC_SAME_VERSION",
		2: "UNRC_NOT_SUPPORT",
		3: "UNRC_OTHER_ERROR",
	}
	UpgradeNotifyResponeCode_value = map[string]int32{
		"UNRC_OK":           0,
		"UNRC_SAME_VERSION": 1,
		"UNRC_NOT_SUPPORT":  2,
		"UNRC_OTHER_ERROR":  3,
	}
)

func (x UpgradeNotifyResponeCode) Enum() *UpgradeNotifyResponeCode {
	p := new(UpgradeNotifyResponeCode)
	*p = x
	return p
}

func (x UpgradeNotifyResponeCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeNotifyResponeCode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_upgrade_proto_enumTypes[5].Descriptor()
}

func (UpgradeNotifyResponeCode) Type() protoreflect.EnumType {
	return &file_agent_upgrade_proto_enumTypes[5]
}

func (x UpgradeNotifyResponeCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeNotifyResponeCode.Descriptor instead.
func (UpgradeNotifyResponeCode) EnumDescriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{5}
}

type ServerPushMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseDownloadInfo *BaseDownloadInfo `protobuf:"bytes,1,opt,name=baseDownloadInfo,proto3" json:"baseDownloadInfo,omitempty"` // agent 文件下载基础信息
	// Types that are assignable to HasDriverUpgradeMessage:
	//
	//	*ServerPushMessage_DriverUpgradeMessage
	HasDriverUpgradeMessage isServerPushMessage_HasDriverUpgradeMessage `protobuf_oneof:"has_driverUpgradeMessage"`
	// Types that are assignable to HasHashenginefile:
	//
	//	*ServerPushMessage_HashEngineFile
	HasHashenginefile isServerPushMessage_HasHashenginefile `protobuf_oneof:"has_hashenginefile"`
	// Types that are assignable to HasSha256Enginefile:
	//
	//	*ServerPushMessage_Sha256EngineFile
	HasSha256Enginefile isServerPushMessage_HasSha256Enginefile `protobuf_oneof:"has_sha256enginefile"`
	// Types that are assignable to HasNgavfile:
	//
	//	*ServerPushMessage_NgavFile
	HasNgavfile isServerPushMessage_HasNgavfile `protobuf_oneof:"has_ngavfile"`
	// Types that are assignable to HasRaspfile:
	//
	//	*ServerPushMessage_RaspFile
	HasRaspfile isServerPushMessage_HasRaspfile `protobuf_oneof:"has_raspfile"`
	// Types that are assignable to HasBaselinefile:
	//
	//	*ServerPushMessage_BaselineFile
	HasBaselinefile isServerPushMessage_HasBaselinefile `protobuf_oneof:"has_baselinefile"`
	// Types that are assignable to HasDomainwhitefile:
	//
	//	*ServerPushMessage_DomainwhiteFile
	HasDomainwhitefile isServerPushMessage_HasDomainwhitefile `protobuf_oneof:"has_domainwhitefile"`
	// Types that are assignable to HasFilesigncomwhitefile:
	//
	//	*ServerPushMessage_FilesigncomwhiteFile
	HasFilesigncomwhitefile isServerPushMessage_HasFilesigncomwhitefile `protobuf_oneof:"has_filesigncomwhitefile"`
	// Types that are assignable to HasDriverblacklistfile:
	//
	//	*ServerPushMessage_DriverblacklistFile
	HasDriverblacklistfile isServerPushMessage_HasDriverblacklistfile `protobuf_oneof:"has_driverblacklistfile"`
	// Types that are assignable to HasAcdrlibfile:
	//
	//	*ServerPushMessage_AcdrlibFile
	HasAcdrlibfile isServerPushMessage_HasAcdrlibfile `protobuf_oneof:"has_acdrlibfile"`
	// Types that are assignable to HasAttackwhitefile:
	//
	//	*ServerPushMessage_AttackwhitelibFile
	HasAttackwhitefile isServerPushMessage_HasAttackwhitefile `protobuf_oneof:"has_attackwhitefile"`
	// Types that are assignable to HasSignblacklibfile:
	//
	//	*ServerPushMessage_SignblacklibFile
	HasSignblacklibfile isServerPushMessage_HasSignblacklibfile `protobuf_oneof:"has_signblacklibfile"`
	// Types that are assignable to HasSllibfile:
	//
	//	*ServerPushMessage_SllibFile
	HasSllibfile isServerPushMessage_HasSllibfile `protobuf_oneof:"has_sllibfile"`
}

func (x *ServerPushMessage) Reset() {
	*x = ServerPushMessage{}
	mi := &file_agent_upgrade_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerPushMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerPushMessage) ProtoMessage() {}

func (x *ServerPushMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerPushMessage.ProtoReflect.Descriptor instead.
func (*ServerPushMessage) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{0}
}

func (x *ServerPushMessage) GetBaseDownloadInfo() *BaseDownloadInfo {
	if x != nil {
		return x.BaseDownloadInfo
	}
	return nil
}

func (m *ServerPushMessage) GetHasDriverUpgradeMessage() isServerPushMessage_HasDriverUpgradeMessage {
	if m != nil {
		return m.HasDriverUpgradeMessage
	}
	return nil
}

func (x *ServerPushMessage) GetDriverUpgradeMessage() *DriverUpgradeMessage {
	if x, ok := x.GetHasDriverUpgradeMessage().(*ServerPushMessage_DriverUpgradeMessage); ok {
		return x.DriverUpgradeMessage
	}
	return nil
}

func (m *ServerPushMessage) GetHasHashenginefile() isServerPushMessage_HasHashenginefile {
	if m != nil {
		return m.HasHashenginefile
	}
	return nil
}

func (x *ServerPushMessage) GetHashEngineFile() *HashEngineFile {
	if x, ok := x.GetHasHashenginefile().(*ServerPushMessage_HashEngineFile); ok {
		return x.HashEngineFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasSha256Enginefile() isServerPushMessage_HasSha256Enginefile {
	if m != nil {
		return m.HasSha256Enginefile
	}
	return nil
}

func (x *ServerPushMessage) GetSha256EngineFile() *HashEngineFile {
	if x, ok := x.GetHasSha256Enginefile().(*ServerPushMessage_Sha256EngineFile); ok {
		return x.Sha256EngineFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasNgavfile() isServerPushMessage_HasNgavfile {
	if m != nil {
		return m.HasNgavfile
	}
	return nil
}

func (x *ServerPushMessage) GetNgavFile() *NgavFile {
	if x, ok := x.GetHasNgavfile().(*ServerPushMessage_NgavFile); ok {
		return x.NgavFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasRaspfile() isServerPushMessage_HasRaspfile {
	if m != nil {
		return m.HasRaspfile
	}
	return nil
}

func (x *ServerPushMessage) GetRaspFile() *RaspFile {
	if x, ok := x.GetHasRaspfile().(*ServerPushMessage_RaspFile); ok {
		return x.RaspFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasBaselinefile() isServerPushMessage_HasBaselinefile {
	if m != nil {
		return m.HasBaselinefile
	}
	return nil
}

func (x *ServerPushMessage) GetBaselineFile() *BaselineFile {
	if x, ok := x.GetHasBaselinefile().(*ServerPushMessage_BaselineFile); ok {
		return x.BaselineFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasDomainwhitefile() isServerPushMessage_HasDomainwhitefile {
	if m != nil {
		return m.HasDomainwhitefile
	}
	return nil
}

func (x *ServerPushMessage) GetDomainwhiteFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasDomainwhitefile().(*ServerPushMessage_DomainwhiteFile); ok {
		return x.DomainwhiteFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasFilesigncomwhitefile() isServerPushMessage_HasFilesigncomwhitefile {
	if m != nil {
		return m.HasFilesigncomwhitefile
	}
	return nil
}

func (x *ServerPushMessage) GetFilesigncomwhiteFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasFilesigncomwhitefile().(*ServerPushMessage_FilesigncomwhiteFile); ok {
		return x.FilesigncomwhiteFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasDriverblacklistfile() isServerPushMessage_HasDriverblacklistfile {
	if m != nil {
		return m.HasDriverblacklistfile
	}
	return nil
}

func (x *ServerPushMessage) GetDriverblacklistFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasDriverblacklistfile().(*ServerPushMessage_DriverblacklistFile); ok {
		return x.DriverblacklistFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasAcdrlibfile() isServerPushMessage_HasAcdrlibfile {
	if m != nil {
		return m.HasAcdrlibfile
	}
	return nil
}

func (x *ServerPushMessage) GetAcdrlibFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasAcdrlibfile().(*ServerPushMessage_AcdrlibFile); ok {
		return x.AcdrlibFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasAttackwhitefile() isServerPushMessage_HasAttackwhitefile {
	if m != nil {
		return m.HasAttackwhitefile
	}
	return nil
}

func (x *ServerPushMessage) GetAttackwhitelibFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasAttackwhitefile().(*ServerPushMessage_AttackwhitelibFile); ok {
		return x.AttackwhitelibFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasSignblacklibfile() isServerPushMessage_HasSignblacklibfile {
	if m != nil {
		return m.HasSignblacklibfile
	}
	return nil
}

func (x *ServerPushMessage) GetSignblacklibFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasSignblacklibfile().(*ServerPushMessage_SignblacklibFile); ok {
		return x.SignblacklibFile
	}
	return nil
}

func (m *ServerPushMessage) GetHasSllibfile() isServerPushMessage_HasSllibfile {
	if m != nil {
		return m.HasSllibfile
	}
	return nil
}

func (x *ServerPushMessage) GetSllibFile() *AgentEngineLibVersion {
	if x, ok := x.GetHasSllibfile().(*ServerPushMessage_SllibFile); ok {
		return x.SllibFile
	}
	return nil
}

type isServerPushMessage_HasDriverUpgradeMessage interface {
	isServerPushMessage_HasDriverUpgradeMessage()
}

type ServerPushMessage_DriverUpgradeMessage struct {
	DriverUpgradeMessage *DriverUpgradeMessage `protobuf:"bytes,2,opt,name=driverUpgradeMessage,proto3,oneof"`
}

func (*ServerPushMessage_DriverUpgradeMessage) isServerPushMessage_HasDriverUpgradeMessage() {}

type isServerPushMessage_HasHashenginefile interface {
	isServerPushMessage_HasHashenginefile()
}

type ServerPushMessage_HashEngineFile struct {
	HashEngineFile *HashEngineFile `protobuf:"bytes,3,opt,name=hash_engine_file,json=hashEngineFile,proto3,oneof"`
}

func (*ServerPushMessage_HashEngineFile) isServerPushMessage_HasHashenginefile() {}

type isServerPushMessage_HasSha256Enginefile interface {
	isServerPushMessage_HasSha256Enginefile()
}

type ServerPushMessage_Sha256EngineFile struct {
	Sha256EngineFile *HashEngineFile `protobuf:"bytes,4,opt,name=sha256_engine_file,json=sha256EngineFile,proto3,oneof"`
}

func (*ServerPushMessage_Sha256EngineFile) isServerPushMessage_HasSha256Enginefile() {}

type isServerPushMessage_HasNgavfile interface {
	isServerPushMessage_HasNgavfile()
}

type ServerPushMessage_NgavFile struct {
	NgavFile *NgavFile `protobuf:"bytes,5,opt,name=ngav_file,json=ngavFile,proto3,oneof"`
}

func (*ServerPushMessage_NgavFile) isServerPushMessage_HasNgavfile() {}

type isServerPushMessage_HasRaspfile interface {
	isServerPushMessage_HasRaspfile()
}

type ServerPushMessage_RaspFile struct {
	RaspFile *RaspFile `protobuf:"bytes,6,opt,name=rasp_file,json=raspFile,proto3,oneof"`
}

func (*ServerPushMessage_RaspFile) isServerPushMessage_HasRaspfile() {}

type isServerPushMessage_HasBaselinefile interface {
	isServerPushMessage_HasBaselinefile()
}

type ServerPushMessage_BaselineFile struct {
	BaselineFile *BaselineFile `protobuf:"bytes,7,opt,name=baseline_file,json=baselineFile,proto3,oneof"`
}

func (*ServerPushMessage_BaselineFile) isServerPushMessage_HasBaselinefile() {}

type isServerPushMessage_HasDomainwhitefile interface {
	isServerPushMessage_HasDomainwhitefile()
}

type ServerPushMessage_DomainwhiteFile struct {
	DomainwhiteFile *AgentEngineLibVersion `protobuf:"bytes,8,opt,name=domainwhite_file,json=domainwhiteFile,proto3,oneof"`
}

func (*ServerPushMessage_DomainwhiteFile) isServerPushMessage_HasDomainwhitefile() {}

type isServerPushMessage_HasFilesigncomwhitefile interface {
	isServerPushMessage_HasFilesigncomwhitefile()
}

type ServerPushMessage_FilesigncomwhiteFile struct {
	FilesigncomwhiteFile *AgentEngineLibVersion `protobuf:"bytes,9,opt,name=filesigncomwhite_file,json=filesigncomwhiteFile,proto3,oneof"`
}

func (*ServerPushMessage_FilesigncomwhiteFile) isServerPushMessage_HasFilesigncomwhitefile() {}

type isServerPushMessage_HasDriverblacklistfile interface {
	isServerPushMessage_HasDriverblacklistfile()
}

type ServerPushMessage_DriverblacklistFile struct {
	DriverblacklistFile *AgentEngineLibVersion `protobuf:"bytes,10,opt,name=driverblacklist_file,json=driverblacklistFile,proto3,oneof"`
}

func (*ServerPushMessage_DriverblacklistFile) isServerPushMessage_HasDriverblacklistfile() {}

type isServerPushMessage_HasAcdrlibfile interface {
	isServerPushMessage_HasAcdrlibfile()
}

type ServerPushMessage_AcdrlibFile struct {
	AcdrlibFile *AgentEngineLibVersion `protobuf:"bytes,11,opt,name=acdrlib_file,json=acdrlibFile,proto3,oneof"`
}

func (*ServerPushMessage_AcdrlibFile) isServerPushMessage_HasAcdrlibfile() {}

type isServerPushMessage_HasAttackwhitefile interface {
	isServerPushMessage_HasAttackwhitefile()
}

type ServerPushMessage_AttackwhitelibFile struct {
	AttackwhitelibFile *AgentEngineLibVersion `protobuf:"bytes,12,opt,name=attackwhitelib_file,json=attackwhitelibFile,proto3,oneof"`
}

func (*ServerPushMessage_AttackwhitelibFile) isServerPushMessage_HasAttackwhitefile() {}

type isServerPushMessage_HasSignblacklibfile interface {
	isServerPushMessage_HasSignblacklibfile()
}

type ServerPushMessage_SignblacklibFile struct {
	SignblacklibFile *AgentEngineLibVersion `protobuf:"bytes,13,opt,name=signblacklib_file,json=signblacklibFile,proto3,oneof"`
}

func (*ServerPushMessage_SignblacklibFile) isServerPushMessage_HasSignblacklibfile() {}

type isServerPushMessage_HasSllibfile interface {
	isServerPushMessage_HasSllibfile()
}

type ServerPushMessage_SllibFile struct {
	SllibFile *AgentEngineLibVersion `protobuf:"bytes,14,opt,name=sllib_file,json=sllibFile,proto3,oneof"`
}

func (*ServerPushMessage_SllibFile) isServerPushMessage_HasSllibfile() {}

type BaseDownloadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PkgUrl   []byte           `protobuf:"bytes,1,opt,name=pkgUrl,proto3" json:"pkgUrl,omitempty"`                        // 下载地址
	Way      AgentDownloadWay `protobuf:"varint,2,opt,name=way,proto3,enum=agent.AgentDownloadWay" json:"way,omitempty"` // 下载方式
	FileInfo *FileInfo        `protobuf:"bytes,3,opt,name=fileInfo,proto3" json:"fileInfo,omitempty"`                    // 当way = ADW_SOCKET 有效
}

func (x *BaseDownloadInfo) Reset() {
	*x = BaseDownloadInfo{}
	mi := &file_agent_upgrade_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseDownloadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseDownloadInfo) ProtoMessage() {}

func (x *BaseDownloadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseDownloadInfo.ProtoReflect.Descriptor instead.
func (*BaseDownloadInfo) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{1}
}

func (x *BaseDownloadInfo) GetPkgUrl() []byte {
	if x != nil {
		return x.PkgUrl
	}
	return nil
}

func (x *BaseDownloadInfo) GetWay() AgentDownloadWay {
	if x != nil {
		return x.Way
	}
	return AgentDownloadWay_ADW_UNKNOWN
}

func (x *BaseDownloadInfo) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type RequestFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrigName      []byte `protobuf:"bytes,1,opt,name=origName,proto3" json:"origName,omitempty"`            // 文件名
	FileBlockSize int32  `protobuf:"varint,2,opt,name=fileBlockSize,proto3" json:"fileBlockSize,omitempty"` // 每块的大小
	FileNum       int32  `protobuf:"varint,3,opt,name=fileNum,proto3" json:"fileNum,omitempty"`             // 从0开始，序号
	RelativePath  []byte `protobuf:"bytes,4,opt,name=relativePath,proto3" json:"relativePath,omitempty"`    // 服务端真实的路径（加密后）
}

func (x *RequestFile) Reset() {
	*x = RequestFile{}
	mi := &file_agent_upgrade_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestFile) ProtoMessage() {}

func (x *RequestFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestFile.ProtoReflect.Descriptor instead.
func (*RequestFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{2}
}

func (x *RequestFile) GetOrigName() []byte {
	if x != nil {
		return x.OrigName
	}
	return nil
}

func (x *RequestFile) GetFileBlockSize() int32 {
	if x != nil {
		return x.FileBlockSize
	}
	return 0
}

func (x *RequestFile) GetFileNum() int32 {
	if x != nil {
		return x.FileNum
	}
	return 0
}

func (x *RequestFile) GetRelativePath() []byte {
	if x != nil {
		return x.RelativePath
	}
	return nil
}

type ResponseFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrigName      []byte `protobuf:"bytes,1,opt,name=origName,proto3" json:"origName,omitempty"`
	FileBlockSize int32  `protobuf:"varint,2,opt,name=fileBlockSize,proto3" json:"fileBlockSize,omitempty"`
	FileNum       int32  `protobuf:"varint,3,opt,name=fileNum,proto3" json:"fileNum,omitempty"`
	FileContent   []byte `protobuf:"bytes,4,opt,name=fileContent,proto3" json:"fileContent,omitempty"` // 文件块的内容
}

func (x *ResponseFile) Reset() {
	*x = ResponseFile{}
	mi := &file_agent_upgrade_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseFile) ProtoMessage() {}

func (x *ResponseFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseFile.ProtoReflect.Descriptor instead.
func (*ResponseFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{3}
}

func (x *ResponseFile) GetOrigName() []byte {
	if x != nil {
		return x.OrigName
	}
	return nil
}

func (x *ResponseFile) GetFileBlockSize() int32 {
	if x != nil {
		return x.FileBlockSize
	}
	return 0
}

func (x *ResponseFile) GetFileNum() int32 {
	if x != nil {
		return x.FileNum
	}
	return 0
}

func (x *ResponseFile) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

type UploadFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Modname     string `protobuf:"bytes,1,opt,name=modname,proto3" json:"modname,omitempty"`
	TotalBlock  int32  `protobuf:"varint,2,opt,name=totalBlock,proto3" json:"totalBlock,omitempty"`
	FileNum     int32  `protobuf:"varint,3,opt,name=fileNum,proto3" json:"fileNum,omitempty"`
	FileContent []byte `protobuf:"bytes,4,opt,name=fileContent,proto3" json:"fileContent,omitempty"` // 文件块的内容
}

func (x *UploadFile) Reset() {
	*x = UploadFile{}
	mi := &file_agent_upgrade_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFile) ProtoMessage() {}

func (x *UploadFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFile.ProtoReflect.Descriptor instead.
func (*UploadFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{4}
}

func (x *UploadFile) GetModname() string {
	if x != nil {
		return x.Modname
	}
	return ""
}

func (x *UploadFile) GetTotalBlock() int32 {
	if x != nil {
		return x.TotalBlock
	}
	return 0
}

func (x *UploadFile) GetFileNum() int32 {
	if x != nil {
		return x.FileNum
	}
	return 0
}

func (x *UploadFile) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrigName     []byte `protobuf:"bytes,1,opt,name=origName,proto3" json:"origName,omitempty"`
	FileSize     int32  `protobuf:"varint,2,opt,name=fileSize,proto3" json:"fileSize,omitempty"`
	FileMd5      []byte `protobuf:"bytes,3,opt,name=fileMd5,proto3" json:"fileMd5,omitempty"`
	RelativePath []byte `protobuf:"bytes,4,opt,name=relativePath,proto3" json:"relativePath,omitempty"` // 服务端绝对路径(加密后）,只有服务端会用
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_agent_upgrade_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{5}
}

func (x *FileInfo) GetOrigName() []byte {
	if x != nil {
		return x.OrigName
	}
	return nil
}

func (x *FileInfo) GetFileSize() int32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileInfo) GetFileMd5() []byte {
	if x != nil {
		return x.FileMd5
	}
	return nil
}

func (x *FileInfo) GetRelativePath() []byte {
	if x != nil {
		return x.RelativePath
	}
	return nil
}

// 服务器推送升级消息
type AgentUpgradeMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VersionName []byte           `protobuf:"bytes,1,opt,name=versionName,proto3" json:"versionName,omitempty"`              // 版本名称
	VersionCode []byte           `protobuf:"bytes,2,opt,name=versionCode,proto3" json:"versionCode,omitempty"`              // 版本代码，检查新版本
	ArchInfo    []byte           `protobuf:"bytes,3,opt,name=archInfo,proto3" json:"archInfo,omitempty"`                    // 系统支持信息（如x86_linux64）
	PkgMd5      []byte           `protobuf:"bytes,4,opt,name=pkgMd5,proto3" json:"pkgMd5,omitempty"`                        // 安装包MD5，下载完成后验证
	PkgUrl      []byte           `protobuf:"bytes,5,opt,name=pkgUrl,proto3" json:"pkgUrl,omitempty"`                        // 下载地址
	Flag        []byte           `protobuf:"bytes,6,opt,name=flag,proto3" json:"flag,omitempty"`                            // flag升级紧急程度（先保留）
	Way         AgentDownloadWay `protobuf:"varint,7,opt,name=way,proto3,enum=agent.AgentDownloadWay" json:"way,omitempty"` // 下载方式
	FileInfo    *FileInfo        `protobuf:"bytes,8,opt,name=fileInfo,proto3" json:"fileInfo,omitempty"`                    // 当way = ADW_SOCKET 有效
}

func (x *AgentUpgradeMsg) Reset() {
	*x = AgentUpgradeMsg{}
	mi := &file_agent_upgrade_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentUpgradeMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentUpgradeMsg) ProtoMessage() {}

func (x *AgentUpgradeMsg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentUpgradeMsg.ProtoReflect.Descriptor instead.
func (*AgentUpgradeMsg) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{6}
}

func (x *AgentUpgradeMsg) GetVersionName() []byte {
	if x != nil {
		return x.VersionName
	}
	return nil
}

func (x *AgentUpgradeMsg) GetVersionCode() []byte {
	if x != nil {
		return x.VersionCode
	}
	return nil
}

func (x *AgentUpgradeMsg) GetArchInfo() []byte {
	if x != nil {
		return x.ArchInfo
	}
	return nil
}

func (x *AgentUpgradeMsg) GetPkgMd5() []byte {
	if x != nil {
		return x.PkgMd5
	}
	return nil
}

func (x *AgentUpgradeMsg) GetPkgUrl() []byte {
	if x != nil {
		return x.PkgUrl
	}
	return nil
}

func (x *AgentUpgradeMsg) GetFlag() []byte {
	if x != nil {
		return x.Flag
	}
	return nil
}

func (x *AgentUpgradeMsg) GetWay() AgentDownloadWay {
	if x != nil {
		return x.Way
	}
	return AgentDownloadWay_ADW_UNKNOWN
}

func (x *AgentUpgradeMsg) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type AgentUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo    *ClientID             `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`                                     // 主机信息标识
	VersionName []byte                `protobuf:"bytes,2,opt,name=versionName,proto3" json:"versionName,omitempty"`                               // 版本名称
	VersionCode []byte                `protobuf:"bytes,3,opt,name=versionCode,proto3" json:"versionCode,omitempty"`                               // 版本代码，检查新版本
	ErrorCode   AgentUpgradeErrorCode `protobuf:"varint,4,opt,name=errorCode,proto3,enum=agent.AgentUpgradeErrorCode" json:"errorCode,omitempty"` // 错误代码
}

func (x *AgentUpgradeResult) Reset() {
	*x = AgentUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentUpgradeResult) ProtoMessage() {}

func (x *AgentUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentUpgradeResult.ProtoReflect.Descriptor instead.
func (*AgentUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{7}
}

func (x *AgentUpgradeResult) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *AgentUpgradeResult) GetVersionName() []byte {
	if x != nil {
		return x.VersionName
	}
	return nil
}

func (x *AgentUpgradeResult) GetVersionCode() []byte {
	if x != nil {
		return x.VersionCode
	}
	return nil
}

func (x *AgentUpgradeResult) GetErrorCode() AgentUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return AgentUpgradeErrorCode_AUEC_OK
}

type DriverUpgradeMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version uint32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"` // 保留字段
}

func (x *DriverUpgradeMessage) Reset() {
	*x = DriverUpgradeMessage{}
	mi := &file_agent_upgrade_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DriverUpgradeMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverUpgradeMessage) ProtoMessage() {}

func (x *DriverUpgradeMessage) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverUpgradeMessage.ProtoReflect.Descriptor instead.
func (*DriverUpgradeMessage) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{8}
}

func (x *DriverUpgradeMessage) GetVersion() uint32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type HashEngineFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *HashEngineFile) Reset() {
	*x = HashEngineFile{}
	mi := &file_agent_upgrade_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HashEngineFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashEngineFile) ProtoMessage() {}

func (x *HashEngineFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashEngineFile.ProtoReflect.Descriptor instead.
func (*HashEngineFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{9}
}

func (x *HashEngineFile) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type NgavFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *NgavFile) Reset() {
	*x = NgavFile{}
	mi := &file_agent_upgrade_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NgavFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NgavFile) ProtoMessage() {}

func (x *NgavFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NgavFile.ProtoReflect.Descriptor instead.
func (*NgavFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{10}
}

func (x *NgavFile) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type RaspFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *RaspFile) Reset() {
	*x = RaspFile{}
	mi := &file_agent_upgrade_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RaspFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RaspFile) ProtoMessage() {}

func (x *RaspFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RaspFile.ProtoReflect.Descriptor instead.
func (*RaspFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{11}
}

func (x *RaspFile) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type BaselineFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *BaselineFile) Reset() {
	*x = BaselineFile{}
	mi := &file_agent_upgrade_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaselineFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaselineFile) ProtoMessage() {}

func (x *BaselineFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaselineFile.ProtoReflect.Descriptor instead.
func (*BaselineFile) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{12}
}

func (x *BaselineFile) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type LinuxAgentOSInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DriverVersion    uint32 `protobuf:"varint,1,opt,name=driver_version,json=driverVersion,proto3" json:"driver_version,omitempty"` // 本地的驱动版本
	AgentVersion     string `protobuf:"bytes,2,opt,name=agent_version,json=agentVersion,proto3" json:"agent_version,omitempty"`     // agent 版本
	LinuxOSInfoLong  string `protobuf:"bytes,3,opt,name=linuxOSInfoLong,proto3" json:"linuxOSInfoLong,omitempty"`                   // linux 系统信息长串，仅linux
	LinuxOSInfoShort string `protobuf:"bytes,4,opt,name=linuxOSInfoShort,proto3" json:"linuxOSInfoShort,omitempty"`                 // linux 系统信息短串，仅linux
}

func (x *LinuxAgentOSInfo) Reset() {
	*x = LinuxAgentOSInfo{}
	mi := &file_agent_upgrade_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LinuxAgentOSInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LinuxAgentOSInfo) ProtoMessage() {}

func (x *LinuxAgentOSInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LinuxAgentOSInfo.ProtoReflect.Descriptor instead.
func (*LinuxAgentOSInfo) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{13}
}

func (x *LinuxAgentOSInfo) GetDriverVersion() uint32 {
	if x != nil {
		return x.DriverVersion
	}
	return 0
}

func (x *LinuxAgentOSInfo) GetAgentVersion() string {
	if x != nil {
		return x.AgentVersion
	}
	return ""
}

func (x *LinuxAgentOSInfo) GetLinuxOSInfoLong() string {
	if x != nil {
		return x.LinuxOSInfoLong
	}
	return ""
}

func (x *LinuxAgentOSInfo) GetLinuxOSInfoShort() string {
	if x != nil {
		return x.LinuxOSInfoShort
	}
	return ""
}

type DriverUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DriverVersion    uint32                 `protobuf:"varint,1,opt,name=driver_version,json=driverVersion,proto3" json:"driver_version,omitempty"`      // 本地的驱动版本
	ErrorCode        DriverUpgradeErrorCode `protobuf:"varint,2,opt,name=errorCode,proto3,enum=agent.DriverUpgradeErrorCode" json:"errorCode,omitempty"` // 错误代码
	LinuxOSInfoLong  string                 `protobuf:"bytes,3,opt,name=linuxOSInfoLong,proto3" json:"linuxOSInfoLong,omitempty"`                        // linux 系统信息长串，仅linux
	LinuxOSInfoShort string                 `protobuf:"bytes,4,opt,name=linuxOSInfoShort,proto3" json:"linuxOSInfoShort,omitempty"`                      // linux 系统信息短串，仅linux
}

func (x *DriverUpgradeResult) Reset() {
	*x = DriverUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DriverUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverUpgradeResult) ProtoMessage() {}

func (x *DriverUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverUpgradeResult.ProtoReflect.Descriptor instead.
func (*DriverUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{14}
}

func (x *DriverUpgradeResult) GetDriverVersion() uint32 {
	if x != nil {
		return x.DriverVersion
	}
	return 0
}

func (x *DriverUpgradeResult) GetErrorCode() DriverUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return DriverUpgradeErrorCode_DUEC_OK
}

func (x *DriverUpgradeResult) GetLinuxOSInfoLong() string {
	if x != nil {
		return x.LinuxOSInfoLong
	}
	return ""
}

func (x *DriverUpgradeResult) GetLinuxOSInfoShort() string {
	if x != nil {
		return x.LinuxOSInfoShort
	}
	return ""
}

type HashEngineFileUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string                         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                 // 本地hash库版本
	ErrorCode HashEngineFileUpgradeErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=agent.HashEngineFileUpgradeErrorCode" json:"error_code,omitempty"` // 错误代码
}

func (x *HashEngineFileUpgradeResult) Reset() {
	*x = HashEngineFileUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HashEngineFileUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HashEngineFileUpgradeResult) ProtoMessage() {}

func (x *HashEngineFileUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HashEngineFileUpgradeResult.ProtoReflect.Descriptor instead.
func (*HashEngineFileUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{15}
}

func (x *HashEngineFileUpgradeResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *HashEngineFileUpgradeResult) GetErrorCode() HashEngineFileUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return HashEngineFileUpgradeErrorCode_HEFUEC_OK
}

type NgavFileUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string                         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                 // 本地ngav库版本
	ErrorCode HashEngineFileUpgradeErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=agent.HashEngineFileUpgradeErrorCode" json:"error_code,omitempty"` // 错误代码
}

func (x *NgavFileUpgradeResult) Reset() {
	*x = NgavFileUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NgavFileUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NgavFileUpgradeResult) ProtoMessage() {}

func (x *NgavFileUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NgavFileUpgradeResult.ProtoReflect.Descriptor instead.
func (*NgavFileUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{16}
}

func (x *NgavFileUpgradeResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *NgavFileUpgradeResult) GetErrorCode() HashEngineFileUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return HashEngineFileUpgradeErrorCode_HEFUEC_OK
}

type RaspFileUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string                         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                 // rasp库版本
	ErrorCode HashEngineFileUpgradeErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=agent.HashEngineFileUpgradeErrorCode" json:"error_code,omitempty"` // 错误代码
}

func (x *RaspFileUpgradeResult) Reset() {
	*x = RaspFileUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RaspFileUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RaspFileUpgradeResult) ProtoMessage() {}

func (x *RaspFileUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RaspFileUpgradeResult.ProtoReflect.Descriptor instead.
func (*RaspFileUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{17}
}

func (x *RaspFileUpgradeResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *RaspFileUpgradeResult) GetErrorCode() HashEngineFileUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return HashEngineFileUpgradeErrorCode_HEFUEC_OK
}

type BaselineFileUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string                         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                 // 基线规则库版本
	ErrorCode HashEngineFileUpgradeErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=agent.HashEngineFileUpgradeErrorCode" json:"error_code,omitempty"` // 错误代码
}

func (x *BaselineFileUpgradeResult) Reset() {
	*x = BaselineFileUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaselineFileUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaselineFileUpgradeResult) ProtoMessage() {}

func (x *BaselineFileUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaselineFileUpgradeResult.ProtoReflect.Descriptor instead.
func (*BaselineFileUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{18}
}

func (x *BaselineFileUpgradeResult) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *BaselineFileUpgradeResult) GetErrorCode() HashEngineFileUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return HashEngineFileUpgradeErrorCode_HEFUEC_OK
}

// 统一引擎库升级请求结构
type AgentEngineLibUpgradeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to HasUpgradeRequest:
	//
	//	*AgentEngineLibUpgradeRequest_DomainWhiteLib
	//	*AgentEngineLibUpgradeRequest_FileSignComWhiteLib
	//	*AgentEngineLibUpgradeRequest_FileDriverblacklistLib
	//	*AgentEngineLibUpgradeRequest_AcdrLib
	//	*AgentEngineLibUpgradeRequest_AttackWhiteLib
	//	*AgentEngineLibUpgradeRequest_FileSignBlackLib
	//	*AgentEngineLibUpgradeRequest_FileSlLib
	HasUpgradeRequest isAgentEngineLibUpgradeRequest_HasUpgradeRequest `protobuf_oneof:"has_upgradeRequest"`
}

func (x *AgentEngineLibUpgradeRequest) Reset() {
	*x = AgentEngineLibUpgradeRequest{}
	mi := &file_agent_upgrade_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentEngineLibUpgradeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentEngineLibUpgradeRequest) ProtoMessage() {}

func (x *AgentEngineLibUpgradeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentEngineLibUpgradeRequest.ProtoReflect.Descriptor instead.
func (*AgentEngineLibUpgradeRequest) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{19}
}

func (m *AgentEngineLibUpgradeRequest) GetHasUpgradeRequest() isAgentEngineLibUpgradeRequest_HasUpgradeRequest {
	if m != nil {
		return m.HasUpgradeRequest
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetDomainWhiteLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_DomainWhiteLib); ok {
		return x.DomainWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetFileSignComWhiteLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_FileSignComWhiteLib); ok {
		return x.FileSignComWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetFileDriverblacklistLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_FileDriverblacklistLib); ok {
		return x.FileDriverblacklistLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetAcdrLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_AcdrLib); ok {
		return x.AcdrLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetAttackWhiteLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_AttackWhiteLib); ok {
		return x.AttackWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetFileSignBlackLib() *AgentEngineLibVersion {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_FileSignBlackLib); ok {
		return x.FileSignBlackLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeRequest) GetFileSlLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeRequest().(*AgentEngineLibUpgradeRequest_FileSlLib); ok {
		return x.FileSlLib
	}
	return nil
}

type isAgentEngineLibUpgradeRequest_HasUpgradeRequest interface {
	isAgentEngineLibUpgradeRequest_HasUpgradeRequest()
}

type AgentEngineLibUpgradeRequest_DomainWhiteLib struct {
	DomainWhiteLib *AgentEngineLibVersion `protobuf:"bytes,1,opt,name=domain_white_lib,json=domainWhiteLib,proto3,oneof"` // 域名白库
}

type AgentEngineLibUpgradeRequest_FileSignComWhiteLib struct {
	FileSignComWhiteLib *AgentEngineLibVersion `protobuf:"bytes,2,opt,name=file_sign_com_white_lib,json=fileSignComWhiteLib,proto3,oneof"` // 文件签名公司白名单库
}

type AgentEngineLibUpgradeRequest_FileDriverblacklistLib struct {
	FileDriverblacklistLib *AgentEngineLibVersion `protobuf:"bytes,3,opt,name=file_driverblacklist_lib,json=fileDriverblacklistLib,proto3,oneof"` //驱动黑库
}

type AgentEngineLibUpgradeRequest_AcdrLib struct {
	AcdrLib *AgentEngineLibVersion `protobuf:"bytes,4,opt,name=acdr_lib,json=acdrLib,proto3,oneof"` // acdr库
}

type AgentEngineLibUpgradeRequest_AttackWhiteLib struct {
	AttackWhiteLib *AgentEngineLibVersion `protobuf:"bytes,5,opt,name=attack_white_lib,json=attackWhiteLib,proto3,oneof"` // 内存、系统攻击白库
}

type AgentEngineLibUpgradeRequest_FileSignBlackLib struct {
	FileSignBlackLib *AgentEngineLibVersion `protobuf:"bytes,6,opt,name=file_sign_black_lib,json=fileSignBlackLib,proto3,oneof"` // 文件签名黑库
}

type AgentEngineLibUpgradeRequest_FileSlLib struct {
	FileSlLib *AgentEngineLibUpgradeItem `protobuf:"bytes,7,opt,name=file_sl_lib,json=fileSlLib,proto3,oneof"` // 统一引擎库
}

func (*AgentEngineLibUpgradeRequest_DomainWhiteLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {
}

func (*AgentEngineLibUpgradeRequest_FileSignComWhiteLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {
}

func (*AgentEngineLibUpgradeRequest_FileDriverblacklistLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {
}

func (*AgentEngineLibUpgradeRequest_AcdrLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {}

func (*AgentEngineLibUpgradeRequest_AttackWhiteLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {
}

func (*AgentEngineLibUpgradeRequest_FileSignBlackLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {
}

func (*AgentEngineLibUpgradeRequest_FileSlLib) isAgentEngineLibUpgradeRequest_HasUpgradeRequest() {}

type AgentEngineLibVersion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"` // 引擎版本
}

func (x *AgentEngineLibVersion) Reset() {
	*x = AgentEngineLibVersion{}
	mi := &file_agent_upgrade_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentEngineLibVersion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentEngineLibVersion) ProtoMessage() {}

func (x *AgentEngineLibVersion) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentEngineLibVersion.ProtoReflect.Descriptor instead.
func (*AgentEngineLibVersion) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{20}
}

func (x *AgentEngineLibVersion) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

// 统一引擎库升级结果上报结构
type AgentEngineLibUpgradeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to HasUpgradeResult:
	//
	//	*AgentEngineLibUpgradeResult_DomainWhiteLib
	//	*AgentEngineLibUpgradeResult_FileSignComWhiteLib
	//	*AgentEngineLibUpgradeResult_FileDriverblacklistLib
	//	*AgentEngineLibUpgradeResult_AcdrLib
	//	*AgentEngineLibUpgradeResult_AttackWhiteLib
	//	*AgentEngineLibUpgradeResult_FileSignBlackLib
	//	*AgentEngineLibUpgradeResult_FileSlLib
	HasUpgradeResult isAgentEngineLibUpgradeResult_HasUpgradeResult `protobuf_oneof:"has_upgradeResult"`
}

func (x *AgentEngineLibUpgradeResult) Reset() {
	*x = AgentEngineLibUpgradeResult{}
	mi := &file_agent_upgrade_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentEngineLibUpgradeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentEngineLibUpgradeResult) ProtoMessage() {}

func (x *AgentEngineLibUpgradeResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentEngineLibUpgradeResult.ProtoReflect.Descriptor instead.
func (*AgentEngineLibUpgradeResult) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{21}
}

func (m *AgentEngineLibUpgradeResult) GetHasUpgradeResult() isAgentEngineLibUpgradeResult_HasUpgradeResult {
	if m != nil {
		return m.HasUpgradeResult
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetDomainWhiteLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_DomainWhiteLib); ok {
		return x.DomainWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetFileSignComWhiteLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_FileSignComWhiteLib); ok {
		return x.FileSignComWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetFileDriverblacklistLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_FileDriverblacklistLib); ok {
		return x.FileDriverblacklistLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetAcdrLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_AcdrLib); ok {
		return x.AcdrLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetAttackWhiteLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_AttackWhiteLib); ok {
		return x.AttackWhiteLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetFileSignBlackLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_FileSignBlackLib); ok {
		return x.FileSignBlackLib
	}
	return nil
}

func (x *AgentEngineLibUpgradeResult) GetFileSlLib() *AgentEngineLibUpgradeItem {
	if x, ok := x.GetHasUpgradeResult().(*AgentEngineLibUpgradeResult_FileSlLib); ok {
		return x.FileSlLib
	}
	return nil
}

type isAgentEngineLibUpgradeResult_HasUpgradeResult interface {
	isAgentEngineLibUpgradeResult_HasUpgradeResult()
}

type AgentEngineLibUpgradeResult_DomainWhiteLib struct {
	DomainWhiteLib *AgentEngineLibUpgradeItem `protobuf:"bytes,1,opt,name=domain_white_lib,json=domainWhiteLib,proto3,oneof"` // 域名白库
}

type AgentEngineLibUpgradeResult_FileSignComWhiteLib struct {
	FileSignComWhiteLib *AgentEngineLibUpgradeItem `protobuf:"bytes,2,opt,name=file_sign_com_white_lib,json=fileSignComWhiteLib,proto3,oneof"` // 文件签名公司白名单库
}

type AgentEngineLibUpgradeResult_FileDriverblacklistLib struct {
	FileDriverblacklistLib *AgentEngineLibUpgradeItem `protobuf:"bytes,3,opt,name=file_driverblacklist_lib,json=fileDriverblacklistLib,proto3,oneof"` // 驱动黑库
}

type AgentEngineLibUpgradeResult_AcdrLib struct {
	AcdrLib *AgentEngineLibUpgradeItem `protobuf:"bytes,4,opt,name=acdr_lib,json=acdrLib,proto3,oneof"` // acdr库
}

type AgentEngineLibUpgradeResult_AttackWhiteLib struct {
	AttackWhiteLib *AgentEngineLibUpgradeItem `protobuf:"bytes,5,opt,name=attack_white_lib,json=attackWhiteLib,proto3,oneof"` // 内存、系统攻击白库
}

type AgentEngineLibUpgradeResult_FileSignBlackLib struct {
	FileSignBlackLib *AgentEngineLibUpgradeItem `protobuf:"bytes,6,opt,name=file_sign_black_lib,json=fileSignBlackLib,proto3,oneof"` // 文件签名黑库
}

type AgentEngineLibUpgradeResult_FileSlLib struct {
	FileSlLib *AgentEngineLibUpgradeItem `protobuf:"bytes,7,opt,name=file_sl_lib,json=fileSlLib,proto3,oneof"` // 统一引擎库
}

func (*AgentEngineLibUpgradeResult_DomainWhiteLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {}

func (*AgentEngineLibUpgradeResult_FileSignComWhiteLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {
}

func (*AgentEngineLibUpgradeResult_FileDriverblacklistLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {
}

func (*AgentEngineLibUpgradeResult_AcdrLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {}

func (*AgentEngineLibUpgradeResult_AttackWhiteLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {}

func (*AgentEngineLibUpgradeResult_FileSignBlackLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {
}

func (*AgentEngineLibUpgradeResult_FileSlLib) isAgentEngineLibUpgradeResult_HasUpgradeResult() {}

type AgentEngineLibUpgradeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version   string                         `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`                                                                 // 引擎版本
	ErrorCode HashEngineFileUpgradeErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=agent.HashEngineFileUpgradeErrorCode" json:"error_code,omitempty"` // 错误代码
}

func (x *AgentEngineLibUpgradeItem) Reset() {
	*x = AgentEngineLibUpgradeItem{}
	mi := &file_agent_upgrade_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentEngineLibUpgradeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentEngineLibUpgradeItem) ProtoMessage() {}

func (x *AgentEngineLibUpgradeItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentEngineLibUpgradeItem.ProtoReflect.Descriptor instead.
func (*AgentEngineLibUpgradeItem) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{22}
}

func (x *AgentEngineLibUpgradeItem) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *AgentEngineLibUpgradeItem) GetErrorCode() HashEngineFileUpgradeErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return HashEngineFileUpgradeErrorCode_HEFUEC_OK
}

type UpgradeNotifyRespone struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind   UpgradeKind              `protobuf:"varint,1,opt,name=kind,proto3,enum=agent.UpgradeKind" json:"kind,omitempty"`
	Code   UpgradeNotifyResponeCode `protobuf:"varint,2,opt,name=code,proto3,enum=agent.UpgradeNotifyResponeCode" json:"code,omitempty"`
	ErrMsg string                   `protobuf:"bytes,3,opt,name=errMsg,proto3" json:"errMsg,omitempty"`
}

func (x *UpgradeNotifyRespone) Reset() {
	*x = UpgradeNotifyRespone{}
	mi := &file_agent_upgrade_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpgradeNotifyRespone) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeNotifyRespone) ProtoMessage() {}

func (x *UpgradeNotifyRespone) ProtoReflect() protoreflect.Message {
	mi := &file_agent_upgrade_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeNotifyRespone.ProtoReflect.Descriptor instead.
func (*UpgradeNotifyRespone) Descriptor() ([]byte, []int) {
	return file_agent_upgrade_proto_rawDescGZIP(), []int{23}
}

func (x *UpgradeNotifyRespone) GetKind() UpgradeKind {
	if x != nil {
		return x.Kind
	}
	return UpgradeKind_UK_UNDEFINED
}

func (x *UpgradeNotifyRespone) GetCode() UpgradeNotifyResponeCode {
	if x != nil {
		return x.Code
	}
	return UpgradeNotifyResponeCode_UNRC_OK
}

func (x *UpgradeNotifyRespone) GetErrMsg() string {
	if x != nil {
		return x.ErrMsg
	}
	return ""
}

var File_agent_upgrade_proto protoreflect.FileDescriptor

var file_agent_upgrade_proto_rawDesc = []byte{
	0x0a, 0x13, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x83, 0x0a, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x75, 0x73, 0x68, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x62, 0x61, 0x73, 0x65, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x44, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x62, 0x61, 0x73, 0x65, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x51, 0x0a, 0x14, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x00, 0x52, 0x14, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x41,
	0x0a, 0x10, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x48,
	0x01, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x45, 0x0a, 0x12, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x5f, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x48, 0x02, 0x52, 0x10, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x6e, 0x67, 0x61, 0x76,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x67, 0x61, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x03, 0x52, 0x08,
	0x6e, 0x67, 0x61, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x09, 0x72, 0x61, 0x73, 0x70,
	0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x73, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x04, 0x52, 0x08,
	0x72, 0x61, 0x73, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x62, 0x61, 0x73, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x48, 0x05, 0x52, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0x49, 0x0a, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x06, 0x52, 0x0f,
	0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x53, 0x0a, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x63, 0x6f, 0x6d, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x07, 0x52, 0x14,
	0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x63, 0x6f, 0x6d, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x14, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x62, 0x6c,
	0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x48, 0x08, 0x52, 0x13, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c,
	0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x41, 0x0a, 0x0c, 0x61, 0x63, 0x64, 0x72, 0x6c,
	0x69, 0x62, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x09, 0x52, 0x0b, 0x61,
	0x63, 0x64, 0x72, 0x6c, 0x69, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x13, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x62, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x0a, 0x52, 0x12, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x11, 0x73,
	0x69, 0x67, 0x6e, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x62, 0x5f, 0x66, 0x69, 0x6c, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x48, 0x0b, 0x52, 0x10, 0x73, 0x69, 0x67, 0x6e, 0x62, 0x6c, 0x61, 0x63,
	0x6b, 0x6c, 0x69, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x73, 0x6c, 0x6c, 0x69,
	0x62, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x0c, 0x52, 0x09, 0x73, 0x6c,
	0x6c, 0x69, 0x62, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x68, 0x61, 0x73, 0x5f, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x42, 0x14, 0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x65,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x68, 0x61, 0x73,
	0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x6e, 0x67, 0x61, 0x76, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x61, 0x73, 0x70, 0x66, 0x69, 0x6c,
	0x65, 0x42, 0x12, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e,
	0x65, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x5f, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x77, 0x68, 0x69, 0x74, 0x65, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x1a, 0x0a, 0x18,
	0x68, 0x61, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x63, 0x6f, 0x6d, 0x77,
	0x68, 0x69, 0x74, 0x65, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x19, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x66,
	0x69, 0x6c, 0x65, 0x42, 0x11, 0x0a, 0x0f, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x63, 0x64, 0x72, 0x6c,
	0x69, 0x62, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x15, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x16, 0x0a,
	0x14, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69,
	0x62, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x68, 0x61, 0x73, 0x5f, 0x73, 0x6c, 0x6c,
	0x69, 0x62, 0x66, 0x69, 0x6c, 0x65, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x42, 0x61, 0x73, 0x65, 0x44,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x6b, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x70, 0x6b, 0x67,
	0x55, 0x72, 0x6c, 0x12, 0x29, 0x0a, 0x03, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x61, 0x79, 0x52, 0x03, 0x77, 0x61, 0x79, 0x12, 0x2b,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8d, 0x01, 0x0a, 0x0b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f,
	0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x6f,
	0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d,
	0x66, 0x69, 0x6c, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x8c, 0x01, 0x0a, 0x0c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x66, 0x69, 0x6c, 0x65,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x66, 0x69, 0x6c, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x66,
	0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x0a, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22,
	0x80, 0x01, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08,
	0x6f, 0x72, 0x69, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x22,
	0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x76, 0x65, 0x50, 0x61,
	0x74, 0x68, 0x22, 0x8d, 0x02, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x72,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x61, 0x72,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6b, 0x67, 0x4d, 0x64, 0x35,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x70, 0x6b, 0x67, 0x4d, 0x64, 0x35, 0x12, 0x16,
	0x0a, 0x06, 0x70, 0x6b, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06,
	0x70, 0x6b, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x29, 0x0a, 0x03, 0x77, 0x61,
	0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x61, 0x79,
	0x52, 0x03, 0x77, 0x61, 0x79, 0x12, 0x2b, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0xc1, 0x01, 0x0a, 0x12, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x2b, 0x0a, 0x08, 0x62, 0x61, 0x73,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08, 0x62, 0x61,
	0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3a, 0x0a, 0x09, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61,
	0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x30, 0x0a, 0x14, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x2a, 0x0a, 0x0e, 0x48, 0x61, 0x73, 0x68,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x24, 0x0a, 0x08, 0x4e, 0x67, 0x61, 0x76, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x24, 0x0a, 0x08, 0x52, 0x61,
	0x73, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x28, 0x0a, 0x0c, 0x42, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xb4, 0x01, 0x0a, 0x10, 0x4c,
	0x69, 0x6e, 0x75, 0x78, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x25, 0x0a, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x6c,
	0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x10, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72,
	0x74, 0x22, 0xcf, 0x01, 0x0a, 0x13, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0d, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x3b, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x6f, 0x6e, 0x67,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x6f, 0x6e, 0x67, 0x12, 0x2a, 0x0a, 0x10, 0x6c, 0x69, 0x6e, 0x75, 0x78,
	0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x53, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x68,
	0x6f, 0x72, 0x74, 0x22, 0x7d, 0x0a, 0x1b, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0a,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x22, 0x77, 0x0a, 0x15, 0x4e, 0x67, 0x61, 0x76, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x77, 0x0a, 0x15, 0x52,
	0x61, 0x73, 0x70, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44,
	0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x22, 0x7b, 0x0a, 0x19, 0x42, 0x61, 0x73, 0x65, 0x6c, 0x69, 0x6e, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0a, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x22, 0xc6, 0x04, 0x0a, 0x1c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x48, 0x0a, 0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x6f,
	0x6d, 0x61, 0x69, 0x6e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x12, 0x54, 0x0a, 0x17,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x5f, 0x77, 0x68,
	0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x13, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x62, 0x12, 0x58, 0x0a, 0x18, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x72, 0x69, 0x76, 0x65,
	0x72, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x48, 0x00, 0x52, 0x16, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72,
	0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x62, 0x12, 0x39, 0x0a, 0x08,
	0x61, 0x63, 0x64, 0x72, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x07,
	0x61, 0x63, 0x64, 0x72, 0x4c, 0x69, 0x62, 0x12, 0x48, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45,
	0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x62, 0x12, 0x4d, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x62,
	0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x10,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x62,
	0x12, 0x42, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6c, 0x5f, 0x6c, 0x69, 0x62, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x6c, 0x4c, 0x69, 0x62, 0x42, 0x14, 0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x75, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x31, 0x0a, 0x15, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xdc, 0x04,
	0x0a, 0x1b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x4c, 0x0a,
	0x10, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x0e, 0x64, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62, 0x12, 0x58, 0x0a, 0x17, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x5f, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00,
	0x52, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x4c, 0x69, 0x62, 0x12, 0x5c, 0x0a, 0x18, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6c, 0x69,
	0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x16, 0x66, 0x69, 0x6c,
	0x65, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x6c, 0x69, 0x73, 0x74,
	0x4c, 0x69, 0x62, 0x12, 0x3d, 0x0a, 0x08, 0x61, 0x63, 0x64, 0x72, 0x5f, 0x6c, 0x69, 0x62, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x64, 0x72, 0x4c,
	0x69, 0x62, 0x12, 0x4c, 0x0a, 0x10, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00,
	0x52, 0x0e, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x62,
	0x12, 0x51, 0x0a, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x62, 0x6c,
	0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x4c, 0x69, 0x62, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48,
	0x00, 0x52, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x67, 0x6e, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x4c, 0x69, 0x62, 0x12, 0x42, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x6c, 0x5f, 0x6c,
	0x69, 0x62, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00, 0x52, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x6c, 0x4c, 0x69, 0x62, 0x42, 0x13, 0x0a, 0x11, 0x68, 0x61, 0x73, 0x5f, 0x75,
	0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x7b, 0x0a, 0x19,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x4c, 0x69, 0x62, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72,
	0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x09,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x14, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x33, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x2a, 0x41, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x57, 0x61, 0x79, 0x12, 0x0f, 0x0a, 0x0b, 0x41,
	0x44, 0x57, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x41, 0x44, 0x57, 0x5f, 0x48, 0x54, 0x54, 0x50, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x44,
	0x57, 0x5f, 0x53, 0x4f, 0x43, 0x4b, 0x45, 0x54, 0x10, 0x02, 0x2a, 0xab, 0x02, 0x0a, 0x15, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x4f, 0x4b, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x41,
	0x55, 0x45, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11,
	0x41, 0x55, 0x45, 0x43, 0x5f, 0x55, 0x4e, 0x5a, 0x49, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x12,
	0x0a, 0x0e, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49,
	0x4f, 0x4e, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x10, 0x06, 0x12, 0x21, 0x0a, 0x1d,
	0x41, 0x55, 0x45, 0x43, 0x5f, 0x43, 0x4f, 0x50, 0x59, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44,
	0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12,
	0x14, 0x0a, 0x10, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x09, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x55, 0x45, 0x43, 0x5f, 0x53, 0x41, 0x4d, 0x45, 0x5f, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x0a, 0x2a, 0xa8, 0x02, 0x0a, 0x16, 0x44, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x18, 0x0a, 0x14, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41,
	0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x44, 0x55,
	0x45, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45,
	0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x44,
	0x55, 0x45, 0x43, 0x5f, 0x55, 0x4e, 0x5a, 0x49, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x4f, 0x50, 0x45, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x12, 0x0a,
	0x0e, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x05, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f,
	0x53, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x14, 0x0a,
	0x10, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x55, 0x45, 0x43, 0x5f, 0x52, 0x45, 0x4e, 0x41,
	0x4d, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x12, 0x1a, 0x0a, 0x16, 0x44,
	0x55, 0x45, 0x43, 0x5f, 0x52, 0x45, 0x4d, 0x4d, 0x4f, 0x44, 0x5f, 0x4f, 0x4c, 0x44, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x55, 0x45, 0x43, 0x5f,
	0x49, 0x4e, 0x53, 0x4d, 0x4f, 0x44, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x0a, 0x2a, 0x8d, 0x02, 0x0a, 0x1e, 0x48, 0x61, 0x73, 0x68, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43,
	0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f,
	0x44, 0x4f, 0x57, 0x4e, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x21, 0x0a, 0x1d, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x47, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f, 0x55,
	0x4e, 0x5a, 0x49, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1b, 0x0a,
	0x17, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x50, 0x45,
	0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x48, 0x45,
	0x46, 0x55, 0x45, 0x43, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x05,
	0x12, 0x1c, 0x0a, 0x18, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12, 0x16,
	0x0a, 0x12, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x48, 0x45, 0x46, 0x55, 0x45, 0x43,
	0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43,
	0x48, 0x10, 0x08, 0x2a, 0x88, 0x02, 0x0a, 0x0b, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4b,
	0x69, 0x6e, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4b, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49,
	0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4b, 0x5f, 0x41, 0x47, 0x45, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4b, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x42, 0x4c,
	0x41, 0x43, 0x4b, 0x4c, 0x49, 0x42, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4b, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x42, 0x10, 0x04, 0x12, 0x16,
	0x0a, 0x12, 0x55, 0x4b, 0x5f, 0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54,
	0x45, 0x4c, 0x49, 0x42, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4b, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x42, 0x10, 0x06,
	0x12, 0x16, 0x0a, 0x12, 0x55, 0x4b, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x52, 0x5f, 0x42, 0x4c,
	0x41, 0x43, 0x4b, 0x4c, 0x49, 0x42, 0x10, 0x07, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4b, 0x5f, 0x41,
	0x43, 0x44, 0x52, 0x5f, 0x4c, 0x49, 0x42, 0x10, 0x08, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x4b, 0x5f,
	0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x42, 0x10,
	0x09, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x53, 0x49, 0x47, 0x4e,
	0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x4c, 0x49, 0x42, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x55,
	0x4b, 0x5f, 0x53, 0x4c, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x4c, 0x69, 0x62, 0x10, 0x0b, 0x2a, 0x6a,
	0x0a, 0x18, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x52, 0x43, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x52, 0x43, 0x5f,
	0x53, 0x41, 0x4d, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x14,
	0x0a, 0x10, 0x55, 0x4e, 0x52, 0x43, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4e, 0x52, 0x43, 0x5f, 0x4f, 0x54, 0x48,
	0x45, 0x52, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x03, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69,
	0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d,
	0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67,
	0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_upgrade_proto_rawDescOnce sync.Once
	file_agent_upgrade_proto_rawDescData = file_agent_upgrade_proto_rawDesc
)

func file_agent_upgrade_proto_rawDescGZIP() []byte {
	file_agent_upgrade_proto_rawDescOnce.Do(func() {
		file_agent_upgrade_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_upgrade_proto_rawDescData)
	})
	return file_agent_upgrade_proto_rawDescData
}

var file_agent_upgrade_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_agent_upgrade_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_agent_upgrade_proto_goTypes = []any{
	(AgentDownloadWay)(0),                // 0: agent.AgentDownloadWay
	(AgentUpgradeErrorCode)(0),           // 1: agent.AgentUpgradeErrorCode
	(DriverUpgradeErrorCode)(0),          // 2: agent.DriverUpgradeErrorCode
	(HashEngineFileUpgradeErrorCode)(0),  // 3: agent.HashEngineFileUpgradeErrorCode
	(UpgradeKind)(0),                     // 4: agent.UpgradeKind
	(UpgradeNotifyResponeCode)(0),        // 5: agent.UpgradeNotifyResponeCode
	(*ServerPushMessage)(nil),            // 6: agent.ServerPushMessage
	(*BaseDownloadInfo)(nil),             // 7: agent.BaseDownloadInfo
	(*RequestFile)(nil),                  // 8: agent.RequestFile
	(*ResponseFile)(nil),                 // 9: agent.ResponseFile
	(*UploadFile)(nil),                   // 10: agent.UploadFile
	(*FileInfo)(nil),                     // 11: agent.FileInfo
	(*AgentUpgradeMsg)(nil),              // 12: agent.AgentUpgradeMsg
	(*AgentUpgradeResult)(nil),           // 13: agent.AgentUpgradeResult
	(*DriverUpgradeMessage)(nil),         // 14: agent.DriverUpgradeMessage
	(*HashEngineFile)(nil),               // 15: agent.HashEngineFile
	(*NgavFile)(nil),                     // 16: agent.NgavFile
	(*RaspFile)(nil),                     // 17: agent.RaspFile
	(*BaselineFile)(nil),                 // 18: agent.BaselineFile
	(*LinuxAgentOSInfo)(nil),             // 19: agent.LinuxAgentOSInfo
	(*DriverUpgradeResult)(nil),          // 20: agent.DriverUpgradeResult
	(*HashEngineFileUpgradeResult)(nil),  // 21: agent.HashEngineFileUpgradeResult
	(*NgavFileUpgradeResult)(nil),        // 22: agent.NgavFileUpgradeResult
	(*RaspFileUpgradeResult)(nil),        // 23: agent.RaspFileUpgradeResult
	(*BaselineFileUpgradeResult)(nil),    // 24: agent.BaselineFileUpgradeResult
	(*AgentEngineLibUpgradeRequest)(nil), // 25: agent.AgentEngineLibUpgradeRequest
	(*AgentEngineLibVersion)(nil),        // 26: agent.AgentEngineLibVersion
	(*AgentEngineLibUpgradeResult)(nil),  // 27: agent.AgentEngineLibUpgradeResult
	(*AgentEngineLibUpgradeItem)(nil),    // 28: agent.AgentEngineLibUpgradeItem
	(*UpgradeNotifyRespone)(nil),         // 29: agent.UpgradeNotifyRespone
	(*ClientID)(nil),                     // 30: agent.ClientID
}
var file_agent_upgrade_proto_depIdxs = []int32{
	7,  // 0: agent.ServerPushMessage.baseDownloadInfo:type_name -> agent.BaseDownloadInfo
	14, // 1: agent.ServerPushMessage.driverUpgradeMessage:type_name -> agent.DriverUpgradeMessage
	15, // 2: agent.ServerPushMessage.hash_engine_file:type_name -> agent.HashEngineFile
	15, // 3: agent.ServerPushMessage.sha256_engine_file:type_name -> agent.HashEngineFile
	16, // 4: agent.ServerPushMessage.ngav_file:type_name -> agent.NgavFile
	17, // 5: agent.ServerPushMessage.rasp_file:type_name -> agent.RaspFile
	18, // 6: agent.ServerPushMessage.baseline_file:type_name -> agent.BaselineFile
	26, // 7: agent.ServerPushMessage.domainwhite_file:type_name -> agent.AgentEngineLibVersion
	26, // 8: agent.ServerPushMessage.filesigncomwhite_file:type_name -> agent.AgentEngineLibVersion
	26, // 9: agent.ServerPushMessage.driverblacklist_file:type_name -> agent.AgentEngineLibVersion
	26, // 10: agent.ServerPushMessage.acdrlib_file:type_name -> agent.AgentEngineLibVersion
	26, // 11: agent.ServerPushMessage.attackwhitelib_file:type_name -> agent.AgentEngineLibVersion
	26, // 12: agent.ServerPushMessage.signblacklib_file:type_name -> agent.AgentEngineLibVersion
	26, // 13: agent.ServerPushMessage.sllib_file:type_name -> agent.AgentEngineLibVersion
	0,  // 14: agent.BaseDownloadInfo.way:type_name -> agent.AgentDownloadWay
	11, // 15: agent.BaseDownloadInfo.fileInfo:type_name -> agent.FileInfo
	0,  // 16: agent.AgentUpgradeMsg.way:type_name -> agent.AgentDownloadWay
	11, // 17: agent.AgentUpgradeMsg.fileInfo:type_name -> agent.FileInfo
	30, // 18: agent.AgentUpgradeResult.baseInfo:type_name -> agent.ClientID
	1,  // 19: agent.AgentUpgradeResult.errorCode:type_name -> agent.AgentUpgradeErrorCode
	2,  // 20: agent.DriverUpgradeResult.errorCode:type_name -> agent.DriverUpgradeErrorCode
	3,  // 21: agent.HashEngineFileUpgradeResult.error_code:type_name -> agent.HashEngineFileUpgradeErrorCode
	3,  // 22: agent.NgavFileUpgradeResult.error_code:type_name -> agent.HashEngineFileUpgradeErrorCode
	3,  // 23: agent.RaspFileUpgradeResult.error_code:type_name -> agent.HashEngineFileUpgradeErrorCode
	3,  // 24: agent.BaselineFileUpgradeResult.error_code:type_name -> agent.HashEngineFileUpgradeErrorCode
	26, // 25: agent.AgentEngineLibUpgradeRequest.domain_white_lib:type_name -> agent.AgentEngineLibVersion
	26, // 26: agent.AgentEngineLibUpgradeRequest.file_sign_com_white_lib:type_name -> agent.AgentEngineLibVersion
	26, // 27: agent.AgentEngineLibUpgradeRequest.file_driverblacklist_lib:type_name -> agent.AgentEngineLibVersion
	26, // 28: agent.AgentEngineLibUpgradeRequest.acdr_lib:type_name -> agent.AgentEngineLibVersion
	26, // 29: agent.AgentEngineLibUpgradeRequest.attack_white_lib:type_name -> agent.AgentEngineLibVersion
	26, // 30: agent.AgentEngineLibUpgradeRequest.file_sign_black_lib:type_name -> agent.AgentEngineLibVersion
	28, // 31: agent.AgentEngineLibUpgradeRequest.file_sl_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 32: agent.AgentEngineLibUpgradeResult.domain_white_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 33: agent.AgentEngineLibUpgradeResult.file_sign_com_white_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 34: agent.AgentEngineLibUpgradeResult.file_driverblacklist_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 35: agent.AgentEngineLibUpgradeResult.acdr_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 36: agent.AgentEngineLibUpgradeResult.attack_white_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 37: agent.AgentEngineLibUpgradeResult.file_sign_black_lib:type_name -> agent.AgentEngineLibUpgradeItem
	28, // 38: agent.AgentEngineLibUpgradeResult.file_sl_lib:type_name -> agent.AgentEngineLibUpgradeItem
	3,  // 39: agent.AgentEngineLibUpgradeItem.error_code:type_name -> agent.HashEngineFileUpgradeErrorCode
	4,  // 40: agent.UpgradeNotifyRespone.kind:type_name -> agent.UpgradeKind
	5,  // 41: agent.UpgradeNotifyRespone.code:type_name -> agent.UpgradeNotifyResponeCode
	42, // [42:42] is the sub-list for method output_type
	42, // [42:42] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_agent_upgrade_proto_init() }
func file_agent_upgrade_proto_init() {
	if File_agent_upgrade_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_upgrade_proto_msgTypes[0].OneofWrappers = []any{
		(*ServerPushMessage_DriverUpgradeMessage)(nil),
		(*ServerPushMessage_HashEngineFile)(nil),
		(*ServerPushMessage_Sha256EngineFile)(nil),
		(*ServerPushMessage_NgavFile)(nil),
		(*ServerPushMessage_RaspFile)(nil),
		(*ServerPushMessage_BaselineFile)(nil),
		(*ServerPushMessage_DomainwhiteFile)(nil),
		(*ServerPushMessage_FilesigncomwhiteFile)(nil),
		(*ServerPushMessage_DriverblacklistFile)(nil),
		(*ServerPushMessage_AcdrlibFile)(nil),
		(*ServerPushMessage_AttackwhitelibFile)(nil),
		(*ServerPushMessage_SignblacklibFile)(nil),
		(*ServerPushMessage_SllibFile)(nil),
	}
	file_agent_upgrade_proto_msgTypes[19].OneofWrappers = []any{
		(*AgentEngineLibUpgradeRequest_DomainWhiteLib)(nil),
		(*AgentEngineLibUpgradeRequest_FileSignComWhiteLib)(nil),
		(*AgentEngineLibUpgradeRequest_FileDriverblacklistLib)(nil),
		(*AgentEngineLibUpgradeRequest_AcdrLib)(nil),
		(*AgentEngineLibUpgradeRequest_AttackWhiteLib)(nil),
		(*AgentEngineLibUpgradeRequest_FileSignBlackLib)(nil),
		(*AgentEngineLibUpgradeRequest_FileSlLib)(nil),
	}
	file_agent_upgrade_proto_msgTypes[21].OneofWrappers = []any{
		(*AgentEngineLibUpgradeResult_DomainWhiteLib)(nil),
		(*AgentEngineLibUpgradeResult_FileSignComWhiteLib)(nil),
		(*AgentEngineLibUpgradeResult_FileDriverblacklistLib)(nil),
		(*AgentEngineLibUpgradeResult_AcdrLib)(nil),
		(*AgentEngineLibUpgradeResult_AttackWhiteLib)(nil),
		(*AgentEngineLibUpgradeResult_FileSignBlackLib)(nil),
		(*AgentEngineLibUpgradeResult_FileSlLib)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_upgrade_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_upgrade_proto_goTypes,
		DependencyIndexes: file_agent_upgrade_proto_depIdxs,
		EnumInfos:         file_agent_upgrade_proto_enumTypes,
		MessageInfos:      file_agent_upgrade_proto_msgTypes,
	}.Build()
	File_agent_upgrade_proto = out.File
	file_agent_upgrade_proto_rawDesc = nil
	file_agent_upgrade_proto_goTypes = nil
	file_agent_upgrade_proto_depIdxs = nil
}
