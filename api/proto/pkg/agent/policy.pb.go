// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/policy.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Server的操作标志，数据库中的每条记录，都有增加项和删除项标志
type Operate int32

const (
	Operate_ADD    Operate = 0
	Operate_DEL    Operate = 1
	Operate_MODIFY Operate = 2
)

// Enum value maps for Operate.
var (
	Operate_name = map[int32]string{
		0: "ADD",
		1: "DEL",
		2: "MODIFY",
	}
	Operate_value = map[string]int32{
		"ADD":    0,
		"DEL":    1,
		"MODIFY": 2,
	}
)

func (x Operate) Enum() *Operate {
	p := new(Operate)
	*p = x
	return p
}

func (x Operate) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Operate) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[0].Descriptor()
}

func (Operate) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[0]
}

func (x Operate) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Operate.Descriptor instead.
func (Operate) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{0}
}

type BWType int32

const (
	BWType_WHITE BWType = 0
	BWType_BLACK BWType = 1
)

// Enum value maps for BWType.
var (
	BWType_name = map[int32]string{
		0: "WHITE",
		1: "BLACK",
	}
	BWType_value = map[string]int32{
		"WHITE": 0,
		"BLACK": 1,
	}
)

func (x BWType) Enum() *BWType {
	p := new(BWType)
	*p = x
	return p
}

func (x BWType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BWType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[1].Descriptor()
}

func (BWType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[1]
}

func (x BWType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BWType.Descriptor instead.
func (BWType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{1}
}

type ModuleType int32

const (
	ModuleType_MEM_RISK  ModuleType = 0
	ModuleType_FILE_RISK ModuleType = 1
	ModuleType_PROC_RISK ModuleType = 2
)

// Enum value maps for ModuleType.
var (
	ModuleType_name = map[int32]string{
		0: "MEM_RISK",
		1: "FILE_RISK",
		2: "PROC_RISK",
	}
	ModuleType_value = map[string]int32{
		"MEM_RISK":  0,
		"FILE_RISK": 1,
		"PROC_RISK": 2,
	}
)

func (x ModuleType) Enum() *ModuleType {
	p := new(ModuleType)
	*p = x
	return p
}

func (x ModuleType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModuleType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[2].Descriptor()
}

func (ModuleType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[2]
}

func (x ModuleType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModuleType.Descriptor instead.
func (ModuleType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{2}
}

type ReqType int32

const (
	ReqType_ALL   ReqType = 0 // 差异化更新涉及到删除某些项，要请求所有[增加+删除]项
	ReqType_ADDED ReqType = 1 // 当第一次agent为空列表时，只需请求所有现有的增加项
)

// Enum value maps for ReqType.
var (
	ReqType_name = map[int32]string{
		0: "ALL",
		1: "ADDED",
	}
	ReqType_value = map[string]int32{
		"ALL":   0,
		"ADDED": 1,
	}
)

func (x ReqType) Enum() *ReqType {
	p := new(ReqType)
	*p = x
	return p
}

func (x ReqType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ReqType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[3].Descriptor()
}

func (ReqType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[3]
}

func (x ReqType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ReqType.Descriptor instead.
func (ReqType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{3}
}

type FileType int32

const (
	FileType_FT_FILETYPEUNKNOWN FileType = 0
	FileType_FT_FILE            FileType = 1
	FileType_FT_DIR             FileType = 2
)

// Enum value maps for FileType.
var (
	FileType_name = map[int32]string{
		0: "FT_FILETYPEUNKNOWN",
		1: "FT_FILE",
		2: "FT_DIR",
	}
	FileType_value = map[string]int32{
		"FT_FILETYPEUNKNOWN": 0,
		"FT_FILE":            1,
		"FT_DIR":             2,
	}
)

func (x FileType) Enum() *FileType {
	p := new(FileType)
	*p = x
	return p
}

func (x FileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[4].Descriptor()
}

func (FileType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[4]
}

func (x FileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileType.Descriptor instead.
func (FileType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{4}
}

type SwitchStatus int32

const (
	SwitchStatus_SS_TURN_OFF     SwitchStatus = 0 //关闭
	SwitchStatus_SS_TURN_ON      SwitchStatus = 1 //开启
	SwitchStatus_SS_ONLY_MONITOR SwitchStatus = 2 //仅上报，不拦截
)

// Enum value maps for SwitchStatus.
var (
	SwitchStatus_name = map[int32]string{
		0: "SS_TURN_OFF",
		1: "SS_TURN_ON",
		2: "SS_ONLY_MONITOR",
	}
	SwitchStatus_value = map[string]int32{
		"SS_TURN_OFF":     0,
		"SS_TURN_ON":      1,
		"SS_ONLY_MONITOR": 2,
	}
)

func (x SwitchStatus) Enum() *SwitchStatus {
	p := new(SwitchStatus)
	*p = x
	return p
}

func (x SwitchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwitchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[5].Descriptor()
}

func (SwitchStatus) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[5]
}

func (x SwitchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwitchStatus.Descriptor instead.
func (SwitchStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{5}
}

// 风险类型
type RiskInterceptType int32

const (
	RiskInterceptType_RIT_UNKNOWN RiskInterceptType = 0
	// mem
	RiskInterceptType_RIT_LoadingRemoteModules  RiskInterceptType = 1  // 加载远程模块
	RiskInterceptType_RIT_StackAttributeAttacks RiskInterceptType = 2  // 栈属性攻击
	RiskInterceptType_RIT_ExecuteCodeOnTheStack RiskInterceptType = 3  // 栈代码执行攻击
	RiskInterceptType_RIT_FilelessAttacks       RiskInterceptType = 15 // 反射式动态库注入-无文件攻击
	RiskInterceptType_RIT_HeapAttacksExploits   RiskInterceptType = 16 // 堆攻击
	RiskInterceptType_RIT_RopAttacks            RiskInterceptType = 24 // ROP攻击
	RiskInterceptType_RIT_LayoutShellcode       RiskInterceptType = 25 // 布局shellcode
	RiskInterceptType_RIT_StackPivot            RiskInterceptType = 40 // 栈翻转
	RiskInterceptType_RIT_RunningShellCode      RiskInterceptType = 41 // 执行shellcode
	RiskInterceptType_RIT_StartProcess          RiskInterceptType = 42 // 启动进程
	RiskInterceptType_RIT_EngineAttack          RiskInterceptType = 43 // 引擎攻击
	// file
	RiskInterceptType_RIT_Virus                           RiskInterceptType = 6  // 文件病毒
	RiskInterceptType_RIT_FH_DynamicLibraryHijacking      RiskInterceptType = 13 // 文件劫持（动态库劫持）
	RiskInterceptType_RIT_FH_EnvironmentVariableHijacking RiskInterceptType = 14 // 文件劫持（环境变量劫持）
	RiskInterceptType_RIT_FH_OpeSensitiveFile             RiskInterceptType = 47 // 操作敏感文件
	// sys
	RiskInterceptType_RIT_RemoteThreadInjection RiskInterceptType = 10 // 远线程注入
	RiskInterceptType_RIT_MiningProcess         RiskInterceptType = 11 // 可疑挖矿攻击
	RiskInterceptType_RIT_HiddenProcess         RiskInterceptType = 17 // 隐藏进程
	RiskInterceptType_RIT_SensitiveSubProc      RiskInterceptType = 44 // 启动敏感子进程
	RiskInterceptType_RIT_RegRisk               RiskInterceptType = 45 // 注册表风险
	RiskInterceptType_RIT_AppRisk               RiskInterceptType = 46 // 应用风险
	RiskInterceptType_RIT_KernelRisk            RiskInterceptType = 48 // 内核风险
	RiskInterceptType_RIT_BackShell             RiskInterceptType = 35 // 反弹Shell
	RiskInterceptType_RIT_ASR                   RiskInterceptType = 36 // 攻击面减少
	RiskInterceptType_RIT_HashLogin             RiskInterceptType = 37 // PTH
	RiskInterceptType_RIT_GoldenTicket          RiskInterceptType = 49 // 黄金票据
	RiskInterceptType_RIT_DirtyCow              RiskInterceptType = 50 // 脏牛漏洞
	RiskInterceptType_RIT_Rootkit_HiddenPort    RiskInterceptType = 18 // rootkit隐藏端口
	RiskInterceptType_RIT_Process_Privilege     RiskInterceptType = 51 // 进程提权
	// 4无签名pe文件 5文件加壳 19高风险进程，CPU或者IO高负载运行
	RiskInterceptType_RIT_KernelHideProc            RiskInterceptType = 52 // rootkit隐藏进程
	RiskInterceptType_RIT_KernelIntegrity           RiskInterceptType = 53 // 内核完整性校验
	RiskInterceptType_RIT_KernelRemoteLeakOverflow  RiskInterceptType = 54
	RiskInterceptType_RIT_KernelHideModule          RiskInterceptType = 55 // rootkit隐藏模块
	RiskInterceptType_RIT_WormRisk                  RiskInterceptType = 56
	RiskInterceptType_RIT_CVELeak                   RiskInterceptType = 57
	RiskInterceptType_RIT_LnkFileDetect             RiskInterceptType = 58
	RiskInterceptType_RIT_Rootkit_ReusePort         RiskInterceptType = 73 // 端口复用
	RiskInterceptType_RIT_KernelEXRisk              RiskInterceptType = 74
	RiskInterceptType_RIT_AccountRisk               RiskInterceptType = 75
	RiskInterceptType_RIT_CreatePuppetProc          RiskInterceptType = 77 //创建傀儡进程
	RiskInterceptType_RIT_ProcListenPortDType       RiskInterceptType = 81 // 非法监听端口
	RiskInterceptType_RIT_WebShellMemory            RiskInterceptType = 82 //内存webshell
	RiskInterceptType_RIT_DomainLogClear            RiskInterceptType = 140
	RiskInterceptType_RIT_DomainLogServiceClose     RiskInterceptType = 141
	RiskInterceptType_RIT_DomainLoginWithCred       RiskInterceptType = 142
	RiskInterceptType_RIT_DomainCreateDirSrvObj     RiskInterceptType = 143
	RiskInterceptType_RIT_DomainAddPlanTask         RiskInterceptType = 144
	RiskInterceptType_RIT_DomainAddSysSrv           RiskInterceptType = 145
	RiskInterceptType_RIT_DomainPsLoggedOn          RiskInterceptType = 146
	RiskInterceptType_RIT_DomainModiSenseGrp        RiskInterceptType = 147
	RiskInterceptType_RIT_DomainCve42278            RiskInterceptType = 148
	RiskInterceptType_RIT_DomainAclUpdate           RiskInterceptType = 149
	RiskInterceptType_RIT_DomainCve42287            RiskInterceptType = 150
	RiskInterceptType_RIT_DomainSAMRQryUsrGrp       RiskInterceptType = 151
	RiskInterceptType_RIT_DomainASREPRoast          RiskInterceptType = 152
	RiskInterceptType_RIT_ProcessEscalationToRoot   RiskInterceptType = 153 // linux 进程提权
	RiskInterceptType_RIT_TimedTaskEscalationToRoot RiskInterceptType = 154 // linux 疑似定时任务提权
	RiskInterceptType_RIT_HashAntivirus             RiskInterceptType = 155 // 恶意文件哈希检测
	RiskInterceptType_RIT_DomainEventLogCheck       RiskInterceptType = 156 //事件日志检测
	RiskInterceptType_RIT_DomainZeroLogon           RiskInterceptType = 157 //ZeroLogon漏洞
	// websehll static scanning
	RiskInterceptType_RIT_WebShellStaticCheck RiskInterceptType = 158 //webshell static scanning
	RiskInterceptType_RIT_AntiVirusDetect     RiskInterceptType = 160 //anti-virus detection
	// 内存马能力
	RiskInterceptType_RIT_JVMLoadSo                  RiskInterceptType = 103 //JVM进程加载.So库
	RiskInterceptType_RIT_JVMDragLib                 RiskInterceptType = 104 //JVM进程执行拖库行为
	RiskInterceptType_RIT_JVMUploadingFile           RiskInterceptType = 105 //JVM文件上传行为
	RiskInterceptType_RIT_JVMRegisterServerComponent RiskInterceptType = 114 //JVM注册服务器组件
	RiskInterceptType_RIT_JVMServerWebShellScan      RiskInterceptType = 115 //JVM应用服务器webShell扫描
	RiskInterceptType_RIT_JVMServerComponentScan     RiskInterceptType = 116 //JVM应用服务器已注册组件扫描
	RiskInterceptType_RIT_JVMFileOp                  RiskInterceptType = 117 //java读写敏感文件
	RiskInterceptType_RIT_JVMVirCmd                  RiskInterceptType = 118 //java虚拟终端
	RiskInterceptType_RIT_JVMScanPort                RiskInterceptType = 131
	RiskInterceptType_RIT_JVMJarLoad                 RiskInterceptType = 132
	RiskInterceptType_RIT_JVMZip                     RiskInterceptType = 133
	RiskInterceptType_RIT_JVMUnzip                   RiskInterceptType = 134
	RiskInterceptType_RIT_JVMHTTPParamsCheck         RiskInterceptType = 135
	RiskInterceptType_RIT_JVMSqlInject               RiskInterceptType = 136
	RiskInterceptType_RIT_JVMRunCmd                  RiskInterceptType = 300 //进程执行外部命令
	RiskInterceptType_RIT_PHPSystemCommand           RiskInterceptType = 323 //系统命令执行
	RiskInterceptType_RIT_PHPCodeExec                RiskInterceptType = 324 //PHP代码执行
	RiskInterceptType_RIT_PHPCallBackFunc            RiskInterceptType = 325 //回调函数利用
	RiskInterceptType_RIT_PHPUploadSuspiciousFiles   RiskInterceptType = 326 //上传可疑文件
	RiskInterceptType_RIT_PHPSensitiveFileAccess     RiskInterceptType = 327 //敏感文件访问
	RiskInterceptType_RIT_PHPExecSuspiciousFile      RiskInterceptType = 328 //执行可疑文件
	RiskInterceptType_RIT_AbnormalLoginRisk          RiskInterceptType = 600 // 异常登录
	RiskInterceptType_RIT_ViolentRisk                RiskInterceptType = 601 // 暴力破解
	RiskInterceptType_RIT_WeakPasswdRisk             RiskInterceptType = 602 // 弱密码
	// V01 拦截、仅上报
	RiskInterceptType_RIT_V01_FileMonitor           RiskInterceptType = 1001 // v01文件   检测 仅上报 拦截
	RiskInterceptType_RIT_V01_IllegalOutreach       RiskInterceptType = 1002 // v01 黑灰产 仅上报 拦截
	RiskInterceptType_RIT_V01_CreatePuppetProc      RiskInterceptType = 1003 // v01内存攻击 傀儡进程 近上报 拦截
	RiskInterceptType_RIT_V01_RopAttacks            RiskInterceptType = 1004 // v01内存攻击 ROP攻击 近上报 拦截
	RiskInterceptType_RIT_V01_BufferOverflow        RiskInterceptType = 1005 // v01内存攻击 缓冲区溢出
	RiskInterceptType_RIT_V01_HeapAttacksExploits   RiskInterceptType = 1006 // v01内存攻击 堆喷射
	RiskInterceptType_RIT_V01_HeapStackAttach       RiskInterceptType = 1007 // v01内存攻击 堆栈属性攻击
	RiskInterceptType_RIT_V01_LayoutShellcode       RiskInterceptType = 1008 // v01内存攻击 布局shellcode
	RiskInterceptType_RIT_V01_RunningShellCode      RiskInterceptType = 1009 // v01内存攻击 shellcode执行
	RiskInterceptType_RIT_V01_LoadingRemoteModules  RiskInterceptType = 1010 // v01内存攻击 执行远程模块
	RiskInterceptType_RIT_V01_StackPivot            RiskInterceptType = 1011 // v01内存攻击 栈翻转
	RiskInterceptType_RIT_V01_EngineAttack          RiskInterceptType = 1012 // v01内存攻击 引擎攻击防护
	RiskInterceptType_RIT_V01_Shell                 RiskInterceptType = 1013 // v01脚本攻击检测 脚本型
	RiskInterceptType_RIT_V01_KernelBehaviorTracing RiskInterceptType = 1014 // v01内存攻击 内核行为跟踪
	RiskInterceptType_RIT_V01_EvilDriver            RiskInterceptType = 1015 // v01内存攻击 恶意驱动
	RiskInterceptType_RIT_V01_LeakDriver            RiskInterceptType = 1016 // v01内存攻击 具有漏洞驱动
	RiskInterceptType_RIT_V01_ShellAttackPowerShell RiskInterceptType = 1017 // v01 白名单 脚本攻击 PowerShell
	RiskInterceptType_RIT_V01_ShellAttackWMI        RiskInterceptType = 1018 // v01 白名单 脚本攻击 WMI
	RiskInterceptType_RIT_V01_ShellAttackVBA        RiskInterceptType = 1019 // v01 白名单 脚本攻击 VBA
	RiskInterceptType_RIT_V01_ShellAttackVBS        RiskInterceptType = 1020 // v01 白名单 脚本攻击 VBS
	RiskInterceptType_RIT_V01_ShellAttackOther      RiskInterceptType = 1021 // v01 白名单 脚本攻击 其他
	RiskInterceptType_RIT_V01_Tunnel_ICMP           RiskInterceptType = 1022 // v01 隧道检测 ICMP
	RiskInterceptType_RIT_V01_Tunnel_DNS            RiskInterceptType = 1023 // v01 隧道检测 DNS
	RiskInterceptType_RIT_V01_ACDRSwitch            RiskInterceptType = 1024 // v01 linux acdr 总开关
	RiskInterceptType_RIT_V01_FileCreateLog         RiskInterceptType = 1025 // v01 全量文件创建 开关
	// 钓鱼增强
	RiskInterceptType_RIT_V01_FILE_SUSPICIOUS_TYPE RiskInterceptType = 1026 // v01 白名单 钓鱼文件可疑类型
	RiskInterceptType_RIT_V01_FILE_DDE_CODE        RiskInterceptType = 1027 // v01 白名单 钓鱼文件DDE代码
	RiskInterceptType_RIT_V01_FILE_VBA             RiskInterceptType = 1028 // v01 白名单 钓鱼文件VBA代码
	RiskInterceptType_RIT_V01_FILE_LNK_TARGET      RiskInterceptType = 1029 // v01 白名单 钓鱼文件LNK目标
	RiskInterceptType_RIT_V01_FILE_LNK_WORK        RiskInterceptType = 1030 // v01 白名单 钓鱼文件LNK工作目录
	RiskInterceptType_RIT_V01_FILE_LNK_CMD         RiskInterceptType = 1031 // v01 白名单 钓鱼文件LNK命令
	RiskInterceptType_RIT_V01_FILE_LNK_ICON        RiskInterceptType = 1032 // v01 白名单 钓鱼文件LNK图标
	RiskInterceptType_RIT_V01_FILE_URL             RiskInterceptType = 1033 // v01 白名单 钓鱼文件URL
)

// Enum value maps for RiskInterceptType.
var (
	RiskInterceptType_name = map[int32]string{
		0:    "RIT_UNKNOWN",
		1:    "RIT_LoadingRemoteModules",
		2:    "RIT_StackAttributeAttacks",
		3:    "RIT_ExecuteCodeOnTheStack",
		15:   "RIT_FilelessAttacks",
		16:   "RIT_HeapAttacksExploits",
		24:   "RIT_RopAttacks",
		25:   "RIT_LayoutShellcode",
		40:   "RIT_StackPivot",
		41:   "RIT_RunningShellCode",
		42:   "RIT_StartProcess",
		43:   "RIT_EngineAttack",
		6:    "RIT_Virus",
		13:   "RIT_FH_DynamicLibraryHijacking",
		14:   "RIT_FH_EnvironmentVariableHijacking",
		47:   "RIT_FH_OpeSensitiveFile",
		10:   "RIT_RemoteThreadInjection",
		11:   "RIT_MiningProcess",
		17:   "RIT_HiddenProcess",
		44:   "RIT_SensitiveSubProc",
		45:   "RIT_RegRisk",
		46:   "RIT_AppRisk",
		48:   "RIT_KernelRisk",
		35:   "RIT_BackShell",
		36:   "RIT_ASR",
		37:   "RIT_HashLogin",
		49:   "RIT_GoldenTicket",
		50:   "RIT_DirtyCow",
		18:   "RIT_Rootkit_HiddenPort",
		51:   "RIT_Process_Privilege",
		52:   "RIT_KernelHideProc",
		53:   "RIT_KernelIntegrity",
		54:   "RIT_KernelRemoteLeakOverflow",
		55:   "RIT_KernelHideModule",
		56:   "RIT_WormRisk",
		57:   "RIT_CVELeak",
		58:   "RIT_LnkFileDetect",
		73:   "RIT_Rootkit_ReusePort",
		74:   "RIT_KernelEXRisk",
		75:   "RIT_AccountRisk",
		77:   "RIT_CreatePuppetProc",
		81:   "RIT_ProcListenPortDType",
		82:   "RIT_WebShellMemory",
		140:  "RIT_DomainLogClear",
		141:  "RIT_DomainLogServiceClose",
		142:  "RIT_DomainLoginWithCred",
		143:  "RIT_DomainCreateDirSrvObj",
		144:  "RIT_DomainAddPlanTask",
		145:  "RIT_DomainAddSysSrv",
		146:  "RIT_DomainPsLoggedOn",
		147:  "RIT_DomainModiSenseGrp",
		148:  "RIT_DomainCve42278",
		149:  "RIT_DomainAclUpdate",
		150:  "RIT_DomainCve42287",
		151:  "RIT_DomainSAMRQryUsrGrp",
		152:  "RIT_DomainASREPRoast",
		153:  "RIT_ProcessEscalationToRoot",
		154:  "RIT_TimedTaskEscalationToRoot",
		155:  "RIT_HashAntivirus",
		156:  "RIT_DomainEventLogCheck",
		157:  "RIT_DomainZeroLogon",
		158:  "RIT_WebShellStaticCheck",
		160:  "RIT_AntiVirusDetect",
		103:  "RIT_JVMLoadSo",
		104:  "RIT_JVMDragLib",
		105:  "RIT_JVMUploadingFile",
		114:  "RIT_JVMRegisterServerComponent",
		115:  "RIT_JVMServerWebShellScan",
		116:  "RIT_JVMServerComponentScan",
		117:  "RIT_JVMFileOp",
		118:  "RIT_JVMVirCmd",
		131:  "RIT_JVMScanPort",
		132:  "RIT_JVMJarLoad",
		133:  "RIT_JVMZip",
		134:  "RIT_JVMUnzip",
		135:  "RIT_JVMHTTPParamsCheck",
		136:  "RIT_JVMSqlInject",
		300:  "RIT_JVMRunCmd",
		323:  "RIT_PHPSystemCommand",
		324:  "RIT_PHPCodeExec",
		325:  "RIT_PHPCallBackFunc",
		326:  "RIT_PHPUploadSuspiciousFiles",
		327:  "RIT_PHPSensitiveFileAccess",
		328:  "RIT_PHPExecSuspiciousFile",
		600:  "RIT_AbnormalLoginRisk",
		601:  "RIT_ViolentRisk",
		602:  "RIT_WeakPasswdRisk",
		1001: "RIT_V01_FileMonitor",
		1002: "RIT_V01_IllegalOutreach",
		1003: "RIT_V01_CreatePuppetProc",
		1004: "RIT_V01_RopAttacks",
		1005: "RIT_V01_BufferOverflow",
		1006: "RIT_V01_HeapAttacksExploits",
		1007: "RIT_V01_HeapStackAttach",
		1008: "RIT_V01_LayoutShellcode",
		1009: "RIT_V01_RunningShellCode",
		1010: "RIT_V01_LoadingRemoteModules",
		1011: "RIT_V01_StackPivot",
		1012: "RIT_V01_EngineAttack",
		1013: "RIT_V01_Shell",
		1014: "RIT_V01_KernelBehaviorTracing",
		1015: "RIT_V01_EvilDriver",
		1016: "RIT_V01_LeakDriver",
		1017: "RIT_V01_ShellAttackPowerShell",
		1018: "RIT_V01_ShellAttackWMI",
		1019: "RIT_V01_ShellAttackVBA",
		1020: "RIT_V01_ShellAttackVBS",
		1021: "RIT_V01_ShellAttackOther",
		1022: "RIT_V01_Tunnel_ICMP",
		1023: "RIT_V01_Tunnel_DNS",
		1024: "RIT_V01_ACDRSwitch",
		1025: "RIT_V01_FileCreateLog",
		1026: "RIT_V01_FILE_SUSPICIOUS_TYPE",
		1027: "RIT_V01_FILE_DDE_CODE",
		1028: "RIT_V01_FILE_VBA",
		1029: "RIT_V01_FILE_LNK_TARGET",
		1030: "RIT_V01_FILE_LNK_WORK",
		1031: "RIT_V01_FILE_LNK_CMD",
		1032: "RIT_V01_FILE_LNK_ICON",
		1033: "RIT_V01_FILE_URL",
	}
	RiskInterceptType_value = map[string]int32{
		"RIT_UNKNOWN":                         0,
		"RIT_LoadingRemoteModules":            1,
		"RIT_StackAttributeAttacks":           2,
		"RIT_ExecuteCodeOnTheStack":           3,
		"RIT_FilelessAttacks":                 15,
		"RIT_HeapAttacksExploits":             16,
		"RIT_RopAttacks":                      24,
		"RIT_LayoutShellcode":                 25,
		"RIT_StackPivot":                      40,
		"RIT_RunningShellCode":                41,
		"RIT_StartProcess":                    42,
		"RIT_EngineAttack":                    43,
		"RIT_Virus":                           6,
		"RIT_FH_DynamicLibraryHijacking":      13,
		"RIT_FH_EnvironmentVariableHijacking": 14,
		"RIT_FH_OpeSensitiveFile":             47,
		"RIT_RemoteThreadInjection":           10,
		"RIT_MiningProcess":                   11,
		"RIT_HiddenProcess":                   17,
		"RIT_SensitiveSubProc":                44,
		"RIT_RegRisk":                         45,
		"RIT_AppRisk":                         46,
		"RIT_KernelRisk":                      48,
		"RIT_BackShell":                       35,
		"RIT_ASR":                             36,
		"RIT_HashLogin":                       37,
		"RIT_GoldenTicket":                    49,
		"RIT_DirtyCow":                        50,
		"RIT_Rootkit_HiddenPort":              18,
		"RIT_Process_Privilege":               51,
		"RIT_KernelHideProc":                  52,
		"RIT_KernelIntegrity":                 53,
		"RIT_KernelRemoteLeakOverflow":        54,
		"RIT_KernelHideModule":                55,
		"RIT_WormRisk":                        56,
		"RIT_CVELeak":                         57,
		"RIT_LnkFileDetect":                   58,
		"RIT_Rootkit_ReusePort":               73,
		"RIT_KernelEXRisk":                    74,
		"RIT_AccountRisk":                     75,
		"RIT_CreatePuppetProc":                77,
		"RIT_ProcListenPortDType":             81,
		"RIT_WebShellMemory":                  82,
		"RIT_DomainLogClear":                  140,
		"RIT_DomainLogServiceClose":           141,
		"RIT_DomainLoginWithCred":             142,
		"RIT_DomainCreateDirSrvObj":           143,
		"RIT_DomainAddPlanTask":               144,
		"RIT_DomainAddSysSrv":                 145,
		"RIT_DomainPsLoggedOn":                146,
		"RIT_DomainModiSenseGrp":              147,
		"RIT_DomainCve42278":                  148,
		"RIT_DomainAclUpdate":                 149,
		"RIT_DomainCve42287":                  150,
		"RIT_DomainSAMRQryUsrGrp":             151,
		"RIT_DomainASREPRoast":                152,
		"RIT_ProcessEscalationToRoot":         153,
		"RIT_TimedTaskEscalationToRoot":       154,
		"RIT_HashAntivirus":                   155,
		"RIT_DomainEventLogCheck":             156,
		"RIT_DomainZeroLogon":                 157,
		"RIT_WebShellStaticCheck":             158,
		"RIT_AntiVirusDetect":                 160,
		"RIT_JVMLoadSo":                       103,
		"RIT_JVMDragLib":                      104,
		"RIT_JVMUploadingFile":                105,
		"RIT_JVMRegisterServerComponent":      114,
		"RIT_JVMServerWebShellScan":           115,
		"RIT_JVMServerComponentScan":          116,
		"RIT_JVMFileOp":                       117,
		"RIT_JVMVirCmd":                       118,
		"RIT_JVMScanPort":                     131,
		"RIT_JVMJarLoad":                      132,
		"RIT_JVMZip":                          133,
		"RIT_JVMUnzip":                        134,
		"RIT_JVMHTTPParamsCheck":              135,
		"RIT_JVMSqlInject":                    136,
		"RIT_JVMRunCmd":                       300,
		"RIT_PHPSystemCommand":                323,
		"RIT_PHPCodeExec":                     324,
		"RIT_PHPCallBackFunc":                 325,
		"RIT_PHPUploadSuspiciousFiles":        326,
		"RIT_PHPSensitiveFileAccess":          327,
		"RIT_PHPExecSuspiciousFile":           328,
		"RIT_AbnormalLoginRisk":               600,
		"RIT_ViolentRisk":                     601,
		"RIT_WeakPasswdRisk":                  602,
		"RIT_V01_FileMonitor":                 1001,
		"RIT_V01_IllegalOutreach":             1002,
		"RIT_V01_CreatePuppetProc":            1003,
		"RIT_V01_RopAttacks":                  1004,
		"RIT_V01_BufferOverflow":              1005,
		"RIT_V01_HeapAttacksExploits":         1006,
		"RIT_V01_HeapStackAttach":             1007,
		"RIT_V01_LayoutShellcode":             1008,
		"RIT_V01_RunningShellCode":            1009,
		"RIT_V01_LoadingRemoteModules":        1010,
		"RIT_V01_StackPivot":                  1011,
		"RIT_V01_EngineAttack":                1012,
		"RIT_V01_Shell":                       1013,
		"RIT_V01_KernelBehaviorTracing":       1014,
		"RIT_V01_EvilDriver":                  1015,
		"RIT_V01_LeakDriver":                  1016,
		"RIT_V01_ShellAttackPowerShell":       1017,
		"RIT_V01_ShellAttackWMI":              1018,
		"RIT_V01_ShellAttackVBA":              1019,
		"RIT_V01_ShellAttackVBS":              1020,
		"RIT_V01_ShellAttackOther":            1021,
		"RIT_V01_Tunnel_ICMP":                 1022,
		"RIT_V01_Tunnel_DNS":                  1023,
		"RIT_V01_ACDRSwitch":                  1024,
		"RIT_V01_FileCreateLog":               1025,
		"RIT_V01_FILE_SUSPICIOUS_TYPE":        1026,
		"RIT_V01_FILE_DDE_CODE":               1027,
		"RIT_V01_FILE_VBA":                    1028,
		"RIT_V01_FILE_LNK_TARGET":             1029,
		"RIT_V01_FILE_LNK_WORK":               1030,
		"RIT_V01_FILE_LNK_CMD":                1031,
		"RIT_V01_FILE_LNK_ICON":               1032,
		"RIT_V01_FILE_URL":                    1033,
	}
)

func (x RiskInterceptType) Enum() *RiskInterceptType {
	p := new(RiskInterceptType)
	*p = x
	return p
}

func (x RiskInterceptType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskInterceptType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[6].Descriptor()
}

func (RiskInterceptType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[6]
}

func (x RiskInterceptType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskInterceptType.Descriptor instead.
func (RiskInterceptType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{6}
}

type NGAVCollectorType int32

const (
	NGAVCollectorType_NCT_UNKNOWN NGAVCollectorType = 0
	NGAVCollectorType_NCT_FILE    NGAVCollectorType = 1
	NGAVCollectorType_NCT_NET     NGAVCollectorType = 2
	NGAVCollectorType_NCT_PROCESS NGAVCollectorType = 3
	NGAVCollectorType_NCT_MEM     NGAVCollectorType = 4
	NGAVCollectorType_NCT_MISC    NGAVCollectorType = 5
)

// Enum value maps for NGAVCollectorType.
var (
	NGAVCollectorType_name = map[int32]string{
		0: "NCT_UNKNOWN",
		1: "NCT_FILE",
		2: "NCT_NET",
		3: "NCT_PROCESS",
		4: "NCT_MEM",
		5: "NCT_MISC",
	}
	NGAVCollectorType_value = map[string]int32{
		"NCT_UNKNOWN": 0,
		"NCT_FILE":    1,
		"NCT_NET":     2,
		"NCT_PROCESS": 3,
		"NCT_MEM":     4,
		"NCT_MISC":    5,
	}
)

func (x NGAVCollectorType) Enum() *NGAVCollectorType {
	p := new(NGAVCollectorType)
	*p = x
	return p
}

func (x NGAVCollectorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NGAVCollectorType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[7].Descriptor()
}

func (NGAVCollectorType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[7]
}

func (x NGAVCollectorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NGAVCollectorType.Descriptor instead.
func (NGAVCollectorType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{7}
}

// PTH->pass the hash
type PTHInterceptMode int32

const (
	PTHInterceptMode_PTH_Unknown         PTHInterceptMode = 0 // 不拦截
	PTHInterceptMode_PTH_SpecifyUser     PTHInterceptMode = 1 // 拦截指定用户名，如果用户名为空，不拦截
	PTHInterceptMode_PTH_AllUser         PTHInterceptMode = 2 // 拦截所有用户
	PTHInterceptMode_PTH_Admin           PTHInterceptMode = 3 // 拦截管理员
	PTHInterceptMode_PTH_SpecifyAndAdmin PTHInterceptMode = 4 // 拦截指定用户名和管理员
)

// Enum value maps for PTHInterceptMode.
var (
	PTHInterceptMode_name = map[int32]string{
		0: "PTH_Unknown",
		1: "PTH_SpecifyUser",
		2: "PTH_AllUser",
		3: "PTH_Admin",
		4: "PTH_SpecifyAndAdmin",
	}
	PTHInterceptMode_value = map[string]int32{
		"PTH_Unknown":         0,
		"PTH_SpecifyUser":     1,
		"PTH_AllUser":         2,
		"PTH_Admin":           3,
		"PTH_SpecifyAndAdmin": 4,
	}
)

func (x PTHInterceptMode) Enum() *PTHInterceptMode {
	p := new(PTHInterceptMode)
	*p = x
	return p
}

func (x PTHInterceptMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PTHInterceptMode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[8].Descriptor()
}

func (PTHInterceptMode) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[8]
}

func (x PTHInterceptMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PTHInterceptMode.Descriptor instead.
func (PTHInterceptMode) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{8}
}

// 扫描类型
type TaskScanType int32

const (
	TaskScanType_SO_UNKNOWN TaskScanType = 0
	TaskScanType_SO_CYCLE   TaskScanType = 1 // 定时执行
	TaskScanType_SO_MANUAL  TaskScanType = 2 // 手动触发
)

// Enum value maps for TaskScanType.
var (
	TaskScanType_name = map[int32]string{
		0: "SO_UNKNOWN",
		1: "SO_CYCLE",
		2: "SO_MANUAL",
	}
	TaskScanType_value = map[string]int32{
		"SO_UNKNOWN": 0,
		"SO_CYCLE":   1,
		"SO_MANUAL":  2,
	}
)

func (x TaskScanType) Enum() *TaskScanType {
	p := new(TaskScanType)
	*p = x
	return p
}

func (x TaskScanType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaskScanType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[9].Descriptor()
}

func (TaskScanType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[9]
}

func (x TaskScanType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaskScanType.Descriptor instead.
func (TaskScanType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{9}
}

// 检测结果
type VirusDetectResult int32

const (
	VirusDetectResult_RESULT_UNKNOWN VirusDetectResult = 0
	VirusDetectResult_RESULT_BLACK   VirusDetectResult = 1
	VirusDetectResult_RESULT_WHITE   VirusDetectResult = 2
	VirusDetectResult_RESULT_TIMEOUT VirusDetectResult = 3
	VirusDetectResult_RESULT_FAIL    VirusDetectResult = 4
)

// Enum value maps for VirusDetectResult.
var (
	VirusDetectResult_name = map[int32]string{
		0: "RESULT_UNKNOWN",
		1: "RESULT_BLACK",
		2: "RESULT_WHITE",
		3: "RESULT_TIMEOUT",
		4: "RESULT_FAIL",
	}
	VirusDetectResult_value = map[string]int32{
		"RESULT_UNKNOWN": 0,
		"RESULT_BLACK":   1,
		"RESULT_WHITE":   2,
		"RESULT_TIMEOUT": 3,
		"RESULT_FAIL":    4,
	}
)

func (x VirusDetectResult) Enum() *VirusDetectResult {
	p := new(VirusDetectResult)
	*p = x
	return p
}

func (x VirusDetectResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VirusDetectResult) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[10].Descriptor()
}

func (VirusDetectResult) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[10]
}

func (x VirusDetectResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VirusDetectResult.Descriptor instead.
func (VirusDetectResult) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{10}
}

// 检测优先级
type VirusDetectPriority int32

const (
	VirusDetectPriority_DETECT_PRIORITY_LOW    VirusDetectPriority = 0
	VirusDetectPriority_DETECT_PRIORITY_MIDDLE VirusDetectPriority = 1
	VirusDetectPriority_DETECT_PRIORITY_HIGH   VirusDetectPriority = 2
)

// Enum value maps for VirusDetectPriority.
var (
	VirusDetectPriority_name = map[int32]string{
		0: "DETECT_PRIORITY_LOW",
		1: "DETECT_PRIORITY_MIDDLE",
		2: "DETECT_PRIORITY_HIGH",
	}
	VirusDetectPriority_value = map[string]int32{
		"DETECT_PRIORITY_LOW":    0,
		"DETECT_PRIORITY_MIDDLE": 1,
		"DETECT_PRIORITY_HIGH":   2,
	}
)

func (x VirusDetectPriority) Enum() *VirusDetectPriority {
	p := new(VirusDetectPriority)
	*p = x
	return p
}

func (x VirusDetectPriority) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VirusDetectPriority) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[11].Descriptor()
}

func (VirusDetectPriority) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[11]
}

func (x VirusDetectPriority) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VirusDetectPriority.Descriptor instead.
func (VirusDetectPriority) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{11}
}

type FileStatus int32

const (
	FileStatus_FILE_NOT_FOUND FileStatus = 0
	FileStatus_FILE_FOUND     FileStatus = 1
)

// Enum value maps for FileStatus.
var (
	FileStatus_name = map[int32]string{
		0: "FILE_NOT_FOUND",
		1: "FILE_FOUND",
	}
	FileStatus_value = map[string]int32{
		"FILE_NOT_FOUND": 0,
		"FILE_FOUND":     1,
	}
)

func (x FileStatus) Enum() *FileStatus {
	p := new(FileStatus)
	*p = x
	return p
}

func (x FileStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[12].Descriptor()
}

func (FileStatus) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[12]
}

func (x FileStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileStatus.Descriptor instead.
func (FileStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{12}
}

type DetectKindIdent int32

const (
	DetectKindIdent_DETECT_KIND_UNKNOWN  DetectKindIdent = 0
	DetectKindIdent_DETECT_KIND_WEBSHELL DetectKindIdent = 1
	DetectKindIdent_DETECT_KIND_VIRUS    DetectKindIdent = 2
)

// Enum value maps for DetectKindIdent.
var (
	DetectKindIdent_name = map[int32]string{
		0: "DETECT_KIND_UNKNOWN",
		1: "DETECT_KIND_WEBSHELL",
		2: "DETECT_KIND_VIRUS",
	}
	DetectKindIdent_value = map[string]int32{
		"DETECT_KIND_UNKNOWN":  0,
		"DETECT_KIND_WEBSHELL": 1,
		"DETECT_KIND_VIRUS":    2,
	}
)

func (x DetectKindIdent) Enum() *DetectKindIdent {
	p := new(DetectKindIdent)
	*p = x
	return p
}

func (x DetectKindIdent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectKindIdent) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[13].Descriptor()
}

func (DetectKindIdent) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[13]
}

func (x DetectKindIdent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectKindIdent.Descriptor instead.
func (DetectKindIdent) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{13}
}

type AgentFileKind int32

const (
	AgentFileKind_AGENT_FILE_KIND_UNKNOWN  AgentFileKind = 0
	AgentFileKind_AGENT_FILE_KIND_CLASS    AgentFileKind = 1 // 类文件
	AgentFileKind_AGENT_FILE_KIND_WEBSHELL AgentFileKind = 2 // webshell文件
	AgentFileKind_AGENT_FILE_KIND_NGAV     AgentFileKind = 3 // ngav文件
)

// Enum value maps for AgentFileKind.
var (
	AgentFileKind_name = map[int32]string{
		0: "AGENT_FILE_KIND_UNKNOWN",
		1: "AGENT_FILE_KIND_CLASS",
		2: "AGENT_FILE_KIND_WEBSHELL",
		3: "AGENT_FILE_KIND_NGAV",
	}
	AgentFileKind_value = map[string]int32{
		"AGENT_FILE_KIND_UNKNOWN":  0,
		"AGENT_FILE_KIND_CLASS":    1,
		"AGENT_FILE_KIND_WEBSHELL": 2,
		"AGENT_FILE_KIND_NGAV":     3,
	}
)

func (x AgentFileKind) Enum() *AgentFileKind {
	p := new(AgentFileKind)
	*p = x
	return p
}

func (x AgentFileKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentFileKind) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[14].Descriptor()
}

func (AgentFileKind) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[14]
}

func (x AgentFileKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentFileKind.Descriptor instead.
func (AgentFileKind) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{14}
}

type AgentFileStatus int32

const (
	AgentFileStatus_AGENT_FILE_STATUS_UNKNOWN      AgentFileStatus = 0
	AgentFileStatus_AGENT_FILE_STATUS_NOT_FOUND    AgentFileStatus = 1
	AgentFileStatus_AGENT_FILE_STATUS_FOUND        AgentFileStatus = 2
	AgentFileStatus_AGENT_FILE_STATUS_DUMP_FAIL    AgentFileStatus = 3
	AgentFileStatus_AGENT_FILE_STATUS_UPLOAD_FAIL  AgentFileStatus = 4
	AgentFileStatus_AGENT_FILE_STATUS_SIZE_OVERRUN AgentFileStatus = 5
)

// Enum value maps for AgentFileStatus.
var (
	AgentFileStatus_name = map[int32]string{
		0: "AGENT_FILE_STATUS_UNKNOWN",
		1: "AGENT_FILE_STATUS_NOT_FOUND",
		2: "AGENT_FILE_STATUS_FOUND",
		3: "AGENT_FILE_STATUS_DUMP_FAIL",
		4: "AGENT_FILE_STATUS_UPLOAD_FAIL",
		5: "AGENT_FILE_STATUS_SIZE_OVERRUN",
	}
	AgentFileStatus_value = map[string]int32{
		"AGENT_FILE_STATUS_UNKNOWN":      0,
		"AGENT_FILE_STATUS_NOT_FOUND":    1,
		"AGENT_FILE_STATUS_FOUND":        2,
		"AGENT_FILE_STATUS_DUMP_FAIL":    3,
		"AGENT_FILE_STATUS_UPLOAD_FAIL":  4,
		"AGENT_FILE_STATUS_SIZE_OVERRUN": 5,
	}
)

func (x AgentFileStatus) Enum() *AgentFileStatus {
	p := new(AgentFileStatus)
	*p = x
	return p
}

func (x AgentFileStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentFileStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[15].Descriptor()
}

func (AgentFileStatus) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[15]
}

func (x AgentFileStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentFileStatus.Descriptor instead.
func (AgentFileStatus) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{15}
}

type V01FileType int32

const (
	V01FileType_V01_FILE_TYPE_UNKNOWN V01FileType = 0
	V01FileType_V01_FILE_TYPE_EXEC    V01FileType = 1 // EXE(可执行程序)
	V01FileType_V01_FILE_TYPE_DOC     V01FileType = 2 // DOC/DOCX/DOCM
	V01FileType_V01_FILE_TYPE_XLS     V01FileType = 3 // XLS/XLSX
	V01FileType_V01_FILE_TYPE_PDF     V01FileType = 4 // PDF
	V01FileType_V01_FILE_TYPE_PPT     V01FileType = 5 // PPT/PPTX/PPTM
	V01FileType_V01_FILE_TYPE_DLL     V01FileType = 6
	V01FileType_V01_FILE_TYPE_BAT     V01FileType = 7
	V01FileType_V01_FILE_TYPE_MSI     V01FileType = 8
	V01FileType_V01_FILE_TYPE_ODT     V01FileType = 9
	V01FileType_V01_FILE_TYPE_VB      V01FileType = 10
	V01FileType_V01_FILE_TYPE_JS      V01FileType = 11
	V01FileType_V01_FILE_TYPE_PS      V01FileType = 12
	V01FileType_V01_FILE_TYPE_PY      V01FileType = 13
	V01FileType_V01_FILE_TYPE_SH      V01FileType = 14 // Linux V01 新增Shell类型
	V01FileType_V01_FILE_TYPE_LNK     V01FileType = 15 // Windows V01 新增链接类型
	V01FileType_V01_FILE_TYPE_ARCHIVE V01FileType = 16 // 新增压缩包类型
)

// Enum value maps for V01FileType.
var (
	V01FileType_name = map[int32]string{
		0:  "V01_FILE_TYPE_UNKNOWN",
		1:  "V01_FILE_TYPE_EXEC",
		2:  "V01_FILE_TYPE_DOC",
		3:  "V01_FILE_TYPE_XLS",
		4:  "V01_FILE_TYPE_PDF",
		5:  "V01_FILE_TYPE_PPT",
		6:  "V01_FILE_TYPE_DLL",
		7:  "V01_FILE_TYPE_BAT",
		8:  "V01_FILE_TYPE_MSI",
		9:  "V01_FILE_TYPE_ODT",
		10: "V01_FILE_TYPE_VB",
		11: "V01_FILE_TYPE_JS",
		12: "V01_FILE_TYPE_PS",
		13: "V01_FILE_TYPE_PY",
		14: "V01_FILE_TYPE_SH",
		15: "V01_FILE_TYPE_LNK",
		16: "V01_FILE_TYPE_ARCHIVE",
	}
	V01FileType_value = map[string]int32{
		"V01_FILE_TYPE_UNKNOWN": 0,
		"V01_FILE_TYPE_EXEC":    1,
		"V01_FILE_TYPE_DOC":     2,
		"V01_FILE_TYPE_XLS":     3,
		"V01_FILE_TYPE_PDF":     4,
		"V01_FILE_TYPE_PPT":     5,
		"V01_FILE_TYPE_DLL":     6,
		"V01_FILE_TYPE_BAT":     7,
		"V01_FILE_TYPE_MSI":     8,
		"V01_FILE_TYPE_ODT":     9,
		"V01_FILE_TYPE_VB":      10,
		"V01_FILE_TYPE_JS":      11,
		"V01_FILE_TYPE_PS":      12,
		"V01_FILE_TYPE_PY":      13,
		"V01_FILE_TYPE_SH":      14,
		"V01_FILE_TYPE_LNK":     15,
		"V01_FILE_TYPE_ARCHIVE": 16,
	}
)

func (x V01FileType) Enum() *V01FileType {
	p := new(V01FileType)
	*p = x
	return p
}

func (x V01FileType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (V01FileType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[16].Descriptor()
}

func (V01FileType) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[16]
}

func (x V01FileType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use V01FileType.Descriptor instead.
func (V01FileType) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{16}
}

type V01FileScanConfig int32

const (
	V01FileScanConfig_V01_FILE_SCAN_CONFIG_UNKNOWN     V01FileScanConfig = 0
	V01FileScanConfig_V01_FILE_SCAN_CONFIG_RECOMMENDED V01FileScanConfig = 1
	V01FileScanConfig_V01_FILE_SCAN_CONFIG_STATIC      V01FileScanConfig = 2
	V01FileScanConfig_V01_FILE_SCAN_CONFIG_FULL        V01FileScanConfig = 3
	V01FileScanConfig_V01_FILE_SCAN_CONFIG_CUSTOM      V01FileScanConfig = 4
)

// Enum value maps for V01FileScanConfig.
var (
	V01FileScanConfig_name = map[int32]string{
		0: "V01_FILE_SCAN_CONFIG_UNKNOWN",
		1: "V01_FILE_SCAN_CONFIG_RECOMMENDED",
		2: "V01_FILE_SCAN_CONFIG_STATIC",
		3: "V01_FILE_SCAN_CONFIG_FULL",
		4: "V01_FILE_SCAN_CONFIG_CUSTOM",
	}
	V01FileScanConfig_value = map[string]int32{
		"V01_FILE_SCAN_CONFIG_UNKNOWN":     0,
		"V01_FILE_SCAN_CONFIG_RECOMMENDED": 1,
		"V01_FILE_SCAN_CONFIG_STATIC":      2,
		"V01_FILE_SCAN_CONFIG_FULL":        3,
		"V01_FILE_SCAN_CONFIG_CUSTOM":      4,
	}
)

func (x V01FileScanConfig) Enum() *V01FileScanConfig {
	p := new(V01FileScanConfig)
	*p = x
	return p
}

func (x V01FileScanConfig) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (V01FileScanConfig) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[17].Descriptor()
}

func (V01FileScanConfig) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[17]
}

func (x V01FileScanConfig) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use V01FileScanConfig.Descriptor instead.
func (V01FileScanConfig) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{17}
}

type GetProcPathPidsResult int32

const (
	GetProcPathPidsResult_GET_PROC_PATH_PIDS_UNKNOWN GetProcPathPidsResult = 0
	GetProcPathPidsResult_GET_PROC_PATH_PIDS_SUCC    GetProcPathPidsResult = 1
	GetProcPathPidsResult_GET_PROC_PATH_PIDS_FAIL    GetProcPathPidsResult = 2
)

// Enum value maps for GetProcPathPidsResult.
var (
	GetProcPathPidsResult_name = map[int32]string{
		0: "GET_PROC_PATH_PIDS_UNKNOWN",
		1: "GET_PROC_PATH_PIDS_SUCC",
		2: "GET_PROC_PATH_PIDS_FAIL",
	}
	GetProcPathPidsResult_value = map[string]int32{
		"GET_PROC_PATH_PIDS_UNKNOWN": 0,
		"GET_PROC_PATH_PIDS_SUCC":    1,
		"GET_PROC_PATH_PIDS_FAIL":    2,
	}
)

func (x GetProcPathPidsResult) Enum() *GetProcPathPidsResult {
	p := new(GetProcPathPidsResult)
	*p = x
	return p
}

func (x GetProcPathPidsResult) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetProcPathPidsResult) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[18].Descriptor()
}

func (GetProcPathPidsResult) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[18]
}

func (x GetProcPathPidsResult) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetProcPathPidsResult.Descriptor instead.
func (GetProcPathPidsResult) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{18}
}

type ThreatenHandleRes int32

const (
	ThreatenHandleRes_THREATEN_HANDLE_RESULT_UNKNOWN ThreatenHandleRes = 0
	ThreatenHandleRes_THREATEN_HANDLE_RESULT_SUCC    ThreatenHandleRes = 1
	ThreatenHandleRes_THREATEN_HANDLE_RESULT_FAIL    ThreatenHandleRes = 2
)

// Enum value maps for ThreatenHandleRes.
var (
	ThreatenHandleRes_name = map[int32]string{
		0: "THREATEN_HANDLE_RESULT_UNKNOWN",
		1: "THREATEN_HANDLE_RESULT_SUCC",
		2: "THREATEN_HANDLE_RESULT_FAIL",
	}
	ThreatenHandleRes_value = map[string]int32{
		"THREATEN_HANDLE_RESULT_UNKNOWN": 0,
		"THREATEN_HANDLE_RESULT_SUCC":    1,
		"THREATEN_HANDLE_RESULT_FAIL":    2,
	}
)

func (x ThreatenHandleRes) Enum() *ThreatenHandleRes {
	p := new(ThreatenHandleRes)
	*p = x
	return p
}

func (x ThreatenHandleRes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ThreatenHandleRes) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[19].Descriptor()
}

func (ThreatenHandleRes) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[19]
}

func (x ThreatenHandleRes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ThreatenHandleRes.Descriptor instead.
func (ThreatenHandleRes) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{19}
}

// webshell 和 杀毒 agent任务数据上报 -- start
type FileVirusKind int32

const (
	FileVirusKind_FILE_KIND_NULL      FileVirusKind = 0
	FileVirusKind_FILE_KIND_WEBSHELL  FileVirusKind = 1
	FileVirusKind_FILE_KIND_ANTIVIRUS FileVirusKind = 2
)

// Enum value maps for FileVirusKind.
var (
	FileVirusKind_name = map[int32]string{
		0: "FILE_KIND_NULL",
		1: "FILE_KIND_WEBSHELL",
		2: "FILE_KIND_ANTIVIRUS",
	}
	FileVirusKind_value = map[string]int32{
		"FILE_KIND_NULL":      0,
		"FILE_KIND_WEBSHELL":  1,
		"FILE_KIND_ANTIVIRUS": 2,
	}
)

func (x FileVirusKind) Enum() *FileVirusKind {
	p := new(FileVirusKind)
	*p = x
	return p
}

func (x FileVirusKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileVirusKind) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[20].Descriptor()
}

func (FileVirusKind) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[20]
}

func (x FileVirusKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileVirusKind.Descriptor instead.
func (FileVirusKind) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{20}
}

// 从神甲Linux移植过来
type SwitchStatusEnum int32

const (
	SwitchStatusEnum_NULL_STATUS  SwitchStatusEnum = 0 // 默认无任何操作
	SwitchStatusEnum_START_STATUS SwitchStatusEnum = 1 // 开启操作标识
	SwitchStatusEnum_STOP_STATUS  SwitchStatusEnum = 2 // 关闭操作标识
)

// Enum value maps for SwitchStatusEnum.
var (
	SwitchStatusEnum_name = map[int32]string{
		0: "NULL_STATUS",
		1: "START_STATUS",
		2: "STOP_STATUS",
	}
	SwitchStatusEnum_value = map[string]int32{
		"NULL_STATUS":  0,
		"START_STATUS": 1,
		"STOP_STATUS":  2,
	}
)

func (x SwitchStatusEnum) Enum() *SwitchStatusEnum {
	p := new(SwitchStatusEnum)
	*p = x
	return p
}

func (x SwitchStatusEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SwitchStatusEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[21].Descriptor()
}

func (SwitchStatusEnum) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[21]
}

func (x SwitchStatusEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SwitchStatusEnum.Descriptor instead.
func (SwitchStatusEnum) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{21}
}

type LonginTimes_Frequency int32

const (
	LonginTimes_INVALID     LonginTimes_Frequency = 0
	LonginTimes_EVERY_DAY   LonginTimes_Frequency = 1
	LonginTimes_EVERY_WEAK  LonginTimes_Frequency = 2
	LonginTimes_EVERY_MONTH LonginTimes_Frequency = 3
)

// Enum value maps for LonginTimes_Frequency.
var (
	LonginTimes_Frequency_name = map[int32]string{
		0: "INVALID",
		1: "EVERY_DAY",
		2: "EVERY_WEAK",
		3: "EVERY_MONTH",
	}
	LonginTimes_Frequency_value = map[string]int32{
		"INVALID":     0,
		"EVERY_DAY":   1,
		"EVERY_WEAK":  2,
		"EVERY_MONTH": 3,
	}
)

func (x LonginTimes_Frequency) Enum() *LonginTimes_Frequency {
	p := new(LonginTimes_Frequency)
	*p = x
	return p
}

func (x LonginTimes_Frequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LonginTimes_Frequency) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[22].Descriptor()
}

func (LonginTimes_Frequency) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[22]
}

func (x LonginTimes_Frequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LonginTimes_Frequency.Descriptor instead.
func (LonginTimes_Frequency) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{15, 0}
}

type AbnormalLoginConfig_StudyMode int32

const (
	AbnormalLoginConfig_Undefined AbnormalLoginConfig_StudyMode = 0
	AbnormalLoginConfig_AutoStudy AbnormalLoginConfig_StudyMode = 1 // 自动学习
	AbnormalLoginConfig_Custom    AbnormalLoginConfig_StudyMode = 2 // 自定义规则
)

// Enum value maps for AbnormalLoginConfig_StudyMode.
var (
	AbnormalLoginConfig_StudyMode_name = map[int32]string{
		0: "Undefined",
		1: "AutoStudy",
		2: "Custom",
	}
	AbnormalLoginConfig_StudyMode_value = map[string]int32{
		"Undefined": 0,
		"AutoStudy": 1,
		"Custom":    2,
	}
)

func (x AbnormalLoginConfig_StudyMode) Enum() *AbnormalLoginConfig_StudyMode {
	p := new(AbnormalLoginConfig_StudyMode)
	*p = x
	return p
}

func (x AbnormalLoginConfig_StudyMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AbnormalLoginConfig_StudyMode) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[23].Descriptor()
}

func (AbnormalLoginConfig_StudyMode) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[23]
}

func (x AbnormalLoginConfig_StudyMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AbnormalLoginConfig_StudyMode.Descriptor instead.
func (AbnormalLoginConfig_StudyMode) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{16, 0}
}

type BWRuleDetail_Scope int32

const (
	BWRuleDetail_Undefined BWRuleDetail_Scope = 0
	BWRuleDetail_AllOf     BWRuleDetail_Scope = 1 // 匹配所有: "且" 条件
	BWRuleDetail_OneOf     BWRuleDetail_Scope = 2 // 匹配任一: "或" 条件
)

// Enum value maps for BWRuleDetail_Scope.
var (
	BWRuleDetail_Scope_name = map[int32]string{
		0: "Undefined",
		1: "AllOf",
		2: "OneOf",
	}
	BWRuleDetail_Scope_value = map[string]int32{
		"Undefined": 0,
		"AllOf":     1,
		"OneOf":     2,
	}
)

func (x BWRuleDetail_Scope) Enum() *BWRuleDetail_Scope {
	p := new(BWRuleDetail_Scope)
	*p = x
	return p
}

func (x BWRuleDetail_Scope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BWRuleDetail_Scope) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[24].Descriptor()
}

func (BWRuleDetail_Scope) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[24]
}

func (x BWRuleDetail_Scope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BWRuleDetail_Scope.Descriptor instead.
func (BWRuleDetail_Scope) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{18, 0}
}

type TimeScheduled_Frequency int32

const (
	TimeScheduled_SET_INVALID     TimeScheduled_Frequency = 0
	TimeScheduled_SET_EVERY_DAY   TimeScheduled_Frequency = 1
	TimeScheduled_SET_EVERY_WEAK  TimeScheduled_Frequency = 2
	TimeScheduled_SET_EVERY_MONTH TimeScheduled_Frequency = 3
)

// Enum value maps for TimeScheduled_Frequency.
var (
	TimeScheduled_Frequency_name = map[int32]string{
		0: "SET_INVALID",
		1: "SET_EVERY_DAY",
		2: "SET_EVERY_WEAK",
		3: "SET_EVERY_MONTH",
	}
	TimeScheduled_Frequency_value = map[string]int32{
		"SET_INVALID":     0,
		"SET_EVERY_DAY":   1,
		"SET_EVERY_WEAK":  2,
		"SET_EVERY_MONTH": 3,
	}
)

func (x TimeScheduled_Frequency) Enum() *TimeScheduled_Frequency {
	p := new(TimeScheduled_Frequency)
	*p = x
	return p
}

func (x TimeScheduled_Frequency) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeScheduled_Frequency) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_policy_proto_enumTypes[25].Descriptor()
}

func (TimeScheduled_Frequency) Type() protoreflect.EnumType {
	return &file_agent_policy_proto_enumTypes[25]
}

func (x TimeScheduled_Frequency) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeScheduled_Frequency.Descriptor instead.
func (TimeScheduled_Frequency) EnumDescriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{25, 0}
}

// 黑白名单项的内容
type BlackWhiteItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Module     ModuleType `protobuf:"varint,1,opt,name=module,proto3,enum=agent.ModuleType" json:"module,omitempty"` // 废弃不用
	UniqueFlag []byte     `protobuf:"bytes,2,opt,name=uniqueFlag,proto3" json:"uniqueFlag,omitempty"`
	BwType     BWType     `protobuf:"varint,3,opt,name=bwType,proto3,enum=agent.BWType" json:"bwType,omitempty"`
	AddOrDel   Operate    `protobuf:"varint,4,opt,name=addOrDel,proto3,enum=agent.Operate" json:"addOrDel,omitempty"`
}

func (x *BlackWhiteItem) Reset() {
	*x = BlackWhiteItem{}
	mi := &file_agent_policy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlackWhiteItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlackWhiteItem) ProtoMessage() {}

func (x *BlackWhiteItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlackWhiteItem.ProtoReflect.Descriptor instead.
func (*BlackWhiteItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{0}
}

func (x *BlackWhiteItem) GetModule() ModuleType {
	if x != nil {
		return x.Module
	}
	return ModuleType_MEM_RISK
}

func (x *BlackWhiteItem) GetUniqueFlag() []byte {
	if x != nil {
		return x.UniqueFlag
	}
	return nil
}

func (x *BlackWhiteItem) GetBwType() BWType {
	if x != nil {
		return x.BwType
	}
	return BWType_WHITE
}

func (x *BlackWhiteItem) GetAddOrDel() Operate {
	if x != nil {
		return x.AddOrDel
	}
	return Operate_ADD
}

// 首次oldTimestamp=curTimestamp,agent自己做判断要不要请求
type UpdateBlackWhitePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldTimestamp   uint64            `protobuf:"varint,1,opt,name=oldTimestamp,proto3" json:"oldTimestamp,omitempty"`
	CurTimestamp   uint64            `protobuf:"varint,2,opt,name=curTimestamp,proto3" json:"curTimestamp,omitempty"`
	BlackwhiteList []*BlackWhiteItem `protobuf:"bytes,3,rep,name=blackwhiteList,proto3" json:"blackwhiteList,omitempty"`
}

func (x *UpdateBlackWhitePolicy) Reset() {
	*x = UpdateBlackWhitePolicy{}
	mi := &file_agent_policy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBlackWhitePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBlackWhitePolicy) ProtoMessage() {}

func (x *UpdateBlackWhitePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBlackWhitePolicy.ProtoReflect.Descriptor instead.
func (*UpdateBlackWhitePolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateBlackWhitePolicy) GetOldTimestamp() uint64 {
	if x != nil {
		return x.OldTimestamp
	}
	return 0
}

func (x *UpdateBlackWhitePolicy) GetCurTimestamp() uint64 {
	if x != nil {
		return x.CurTimestamp
	}
	return 0
}

func (x *UpdateBlackWhitePolicy) GetBlackwhiteList() []*BlackWhiteItem {
	if x != nil {
		return x.BlackwhiteList
	}
	return nil
}

type FileMonitorItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filepath     []byte `protobuf:"bytes,1,opt,name=filepath,proto3" json:"filepath,omitempty"`         // 文件或者目录的全路径
	Filetype     uint32 `protobuf:"varint,2,opt,name=filetype,proto3" json:"filetype,omitempty"`        // FileType相加的值，dir下的文件或目录,包含dir本身
	Operate      uint32 `protobuf:"varint,3,opt,name=operate,proto3" json:"operate,omitempty"`          // FileOperate相加的值
	WhitePath    []byte `protobuf:"bytes,4,opt,name=whitePath,proto3" json:"whitePath,omitempty"`       // 白名单路径列表 | 分隔 每个路径都带通配符，默认*后缀，eg:c:/abc/*|d:/bcd/*
	WhiteProc    []byte `protobuf:"bytes,5,opt,name=whiteProc,proto3" json:"whiteProc,omitempty"`       // 白名单进程名列表 | 分隔
	Filenamelist []byte `protobuf:"bytes,6,opt,name=filenamelist,proto3" json:"filenamelist,omitempty"` // 文件名列表 | 分隔 支持*？通配符
	IncludeExt   []byte `protobuf:"bytes,7,opt,name=includeExt,proto3" json:"includeExt,omitempty"`     // 包含的扩展名列表 | 分隔 与excludeExt互斥
	ExcludeExt   []byte `protobuf:"bytes,8,opt,name=excludeExt,proto3" json:"excludeExt,omitempty"`     // 排除的扩展名列表 | 分隔 与includeExt互斥
}

func (x *FileMonitorItem) Reset() {
	*x = FileMonitorItem{}
	mi := &file_agent_policy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileMonitorItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileMonitorItem) ProtoMessage() {}

func (x *FileMonitorItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileMonitorItem.ProtoReflect.Descriptor instead.
func (*FileMonitorItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{2}
}

func (x *FileMonitorItem) GetFilepath() []byte {
	if x != nil {
		return x.Filepath
	}
	return nil
}

func (x *FileMonitorItem) GetFiletype() uint32 {
	if x != nil {
		return x.Filetype
	}
	return 0
}

func (x *FileMonitorItem) GetOperate() uint32 {
	if x != nil {
		return x.Operate
	}
	return 0
}

func (x *FileMonitorItem) GetWhitePath() []byte {
	if x != nil {
		return x.WhitePath
	}
	return nil
}

func (x *FileMonitorItem) GetWhiteProc() []byte {
	if x != nil {
		return x.WhiteProc
	}
	return nil
}

func (x *FileMonitorItem) GetFilenamelist() []byte {
	if x != nil {
		return x.Filenamelist
	}
	return nil
}

func (x *FileMonitorItem) GetIncludeExt() []byte {
	if x != nil {
		return x.IncludeExt
	}
	return nil
}

func (x *FileMonitorItem) GetExcludeExt() []byte {
	if x != nil {
		return x.ExcludeExt
	}
	return nil
}

type UpdateFileMonitorPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result          bool               `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	FileMonitorList []*FileMonitorItem `protobuf:"bytes,2,rep,name=fileMonitorList,proto3" json:"fileMonitorList,omitempty"`
}

func (x *UpdateFileMonitorPolicy) Reset() {
	*x = UpdateFileMonitorPolicy{}
	mi := &file_agent_policy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFileMonitorPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileMonitorPolicy) ProtoMessage() {}

func (x *UpdateFileMonitorPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileMonitorPolicy.ProtoReflect.Descriptor instead.
func (*UpdateFileMonitorPolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateFileMonitorPolicy) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *UpdateFileMonitorPolicy) GetFileMonitorList() []*FileMonitorItem {
	if x != nil {
		return x.FileMonitorList
	}
	return nil
}

type RequestPolicyList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LocalTimestamp uint64  `protobuf:"varint,1,opt,name=localTimestamp,proto3" json:"localTimestamp,omitempty"`
	ReqType        ReqType `protobuf:"varint,2,opt,name=reqType,proto3,enum=agent.ReqType" json:"reqType,omitempty"`
}

func (x *RequestPolicyList) Reset() {
	*x = RequestPolicyList{}
	mi := &file_agent_policy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestPolicyList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestPolicyList) ProtoMessage() {}

func (x *RequestPolicyList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestPolicyList.ProtoReflect.Descriptor instead.
func (*RequestPolicyList) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{4}
}

func (x *RequestPolicyList) GetLocalTimestamp() uint64 {
	if x != nil {
		return x.LocalTimestamp
	}
	return 0
}

func (x *RequestPolicyList) GetReqType() ReqType {
	if x != nil {
		return x.ReqType
	}
	return ReqType_ALL
}

type RiskInterceptSwitchMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskInterceptOff   []RiskInterceptType `protobuf:"varint,1,rep,packed,name=riskInterceptOff,proto3,enum=agent.RiskInterceptType" json:"riskInterceptOff,omitempty"` // 关闭拦截的
	RiskOn             []RiskInterceptType `protobuf:"varint,2,rep,packed,name=riskOn,proto3,enum=agent.RiskInterceptType" json:"riskOn,omitempty"`                     // 风险开关
	RiskBehaviorSwitch *RiskBehaviorSwitch `protobuf:"bytes,3,opt,name=riskBehaviorSwitch,proto3" json:"riskBehaviorSwitch,omitempty"`                                  // 行为分析开关,仅windows
}

func (x *RiskInterceptSwitchMsg) Reset() {
	*x = RiskInterceptSwitchMsg{}
	mi := &file_agent_policy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskInterceptSwitchMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskInterceptSwitchMsg) ProtoMessage() {}

func (x *RiskInterceptSwitchMsg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskInterceptSwitchMsg.ProtoReflect.Descriptor instead.
func (*RiskInterceptSwitchMsg) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{5}
}

func (x *RiskInterceptSwitchMsg) GetRiskInterceptOff() []RiskInterceptType {
	if x != nil {
		return x.RiskInterceptOff
	}
	return nil
}

func (x *RiskInterceptSwitchMsg) GetRiskOn() []RiskInterceptType {
	if x != nil {
		return x.RiskOn
	}
	return nil
}

func (x *RiskInterceptSwitchMsg) GetRiskBehaviorSwitch() *RiskBehaviorSwitch {
	if x != nil {
		return x.RiskBehaviorSwitch
	}
	return nil
}

type RiskBehaviorSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RiskSwitch SwitchStatus `protobuf:"varint,1,opt,name=riskSwitch,proto3,enum=agent.SwitchStatus" json:"riskSwitch,omitempty"`
}

func (x *RiskBehaviorSwitch) Reset() {
	*x = RiskBehaviorSwitch{}
	mi := &file_agent_policy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskBehaviorSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskBehaviorSwitch) ProtoMessage() {}

func (x *RiskBehaviorSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskBehaviorSwitch.ProtoReflect.Descriptor instead.
func (*RiskBehaviorSwitch) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{6}
}

func (x *RiskBehaviorSwitch) GetRiskSwitch() SwitchStatus {
	if x != nil {
		return x.RiskSwitch
	}
	return SwitchStatus_SS_TURN_OFF
}

// 全局开关
type GlobalSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PTHEventLog        bool                `protobuf:"varint,1,opt,name=PTHEventLog,proto3" json:"PTHEventLog,omitempty"`           // 是否记录PTH日志
	DumpLsass          bool                `protobuf:"varint,2,opt,name=DumpLsass,proto3" json:"DumpLsass,omitempty"`               // 是否开启LSASS进程防护
	NetLogon           bool                `protobuf:"varint,3,opt,name=NetLogon,proto3" json:"NetLogon,omitempty"`                 // 权限提升
	AccRisk            bool                `protobuf:"varint,4,opt,name=AccRisk,proto3" json:"AccRisk,omitempty"`                   // 账户风险开关
	PuppetProc         bool                `protobuf:"varint,5,opt,name=PuppetProc,proto3" json:"PuppetProc,omitempty"`             // 傀儡进程
	WebShellMemory     bool                `protobuf:"varint,6,opt,name=WebShellMemory,proto3" json:"WebShellMemory,omitempty"`     //内存webshell
	KernelInject       bool                `protobuf:"varint,7,opt,name=KernelInject,proto3" json:"KernelInject,omitempty"`         //内核注入
	SelfProtect        bool                `protobuf:"varint,8,opt,name=SelfProtect,proto3" json:"SelfProtect,omitempty"`           // 自保护
	CheckRootkit       bool                `protobuf:"varint,9,opt,name=CheckRootkit,proto3" json:"CheckRootkit,omitempty"`         // rootkit检测
	KernelInjectList   []*KernelInjectList `protobuf:"bytes,10,rep,name=kernelInjectList,proto3" json:"kernelInjectList,omitempty"` // 内核注入详细信息
	InjectTomcat       bool                `protobuf:"varint,11,opt,name=InjectTomcat,proto3" json:"InjectTomcat,omitempty"`
	InjectWeblogic     bool                `protobuf:"varint,12,opt,name=InjectWeblogic,proto3" json:"InjectWeblogic,omitempty"`
	InjectWebsphere    bool                `protobuf:"varint,13,opt,name=InjectWebsphere,proto3" json:"InjectWebsphere,omitempty"`
	InjectJetty        bool                `protobuf:"varint,14,opt,name=InjectJetty,proto3" json:"InjectJetty,omitempty"`
	InjectJbossWildfly bool                `protobuf:"varint,15,opt,name=InjectJbossWildfly,proto3" json:"InjectJbossWildfly,omitempty"`
	InjectResin        bool                `protobuf:"varint,16,opt,name=InjectResin,proto3" json:"InjectResin,omitempty"`
	InjectSpring       bool                `protobuf:"varint,17,opt,name=InjectSpring,proto3" json:"InjectSpring,omitempty"`
	InjectBES          bool                `protobuf:"varint,18,opt,name=InjectBES,proto3" json:"InjectBES,omitempty"`
	InjectPHP          bool                `protobuf:"varint,19,opt,name=InjectPHP,proto3" json:"InjectPHP,omitempty"`
	Performance        bool                `protobuf:"varint,20,opt,name=Performance,proto3" json:"Performance,omitempty"`
	PHPApp             bool                `protobuf:"varint,21,opt,name=PHPApp,proto3" json:"PHPApp,omitempty"`
	BankCompatible     bool                `protobuf:"varint,30,opt,name=BankCompatible,proto3" json:"BankCompatible,omitempty"` // 银行兼容模式
	// v01相关开关
	V01FileMonitor     bool `protobuf:"varint,31,opt,name=V01FileMonitor,proto3" json:"V01FileMonitor,omitempty"`         // v01文件检测威胁开关
	V01IllegalOutreach bool `protobuf:"varint,32,opt,name=V01IllegalOutreach,proto3" json:"V01IllegalOutreach,omitempty"` // v01非法外联开关
	V01MemoryAttack    bool `protobuf:"varint,33,opt,name=V01MemoryAttack,proto3" json:"V01MemoryAttack,omitempty"`       // v01内存攻击检测
	V01ShellAttack     bool `protobuf:"varint,34,opt,name=V01ShellAttack,proto3" json:"V01ShellAttack,omitempty"`         // v01脚本攻击检测
	VO1CheckLdr        bool `protobuf:"varint,35,opt,name=VO1CheckLdr,proto3" json:"VO1CheckLdr,omitempty"`               // v01添加对PEB.LDR的檢测
	V01ACDRSwitch      bool `protobuf:"varint,36,opt,name=V01ACDRSwitch,proto3" json:"V01ACDRSwitch,omitempty"`           // v01 ACDR 开关
}

func (x *GlobalSwitch) Reset() {
	*x = GlobalSwitch{}
	mi := &file_agent_policy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GlobalSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GlobalSwitch) ProtoMessage() {}

func (x *GlobalSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GlobalSwitch.ProtoReflect.Descriptor instead.
func (*GlobalSwitch) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{7}
}

func (x *GlobalSwitch) GetPTHEventLog() bool {
	if x != nil {
		return x.PTHEventLog
	}
	return false
}

func (x *GlobalSwitch) GetDumpLsass() bool {
	if x != nil {
		return x.DumpLsass
	}
	return false
}

func (x *GlobalSwitch) GetNetLogon() bool {
	if x != nil {
		return x.NetLogon
	}
	return false
}

func (x *GlobalSwitch) GetAccRisk() bool {
	if x != nil {
		return x.AccRisk
	}
	return false
}

func (x *GlobalSwitch) GetPuppetProc() bool {
	if x != nil {
		return x.PuppetProc
	}
	return false
}

func (x *GlobalSwitch) GetWebShellMemory() bool {
	if x != nil {
		return x.WebShellMemory
	}
	return false
}

func (x *GlobalSwitch) GetKernelInject() bool {
	if x != nil {
		return x.KernelInject
	}
	return false
}

func (x *GlobalSwitch) GetSelfProtect() bool {
	if x != nil {
		return x.SelfProtect
	}
	return false
}

func (x *GlobalSwitch) GetCheckRootkit() bool {
	if x != nil {
		return x.CheckRootkit
	}
	return false
}

func (x *GlobalSwitch) GetKernelInjectList() []*KernelInjectList {
	if x != nil {
		return x.KernelInjectList
	}
	return nil
}

func (x *GlobalSwitch) GetInjectTomcat() bool {
	if x != nil {
		return x.InjectTomcat
	}
	return false
}

func (x *GlobalSwitch) GetInjectWeblogic() bool {
	if x != nil {
		return x.InjectWeblogic
	}
	return false
}

func (x *GlobalSwitch) GetInjectWebsphere() bool {
	if x != nil {
		return x.InjectWebsphere
	}
	return false
}

func (x *GlobalSwitch) GetInjectJetty() bool {
	if x != nil {
		return x.InjectJetty
	}
	return false
}

func (x *GlobalSwitch) GetInjectJbossWildfly() bool {
	if x != nil {
		return x.InjectJbossWildfly
	}
	return false
}

func (x *GlobalSwitch) GetInjectResin() bool {
	if x != nil {
		return x.InjectResin
	}
	return false
}

func (x *GlobalSwitch) GetInjectSpring() bool {
	if x != nil {
		return x.InjectSpring
	}
	return false
}

func (x *GlobalSwitch) GetInjectBES() bool {
	if x != nil {
		return x.InjectBES
	}
	return false
}

func (x *GlobalSwitch) GetInjectPHP() bool {
	if x != nil {
		return x.InjectPHP
	}
	return false
}

func (x *GlobalSwitch) GetPerformance() bool {
	if x != nil {
		return x.Performance
	}
	return false
}

func (x *GlobalSwitch) GetPHPApp() bool {
	if x != nil {
		return x.PHPApp
	}
	return false
}

func (x *GlobalSwitch) GetBankCompatible() bool {
	if x != nil {
		return x.BankCompatible
	}
	return false
}

func (x *GlobalSwitch) GetV01FileMonitor() bool {
	if x != nil {
		return x.V01FileMonitor
	}
	return false
}

func (x *GlobalSwitch) GetV01IllegalOutreach() bool {
	if x != nil {
		return x.V01IllegalOutreach
	}
	return false
}

func (x *GlobalSwitch) GetV01MemoryAttack() bool {
	if x != nil {
		return x.V01MemoryAttack
	}
	return false
}

func (x *GlobalSwitch) GetV01ShellAttack() bool {
	if x != nil {
		return x.V01ShellAttack
	}
	return false
}

func (x *GlobalSwitch) GetVO1CheckLdr() bool {
	if x != nil {
		return x.VO1CheckLdr
	}
	return false
}

func (x *GlobalSwitch) GetV01ACDRSwitch() bool {
	if x != nil {
		return x.V01ACDRSwitch
	}
	return false
}

type PTHPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Switch       bool             `protobuf:"varint,1,opt,name=switch,proto3" json:"switch,omitempty"`                               // 开关
	HostList     []string         `protobuf:"bytes,2,rep,name=hostList,proto3" json:"hostList,omitempty"`                            // IP数组
	PthMode      PTHInterceptMode `protobuf:"varint,3,opt,name=pthMode,proto3,enum=agent.PTHInterceptMode" json:"pthMode,omitempty"` // 拦截模式
	UsernameList []string         `protobuf:"bytes,4,rep,name=usernameList,proto3" json:"usernameList,omitempty"`                    // 用户名数组
}

func (x *PTHPolicy) Reset() {
	*x = PTHPolicy{}
	mi := &file_agent_policy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PTHPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PTHPolicy) ProtoMessage() {}

func (x *PTHPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PTHPolicy.ProtoReflect.Descriptor instead.
func (*PTHPolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{8}
}

func (x *PTHPolicy) GetSwitch() bool {
	if x != nil {
		return x.Switch
	}
	return false
}

func (x *PTHPolicy) GetHostList() []string {
	if x != nil {
		return x.HostList
	}
	return nil
}

func (x *PTHPolicy) GetPthMode() PTHInterceptMode {
	if x != nil {
		return x.PthMode
	}
	return PTHInterceptMode_PTH_Unknown
}

func (x *PTHPolicy) GetUsernameList() []string {
	if x != nil {
		return x.UsernameList
	}
	return nil
}

type CustomFileBlackWhitePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BwList []*CustomFileBlackWhiteItem `protobuf:"bytes,1,rep,name=bwList,proto3" json:"bwList,omitempty"` // 手动添加的黑白名单
}

func (x *CustomFileBlackWhitePolicy) Reset() {
	*x = CustomFileBlackWhitePolicy{}
	mi := &file_agent_policy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomFileBlackWhitePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomFileBlackWhitePolicy) ProtoMessage() {}

func (x *CustomFileBlackWhitePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomFileBlackWhitePolicy.ProtoReflect.Descriptor instead.
func (*CustomFileBlackWhitePolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{9}
}

func (x *CustomFileBlackWhitePolicy) GetBwList() []*CustomFileBlackWhiteItem {
	if x != nil {
		return x.BwList
	}
	return nil
}

type CustomFileBlackWhiteItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Md5    []byte `protobuf:"bytes,1,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha1   []byte `protobuf:"bytes,2,opt,name=sha1,proto3" json:"sha1,omitempty"`
	BwType BWType `protobuf:"varint,3,opt,name=bwType,proto3,enum=agent.BWType" json:"bwType,omitempty"` // 黑或白
}

func (x *CustomFileBlackWhiteItem) Reset() {
	*x = CustomFileBlackWhiteItem{}
	mi := &file_agent_policy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomFileBlackWhiteItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomFileBlackWhiteItem) ProtoMessage() {}

func (x *CustomFileBlackWhiteItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomFileBlackWhiteItem.ProtoReflect.Descriptor instead.
func (*CustomFileBlackWhiteItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{10}
}

func (x *CustomFileBlackWhiteItem) GetMd5() []byte {
	if x != nil {
		return x.Md5
	}
	return nil
}

func (x *CustomFileBlackWhiteItem) GetSha1() []byte {
	if x != nil {
		return x.Sha1
	}
	return nil
}

func (x *CustomFileBlackWhiteItem) GetBwType() BWType {
	if x != nil {
		return x.BwType
	}
	return BWType_WHITE
}

type DigitalSignaturePolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DsList []*DigitalSignatureItem `protobuf:"bytes,1,rep,name=dsList,proto3" json:"dsList,omitempty"` // 数字签名列表，只有Windows会有
}

func (x *DigitalSignaturePolicy) Reset() {
	*x = DigitalSignaturePolicy{}
	mi := &file_agent_policy_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DigitalSignaturePolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalSignaturePolicy) ProtoMessage() {}

func (x *DigitalSignaturePolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalSignaturePolicy.ProtoReflect.Descriptor instead.
func (*DigitalSignaturePolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{11}
}

func (x *DigitalSignaturePolicy) GetDsList() []*DigitalSignatureItem {
	if x != nil {
		return x.DsList
	}
	return nil
}

type DigitalSignatureItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signature   []byte `protobuf:"bytes,1,opt,name=signature,proto3" json:"signature,omitempty"`              // 数字签名
	Certificate []byte `protobuf:"bytes,2,opt,name=certificate,proto3" json:"certificate,omitempty"`          // 证书指纹
	BwType      BWType `protobuf:"varint,3,opt,name=bwType,proto3,enum=agent.BWType" json:"bwType,omitempty"` // 黑或白
}

func (x *DigitalSignatureItem) Reset() {
	*x = DigitalSignatureItem{}
	mi := &file_agent_policy_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DigitalSignatureItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DigitalSignatureItem) ProtoMessage() {}

func (x *DigitalSignatureItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DigitalSignatureItem.ProtoReflect.Descriptor instead.
func (*DigitalSignatureItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{12}
}

func (x *DigitalSignatureItem) GetSignature() []byte {
	if x != nil {
		return x.Signature
	}
	return nil
}

func (x *DigitalSignatureItem) GetCertificate() []byte {
	if x != nil {
		return x.Certificate
	}
	return nil
}

func (x *DigitalSignatureItem) GetBwType() BWType {
	if x != nil {
		return x.BwType
	}
	return BWType_WHITE
}

// 驱动注入模块应用列表
type KernelInjectItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath    string `protobuf:"bytes,1,opt,name=filePath,proto3" json:"filePath,omitempty"`       // 进程路径
	CmdLineArgs string `protobuf:"bytes,2,opt,name=cmdLineArgs,proto3" json:"cmdLineArgs,omitempty"` // 命令行参数
}

func (x *KernelInjectItem) Reset() {
	*x = KernelInjectItem{}
	mi := &file_agent_policy_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KernelInjectItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KernelInjectItem) ProtoMessage() {}

func (x *KernelInjectItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KernelInjectItem.ProtoReflect.Descriptor instead.
func (*KernelInjectItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{13}
}

func (x *KernelInjectItem) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *KernelInjectItem) GetCmdLineArgs() string {
	if x != nil {
		return x.CmdLineArgs
	}
	return ""
}

// 驱动注入模块应用列表
type KernelInjectList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               uint32              `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"` // 数据库里面对应的id
	KernelInjectItem []*KernelInjectItem `protobuf:"bytes,2,rep,name=kernelInjectItem,proto3" json:"kernelInjectItem,omitempty"`
}

func (x *KernelInjectList) Reset() {
	*x = KernelInjectList{}
	mi := &file_agent_policy_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KernelInjectList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KernelInjectList) ProtoMessage() {}

func (x *KernelInjectList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KernelInjectList.ProtoReflect.Descriptor instead.
func (*KernelInjectList) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{14}
}

func (x *KernelInjectList) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KernelInjectList) GetKernelInjectItem() []*KernelInjectItem {
	if x != nil {
		return x.KernelInjectItem
	}
	return nil
}

type LonginTimes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LonginFrequency LonginTimes_Frequency `protobuf:"varint,1,opt,name=longin_frequency,json=longinFrequency,proto3,enum=agent.LonginTimes_Frequency" json:"longin_frequency,omitempty"` // 登录周期
	Days            []uint32              `protobuf:"varint,2,rep,packed,name=days,proto3" json:"days,omitempty"`                                                                        // 每日的情况不用传，每周的话传1-7的数组， 每月的话传1-31的数组
	StartTime       string                `protobuf:"bytes,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                                                     // 开始时间
	EndTime         string                `protobuf:"bytes,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                                                           // 结束时间
}

func (x *LonginTimes) Reset() {
	*x = LonginTimes{}
	mi := &file_agent_policy_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LonginTimes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LonginTimes) ProtoMessage() {}

func (x *LonginTimes) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LonginTimes.ProtoReflect.Descriptor instead.
func (*LonginTimes) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{15}
}

func (x *LonginTimes) GetLonginFrequency() LonginTimes_Frequency {
	if x != nil {
		return x.LonginFrequency
	}
	return LonginTimes_INVALID
}

func (x *LonginTimes) GetDays() []uint32 {
	if x != nil {
		return x.Days
	}
	return nil
}

func (x *LonginTimes) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *LonginTimes) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type AbnormalLoginConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginIp    []string                      `protobuf:"bytes,1,rep,name=login_ip,json=loginIp,proto3" json:"login_ip,omitempty"`                                                 // 登录的白ip
	LoginTimes []*LonginTimes                `protobuf:"bytes,2,rep,name=login_times,json=loginTimes,proto3" json:"login_times,omitempty"`                                        // 登录的白时间段，考虑后面的扩展性，可以同时配置多个白时间段
	LoginUser  []string                      `protobuf:"bytes,3,rep,name=login_user,json=loginUser,proto3" json:"login_user,omitempty"`                                           // 登录账号
	StudyMode  AbnormalLoginConfig_StudyMode `protobuf:"varint,4,opt,name=study_mode,json=studyMode,proto3,enum=agent.AbnormalLoginConfig_StudyMode" json:"study_mode,omitempty"` // 检测学习模式
}

func (x *AbnormalLoginConfig) Reset() {
	*x = AbnormalLoginConfig{}
	mi := &file_agent_policy_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AbnormalLoginConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AbnormalLoginConfig) ProtoMessage() {}

func (x *AbnormalLoginConfig) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AbnormalLoginConfig.ProtoReflect.Descriptor instead.
func (*AbnormalLoginConfig) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{16}
}

func (x *AbnormalLoginConfig) GetLoginIp() []string {
	if x != nil {
		return x.LoginIp
	}
	return nil
}

func (x *AbnormalLoginConfig) GetLoginTimes() []*LonginTimes {
	if x != nil {
		return x.LoginTimes
	}
	return nil
}

func (x *AbnormalLoginConfig) GetLoginUser() []string {
	if x != nil {
		return x.LoginUser
	}
	return nil
}

func (x *AbnormalLoginConfig) GetStudyMode() AbnormalLoginConfig_StudyMode {
	if x != nil {
		return x.StudyMode
	}
	return AbnormalLoginConfig_Undefined
}

type UpdateFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"` //版本
	Md5     []byte `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`         //md5
}

func (x *UpdateFileInfo) Reset() {
	*x = UpdateFileInfo{}
	mi := &file_agent_policy_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateFileInfo) ProtoMessage() {}

func (x *UpdateFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateFileInfo.ProtoReflect.Descriptor instead.
func (*UpdateFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateFileInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *UpdateFileInfo) GetMd5() []byte {
	if x != nil {
		return x.Md5
	}
	return nil
}

// BWRuleDetail 细项规则详细内容
// 语义化表述为:
//   - [属性] [匹配] [范围] [值...]
//
// 如:
//   - 文件md5 等于 以下任一 ["abcd1234", "foobar"]
//   - 进程名 包含 以下所有 ["hello", "foobar"]
type BWRuleDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attribute string             `protobuf:"bytes,1,opt,name=attribute,proto3" json:"attribute,omitempty"`                        // 属性名: md5; 进程名; 全路径; ...
	Condition uint32             `protobuf:"varint,2,opt,name=condition,proto3" json:"condition,omitempty"`                       // 匹配条件: 等于; 包含; 前缀; ...
	Scope     BWRuleDetail_Scope `protobuf:"varint,3,opt,name=scope,proto3,enum=agent.BWRuleDetail_Scope" json:"scope,omitempty"` // 匹配范围: 1: 全部; 2: 任一
	Values    []string           `protobuf:"bytes,4,rep,name=values,proto3" json:"values,omitempty"`                              // 匹配值列表
}

func (x *BWRuleDetail) Reset() {
	*x = BWRuleDetail{}
	mi := &file_agent_policy_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BWRuleDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BWRuleDetail) ProtoMessage() {}

func (x *BWRuleDetail) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BWRuleDetail.ProtoReflect.Descriptor instead.
func (*BWRuleDetail) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{18}
}

func (x *BWRuleDetail) GetAttribute() string {
	if x != nil {
		return x.Attribute
	}
	return ""
}

func (x *BWRuleDetail) GetCondition() uint32 {
	if x != nil {
		return x.Condition
	}
	return 0
}

func (x *BWRuleDetail) GetScope() BWRuleDetail_Scope {
	if x != nil {
		return x.Scope
	}
	return BWRuleDetail_Undefined
}

func (x *BWRuleDetail) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// BWRulePkg 规则包
// 单项规则: 由一个细项规则构成
// 组合规则: 由多个细项规则以 "且" 的关系组合而成
type BWRulePkg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Checkpoint  string          `protobuf:"bytes,1,opt,name=checkpoint,proto3" json:"checkpoint,omitempty"`                      // 功能点
	Id          int64           `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`                                     // 规则包ID
	RuleDetails []*BWRuleDetail `protobuf:"bytes,3,rep,name=rule_details,json=ruleDetails,proto3" json:"rule_details,omitempty"` // 此规则包包含的组合规则列表, 单项规则只有一个元素
	MinVer      string          `protobuf:"bytes,4,opt,name=min_ver,json=minVer,proto3" json:"min_ver,omitempty"`                // 最小版本 "":无限制 或者 "V2.0.2401.001"
	MaxVer      string          `protobuf:"bytes,5,opt,name=max_ver,json=maxVer,proto3" json:"max_ver,omitempty"`                // 最大版本 "":无限制
}

func (x *BWRulePkg) Reset() {
	*x = BWRulePkg{}
	mi := &file_agent_policy_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BWRulePkg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BWRulePkg) ProtoMessage() {}

func (x *BWRulePkg) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BWRulePkg.ProtoReflect.Descriptor instead.
func (*BWRulePkg) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{19}
}

func (x *BWRulePkg) GetCheckpoint() string {
	if x != nil {
		return x.Checkpoint
	}
	return ""
}

func (x *BWRulePkg) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BWRulePkg) GetRuleDetails() []*BWRuleDetail {
	if x != nil {
		return x.RuleDetails
	}
	return nil
}

func (x *BWRulePkg) GetMinVer() string {
	if x != nil {
		return x.MinVer
	}
	return ""
}

func (x *BWRulePkg) GetMaxVer() string {
	if x != nil {
		return x.MaxVer
	}
	return ""
}

// BWRuleList 规则包列表
type BWRuleList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RuleList []*BWRulePkg `protobuf:"bytes,1,rep,name=rule_list,json=ruleList,proto3" json:"rule_list,omitempty"` // 此 agent 应用的所有规则包列表
}

func (x *BWRuleList) Reset() {
	*x = BWRuleList{}
	mi := &file_agent_policy_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BWRuleList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BWRuleList) ProtoMessage() {}

func (x *BWRuleList) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BWRuleList.ProtoReflect.Descriptor instead.
func (*BWRuleList) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{20}
}

func (x *BWRuleList) GetRuleList() []*BWRulePkg {
	if x != nil {
		return x.RuleList
	}
	return nil
}

type Settings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 使用oneof用于变相支持has方法
	// 通过如下方式检测元素是否为空 ：setting.has_usualLogonThreshold_case() == MemProtect::Setting::HAS_USUALLOGONTHRESHOLD_NOT_SET;
	//
	// Types that are assignable to HasUsualLogonThreshold:
	//
	//	*Settings_UsualLogonThreshold
	HasUsualLogonThreshold isSettings_HasUsualLogonThreshold `protobuf_oneof:"has_usualLogonThreshold"`
	// Types that are assignable to HasWeakPasswordListTimestamp:
	//
	//	*Settings_WeakPasswordListTimestamp
	HasWeakPasswordListTimestamp isSettings_HasWeakPasswordListTimestamp `protobuf_oneof:"has_weakPasswordListTimestamp"`
	// Types that are assignable to HasRiskInterceptSwitchMsg:
	//
	//	*Settings_RiskInterceptSwitch
	HasRiskInterceptSwitchMsg isSettings_HasRiskInterceptSwitchMsg `protobuf_oneof:"has_riskInterceptSwitchMsg"`
	// Types that are assignable to HasGlobalSwitch:
	//
	//	*Settings_GlobalSwitch
	HasGlobalSwitch isSettings_HasGlobalSwitch `protobuf_oneof:"has_globalSwitch"`
	//	oneof has_driverstatus {SetDriverStatus driverStatus = 5;} // 暂时不通过这里下发
	//
	// Types that are assignable to Has_AbnormalLoginConfig:
	//
	//	*Settings_AbnormalLoginConfig
	Has_AbnormalLoginConfig isSettings_Has_AbnormalLoginConfig `protobuf_oneof:"has_AbnormalLoginConfig"`
	// Types that are assignable to Has_WhiteRuleList:
	//
	//	*Settings_WhiteRuleList
	Has_WhiteRuleList isSettings_Has_WhiteRuleList `protobuf_oneof:"has_WhiteRuleList"`
}

func (x *Settings) Reset() {
	*x = Settings{}
	mi := &file_agent_policy_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Settings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings) ProtoMessage() {}

func (x *Settings) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings.ProtoReflect.Descriptor instead.
func (*Settings) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{21}
}

func (m *Settings) GetHasUsualLogonThreshold() isSettings_HasUsualLogonThreshold {
	if m != nil {
		return m.HasUsualLogonThreshold
	}
	return nil
}

func (x *Settings) GetUsualLogonThreshold() uint32 {
	if x, ok := x.GetHasUsualLogonThreshold().(*Settings_UsualLogonThreshold); ok {
		return x.UsualLogonThreshold
	}
	return 0
}

func (m *Settings) GetHasWeakPasswordListTimestamp() isSettings_HasWeakPasswordListTimestamp {
	if m != nil {
		return m.HasWeakPasswordListTimestamp
	}
	return nil
}

func (x *Settings) GetWeakPasswordListTimestamp() uint64 {
	if x, ok := x.GetHasWeakPasswordListTimestamp().(*Settings_WeakPasswordListTimestamp); ok {
		return x.WeakPasswordListTimestamp
	}
	return 0
}

func (m *Settings) GetHasRiskInterceptSwitchMsg() isSettings_HasRiskInterceptSwitchMsg {
	if m != nil {
		return m.HasRiskInterceptSwitchMsg
	}
	return nil
}

func (x *Settings) GetRiskInterceptSwitch() *RiskInterceptSwitchMsg {
	if x, ok := x.GetHasRiskInterceptSwitchMsg().(*Settings_RiskInterceptSwitch); ok {
		return x.RiskInterceptSwitch
	}
	return nil
}

func (m *Settings) GetHasGlobalSwitch() isSettings_HasGlobalSwitch {
	if m != nil {
		return m.HasGlobalSwitch
	}
	return nil
}

func (x *Settings) GetGlobalSwitch() *GlobalSwitch {
	if x, ok := x.GetHasGlobalSwitch().(*Settings_GlobalSwitch); ok {
		return x.GlobalSwitch
	}
	return nil
}

func (m *Settings) GetHas_AbnormalLoginConfig() isSettings_Has_AbnormalLoginConfig {
	if m != nil {
		return m.Has_AbnormalLoginConfig
	}
	return nil
}

func (x *Settings) GetAbnormalLoginConfig() *AbnormalLoginConfig {
	if x, ok := x.GetHas_AbnormalLoginConfig().(*Settings_AbnormalLoginConfig); ok {
		return x.AbnormalLoginConfig
	}
	return nil
}

func (m *Settings) GetHas_WhiteRuleList() isSettings_Has_WhiteRuleList {
	if m != nil {
		return m.Has_WhiteRuleList
	}
	return nil
}

func (x *Settings) GetWhiteRuleList() *BWRuleList {
	if x, ok := x.GetHas_WhiteRuleList().(*Settings_WhiteRuleList); ok {
		return x.WhiteRuleList
	}
	return nil
}

type isSettings_HasUsualLogonThreshold interface {
	isSettings_HasUsualLogonThreshold()
}

type Settings_UsualLogonThreshold struct {
	UsualLogonThreshold uint32 `protobuf:"varint,1,opt,name=usualLogonThreshold,proto3,oneof"`
}

func (*Settings_UsualLogonThreshold) isSettings_HasUsualLogonThreshold() {}

type isSettings_HasWeakPasswordListTimestamp interface {
	isSettings_HasWeakPasswordListTimestamp()
}

type Settings_WeakPasswordListTimestamp struct {
	WeakPasswordListTimestamp uint64 `protobuf:"varint,2,opt,name=weakPasswordListTimestamp,proto3,oneof"`
}

func (*Settings_WeakPasswordListTimestamp) isSettings_HasWeakPasswordListTimestamp() {}

type isSettings_HasRiskInterceptSwitchMsg interface {
	isSettings_HasRiskInterceptSwitchMsg()
}

type Settings_RiskInterceptSwitch struct {
	RiskInterceptSwitch *RiskInterceptSwitchMsg `protobuf:"bytes,3,opt,name=riskInterceptSwitch,proto3,oneof"`
}

func (*Settings_RiskInterceptSwitch) isSettings_HasRiskInterceptSwitchMsg() {}

type isSettings_HasGlobalSwitch interface {
	isSettings_HasGlobalSwitch()
}

type Settings_GlobalSwitch struct {
	GlobalSwitch *GlobalSwitch `protobuf:"bytes,4,opt,name=globalSwitch,proto3,oneof"`
}

func (*Settings_GlobalSwitch) isSettings_HasGlobalSwitch() {}

type isSettings_Has_AbnormalLoginConfig interface {
	isSettings_Has_AbnormalLoginConfig()
}

type Settings_AbnormalLoginConfig struct {
	AbnormalLoginConfig *AbnormalLoginConfig `protobuf:"bytes,6,opt,name=abnormal_login_config,json=abnormalLoginConfig,proto3,oneof"`
}

func (*Settings_AbnormalLoginConfig) isSettings_Has_AbnormalLoginConfig() {}

type isSettings_Has_WhiteRuleList interface {
	isSettings_Has_WhiteRuleList()
}

type Settings_WhiteRuleList struct {
	WhiteRuleList *BWRuleList `protobuf:"bytes,8,opt,name=white_rule_list,json=whiteRuleList,proto3,oneof"`
}

func (*Settings_WhiteRuleList) isSettings_Has_WhiteRuleList() {}

// 仅客户端用配置文件结构
type AgentSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerSettings *Settings `protobuf:"bytes,1,opt,name=serverSettings,proto3" json:"serverSettings,omitempty"`
	// Types that are assignable to HashHashlibinfo:
	//
	//	*AgentSettings_HashLibInfo
	HashHashlibinfo isAgentSettings_HashHashlibinfo `protobuf_oneof:"hash_hashlibinfo"`
	// 此处可以定义agent自用的一些配置信息
	//
	// Types that are assignable to HasSha256Libinfo:
	//
	//	*AgentSettings_Sha256LibInfo
	HasSha256Libinfo isAgentSettings_HasSha256Libinfo `protobuf_oneof:"has_sha256libinfo"`
	// Types that are assignable to HasAntiviuspolicy:
	//
	//	*AgentSettings_AntiVirusPolicy
	HasAntiviuspolicy isAgentSettings_HasAntiviuspolicy `protobuf_oneof:"has_antiviuspolicy"`
	// Types that are assignable to HasWebshellengineVer:
	//
	//	*AgentSettings_WebshellEngineVer
	HasWebshellengineVer isAgentSettings_HasWebshellengineVer `protobuf_oneof:"has_webshellengine_ver"`
	// Types that are assignable to HasVirusengineVer:
	//
	//	*AgentSettings_VirusEngineVer
	HasVirusengineVer isAgentSettings_HasVirusengineVer `protobuf_oneof:"has_virusengine_ver"`
}

func (x *AgentSettings) Reset() {
	*x = AgentSettings{}
	mi := &file_agent_policy_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentSettings) ProtoMessage() {}

func (x *AgentSettings) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentSettings.ProtoReflect.Descriptor instead.
func (*AgentSettings) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{22}
}

func (x *AgentSettings) GetServerSettings() *Settings {
	if x != nil {
		return x.ServerSettings
	}
	return nil
}

func (m *AgentSettings) GetHashHashlibinfo() isAgentSettings_HashHashlibinfo {
	if m != nil {
		return m.HashHashlibinfo
	}
	return nil
}

func (x *AgentSettings) GetHashLibInfo() *UpdateFileInfo {
	if x, ok := x.GetHashHashlibinfo().(*AgentSettings_HashLibInfo); ok {
		return x.HashLibInfo
	}
	return nil
}

func (m *AgentSettings) GetHasSha256Libinfo() isAgentSettings_HasSha256Libinfo {
	if m != nil {
		return m.HasSha256Libinfo
	}
	return nil
}

func (x *AgentSettings) GetSha256LibInfo() *UpdateFileInfo {
	if x, ok := x.GetHasSha256Libinfo().(*AgentSettings_Sha256LibInfo); ok {
		return x.Sha256LibInfo
	}
	return nil
}

func (m *AgentSettings) GetHasAntiviuspolicy() isAgentSettings_HasAntiviuspolicy {
	if m != nil {
		return m.HasAntiviuspolicy
	}
	return nil
}

func (x *AgentSettings) GetAntiVirusPolicy() *AntiVirusPolicy {
	if x, ok := x.GetHasAntiviuspolicy().(*AgentSettings_AntiVirusPolicy); ok {
		return x.AntiVirusPolicy
	}
	return nil
}

func (m *AgentSettings) GetHasWebshellengineVer() isAgentSettings_HasWebshellengineVer {
	if m != nil {
		return m.HasWebshellengineVer
	}
	return nil
}

func (x *AgentSettings) GetWebshellEngineVer() *DetectEngineVerResp {
	if x, ok := x.GetHasWebshellengineVer().(*AgentSettings_WebshellEngineVer); ok {
		return x.WebshellEngineVer
	}
	return nil
}

func (m *AgentSettings) GetHasVirusengineVer() isAgentSettings_HasVirusengineVer {
	if m != nil {
		return m.HasVirusengineVer
	}
	return nil
}

func (x *AgentSettings) GetVirusEngineVer() *DetectEngineVerResp {
	if x, ok := x.GetHasVirusengineVer().(*AgentSettings_VirusEngineVer); ok {
		return x.VirusEngineVer
	}
	return nil
}

type isAgentSettings_HashHashlibinfo interface {
	isAgentSettings_HashHashlibinfo()
}

type AgentSettings_HashLibInfo struct {
	HashLibInfo *UpdateFileInfo `protobuf:"bytes,2,opt,name=hash_lib_info,json=hashLibInfo,proto3,oneof"`
}

func (*AgentSettings_HashLibInfo) isAgentSettings_HashHashlibinfo() {}

type isAgentSettings_HasSha256Libinfo interface {
	isAgentSettings_HasSha256Libinfo()
}

type AgentSettings_Sha256LibInfo struct {
	Sha256LibInfo *UpdateFileInfo `protobuf:"bytes,3,opt,name=sha256_lib_info,json=sha256LibInfo,proto3,oneof"`
}

func (*AgentSettings_Sha256LibInfo) isAgentSettings_HasSha256Libinfo() {}

type isAgentSettings_HasAntiviuspolicy interface {
	isAgentSettings_HasAntiviuspolicy()
}

type AgentSettings_AntiVirusPolicy struct {
	AntiVirusPolicy *AntiVirusPolicy `protobuf:"bytes,4,opt,name=anti_virus_policy,json=antiVirusPolicy,proto3,oneof"`
}

func (*AgentSettings_AntiVirusPolicy) isAgentSettings_HasAntiviuspolicy() {}

type isAgentSettings_HasWebshellengineVer interface {
	isAgentSettings_HasWebshellengineVer()
}

type AgentSettings_WebshellEngineVer struct {
	WebshellEngineVer *DetectEngineVerResp `protobuf:"bytes,5,opt,name=webshell_engine_ver,json=webshellEngineVer,proto3,oneof"`
}

func (*AgentSettings_WebshellEngineVer) isAgentSettings_HasWebshellengineVer() {}

type isAgentSettings_HasVirusengineVer interface {
	isAgentSettings_HasVirusengineVer()
}

type AgentSettings_VirusEngineVer struct {
	VirusEngineVer *DetectEngineVerResp `protobuf:"bytes,6,opt,name=virus_engine_ver,json=virusEngineVer,proto3,oneof"`
}

func (*AgentSettings_VirusEngineVer) isAgentSettings_HasVirusengineVer() {}

type WeakPwdListReponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WeakPasswordList      []string `protobuf:"bytes,1,rep,name=weakPasswordList,proto3" json:"weakPasswordList,omitempty"`           // 弱密码表
	WeakPasswordRegexList []string `protobuf:"bytes,2,rep,name=weakPasswordRegexList,proto3" json:"weakPasswordRegexList,omitempty"` // 弱密码正则
}

func (x *WeakPwdListReponse) Reset() {
	*x = WeakPwdListReponse{}
	mi := &file_agent_policy_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeakPwdListReponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeakPwdListReponse) ProtoMessage() {}

func (x *WeakPwdListReponse) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeakPwdListReponse.ProtoReflect.Descriptor instead.
func (*WeakPwdListReponse) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{23}
}

func (x *WeakPwdListReponse) GetWeakPasswordList() []string {
	if x != nil {
		return x.WeakPasswordList
	}
	return nil
}

func (x *WeakPwdListReponse) GetWeakPasswordRegexList() []string {
	if x != nil {
		return x.WeakPasswordRegexList
	}
	return nil
}

type TimeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime string `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 开始执行时间
	EndTime   string `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 截止执行时间
}

func (x *TimeItem) Reset() {
	*x = TimeItem{}
	mi := &file_agent_policy_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeItem) ProtoMessage() {}

func (x *TimeItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeItem.ProtoReflect.Descriptor instead.
func (*TimeItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{24}
}

func (x *TimeItem) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *TimeItem) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type TimeScheduled struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Frequency    TimeScheduled_Frequency `protobuf:"varint,1,opt,name=frequency,proto3,enum=agent.TimeScheduled_Frequency" json:"frequency,omitempty"` // 登录周期
	Days         []uint32                `protobuf:"varint,2,rep,packed,name=days,proto3" json:"days,omitempty"`                                       // 每日的情况不用传，每周的话传1-7的数组， 每月的话传1-31的数组
	TimeItemList []*TimeItem             `protobuf:"bytes,3,rep,name=time_item_list,json=timeItemList,proto3" json:"time_item_list,omitempty"`         // 时间段
	Immediately  int64                   `protobuf:"varint,4,opt,name=immediately,proto3" json:"immediately,omitempty"`                                // 立即扫描时间戳
}

func (x *TimeScheduled) Reset() {
	*x = TimeScheduled{}
	mi := &file_agent_policy_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeScheduled) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeScheduled) ProtoMessage() {}

func (x *TimeScheduled) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeScheduled.ProtoReflect.Descriptor instead.
func (*TimeScheduled) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{25}
}

func (x *TimeScheduled) GetFrequency() TimeScheduled_Frequency {
	if x != nil {
		return x.Frequency
	}
	return TimeScheduled_SET_INVALID
}

func (x *TimeScheduled) GetDays() []uint32 {
	if x != nil {
		return x.Days
	}
	return nil
}

func (x *TimeScheduled) GetTimeItemList() []*TimeItem {
	if x != nil {
		return x.TimeItemList
	}
	return nil
}

func (x *TimeScheduled) GetImmediately() int64 {
	if x != nil {
		return x.Immediately
	}
	return 0
}

type WebshellScanItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Scanpathpolicy uint32         `protobuf:"varint,1,opt,name=scanpathpolicy,proto3" json:"scanpathpolicy,omitempty"`                   // 扫描路径策略 1 只扫描webshell站点， 2 只扫描自定义目录 ， 3 扫描web site + 自定义目录
	TimeScheduled  *TimeScheduled `protobuf:"bytes,2,opt,name=time_scheduled,json=timeScheduled,proto3" json:"time_scheduled,omitempty"` // 定时设置选项
	MonitorPath    []byte         `protobuf:"bytes,3,opt,name=monitor_path,json=monitorPath,proto3" json:"monitor_path,omitempty"`       // 监控目录列表 | 分割
	IgnorePath     []byte         `protobuf:"bytes,4,opt,name=ignore_path,json=ignorePath,proto3" json:"ignore_path,omitempty"`          // 忽略目录列表 | 分隔 , 应该为monitorpath的子目录，优先级高于monitorpath
}

func (x *WebshellScanItem) Reset() {
	*x = WebshellScanItem{}
	mi := &file_agent_policy_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebshellScanItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebshellScanItem) ProtoMessage() {}

func (x *WebshellScanItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebshellScanItem.ProtoReflect.Descriptor instead.
func (*WebshellScanItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{26}
}

func (x *WebshellScanItem) GetScanpathpolicy() uint32 {
	if x != nil {
		return x.Scanpathpolicy
	}
	return 0
}

func (x *WebshellScanItem) GetTimeScheduled() *TimeScheduled {
	if x != nil {
		return x.TimeScheduled
	}
	return nil
}

func (x *WebshellScanItem) GetMonitorPath() []byte {
	if x != nil {
		return x.MonitorPath
	}
	return nil
}

func (x *WebshellScanItem) GetIgnorePath() []byte {
	if x != nil {
		return x.IgnorePath
	}
	return nil
}

// webshell static scanning 策略下发
type UpdateWebshellScanPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result           bool                `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	WebshellScanList []*WebshellScanItem `protobuf:"bytes,2,rep,name=webshell_scan_list,json=webshellScanList,proto3" json:"webshell_scan_list,omitempty"`
}

func (x *UpdateWebshellScanPolicy) Reset() {
	*x = UpdateWebshellScanPolicy{}
	mi := &file_agent_policy_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateWebshellScanPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateWebshellScanPolicy) ProtoMessage() {}

func (x *UpdateWebshellScanPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateWebshellScanPolicy.ProtoReflect.Descriptor instead.
func (*UpdateWebshellScanPolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateWebshellScanPolicy) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *UpdateWebshellScanPolicy) GetWebshellScanList() []*WebshellScanItem {
	if x != nil {
		return x.WebshellScanList
	}
	return nil
}

// Anti-Virus
type AntiVirusPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result                bool              `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`                                   // agent响应为true
	Scanpathpolicy        uint32            `protobuf:"varint,2,opt,name=scanpathpolicy,proto3" json:"scanpathpolicy,omitempty"`                   // 0b0001: 全盘; 0b0010: 自定义; 0b0100: 推荐
	TimeScheduled         *TimeScheduled    `protobuf:"bytes,3,opt,name=time_scheduled,json=timeScheduled,proto3" json:"time_scheduled,omitempty"` // 扫描设置实时，定期
	MonitorPath           []byte            `protobuf:"bytes,4,opt,name=monitor_path,json=monitorPath,proto3" json:"monitor_path,omitempty"`       // 监控目录列表 | 分割
	IgnorePath            []byte            `protobuf:"bytes,5,opt,name=ignore_path,json=ignorePath,proto3" json:"ignore_path,omitempty"`
	Enable                bool              `protobuf:"varint,6,opt,name=enable,proto3" json:"enable,omitempty"`                                                                                            // V01 新增 是否开启
	MaxFilesize           uint32            `protobuf:"varint,7,opt,name=max_filesize,json=maxFilesize,proto3" json:"max_filesize,omitempty"`                                                               // V01 文件最大限制 单位为M
	AntiScanTypeList      []V01FileType     `protobuf:"varint,8,rep,packed,name=anti_scan_type_list,json=antiScanTypeList,proto3,enum=agent.V01FileType" json:"anti_scan_type_list,omitempty"`              // V01 630 新增扫描文件类型
	SnapshotEnable        bool              `protobuf:"varint,9,opt,name=snapshot_enable,json=snapshotEnable,proto3" json:"snapshot_enable,omitempty"`                                                      // V01 新增 文件属性是否开启
	SnapshotTimeScheduled *TimeScheduled    `protobuf:"bytes,10,opt,name=snapshot_time_scheduled,json=snapshotTimeScheduled,proto3" json:"snapshot_time_scheduled,omitempty"`                               // 文件属性扫描设置实时，定期
	SnapshotScanTypeList  []V01FileType     `protobuf:"varint,11,rep,packed,name=snapshot_scan_type_list,json=snapshotScanTypeList,proto3,enum=agent.V01FileType" json:"snapshot_scan_type_list,omitempty"` // V01 630文件属性扫描文件类型
	SnapshotMaxFilesize   uint32            `protobuf:"varint,12,opt,name=snapshot_max_filesize,json=snapshotMaxFilesize,proto3" json:"snapshot_max_filesize,omitempty"`
	AntiScanTypeConf      V01FileScanConfig `protobuf:"varint,13,opt,name=anti_scan_type_conf,json=antiScanTypeConf,proto3,enum=agent.V01FileScanConfig" json:"anti_scan_type_conf,omitempty"` // 高危文件采集; 静态文件采集; 全量文件采集; 自定义采集
	PhishingDetect        bool              `protobuf:"varint,14,opt,name=phishing_detect,json=phishingDetect,proto3" json:"phishing_detect,omitempty"`                                        // 釣魚實時檢測
	RealTimeScanLinux     bool              `protobuf:"varint,15,opt,name=real_time_scan_linux,json=realTimeScanLinux,proto3" json:"real_time_scan_linux,omitempty"`                           // 实时扫描 (Linux)
	RealTimeScanWindows   bool              `protobuf:"varint,16,opt,name=real_time_scan_windows,json=realTimeScanWindows,proto3" json:"real_time_scan_windows,omitempty"`                     // 实时扫描 (Windows)
	UdsikPhishingDetect   bool              `protobuf:"varint,17,opt,name=udsik_phishing_detect,json=udsikPhishingDetect,proto3" json:"udsik_phishing_detect,omitempty"`                       // 钓鱼实时监测->U盘钓鱼
	ImPhishingDetect      bool              `protobuf:"varint,18,opt,name=im_phishing_detect,json=imPhishingDetect,proto3" json:"im_phishing_detect,omitempty"`                                // 钓鱼实时监测->im钓鱼
	EmailPhishingDetect   bool              `protobuf:"varint,19,opt,name=email_phishing_detect,json=emailPhishingDetect,proto3" json:"email_phishing_detect,omitempty"`                       // 钓鱼实时监测->email钓鱼
}

func (x *AntiVirusPolicy) Reset() {
	*x = AntiVirusPolicy{}
	mi := &file_agent_policy_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AntiVirusPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AntiVirusPolicy) ProtoMessage() {}

func (x *AntiVirusPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AntiVirusPolicy.ProtoReflect.Descriptor instead.
func (*AntiVirusPolicy) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{28}
}

func (x *AntiVirusPolicy) GetResult() bool {
	if x != nil {
		return x.Result
	}
	return false
}

func (x *AntiVirusPolicy) GetScanpathpolicy() uint32 {
	if x != nil {
		return x.Scanpathpolicy
	}
	return 0
}

func (x *AntiVirusPolicy) GetTimeScheduled() *TimeScheduled {
	if x != nil {
		return x.TimeScheduled
	}
	return nil
}

func (x *AntiVirusPolicy) GetMonitorPath() []byte {
	if x != nil {
		return x.MonitorPath
	}
	return nil
}

func (x *AntiVirusPolicy) GetIgnorePath() []byte {
	if x != nil {
		return x.IgnorePath
	}
	return nil
}

func (x *AntiVirusPolicy) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *AntiVirusPolicy) GetMaxFilesize() uint32 {
	if x != nil {
		return x.MaxFilesize
	}
	return 0
}

func (x *AntiVirusPolicy) GetAntiScanTypeList() []V01FileType {
	if x != nil {
		return x.AntiScanTypeList
	}
	return nil
}

func (x *AntiVirusPolicy) GetSnapshotEnable() bool {
	if x != nil {
		return x.SnapshotEnable
	}
	return false
}

func (x *AntiVirusPolicy) GetSnapshotTimeScheduled() *TimeScheduled {
	if x != nil {
		return x.SnapshotTimeScheduled
	}
	return nil
}

func (x *AntiVirusPolicy) GetSnapshotScanTypeList() []V01FileType {
	if x != nil {
		return x.SnapshotScanTypeList
	}
	return nil
}

func (x *AntiVirusPolicy) GetSnapshotMaxFilesize() uint32 {
	if x != nil {
		return x.SnapshotMaxFilesize
	}
	return 0
}

func (x *AntiVirusPolicy) GetAntiScanTypeConf() V01FileScanConfig {
	if x != nil {
		return x.AntiScanTypeConf
	}
	return V01FileScanConfig_V01_FILE_SCAN_CONFIG_UNKNOWN
}

func (x *AntiVirusPolicy) GetPhishingDetect() bool {
	if x != nil {
		return x.PhishingDetect
	}
	return false
}

func (x *AntiVirusPolicy) GetRealTimeScanLinux() bool {
	if x != nil {
		return x.RealTimeScanLinux
	}
	return false
}

func (x *AntiVirusPolicy) GetRealTimeScanWindows() bool {
	if x != nil {
		return x.RealTimeScanWindows
	}
	return false
}

func (x *AntiVirusPolicy) GetUdsikPhishingDetect() bool {
	if x != nil {
		return x.UdsikPhishingDetect
	}
	return false
}

func (x *AntiVirusPolicy) GetImPhishingDetect() bool {
	if x != nil {
		return x.ImPhishingDetect
	}
	return false
}

func (x *AntiVirusPolicy) GetEmailPhishingDetect() bool {
	if x != nil {
		return x.EmailPhishingDetect
	}
	return false
}

type VirusDetectItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256        string            `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`                                               // sha256值
	Result        VirusDetectResult `protobuf:"varint,2,opt,name=result,proto3,enum=agent.VirusDetectResult" json:"result,omitempty"`                 // 威胁检测结果 黑，白，超时，失败
	RiskLevel     int32             `protobuf:"varint,3,opt,name=risk_level,json=riskLevel,proto3" json:"risk_level,omitempty"`                       // 威胁等级
	RiskType      int32             `protobuf:"varint,4,opt,name=risk_type,json=riskType,proto3" json:"risk_type,omitempty"`                          // 威胁类型
	VirusName     string            `protobuf:"bytes,5,opt,name=virus_name,json=virusName,proto3" json:"virus_name,omitempty"`                        // 病毒名称
	MalwareType   string            `protobuf:"bytes,6,opt,name=malware_type,json=malwareType,proto3" json:"malware_type,omitempty"`                  // 威胁类型
	FileType      FileTypeIdent     `protobuf:"varint,7,opt,name=file_type,json=fileType,proto3,enum=agent.FileTypeIdent" json:"file_type,omitempty"` // 文件类型(php ,asp,jsp,...)
	FilePath      string            `protobuf:"bytes,8,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                           // 文件路径
	ServerAddr    string            `protobuf:"bytes,9,opt,name=server_addr,json=serverAddr,proto3" json:"server_addr,omitempty"`                     // 服务端地址
	AgentAddr     string            `protobuf:"bytes,10,opt,name=agent_addr,json=agentAddr,proto3" json:"agent_addr,omitempty"`                       // agent地址
	Source        int32             `protobuf:"varint,11,opt,name=source,proto3" json:"source,omitempty"`                                             // 来源  1.rasp
	ReqTime       int64             `protobuf:"varint,12,opt,name=req_time,json=reqTime,proto3" json:"req_time,omitempty"`                            // 请求时间
	Atime         int64             `protobuf:"varint,13,opt,name=atime,proto3" json:"atime,omitempty"`                                               // 最后一次访问文件时间
	Mtime         int64             `protobuf:"varint,14,opt,name=mtime,proto3" json:"mtime,omitempty"`                                               // 最后一次文件修改时间
	Ctime         int64             `protobuf:"varint,15,opt,name=ctime,proto3" json:"ctime,omitempty"`                                               // 最后一次文件改变时间
	StMode        string            `protobuf:"bytes,16,opt,name=st_mode,json=stMode,proto3" json:"st_mode,omitempty"`                                // 文件权限
	Sha1          string            `protobuf:"bytes,17,opt,name=sha1,proto3" json:"sha1,omitempty"`                                                  // 文件sha1
	Md5           string            `protobuf:"bytes,18,opt,name=md5,proto3" json:"md5,omitempty"`                                                    // 文件md5
	FileSize      int32             `protobuf:"varint,19,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                         // 文件大小
	UniqueFlag    []byte            `protobuf:"bytes,20,opt,name=uniqueFlag,proto3" json:"uniqueFlag,omitempty"`                                      // 唯一的处理相关信息
	FileVersion   string            `protobuf:"bytes,21,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`                 // 文件版本
	FileVendor    []byte            `protobuf:"bytes,22,opt,name=file_vendor,json=fileVendor,proto3" json:"file_vendor,omitempty"`                    // 文件厂商
	SignatureInfo []*SignatureInfo  `protobuf:"bytes,23,rep,name=signatureInfo,proto3" json:"signatureInfo,omitempty"`                                // 进程文件签名信息
	TlshHash      string            `protobuf:"bytes,24,opt,name=tlsh_hash,json=tlshHash,proto3" json:"tlsh_hash,omitempty"`                          // TLSH哈希
	ImpHash       string            `protobuf:"bytes,25,opt,name=imp_hash,json=impHash,proto3" json:"imp_hash,omitempty"`                             // IMP哈希
	Priority      int32             `protobuf:"varint,26,opt,name=priority,proto3" json:"priority,omitempty"`                                         // 检测优先级
}

func (x *VirusDetectItem) Reset() {
	*x = VirusDetectItem{}
	mi := &file_agent_policy_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusDetectItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusDetectItem) ProtoMessage() {}

func (x *VirusDetectItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusDetectItem.ProtoReflect.Descriptor instead.
func (*VirusDetectItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{29}
}

func (x *VirusDetectItem) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *VirusDetectItem) GetResult() VirusDetectResult {
	if x != nil {
		return x.Result
	}
	return VirusDetectResult_RESULT_UNKNOWN
}

func (x *VirusDetectItem) GetRiskLevel() int32 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *VirusDetectItem) GetRiskType() int32 {
	if x != nil {
		return x.RiskType
	}
	return 0
}

func (x *VirusDetectItem) GetVirusName() string {
	if x != nil {
		return x.VirusName
	}
	return ""
}

func (x *VirusDetectItem) GetMalwareType() string {
	if x != nil {
		return x.MalwareType
	}
	return ""
}

func (x *VirusDetectItem) GetFileType() FileTypeIdent {
	if x != nil {
		return x.FileType
	}
	return FileTypeIdent_FILE_TYPE_UNKNOWN
}

func (x *VirusDetectItem) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *VirusDetectItem) GetServerAddr() string {
	if x != nil {
		return x.ServerAddr
	}
	return ""
}

func (x *VirusDetectItem) GetAgentAddr() string {
	if x != nil {
		return x.AgentAddr
	}
	return ""
}

func (x *VirusDetectItem) GetSource() int32 {
	if x != nil {
		return x.Source
	}
	return 0
}

func (x *VirusDetectItem) GetReqTime() int64 {
	if x != nil {
		return x.ReqTime
	}
	return 0
}

func (x *VirusDetectItem) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *VirusDetectItem) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *VirusDetectItem) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

func (x *VirusDetectItem) GetStMode() string {
	if x != nil {
		return x.StMode
	}
	return ""
}

func (x *VirusDetectItem) GetSha1() string {
	if x != nil {
		return x.Sha1
	}
	return ""
}

func (x *VirusDetectItem) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *VirusDetectItem) GetFileSize() int32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *VirusDetectItem) GetUniqueFlag() []byte {
	if x != nil {
		return x.UniqueFlag
	}
	return nil
}

func (x *VirusDetectItem) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *VirusDetectItem) GetFileVendor() []byte {
	if x != nil {
		return x.FileVendor
	}
	return nil
}

func (x *VirusDetectItem) GetSignatureInfo() []*SignatureInfo {
	if x != nil {
		return x.SignatureInfo
	}
	return nil
}

func (x *VirusDetectItem) GetTlshHash() string {
	if x != nil {
		return x.TlshHash
	}
	return ""
}

func (x *VirusDetectItem) GetImpHash() string {
	if x != nil {
		return x.ImpHash
	}
	return ""
}

func (x *VirusDetectItem) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

// 请求文件结果查询接口
type SearchBySha256Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskScanType TaskScanType       `protobuf:"varint,1,opt,name=task_scan_type,json=taskScanType,proto3,enum=agent.TaskScanType" json:"task_scan_type,omitempty"` // 查询触发方式，这一版没有用，预留的
	Sha256List   []*VirusDetectItem `protobuf:"bytes,2,rep,name=sha256_list,json=sha256List,proto3" json:"sha256_list,omitempty"`                                  // 请求查询的sha256列表
}

func (x *SearchBySha256Req) Reset() {
	*x = SearchBySha256Req{}
	mi := &file_agent_policy_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchBySha256Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchBySha256Req) ProtoMessage() {}

func (x *SearchBySha256Req) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchBySha256Req.ProtoReflect.Descriptor instead.
func (*SearchBySha256Req) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{30}
}

func (x *SearchBySha256Req) GetTaskScanType() TaskScanType {
	if x != nil {
		return x.TaskScanType
	}
	return TaskScanType_SO_UNKNOWN
}

func (x *SearchBySha256Req) GetSha256List() []*VirusDetectItem {
	if x != nil {
		return x.Sha256List
	}
	return nil
}

// 请求文件结果响应接口
type SearchBySha256Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VirusDetect_List []*VirusDetectItem `protobuf:"bytes,1,rep,name=virus_detect_List,json=virusDetectList,proto3" json:"virus_detect_List,omitempty"`
}

func (x *SearchBySha256Resp) Reset() {
	*x = SearchBySha256Resp{}
	mi := &file_agent_policy_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchBySha256Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchBySha256Resp) ProtoMessage() {}

func (x *SearchBySha256Resp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchBySha256Resp.ProtoReflect.Descriptor instead.
func (*SearchBySha256Resp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{31}
}

func (x *SearchBySha256Resp) GetVirusDetect_List() []*VirusDetectItem {
	if x != nil {
		return x.VirusDetect_List
	}
	return nil
}

// 文件上传通信接口
type UploadFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256   string            `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`                                                                                         // sha256值
	Url      string            `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`                                                                                               // 上传地址的url
	Params   map[string]string `protobuf:"bytes,3,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // post参数
	Filepath string            `protobuf:"bytes,4,opt,name=filepath,proto3" json:"filepath,omitempty"`                                                                                     // v01 新增 要上传的文件路径
}

func (x *UploadFileReq) Reset() {
	*x = UploadFileReq{}
	mi := &file_agent_policy_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileReq) ProtoMessage() {}

func (x *UploadFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileReq.ProtoReflect.Descriptor instead.
func (*UploadFileReq) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{32}
}

func (x *UploadFileReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *UploadFileReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UploadFileReq) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *UploadFileReq) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

type UploadFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   FileStatus    `protobuf:"varint,1,opt,name=result,proto3,enum=agent.FileStatus" json:"result,omitempty"`                        //文件状态返回
	Sha256   string        `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                                               // sha256值
	FileType FileTypeIdent `protobuf:"varint,3,opt,name=file_type,json=fileType,proto3,enum=agent.FileTypeIdent" json:"file_type,omitempty"` //文件类型(php ,asp,jsp,...)
	Filename string        `protobuf:"bytes,4,opt,name=filename,proto3" json:"filename,omitempty"`                                           //文件名称
}

func (x *UploadFileResp) Reset() {
	*x = UploadFileResp{}
	mi := &file_agent_policy_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileResp) ProtoMessage() {}

func (x *UploadFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileResp.ProtoReflect.Descriptor instead.
func (*UploadFileResp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{33}
}

func (x *UploadFileResp) GetResult() FileStatus {
	if x != nil {
		return x.Result
	}
	return FileStatus_FILE_NOT_FOUND
}

func (x *UploadFileResp) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *UploadFileResp) GetFileType() FileTypeIdent {
	if x != nil {
		return x.FileType
	}
	return FileTypeIdent_FILE_TYPE_UNKNOWN
}

func (x *UploadFileResp) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

type DetectEngineVerReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind DetectKindIdent `protobuf:"varint,1,opt,name=kind,proto3,enum=agent.DetectKindIdent" json:"kind,omitempty"`
}

func (x *DetectEngineVerReq) Reset() {
	*x = DetectEngineVerReq{}
	mi := &file_agent_policy_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectEngineVerReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectEngineVerReq) ProtoMessage() {}

func (x *DetectEngineVerReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectEngineVerReq.ProtoReflect.Descriptor instead.
func (*DetectEngineVerReq) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{34}
}

func (x *DetectEngineVerReq) GetKind() DetectKindIdent {
	if x != nil {
		return x.Kind
	}
	return DetectKindIdent_DETECT_KIND_UNKNOWN
}

type DetectEngineVerResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind              DetectKindIdent `protobuf:"varint,1,opt,name=kind,proto3,enum=agent.DetectKindIdent" json:"kind,omitempty"`
	EngineVerStamp    int64           `protobuf:"varint,2,opt,name=EngineVerStamp,proto3" json:"EngineVerStamp,omitempty"`
	EngineSwitchStamp int64           `protobuf:"varint,3,opt,name=EngineSwitchStamp,proto3" json:"EngineSwitchStamp,omitempty"`
}

func (x *DetectEngineVerResp) Reset() {
	*x = DetectEngineVerResp{}
	mi := &file_agent_policy_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectEngineVerResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectEngineVerResp) ProtoMessage() {}

func (x *DetectEngineVerResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectEngineVerResp.ProtoReflect.Descriptor instead.
func (*DetectEngineVerResp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{35}
}

func (x *DetectEngineVerResp) GetKind() DetectKindIdent {
	if x != nil {
		return x.Kind
	}
	return DetectKindIdent_DETECT_KIND_UNKNOWN
}

func (x *DetectEngineVerResp) GetEngineVerStamp() int64 {
	if x != nil {
		return x.EngineVerStamp
	}
	return 0
}

func (x *DetectEngineVerResp) GetEngineSwitchStamp() int64 {
	if x != nil {
		return x.EngineSwitchStamp
	}
	return 0
}

type AgentFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId        int64             `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`                                                                    // 风险详情ID，agent收到请求后的回复需要把该ID带上
	FilePath        string            `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                                                                     // 文件路径
	ProcessId       int64             `protobuf:"varint,3,opt,name=process_id,json=processId,proto3" json:"process_id,omitempty"`                                                                 // 进程ID
	ClassName       string            `protobuf:"bytes,4,opt,name=class_name,json=className,proto3" json:"class_name,omitempty"`                                                                  // 类名
	ClassLoaderName string            `protobuf:"bytes,5,opt,name=class_loader_name,json=classLoaderName,proto3" json:"class_loader_name,omitempty"`                                              // 所属类加载器
	Url             string            `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`                                                                                               // 上传地址的url
	Params          map[string]string `protobuf:"bytes,7,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // post参数
	Kind            AgentFileKind     `protobuf:"varint,8,opt,name=kind,proto3,enum=agent.AgentFileKind" json:"kind,omitempty"`                                                                   // 文件类型
	Host            string            `protobuf:"bytes,9,opt,name=host,proto3" json:"host,omitempty"`
}

func (x *AgentFileReq) Reset() {
	*x = AgentFileReq{}
	mi := &file_agent_policy_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentFileReq) ProtoMessage() {}

func (x *AgentFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentFileReq.ProtoReflect.Descriptor instead.
func (*AgentFileReq) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{36}
}

func (x *AgentFileReq) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *AgentFileReq) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *AgentFileReq) GetProcessId() int64 {
	if x != nil {
		return x.ProcessId
	}
	return 0
}

func (x *AgentFileReq) GetClassName() string {
	if x != nil {
		return x.ClassName
	}
	return ""
}

func (x *AgentFileReq) GetClassLoaderName() string {
	if x != nil {
		return x.ClassLoaderName
	}
	return ""
}

func (x *AgentFileReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AgentFileReq) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *AgentFileReq) GetKind() AgentFileKind {
	if x != nil {
		return x.Kind
	}
	return AgentFileKind_AGENT_FILE_KIND_UNKNOWN
}

func (x *AgentFileReq) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

type AgentFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId int64           `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`        // 风险详情ID，agent收到请求后的回复需要把该ID带上
	Kind     AgentFileKind   `protobuf:"varint,2,opt,name=kind,proto3,enum=agent.AgentFileKind" json:"kind,omitempty"`       // 文件类型
	Status   AgentFileStatus `protobuf:"varint,3,opt,name=status,proto3,enum=agent.AgentFileStatus" json:"status,omitempty"` // 文件状态返回
}

func (x *AgentFileResp) Reset() {
	*x = AgentFileResp{}
	mi := &file_agent_policy_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentFileResp) ProtoMessage() {}

func (x *AgentFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentFileResp.ProtoReflect.Descriptor instead.
func (*AgentFileResp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{37}
}

func (x *AgentFileResp) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *AgentFileResp) GetKind() AgentFileKind {
	if x != nil {
		return x.Kind
	}
	return AgentFileKind_AGENT_FILE_KIND_UNKNOWN
}

func (x *AgentFileResp) GetStatus() AgentFileStatus {
	if x != nil {
		return x.Status
	}
	return AgentFileStatus_AGENT_FILE_STATUS_UNKNOWN
}

type GetProcPathPidsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId        int64    `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`                       // 风险详情ID，agent回复列表时需要把该ID带上
	ProcessPathList []string `protobuf:"bytes,2,rep,name=process_path_list,json=processPathList,proto3" json:"process_path_list,omitempty"` // 进程路径列表，告警只会有一个，但事件可能有多个
}

func (x *GetProcPathPidsReq) Reset() {
	*x = GetProcPathPidsReq{}
	mi := &file_agent_policy_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProcPathPidsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcPathPidsReq) ProtoMessage() {}

func (x *GetProcPathPidsReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcPathPidsReq.ProtoReflect.Descriptor instead.
func (*GetProcPathPidsReq) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{38}
}

func (x *GetProcPathPidsReq) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *GetProcPathPidsReq) GetProcessPathList() []string {
	if x != nil {
		return x.ProcessPathList
	}
	return nil
}

type ProcPathPidsItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result      GetProcPathPidsResult `protobuf:"varint,1,opt,name=result,proto3,enum=agent.GetProcPathPidsResult" json:"result,omitempty"` // pid列表获取结果
	ProcessPath string                `protobuf:"bytes,2,opt,name=process_path,json=processPath,proto3" json:"process_path,omitempty"`      // 进程路径
	Pids        []int32               `protobuf:"varint,3,rep,packed,name=pids,proto3" json:"pids,omitempty"`                               // pid列表
}

func (x *ProcPathPidsItem) Reset() {
	*x = ProcPathPidsItem{}
	mi := &file_agent_policy_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcPathPidsItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcPathPidsItem) ProtoMessage() {}

func (x *ProcPathPidsItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcPathPidsItem.ProtoReflect.Descriptor instead.
func (*ProcPathPidsItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{39}
}

func (x *ProcPathPidsItem) GetResult() GetProcPathPidsResult {
	if x != nil {
		return x.Result
	}
	return GetProcPathPidsResult_GET_PROC_PATH_PIDS_UNKNOWN
}

func (x *ProcPathPidsItem) GetProcessPath() string {
	if x != nil {
		return x.ProcessPath
	}
	return ""
}

func (x *ProcPathPidsItem) GetPids() []int32 {
	if x != nil {
		return x.Pids
	}
	return nil
}

type GetProcPathPidsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId int64               `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"` // 风险详情ID，agent回复列表时需要把该ID带上
	List     []*ProcPathPidsItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetProcPathPidsResp) Reset() {
	*x = GetProcPathPidsResp{}
	mi := &file_agent_policy_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProcPathPidsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProcPathPidsResp) ProtoMessage() {}

func (x *GetProcPathPidsResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProcPathPidsResp.ProtoReflect.Descriptor instead.
func (*GetProcPathPidsResp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{40}
}

func (x *GetProcPathPidsResp) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *GetProcPathPidsResp) GetList() []*ProcPathPidsItem {
	if x != nil {
		return x.List
	}
	return nil
}

type ThreatenHandleItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessPath string                   `protobuf:"bytes,1,opt,name=process_path,json=processPath,proto3" json:"process_path,omitempty"` // 进程路径
	List        []*ThreatenHandleResItem `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`                                  // pid列表
}

func (x *ThreatenHandleItem) Reset() {
	*x = ThreatenHandleItem{}
	mi := &file_agent_policy_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreatenHandleItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreatenHandleItem) ProtoMessage() {}

func (x *ThreatenHandleItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreatenHandleItem.ProtoReflect.Descriptor instead.
func (*ThreatenHandleItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{41}
}

func (x *ThreatenHandleItem) GetProcessPath() string {
	if x != nil {
		return x.ProcessPath
	}
	return ""
}

func (x *ThreatenHandleItem) GetList() []*ThreatenHandleResItem {
	if x != nil {
		return x.List
	}
	return nil
}

type ThreatenHandleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId   int64                 `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`       // 风险详情ID，agent回复需要把该ID带上
	HandleTime int64                 `protobuf:"varint,2,opt,name=handle_time,json=handleTime,proto3" json:"handle_time,omitempty"` // 下发处置的时间，agent回复需要把该时间带上
	List       []*ThreatenHandleItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                // 处置的列表，告警一次只会处置一个进程，但事件可能有多个
}

func (x *ThreatenHandleReq) Reset() {
	*x = ThreatenHandleReq{}
	mi := &file_agent_policy_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreatenHandleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreatenHandleReq) ProtoMessage() {}

func (x *ThreatenHandleReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreatenHandleReq.ProtoReflect.Descriptor instead.
func (*ThreatenHandleReq) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{42}
}

func (x *ThreatenHandleReq) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *ThreatenHandleReq) GetHandleTime() int64 {
	if x != nil {
		return x.HandleTime
	}
	return 0
}

func (x *ThreatenHandleReq) GetList() []*ThreatenHandleItem {
	if x != nil {
		return x.List
	}
	return nil
}

type ThreatenHandleResItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result ThreatenHandleRes `protobuf:"varint,1,opt,name=result,proto3,enum=agent.ThreatenHandleRes" json:"result,omitempty"`
	Pids   []int32           `protobuf:"varint,2,rep,packed,name=pids,proto3" json:"pids,omitempty"`
}

func (x *ThreatenHandleResItem) Reset() {
	*x = ThreatenHandleResItem{}
	mi := &file_agent_policy_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreatenHandleResItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreatenHandleResItem) ProtoMessage() {}

func (x *ThreatenHandleResItem) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreatenHandleResItem.ProtoReflect.Descriptor instead.
func (*ThreatenHandleResItem) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{43}
}

func (x *ThreatenHandleResItem) GetResult() ThreatenHandleRes {
	if x != nil {
		return x.Result
	}
	return ThreatenHandleRes_THREATEN_HANDLE_RESULT_UNKNOWN
}

func (x *ThreatenHandleResItem) GetPids() []int32 {
	if x != nil {
		return x.Pids
	}
	return nil
}

type ThreatenHandleResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DetailId   int64                 `protobuf:"varint,1,opt,name=detail_id,json=detailId,proto3" json:"detail_id,omitempty"`       // 风险详情ID，agent回复需要把该ID带上
	HandleTime int64                 `protobuf:"varint,2,opt,name=handle_time,json=handleTime,proto3" json:"handle_time,omitempty"` // 下发处置的时间，agent回复需要把该时间带上
	List       []*ThreatenHandleItem `protobuf:"bytes,3,rep,name=list,proto3" json:"list,omitempty"`                                // 处置结果的的列表
}

func (x *ThreatenHandleResp) Reset() {
	*x = ThreatenHandleResp{}
	mi := &file_agent_policy_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreatenHandleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreatenHandleResp) ProtoMessage() {}

func (x *ThreatenHandleResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreatenHandleResp.ProtoReflect.Descriptor instead.
func (*ThreatenHandleResp) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{44}
}

func (x *ThreatenHandleResp) GetDetailId() int64 {
	if x != nil {
		return x.DetailId
	}
	return 0
}

func (x *ThreatenHandleResp) GetHandleTime() int64 {
	if x != nil {
		return x.HandleTime
	}
	return 0
}

func (x *ThreatenHandleResp) GetList() []*ThreatenHandleItem {
	if x != nil {
		return x.List
	}
	return nil
}

type FileVirusDetectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileKind             FileVirusKind `protobuf:"varint,1,opt,name=file_kind,json=fileKind,proto3,enum=agent.FileVirusKind" json:"file_kind,omitempty"`              // 区分上报 webshell 还是 杀毒
	TotalScannedNumbers  uint32        `protobuf:"varint,2,opt,name=total_scanned_numbers,json=totalScannedNumbers,proto3" json:"total_scanned_numbers,omitempty"`    // agent 总的扫描文件个数
	FinishScannedNumbers uint32        `protobuf:"varint,3,opt,name=finish_scanned_numbers,json=finishScannedNumbers,proto3" json:"finish_scanned_numbers,omitempty"` // agent 已经检测完成的文件个数
}

func (x *FileVirusDetectionInfo) Reset() {
	*x = FileVirusDetectionInfo{}
	mi := &file_agent_policy_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileVirusDetectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileVirusDetectionInfo) ProtoMessage() {}

func (x *FileVirusDetectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_policy_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileVirusDetectionInfo.ProtoReflect.Descriptor instead.
func (*FileVirusDetectionInfo) Descriptor() ([]byte, []int) {
	return file_agent_policy_proto_rawDescGZIP(), []int{45}
}

func (x *FileVirusDetectionInfo) GetFileKind() FileVirusKind {
	if x != nil {
		return x.FileKind
	}
	return FileVirusKind_FILE_KIND_NULL
}

func (x *FileVirusDetectionInfo) GetTotalScannedNumbers() uint32 {
	if x != nil {
		return x.TotalScannedNumbers
	}
	return 0
}

func (x *FileVirusDetectionInfo) GetFinishScannedNumbers() uint32 {
	if x != nil {
		return x.FinishScannedNumbers
	}
	return 0
}

var File_agent_policy_proto protoreflect.FileDescriptor

var file_agent_policy_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xae, 0x01, 0x0a, 0x0e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x29, 0x0a, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x25, 0x0a,
	0x06, 0x62, 0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x57, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x62, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x08, 0x61, 0x64, 0x64, 0x4f, 0x72, 0x44, 0x65, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x52, 0x08, 0x61, 0x64, 0x64, 0x4f, 0x72, 0x44, 0x65, 0x6c,
	0x22, 0x9f, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b,
	0x57, 0x68, 0x69, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x22, 0x0a, 0x0c, 0x6f,
	0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x0c, 0x6f, 0x6c, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x63, 0x75, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x3d, 0x0a, 0x0e, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x0e, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x83, 0x02, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65, 0x50,
	0x72, 0x6f, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x50, 0x72, 0x6f, 0x63, 0x12, 0x22, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x45, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x45, 0x78, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x45, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x45, 0x78, 0x74, 0x22, 0x73, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x40, 0x0a, 0x0f, 0x66,
	0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x66, 0x69,
	0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x65, 0x0a,
	0x11, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61,
	0x6c, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65,
	0x71, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x72, 0x65, 0x71,
	0x54, 0x79, 0x70, 0x65, 0x22, 0xdb, 0x01, 0x0a, 0x16, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4d, 0x73, 0x67, 0x12,
	0x44, 0x0a, 0x10, 0x72, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74,
	0x4f, 0x66, 0x66, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x10, 0x72, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x4f, 0x66, 0x66, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x69, 0x73, 0x6b, 0x4f, 0x6e, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x06, 0x72, 0x69, 0x73, 0x6b, 0x4f, 0x6e, 0x12, 0x49, 0x0a, 0x12, 0x72, 0x69, 0x73, 0x6b, 0x42,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x12,
	0x72, 0x69, 0x73, 0x6b, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x22, 0x49, 0x0a, 0x12, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x33, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x99, 0x08,
	0x0a, 0x0c, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x20,
	0x0a, 0x0b, 0x50, 0x54, 0x48, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x50, 0x54, 0x48, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x44, 0x75, 0x6d, 0x70, 0x4c, 0x73, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x44, 0x75, 0x6d, 0x70, 0x4c, 0x73, 0x61, 0x73, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x4e, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x4e, 0x65, 0x74, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x63,
	0x63, 0x52, 0x69, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x41, 0x63, 0x63,
	0x52, 0x69, 0x73, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72,
	0x6f, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x12, 0x26, 0x0a, 0x0e, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x57, 0x65,
	0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x22, 0x0a, 0x0c,
	0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0c, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x53, 0x65, 0x6c, 0x66, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x53, 0x65, 0x6c, 0x66, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x6f, 0x6f, 0x74, 0x6b,
	0x69, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x6f, 0x6f, 0x74, 0x6b, 0x69, 0x74, 0x12, 0x43, 0x0a, 0x10, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x10, 0x6b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x6f, 0x6d, 0x63, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x6f, 0x6d, 0x63, 0x61, 0x74, 0x12,
	0x26, 0x0a, 0x0e, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x57, 0x65, 0x62, 0x6c, 0x6f, 0x67, 0x69,
	0x63, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x57,
	0x65, 0x62, 0x6c, 0x6f, 0x67, 0x69, 0x63, 0x12, 0x28, 0x0a, 0x0f, 0x49, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x57, 0x65, 0x62, 0x73, 0x70, 0x68, 0x65, 0x72, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0f, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x57, 0x65, 0x62, 0x73, 0x70, 0x68, 0x65, 0x72,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x65, 0x74, 0x74, 0x79,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x65,
	0x74, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x12, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x62, 0x6f,
	0x73, 0x73, 0x57, 0x69, 0x6c, 0x64, 0x66, 0x6c, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x4a, 0x62, 0x6f, 0x73, 0x73, 0x57, 0x69, 0x6c, 0x64,
	0x66, 0x6c, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73,
	0x69, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x69, 0x6e, 0x12, 0x22, 0x0a, 0x0c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x53,
	0x70, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x49, 0x6e, 0x6a,
	0x65, 0x63, 0x74, 0x53, 0x70, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x6e, 0x6a,
	0x65, 0x63, 0x74, 0x42, 0x45, 0x53, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x6e,
	0x6a, 0x65, 0x63, 0x74, 0x42, 0x45, 0x53, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x6e, 0x6a, 0x65, 0x63,
	0x74, 0x50, 0x48, 0x50, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x50, 0x48, 0x50, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x65, 0x72, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x50, 0x65, 0x72, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x50, 0x48, 0x50, 0x41, 0x70,
	0x70, 0x18, 0x15, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x50, 0x48, 0x50, 0x41, 0x70, 0x70, 0x12,
	0x26, 0x0a, 0x0e, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x74, 0x69, 0x62, 0x6c,
	0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x42, 0x61, 0x6e, 0x6b, 0x43, 0x6f, 0x6d,
	0x70, 0x61, 0x74, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x56, 0x30, 0x31, 0x46, 0x69,
	0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0e, 0x56, 0x30, 0x31, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12,
	0x2e, 0x0a, 0x12, 0x56, 0x30, 0x31, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x56, 0x30, 0x31,
	0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x12,
	0x28, 0x0a, 0x0f, 0x56, 0x30, 0x31, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x18, 0x21, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x56, 0x30, 0x31, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x12, 0x26, 0x0a, 0x0e, 0x56, 0x30, 0x31,
	0x53, 0x68, 0x65, 0x6c, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x56, 0x30, 0x31, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x56, 0x4f, 0x31, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4c, 0x64, 0x72,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x56, 0x4f, 0x31, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x4c, 0x64, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x56, 0x30, 0x31, 0x41, 0x43, 0x44, 0x52, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x18, 0x24, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x56, 0x30, 0x31, 0x41,
	0x43, 0x44, 0x52, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x22, 0x96, 0x01, 0x0a, 0x09, 0x50, 0x54,
	0x48, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12,
	0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x07, 0x70,
	0x74, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x54, 0x48, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70,
	0x74, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x70, 0x74, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x55, 0x0a, 0x1a, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x65,
	0x42, 0x6c, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x37, 0x0a, 0x06, 0x62, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x46,
	0x69, 0x6c, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x06, 0x62, 0x77, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x67, 0x0a, 0x18, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x25, 0x0a, 0x06, 0x62,
	0x77, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x42, 0x57, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x62, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x4d, 0x0a, 0x16, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x33, 0x0a, 0x06,
	0x64, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x64, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x7d, 0x0a, 0x14, 0x44, 0x69, 0x67, 0x69, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x73, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x65, 0x72, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x63, 0x65,
	0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x06, 0x62, 0x77, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x42, 0x57, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x62, 0x77, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x50, 0x0a, 0x10, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x20, 0x0a, 0x0b, 0x63, 0x6d, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x41, 0x72, 0x67, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6d, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x41, 0x72,
	0x67, 0x73, 0x22, 0x67, 0x0a, 0x10, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e, 0x6a, 0x65,
	0x63, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x02, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x10, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49,
	0x6e, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x10, 0x6b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x22, 0xee, 0x01, 0x0a, 0x0b,
	0x4c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x10, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f,
	0x6e, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x52, 0x0f, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x6e, 0x46, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0d, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0x48, 0x0a, 0x09, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09,
	0x45, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x45,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x57, 0x45, 0x41, 0x4b, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x45,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x03, 0x22, 0x80, 0x02, 0x0a,
	0x13, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x69, 0x70,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x49, 0x70, 0x12,
	0x33, 0x0a, 0x0b, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x6e,
	0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0a, 0x73, 0x74, 0x75, 0x64, 0x79, 0x5f, 0x6d, 0x6f, 0x64,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x53, 0x74, 0x75, 0x64, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x09, 0x73,
	0x74, 0x75, 0x64, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x35, 0x0a, 0x09, 0x53, 0x74, 0x75, 0x64,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e,
	0x65, 0x64, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x75, 0x74, 0x6f, 0x53, 0x74, 0x75, 0x64,
	0x79, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x10, 0x02, 0x22,
	0x3c, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6d,
	0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x22, 0xc1, 0x01,
	0x0a, 0x0c, 0x42, 0x57, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c,
	0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2f, 0x0a, 0x05, 0x73, 0x63,
	0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x42, 0x57, 0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x53,
	0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x22, 0x2c, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09,
	0x55, 0x6e, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x65, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x41,
	0x6c, 0x6c, 0x4f, 0x66, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x10,
	0x02, 0x22, 0xa5, 0x01, 0x0a, 0x09, 0x42, 0x57, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x6b, 0x67, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x36, 0x0a, 0x0c, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x57,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0b, 0x72, 0x75, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x69, 0x6e, 0x5f, 0x76,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x69, 0x6e, 0x56, 0x65, 0x72,
	0x12, 0x17, 0x0a, 0x07, 0x6d, 0x61, 0x78, 0x5f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x56, 0x65, 0x72, 0x22, 0x3b, 0x0a, 0x0a, 0x42, 0x57, 0x52,
	0x75, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x09, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x42, 0x57, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x6b, 0x67, 0x52, 0x08, 0x72, 0x75,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb9, 0x04, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x32, 0x0a, 0x13, 0x75, 0x73, 0x75, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x6f,
	0x6e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d,
	0x48, 0x00, 0x52, 0x13, 0x75, 0x73, 0x75, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x54, 0x68,
	0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x3e, 0x0a, 0x19, 0x77, 0x65, 0x61, 0x6b, 0x50,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x48, 0x01, 0x52, 0x19, 0x77, 0x65,
	0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x51, 0x0a, 0x13, 0x72, 0x69, 0x73, 0x6b, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68,
	0x4d, 0x73, 0x67, 0x48, 0x02, 0x52, 0x13, 0x72, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x63, 0x65, 0x70, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x39, 0x0a, 0x0c, 0x67, 0x6c,
	0x6f, 0x62, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x48, 0x03, 0x52, 0x0c, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x50, 0x0a, 0x15, 0x61, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x5f, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x62, 0x6e,
	0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x48, 0x04, 0x52, 0x13, 0x61, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3b, 0x0a, 0x0f, 0x77, 0x68, 0x69, 0x74, 0x65,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x57, 0x52, 0x75, 0x6c, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x48, 0x05, 0x52, 0x0d, 0x77, 0x68, 0x69, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x42, 0x19, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f, 0x75, 0x73, 0x75, 0x61,
	0x6c, 0x4c, 0x6f, 0x67, 0x6f, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x42,
	0x1f, 0x0a, 0x1d, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x1c, 0x0a, 0x1a, 0x68, 0x61, 0x73, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x63, 0x65, 0x70, 0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x4d, 0x73, 0x67, 0x42, 0x12,
	0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f, 0x67, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x53, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x42, 0x19, 0x0a, 0x17, 0x68, 0x61, 0x73, 0x5f, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d,
	0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x13, 0x0a,
	0x11, 0x68, 0x61, 0x73, 0x5f, 0x57, 0x68, 0x69, 0x74, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x92, 0x04, 0x0a, 0x0d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x37, 0x0a, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3b, 0x0a,
	0x0d, 0x68, 0x61, 0x73, 0x68, 0x5f, 0x6c, 0x69, 0x62, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x0b, 0x68,
	0x61, 0x73, 0x68, 0x4c, 0x69, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3f, 0x0a, 0x0f, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x5f, 0x6c, 0x69, 0x62, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x01, 0x52, 0x0d, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x4c, 0x69, 0x62, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x11, 0x61,
	0x6e, 0x74, 0x69, 0x5f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x6e, 0x74, 0x69, 0x56, 0x69, 0x72, 0x75, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x48, 0x02,
	0x52, 0x0f, 0x61, 0x6e, 0x74, 0x69, 0x56, 0x69, 0x72, 0x75, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x4c, 0x0a, 0x13, 0x77, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x5f, 0x65, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x48, 0x03, 0x52, 0x11, 0x77, 0x65,
	0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x12,
	0x46, 0x0a, 0x10, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f,
	0x76, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x48, 0x04, 0x52, 0x0e, 0x76, 0x69, 0x72, 0x75, 0x73, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x42, 0x12, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x68, 0x5f,
	0x68, 0x61, 0x73, 0x68, 0x6c, 0x69, 0x62, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x13, 0x0a, 0x11, 0x68,
	0x61, 0x73, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x6c, 0x69, 0x62, 0x69, 0x6e, 0x66, 0x6f,
	0x42, 0x14, 0x0a, 0x12, 0x68, 0x61, 0x73, 0x5f, 0x61, 0x6e, 0x74, 0x69, 0x76, 0x69, 0x75, 0x73,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x42, 0x18, 0x0a, 0x16, 0x68, 0x61, 0x73, 0x5f, 0x77, 0x65,
	0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72,
	0x42, 0x15, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x5f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x22, 0x76, 0x0a, 0x12, 0x57, 0x65, 0x61, 0x6b, 0x50,
	0x77, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2a, 0x0a,
	0x10, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x15, 0x77, 0x65, 0x61,
	0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x67, 0x65, 0x78, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x77, 0x65, 0x61, 0x6b, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x67, 0x65, 0x78, 0x4c, 0x69, 0x73, 0x74, 0x22,
	0x44, 0x0a, 0x08, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x94, 0x02, 0x0a, 0x0d, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12, 0x3c, 0x0a, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64,
	0x2e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x79, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0d, 0x52, 0x04, 0x64, 0x61, 0x79, 0x73, 0x12, 0x35, 0x0a, 0x0e, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x0c, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65, 0x6c, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x69, 0x6d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x74, 0x65,
	0x6c, 0x79, 0x22, 0x58, 0x0a, 0x09, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12,
	0x0f, 0x0a, 0x0b, 0x53, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00,
	0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x44, 0x41,
	0x59, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x45, 0x54, 0x5f, 0x45, 0x56, 0x45, 0x52, 0x59,
	0x5f, 0x57, 0x45, 0x41, 0x4b, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x54, 0x5f, 0x45,
	0x56, 0x45, 0x52, 0x59, 0x5f, 0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x10, 0x03, 0x22, 0xbb, 0x01, 0x0a,
	0x10, 0x57, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x53, 0x63, 0x61, 0x6e, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x70, 0x61, 0x74, 0x68, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x70,
	0x61, 0x74, 0x68, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3b, 0x0a, 0x0e, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0x79, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x57, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x53, 0x63, 0x61, 0x6e,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45,
	0x0a, 0x12, 0x77, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x5f,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x57, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x53, 0x63, 0x61, 0x6e, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x10, 0x77, 0x65, 0x62, 0x73, 0x68, 0x65, 0x6c, 0x6c, 0x53, 0x63, 0x61,
	0x6e, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb4, 0x07, 0x0a, 0x0f, 0x41, 0x6e, 0x74, 0x69, 0x56, 0x69,
	0x72, 0x75, 0x73, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x12, 0x26, 0x0a, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x70, 0x61, 0x74, 0x68, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x73, 0x63, 0x61, 0x6e, 0x70,
	0x61, 0x74, 0x68, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x3b, 0x0a, 0x0e, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x52, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0b, 0x6d, 0x61, 0x78, 0x46, 0x69, 0x6c,
	0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x41, 0x0a, 0x13, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x73, 0x63,
	0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x08, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x46, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x6e, 0x74, 0x69, 0x53, 0x63, 0x61, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x12, 0x4c, 0x0a, 0x17, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x52, 0x15, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68,
	0x6f, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x12,
	0x49, 0x0a, 0x17, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x14, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x53, 0x63,
	0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x6e,
	0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x13, 0x73, 0x6e, 0x61, 0x70, 0x73,
	0x68, 0x6f, 0x74, 0x4d, 0x61, 0x78, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x47,
	0x0a, 0x13, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x56, 0x30, 0x31, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x10, 0x61, 0x6e, 0x74, 0x69, 0x53, 0x63, 0x61, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x12, 0x27, 0x0a, 0x0f, 0x70, 0x68, 0x69, 0x73, 0x68,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0e, 0x70, 0x68, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x12, 0x2f, 0x0a, 0x14, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x63,
	0x61, 0x6e, 0x5f, 0x6c, 0x69, 0x6e, 0x75, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x72, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x4c, 0x69, 0x6e, 0x75,
	0x78, 0x12, 0x33, 0x0a, 0x16, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73,
	0x63, 0x61, 0x6e, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x72, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x57,
	0x69, 0x6e, 0x64, 0x6f, 0x77, 0x73, 0x12, 0x32, 0x0a, 0x15, 0x75, 0x64, 0x73, 0x69, 0x6b, 0x5f,
	0x70, 0x68, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x75, 0x64, 0x73, 0x69, 0x6b, 0x50, 0x68, 0x69, 0x73,
	0x68, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6d,
	0x5f, 0x70, 0x68, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x6d, 0x50, 0x68, 0x69, 0x73, 0x68, 0x69,
	0x6e, 0x67, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x5f, 0x70, 0x68, 0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x50, 0x68,
	0x69, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x22, 0xae, 0x06, 0x0a,
	0x0f, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69,
	0x73, 0x6b, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x72, 0x69,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x69, 0x72, 0x75,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x6c, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x6c,
	0x77, 0x61, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x41, 0x64, 0x64, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x71, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x72, 0x65, 0x71, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x61,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x74, 0x69, 0x6d,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x73, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x68, 0x61, 0x31, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x73, 0x68, 0x61, 0x31, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64,
	0x35, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x3a, 0x0a,
	0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x17,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x6c, 0x73,
	0x68, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6c,
	0x73, 0x68, 0x48, 0x61, 0x73, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6d, 0x70, 0x5f, 0x68, 0x61,
	0x73, 0x68, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6d, 0x70, 0x48, 0x61, 0x73,
	0x68, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x1a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x87, 0x01,
	0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x42, 0x79, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x52, 0x65, 0x71, 0x12, 0x39, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x73, 0x63, 0x61, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37,
	0x0a, 0x0b, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x69, 0x72, 0x75,
	0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0a, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x58, 0x0a, 0x12, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x42, 0x79, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a,
	0x11, 0x76, 0x69, 0x72, 0x75, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x5f, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x0f, 0x76, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0xca, 0x01, 0x0a, 0x0d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x38, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70,
	0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70,
	0x61, 0x74, 0x68, 0x1a, 0x39, 0x0a, 0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa2,
	0x01, 0x0a, 0x0e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x29, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x31, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x40, 0x0a, 0x12, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x52,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x97, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2a, 0x0a,
	0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x45, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x53, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x2c, 0x0a, 0x11, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x45, 0x6e,
	0x67, 0x69, 0x6e, 0x65, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x6d, 0x70, 0x22,
	0xf6, 0x02, 0x0a, 0x0c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x37, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x28, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4b,
	0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73,
	0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x1a, 0x39, 0x0a,
	0x0b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x86, 0x01, 0x0a, 0x0d, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e,
	0x64, 0x12, 0x2e, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x5d, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68,
	0x50, 0x69, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x7f, 0x0a, 0x10, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x50, 0x69, 0x64, 0x73,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x34, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x50, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x70, 0x69, 0x64,
	0x73, 0x22, 0x5f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68,
	0x50, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63,
	0x50, 0x61, 0x74, 0x68, 0x50, 0x69, 0x64, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x22, 0x69, 0x0a, 0x12, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x30, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x80, 0x01,
	0x0a, 0x11, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x5d, 0x0a, 0x15, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x30, 0x0a, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x04, 0x70, 0x69, 0x64, 0x73, 0x22,
	0x81, 0x01, 0x0a, 0x12, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x22, 0xb5, 0x01, 0x0a, 0x16, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x69, 0x72, 0x75,
	0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x31,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x56, 0x69,
	0x72, 0x75, 0x73, 0x4b, 0x69, 0x6e, 0x64, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x12, 0x32, 0x0a, 0x15, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x61, 0x6e, 0x6e,
	0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x64, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x5f,
	0x73, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x14, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x53, 0x63, 0x61,
	0x6e, 0x6e, 0x65, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x2a, 0x27, 0x0a, 0x07, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x44, 0x44, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x44, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x4f, 0x44, 0x49,
	0x46, 0x59, 0x10, 0x02, 0x2a, 0x1e, 0x0a, 0x06, 0x42, 0x57, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09,
	0x0a, 0x05, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x4c, 0x41,
	0x43, 0x4b, 0x10, 0x01, 0x2a, 0x38, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x45, 0x4d, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x00,
	0x12, 0x0d, 0x0a, 0x09, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x01, 0x12,
	0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x43, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x02, 0x2a, 0x1d,
	0x0a, 0x07, 0x52, 0x65, 0x71, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10, 0x01, 0x2a, 0x3b, 0x0a,
	0x08, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x54, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x54, 0x59, 0x50, 0x45, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x46, 0x54, 0x5f, 0x44, 0x49, 0x52, 0x10, 0x02, 0x2a, 0x44, 0x0a, 0x0c, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x53,
	0x5f, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53,
	0x53, 0x5f, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x53, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x5f, 0x4d, 0x4f, 0x4e, 0x49, 0x54, 0x4f, 0x52, 0x10, 0x02,
	0x2a, 0xdf, 0x18, 0x0a, 0x11, 0x52, 0x69, 0x73, 0x6b, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65,
	0x70, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x49, 0x54, 0x5f, 0x4c,
	0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x73, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x49, 0x54, 0x5f, 0x53, 0x74, 0x61,
	0x63, 0x6b, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x73, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x49, 0x54, 0x5f, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x4f, 0x6e, 0x54, 0x68, 0x65, 0x53, 0x74, 0x61, 0x63,
	0x6b, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x6c,
	0x65, 0x73, 0x73, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x73, 0x10, 0x0f, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x49, 0x54, 0x5f, 0x48, 0x65, 0x61, 0x70, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x73, 0x45,
	0x78, 0x70, 0x6c, 0x6f, 0x69, 0x74, 0x73, 0x10, 0x10, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x54,
	0x5f, 0x52, 0x6f, 0x70, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x73, 0x10, 0x18, 0x12, 0x17, 0x0a,
	0x13, 0x52, 0x49, 0x54, 0x5f, 0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c,
	0x63, 0x6f, 0x64, 0x65, 0x10, 0x19, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x54, 0x5f, 0x53, 0x74,
	0x61, 0x63, 0x6b, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x10, 0x28, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x49,
	0x54, 0x5f, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x10, 0x29, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x54, 0x5f, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x10, 0x2a, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49,
	0x54, 0x5f, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x10, 0x2b,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x69, 0x72, 0x75, 0x73, 0x10, 0x06, 0x12,
	0x22, 0x0a, 0x1e, 0x52, 0x49, 0x54, 0x5f, 0x46, 0x48, 0x5f, 0x44, 0x79, 0x6e, 0x61, 0x6d, 0x69,
	0x63, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x10, 0x0d, 0x12, 0x27, 0x0a, 0x23, 0x52, 0x49, 0x54, 0x5f, 0x46, 0x48, 0x5f, 0x45, 0x6e,
	0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c,
	0x65, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17,
	0x52, 0x49, 0x54, 0x5f, 0x46, 0x48, 0x5f, 0x4f, 0x70, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74,
	0x69, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x10, 0x2f, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x49, 0x54,
	0x5f, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x64, 0x49, 0x6e, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x0a, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x54, 0x5f,
	0x4d, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x10, 0x0b, 0x12,
	0x15, 0x0a, 0x11, 0x52, 0x49, 0x54, 0x5f, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x10, 0x11, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x53, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x53, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x10, 0x2c,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x54, 0x5f, 0x52, 0x65, 0x67, 0x52, 0x69, 0x73, 0x6b, 0x10,
	0x2d, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x54, 0x5f, 0x41, 0x70, 0x70, 0x52, 0x69, 0x73, 0x6b,
	0x10, 0x2e, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x49, 0x54, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c,
	0x52, 0x69, 0x73, 0x6b, 0x10, 0x30, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x54, 0x5f, 0x42, 0x61,
	0x63, 0x6b, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x10, 0x23, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x49, 0x54,
	0x5f, 0x41, 0x53, 0x52, 0x10, 0x24, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x54, 0x5f, 0x48, 0x61,
	0x73, 0x68, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x25, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49, 0x54,
	0x5f, 0x47, 0x6f, 0x6c, 0x64, 0x65, 0x6e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x31, 0x12,
	0x10, 0x0a, 0x0c, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x69, 0x72, 0x74, 0x79, 0x43, 0x6f, 0x77, 0x10,
	0x32, 0x12, 0x1a, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x52, 0x6f, 0x6f, 0x74, 0x6b, 0x69, 0x74,
	0x5f, 0x48, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x10, 0x12, 0x12, 0x19, 0x0a,
	0x15, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x50, 0x72, 0x69,
	0x76, 0x69, 0x6c, 0x65, 0x67, 0x65, 0x10, 0x33, 0x12, 0x16, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f,
	0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x64, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x10, 0x34,
	0x12, 0x17, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x49, 0x6e,
	0x74, 0x65, 0x67, 0x72, 0x69, 0x74, 0x79, 0x10, 0x35, 0x12, 0x20, 0x0a, 0x1c, 0x52, 0x49, 0x54,
	0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4c, 0x65, 0x61,
	0x6b, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f, 0x77, 0x10, 0x36, 0x12, 0x18, 0x0a, 0x14, 0x52,
	0x49, 0x54, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x48, 0x69, 0x64, 0x65, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x10, 0x37, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x49, 0x54, 0x5f, 0x57, 0x6f, 0x72,
	0x6d, 0x52, 0x69, 0x73, 0x6b, 0x10, 0x38, 0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x49, 0x54, 0x5f, 0x43,
	0x56, 0x45, 0x4c, 0x65, 0x61, 0x6b, 0x10, 0x39, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x49, 0x54, 0x5f,
	0x4c, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x10, 0x3a, 0x12,
	0x19, 0x0a, 0x15, 0x52, 0x49, 0x54, 0x5f, 0x52, 0x6f, 0x6f, 0x74, 0x6b, 0x69, 0x74, 0x5f, 0x52,
	0x65, 0x75, 0x73, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x10, 0x49, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x49,
	0x54, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x45, 0x58, 0x52, 0x69, 0x73, 0x6b, 0x10, 0x4a,
	0x12, 0x13, 0x0a, 0x0f, 0x52, 0x49, 0x54, 0x5f, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x69, 0x73, 0x6b, 0x10, 0x4b, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x10, 0x4d, 0x12,
	0x1b, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x72, 0x6f, 0x63, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x50, 0x6f, 0x72, 0x74, 0x44, 0x54, 0x79, 0x70, 0x65, 0x10, 0x51, 0x12, 0x16, 0x0a, 0x12,
	0x52, 0x49, 0x54, 0x5f, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x10, 0x52, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x10, 0x8c, 0x01, 0x12, 0x1e, 0x0a,
	0x19, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x8d, 0x01, 0x12, 0x1c, 0x0a,
	0x17, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x57, 0x69, 0x74, 0x68, 0x43, 0x72, 0x65, 0x64, 0x10, 0x8e, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x52,
	0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x72, 0x53, 0x72, 0x76, 0x4f, 0x62, 0x6a, 0x10, 0x8f, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x52,
	0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x50, 0x6c, 0x61, 0x6e,
	0x54, 0x61, 0x73, 0x6b, 0x10, 0x90, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x44,
	0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x41, 0x64, 0x64, 0x53, 0x79, 0x73, 0x53, 0x72, 0x76, 0x10, 0x91,
	0x01, 0x12, 0x19, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x50,
	0x73, 0x4c, 0x6f, 0x67, 0x67, 0x65, 0x64, 0x4f, 0x6e, 0x10, 0x92, 0x01, 0x12, 0x1b, 0x0a, 0x16,
	0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4d, 0x6f, 0x64, 0x69, 0x53, 0x65,
	0x6e, 0x73, 0x65, 0x47, 0x72, 0x70, 0x10, 0x93, 0x01, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54,
	0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x76, 0x65, 0x34, 0x32, 0x32, 0x37, 0x38, 0x10,
	0x94, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x41, 0x63, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x95, 0x01, 0x12, 0x17, 0x0a, 0x12,
	0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x76, 0x65, 0x34, 0x32, 0x32,
	0x38, 0x37, 0x10, 0x96, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x53, 0x41, 0x4d, 0x52, 0x51, 0x72, 0x79, 0x55, 0x73, 0x72, 0x47, 0x72, 0x70,
	0x10, 0x97, 0x01, 0x12, 0x19, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69,
	0x6e, 0x41, 0x53, 0x52, 0x45, 0x50, 0x52, 0x6f, 0x61, 0x73, 0x74, 0x10, 0x98, 0x01, 0x12, 0x20,
	0x0a, 0x1b, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x73, 0x63,
	0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52, 0x6f, 0x6f, 0x74, 0x10, 0x99, 0x01,
	0x12, 0x22, 0x0a, 0x1d, 0x52, 0x49, 0x54, 0x5f, 0x54, 0x69, 0x6d, 0x65, 0x64, 0x54, 0x61, 0x73,
	0x6b, 0x45, 0x73, 0x63, 0x61, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x52, 0x6f, 0x6f,
	0x74, 0x10, 0x9a, 0x01, 0x12, 0x16, 0x0a, 0x11, 0x52, 0x49, 0x54, 0x5f, 0x48, 0x61, 0x73, 0x68,
	0x41, 0x6e, 0x74, 0x69, 0x76, 0x69, 0x72, 0x75, 0x73, 0x10, 0x9b, 0x01, 0x12, 0x1c, 0x0a, 0x17,
	0x52, 0x49, 0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4c,
	0x6f, 0x67, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x10, 0x9c, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x49,
	0x54, 0x5f, 0x44, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5a, 0x65, 0x72, 0x6f, 0x4c, 0x6f, 0x67, 0x6f,
	0x6e, 0x10, 0x9d, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x57, 0x65, 0x62, 0x53,
	0x68, 0x65, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x10,
	0x9e, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x41, 0x6e, 0x74, 0x69, 0x56, 0x69,
	0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x10, 0xa0, 0x01, 0x12, 0x11, 0x0a, 0x0d,
	0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x4c, 0x6f, 0x61, 0x64, 0x53, 0x6f, 0x10, 0x67, 0x12,
	0x12, 0x0a, 0x0e, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x44, 0x72, 0x61, 0x67, 0x4c, 0x69,
	0x62, 0x10, 0x68, 0x12, 0x18, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x10, 0x69, 0x12, 0x22, 0x0a,
	0x1e, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x10,
	0x72, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x57, 0x65, 0x62, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x53, 0x63, 0x61, 0x6e, 0x10, 0x73,
	0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x61, 0x6e, 0x10, 0x74,
	0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x46, 0x69, 0x6c, 0x65, 0x4f,
	0x70, 0x10, 0x75, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x56, 0x69,
	0x72, 0x43, 0x6d, 0x64, 0x10, 0x76, 0x12, 0x14, 0x0a, 0x0f, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56,
	0x4d, 0x53, 0x63, 0x61, 0x6e, 0x50, 0x6f, 0x72, 0x74, 0x10, 0x83, 0x01, 0x12, 0x13, 0x0a, 0x0e,
	0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x4a, 0x61, 0x72, 0x4c, 0x6f, 0x61, 0x64, 0x10, 0x84,
	0x01, 0x12, 0x0f, 0x0a, 0x0a, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x5a, 0x69, 0x70, 0x10,
	0x85, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x55, 0x6e, 0x7a,
	0x69, 0x70, 0x10, 0x86, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d,
	0x48, 0x54, 0x54, 0x50, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x10,
	0x87, 0x01, 0x12, 0x15, 0x0a, 0x10, 0x52, 0x49, 0x54, 0x5f, 0x4a, 0x56, 0x4d, 0x53, 0x71, 0x6c,
	0x49, 0x6e, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x88, 0x01, 0x12, 0x12, 0x0a, 0x0d, 0x52, 0x49, 0x54,
	0x5f, 0x4a, 0x56, 0x4d, 0x52, 0x75, 0x6e, 0x43, 0x6d, 0x64, 0x10, 0xac, 0x02, 0x12, 0x19, 0x0a,
	0x14, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x48, 0x50, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x10, 0xc3, 0x02, 0x12, 0x14, 0x0a, 0x0f, 0x52, 0x49, 0x54, 0x5f,
	0x50, 0x48, 0x50, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x65, 0x63, 0x10, 0xc4, 0x02, 0x12, 0x18,
	0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x50, 0x48, 0x50, 0x43, 0x61, 0x6c, 0x6c, 0x42, 0x61, 0x63,
	0x6b, 0x46, 0x75, 0x6e, 0x63, 0x10, 0xc5, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x52, 0x49, 0x54, 0x5f,
	0x50, 0x48, 0x50, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x75, 0x73, 0x70, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x10, 0xc6, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x52,
	0x49, 0x54, 0x5f, 0x50, 0x48, 0x50, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0xc7, 0x02, 0x12, 0x1e, 0x0a, 0x19,
	0x52, 0x49, 0x54, 0x5f, 0x50, 0x48, 0x50, 0x45, 0x78, 0x65, 0x63, 0x53, 0x75, 0x73, 0x70, 0x69,
	0x63, 0x69, 0x6f, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x65, 0x10, 0xc8, 0x02, 0x12, 0x1a, 0x0a, 0x15,
	0x52, 0x49, 0x54, 0x5f, 0x41, 0x62, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x52, 0x69, 0x73, 0x6b, 0x10, 0xd8, 0x04, 0x12, 0x14, 0x0a, 0x0f, 0x52, 0x49, 0x54, 0x5f,
	0x56, 0x69, 0x6f, 0x6c, 0x65, 0x6e, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x10, 0xd9, 0x04, 0x12, 0x17,
	0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x57, 0x65, 0x61, 0x6b, 0x50, 0x61, 0x73, 0x73, 0x77, 0x64,
	0x52, 0x69, 0x73, 0x6b, 0x10, 0xda, 0x04, 0x12, 0x18, 0x0a, 0x13, 0x52, 0x49, 0x54, 0x5f, 0x56,
	0x30, 0x31, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x10, 0xe9,
	0x07, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x49, 0x6c, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x10, 0xea, 0x07, 0x12,
	0x1d, 0x0a, 0x18, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x75, 0x70, 0x70, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x10, 0xeb, 0x07, 0x12, 0x17,
	0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x52, 0x6f, 0x70, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x73, 0x10, 0xec, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x56,
	0x30, 0x31, 0x5f, 0x42, 0x75, 0x66, 0x66, 0x65, 0x72, 0x4f, 0x76, 0x65, 0x72, 0x66, 0x6c, 0x6f,
	0x77, 0x10, 0xed, 0x07, 0x12, 0x20, 0x0a, 0x1b, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f,
	0x48, 0x65, 0x61, 0x70, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x73, 0x45, 0x78, 0x70, 0x6c, 0x6f,
	0x69, 0x74, 0x73, 0x10, 0xee, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30,
	0x31, 0x5f, 0x48, 0x65, 0x61, 0x70, 0x53, 0x74, 0x61, 0x63, 0x6b, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x10, 0xef, 0x07, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f,
	0x4c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x63, 0x6f, 0x64, 0x65, 0x10,
	0xf0, 0x07, 0x12, 0x1d, 0x0a, 0x18, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x52, 0x75,
	0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x10, 0xf1,
	0x07, 0x12, 0x21, 0x0a, 0x1c, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x4c, 0x6f, 0x61,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65,
	0x73, 0x10, 0xf2, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f,
	0x53, 0x74, 0x61, 0x63, 0x6b, 0x50, 0x69, 0x76, 0x6f, 0x74, 0x10, 0xf3, 0x07, 0x12, 0x19, 0x0a,
	0x14, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x10, 0xf4, 0x07, 0x12, 0x12, 0x0a, 0x0d, 0x52, 0x49, 0x54, 0x5f,
	0x56, 0x30, 0x31, 0x5f, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x10, 0xf5, 0x07, 0x12, 0x22, 0x0a, 0x1d,
	0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x4b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x42, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x54, 0x72, 0x61, 0x63, 0x69, 0x6e, 0x67, 0x10, 0xf6, 0x07,
	0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x45, 0x76, 0x69, 0x6c,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x10, 0xf7, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54,
	0x5f, 0x56, 0x30, 0x31, 0x5f, 0x4c, 0x65, 0x61, 0x6b, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x10,
	0xf8, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x53, 0x68,
	0x65, 0x6c, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x50, 0x6f, 0x77, 0x65, 0x72, 0x53, 0x68,
	0x65, 0x6c, 0x6c, 0x10, 0xf9, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30,
	0x31, 0x5f, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x57, 0x4d, 0x49,
	0x10, 0xfa, 0x07, 0x12, 0x1b, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x53,
	0x68, 0x65, 0x6c, 0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x42, 0x41, 0x10, 0xfb, 0x07,
	0x12, 0x1b, 0x0a, 0x16, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x53, 0x68, 0x65, 0x6c,
	0x6c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x56, 0x42, 0x53, 0x10, 0xfc, 0x07, 0x12, 0x1d, 0x0a,
	0x18, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x53, 0x68, 0x65, 0x6c, 0x6c, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x10, 0xfd, 0x07, 0x12, 0x18, 0x0a, 0x13,
	0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x49,
	0x43, 0x4d, 0x50, 0x10, 0xfe, 0x07, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30,
	0x31, 0x5f, 0x54, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x5f, 0x44, 0x4e, 0x53, 0x10, 0xff, 0x07, 0x12,
	0x17, 0x0a, 0x12, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x41, 0x43, 0x44, 0x52, 0x53,
	0x77, 0x69, 0x74, 0x63, 0x68, 0x10, 0x80, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x49, 0x54, 0x5f,
	0x56, 0x30, 0x31, 0x5f, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x10, 0x81, 0x08, 0x12, 0x21, 0x0a, 0x1c, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x55, 0x53, 0x50, 0x49, 0x43, 0x49, 0x4f, 0x55, 0x53, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x10, 0x82, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x49, 0x54, 0x5f, 0x56,
	0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x83, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x56, 0x42, 0x41, 0x10, 0x84, 0x08, 0x12, 0x1c, 0x0a, 0x17, 0x52, 0x49,
	0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4c, 0x4e, 0x4b, 0x5f, 0x54,
	0x41, 0x52, 0x47, 0x45, 0x54, 0x10, 0x85, 0x08, 0x12, 0x1a, 0x0a, 0x15, 0x52, 0x49, 0x54, 0x5f,
	0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4c, 0x4e, 0x4b, 0x5f, 0x57, 0x4f, 0x52,
	0x4b, 0x10, 0x86, 0x08, 0x12, 0x19, 0x0a, 0x14, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4c, 0x4e, 0x4b, 0x5f, 0x43, 0x4d, 0x44, 0x10, 0x87, 0x08, 0x12,
	0x1a, 0x0a, 0x15, 0x52, 0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x4c, 0x4e, 0x4b, 0x5f, 0x49, 0x43, 0x4f, 0x4e, 0x10, 0x88, 0x08, 0x12, 0x15, 0x0a, 0x10, 0x52,
	0x49, 0x54, 0x5f, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x55, 0x52, 0x4c, 0x10,
	0x89, 0x08, 0x2a, 0x6b, 0x0a, 0x11, 0x4e, 0x47, 0x41, 0x56, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x43, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x43, 0x54, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x43, 0x54, 0x5f, 0x4e, 0x45,
	0x54, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45,
	0x53, 0x53, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x43, 0x54, 0x5f, 0x4d, 0x45, 0x4d, 0x10,
	0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x43, 0x54, 0x5f, 0x4d, 0x49, 0x53, 0x43, 0x10, 0x05, 0x2a,
	0x71, 0x0a, 0x10, 0x50, 0x54, 0x48, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x63, 0x65, 0x70, 0x74, 0x4d,
	0x6f, 0x64, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x54, 0x48, 0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x54, 0x48, 0x5f, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x79, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x54, 0x48,
	0x5f, 0x41, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x54,
	0x48, 0x5f, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x54, 0x48,
	0x5f, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x79, 0x41, 0x6e, 0x64, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x10, 0x04, 0x2a, 0x3b, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x61, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x4f, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x4f, 0x5f, 0x43, 0x59, 0x43, 0x4c, 0x45, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x53, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x2a,
	0x70, 0x0a, 0x11, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55,
	0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45, 0x53, 0x55,
	0x4c, 0x54, 0x5f, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e,
	0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10,
	0x04, 0x2a, 0x64, 0x0a, 0x13, 0x56, 0x69, 0x72, 0x75, 0x73, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x50, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x45, 0x54, 0x45,
	0x43, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4c, 0x4f, 0x57, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x4f,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x4d, 0x49, 0x44, 0x44, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x18, 0x0a,
	0x14, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x4f, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x48, 0x49, 0x47, 0x48, 0x10, 0x02, 0x2a, 0x30, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x2a, 0x5b, 0x0a, 0x0f, 0x44, 0x65, 0x74,
	0x65, 0x63, 0x74, 0x4b, 0x69, 0x6e, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x17, 0x0a, 0x13,
	0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e,
	0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x10, 0x01, 0x12,
	0x15, 0x0a, 0x11, 0x44, 0x45, 0x54, 0x45, 0x43, 0x54, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x56,
	0x49, 0x52, 0x55, 0x53, 0x10, 0x02, 0x2a, 0x7f, 0x0a, 0x0d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x47, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x43, 0x4c, 0x41, 0x53, 0x53, 0x10, 0x01, 0x12,
	0x1c, 0x0a, 0x18, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44,
	0x5f, 0x4e, 0x47, 0x41, 0x56, 0x10, 0x03, 0x2a, 0xd6, 0x01, 0x0a, 0x0f, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x47,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x41,
	0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x47, 0x45, 0x4e,
	0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x55,
	0x4d, 0x50, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x47, 0x45,
	0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e,
	0x41, 0x47, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x52, 0x55, 0x4e, 0x10, 0x05,
	0x2a, 0x98, 0x03, 0x0a, 0x0b, 0x56, 0x30, 0x31, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x19, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x56,
	0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x58, 0x45,
	0x43, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30,
	0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x4c, 0x53, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x44, 0x46, 0x10, 0x04, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x50, 0x54, 0x10, 0x05, 0x12,
	0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x44, 0x4c, 0x4c, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49,
	0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x41, 0x54, 0x10, 0x07, 0x12, 0x15, 0x0a,
	0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x53, 0x49, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4f, 0x44, 0x54, 0x10, 0x09, 0x12, 0x14, 0x0a, 0x10, 0x56,
	0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x42, 0x10,
	0x0a, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4a, 0x53, 0x10, 0x0b, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x5f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x53, 0x10, 0x0c, 0x12, 0x14, 0x0a,
	0x10, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50,
	0x59, 0x10, 0x0d, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x48, 0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x30, 0x31,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4e, 0x4b, 0x10, 0x0f,
	0x12, 0x19, 0x0a, 0x15, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x41, 0x52, 0x43, 0x48, 0x49, 0x56, 0x45, 0x10, 0x10, 0x2a, 0xbc, 0x01, 0x0a, 0x11,
	0x56, 0x30, 0x31, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x63, 0x61, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x20, 0x0a, 0x1c, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x43,
	0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x56, 0x30, 0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x53, 0x43, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x4d, 0x4d, 0x45, 0x4e, 0x44, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x30, 0x31,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x47, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x30,
	0x31, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46,
	0x49, 0x47, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x56, 0x30, 0x31,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49,
	0x47, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x10, 0x04, 0x2a, 0x71, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x50, 0x69, 0x64, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x1a, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x5f,
	0x50, 0x41, 0x54, 0x48, 0x5f, 0x50, 0x49, 0x44, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x5f,
	0x50, 0x41, 0x54, 0x48, 0x5f, 0x50, 0x49, 0x44, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x01,
	0x12, 0x1b, 0x0a, 0x17, 0x47, 0x45, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x5f, 0x50, 0x41, 0x54,
	0x48, 0x5f, 0x50, 0x49, 0x44, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0x79, 0x0a,
	0x11, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x54, 0x48, 0x52, 0x45, 0x41, 0x54, 0x45, 0x4e, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x48, 0x52, 0x45, 0x41, 0x54,
	0x45, 0x4e, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x48, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x4e, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c,
	0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0x54, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65,
	0x56, 0x69, 0x72, 0x75, 0x73, 0x4b, 0x69, 0x6e, 0x64, 0x12, 0x12, 0x0a, 0x0e, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x16, 0x0a,
	0x12, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x57, 0x45, 0x42, 0x53, 0x48,
	0x45, 0x4c, 0x4c, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x54, 0x49, 0x56, 0x49, 0x52, 0x55, 0x53, 0x10, 0x02, 0x2a, 0x46,
	0x0a, 0x10, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e,
	0x75, 0x6d, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x55, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x54, 0x41, 0x52, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x4f, 0x50, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_policy_proto_rawDescOnce sync.Once
	file_agent_policy_proto_rawDescData = file_agent_policy_proto_rawDesc
)

func file_agent_policy_proto_rawDescGZIP() []byte {
	file_agent_policy_proto_rawDescOnce.Do(func() {
		file_agent_policy_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_policy_proto_rawDescData)
	})
	return file_agent_policy_proto_rawDescData
}

var file_agent_policy_proto_enumTypes = make([]protoimpl.EnumInfo, 26)
var file_agent_policy_proto_msgTypes = make([]protoimpl.MessageInfo, 48)
var file_agent_policy_proto_goTypes = []any{
	(Operate)(0),                       // 0: agent.Operate
	(BWType)(0),                        // 1: agent.BWType
	(ModuleType)(0),                    // 2: agent.ModuleType
	(ReqType)(0),                       // 3: agent.ReqType
	(FileType)(0),                      // 4: agent.FileType
	(SwitchStatus)(0),                  // 5: agent.SwitchStatus
	(RiskInterceptType)(0),             // 6: agent.RiskInterceptType
	(NGAVCollectorType)(0),             // 7: agent.NGAVCollectorType
	(PTHInterceptMode)(0),              // 8: agent.PTHInterceptMode
	(TaskScanType)(0),                  // 9: agent.TaskScanType
	(VirusDetectResult)(0),             // 10: agent.VirusDetectResult
	(VirusDetectPriority)(0),           // 11: agent.VirusDetectPriority
	(FileStatus)(0),                    // 12: agent.FileStatus
	(DetectKindIdent)(0),               // 13: agent.DetectKindIdent
	(AgentFileKind)(0),                 // 14: agent.AgentFileKind
	(AgentFileStatus)(0),               // 15: agent.AgentFileStatus
	(V01FileType)(0),                   // 16: agent.V01FileType
	(V01FileScanConfig)(0),             // 17: agent.V01FileScanConfig
	(GetProcPathPidsResult)(0),         // 18: agent.GetProcPathPidsResult
	(ThreatenHandleRes)(0),             // 19: agent.ThreatenHandleRes
	(FileVirusKind)(0),                 // 20: agent.FileVirusKind
	(SwitchStatusEnum)(0),              // 21: agent.SwitchStatusEnum
	(LonginTimes_Frequency)(0),         // 22: agent.LonginTimes.Frequency
	(AbnormalLoginConfig_StudyMode)(0), // 23: agent.AbnormalLoginConfig.StudyMode
	(BWRuleDetail_Scope)(0),            // 24: agent.BWRuleDetail.Scope
	(TimeScheduled_Frequency)(0),       // 25: agent.TimeScheduled.Frequency
	(*BlackWhiteItem)(nil),             // 26: agent.BlackWhiteItem
	(*UpdateBlackWhitePolicy)(nil),     // 27: agent.UpdateBlackWhitePolicy
	(*FileMonitorItem)(nil),            // 28: agent.FileMonitorItem
	(*UpdateFileMonitorPolicy)(nil),    // 29: agent.UpdateFileMonitorPolicy
	(*RequestPolicyList)(nil),          // 30: agent.RequestPolicyList
	(*RiskInterceptSwitchMsg)(nil),     // 31: agent.RiskInterceptSwitchMsg
	(*RiskBehaviorSwitch)(nil),         // 32: agent.RiskBehaviorSwitch
	(*GlobalSwitch)(nil),               // 33: agent.GlobalSwitch
	(*PTHPolicy)(nil),                  // 34: agent.PTHPolicy
	(*CustomFileBlackWhitePolicy)(nil), // 35: agent.CustomFileBlackWhitePolicy
	(*CustomFileBlackWhiteItem)(nil),   // 36: agent.CustomFileBlackWhiteItem
	(*DigitalSignaturePolicy)(nil),     // 37: agent.DigitalSignaturePolicy
	(*DigitalSignatureItem)(nil),       // 38: agent.DigitalSignatureItem
	(*KernelInjectItem)(nil),           // 39: agent.KernelInjectItem
	(*KernelInjectList)(nil),           // 40: agent.KernelInjectList
	(*LonginTimes)(nil),                // 41: agent.LonginTimes
	(*AbnormalLoginConfig)(nil),        // 42: agent.AbnormalLoginConfig
	(*UpdateFileInfo)(nil),             // 43: agent.UpdateFileInfo
	(*BWRuleDetail)(nil),               // 44: agent.BWRuleDetail
	(*BWRulePkg)(nil),                  // 45: agent.BWRulePkg
	(*BWRuleList)(nil),                 // 46: agent.BWRuleList
	(*Settings)(nil),                   // 47: agent.Settings
	(*AgentSettings)(nil),              // 48: agent.AgentSettings
	(*WeakPwdListReponse)(nil),         // 49: agent.WeakPwdListReponse
	(*TimeItem)(nil),                   // 50: agent.TimeItem
	(*TimeScheduled)(nil),              // 51: agent.TimeScheduled
	(*WebshellScanItem)(nil),           // 52: agent.WebshellScanItem
	(*UpdateWebshellScanPolicy)(nil),   // 53: agent.UpdateWebshellScanPolicy
	(*AntiVirusPolicy)(nil),            // 54: agent.AntiVirusPolicy
	(*VirusDetectItem)(nil),            // 55: agent.VirusDetectItem
	(*SearchBySha256Req)(nil),          // 56: agent.SearchBySha256Req
	(*SearchBySha256Resp)(nil),         // 57: agent.SearchBySha256Resp
	(*UploadFileReq)(nil),              // 58: agent.UploadFileReq
	(*UploadFileResp)(nil),             // 59: agent.UploadFileResp
	(*DetectEngineVerReq)(nil),         // 60: agent.DetectEngineVerReq
	(*DetectEngineVerResp)(nil),        // 61: agent.DetectEngineVerResp
	(*AgentFileReq)(nil),               // 62: agent.AgentFileReq
	(*AgentFileResp)(nil),              // 63: agent.AgentFileResp
	(*GetProcPathPidsReq)(nil),         // 64: agent.GetProcPathPidsReq
	(*ProcPathPidsItem)(nil),           // 65: agent.ProcPathPidsItem
	(*GetProcPathPidsResp)(nil),        // 66: agent.GetProcPathPidsResp
	(*ThreatenHandleItem)(nil),         // 67: agent.ThreatenHandleItem
	(*ThreatenHandleReq)(nil),          // 68: agent.ThreatenHandleReq
	(*ThreatenHandleResItem)(nil),      // 69: agent.ThreatenHandleResItem
	(*ThreatenHandleResp)(nil),         // 70: agent.ThreatenHandleResp
	(*FileVirusDetectionInfo)(nil),     // 71: agent.FileVirusDetectionInfo
	nil,                                // 72: agent.UploadFileReq.ParamsEntry
	nil,                                // 73: agent.AgentFileReq.ParamsEntry
	(FileTypeIdent)(0),                 // 74: agent.FileTypeIdent
	(*SignatureInfo)(nil),              // 75: agent.SignatureInfo
}
var file_agent_policy_proto_depIdxs = []int32{
	2,  // 0: agent.BlackWhiteItem.module:type_name -> agent.ModuleType
	1,  // 1: agent.BlackWhiteItem.bwType:type_name -> agent.BWType
	0,  // 2: agent.BlackWhiteItem.addOrDel:type_name -> agent.Operate
	26, // 3: agent.UpdateBlackWhitePolicy.blackwhiteList:type_name -> agent.BlackWhiteItem
	28, // 4: agent.UpdateFileMonitorPolicy.fileMonitorList:type_name -> agent.FileMonitorItem
	3,  // 5: agent.RequestPolicyList.reqType:type_name -> agent.ReqType
	6,  // 6: agent.RiskInterceptSwitchMsg.riskInterceptOff:type_name -> agent.RiskInterceptType
	6,  // 7: agent.RiskInterceptSwitchMsg.riskOn:type_name -> agent.RiskInterceptType
	32, // 8: agent.RiskInterceptSwitchMsg.riskBehaviorSwitch:type_name -> agent.RiskBehaviorSwitch
	5,  // 9: agent.RiskBehaviorSwitch.riskSwitch:type_name -> agent.SwitchStatus
	40, // 10: agent.GlobalSwitch.kernelInjectList:type_name -> agent.KernelInjectList
	8,  // 11: agent.PTHPolicy.pthMode:type_name -> agent.PTHInterceptMode
	36, // 12: agent.CustomFileBlackWhitePolicy.bwList:type_name -> agent.CustomFileBlackWhiteItem
	1,  // 13: agent.CustomFileBlackWhiteItem.bwType:type_name -> agent.BWType
	38, // 14: agent.DigitalSignaturePolicy.dsList:type_name -> agent.DigitalSignatureItem
	1,  // 15: agent.DigitalSignatureItem.bwType:type_name -> agent.BWType
	39, // 16: agent.KernelInjectList.kernelInjectItem:type_name -> agent.KernelInjectItem
	22, // 17: agent.LonginTimes.longin_frequency:type_name -> agent.LonginTimes.Frequency
	41, // 18: agent.AbnormalLoginConfig.login_times:type_name -> agent.LonginTimes
	23, // 19: agent.AbnormalLoginConfig.study_mode:type_name -> agent.AbnormalLoginConfig.StudyMode
	24, // 20: agent.BWRuleDetail.scope:type_name -> agent.BWRuleDetail.Scope
	44, // 21: agent.BWRulePkg.rule_details:type_name -> agent.BWRuleDetail
	45, // 22: agent.BWRuleList.rule_list:type_name -> agent.BWRulePkg
	31, // 23: agent.Settings.riskInterceptSwitch:type_name -> agent.RiskInterceptSwitchMsg
	33, // 24: agent.Settings.globalSwitch:type_name -> agent.GlobalSwitch
	42, // 25: agent.Settings.abnormal_login_config:type_name -> agent.AbnormalLoginConfig
	46, // 26: agent.Settings.white_rule_list:type_name -> agent.BWRuleList
	47, // 27: agent.AgentSettings.serverSettings:type_name -> agent.Settings
	43, // 28: agent.AgentSettings.hash_lib_info:type_name -> agent.UpdateFileInfo
	43, // 29: agent.AgentSettings.sha256_lib_info:type_name -> agent.UpdateFileInfo
	54, // 30: agent.AgentSettings.anti_virus_policy:type_name -> agent.AntiVirusPolicy
	61, // 31: agent.AgentSettings.webshell_engine_ver:type_name -> agent.DetectEngineVerResp
	61, // 32: agent.AgentSettings.virus_engine_ver:type_name -> agent.DetectEngineVerResp
	25, // 33: agent.TimeScheduled.frequency:type_name -> agent.TimeScheduled.Frequency
	50, // 34: agent.TimeScheduled.time_item_list:type_name -> agent.TimeItem
	51, // 35: agent.WebshellScanItem.time_scheduled:type_name -> agent.TimeScheduled
	52, // 36: agent.UpdateWebshellScanPolicy.webshell_scan_list:type_name -> agent.WebshellScanItem
	51, // 37: agent.AntiVirusPolicy.time_scheduled:type_name -> agent.TimeScheduled
	16, // 38: agent.AntiVirusPolicy.anti_scan_type_list:type_name -> agent.V01FileType
	51, // 39: agent.AntiVirusPolicy.snapshot_time_scheduled:type_name -> agent.TimeScheduled
	16, // 40: agent.AntiVirusPolicy.snapshot_scan_type_list:type_name -> agent.V01FileType
	17, // 41: agent.AntiVirusPolicy.anti_scan_type_conf:type_name -> agent.V01FileScanConfig
	10, // 42: agent.VirusDetectItem.result:type_name -> agent.VirusDetectResult
	74, // 43: agent.VirusDetectItem.file_type:type_name -> agent.FileTypeIdent
	75, // 44: agent.VirusDetectItem.signatureInfo:type_name -> agent.SignatureInfo
	9,  // 45: agent.SearchBySha256Req.task_scan_type:type_name -> agent.TaskScanType
	55, // 46: agent.SearchBySha256Req.sha256_list:type_name -> agent.VirusDetectItem
	55, // 47: agent.SearchBySha256Resp.virus_detect_List:type_name -> agent.VirusDetectItem
	72, // 48: agent.UploadFileReq.params:type_name -> agent.UploadFileReq.ParamsEntry
	12, // 49: agent.UploadFileResp.result:type_name -> agent.FileStatus
	74, // 50: agent.UploadFileResp.file_type:type_name -> agent.FileTypeIdent
	13, // 51: agent.DetectEngineVerReq.kind:type_name -> agent.DetectKindIdent
	13, // 52: agent.DetectEngineVerResp.kind:type_name -> agent.DetectKindIdent
	73, // 53: agent.AgentFileReq.params:type_name -> agent.AgentFileReq.ParamsEntry
	14, // 54: agent.AgentFileReq.kind:type_name -> agent.AgentFileKind
	14, // 55: agent.AgentFileResp.kind:type_name -> agent.AgentFileKind
	15, // 56: agent.AgentFileResp.status:type_name -> agent.AgentFileStatus
	18, // 57: agent.ProcPathPidsItem.result:type_name -> agent.GetProcPathPidsResult
	65, // 58: agent.GetProcPathPidsResp.list:type_name -> agent.ProcPathPidsItem
	69, // 59: agent.ThreatenHandleItem.list:type_name -> agent.ThreatenHandleResItem
	67, // 60: agent.ThreatenHandleReq.list:type_name -> agent.ThreatenHandleItem
	19, // 61: agent.ThreatenHandleResItem.result:type_name -> agent.ThreatenHandleRes
	67, // 62: agent.ThreatenHandleResp.list:type_name -> agent.ThreatenHandleItem
	20, // 63: agent.FileVirusDetectionInfo.file_kind:type_name -> agent.FileVirusKind
	64, // [64:64] is the sub-list for method output_type
	64, // [64:64] is the sub-list for method input_type
	64, // [64:64] is the sub-list for extension type_name
	64, // [64:64] is the sub-list for extension extendee
	0,  // [0:64] is the sub-list for field type_name
}

func init() { file_agent_policy_proto_init() }
func file_agent_policy_proto_init() {
	if File_agent_policy_proto != nil {
		return
	}
	file_agent_common_proto_init()
	file_agent_policy_proto_msgTypes[21].OneofWrappers = []any{
		(*Settings_UsualLogonThreshold)(nil),
		(*Settings_WeakPasswordListTimestamp)(nil),
		(*Settings_RiskInterceptSwitch)(nil),
		(*Settings_GlobalSwitch)(nil),
		(*Settings_AbnormalLoginConfig)(nil),
		(*Settings_WhiteRuleList)(nil),
	}
	file_agent_policy_proto_msgTypes[22].OneofWrappers = []any{
		(*AgentSettings_HashLibInfo)(nil),
		(*AgentSettings_Sha256LibInfo)(nil),
		(*AgentSettings_AntiVirusPolicy)(nil),
		(*AgentSettings_WebshellEngineVer)(nil),
		(*AgentSettings_VirusEngineVer)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_policy_proto_rawDesc,
			NumEnums:      26,
			NumMessages:   48,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_policy_proto_goTypes,
		DependencyIndexes: file_agent_policy_proto_depIdxs,
		EnumInfos:         file_agent_policy_proto_enumTypes,
		MessageInfos:      file_agent_policy_proto_msgTypes,
	}.Build()
	File_agent_policy_proto = out.File
	file_agent_policy_proto_rawDesc = nil
	file_agent_policy_proto_goTypes = nil
	file_agent_policy_proto_depIdxs = nil
}
