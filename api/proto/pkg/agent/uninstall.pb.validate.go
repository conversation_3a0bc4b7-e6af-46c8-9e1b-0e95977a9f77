// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/uninstall.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UninstallAgent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UninstallAgent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UninstallAgent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UninstallAgentMultiError,
// or nil if none found.
func (m *UninstallAgent) ValidateAll() error {
	return m.validate(true)
}

func (m *UninstallAgent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UninstallAgentMultiError(errors)
	}

	return nil
}

// UninstallAgentMultiError is an error wrapping multiple validation errors
// returned by UninstallAgent.ValidateAll() if the designated constraints
// aren't met.
type UninstallAgentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UninstallAgentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UninstallAgentMultiError) AllErrors() []error { return m }

// UninstallAgentValidationError is the validation error returned by
// UninstallAgent.Validate if the designated constraints aren't met.
type UninstallAgentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UninstallAgentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UninstallAgentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UninstallAgentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UninstallAgentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UninstallAgentValidationError) ErrorName() string { return "UninstallAgentValidationError" }

// Error satisfies the builtin error interface
func (e UninstallAgentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUninstallAgent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UninstallAgentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UninstallAgentValidationError{}

// Validate checks the field values on UninstallAgentResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UninstallAgentResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UninstallAgentResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UninstallAgentResultMultiError, or nil if none found.
func (m *UninstallAgentResult) ValidateAll() error {
	return m.validate(true)
}

func (m *UninstallAgentResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ErrorCode

	if len(errors) > 0 {
		return UninstallAgentResultMultiError(errors)
	}

	return nil
}

// UninstallAgentResultMultiError is an error wrapping multiple validation
// errors returned by UninstallAgentResult.ValidateAll() if the designated
// constraints aren't met.
type UninstallAgentResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UninstallAgentResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UninstallAgentResultMultiError) AllErrors() []error { return m }

// UninstallAgentResultValidationError is the validation error returned by
// UninstallAgentResult.Validate if the designated constraints aren't met.
type UninstallAgentResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UninstallAgentResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UninstallAgentResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UninstallAgentResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UninstallAgentResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UninstallAgentResultValidationError) ErrorName() string {
	return "UninstallAgentResultValidationError"
}

// Error satisfies the builtin error interface
func (e UninstallAgentResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUninstallAgentResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UninstallAgentResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UninstallAgentResultValidationError{}
