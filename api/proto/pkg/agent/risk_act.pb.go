// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_act.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// --------------------------------------------------
//
//	行为风险识别结果
//	对应 g_CmdMemProtectRiskActInfo
//
// --------------------------------------------------
type MemProtectRiskActInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo                  *ClientID                 `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	NewAdminAccountList       []*NewAdminAccout         `protobuf:"bytes,2,rep,name=newAdminAccountList,proto3" json:"newAdminAccountList,omitempty"`
	FireWallSwitchList        []*FireWallSwitch         `protobuf:"bytes,3,rep,name=fireWallSwitchList,proto3" json:"fireWallSwitchList,omitempty"`
	IpFireWallOffList         []*IPFireWallOFF          `protobuf:"bytes,4,rep,name=ipFireWallOffList,proto3" json:"ipFireWallOffList,omitempty"`
	SELinuxOffList            []*SELinuxOFF             `protobuf:"bytes,5,rep,name=SELinuxOffList,proto3" json:"SELinuxOffList,omitempty"`
	LoadDriverList            []*LoadDriver             `protobuf:"bytes,6,rep,name=loadDriverList,proto3" json:"loadDriverList,omitempty"`
	RegSetValueStartupList    []*RegOperateValueStartup `protobuf:"bytes,7,rep,name=regSetValueStartupList,proto3" json:"regSetValueStartupList,omitempty"`
	RegDelStartupList         []*RegOperateValueStartup `protobuf:"bytes,8,rep,name=regDelStartupList,proto3" json:"regDelStartupList,omitempty"`
	RegCreateValueStartupList []*RegOperateValueStartup `protobuf:"bytes,9,rep,name=regCreateValueStartupList,proto3" json:"regCreateValueStartupList,omitempty"`
}

func (x *MemProtectRiskActInfo) Reset() {
	*x = MemProtectRiskActInfo{}
	mi := &file_agent_risk_act_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskActInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskActInfo) ProtoMessage() {}

func (x *MemProtectRiskActInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskActInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskActInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskActInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetNewAdminAccountList() []*NewAdminAccout {
	if x != nil {
		return x.NewAdminAccountList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetFireWallSwitchList() []*FireWallSwitch {
	if x != nil {
		return x.FireWallSwitchList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetIpFireWallOffList() []*IPFireWallOFF {
	if x != nil {
		return x.IpFireWallOffList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetSELinuxOffList() []*SELinuxOFF {
	if x != nil {
		return x.SELinuxOffList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetLoadDriverList() []*LoadDriver {
	if x != nil {
		return x.LoadDriverList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetRegSetValueStartupList() []*RegOperateValueStartup {
	if x != nil {
		return x.RegSetValueStartupList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetRegDelStartupList() []*RegOperateValueStartup {
	if x != nil {
		return x.RegDelStartupList
	}
	return nil
}

func (x *MemProtectRiskActInfo) GetRegCreateValueStartupList() []*RegOperateValueStartup {
	if x != nil {
		return x.RegCreateValueStartupList
	}
	return nil
}

// 账户提权到管理员
type NewAdminAccout struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	UserName []byte      `protobuf:"bytes,2,opt,name=UserName,proto3" json:"UserName,omitempty"`
}

func (x *NewAdminAccout) Reset() {
	*x = NewAdminAccout{}
	mi := &file_agent_risk_act_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewAdminAccout) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewAdminAccout) ProtoMessage() {}

func (x *NewAdminAccout) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewAdminAccout.ProtoReflect.Descriptor instead.
func (*NewAdminAccout) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{1}
}

func (x *NewAdminAccout) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *NewAdminAccout) GetUserName() []byte {
	if x != nil {
		return x.UserName
	}
	return nil
}

// 防火墙开关 (win)
type FireWallSwitch struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	Private       uint32      `protobuf:"varint,2,opt,name=Private,proto3" json:"Private,omitempty"`
	PublicOrGuest uint32      `protobuf:"varint,3,opt,name=PublicOrGuest,proto3" json:"PublicOrGuest,omitempty"`
}

func (x *FireWallSwitch) Reset() {
	*x = FireWallSwitch{}
	mi := &file_agent_risk_act_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FireWallSwitch) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FireWallSwitch) ProtoMessage() {}

func (x *FireWallSwitch) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FireWallSwitch.ProtoReflect.Descriptor instead.
func (*FireWallSwitch) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{2}
}

func (x *FireWallSwitch) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FireWallSwitch) GetPrivate() uint32 {
	if x != nil {
		return x.Private
	}
	return 0
}

func (x *FireWallSwitch) GetPublicOrGuest() uint32 {
	if x != nil {
		return x.PublicOrGuest
	}
	return 0
}

// IP防火墙被关闭 (linux)
type IPFireWallOFF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *RiskHeader  `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	Process *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"` // 执行关闭防火墙进程信息
	Comment []byte       `protobuf:"bytes,3,opt,name=Comment,proto3" json:"Comment,omitempty"` // 关闭防火墙命令描述
}

func (x *IPFireWallOFF) Reset() {
	*x = IPFireWallOFF{}
	mi := &file_agent_risk_act_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPFireWallOFF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPFireWallOFF) ProtoMessage() {}

func (x *IPFireWallOFF) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPFireWallOFF.ProtoReflect.Descriptor instead.
func (*IPFireWallOFF) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{3}
}

func (x *IPFireWallOFF) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *IPFireWallOFF) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *IPFireWallOFF) GetComment() []byte {
	if x != nil {
		return x.Comment
	}
	return nil
}

// SELINUX被关闭 (linux)
type SELinuxOFF struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *RiskHeader  `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	Process *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"` // 执行关闭SELINUX进程信息
	Comment []byte       `protobuf:"bytes,3,opt,name=Comment,proto3" json:"Comment,omitempty"` // 关闭SELINUX命令描述
}

func (x *SELinuxOFF) Reset() {
	*x = SELinuxOFF{}
	mi := &file_agent_risk_act_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SELinuxOFF) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SELinuxOFF) ProtoMessage() {}

func (x *SELinuxOFF) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SELinuxOFF.ProtoReflect.Descriptor instead.
func (*SELinuxOFF) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{4}
}

func (x *SELinuxOFF) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SELinuxOFF) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *SELinuxOFF) GetComment() []byte {
	if x != nil {
		return x.Comment
	}
	return nil
}

// 设置注册表启动项
type RegOperateValueStartup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *RiskHeader `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	ProcessID      uint32      `protobuf:"varint,2,opt,name=processID,proto3" json:"processID,omitempty"`          //进程Id
	Ring3KeyPath   []byte      `protobuf:"bytes,3,opt,name=ring3KeyPath,proto3" json:"ring3KeyPath,omitempty"`     //注册表路径
	RegValueName   []byte      `protobuf:"bytes,4,opt,name=regValueName,proto3" json:"regValueName,omitempty"`     //注册表值名
	RegValueType   uint32      `protobuf:"varint,5,opt,name=regValueType,proto3" json:"regValueType,omitempty"`    //值类型
	RegBinaryValue []byte      `protobuf:"bytes,6,opt,name=regBinaryValue,proto3" json:"regBinaryValue,omitempty"` //值
}

func (x *RegOperateValueStartup) Reset() {
	*x = RegOperateValueStartup{}
	mi := &file_agent_risk_act_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegOperateValueStartup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegOperateValueStartup) ProtoMessage() {}

func (x *RegOperateValueStartup) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegOperateValueStartup.ProtoReflect.Descriptor instead.
func (*RegOperateValueStartup) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{5}
}

func (x *RegOperateValueStartup) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *RegOperateValueStartup) GetProcessID() uint32 {
	if x != nil {
		return x.ProcessID
	}
	return 0
}

func (x *RegOperateValueStartup) GetRing3KeyPath() []byte {
	if x != nil {
		return x.Ring3KeyPath
	}
	return nil
}

func (x *RegOperateValueStartup) GetRegValueName() []byte {
	if x != nil {
		return x.RegValueName
	}
	return nil
}

func (x *RegOperateValueStartup) GetRegValueType() uint32 {
	if x != nil {
		return x.RegValueType
	}
	return 0
}

func (x *RegOperateValueStartup) GetRegBinaryValue() []byte {
	if x != nil {
		return x.RegBinaryValue
	}
	return nil
}

// 驱动加载
type LoadDriver struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *RiskHeader  `protobuf:"bytes,1,opt,name=Header,proto3" json:"Header,omitempty"`
	SourceProcess *ProcessInfo `protobuf:"bytes,2,opt,name=SourceProcess,proto3" json:"SourceProcess,omitempty"`
	DriverPath    []byte       `protobuf:"bytes,3,opt,name=DriverPath,proto3" json:"DriverPath,omitempty"`     // 驱动路径
	DriverSha256  []byte       `protobuf:"bytes,4,opt,name=DriverSha256,proto3" json:"DriverSha256,omitempty"` // 驱动文件的sha256
	Company       []byte       `protobuf:"bytes,5,opt,name=Company,proto3" json:"Company,omitempty"`           // 公司
	SignCompany   []byte       `protobuf:"bytes,6,opt,name=SignCompany,proto3" json:"SignCompany,omitempty"`   // 签名者信息
}

func (x *LoadDriver) Reset() {
	*x = LoadDriver{}
	mi := &file_agent_risk_act_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadDriver) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadDriver) ProtoMessage() {}

func (x *LoadDriver) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_act_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadDriver.ProtoReflect.Descriptor instead.
func (*LoadDriver) Descriptor() ([]byte, []int) {
	return file_agent_risk_act_proto_rawDescGZIP(), []int{6}
}

func (x *LoadDriver) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LoadDriver) GetSourceProcess() *ProcessInfo {
	if x != nil {
		return x.SourceProcess
	}
	return nil
}

func (x *LoadDriver) GetDriverPath() []byte {
	if x != nil {
		return x.DriverPath
	}
	return nil
}

func (x *LoadDriver) GetDriverSha256() []byte {
	if x != nil {
		return x.DriverSha256
	}
	return nil
}

func (x *LoadDriver) GetCompany() []byte {
	if x != nil {
		return x.Company
	}
	return nil
}

func (x *LoadDriver) GetSignCompany() []byte {
	if x != nil {
		return x.SignCompany
	}
	return nil
}

var File_agent_risk_act_proto protoreflect.FileDescriptor

var file_agent_risk_act_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x61, 0x63, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x8f, 0x05, 0x0a, 0x15, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x52, 0x69, 0x73, 0x6b, 0x41, 0x63, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a, 0x08, 0x62,
	0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x08,
	0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x47, 0x0a, 0x13, 0x6e, 0x65, 0x77, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4e, 0x65,
	0x77, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x74, 0x52, 0x13, 0x6e, 0x65,
	0x77, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x45, 0x0a, 0x12, 0x66, 0x69, 0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x52, 0x12, 0x66, 0x69, 0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x53, 0x77,
	0x69, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a, 0x11, 0x69, 0x70, 0x46, 0x69,
	0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x50, 0x46, 0x69,
	0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x4f, 0x46, 0x46, 0x52, 0x11, 0x69, 0x70, 0x46, 0x69, 0x72,
	0x65, 0x57, 0x61, 0x6c, 0x6c, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0e,
	0x53, 0x45, 0x4c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x45, 0x4c,
	0x69, 0x6e, 0x75, 0x78, 0x4f, 0x46, 0x46, 0x52, 0x0e, 0x53, 0x45, 0x4c, 0x69, 0x6e, 0x75, 0x78,
	0x4f, 0x66, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x44, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x52, 0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x55, 0x0a, 0x16, 0x72, 0x65, 0x67, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75,
	0x70, 0x52, 0x16, 0x72, 0x65, 0x67, 0x53, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4b, 0x0a, 0x11, 0x72, 0x65, 0x67,
	0x44, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x67,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x75, 0x70, 0x52, 0x11, 0x72, 0x65, 0x67, 0x44, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x75, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x19, 0x72, 0x65, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x52, 0x19, 0x72, 0x65, 0x67, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x4c,
	0x69, 0x73, 0x74, 0x22, 0x57, 0x0a, 0x0e, 0x4e, 0x65, 0x77, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x74, 0x12, 0x29, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x08, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7b, 0x0a, 0x0e,
	0x46, 0x69, 0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x29,
	0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x4f, 0x72, 0x47,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x4f, 0x72, 0x47, 0x75, 0x65, 0x73, 0x74, 0x22, 0x82, 0x01, 0x0a, 0x0d, 0x49, 0x50,
	0x46, 0x69, 0x72, 0x65, 0x57, 0x61, 0x6c, 0x6c, 0x4f, 0x46, 0x46, 0x12, 0x29, 0x0a, 0x06, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x7f,
	0x0a, 0x0a, 0x53, 0x45, 0x4c, 0x69, 0x6e, 0x75, 0x78, 0x4f, 0x46, 0x46, 0x12, 0x29, 0x0a, 0x06,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x22,
	0xf5, 0x01, 0x0a, 0x16, 0x52, 0x65, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x75, 0x70, 0x12, 0x29, 0x0a, 0x06, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x69, 0x6e, 0x67, 0x33, 0x4b, 0x65, 0x79, 0x50,
	0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72, 0x69, 0x6e, 0x67, 0x33,
	0x4b, 0x65, 0x79, 0x50, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x67, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72,
	0x65, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x72,
	0x65, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0c, 0x72, 0x65, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x72, 0x65, 0x67, 0x42, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x72, 0x65, 0x67, 0x42, 0x69, 0x6e, 0x61,
	0x72, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xf1, 0x01, 0x0a, 0x0a, 0x4c, 0x6f, 0x61, 0x64,
	0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x38, 0x0a, 0x0d, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x0a, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0c, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12,
	0x18, 0x0a, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x07, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x69, 0x67,
	0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0b,
	0x53, 0x69, 0x67, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x42, 0x2a, 0x5a, 0x28, 0x67,
	0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31,
	0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b,
	0x67, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_act_proto_rawDescOnce sync.Once
	file_agent_risk_act_proto_rawDescData = file_agent_risk_act_proto_rawDesc
)

func file_agent_risk_act_proto_rawDescGZIP() []byte {
	file_agent_risk_act_proto_rawDescOnce.Do(func() {
		file_agent_risk_act_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_act_proto_rawDescData)
	})
	return file_agent_risk_act_proto_rawDescData
}

var file_agent_risk_act_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_agent_risk_act_proto_goTypes = []any{
	(*MemProtectRiskActInfo)(nil),  // 0: agent.MemProtectRiskActInfo
	(*NewAdminAccout)(nil),         // 1: agent.NewAdminAccout
	(*FireWallSwitch)(nil),         // 2: agent.FireWallSwitch
	(*IPFireWallOFF)(nil),          // 3: agent.IPFireWallOFF
	(*SELinuxOFF)(nil),             // 4: agent.SELinuxOFF
	(*RegOperateValueStartup)(nil), // 5: agent.RegOperateValueStartup
	(*LoadDriver)(nil),             // 6: agent.LoadDriver
	(*ClientID)(nil),               // 7: agent.ClientID
	(*RiskHeader)(nil),             // 8: agent.RiskHeader
	(*ProcessInfo)(nil),            // 9: agent.ProcessInfo
}
var file_agent_risk_act_proto_depIdxs = []int32{
	7,  // 0: agent.MemProtectRiskActInfo.baseInfo:type_name -> agent.ClientID
	1,  // 1: agent.MemProtectRiskActInfo.newAdminAccountList:type_name -> agent.NewAdminAccout
	2,  // 2: agent.MemProtectRiskActInfo.fireWallSwitchList:type_name -> agent.FireWallSwitch
	3,  // 3: agent.MemProtectRiskActInfo.ipFireWallOffList:type_name -> agent.IPFireWallOFF
	4,  // 4: agent.MemProtectRiskActInfo.SELinuxOffList:type_name -> agent.SELinuxOFF
	6,  // 5: agent.MemProtectRiskActInfo.loadDriverList:type_name -> agent.LoadDriver
	5,  // 6: agent.MemProtectRiskActInfo.regSetValueStartupList:type_name -> agent.RegOperateValueStartup
	5,  // 7: agent.MemProtectRiskActInfo.regDelStartupList:type_name -> agent.RegOperateValueStartup
	5,  // 8: agent.MemProtectRiskActInfo.regCreateValueStartupList:type_name -> agent.RegOperateValueStartup
	8,  // 9: agent.NewAdminAccout.Header:type_name -> agent.RiskHeader
	8,  // 10: agent.FireWallSwitch.Header:type_name -> agent.RiskHeader
	8,  // 11: agent.IPFireWallOFF.Header:type_name -> agent.RiskHeader
	9,  // 12: agent.IPFireWallOFF.Process:type_name -> agent.ProcessInfo
	8,  // 13: agent.SELinuxOFF.Header:type_name -> agent.RiskHeader
	9,  // 14: agent.SELinuxOFF.Process:type_name -> agent.ProcessInfo
	8,  // 15: agent.RegOperateValueStartup.Header:type_name -> agent.RiskHeader
	8,  // 16: agent.LoadDriver.Header:type_name -> agent.RiskHeader
	9,  // 17: agent.LoadDriver.SourceProcess:type_name -> agent.ProcessInfo
	18, // [18:18] is the sub-list for method output_type
	18, // [18:18] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_agent_risk_act_proto_init() }
func file_agent_risk_act_proto_init() {
	if File_agent_risk_act_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_act_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_act_proto_goTypes,
		DependencyIndexes: file_agent_risk_act_proto_depIdxs,
		MessageInfos:      file_agent_risk_act_proto_msgTypes,
	}.Build()
	File_agent_risk_act_proto = out.File
	file_agent_risk_act_proto_rawDesc = nil
	file_agent_risk_act_proto_goTypes = nil
	file_agent_risk_act_proto_depIdxs = nil
}
