// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/pull.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PullReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxUnicastSeq   int64 `protobuf:"varint,1,opt,name=max_unicast_seq,json=maxUnicastSeq,proto3" json:"max_unicast_seq,omitempty"`       // 当前agent端单播消息最大序号
	MaxMulticastSeq int64 `protobuf:"varint,2,opt,name=max_multicast_seq,json=maxMulticastSeq,proto3" json:"max_multicast_seq,omitempty"` // 当前agent端组播消息最大序号
	MaxBroadcastSeq int64 `protobuf:"varint,3,opt,name=max_broadcast_seq,json=maxBroadcastSeq,proto3" json:"max_broadcast_seq,omitempty"` // 当前agent端广播消息最大序号
	GroupId         int64 `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id,omitempty"`                           // 当前agent分组id
}

func (x *PullReq) Reset() {
	*x = PullReq{}
	mi := &file_agent_pull_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullReq) ProtoMessage() {}

func (x *PullReq) ProtoReflect() protoreflect.Message {
	mi := &file_agent_pull_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullReq.ProtoReflect.Descriptor instead.
func (*PullReq) Descriptor() ([]byte, []int) {
	return file_agent_pull_proto_rawDescGZIP(), []int{0}
}

func (x *PullReq) GetMaxUnicastSeq() int64 {
	if x != nil {
		return x.MaxUnicastSeq
	}
	return 0
}

func (x *PullReq) GetMaxMulticastSeq() int64 {
	if x != nil {
		return x.MaxMulticastSeq
	}
	return 0
}

func (x *PullReq) GetMaxBroadcastSeq() int64 {
	if x != nil {
		return x.MaxBroadcastSeq
	}
	return 0
}

func (x *PullReq) GetGroupId() int64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

type PullResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaxUnicastSeq   int64      `protobuf:"varint,1,opt,name=max_unicast_seq,json=maxUnicastSeq,proto3" json:"max_unicast_seq,omitempty"`       // 当前服务端单播消息最大序号
	MaxMulticastSeq int64      `protobuf:"varint,2,opt,name=max_multicast_seq,json=maxMulticastSeq,proto3" json:"max_multicast_seq,omitempty"` // 当前服务端组播消息最大序号
	MaxBroadcastSeq int64      `protobuf:"varint,3,opt,name=max_broadcast_seq,json=maxBroadcastSeq,proto3" json:"max_broadcast_seq,omitempty"` // 当前服务端广播消息最大序号
	Contents        []*Content `protobuf:"bytes,4,rep,name=contents,proto3" json:"contents,omitempty"`                                         // 消息内容
}

func (x *PullResp) Reset() {
	*x = PullResp{}
	mi := &file_agent_pull_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullResp) ProtoMessage() {}

func (x *PullResp) ProtoReflect() protoreflect.Message {
	mi := &file_agent_pull_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullResp.ProtoReflect.Descriptor instead.
func (*PullResp) Descriptor() ([]byte, []int) {
	return file_agent_pull_proto_rawDescGZIP(), []int{1}
}

func (x *PullResp) GetMaxUnicastSeq() int64 {
	if x != nil {
		return x.MaxUnicastSeq
	}
	return 0
}

func (x *PullResp) GetMaxMulticastSeq() int64 {
	if x != nil {
		return x.MaxMulticastSeq
	}
	return 0
}

func (x *PullResp) GetMaxBroadcastSeq() int64 {
	if x != nil {
		return x.MaxBroadcastSeq
	}
	return 0
}

func (x *PullResp) GetContents() []*Content {
	if x != nil {
		return x.Contents
	}
	return nil
}

type Content struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uuid     string `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid,omitempty"`          // 消息唯一标识（通过uuid进行去重操作）
	Category int32  `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"` // 消息分类（枚举数值参考：fizz.MsgCategoryType）
	Content  []byte `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`    // 消息内容
	Size     int64  `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`         // 消息大小
	Seq      int64  `protobuf:"varint,5,opt,name=seq,proto3" json:"seq,omitempty"`           // 消息序号（当前消息对应的序号）
	Type     int32  `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`         // 消息类型（枚举数值参考：fizz.MsgContentType）
	Os       int32  `protobuf:"varint,7,opt,name=os,proto3" json:"os,omitempty"`             // 操作系统类型（枚举数值参考：fizz.OsType）
}

func (x *Content) Reset() {
	*x = Content{}
	mi := &file_agent_pull_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Content) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Content) ProtoMessage() {}

func (x *Content) ProtoReflect() protoreflect.Message {
	mi := &file_agent_pull_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Content.ProtoReflect.Descriptor instead.
func (*Content) Descriptor() ([]byte, []int) {
	return file_agent_pull_proto_rawDescGZIP(), []int{2}
}

func (x *Content) GetUuid() string {
	if x != nil {
		return x.Uuid
	}
	return ""
}

func (x *Content) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *Content) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *Content) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Content) GetSeq() int64 {
	if x != nil {
		return x.Seq
	}
	return 0
}

func (x *Content) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *Content) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

var File_agent_pull_proto protoreflect.FileDescriptor

var file_agent_pull_proto_rawDesc = []byte{
	0x0a, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x75, 0x6c, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x22, 0xa4, 0x01, 0x0a, 0x07, 0x50, 0x75,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x75, 0x6e, 0x69,
	0x63, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d,
	0x6d, 0x61, 0x78, 0x55, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x53, 0x65, 0x71, 0x12, 0x2a, 0x0a,
	0x11, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x73,
	0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x53, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78,
	0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x61, 0x78, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x53, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64,
	0x22, 0xb6, 0x01, 0x0a, 0x08, 0x50, 0x75, 0x6c, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x26, 0x0a,
	0x0f, 0x6d, 0x61, 0x78, 0x5f, 0x75, 0x6e, 0x69, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x61, 0x78, 0x55, 0x6e, 0x69, 0x63, 0x61,
	0x73, 0x74, 0x53, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x6d, 0x75, 0x6c,
	0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0f, 0x6d, 0x61, 0x78, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x63, 0x61, 0x73, 0x74, 0x53, 0x65,
	0x71, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x62, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61,
	0x73, 0x74, 0x5f, 0x73, 0x65, 0x71, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x61,
	0x78, 0x42, 0x72, 0x6f, 0x61, 0x64, 0x63, 0x61, 0x73, 0x74, 0x53, 0x65, 0x71, 0x12, 0x2a, 0x0a,
	0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52,
	0x08, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x07, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x65, 0x71, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x03, 0x73, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74,
	0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_pull_proto_rawDescOnce sync.Once
	file_agent_pull_proto_rawDescData = file_agent_pull_proto_rawDesc
)

func file_agent_pull_proto_rawDescGZIP() []byte {
	file_agent_pull_proto_rawDescOnce.Do(func() {
		file_agent_pull_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_pull_proto_rawDescData)
	})
	return file_agent_pull_proto_rawDescData
}

var file_agent_pull_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_agent_pull_proto_goTypes = []any{
	(*PullReq)(nil),  // 0: agent.PullReq
	(*PullResp)(nil), // 1: agent.PullResp
	(*Content)(nil),  // 2: agent.Content
}
var file_agent_pull_proto_depIdxs = []int32{
	2, // 0: agent.PullResp.contents:type_name -> agent.Content
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_agent_pull_proto_init() }
func file_agent_pull_proto_init() {
	if File_agent_pull_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_pull_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_pull_proto_goTypes,
		DependencyIndexes: file_agent_pull_proto_depIdxs,
		MessageInfos:      file_agent_pull_proto_msgTypes,
	}.Build()
	File_agent_pull_proto = out.File
	file_agent_pull_proto_rawDesc = nil
	file_agent_pull_proto_goTypes = nil
	file_agent_pull_proto_depIdxs = nil
}
