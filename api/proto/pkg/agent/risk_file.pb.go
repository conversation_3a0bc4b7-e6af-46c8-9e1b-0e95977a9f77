// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: agent/risk_file.proto

package agent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 需要与客户端策略类型值保持一致！！！
type FileOperation int32

const (
	FileOperation_fgo_unset  FileOperation = 0
	FileOperation_fgo_create FileOperation = 65536
	FileOperation_fgo_delete FileOperation = 65539
	FileOperation_fgo_write  FileOperation = 65542
)

// Enum value maps for FileOperation.
var (
	FileOperation_name = map[int32]string{
		0:     "fgo_unset",
		65536: "fgo_create",
		65539: "fgo_delete",
		65542: "fgo_write",
	}
	FileOperation_value = map[string]int32{
		"fgo_unset":  0,
		"fgo_create": 65536,
		"fgo_delete": 65539,
		"fgo_write":  65542,
	}
)

func (x FileOperation) Enum() *FileOperation {
	p := new(FileOperation)
	*p = x
	return p
}

func (x FileOperation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileOperation) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_file_proto_enumTypes[0].Descriptor()
}

func (FileOperation) Type() protoreflect.EnumType {
	return &file_agent_risk_file_proto_enumTypes[0]
}

func (x FileOperation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileOperation.Descriptor instead.
func (FileOperation) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{0}
}

type DeleteResultType int32

const (
	DeleteResultType_DELETE_DEFAULT               DeleteResultType = 0  //默认值，无意义
	DeleteResultType_DELETE_SUCCESS               DeleteResultType = 1  //删除成功
	DeleteResultType_DELETE_FILE_NOT_EXIST        DeleteResultType = 2  //文件不存在
	DeleteResultType_DELETE_ACCESS_DENIED         DeleteResultType = 3  //无法访问
	DeleteResultType_DELETE_FILE_OCCUPIED         DeleteResultType = 4  //文件被占用
	DeleteResultType_DELETE_FILE_MISMATCH         DeleteResultType = 5  //文件不匹配
	DeleteResultType_DELETE_UNKNOWN_ERROR         DeleteResultType = 6  //未知错误
	DeleteResultType_QUARANT_BAD_DB               DeleteResultType = 7  // 隔离区数据损坏
	DeleteResultType_QUARANT_FILE_NOT_IN_DB       DeleteResultType = 8  // 恢复文件不在隔离区
	DeleteResultType_QUARANT_FILE_ENC_DEC_FAILED  DeleteResultType = 9  // 加密或解密文件失败
	DeleteResultType_QUARANT_FILE_ALREADY_EXIST   DeleteResultType = 10 // 恢复时文件已存在
	DeleteResultType_QUARANT_FILE_NOT_IN_QUARANT  DeleteResultType = 11 // 恢复的文件不在隔离区
	DeleteResultType_QUARANT_DISK_NOT_ENOUGH      DeleteResultType = 12 // 隔离区磁盘空间不足
	DeleteResultType_QUARANT_DB_NOT_ENOUGH        DeleteResultType = 13 // 隔离区数据条目超过10000
	DeleteResultType_QUARANT_TARGET_DIR_NOT_EXIST DeleteResultType = 14 // 文件恢复路径不存在
	DeleteResultType_QUARANT_IS_FULL              DeleteResultType = 15 // 隔離區大小超過限制
	DeleteResultType_QUARANT_FILE_SIZE_EXCEED     DeleteResultType = 16 // 單個文件大小超過限制
)

// Enum value maps for DeleteResultType.
var (
	DeleteResultType_name = map[int32]string{
		0:  "DELETE_DEFAULT",
		1:  "DELETE_SUCCESS",
		2:  "DELETE_FILE_NOT_EXIST",
		3:  "DELETE_ACCESS_DENIED",
		4:  "DELETE_FILE_OCCUPIED",
		5:  "DELETE_FILE_MISMATCH",
		6:  "DELETE_UNKNOWN_ERROR",
		7:  "QUARANT_BAD_DB",
		8:  "QUARANT_FILE_NOT_IN_DB",
		9:  "QUARANT_FILE_ENC_DEC_FAILED",
		10: "QUARANT_FILE_ALREADY_EXIST",
		11: "QUARANT_FILE_NOT_IN_QUARANT",
		12: "QUARANT_DISK_NOT_ENOUGH",
		13: "QUARANT_DB_NOT_ENOUGH",
		14: "QUARANT_TARGET_DIR_NOT_EXIST",
		15: "QUARANT_IS_FULL",
		16: "QUARANT_FILE_SIZE_EXCEED",
	}
	DeleteResultType_value = map[string]int32{
		"DELETE_DEFAULT":               0,
		"DELETE_SUCCESS":               1,
		"DELETE_FILE_NOT_EXIST":        2,
		"DELETE_ACCESS_DENIED":         3,
		"DELETE_FILE_OCCUPIED":         4,
		"DELETE_FILE_MISMATCH":         5,
		"DELETE_UNKNOWN_ERROR":         6,
		"QUARANT_BAD_DB":               7,
		"QUARANT_FILE_NOT_IN_DB":       8,
		"QUARANT_FILE_ENC_DEC_FAILED":  9,
		"QUARANT_FILE_ALREADY_EXIST":   10,
		"QUARANT_FILE_NOT_IN_QUARANT":  11,
		"QUARANT_DISK_NOT_ENOUGH":      12,
		"QUARANT_DB_NOT_ENOUGH":        13,
		"QUARANT_TARGET_DIR_NOT_EXIST": 14,
		"QUARANT_IS_FULL":              15,
		"QUARANT_FILE_SIZE_EXCEED":     16,
	}
)

func (x DeleteResultType) Enum() *DeleteResultType {
	p := new(DeleteResultType)
	*p = x
	return p
}

func (x DeleteResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_file_proto_enumTypes[1].Descriptor()
}

func (DeleteResultType) Type() protoreflect.EnumType {
	return &file_agent_risk_file_proto_enumTypes[1]
}

func (x DeleteResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteResultType.Descriptor instead.
func (DeleteResultType) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{1}
}

type DeleteFileStage int32

const (
	DeleteFileStage_STAGE_UNKNOWN DeleteFileStage = 0 // 无意义
	DeleteFileStage_STAGE_ISOLATE DeleteFileStage = 1 // 隔离文件
	DeleteFileStage_STAGE_DELETE  DeleteFileStage = 2 // 永久删除
	DeleteFileStage_STAGE_RESTORE DeleteFileStage = 3 // 恢复文件
	DeleteFileStage_STAGE_EXPIRED DeleteFileStage = 4 // 隔离区自动清理
)

// Enum value maps for DeleteFileStage.
var (
	DeleteFileStage_name = map[int32]string{
		0: "STAGE_UNKNOWN",
		1: "STAGE_ISOLATE",
		2: "STAGE_DELETE",
		3: "STAGE_RESTORE",
		4: "STAGE_EXPIRED",
	}
	DeleteFileStage_value = map[string]int32{
		"STAGE_UNKNOWN": 0,
		"STAGE_ISOLATE": 1,
		"STAGE_DELETE":  2,
		"STAGE_RESTORE": 3,
		"STAGE_EXPIRED": 4,
	}
)

func (x DeleteFileStage) Enum() *DeleteFileStage {
	p := new(DeleteFileStage)
	*p = x
	return p
}

func (x DeleteFileStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteFileStage) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_file_proto_enumTypes[2].Descriptor()
}

func (DeleteFileStage) Type() protoreflect.EnumType {
	return &file_agent_risk_file_proto_enumTypes[2]
}

func (x DeleteFileStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteFileStage.Descriptor instead.
func (DeleteFileStage) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{2}
}

type DeleteFileSource int32

const (
	DeleteFileSource_SOURCE_UNKNOWN          DeleteFileSource = 0 // 未知
	DeleteFileSource_SOURCE_DYNAMIC          DeleteFileSource = 1 // 動態檢測
	DeleteFileSource_SOURCE_STATIC_WEBSHELL  DeleteFileSource = 2 // WebShell 靜態檢測
	DeleteFileSource_SOURCE_STATIC_ANTIVIRUS DeleteFileSource = 3 // 殺毒靜態檢測
)

// Enum value maps for DeleteFileSource.
var (
	DeleteFileSource_name = map[int32]string{
		0: "SOURCE_UNKNOWN",
		1: "SOURCE_DYNAMIC",
		2: "SOURCE_STATIC_WEBSHELL",
		3: "SOURCE_STATIC_ANTIVIRUS",
	}
	DeleteFileSource_value = map[string]int32{
		"SOURCE_UNKNOWN":          0,
		"SOURCE_DYNAMIC":          1,
		"SOURCE_STATIC_WEBSHELL":  2,
		"SOURCE_STATIC_ANTIVIRUS": 3,
	}
)

func (x DeleteFileSource) Enum() *DeleteFileSource {
	p := new(DeleteFileSource)
	*p = x
	return p
}

func (x DeleteFileSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeleteFileSource) Descriptor() protoreflect.EnumDescriptor {
	return file_agent_risk_file_proto_enumTypes[3].Descriptor()
}

func (DeleteFileSource) Type() protoreflect.EnumType {
	return &file_agent_risk_file_proto_enumTypes[3]
}

func (x DeleteFileSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeleteFileSource.Descriptor instead.
func (DeleteFileSource) EnumDescriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{3}
}

// --------------------------------------------------
//
//	文件风险 识别结果
//	对应 g_CmdMemProtectRiskFileInfo
//
// --------------------------------------------------
type MemProtectRiskFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BaseInfo               *ClientID             `protobuf:"bytes,1,opt,name=baseInfo,proto3" json:"baseInfo,omitempty"`
	VirusScannerList       []*VirusCheckInfo     `protobuf:"bytes,2,rep,name=virusScannerList,proto3" json:"virusScannerList,omitempty"`
	FileDllHijackList      []*FileDllHijack      `protobuf:"bytes,3,rep,name=fileDllHijackList,proto3" json:"fileDllHijackList,omitempty"`
	FileEnvHijackList      []*FileEnvHijack      `protobuf:"bytes,4,rep,name=fileEnvHijackList,proto3" json:"fileEnvHijackList,omitempty"`
	PeWithoutSignatureList []*PEWithoutSignature `protobuf:"bytes,5,rep,name=peWithoutSignatureList,proto3" json:"peWithoutSignatureList,omitempty"`
	PePackerNameList       []*PEPackerName       `protobuf:"bytes,6,rep,name=pePackerNameList,proto3" json:"pePackerNameList,omitempty"`
	FileSensitivityList    []*FileSensitivity    `protobuf:"bytes,7,rep,name=fileSensitivityList,proto3" json:"fileSensitivityList,omitempty"`
	LnkFileList            []*LnkFile            `protobuf:"bytes,8,rep,name=lnkFileList,proto3" json:"lnkFileList,omitempty"`
	ClearLogList           []*ClearLog           `protobuf:"bytes,9,rep,name=clearLogList,proto3" json:"clearLogList,omitempty"`
}

func (x *MemProtectRiskFileInfo) Reset() {
	*x = MemProtectRiskFileInfo{}
	mi := &file_agent_risk_file_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemProtectRiskFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemProtectRiskFileInfo) ProtoMessage() {}

func (x *MemProtectRiskFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemProtectRiskFileInfo.ProtoReflect.Descriptor instead.
func (*MemProtectRiskFileInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{0}
}

func (x *MemProtectRiskFileInfo) GetBaseInfo() *ClientID {
	if x != nil {
		return x.BaseInfo
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetVirusScannerList() []*VirusCheckInfo {
	if x != nil {
		return x.VirusScannerList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetFileDllHijackList() []*FileDllHijack {
	if x != nil {
		return x.FileDllHijackList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetFileEnvHijackList() []*FileEnvHijack {
	if x != nil {
		return x.FileEnvHijackList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetPeWithoutSignatureList() []*PEWithoutSignature {
	if x != nil {
		return x.PeWithoutSignatureList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetPePackerNameList() []*PEPackerName {
	if x != nil {
		return x.PePackerNameList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetFileSensitivityList() []*FileSensitivity {
	if x != nil {
		return x.FileSensitivityList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetLnkFileList() []*LnkFile {
	if x != nil {
		return x.LnkFileList
	}
	return nil
}

func (x *MemProtectRiskFileInfo) GetClearLogList() []*ClearLog {
	if x != nil {
		return x.ClearLogList
	}
	return nil
}

// 没有签名的PE文件
type PEWithoutSignature struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FilePath []byte      `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"` //文件路径
	Sha256   []byte      `protobuf:"bytes,3,opt,name=sha256,proto3" json:"sha256,omitempty"`
}

func (x *PEWithoutSignature) Reset() {
	*x = PEWithoutSignature{}
	mi := &file_agent_risk_file_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PEWithoutSignature) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PEWithoutSignature) ProtoMessage() {}

func (x *PEWithoutSignature) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PEWithoutSignature.ProtoReflect.Descriptor instead.
func (*PEWithoutSignature) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{1}
}

func (x *PEWithoutSignature) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PEWithoutSignature) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *PEWithoutSignature) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

// PE加壳
type PEPackerName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header     *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FilePath   []byte      `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"`     //文件路径
	PackerName []byte      `protobuf:"bytes,3,opt,name=packerName,proto3" json:"packerName,omitempty"` //壳名
	Sha256     []byte      `protobuf:"bytes,4,opt,name=sha256,proto3" json:"sha256,omitempty"`         // sha256
}

func (x *PEPackerName) Reset() {
	*x = PEPackerName{}
	mi := &file_agent_risk_file_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PEPackerName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PEPackerName) ProtoMessage() {}

func (x *PEPackerName) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PEPackerName.ProtoReflect.Descriptor instead.
func (*PEPackerName) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{2}
}

func (x *PEPackerName) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PEPackerName) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *PEPackerName) GetPackerName() []byte {
	if x != nil {
		return x.PackerName
	}
	return nil
}

func (x *PEPackerName) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

// 病毒扫描
type VirusCheckInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FilePath  []byte      `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"`   //文件路径
	VirusName []byte      `protobuf:"bytes,3,opt,name=virusName,proto3" json:"virusName,omitempty"` //病毒名
	Sha256    []byte      `protobuf:"bytes,4,opt,name=sha256,proto3" json:"sha256,omitempty"`
}

func (x *VirusCheckInfo) Reset() {
	*x = VirusCheckInfo{}
	mi := &file_agent_risk_file_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VirusCheckInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VirusCheckInfo) ProtoMessage() {}

func (x *VirusCheckInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VirusCheckInfo.ProtoReflect.Descriptor instead.
func (*VirusCheckInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{3}
}

func (x *VirusCheckInfo) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VirusCheckInfo) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *VirusCheckInfo) GetVirusName() []byte {
	if x != nil {
		return x.VirusName
	}
	return nil
}

func (x *VirusCheckInfo) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

// DLL劫持： 进程加载的系统dll，如果不在系统路径下(system32)，认为是DLL劫持
type FunName struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name []byte `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *FunName) Reset() {
	*x = FunName{}
	mi := &file_agent_risk_file_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FunName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunName) ProtoMessage() {}

func (x *FunName) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunName.ProtoReflect.Descriptor instead.
func (*FunName) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{4}
}

func (x *FunName) GetName() []byte {
	if x != nil {
		return x.Name
	}
	return nil
}

type FileDllHijack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process      *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	DllName      []byte       `protobuf:"bytes,3,opt,name=DllName,proto3" json:"DllName,omitempty"`           // 劫持的文件名
	DllPath      []byte       `protobuf:"bytes,4,opt,name=DllPath,proto3" json:"DllPath,omitempty"`           // 全路径
	Sha256       []byte       `protobuf:"bytes,5,opt,name=Sha256,proto3" json:"Sha256,omitempty"`             // sha256
	FuncNameList []*FunName   `protobuf:"bytes,6,rep,name=funcNameList,proto3" json:"funcNameList,omitempty"` // 劫持的可疑的系统函数
}

func (x *FileDllHijack) Reset() {
	*x = FileDllHijack{}
	mi := &file_agent_risk_file_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDllHijack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDllHijack) ProtoMessage() {}

func (x *FileDllHijack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDllHijack.ProtoReflect.Descriptor instead.
func (*FileDllHijack) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{5}
}

func (x *FileDllHijack) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FileDllHijack) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *FileDllHijack) GetDllName() []byte {
	if x != nil {
		return x.DllName
	}
	return nil
}

func (x *FileDllHijack) GetDllPath() []byte {
	if x != nil {
		return x.DllPath
	}
	return nil
}

func (x *FileDllHijack) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

func (x *FileDllHijack) GetFuncNameList() []*FunName {
	if x != nil {
		return x.FuncNameList
	}
	return nil
}

// 环境变量劫持，思路如果path里的第三方路径有系统dll文件，认为是环境变量劫持
type FileEnvHijack struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FullValue []byte      `protobuf:"bytes,2,opt,name=FullValue,proto3" json:"FullValue,omitempty"` // Path环境变量的完整值
	RiskValue []byte      `protobuf:"bytes,3,opt,name=RiskValue,proto3" json:"RiskValue,omitempty"` // 有风险的是哪个路径
	Feature   []byte      `protobuf:"bytes,4,opt,name=Feature,proto3" json:"Feature,omitempty"`     // 判断为风险路径的特征文件名，比如lpk.dll
	Sha256    []byte      `protobuf:"bytes,5,opt,name=Sha256,proto3" json:"Sha256,omitempty"`       // 特征文件的sha256，特征文件可能不只一个，如何处理？(分成多条来处理)
}

func (x *FileEnvHijack) Reset() {
	*x = FileEnvHijack{}
	mi := &file_agent_risk_file_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileEnvHijack) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEnvHijack) ProtoMessage() {}

func (x *FileEnvHijack) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEnvHijack.ProtoReflect.Descriptor instead.
func (*FileEnvHijack) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{6}
}

func (x *FileEnvHijack) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FileEnvHijack) GetFullValue() []byte {
	if x != nil {
		return x.FullValue
	}
	return nil
}

func (x *FileEnvHijack) GetRiskValue() []byte {
	if x != nil {
		return x.RiskValue
	}
	return nil
}

func (x *FileEnvHijack) GetFeature() []byte {
	if x != nil {
		return x.Feature
	}
	return nil
}

func (x *FileEnvHijack) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

// 敏感文件操作
type FileSensitivity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process  *ProcessInfo  `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"`
	FileName []byte        `protobuf:"bytes,3,opt,name=fileName,proto3" json:"fileName,omitempty"`                       // 操作的文件名
	FilePath []byte        `protobuf:"bytes,4,opt,name=filePath,proto3" json:"filePath,omitempty"`                       // 操作的文件路径
	FileOp   FileOperation `protobuf:"varint,5,opt,name=fileOp,proto3,enum=agent.FileOperation" json:"fileOp,omitempty"` // 文件操作类型
	Sha256   []byte        `protobuf:"bytes,6,opt,name=Sha256,proto3" json:"Sha256,omitempty"`                           // 特征文件的sha256，特征文件可能不只一个，如何处理？(分成多条来处理)
}

func (x *FileSensitivity) Reset() {
	*x = FileSensitivity{}
	mi := &file_agent_risk_file_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSensitivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSensitivity) ProtoMessage() {}

func (x *FileSensitivity) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSensitivity.ProtoReflect.Descriptor instead.
func (*FileSensitivity) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{7}
}

func (x *FileSensitivity) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FileSensitivity) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *FileSensitivity) GetFileName() []byte {
	if x != nil {
		return x.FileName
	}
	return nil
}

func (x *FileSensitivity) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *FileSensitivity) GetFileOp() FileOperation {
	if x != nil {
		return x.FileOp
	}
	return FileOperation_fgo_unset
}

func (x *FileSensitivity) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

type LnkFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header   *RiskHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	FileName []byte      `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"`        // 文件名
	FilePath []byte      `protobuf:"bytes,3,opt,name=filePath,proto3" json:"filePath,omitempty"`        // 件路径
	CVE_ID   []byte      `protobuf:"bytes,4,opt,name=CVE_ID,json=CVEID,proto3" json:"CVE_ID,omitempty"` // CVE编号
	Sha256   []byte      `protobuf:"bytes,5,opt,name=Sha256,proto3" json:"Sha256,omitempty"`            // 文件的sha256
}

func (x *LnkFile) Reset() {
	*x = LnkFile{}
	mi := &file_agent_risk_file_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LnkFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LnkFile) ProtoMessage() {}

func (x *LnkFile) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LnkFile.ProtoReflect.Descriptor instead.
func (*LnkFile) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{8}
}

func (x *LnkFile) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *LnkFile) GetFileName() []byte {
	if x != nil {
		return x.FileName
	}
	return nil
}

func (x *LnkFile) GetFilePath() []byte {
	if x != nil {
		return x.FilePath
	}
	return nil
}

func (x *LnkFile) GetCVE_ID() []byte {
	if x != nil {
		return x.CVE_ID
	}
	return nil
}

func (x *LnkFile) GetSha256() []byte {
	if x != nil {
		return x.Sha256
	}
	return nil
}

// 清空日志操作
type ClearLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *RiskHeader  `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Process *ProcessInfo `protobuf:"bytes,2,opt,name=Process,proto3" json:"Process,omitempty"` //清空日志的进程信息
	LogName string       `protobuf:"bytes,3,opt,name=LogName,proto3" json:"LogName,omitempty"` //被清空的日志名称
}

func (x *ClearLog) Reset() {
	*x = ClearLog{}
	mi := &file_agent_risk_file_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClearLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearLog) ProtoMessage() {}

func (x *ClearLog) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearLog.ProtoReflect.Descriptor instead.
func (*ClearLog) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{9}
}

func (x *ClearLog) GetHeader() *RiskHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ClearLog) GetProcess() *ProcessInfo {
	if x != nil {
		return x.Process
	}
	return nil
}

func (x *ClearLog) GetLogName() string {
	if x != nil {
		return x.LogName
	}
	return ""
}

// 文件处置-删除文件请求
type FileHandlingDeleteFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256          string             `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`
	DeleteFilesList []*DeleteFilesInfo `protobuf:"bytes,2,rep,name=DeleteFilesList,proto3" json:"DeleteFilesList,omitempty"`
	Stage           DeleteFileStage    `protobuf:"varint,3,opt,name=stage,proto3,enum=agent.DeleteFileStage" json:"stage,omitempty"`    // 删除阶段: 隔离; 删除; 恢复
	Source          DeleteFileSource   `protobuf:"varint,4,opt,name=source,proto3,enum=agent.DeleteFileSource" json:"source,omitempty"` // 文件處置命令來源: 動態檢測, 靜態檢測, ...
}

func (x *FileHandlingDeleteFilesRequest) Reset() {
	*x = FileHandlingDeleteFilesRequest{}
	mi := &file_agent_risk_file_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHandlingDeleteFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHandlingDeleteFilesRequest) ProtoMessage() {}

func (x *FileHandlingDeleteFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHandlingDeleteFilesRequest.ProtoReflect.Descriptor instead.
func (*FileHandlingDeleteFilesRequest) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{10}
}

func (x *FileHandlingDeleteFilesRequest) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileHandlingDeleteFilesRequest) GetDeleteFilesList() []*DeleteFilesInfo {
	if x != nil {
		return x.DeleteFilesList
	}
	return nil
}

func (x *FileHandlingDeleteFilesRequest) GetStage() DeleteFileStage {
	if x != nil {
		return x.Stage
	}
	return DeleteFileStage_STAGE_UNKNOWN
}

func (x *FileHandlingDeleteFilesRequest) GetSource() DeleteFileSource {
	if x != nil {
		return x.Source
	}
	return DeleteFileSource_SOURCE_UNKNOWN
}

// 文件处置-删除文件结果上报
type FileHandlingDeleteFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sha256     string               `protobuf:"bytes,1,opt,name=sha256,proto3" json:"sha256,omitempty"`
	ResultList []*DeleteFilesResult `protobuf:"bytes,2,rep,name=resultList,proto3" json:"resultList,omitempty"`
	Stage      DeleteFileStage      `protobuf:"varint,3,opt,name=stage,proto3,enum=agent.DeleteFileStage" json:"stage,omitempty"`    // 删除阶段: 隔离; 删除; 恢复
	Source     DeleteFileSource     `protobuf:"varint,4,opt,name=source,proto3,enum=agent.DeleteFileSource" json:"source,omitempty"` // 文件處置命令來源: 動態檢測, 靜態檢測, ...
}

func (x *FileHandlingDeleteFilesResponse) Reset() {
	*x = FileHandlingDeleteFilesResponse{}
	mi := &file_agent_risk_file_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileHandlingDeleteFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileHandlingDeleteFilesResponse) ProtoMessage() {}

func (x *FileHandlingDeleteFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileHandlingDeleteFilesResponse.ProtoReflect.Descriptor instead.
func (*FileHandlingDeleteFilesResponse) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{11}
}

func (x *FileHandlingDeleteFilesResponse) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileHandlingDeleteFilesResponse) GetResultList() []*DeleteFilesResult {
	if x != nil {
		return x.ResultList
	}
	return nil
}

func (x *FileHandlingDeleteFilesResponse) GetStage() DeleteFileStage {
	if x != nil {
		return x.Stage
	}
	return DeleteFileStage_STAGE_UNKNOWN
}

func (x *FileHandlingDeleteFilesResponse) GetSource() DeleteFileSource {
	if x != nil {
		return x.Source
	}
	return DeleteFileSource_SOURCE_UNKNOWN
}

type DeleteFilesInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FilePath   string `protobuf:"bytes,1,opt,name=filePath,proto3" json:"filePath,omitempty"`
	MacPathKey string `protobuf:"bytes,2,opt,name=macPathKey,proto3" json:"macPathKey,omitempty"`
}

func (x *DeleteFilesInfo) Reset() {
	*x = DeleteFilesInfo{}
	mi := &file_agent_risk_file_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFilesInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFilesInfo) ProtoMessage() {}

func (x *DeleteFilesInfo) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFilesInfo.ProtoReflect.Descriptor instead.
func (*DeleteFilesInfo) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteFilesInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *DeleteFilesInfo) GetMacPathKey() string {
	if x != nil {
		return x.MacPathKey
	}
	return ""
}

type DeleteFilesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MacPathKey string           `protobuf:"bytes,1,opt,name=macPathKey,proto3" json:"macPathKey,omitempty"`
	Result     DeleteResultType `protobuf:"varint,2,opt,name=result,proto3,enum=agent.DeleteResultType" json:"result,omitempty"`
	FilePath   string           `protobuf:"bytes,3,opt,name=filePath,proto3" json:"filePath,omitempty"` // 根据sha256删除时, 上报文件路径
}

func (x *DeleteFilesResult) Reset() {
	*x = DeleteFilesResult{}
	mi := &file_agent_risk_file_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFilesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFilesResult) ProtoMessage() {}

func (x *DeleteFilesResult) ProtoReflect() protoreflect.Message {
	mi := &file_agent_risk_file_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFilesResult.ProtoReflect.Descriptor instead.
func (*DeleteFilesResult) Descriptor() ([]byte, []int) {
	return file_agent_risk_file_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteFilesResult) GetMacPathKey() string {
	if x != nil {
		return x.MacPathKey
	}
	return ""
}

func (x *DeleteFilesResult) GetResult() DeleteResultType {
	if x != nil {
		return x.Result
	}
	return DeleteResultType_DELETE_DEFAULT
}

func (x *DeleteFilesResult) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

var File_agent_risk_file_proto protoreflect.FileDescriptor

var file_agent_risk_file_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x1a, 0x12,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xd5, 0x04, 0x0a, 0x16, 0x4d, 0x65, 0x6d, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2b, 0x0a,
	0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x52, 0x08, 0x62, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x10, 0x76, 0x69,
	0x72, 0x75, 0x73, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x56, 0x69, 0x72,
	0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x76, 0x69, 0x72,
	0x75, 0x73, 0x53, 0x63, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x42, 0x0a,
	0x11, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x52, 0x11,
	0x66, 0x69, 0x6c, 0x65, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x42, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61,
	0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61,
	0x63, 0x6b, 0x52, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63,
	0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x16, 0x70, 0x65, 0x57, 0x69, 0x74, 0x68, 0x6f,
	0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x45,
	0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x52, 0x16, 0x70, 0x65, 0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x10, 0x70, 0x65, 0x50, 0x61,
	0x63, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x45, 0x50, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x10, 0x70, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x13, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x13,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x0b, 0x6c, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x4c, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x0b, 0x6c, 0x6e, 0x6b, 0x46, 0x69, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x0c, 0x63, 0x6c, 0x65, 0x61, 0x72, 0x4c, 0x6f,
	0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x4c, 0x6f, 0x67, 0x52, 0x0c, 0x63, 0x6c,
	0x65, 0x61, 0x72, 0x4c, 0x6f, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x73, 0x0a, 0x12, 0x50, 0x45,
	0x57, 0x69, 0x74, 0x68, 0x6f, 0x75, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22,
	0x8d, 0x01, 0x0a, 0x0c, 0x50, 0x45, 0x50, 0x61, 0x63, 0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x63, 0x6b, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x70, 0x61, 0x63,
	0x6b, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22,
	0x8d, 0x01, 0x0a, 0x0e, 0x56, 0x69, 0x72, 0x75, 0x73, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x76, 0x69, 0x72,
	0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x76, 0x69,
	0x72, 0x75, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22,
	0x1d, 0x0a, 0x07, 0x66, 0x75, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe8,
	0x01, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x6c, 0x6c, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b,
	0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x6c, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x44, 0x6c, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x44, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x44, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12, 0x16, 0x0a,
	0x06, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x53,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x32, 0x0a, 0x0c, 0x66, 0x75, 0x6e, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x66, 0x75, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0c, 0x66, 0x75, 0x6e,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa8, 0x01, 0x0a, 0x0d, 0x46, 0x69,
	0x6c, 0x65, 0x45, 0x6e, 0x76, 0x48, 0x69, 0x6a, 0x61, 0x63, 0x6b, 0x12, 0x29, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x46, 0x75, 0x6c, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x46, 0x75, 0x6c, 0x6c, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x53, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x22, 0xe8, 0x01, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2c, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x65, 0x4f, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22,
	0x9b, 0x01, 0x0a, 0x07, 0x4c, 0x6e, 0x6b, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x29, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x15,
	0x0a, 0x06, 0x43, 0x56, 0x45, 0x5f, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x43, 0x56, 0x45, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x06, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22, 0x7d, 0x0a,
	0x08, 0x43, 0x6c, 0x65, 0x61, 0x72, 0x4c, 0x6f, 0x67, 0x12, 0x29, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x4c, 0x6f, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd9, 0x01, 0x0a,
	0x1e, 0x46, 0x69, 0x6c, 0x65, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x40, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xd2, 0x01, 0x0a, 0x1f, 0x46, 0x69, 0x6c,
	0x65, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x38, 0x0a, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x06,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x4d, 0x0a,
	0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a,
	0x6d, 0x61, 0x63, 0x50, 0x61, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6d, 0x61, 0x63, 0x50, 0x61, 0x74, 0x68, 0x4b, 0x65, 0x79, 0x22, 0x80, 0x01, 0x0a,
	0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x50, 0x61, 0x74, 0x68, 0x4b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x50, 0x61, 0x74, 0x68, 0x4b,
	0x65, 0x79, 0x12, 0x2f, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x17, 0x2e, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x72, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x2a,
	0x53, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0d, 0x0a, 0x09, 0x66, 0x67, 0x6f, 0x5f, 0x75, 0x6e, 0x73, 0x65, 0x74, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0a, 0x66, 0x67, 0x6f, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x10, 0x80, 0x80,
	0x04, 0x12, 0x10, 0x0a, 0x0a, 0x66, 0x67, 0x6f, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x10,
	0x83, 0x80, 0x04, 0x12, 0x0f, 0x0a, 0x09, 0x66, 0x67, 0x6f, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65,
	0x10, 0x86, 0x80, 0x04, 0x2a, 0xdc, 0x03, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10, 0x00, 0x12, 0x12, 0x0a,
	0x0e, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10,
	0x01, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x02, 0x12, 0x18, 0x0a, 0x14,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x44, 0x45,
	0x4e, 0x49, 0x45, 0x44, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4f, 0x43, 0x43, 0x55, 0x50, 0x49, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x18, 0x0a, 0x14, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x44, 0x45,
	0x4c, 0x45, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x45, 0x52, 0x52,
	0x4f, 0x52, 0x10, 0x06, 0x12, 0x12, 0x0a, 0x0e, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54, 0x5f,
	0x42, 0x41, 0x44, 0x5f, 0x44, 0x42, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16, 0x51, 0x55, 0x41, 0x52,
	0x41, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f,
	0x44, 0x42, 0x10, 0x08, 0x12, 0x1f, 0x0a, 0x1b, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54, 0x5f,
	0x46, 0x49, 0x4c, 0x45, 0x5f, 0x45, 0x4e, 0x43, 0x5f, 0x44, 0x45, 0x43, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x09, 0x12, 0x1e, 0x0a, 0x1a, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x10, 0x0a, 0x12, 0x1f, 0x0a, 0x1b, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x51, 0x55, 0x41,
	0x52, 0x41, 0x4e, 0x54, 0x10, 0x0b, 0x12, 0x1b, 0x0a, 0x17, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e,
	0x54, 0x5f, 0x44, 0x49, 0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47,
	0x48, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54, 0x5f, 0x44,
	0x42, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x4e, 0x4f, 0x55, 0x47, 0x48, 0x10, 0x0d, 0x12, 0x20,
	0x0a, 0x1c, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54, 0x5f, 0x54, 0x41, 0x52, 0x47, 0x45, 0x54,
	0x5f, 0x44, 0x49, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x0e,
	0x12, 0x13, 0x0a, 0x0f, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54, 0x5f, 0x49, 0x53, 0x5f, 0x46,
	0x55, 0x4c, 0x4c, 0x10, 0x0f, 0x12, 0x1c, 0x0a, 0x18, 0x51, 0x55, 0x41, 0x52, 0x41, 0x4e, 0x54,
	0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x5a, 0x45, 0x5f, 0x45, 0x58, 0x43, 0x45, 0x45,
	0x44, 0x10, 0x10, 0x2a, 0x6f, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41,
	0x47, 0x45, 0x5f, 0x49, 0x53, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c,
	0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12, 0x11,
	0x0a, 0x0d, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x45, 0x10,
	0x03, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x44, 0x10, 0x04, 0x2a, 0x73, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x10, 0x01,
	0x12, 0x1a, 0x0a, 0x16, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49,
	0x43, 0x5f, 0x57, 0x45, 0x42, 0x53, 0x48, 0x45, 0x4c, 0x4c, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x49, 0x43, 0x5f, 0x41, 0x4e,
	0x54, 0x49, 0x56, 0x49, 0x52, 0x55, 0x53, 0x10, 0x03, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74,
	0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_agent_risk_file_proto_rawDescOnce sync.Once
	file_agent_risk_file_proto_rawDescData = file_agent_risk_file_proto_rawDesc
)

func file_agent_risk_file_proto_rawDescGZIP() []byte {
	file_agent_risk_file_proto_rawDescOnce.Do(func() {
		file_agent_risk_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_agent_risk_file_proto_rawDescData)
	})
	return file_agent_risk_file_proto_rawDescData
}

var file_agent_risk_file_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_agent_risk_file_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_agent_risk_file_proto_goTypes = []any{
	(FileOperation)(0),                      // 0: agent.FileOperation
	(DeleteResultType)(0),                   // 1: agent.DeleteResultType
	(DeleteFileStage)(0),                    // 2: agent.DeleteFileStage
	(DeleteFileSource)(0),                   // 3: agent.DeleteFileSource
	(*MemProtectRiskFileInfo)(nil),          // 4: agent.MemProtectRiskFileInfo
	(*PEWithoutSignature)(nil),              // 5: agent.PEWithoutSignature
	(*PEPackerName)(nil),                    // 6: agent.PEPackerName
	(*VirusCheckInfo)(nil),                  // 7: agent.VirusCheckInfo
	(*FunName)(nil),                         // 8: agent.funName
	(*FileDllHijack)(nil),                   // 9: agent.FileDllHijack
	(*FileEnvHijack)(nil),                   // 10: agent.FileEnvHijack
	(*FileSensitivity)(nil),                 // 11: agent.FileSensitivity
	(*LnkFile)(nil),                         // 12: agent.LnkFile
	(*ClearLog)(nil),                        // 13: agent.ClearLog
	(*FileHandlingDeleteFilesRequest)(nil),  // 14: agent.FileHandlingDeleteFilesRequest
	(*FileHandlingDeleteFilesResponse)(nil), // 15: agent.FileHandlingDeleteFilesResponse
	(*DeleteFilesInfo)(nil),                 // 16: agent.DeleteFilesInfo
	(*DeleteFilesResult)(nil),               // 17: agent.DeleteFilesResult
	(*ClientID)(nil),                        // 18: agent.ClientID
	(*RiskHeader)(nil),                      // 19: agent.RiskHeader
	(*ProcessInfo)(nil),                     // 20: agent.ProcessInfo
}
var file_agent_risk_file_proto_depIdxs = []int32{
	18, // 0: agent.MemProtectRiskFileInfo.baseInfo:type_name -> agent.ClientID
	7,  // 1: agent.MemProtectRiskFileInfo.virusScannerList:type_name -> agent.VirusCheckInfo
	9,  // 2: agent.MemProtectRiskFileInfo.fileDllHijackList:type_name -> agent.FileDllHijack
	10, // 3: agent.MemProtectRiskFileInfo.fileEnvHijackList:type_name -> agent.FileEnvHijack
	5,  // 4: agent.MemProtectRiskFileInfo.peWithoutSignatureList:type_name -> agent.PEWithoutSignature
	6,  // 5: agent.MemProtectRiskFileInfo.pePackerNameList:type_name -> agent.PEPackerName
	11, // 6: agent.MemProtectRiskFileInfo.fileSensitivityList:type_name -> agent.FileSensitivity
	12, // 7: agent.MemProtectRiskFileInfo.lnkFileList:type_name -> agent.LnkFile
	13, // 8: agent.MemProtectRiskFileInfo.clearLogList:type_name -> agent.ClearLog
	19, // 9: agent.PEWithoutSignature.header:type_name -> agent.RiskHeader
	19, // 10: agent.PEPackerName.header:type_name -> agent.RiskHeader
	19, // 11: agent.VirusCheckInfo.header:type_name -> agent.RiskHeader
	19, // 12: agent.FileDllHijack.header:type_name -> agent.RiskHeader
	20, // 13: agent.FileDllHijack.Process:type_name -> agent.ProcessInfo
	8,  // 14: agent.FileDllHijack.funcNameList:type_name -> agent.funName
	19, // 15: agent.FileEnvHijack.header:type_name -> agent.RiskHeader
	19, // 16: agent.FileSensitivity.header:type_name -> agent.RiskHeader
	20, // 17: agent.FileSensitivity.Process:type_name -> agent.ProcessInfo
	0,  // 18: agent.FileSensitivity.fileOp:type_name -> agent.FileOperation
	19, // 19: agent.LnkFile.header:type_name -> agent.RiskHeader
	19, // 20: agent.ClearLog.header:type_name -> agent.RiskHeader
	20, // 21: agent.ClearLog.Process:type_name -> agent.ProcessInfo
	16, // 22: agent.FileHandlingDeleteFilesRequest.DeleteFilesList:type_name -> agent.DeleteFilesInfo
	2,  // 23: agent.FileHandlingDeleteFilesRequest.stage:type_name -> agent.DeleteFileStage
	3,  // 24: agent.FileHandlingDeleteFilesRequest.source:type_name -> agent.DeleteFileSource
	17, // 25: agent.FileHandlingDeleteFilesResponse.resultList:type_name -> agent.DeleteFilesResult
	2,  // 26: agent.FileHandlingDeleteFilesResponse.stage:type_name -> agent.DeleteFileStage
	3,  // 27: agent.FileHandlingDeleteFilesResponse.source:type_name -> agent.DeleteFileSource
	1,  // 28: agent.DeleteFilesResult.result:type_name -> agent.DeleteResultType
	29, // [29:29] is the sub-list for method output_type
	29, // [29:29] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_agent_risk_file_proto_init() }
func file_agent_risk_file_proto_init() {
	if File_agent_risk_file_proto != nil {
		return
	}
	file_agent_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_agent_risk_file_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_agent_risk_file_proto_goTypes,
		DependencyIndexes: file_agent_risk_file_proto_depIdxs,
		EnumInfos:         file_agent_risk_file_proto_enumTypes,
		MessageInfos:      file_agent_risk_file_proto_msgTypes,
	}.Build()
	File_agent_risk_file_proto = out.File
	file_agent_risk_file_proto_rawDesc = nil
	file_agent_risk_file_proto_goTypes = nil
	file_agent_risk_file_proto_depIdxs = nil
}
