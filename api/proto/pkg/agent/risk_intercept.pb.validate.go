// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_intercept.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectAutoInterceptInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectAutoInterceptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectAutoInterceptInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectAutoInterceptInfoMultiError, or nil if none found.
func (m *MemProtectAutoInterceptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectAutoInterceptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectAutoInterceptInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectAutoInterceptInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectAutoInterceptInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAutoInterceptList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectAutoInterceptInfoValidationError{
						field:  fmt.Sprintf("AutoInterceptList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectAutoInterceptInfoValidationError{
						field:  fmt.Sprintf("AutoInterceptList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectAutoInterceptInfoValidationError{
					field:  fmt.Sprintf("AutoInterceptList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectAutoInterceptInfoMultiError(errors)
	}

	return nil
}

// MemProtectAutoInterceptInfoMultiError is an error wrapping multiple
// validation errors returned by MemProtectAutoInterceptInfo.ValidateAll() if
// the designated constraints aren't met.
type MemProtectAutoInterceptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectAutoInterceptInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectAutoInterceptInfoMultiError) AllErrors() []error { return m }

// MemProtectAutoInterceptInfoValidationError is the validation error returned
// by MemProtectAutoInterceptInfo.Validate if the designated constraints
// aren't met.
type MemProtectAutoInterceptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectAutoInterceptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectAutoInterceptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectAutoInterceptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectAutoInterceptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectAutoInterceptInfoValidationError) ErrorName() string {
	return "MemProtectAutoInterceptInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectAutoInterceptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectAutoInterceptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectAutoInterceptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectAutoInterceptInfoValidationError{}

// Validate checks the field values on AutoInterceptInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AutoInterceptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AutoInterceptInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AutoInterceptInfoMultiError, or nil if none found.
func (m *AutoInterceptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoInterceptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoInterceptInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoInterceptInfoValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoInterceptInfoValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DetailInfo

	// no validation rules for PolicyId

	if len(errors) > 0 {
		return AutoInterceptInfoMultiError(errors)
	}

	return nil
}

// AutoInterceptInfoMultiError is an error wrapping multiple validation errors
// returned by AutoInterceptInfo.ValidateAll() if the designated constraints
// aren't met.
type AutoInterceptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoInterceptInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoInterceptInfoMultiError) AllErrors() []error { return m }

// AutoInterceptInfoValidationError is the validation error returned by
// AutoInterceptInfo.Validate if the designated constraints aren't met.
type AutoInterceptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoInterceptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AutoInterceptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AutoInterceptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AutoInterceptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoInterceptInfoValidationError) ErrorName() string {
	return "AutoInterceptInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AutoInterceptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoInterceptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoInterceptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoInterceptInfoValidationError{}
