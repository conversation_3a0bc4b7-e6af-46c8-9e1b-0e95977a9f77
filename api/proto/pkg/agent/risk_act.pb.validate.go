// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: agent/risk_act.proto

package agent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MemProtectRiskActInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemProtectRiskActInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemProtectRiskActInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemProtectRiskActInfoMultiError, or nil if none found.
func (m *MemProtectRiskActInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemProtectRiskActInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBaseInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemProtectRiskActInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemProtectRiskActInfoValidationError{
					field:  "BaseInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBaseInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemProtectRiskActInfoValidationError{
				field:  "BaseInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNewAdminAccountList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("NewAdminAccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("NewAdminAccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("NewAdminAccountList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFireWallSwitchList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("FireWallSwitchList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("FireWallSwitchList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("FireWallSwitchList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetIpFireWallOffList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("IpFireWallOffList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("IpFireWallOffList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("IpFireWallOffList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSELinuxOffList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("SELinuxOffList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("SELinuxOffList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("SELinuxOffList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLoadDriverList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("LoadDriverList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("LoadDriverList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("LoadDriverList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRegSetValueStartupList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegSetValueStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegSetValueStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("RegSetValueStartupList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRegDelStartupList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegDelStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegDelStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("RegDelStartupList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetRegCreateValueStartupList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegCreateValueStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemProtectRiskActInfoValidationError{
						field:  fmt.Sprintf("RegCreateValueStartupList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemProtectRiskActInfoValidationError{
					field:  fmt.Sprintf("RegCreateValueStartupList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MemProtectRiskActInfoMultiError(errors)
	}

	return nil
}

// MemProtectRiskActInfoMultiError is an error wrapping multiple validation
// errors returned by MemProtectRiskActInfo.ValidateAll() if the designated
// constraints aren't met.
type MemProtectRiskActInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemProtectRiskActInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemProtectRiskActInfoMultiError) AllErrors() []error { return m }

// MemProtectRiskActInfoValidationError is the validation error returned by
// MemProtectRiskActInfo.Validate if the designated constraints aren't met.
type MemProtectRiskActInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemProtectRiskActInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemProtectRiskActInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemProtectRiskActInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemProtectRiskActInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemProtectRiskActInfoValidationError) ErrorName() string {
	return "MemProtectRiskActInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemProtectRiskActInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemProtectRiskActInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemProtectRiskActInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemProtectRiskActInfoValidationError{}

// Validate checks the field values on NewAdminAccout with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NewAdminAccout) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NewAdminAccout with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NewAdminAccoutMultiError,
// or nil if none found.
func (m *NewAdminAccout) ValidateAll() error {
	return m.validate(true)
}

func (m *NewAdminAccout) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NewAdminAccoutValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NewAdminAccoutValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NewAdminAccoutValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserName

	if len(errors) > 0 {
		return NewAdminAccoutMultiError(errors)
	}

	return nil
}

// NewAdminAccoutMultiError is an error wrapping multiple validation errors
// returned by NewAdminAccout.ValidateAll() if the designated constraints
// aren't met.
type NewAdminAccoutMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NewAdminAccoutMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NewAdminAccoutMultiError) AllErrors() []error { return m }

// NewAdminAccoutValidationError is the validation error returned by
// NewAdminAccout.Validate if the designated constraints aren't met.
type NewAdminAccoutValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NewAdminAccoutValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NewAdminAccoutValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NewAdminAccoutValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NewAdminAccoutValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NewAdminAccoutValidationError) ErrorName() string { return "NewAdminAccoutValidationError" }

// Error satisfies the builtin error interface
func (e NewAdminAccoutValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNewAdminAccout.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NewAdminAccoutValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NewAdminAccoutValidationError{}

// Validate checks the field values on FireWallSwitch with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FireWallSwitch) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FireWallSwitch with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FireWallSwitchMultiError,
// or nil if none found.
func (m *FireWallSwitch) ValidateAll() error {
	return m.validate(true)
}

func (m *FireWallSwitch) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FireWallSwitchValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FireWallSwitchValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FireWallSwitchValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Private

	// no validation rules for PublicOrGuest

	if len(errors) > 0 {
		return FireWallSwitchMultiError(errors)
	}

	return nil
}

// FireWallSwitchMultiError is an error wrapping multiple validation errors
// returned by FireWallSwitch.ValidateAll() if the designated constraints
// aren't met.
type FireWallSwitchMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FireWallSwitchMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FireWallSwitchMultiError) AllErrors() []error { return m }

// FireWallSwitchValidationError is the validation error returned by
// FireWallSwitch.Validate if the designated constraints aren't met.
type FireWallSwitchValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FireWallSwitchValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FireWallSwitchValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FireWallSwitchValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FireWallSwitchValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FireWallSwitchValidationError) ErrorName() string { return "FireWallSwitchValidationError" }

// Error satisfies the builtin error interface
func (e FireWallSwitchValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFireWallSwitch.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FireWallSwitchValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FireWallSwitchValidationError{}

// Validate checks the field values on IPFireWallOFF with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IPFireWallOFF) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPFireWallOFF with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IPFireWallOFFMultiError, or
// nil if none found.
func (m *IPFireWallOFF) ValidateAll() error {
	return m.validate(true)
}

func (m *IPFireWallOFF) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPFireWallOFFValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPFireWallOFFValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPFireWallOFFValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPFireWallOFFValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPFireWallOFFValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPFireWallOFFValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return IPFireWallOFFMultiError(errors)
	}

	return nil
}

// IPFireWallOFFMultiError is an error wrapping multiple validation errors
// returned by IPFireWallOFF.ValidateAll() if the designated constraints
// aren't met.
type IPFireWallOFFMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPFireWallOFFMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPFireWallOFFMultiError) AllErrors() []error { return m }

// IPFireWallOFFValidationError is the validation error returned by
// IPFireWallOFF.Validate if the designated constraints aren't met.
type IPFireWallOFFValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPFireWallOFFValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPFireWallOFFValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPFireWallOFFValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPFireWallOFFValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPFireWallOFFValidationError) ErrorName() string { return "IPFireWallOFFValidationError" }

// Error satisfies the builtin error interface
func (e IPFireWallOFFValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPFireWallOFF.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPFireWallOFFValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPFireWallOFFValidationError{}

// Validate checks the field values on SELinuxOFF with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SELinuxOFF) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SELinuxOFF with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SELinuxOFFMultiError, or
// nil if none found.
func (m *SELinuxOFF) ValidateAll() error {
	return m.validate(true)
}

func (m *SELinuxOFF) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SELinuxOFFValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SELinuxOFFValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SELinuxOFFValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SELinuxOFFValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SELinuxOFFValidationError{
					field:  "Process",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SELinuxOFFValidationError{
				field:  "Process",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return SELinuxOFFMultiError(errors)
	}

	return nil
}

// SELinuxOFFMultiError is an error wrapping multiple validation errors
// returned by SELinuxOFF.ValidateAll() if the designated constraints aren't met.
type SELinuxOFFMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SELinuxOFFMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SELinuxOFFMultiError) AllErrors() []error { return m }

// SELinuxOFFValidationError is the validation error returned by
// SELinuxOFF.Validate if the designated constraints aren't met.
type SELinuxOFFValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SELinuxOFFValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SELinuxOFFValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SELinuxOFFValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SELinuxOFFValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SELinuxOFFValidationError) ErrorName() string { return "SELinuxOFFValidationError" }

// Error satisfies the builtin error interface
func (e SELinuxOFFValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSELinuxOFF.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SELinuxOFFValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SELinuxOFFValidationError{}

// Validate checks the field values on RegOperateValueStartup with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RegOperateValueStartup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RegOperateValueStartup with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RegOperateValueStartupMultiError, or nil if none found.
func (m *RegOperateValueStartup) ValidateAll() error {
	return m.validate(true)
}

func (m *RegOperateValueStartup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RegOperateValueStartupValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RegOperateValueStartupValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RegOperateValueStartupValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessID

	// no validation rules for Ring3KeyPath

	// no validation rules for RegValueName

	// no validation rules for RegValueType

	// no validation rules for RegBinaryValue

	if len(errors) > 0 {
		return RegOperateValueStartupMultiError(errors)
	}

	return nil
}

// RegOperateValueStartupMultiError is an error wrapping multiple validation
// errors returned by RegOperateValueStartup.ValidateAll() if the designated
// constraints aren't met.
type RegOperateValueStartupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RegOperateValueStartupMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RegOperateValueStartupMultiError) AllErrors() []error { return m }

// RegOperateValueStartupValidationError is the validation error returned by
// RegOperateValueStartup.Validate if the designated constraints aren't met.
type RegOperateValueStartupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RegOperateValueStartupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RegOperateValueStartupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RegOperateValueStartupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RegOperateValueStartupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RegOperateValueStartupValidationError) ErrorName() string {
	return "RegOperateValueStartupValidationError"
}

// Error satisfies the builtin error interface
func (e RegOperateValueStartupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRegOperateValueStartup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RegOperateValueStartupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RegOperateValueStartupValidationError{}

// Validate checks the field values on LoadDriver with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoadDriver) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoadDriver with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoadDriverMultiError, or
// nil if none found.
func (m *LoadDriver) ValidateAll() error {
	return m.validate(true)
}

func (m *LoadDriver) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoadDriverValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoadDriverValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoadDriverValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceProcess()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoadDriverValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoadDriverValidationError{
					field:  "SourceProcess",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceProcess()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoadDriverValidationError{
				field:  "SourceProcess",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DriverPath

	// no validation rules for DriverSha256

	// no validation rules for Company

	// no validation rules for SignCompany

	if len(errors) > 0 {
		return LoadDriverMultiError(errors)
	}

	return nil
}

// LoadDriverMultiError is an error wrapping multiple validation errors
// returned by LoadDriver.ValidateAll() if the designated constraints aren't met.
type LoadDriverMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoadDriverMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoadDriverMultiError) AllErrors() []error { return m }

// LoadDriverValidationError is the validation error returned by
// LoadDriver.Validate if the designated constraints aren't met.
type LoadDriverValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoadDriverValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoadDriverValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoadDriverValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoadDriverValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoadDriverValidationError) ErrorName() string { return "LoadDriverValidationError" }

// Error satisfies the builtin error interface
func (e LoadDriverValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoadDriver.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoadDriverValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoadDriverValidationError{}
