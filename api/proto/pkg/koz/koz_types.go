package koz

type VirusConfigListArgs struct {
	GroupIDs []int64 `json:"group_ids"`
}

type VirusConfigList struct {
	Items []*VirusConfigListItem `json:"items"`
}

type VirusConfigListItem struct {
	GroupID         int64     `json:"group_id"`
	PackageID       int64     `json:"package_id"`
	VirusObjWindows *VirusObj `json:"virus_obj_windows"`
	VirusObjLinux   *VirusObj `json:"virus_obj_linux"`
}

type VirusObj struct {
	RealTimeScan     bool             `json:"real_time_scan"`
	PhishingDetect   bool             `json:"phishing_detect"`
	Time             VirusTime        `json:"time"`
	Path             []string         `json:"path"` // "1": 全盘; "2": 自定义; "3": 推荐
	VirusCustom      VirusCustom      `json:"custom"`
	VirusIgnore      VirusIgnore      `json:"ignore"`
	VirusTypesConfig int8             `json:"virus_types_config"` // 1: 高危文件; 2: 静态文件; 3: 全量文件; 4: 自定义采集
	VirusTypesMapper VirusTypesMapper `json:"virus_types_mapper"`
	VirusFilesize    int              `json:"virus_filesize"`
}

type VirusTime struct {
	Immediate int64            `json:"immediate"`
	Plant     uint32           `json:"plant"`  // 扫描时间: 0b0001: 立即; 0b0010: 周期; 0b0011: 立即+周期
	Option    uint32           `json:"option"` // 1: daily; 2: weekly; 3: monthly
	Dom       []uint32         `json:"day_of_mon"`
	Dow       []uint32         `json:"day_of_week"`
	TimeRange []VirusTimeRange `json:"timerange"`
}

type VirusTimeRange struct {
	Begin string `json:"begin"`
	End   string `json:"end"`
}

type VirusCustom struct {
	Content []string `json:"content"`
}

type VirusIgnore struct {
	Content []string `json:"content"`
}

// VirusTypesMapper 新分级上报配置映射, 从库中读取时, 需要对旧 Switch 作兼容处理
type VirusTypesMapper struct {
	Exec     map[uint32]uint8 `json:"exec"`
	Document map[uint32]uint8 `json:"document"`
	Shell    map[uint32]uint8 `json:"shell"`
	Link     map[uint32]uint8 `json:"link"`
}
