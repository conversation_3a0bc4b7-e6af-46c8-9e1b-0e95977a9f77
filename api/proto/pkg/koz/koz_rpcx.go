package koz

import (
	"context"
	"git.anxin.com/pkg/axgolib/res"
	"git.anxin.com/pkg/golib/base"
	"git.anxin.com/pkg/golib/errs"
)

type RpcxClient struct {
	client *base.RpcxClient
}

func NewRpcxClient(client *base.RpcxClient) (*RpcxClient, error) {
	return &RpcxClient{
		client: client,
	}, nil
}

// VirusConfigListByGroupIDs 基于主机分组id提取策略信息
func (c *RpcxClient) VirusConfigListByGroupIDs(ctx context.Context, args *VirusConfigListArgs) (*VirusConfigList, error) {
	resp := &VirusConfigList{}
	err := c.call(ctx, "VirusConfigListByGroupIDs", args, resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *RpcxClient) call(ctx context.Context, method string, args any, resData any) error {
	var reply res.Response
	if err := c.client.P2PCall(ctx, method, args, &reply); err != nil {
		return err
	}
	if reply.IsError() {
		return errs.New(reply.Code, reply.Message)
	}
	if resData != nil {
		reply.ReadData(resData)
	}
	return nil
}
