// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: portal/portal.proto

package portal

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProbeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AuthAddr     string `protobuf:"bytes,1,opt,name=auth_addr,json=authAddr,proto3" json:"auth_addr,omitempty"`             // 身份认证地址
	AuthKey      string `protobuf:"bytes,2,opt,name=auth_key,json=authKey,proto3" json:"auth_key,omitempty"`                // 身份认证key
	CloudAddr    string `protobuf:"bytes,3,opt,name=cloud_addr,json=cloudAddr,proto3" json:"cloud_addr,omitempty"`          // 沙箱服务地址
	CloudSecret  string `protobuf:"bytes,4,opt,name=cloud_secret,json=cloudSecret,proto3" json:"cloud_secret,omitempty"`    // 沙箱服务密钥
	BucketAddr   string `protobuf:"bytes,5,opt,name=bucket_addr,json=bucketAddr,proto3" json:"bucket_addr,omitempty"`       // 存储桶地址
	BucketSecret string `protobuf:"bytes,6,opt,name=bucket_secret,json=bucketSecret,proto3" json:"bucket_secret,omitempty"` // 存储桶密钥
}

func (x *ProbeReq) Reset() {
	*x = ProbeReq{}
	mi := &file_portal_portal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProbeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProbeReq) ProtoMessage() {}

func (x *ProbeReq) ProtoReflect() protoreflect.Message {
	mi := &file_portal_portal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProbeReq.ProtoReflect.Descriptor instead.
func (*ProbeReq) Descriptor() ([]byte, []int) {
	return file_portal_portal_proto_rawDescGZIP(), []int{0}
}

func (x *ProbeReq) GetAuthAddr() string {
	if x != nil {
		return x.AuthAddr
	}
	return ""
}

func (x *ProbeReq) GetAuthKey() string {
	if x != nil {
		return x.AuthKey
	}
	return ""
}

func (x *ProbeReq) GetCloudAddr() string {
	if x != nil {
		return x.CloudAddr
	}
	return ""
}

func (x *ProbeReq) GetCloudSecret() string {
	if x != nil {
		return x.CloudSecret
	}
	return ""
}

func (x *ProbeReq) GetBucketAddr() string {
	if x != nil {
		return x.BucketAddr
	}
	return ""
}

func (x *ProbeReq) GetBucketSecret() string {
	if x != nil {
		return x.BucketSecret
	}
	return ""
}

type ProbeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ProbeResp) Reset() {
	*x = ProbeResp{}
	mi := &file_portal_portal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProbeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProbeResp) ProtoMessage() {}

func (x *ProbeResp) ProtoReflect() protoreflect.Message {
	mi := &file_portal_portal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProbeResp.ProtoReflect.Descriptor instead.
func (*ProbeResp) Descriptor() ([]byte, []int) {
	return file_portal_portal_proto_rawDescGZIP(), []int{1}
}

type FileDetectReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename  string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`                    // 仅首个包携带
	Sha256    string `protobuf:"bytes,2,opt,name=sha256,proto3" json:"sha256,omitempty"`                        // 文件sha256
	ChunkData []byte `protobuf:"bytes,3,opt,name=chunk_data,json=chunkData,proto3" json:"chunk_data,omitempty"` // 数据块
	Eof       bool   `protobuf:"varint,4,opt,name=eof,proto3" json:"eof,omitempty"`                             // 是否结束标志
}

func (x *FileDetectReq) Reset() {
	*x = FileDetectReq{}
	mi := &file_portal_portal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectReq) ProtoMessage() {}

func (x *FileDetectReq) ProtoReflect() protoreflect.Message {
	mi := &file_portal_portal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectReq.ProtoReflect.Descriptor instead.
func (*FileDetectReq) Descriptor() ([]byte, []int) {
	return file_portal_portal_proto_rawDescGZIP(), []int{2}
}

func (x *FileDetectReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FileDetectReq) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileDetectReq) GetChunkData() []byte {
	if x != nil {
		return x.ChunkData
	}
	return nil
}

func (x *FileDetectReq) GetEof() bool {
	if x != nil {
		return x.Eof
	}
	return false
}

type FileDetectResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 检测任务Id
}

func (x *FileDetectResp) Reset() {
	*x = FileDetectResp{}
	mi := &file_portal_portal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetectResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetectResp) ProtoMessage() {}

func (x *FileDetectResp) ProtoReflect() protoreflect.Message {
	mi := &file_portal_portal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetectResp.ProtoReflect.Descriptor instead.
func (*FileDetectResp) Descriptor() ([]byte, []int) {
	return file_portal_portal_proto_rawDescGZIP(), []int{3}
}

func (x *FileDetectResp) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

var File_portal_portal_proto protoreflect.FileDescriptor

var file_portal_portal_proto_rawDesc = []byte{
	0x0a, 0x13, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2f, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x22, 0xca, 0x01,
	0x0a, 0x08, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61,
	0x75, 0x74, 0x68, 0x41, 0x64, 0x64, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x75, 0x74, 0x68, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x75, 0x74, 0x68, 0x4b,
	0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x41, 0x64, 0x64,
	0x72, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x22, 0x0b, 0x0a, 0x09, 0x50, 0x72,
	0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x74, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03, 0x65,
	0x6f, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x03, 0x65, 0x6f, 0x66, 0x22, 0x29, 0x0a,
	0x0e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x32, 0x7b, 0x0a, 0x06, 0x50, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x05, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x12, 0x10, 0x2e, 0x70, 0x6f,
	0x72, 0x74, 0x61, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e,
	0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x50, 0x72, 0x6f, 0x62, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x22, 0x00, 0x12, 0x41, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x12, 0x15, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x70, 0x6f, 0x72, 0x74, 0x61, 0x6c,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x70, 0x22,
	0x00, 0x28, 0x01, 0x30, 0x01, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x70, 0x6f, 0x72, 0x74,
	0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_portal_portal_proto_rawDescOnce sync.Once
	file_portal_portal_proto_rawDescData = file_portal_portal_proto_rawDesc
)

func file_portal_portal_proto_rawDescGZIP() []byte {
	file_portal_portal_proto_rawDescOnce.Do(func() {
		file_portal_portal_proto_rawDescData = protoimpl.X.CompressGZIP(file_portal_portal_proto_rawDescData)
	})
	return file_portal_portal_proto_rawDescData
}

var file_portal_portal_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_portal_portal_proto_goTypes = []any{
	(*ProbeReq)(nil),       // 0: portal.ProbeReq
	(*ProbeResp)(nil),      // 1: portal.ProbeResp
	(*FileDetectReq)(nil),  // 2: portal.FileDetectReq
	(*FileDetectResp)(nil), // 3: portal.FileDetectResp
}
var file_portal_portal_proto_depIdxs = []int32{
	0, // 0: portal.Portal.Probe:input_type -> portal.ProbeReq
	2, // 1: portal.Portal.FileDetect:input_type -> portal.FileDetectReq
	1, // 2: portal.Portal.Probe:output_type -> portal.ProbeResp
	3, // 3: portal.Portal.FileDetect:output_type -> portal.FileDetectResp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_portal_portal_proto_init() }
func file_portal_portal_proto_init() {
	if File_portal_portal_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_portal_portal_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_portal_portal_proto_goTypes,
		DependencyIndexes: file_portal_portal_proto_depIdxs,
		MessageInfos:      file_portal_portal_proto_msgTypes,
	}.Build()
	File_portal_portal_proto = out.File
	file_portal_portal_proto_rawDesc = nil
	file_portal_portal_proto_goTypes = nil
	file_portal_portal_proto_depIdxs = nil
}
