// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: portal/portal.proto

package portal

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Portal_Probe_FullMethodName      = "/portal.Portal/Probe"
	Portal_FileDetect_FullMethodName = "/portal.Portal/FileDetect"
)

// PortalClient is the client API for Portal service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 网防云接入服务
type PortalClient interface {
	// 探测云沙箱
	Probe(ctx context.Context, in *ProbeReq, opts ...grpc.CallOption) (*ProbeResp, error)
	// 文件检测
	FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error)
}

type portalClient struct {
	cc grpc.ClientConnInterface
}

func NewPortalClient(cc grpc.ClientConnInterface) PortalClient {
	return &portalClient{cc}
}

func (c *portalClient) Probe(ctx context.Context, in *ProbeReq, opts ...grpc.CallOption) (*ProbeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProbeResp)
	err := c.cc.Invoke(ctx, Portal_Probe_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *portalClient) FileDetect(ctx context.Context, opts ...grpc.CallOption) (grpc.BidiStreamingClient[FileDetectReq, FileDetectResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &Portal_ServiceDesc.Streams[0], Portal_FileDetect_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[FileDetectReq, FileDetectResp]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Portal_FileDetectClient = grpc.BidiStreamingClient[FileDetectReq, FileDetectResp]

// PortalServer is the server API for Portal service.
// All implementations must embed UnimplementedPortalServer
// for forward compatibility.
//
// 网防云接入服务
type PortalServer interface {
	// 探测云沙箱
	Probe(context.Context, *ProbeReq) (*ProbeResp, error)
	// 文件检测
	FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error
	mustEmbedUnimplementedPortalServer()
}

// UnimplementedPortalServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPortalServer struct{}

func (UnimplementedPortalServer) Probe(context.Context, *ProbeReq) (*ProbeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Probe not implemented")
}
func (UnimplementedPortalServer) FileDetect(grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]) error {
	return status.Errorf(codes.Unimplemented, "method FileDetect not implemented")
}
func (UnimplementedPortalServer) mustEmbedUnimplementedPortalServer() {}
func (UnimplementedPortalServer) testEmbeddedByValue()                {}

// UnsafePortalServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PortalServer will
// result in compilation errors.
type UnsafePortalServer interface {
	mustEmbedUnimplementedPortalServer()
}

func RegisterPortalServer(s grpc.ServiceRegistrar, srv PortalServer) {
	// If the following call pancis, it indicates UnimplementedPortalServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Portal_ServiceDesc, srv)
}

func _Portal_Probe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProbeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PortalServer).Probe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Portal_Probe_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PortalServer).Probe(ctx, req.(*ProbeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Portal_FileDetect_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(PortalServer).FileDetect(&grpc.GenericServerStream[FileDetectReq, FileDetectResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type Portal_FileDetectServer = grpc.BidiStreamingServer[FileDetectReq, FileDetectResp]

// Portal_ServiceDesc is the grpc.ServiceDesc for Portal service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Portal_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "portal.Portal",
	HandlerType: (*PortalServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Probe",
			Handler:    _Portal_Probe_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "FileDetect",
			Handler:       _Portal_FileDetect_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "portal/portal.proto",
}
