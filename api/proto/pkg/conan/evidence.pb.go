// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/evidence.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 资源任务状态枚举
type ResourceTaskStatus int32

const (
	ResourceTaskStatus_RESOURCE_TASK_STATUS_NULL     ResourceTaskStatus = 0 // 未知
	ResourceTaskStatus_RESOURCE_TASK_STATUS_UNSENT   ResourceTaskStatus = 1 // 未下发 (业务创建时的状态)
	ResourceTaskStatus_RESOURCE_TASK_STATUS_PREPARE  ResourceTaskStatus = 2 // 待下发 (生成临时Token, 并已通知业务服务)
	ResourceTaskStatus_RESOURCE_TASK_STATUS_SENT     ResourceTaskStatus = 3 // 已下发 (业务服务已通过临时Token换取会话Token)
	ResourceTaskStatus_RESOURCE_TASK_STATUS_FINISHED ResourceTaskStatus = 4 // 已完成
	ResourceTaskStatus_RESOURCE_TASK_STATUS_TIMEOUT  ResourceTaskStatus = 5 // 超时
	ResourceTaskStatus_RESOURCE_TASK_STATUS_CANCEL   ResourceTaskStatus = 6 // 用户取消
)

// Enum value maps for ResourceTaskStatus.
var (
	ResourceTaskStatus_name = map[int32]string{
		0: "RESOURCE_TASK_STATUS_NULL",
		1: "RESOURCE_TASK_STATUS_UNSENT",
		2: "RESOURCE_TASK_STATUS_PREPARE",
		3: "RESOURCE_TASK_STATUS_SENT",
		4: "RESOURCE_TASK_STATUS_FINISHED",
		5: "RESOURCE_TASK_STATUS_TIMEOUT",
		6: "RESOURCE_TASK_STATUS_CANCEL",
	}
	ResourceTaskStatus_value = map[string]int32{
		"RESOURCE_TASK_STATUS_NULL":     0,
		"RESOURCE_TASK_STATUS_UNSENT":   1,
		"RESOURCE_TASK_STATUS_PREPARE":  2,
		"RESOURCE_TASK_STATUS_SENT":     3,
		"RESOURCE_TASK_STATUS_FINISHED": 4,
		"RESOURCE_TASK_STATUS_TIMEOUT":  5,
		"RESOURCE_TASK_STATUS_CANCEL":   6,
	}
)

func (x ResourceTaskStatus) Enum() *ResourceTaskStatus {
	p := new(ResourceTaskStatus)
	*p = x
	return p
}

func (x ResourceTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[0].Descriptor()
}

func (ResourceTaskStatus) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[0]
}

func (x ResourceTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceTaskStatus.Descriptor instead.
func (ResourceTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{0}
}

// 证据提取状态
type EvidenceStatus int32

const (
	EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED         EvidenceStatus = 0  // 未提取
	EvidenceStatus_EVIDENCE_STATUS_PREPARE             EvidenceStatus = 1  // 待取证
	EvidenceStatus_EVIDENCE_STATUS_EXTRACTING          EvidenceStatus = 2  // 取证中
	EvidenceStatus_EVIDENCE_STATUS_SUCCESS             EvidenceStatus = 3  // 成功
	EvidenceStatus_EVIDENCE_STATUS_IN_WHITELIST        EvidenceStatus = 4  // 证据在白名单中,不提取
	EvidenceStatus_EVIDENCE_STATUS_HOST_NOT_FOUND      EvidenceStatus = 5  // 主机不存在
	EvidenceStatus_EVIDENCE_STATUS_HOST_OFFLINE        EvidenceStatus = 6  // 主机已离线
	EvidenceStatus_EVIDENCE_STATUS_SEND_FAILED         EvidenceStatus = 7  // 取证任务下发失败
	EvidenceStatus_EVIDENCE_STATUS_TIMEOUT             EvidenceStatus = 8  // 超时
	EvidenceStatus_EVIDENCE_STATUS_FILE_SIZE_TOO_LARGE EvidenceStatus = 9  // 文件太大
	EvidenceStatus_EVIDENCE_STATUS_FILE_NOT_FOUND      EvidenceStatus = 10 // 证据未找到
	EvidenceStatus_EVIDENCE_STATUS_UPLOAD_FAILED       EvidenceStatus = 11 // 证据上传失败
	EvidenceStatus_EVIDENCE_STATUS_HAS_EXPIRED         EvidenceStatus = 12 // 证据已过期
	EvidenceStatus_EVIDENCE_STATUS_NOT_SUPPORT         EvidenceStatus = 13 // 不支持取证
	EvidenceStatus_EVIDENCE_STATUS_UPLOAD_UNKNOWN      EvidenceStatus = 99 // 未知错误
)

// Enum value maps for EvidenceStatus.
var (
	EvidenceStatus_name = map[int32]string{
		0:  "EVIDENCE_STATUS_UNSPECIFIED",
		1:  "EVIDENCE_STATUS_PREPARE",
		2:  "EVIDENCE_STATUS_EXTRACTING",
		3:  "EVIDENCE_STATUS_SUCCESS",
		4:  "EVIDENCE_STATUS_IN_WHITELIST",
		5:  "EVIDENCE_STATUS_HOST_NOT_FOUND",
		6:  "EVIDENCE_STATUS_HOST_OFFLINE",
		7:  "EVIDENCE_STATUS_SEND_FAILED",
		8:  "EVIDENCE_STATUS_TIMEOUT",
		9:  "EVIDENCE_STATUS_FILE_SIZE_TOO_LARGE",
		10: "EVIDENCE_STATUS_FILE_NOT_FOUND",
		11: "EVIDENCE_STATUS_UPLOAD_FAILED",
		12: "EVIDENCE_STATUS_HAS_EXPIRED",
		13: "EVIDENCE_STATUS_NOT_SUPPORT",
		99: "EVIDENCE_STATUS_UPLOAD_UNKNOWN",
	}
	EvidenceStatus_value = map[string]int32{
		"EVIDENCE_STATUS_UNSPECIFIED":         0,
		"EVIDENCE_STATUS_PREPARE":             1,
		"EVIDENCE_STATUS_EXTRACTING":          2,
		"EVIDENCE_STATUS_SUCCESS":             3,
		"EVIDENCE_STATUS_IN_WHITELIST":        4,
		"EVIDENCE_STATUS_HOST_NOT_FOUND":      5,
		"EVIDENCE_STATUS_HOST_OFFLINE":        6,
		"EVIDENCE_STATUS_SEND_FAILED":         7,
		"EVIDENCE_STATUS_TIMEOUT":             8,
		"EVIDENCE_STATUS_FILE_SIZE_TOO_LARGE": 9,
		"EVIDENCE_STATUS_FILE_NOT_FOUND":      10,
		"EVIDENCE_STATUS_UPLOAD_FAILED":       11,
		"EVIDENCE_STATUS_HAS_EXPIRED":         12,
		"EVIDENCE_STATUS_NOT_SUPPORT":         13,
		"EVIDENCE_STATUS_UPLOAD_UNKNOWN":      99,
	}
)

func (x EvidenceStatus) Enum() *EvidenceStatus {
	p := new(EvidenceStatus)
	*p = x
	return p
}

func (x EvidenceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[1].Descriptor()
}

func (EvidenceStatus) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[1]
}

func (x EvidenceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceStatus.Descriptor instead.
func (EvidenceStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{1}
}

// 证据来源类型
type EvidenceSourceType int32

const (
	EvidenceSourceType_EVIDENCE_SOURCE_TYPE_UNDEFINED EvidenceSourceType = 0
	EvidenceSourceType_EVIDENCE_SOURCE_TYPE_AUTO      EvidenceSourceType = 1
	EvidenceSourceType_EVIDENCE_SOURCE_TYPE_MANUAL    EvidenceSourceType = 2
	EvidenceSourceType_EVIDENCE_SOURCE_TYPE_EXTERNAL  EvidenceSourceType = 3
)

// Enum value maps for EvidenceSourceType.
var (
	EvidenceSourceType_name = map[int32]string{
		0: "EVIDENCE_SOURCE_TYPE_UNDEFINED",
		1: "EVIDENCE_SOURCE_TYPE_AUTO",
		2: "EVIDENCE_SOURCE_TYPE_MANUAL",
		3: "EVIDENCE_SOURCE_TYPE_EXTERNAL",
	}
	EvidenceSourceType_value = map[string]int32{
		"EVIDENCE_SOURCE_TYPE_UNDEFINED": 0,
		"EVIDENCE_SOURCE_TYPE_AUTO":      1,
		"EVIDENCE_SOURCE_TYPE_MANUAL":    2,
		"EVIDENCE_SOURCE_TYPE_EXTERNAL":  3,
	}
)

func (x EvidenceSourceType) Enum() *EvidenceSourceType {
	p := new(EvidenceSourceType)
	*p = x
	return p
}

func (x EvidenceSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[2].Descriptor()
}

func (EvidenceSourceType) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[2]
}

func (x EvidenceSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceSourceType.Descriptor instead.
func (EvidenceSourceType) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{2}
}

// 证据类型
type EvidenceType int32

const (
	EvidenceType_EVIDENCE_TYPE_EMPTY            EvidenceType = 0
	EvidenceType_EVIDENCE_TYPE_PROCESS_CHAIN    EvidenceType = 1
	EvidenceType_EVIDENCE_TYPE_FILE             EvidenceType = 2
	EvidenceType_EVIDENCE_TYPE_SCRIPT_FILE      EvidenceType = 4
	EvidenceType_EVIDENCE_TYPE_MEMORY_DUMP      EvidenceType = 8
	EvidenceType_EVIDENCE_TYPE_LOG              EvidenceType = 16
	EvidenceType_EVIDENCE_TYPE_MINI_MEMORY_DUMP EvidenceType = 32
	EvidenceType_EVIDENCE_TYPE_MEMORY_SEGMENT   EvidenceType = 64
	EvidenceType_EVIDENCE_TYPE_ALL              EvidenceType = 95 // exclude MINI_MEMORY_DUMP
	EvidenceType_EVIDENCE_TYPE_AUTO             EvidenceType = 71 // EvidenceTypeProcessChain | EvidenceTypeFile | EvidenceTypeScriptFile | EvidenceTypeMemorySegment
)

// Enum value maps for EvidenceType.
var (
	EvidenceType_name = map[int32]string{
		0:  "EVIDENCE_TYPE_EMPTY",
		1:  "EVIDENCE_TYPE_PROCESS_CHAIN",
		2:  "EVIDENCE_TYPE_FILE",
		4:  "EVIDENCE_TYPE_SCRIPT_FILE",
		8:  "EVIDENCE_TYPE_MEMORY_DUMP",
		16: "EVIDENCE_TYPE_LOG",
		32: "EVIDENCE_TYPE_MINI_MEMORY_DUMP",
		64: "EVIDENCE_TYPE_MEMORY_SEGMENT",
		95: "EVIDENCE_TYPE_ALL",
		71: "EVIDENCE_TYPE_AUTO",
	}
	EvidenceType_value = map[string]int32{
		"EVIDENCE_TYPE_EMPTY":            0,
		"EVIDENCE_TYPE_PROCESS_CHAIN":    1,
		"EVIDENCE_TYPE_FILE":             2,
		"EVIDENCE_TYPE_SCRIPT_FILE":      4,
		"EVIDENCE_TYPE_MEMORY_DUMP":      8,
		"EVIDENCE_TYPE_LOG":              16,
		"EVIDENCE_TYPE_MINI_MEMORY_DUMP": 32,
		"EVIDENCE_TYPE_MEMORY_SEGMENT":   64,
		"EVIDENCE_TYPE_ALL":              95,
		"EVIDENCE_TYPE_AUTO":             71,
	}
)

func (x EvidenceType) Enum() *EvidenceType {
	p := new(EvidenceType)
	*p = x
	return p
}

func (x EvidenceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[3].Descriptor()
}

func (EvidenceType) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[3]
}

func (x EvidenceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceType.Descriptor instead.
func (EvidenceType) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{3}
}

// 证据存储模式
type EvidenceStorageMode int32

const (
	EvidenceStorageMode_EVIDENCE_STORAGE_MODE_UNDEFINED EvidenceStorageMode = 0 // 未定义
	EvidenceStorageMode_EVIDENCE_STORAGE_MODE_FILE      EvidenceStorageMode = 1 // 文件(minio: bucket + objectname)
	EvidenceStorageMode_EVIDENCE_STORAGE_MODE_CONTENT   EvidenceStorageMode = 2 // 内容(json)
)

// Enum value maps for EvidenceStorageMode.
var (
	EvidenceStorageMode_name = map[int32]string{
		0: "EVIDENCE_STORAGE_MODE_UNDEFINED",
		1: "EVIDENCE_STORAGE_MODE_FILE",
		2: "EVIDENCE_STORAGE_MODE_CONTENT",
	}
	EvidenceStorageMode_value = map[string]int32{
		"EVIDENCE_STORAGE_MODE_UNDEFINED": 0,
		"EVIDENCE_STORAGE_MODE_FILE":      1,
		"EVIDENCE_STORAGE_MODE_CONTENT":   2,
	}
)

func (x EvidenceStorageMode) Enum() *EvidenceStorageMode {
	p := new(EvidenceStorageMode)
	*p = x
	return p
}

func (x EvidenceStorageMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceStorageMode) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[4].Descriptor()
}

func (EvidenceStorageMode) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[4]
}

func (x EvidenceStorageMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceStorageMode.Descriptor instead.
func (EvidenceStorageMode) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{4}
}

// 取证结果返回方式
type EvidenceReturnWay int32

const (
	EvidenceReturnWay_EVIDENCE_RETURN_WAY_UNSPECIFIED EvidenceReturnWay = 0
	EvidenceReturnWay_EVIDENCE_RETURN_WAY_POLLING     EvidenceReturnWay = 1 // 轮询
	EvidenceReturnWay_EVIDENCE_RETURN_WAY_CALLBACK    EvidenceReturnWay = 2 // 回调
)

// Enum value maps for EvidenceReturnWay.
var (
	EvidenceReturnWay_name = map[int32]string{
		0: "EVIDENCE_RETURN_WAY_UNSPECIFIED",
		1: "EVIDENCE_RETURN_WAY_POLLING",
		2: "EVIDENCE_RETURN_WAY_CALLBACK",
	}
	EvidenceReturnWay_value = map[string]int32{
		"EVIDENCE_RETURN_WAY_UNSPECIFIED": 0,
		"EVIDENCE_RETURN_WAY_POLLING":     1,
		"EVIDENCE_RETURN_WAY_CALLBACK":    2,
	}
)

func (x EvidenceReturnWay) Enum() *EvidenceReturnWay {
	p := new(EvidenceReturnWay)
	*p = x
	return p
}

func (x EvidenceReturnWay) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvidenceReturnWay) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_evidence_proto_enumTypes[5].Descriptor()
}

func (EvidenceReturnWay) Type() protoreflect.EnumType {
	return &file_conan_evidence_proto_enumTypes[5]
}

func (x EvidenceReturnWay) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvidenceReturnWay.Descriptor instead.
func (EvidenceReturnWay) EnumDescriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{5}
}

type ClueObtainEvidenceTaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvidenceType EvidenceType              `protobuf:"varint,1,opt,name=evidence_type,json=evidenceType,proto3,enum=conan.EvidenceType" json:"evidence_type,omitempty"`
	Status       *ObtainEvidenceTaskStatus `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ClueObtainEvidenceTaskStatus) Reset() {
	*x = ClueObtainEvidenceTaskStatus{}
	mi := &file_conan_evidence_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueObtainEvidenceTaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueObtainEvidenceTaskStatus) ProtoMessage() {}

func (x *ClueObtainEvidenceTaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueObtainEvidenceTaskStatus.ProtoReflect.Descriptor instead.
func (*ClueObtainEvidenceTaskStatus) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{0}
}

func (x *ClueObtainEvidenceTaskStatus) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_EMPTY
}

func (x *ClueObtainEvidenceTaskStatus) GetStatus() *ObtainEvidenceTaskStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

// 通道可用通知信息
type LimitPassNotifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`         // 取证任务ID
	Channel    int64  `protobuf:"varint,2,opt,name=channel,proto3" json:"channel,omitempty"`                     // 限流通道
	Category   int32  `protobuf:"varint,3,opt,name=category,proto3" json:"category,omitempty"`                   // 分类(非必要字段)
	TempToken  string `protobuf:"bytes,4,opt,name=temp_token,json=tempToken,proto3" json:"temp_token,omitempty"` // 临时Token
	Identifier string `protobuf:"bytes,5,opt,name=identifier,proto3" json:"identifier,omitempty"`                // 唯一串
	Content    string `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`                      // 内容
}

func (x *LimitPassNotifyRequest) Reset() {
	*x = LimitPassNotifyRequest{}
	mi := &file_conan_evidence_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LimitPassNotifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitPassNotifyRequest) ProtoMessage() {}

func (x *LimitPassNotifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitPassNotifyRequest.ProtoReflect.Descriptor instead.
func (*LimitPassNotifyRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{1}
}

func (x *LimitPassNotifyRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *LimitPassNotifyRequest) GetChannel() int64 {
	if x != nil {
		return x.Channel
	}
	return 0
}

func (x *LimitPassNotifyRequest) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *LimitPassNotifyRequest) GetTempToken() string {
	if x != nil {
		return x.TempToken
	}
	return ""
}

func (x *LimitPassNotifyRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *LimitPassNotifyRequest) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type ObtainEvidenceChannelAvailableNotifyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status ResourceTaskStatus `protobuf:"varint,1,opt,name=status,proto3,enum=conan.ResourceTaskStatus" json:"status,omitempty"`
}

func (x *ObtainEvidenceChannelAvailableNotifyResponse) Reset() {
	*x = ObtainEvidenceChannelAvailableNotifyResponse{}
	mi := &file_conan_evidence_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceChannelAvailableNotifyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceChannelAvailableNotifyResponse) ProtoMessage() {}

func (x *ObtainEvidenceChannelAvailableNotifyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceChannelAvailableNotifyResponse.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceChannelAvailableNotifyResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{2}
}

func (x *ObtainEvidenceChannelAvailableNotifyResponse) GetStatus() ResourceTaskStatus {
	if x != nil {
		return x.Status
	}
	return ResourceTaskStatus_RESOURCE_TASK_STATUS_NULL
}

// 取证任务状态
type ObtainEvidenceTaskStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId  int64          `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status  EvidenceStatus `protobuf:"varint,2,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	Existed bool           `protobuf:"varint,3,opt,name=existed,proto3" json:"existed,omitempty"`
}

func (x *ObtainEvidenceTaskStatus) Reset() {
	*x = ObtainEvidenceTaskStatus{}
	mi := &file_conan_evidence_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceTaskStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceTaskStatus) ProtoMessage() {}

func (x *ObtainEvidenceTaskStatus) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceTaskStatus.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceTaskStatus) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{3}
}

func (x *ObtainEvidenceTaskStatus) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ObtainEvidenceTaskStatus) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *ObtainEvidenceTaskStatus) GetExisted() bool {
	if x != nil {
		return x.Existed
	}
	return false
}

// 上传信息
type UploadInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BucketName string `protobuf:"bytes,1,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	ObjectName string `protobuf:"bytes,2,opt,name=object_name,json=objectName,proto3" json:"object_name,omitempty"`
	Md5        string `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	Size       int64  `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
}

func (x *UploadInfo) Reset() {
	*x = UploadInfo{}
	mi := &file_conan_evidence_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadInfo) ProtoMessage() {}

func (x *UploadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadInfo.ProtoReflect.Descriptor instead.
func (*UploadInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{4}
}

func (x *UploadInfo) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

func (x *UploadInfo) GetObjectName() string {
	if x != nil {
		return x.ObjectName
	}
	return ""
}

func (x *UploadInfo) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *UploadInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

// 取证完成信息通知
type ObtainEvidenceFinishedNotifyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadInfo  *UploadInfo        `protobuf:"bytes,1,opt,name=upload_info,json=uploadInfo,proto3" json:"upload_info,omitempty"`
	RawFilename string             `protobuf:"bytes,2,opt,name=raw_filename,json=rawFilename,proto3" json:"raw_filename,omitempty"`
	TaskId      int64              `protobuf:"varint,3,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Identifier  string             `protobuf:"bytes,4,opt,name=identifier,proto3" json:"identifier,omitempty"`
	Status      ResourceTaskStatus `protobuf:"varint,5,opt,name=status,proto3,enum=conan.ResourceTaskStatus" json:"status,omitempty"` // 修改为使用 ResourceTaskStatus 枚举
	FormParams  map[string]string  `protobuf:"bytes,6,rep,name=form_params,json=formParams,proto3" json:"form_params,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ObtainEvidenceFinishedNotifyRequest) Reset() {
	*x = ObtainEvidenceFinishedNotifyRequest{}
	mi := &file_conan_evidence_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceFinishedNotifyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceFinishedNotifyRequest) ProtoMessage() {}

func (x *ObtainEvidenceFinishedNotifyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceFinishedNotifyRequest.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceFinishedNotifyRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{5}
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetUploadInfo() *UploadInfo {
	if x != nil {
		return x.UploadInfo
	}
	return nil
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetRawFilename() string {
	if x != nil {
		return x.RawFilename
	}
	return ""
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetStatus() ResourceTaskStatus {
	if x != nil {
		return x.Status
	}
	return ResourceTaskStatus_RESOURCE_TASK_STATUS_NULL
}

func (x *ObtainEvidenceFinishedNotifyRequest) GetFormParams() map[string]string {
	if x != nil {
		return x.FormParams
	}
	return nil
}

// 取证结果响应
type ObtainEvidenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 这里需要根据实际的 MemProtect.ObtainEvidenceResp 结构定义
	// 由于没有看到具体定义，这里只是一个占位符
	Data string `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ObtainEvidenceResponse) Reset() {
	*x = ObtainEvidenceResponse{}
	mi := &file_conan_evidence_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceResponse) ProtoMessage() {}

func (x *ObtainEvidenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceResponse.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{6}
}

func (x *ObtainEvidenceResponse) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

// 证据来源信息
type EvidenceSourceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceType int32  `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"` // 取证来源类型
	SourceId   int64  `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`       // 取证来源ID
	Identifier string `protobuf:"bytes,3,opt,name=identifier,proto3" json:"identifier,omitempty"`                    // 唯一标识
}

func (x *EvidenceSourceInfo) Reset() {
	*x = EvidenceSourceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceSourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceSourceInfo) ProtoMessage() {}

func (x *EvidenceSourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceSourceInfo.ProtoReflect.Descriptor instead.
func (*EvidenceSourceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{7}
}

func (x *EvidenceSourceInfo) GetSourceType() int32 {
	if x != nil {
		return x.SourceType
	}
	return 0
}

func (x *EvidenceSourceInfo) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *EvidenceSourceInfo) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

// 主机线索信息
type HostClueInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	ClueKey   string `protobuf:"bytes,2,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
}

func (x *HostClueInfo) Reset() {
	*x = HostClueInfo{}
	mi := &file_conan_evidence_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HostClueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HostClueInfo) ProtoMessage() {}

func (x *HostClueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HostClueInfo.ProtoReflect.Descriptor instead.
func (*HostClueInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{8}
}

func (x *HostClueInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *HostClueInfo) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

// 线索取证任务请求
type ClueObtainEvidenceTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceInfo   *EvidenceSourceInfo `protobuf:"bytes,1,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	ClueId       string              `protobuf:"bytes,2,opt,name=clue_id,json=clueId,proto3" json:"clue_id,omitempty"`
	ClueType     int32               `protobuf:"varint,3,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	EvidenceType EvidenceType        `protobuf:"varint,4,opt,name=evidence_type,json=evidenceType,proto3,enum=conan.EvidenceType" json:"evidence_type,omitempty"`
}

func (x *ClueObtainEvidenceTaskRequest) Reset() {
	*x = ClueObtainEvidenceTaskRequest{}
	mi := &file_conan_evidence_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueObtainEvidenceTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueObtainEvidenceTaskRequest) ProtoMessage() {}

func (x *ClueObtainEvidenceTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueObtainEvidenceTaskRequest.ProtoReflect.Descriptor instead.
func (*ClueObtainEvidenceTaskRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{9}
}

func (x *ClueObtainEvidenceTaskRequest) GetSourceInfo() *EvidenceSourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *ClueObtainEvidenceTaskRequest) GetClueId() string {
	if x != nil {
		return x.ClueId
	}
	return ""
}

func (x *ClueObtainEvidenceTaskRequest) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *ClueObtainEvidenceTaskRequest) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_EMPTY
}

// 文件信息
type EvicenceFileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName    string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`           // 文件名
	FilePath    string `protobuf:"bytes,2,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`           // 文件路径
	RawFilesize int64  `protobuf:"varint,3,opt,name=raw_filesize,json=rawFilesize,proto3" json:"raw_filesize,omitempty"` // 原始文件size
	FileMd5     string `protobuf:"bytes,4,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`              // 文件md5
	FileSha1    string `protobuf:"bytes,5,opt,name=file_sha1,json=fileSha1,proto3" json:"file_sha1,omitempty"`           // 文件sha1
	FileSha256  string `protobuf:"bytes,6,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`     // 文件sha256
	Atime       int64  `protobuf:"varint,7,opt,name=atime,proto3" json:"atime,omitempty"`                                // 最后一次访问文件时间
	Mtime       int64  `protobuf:"varint,8,opt,name=mtime,proto3" json:"mtime,omitempty"`                                // 最后一次文件修改时间
	Ctime       int64  `protobuf:"varint,9,opt,name=ctime,proto3" json:"ctime,omitempty"`                                // 文件修改时间
}

func (x *EvicenceFileInfo) Reset() {
	*x = EvicenceFileInfo{}
	mi := &file_conan_evidence_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvicenceFileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvicenceFileInfo) ProtoMessage() {}

func (x *EvicenceFileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvicenceFileInfo.ProtoReflect.Descriptor instead.
func (*EvicenceFileInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{10}
}

func (x *EvicenceFileInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *EvicenceFileInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *EvicenceFileInfo) GetRawFilesize() int64 {
	if x != nil {
		return x.RawFilesize
	}
	return 0
}

func (x *EvicenceFileInfo) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *EvicenceFileInfo) GetFileSha1() string {
	if x != nil {
		return x.FileSha1
	}
	return ""
}

func (x *EvicenceFileInfo) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *EvicenceFileInfo) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *EvicenceFileInfo) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *EvicenceFileInfo) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

// 证据信息
type EvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvidenceType int32       `protobuf:"varint,1,opt,name=evidence_type,json=evidenceType,proto3" json:"evidence_type,omitempty"`
	UniqueFlag   string      `protobuf:"bytes,2,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	EvidenceSize uint64      `protobuf:"varint,3,opt,name=evidence_size,json=evidenceSize,proto3" json:"evidence_size,omitempty"`
	Filepath     string      `protobuf:"bytes,4,opt,name=filepath,proto3" json:"filepath,omitempty"`
	Filename     string      `protobuf:"bytes,5,opt,name=filename,proto3" json:"filename,omitempty"`
	FileDetail   *FileDetail `protobuf:"bytes,6,opt,name=file_detail,json=fileDetail,proto3" json:"file_detail,omitempty"`
}

func (x *EvidenceInfo) Reset() {
	*x = EvidenceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceInfo) ProtoMessage() {}

func (x *EvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceInfo.ProtoReflect.Descriptor instead.
func (*EvidenceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{11}
}

func (x *EvidenceInfo) GetEvidenceType() int32 {
	if x != nil {
		return x.EvidenceType
	}
	return 0
}

func (x *EvidenceInfo) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *EvidenceInfo) GetEvidenceSize() uint64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *EvidenceInfo) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *EvidenceInfo) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *EvidenceInfo) GetFileDetail() *FileDetail {
	if x != nil {
		return x.FileDetail
	}
	return nil
}

type FileDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawFilesize int64  `protobuf:"varint,1,opt,name=raw_filesize,json=rawFilesize,proto3" json:"raw_filesize,omitempty"`
	Md5         string `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	Sha256      string `protobuf:"bytes,3,opt,name=sha256,proto3" json:"sha256,omitempty"`
	Atime       int64  `protobuf:"varint,4,opt,name=atime,proto3" json:"atime,omitempty"`
	Mtime       int64  `protobuf:"varint,5,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Ctime       int64  `protobuf:"varint,6,opt,name=ctime,proto3" json:"ctime,omitempty"`
}

func (x *FileDetail) Reset() {
	*x = FileDetail{}
	mi := &file_conan_evidence_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileDetail) ProtoMessage() {}

func (x *FileDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileDetail.ProtoReflect.Descriptor instead.
func (*FileDetail) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{12}
}

func (x *FileDetail) GetRawFilesize() int64 {
	if x != nil {
		return x.RawFilesize
	}
	return 0
}

func (x *FileDetail) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *FileDetail) GetSha256() string {
	if x != nil {
		return x.Sha256
	}
	return ""
}

func (x *FileDetail) GetAtime() int64 {
	if x != nil {
		return x.Atime
	}
	return 0
}

func (x *FileDetail) GetMtime() int64 {
	if x != nil {
		return x.Mtime
	}
	return 0
}

func (x *FileDetail) GetCtime() int64 {
	if x != nil {
		return x.Ctime
	}
	return 0
}

// 文件取证任务请求
type FileObtainEvidenceTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostClueInfo      *HostClueInfo       `protobuf:"bytes,1,opt,name=host_clue_info,json=hostClueInfo,proto3" json:"host_clue_info,omitempty"`
	SourceInfo        *EvidenceSourceInfo `protobuf:"bytes,2,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	SearchKey         string              `protobuf:"bytes,3,opt,name=search_key,json=searchKey,proto3" json:"search_key,omitempty"`
	Timestamp         int64               `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ExistedRaiseError bool                `protobuf:"varint,5,opt,name=existed_raise_error,json=existedRaiseError,proto3" json:"existed_raise_error,omitempty"`
	EvidenceInfo      *EvidenceInfo       `protobuf:"bytes,6,opt,name=evidence_info,json=evidenceInfo,proto3" json:"evidence_info,omitempty"`
}

func (x *FileObtainEvidenceTaskInfo) Reset() {
	*x = FileObtainEvidenceTaskInfo{}
	mi := &file_conan_evidence_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileObtainEvidenceTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileObtainEvidenceTaskInfo) ProtoMessage() {}

func (x *FileObtainEvidenceTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileObtainEvidenceTaskInfo.ProtoReflect.Descriptor instead.
func (*FileObtainEvidenceTaskInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{13}
}

func (x *FileObtainEvidenceTaskInfo) GetHostClueInfo() *HostClueInfo {
	if x != nil {
		return x.HostClueInfo
	}
	return nil
}

func (x *FileObtainEvidenceTaskInfo) GetSourceInfo() *EvidenceSourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *FileObtainEvidenceTaskInfo) GetSearchKey() string {
	if x != nil {
		return x.SearchKey
	}
	return ""
}

func (x *FileObtainEvidenceTaskInfo) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *FileObtainEvidenceTaskInfo) GetExistedRaiseError() bool {
	if x != nil {
		return x.ExistedRaiseError
	}
	return false
}

func (x *FileObtainEvidenceTaskInfo) GetEvidenceInfo() *EvidenceInfo {
	if x != nil {
		return x.EvidenceInfo
	}
	return nil
}

// 批量文件取证任务请求
type BatchFileObtainEvidenceTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceInfo     *EvidenceSourceInfo `protobuf:"bytes,1,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	FileObjectName string              `protobuf:"bytes,2,opt,name=file_object_name,json=fileObjectName,proto3" json:"file_object_name,omitempty"`
}

func (x *BatchFileObtainEvidenceTaskRequest) Reset() {
	*x = BatchFileObtainEvidenceTaskRequest{}
	mi := &file_conan_evidence_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchFileObtainEvidenceTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchFileObtainEvidenceTaskRequest) ProtoMessage() {}

func (x *BatchFileObtainEvidenceTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchFileObtainEvidenceTaskRequest.ProtoReflect.Descriptor instead.
func (*BatchFileObtainEvidenceTaskRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{14}
}

func (x *BatchFileObtainEvidenceTaskRequest) GetSourceInfo() *EvidenceSourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *BatchFileObtainEvidenceTaskRequest) GetFileObjectName() string {
	if x != nil {
		return x.FileObjectName
	}
	return ""
}

// 批量文件取证响应
type BatchFileObtainEvidenceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchFileObtainEvidenceResponse) Reset() {
	*x = BatchFileObtainEvidenceResponse{}
	mi := &file_conan_evidence_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchFileObtainEvidenceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchFileObtainEvidenceResponse) ProtoMessage() {}

func (x *BatchFileObtainEvidenceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchFileObtainEvidenceResponse.ProtoReflect.Descriptor instead.
func (*BatchFileObtainEvidenceResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{15}
}

// 网络连接信息
type NetConnectionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DestIp   string `protobuf:"bytes,1,opt,name=dest_ip,json=destIp,proto3" json:"dest_ip,omitempty"`
	DestPort int32  `protobuf:"varint,2,opt,name=dest_port,json=destPort,proto3" json:"dest_port,omitempty"`
	SrcIp    string `protobuf:"bytes,3,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	SrcPort  int32  `protobuf:"varint,4,opt,name=src_port,json=srcPort,proto3" json:"src_port,omitempty"`
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
}

func (x *NetConnectionInfo) Reset() {
	*x = NetConnectionInfo{}
	mi := &file_conan_evidence_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetConnectionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetConnectionInfo) ProtoMessage() {}

func (x *NetConnectionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetConnectionInfo.ProtoReflect.Descriptor instead.
func (*NetConnectionInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{16}
}

func (x *NetConnectionInfo) GetDestIp() string {
	if x != nil {
		return x.DestIp
	}
	return ""
}

func (x *NetConnectionInfo) GetDestPort() int32 {
	if x != nil {
		return x.DestPort
	}
	return 0
}

func (x *NetConnectionInfo) GetSrcIp() string {
	if x != nil {
		return x.SrcIp
	}
	return ""
}

func (x *NetConnectionInfo) GetSrcPort() int32 {
	if x != nil {
		return x.SrcPort
	}
	return 0
}

func (x *NetConnectionInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

// 外联信息
type EvidenceOutReachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ConnectionInfo *NetConnectionInfo `protobuf:"bytes,1,opt,name=connection_info,json=connectionInfo,proto3" json:"connection_info,omitempty"`
	Timestamp      int64              `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *EvidenceOutReachInfo) Reset() {
	*x = EvidenceOutReachInfo{}
	mi := &file_conan_evidence_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceOutReachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceOutReachInfo) ProtoMessage() {}

func (x *EvidenceOutReachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceOutReachInfo.ProtoReflect.Descriptor instead.
func (*EvidenceOutReachInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{17}
}

func (x *EvidenceOutReachInfo) GetConnectionInfo() *NetConnectionInfo {
	if x != nil {
		return x.ConnectionInfo
	}
	return nil
}

func (x *EvidenceOutReachInfo) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// 网络取证任务请求
type NetObtainEvidenceTaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostClueInfo      *HostClueInfo         `protobuf:"bytes,1,opt,name=host_clue_info,json=hostClueInfo,proto3" json:"host_clue_info,omitempty"`
	SourceInfo        *EvidenceSourceInfo   `protobuf:"bytes,2,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	OutreachInfo      *EvidenceOutReachInfo `protobuf:"bytes,3,opt,name=outreach_info,json=outreachInfo,proto3" json:"outreach_info,omitempty"`
	ExistedRaiseError bool                  `protobuf:"varint,4,opt,name=existed_raise_error,json=existedRaiseError,proto3" json:"existed_raise_error,omitempty"`
}

func (x *NetObtainEvidenceTaskInfo) Reset() {
	*x = NetObtainEvidenceTaskInfo{}
	mi := &file_conan_evidence_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetObtainEvidenceTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetObtainEvidenceTaskInfo) ProtoMessage() {}

func (x *NetObtainEvidenceTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetObtainEvidenceTaskInfo.ProtoReflect.Descriptor instead.
func (*NetObtainEvidenceTaskInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{18}
}

func (x *NetObtainEvidenceTaskInfo) GetHostClueInfo() *HostClueInfo {
	if x != nil {
		return x.HostClueInfo
	}
	return nil
}

func (x *NetObtainEvidenceTaskInfo) GetSourceInfo() *EvidenceSourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *NetObtainEvidenceTaskInfo) GetOutreachInfo() *EvidenceOutReachInfo {
	if x != nil {
		return x.OutreachInfo
	}
	return nil
}

func (x *NetObtainEvidenceTaskInfo) GetExistedRaiseError() bool {
	if x != nil {
		return x.ExistedRaiseError
	}
	return false
}

// 日志取证任务请求
type LogObtainEvidenceTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId         string              `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	SourceInfo        *EvidenceSourceInfo `protobuf:"bytes,2,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	LogBit            int32               `protobuf:"varint,3,opt,name=log_bit,json=logBit,proto3" json:"log_bit,omitempty"`
	Timestamp         int64               `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	ExistedRaiseError bool                `protobuf:"varint,5,opt,name=existed_raise_error,json=existedRaiseError,proto3" json:"existed_raise_error,omitempty"`
}

func (x *LogObtainEvidenceTaskRequest) Reset() {
	*x = LogObtainEvidenceTaskRequest{}
	mi := &file_conan_evidence_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogObtainEvidenceTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogObtainEvidenceTaskRequest) ProtoMessage() {}

func (x *LogObtainEvidenceTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogObtainEvidenceTaskRequest.ProtoReflect.Descriptor instead.
func (*LogObtainEvidenceTaskRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{19}
}

func (x *LogObtainEvidenceTaskRequest) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *LogObtainEvidenceTaskRequest) GetSourceInfo() *EvidenceSourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *LogObtainEvidenceTaskRequest) GetLogBit() int32 {
	if x != nil {
		return x.LogBit
	}
	return 0
}

func (x *LogObtainEvidenceTaskRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *LogObtainEvidenceTaskRequest) GetExistedRaiseError() bool {
	if x != nil {
		return x.ExistedRaiseError
	}
	return false
}

// 重新提取证据请求
type RenewObtainEvidenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *RenewObtainEvidenceRequest) Reset() {
	*x = RenewObtainEvidenceRequest{}
	mi := &file_conan_evidence_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenewObtainEvidenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewObtainEvidenceRequest) ProtoMessage() {}

func (x *RenewObtainEvidenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewObtainEvidenceRequest.ProtoReflect.Descriptor instead.
func (*RenewObtainEvidenceRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{20}
}

func (x *RenewObtainEvidenceRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// 线索取证查询请求
type ClueEvidenceQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClueType     ClueType     `protobuf:"varint,1,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	ClueKey      string       `protobuf:"bytes,2,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	EvidenceType EvidenceType `protobuf:"varint,3,opt,name=evidence_type,json=evidenceType,proto3,enum=conan.EvidenceType" json:"evidence_type,omitempty"`
	IsUrlZip     bool         `protobuf:"varint,4,opt,name=is_url_zip,json=isUrlZip,proto3" json:"is_url_zip,omitempty"` // URL下载文件，是否需zip加密格式
}

func (x *ClueEvidenceQueryRequest) Reset() {
	*x = ClueEvidenceQueryRequest{}
	mi := &file_conan_evidence_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueEvidenceQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueEvidenceQueryRequest) ProtoMessage() {}

func (x *ClueEvidenceQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueEvidenceQueryRequest.ProtoReflect.Descriptor instead.
func (*ClueEvidenceQueryRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{21}
}

func (x *ClueEvidenceQueryRequest) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ClueEvidenceQueryRequest) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *ClueEvidenceQueryRequest) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_EMPTY
}

func (x *ClueEvidenceQueryRequest) GetIsUrlZip() bool {
	if x != nil {
		return x.IsUrlZip
	}
	return false
}

// 获取文件证据请求
type GetFileEvidenceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId  string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	SearchData string `protobuf:"bytes,2,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`
	Timestamp  int64  `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	IsUrlZip   bool   `protobuf:"varint,4,opt,name=is_url_zip,json=isUrlZip,proto3" json:"is_url_zip,omitempty"` // URL下载文件，是否需zip加密格式
}

func (x *GetFileEvidenceRequest) Reset() {
	*x = GetFileEvidenceRequest{}
	mi := &file_conan_evidence_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileEvidenceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileEvidenceRequest) ProtoMessage() {}

func (x *GetFileEvidenceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileEvidenceRequest.ProtoReflect.Descriptor instead.
func (*GetFileEvidenceRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{22}
}

func (x *GetFileEvidenceRequest) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *GetFileEvidenceRequest) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *GetFileEvidenceRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GetFileEvidenceRequest) GetIsUrlZip() bool {
	if x != nil {
		return x.IsUrlZip
	}
	return false
}

// 证据提取结果
type EvidenceExtractionResultResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*EvidenceExtractionResult `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *EvidenceExtractionResultResponse) Reset() {
	*x = EvidenceExtractionResultResponse{}
	mi := &file_conan_evidence_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceExtractionResultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceExtractionResultResponse) ProtoMessage() {}

func (x *EvidenceExtractionResultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceExtractionResultResponse.ProtoReflect.Descriptor instead.
func (*EvidenceExtractionResultResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{23}
}

func (x *EvidenceExtractionResultResponse) GetResults() []*EvidenceExtractionResult {
	if x != nil {
		return x.Results
	}
	return nil
}

type EvidenceExtractionResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId       int64          `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	EvidenceType EvidenceType   `protobuf:"varint,2,opt,name=evidence_type,json=evidenceType,proto3,enum=conan.EvidenceType" json:"evidence_type,omitempty"`
	Status       EvidenceStatus `protobuf:"varint,3,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	Url          string         `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	CreatedAt    int64          `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *EvidenceExtractionResult) Reset() {
	*x = EvidenceExtractionResult{}
	mi := &file_conan_evidence_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceExtractionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceExtractionResult) ProtoMessage() {}

func (x *EvidenceExtractionResult) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceExtractionResult.ProtoReflect.Descriptor instead.
func (*EvidenceExtractionResult) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{24}
}

func (x *EvidenceExtractionResult) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *EvidenceExtractionResult) GetEvidenceType() EvidenceType {
	if x != nil {
		return x.EvidenceType
	}
	return EvidenceType_EVIDENCE_TYPE_EMPTY
}

func (x *EvidenceExtractionResult) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *EvidenceExtractionResult) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *EvidenceExtractionResult) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// 证据过滤条件
type EvidenceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SearchData string `protobuf:"bytes,1,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`
	IsUrlZip   bool   `protobuf:"varint,2,opt,name=is_url_zip,json=isUrlZip,proto3" json:"is_url_zip,omitempty"` // URL下载文件，是否需zip加密格式
}

func (x *EvidenceFilter) Reset() {
	*x = EvidenceFilter{}
	mi := &file_conan_evidence_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceFilter) ProtoMessage() {}

func (x *EvidenceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceFilter.ProtoReflect.Descriptor instead.
func (*EvidenceFilter) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{25}
}

func (x *EvidenceFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *EvidenceFilter) GetIsUrlZip() bool {
	if x != nil {
		return x.IsUrlZip
	}
	return false
}

// 网络取证查询请求
type NetEvidenceQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *EvidenceFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string          `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Page    *PageRequest    `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *NetEvidenceQueryRequest) Reset() {
	*x = NetEvidenceQueryRequest{}
	mi := &file_conan_evidence_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetEvidenceQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetEvidenceQueryRequest) ProtoMessage() {}

func (x *NetEvidenceQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetEvidenceQueryRequest.ProtoReflect.Descriptor instead.
func (*NetEvidenceQueryRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{26}
}

func (x *NetEvidenceQueryRequest) GetFilter() *EvidenceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *NetEvidenceQueryRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *NetEvidenceQueryRequest) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 证据来源项
type EvidenceSourceItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceType int32  `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	SourceId   int64  `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	SourceName string `protobuf:"bytes,3,opt,name=source_name,json=sourceName,proto3" json:"source_name,omitempty"`
}

func (x *EvidenceSourceItem) Reset() {
	*x = EvidenceSourceItem{}
	mi := &file_conan_evidence_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceSourceItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceSourceItem) ProtoMessage() {}

func (x *EvidenceSourceItem) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceSourceItem.ProtoReflect.Descriptor instead.
func (*EvidenceSourceItem) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{27}
}

func (x *EvidenceSourceItem) GetSourceType() int32 {
	if x != nil {
		return x.SourceType
	}
	return 0
}

func (x *EvidenceSourceItem) GetSourceId() int64 {
	if x != nil {
		return x.SourceId
	}
	return 0
}

func (x *EvidenceSourceItem) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

// 网络取证信息
type NetEvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      int64                 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	MachineId   string                `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Status      EvidenceStatus        `protobuf:"varint,3,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	RemoteIp    string                `protobuf:"bytes,4,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`
	RemotePort  int32                 `protobuf:"varint,5,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`
	LocalIp     string                `protobuf:"bytes,6,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`
	LocalPort   int32                 `protobuf:"varint,7,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`
	Protocol    string                `protobuf:"bytes,8,opt,name=protocol,proto3" json:"protocol,omitempty"`
	Domain      string                `protobuf:"bytes,9,opt,name=domain,proto3" json:"domain,omitempty"`
	CreatedAt   int64                 `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   int64                 `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Sources     []*EvidenceSourceItem `protobuf:"bytes,12,rep,name=sources,proto3" json:"sources,omitempty"`
	DownloadUrl string                `protobuf:"bytes,13,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *NetEvidenceInfo) Reset() {
	*x = NetEvidenceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetEvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetEvidenceInfo) ProtoMessage() {}

func (x *NetEvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetEvidenceInfo.ProtoReflect.Descriptor instead.
func (*NetEvidenceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{28}
}

func (x *NetEvidenceInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *NetEvidenceInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *NetEvidenceInfo) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *NetEvidenceInfo) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *NetEvidenceInfo) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *NetEvidenceInfo) GetLocalIp() string {
	if x != nil {
		return x.LocalIp
	}
	return ""
}

func (x *NetEvidenceInfo) GetLocalPort() int32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

func (x *NetEvidenceInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *NetEvidenceInfo) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *NetEvidenceInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *NetEvidenceInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *NetEvidenceInfo) GetSources() []*EvidenceSourceItem {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *NetEvidenceInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 网络取证信息列表响应
type NetEvidenceInfoListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*NetEvidenceInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Page  *PageResponse      `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *NetEvidenceInfoListResponse) Reset() {
	*x = NetEvidenceInfoListResponse{}
	mi := &file_conan_evidence_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetEvidenceInfoListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetEvidenceInfoListResponse) ProtoMessage() {}

func (x *NetEvidenceInfoListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetEvidenceInfoListResponse.ProtoReflect.Descriptor instead.
func (*NetEvidenceInfoListResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{29}
}

func (x *NetEvidenceInfoListResponse) GetItems() []*NetEvidenceInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *NetEvidenceInfoListResponse) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 文件取证查询请求
type FileEvidenceQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *EvidenceFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string          `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Page    *PageRequest    `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *FileEvidenceQueryRequest) Reset() {
	*x = FileEvidenceQueryRequest{}
	mi := &file_conan_evidence_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileEvidenceQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEvidenceQueryRequest) ProtoMessage() {}

func (x *FileEvidenceQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEvidenceQueryRequest.ProtoReflect.Descriptor instead.
func (*FileEvidenceQueryRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{30}
}

func (x *FileEvidenceQueryRequest) GetFilter() *EvidenceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *FileEvidenceQueryRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *FileEvidenceQueryRequest) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 文件取证信息
type FileEvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      int64                 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Category    int32                 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	MachineId   string                `protobuf:"bytes,3,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Status      EvidenceStatus        `protobuf:"varint,4,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	Isolate     int32                 `protobuf:"varint,5,opt,name=isolate,proto3" json:"isolate,omitempty"` // 文件隔離狀態
	FileInfo    *EvicenceFileInfo     `protobuf:"bytes,6,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`
	CreatedAt   int64                 `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   int64                 `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Sources     []*EvidenceSourceItem `protobuf:"bytes,9,rep,name=sources,proto3" json:"sources,omitempty"`
	DownloadUrl string                `protobuf:"bytes,10,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *FileEvidenceInfo) Reset() {
	*x = FileEvidenceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileEvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEvidenceInfo) ProtoMessage() {}

func (x *FileEvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEvidenceInfo.ProtoReflect.Descriptor instead.
func (*FileEvidenceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{31}
}

func (x *FileEvidenceInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *FileEvidenceInfo) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *FileEvidenceInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *FileEvidenceInfo) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *FileEvidenceInfo) GetIsolate() int32 {
	if x != nil {
		return x.Isolate
	}
	return 0
}

func (x *FileEvidenceInfo) GetFileInfo() *EvicenceFileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *FileEvidenceInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *FileEvidenceInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *FileEvidenceInfo) GetSources() []*EvidenceSourceItem {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *FileEvidenceInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 文件取证信息列表响应
type FileEvidenceInfoListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*FileEvidenceInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Page  *PageResponse       `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *FileEvidenceInfoListResponse) Reset() {
	*x = FileEvidenceInfoListResponse{}
	mi := &file_conan_evidence_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileEvidenceInfoListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEvidenceInfoListResponse) ProtoMessage() {}

func (x *FileEvidenceInfoListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEvidenceInfoListResponse.ProtoReflect.Descriptor instead.
func (*FileEvidenceInfoListResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{32}
}

func (x *FileEvidenceInfoListResponse) GetItems() []*FileEvidenceInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *FileEvidenceInfoListResponse) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 内存dump取证查询请求
type MemDumpEvidenceQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *EvidenceFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string          `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Page    *PageRequest    `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *MemDumpEvidenceQueryRequest) Reset() {
	*x = MemDumpEvidenceQueryRequest{}
	mi := &file_conan_evidence_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemDumpEvidenceQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemDumpEvidenceQueryRequest) ProtoMessage() {}

func (x *MemDumpEvidenceQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemDumpEvidenceQueryRequest.ProtoReflect.Descriptor instead.
func (*MemDumpEvidenceQueryRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{33}
}

func (x *MemDumpEvidenceQueryRequest) GetFilter() *EvidenceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *MemDumpEvidenceQueryRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *MemDumpEvidenceQueryRequest) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 内存dump取证信息
type MemDumpEvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      int64                 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Category    int32                 `protobuf:"varint,2,opt,name=category,proto3" json:"category,omitempty"`
	MachineId   string                `protobuf:"bytes,3,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Status      EvidenceStatus        `protobuf:"varint,4,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	Pid         int32                 `protobuf:"varint,5,opt,name=pid,proto3" json:"pid,omitempty"`                       // 进程ID
	Name        string                `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                      // 进程名
	Filepath    string                `protobuf:"bytes,7,opt,name=filepath,proto3" json:"filepath,omitempty"`              // 进程文件路径
	FileMd5     string                `protobuf:"bytes,8,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"` // 文件md5
	Cmd         string                `protobuf:"bytes,9,opt,name=cmd,proto3" json:"cmd,omitempty"`                        // 进程命令行
	Owner       string                `protobuf:"bytes,10,opt,name=owner,proto3" json:"owner,omitempty"`                   // 进程创建用户
	CreatedAt   int64                 `protobuf:"varint,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   int64                 `protobuf:"varint,12,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Sources     []*EvidenceSourceItem `protobuf:"bytes,13,rep,name=sources,proto3" json:"sources,omitempty"`
	DownloadUrl string                `protobuf:"bytes,14,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *MemDumpEvidenceInfo) Reset() {
	*x = MemDumpEvidenceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemDumpEvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemDumpEvidenceInfo) ProtoMessage() {}

func (x *MemDumpEvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemDumpEvidenceInfo.ProtoReflect.Descriptor instead.
func (*MemDumpEvidenceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{34}
}

func (x *MemDumpEvidenceInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *MemDumpEvidenceInfo) GetCategory() int32 {
	if x != nil {
		return x.Category
	}
	return 0
}

func (x *MemDumpEvidenceInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *MemDumpEvidenceInfo) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *MemDumpEvidenceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *MemDumpEvidenceInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *MemDumpEvidenceInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *MemDumpEvidenceInfo) GetSources() []*EvidenceSourceItem {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *MemDumpEvidenceInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 内存dump取证信息列表响应
type MemDumpEvidenceInfoListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*MemDumpEvidenceInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Page  *PageResponse          `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *MemDumpEvidenceInfoListResponse) Reset() {
	*x = MemDumpEvidenceInfoListResponse{}
	mi := &file_conan_evidence_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemDumpEvidenceInfoListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemDumpEvidenceInfoListResponse) ProtoMessage() {}

func (x *MemDumpEvidenceInfoListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemDumpEvidenceInfoListResponse.ProtoReflect.Descriptor instead.
func (*MemDumpEvidenceInfoListResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{35}
}

func (x *MemDumpEvidenceInfoListResponse) GetItems() []*MemDumpEvidenceInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *MemDumpEvidenceInfoListResponse) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 日志取证过滤条件
type LogEvidenceFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIdSlice []string `protobuf:"bytes,1,rep,name=machine_id_slice,json=machineIdSlice,proto3" json:"machine_id_slice,omitempty"`
	LogType        int32    `protobuf:"varint,2,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"` // 日志类型：1. 系统, 2. 安全，4. setup, 8. 应用
}

func (x *LogEvidenceFilter) Reset() {
	*x = LogEvidenceFilter{}
	mi := &file_conan_evidence_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEvidenceFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEvidenceFilter) ProtoMessage() {}

func (x *LogEvidenceFilter) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEvidenceFilter.ProtoReflect.Descriptor instead.
func (*LogEvidenceFilter) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{36}
}

func (x *LogEvidenceFilter) GetMachineIdSlice() []string {
	if x != nil {
		return x.MachineIdSlice
	}
	return nil
}

func (x *LogEvidenceFilter) GetLogType() int32 {
	if x != nil {
		return x.LogType
	}
	return 0
}

// 日志取证查询请求
type LogEvidenceQueryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter  *LogEvidenceFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	OrderBy string             `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	Page    *PageRequest       `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *LogEvidenceQueryRequest) Reset() {
	*x = LogEvidenceQueryRequest{}
	mi := &file_conan_evidence_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEvidenceQueryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEvidenceQueryRequest) ProtoMessage() {}

func (x *LogEvidenceQueryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEvidenceQueryRequest.ProtoReflect.Descriptor instead.
func (*LogEvidenceQueryRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{37}
}

func (x *LogEvidenceQueryRequest) GetFilter() *LogEvidenceFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *LogEvidenceQueryRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *LogEvidenceQueryRequest) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 日志取证信息
type LogEvidenceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId      int64                 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	MachineId   string                `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Status      EvidenceStatus        `protobuf:"varint,3,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	LogBit      int32                 `protobuf:"varint,4,opt,name=log_bit,json=logBit,proto3" json:"log_bit,omitempty"`
	CreatedAt   int64                 `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt   int64                 `protobuf:"varint,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Sources     []*EvidenceSourceItem `protobuf:"bytes,7,rep,name=sources,proto3" json:"sources,omitempty"`
	DownloadUrl string                `protobuf:"bytes,8,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
}

func (x *LogEvidenceInfo) Reset() {
	*x = LogEvidenceInfo{}
	mi := &file_conan_evidence_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEvidenceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEvidenceInfo) ProtoMessage() {}

func (x *LogEvidenceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEvidenceInfo.ProtoReflect.Descriptor instead.
func (*LogEvidenceInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{38}
}

func (x *LogEvidenceInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *LogEvidenceInfo) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *LogEvidenceInfo) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *LogEvidenceInfo) GetLogBit() int32 {
	if x != nil {
		return x.LogBit
	}
	return 0
}

func (x *LogEvidenceInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *LogEvidenceInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *LogEvidenceInfo) GetSources() []*EvidenceSourceItem {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *LogEvidenceInfo) GetDownloadUrl() string {
	if x != nil {
		return x.DownloadUrl
	}
	return ""
}

// 日志取证信息列表响应
type LogEvidenceInfoListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*LogEvidenceInfo `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Page  *PageResponse      `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *LogEvidenceInfoListResponse) Reset() {
	*x = LogEvidenceInfoListResponse{}
	mi := &file_conan_evidence_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEvidenceInfoListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEvidenceInfoListResponse) ProtoMessage() {}

func (x *LogEvidenceInfoListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEvidenceInfoListResponse.ProtoReflect.Descriptor instead.
func (*LogEvidenceInfoListResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{39}
}

func (x *LogEvidenceInfoListResponse) GetItems() []*LogEvidenceInfo {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *LogEvidenceInfoListResponse) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 获取取证结果信息请求
type GetEvidenceResultInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *GetEvidenceResultInfoRequest) Reset() {
	*x = GetEvidenceResultInfoRequest{}
	mi := &file_conan_evidence_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEvidenceResultInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvidenceResultInfoRequest) ProtoMessage() {}

func (x *GetEvidenceResultInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvidenceResultInfoRequest.ProtoReflect.Descriptor instead.
func (*GetEvidenceResultInfoRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{40}
}

func (x *GetEvidenceResultInfoRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// 终端信息
type TerminalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HostId   string `protobuf:"bytes,1,opt,name=host_id,json=hostId,proto3" json:"host_id,omitempty"`
	Hostname string `protobuf:"bytes,2,opt,name=hostname,proto3" json:"hostname,omitempty"`
	Ip       string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Os       string `protobuf:"bytes,4,opt,name=os,proto3" json:"os,omitempty"`
	Group    string `protobuf:"bytes,5,opt,name=group,proto3" json:"group,omitempty"`
}

func (x *TerminalInfo) Reset() {
	*x = TerminalInfo{}
	mi := &file_conan_evidence_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalInfo) ProtoMessage() {}

func (x *TerminalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalInfo.ProtoReflect.Descriptor instead.
func (*TerminalInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{41}
}

func (x *TerminalInfo) GetHostId() string {
	if x != nil {
		return x.HostId
	}
	return ""
}

func (x *TerminalInfo) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *TerminalInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *TerminalInfo) GetOs() string {
	if x != nil {
		return x.Os
	}
	return ""
}

func (x *TerminalInfo) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

// 进程信息
type EvidenceProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid             int64  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                                                  // 进程ID
	StartTime       int64  `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                     // 进程创建时间
	Ppid            int64  `protobuf:"varint,3,opt,name=ppid,proto3" json:"ppid,omitempty"`                                                // 父进程ID
	ParentStartTime int64  `protobuf:"varint,4,opt,name=parent_start_time,json=parentStartTime,proto3" json:"parent_start_time,omitempty"` // 进程创建时间
	ProcessName     string `protobuf:"bytes,5,opt,name=process_name,json=processName,proto3" json:"process_name,omitempty"`                // 进程名
	Cmd             string `protobuf:"bytes,6,opt,name=cmd,proto3" json:"cmd,omitempty"`                                                   // 进程命令行
	Filepath        string `protobuf:"bytes,7,opt,name=filepath,proto3" json:"filepath,omitempty"`                                         // 进程文件路径
	FileMd5         string `protobuf:"bytes,8,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`                            // 文件md5
	FileSha256      string `protobuf:"bytes,9,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`                   // 文件sha256
	FileSize        int64  `protobuf:"varint,10,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                       // 进程文件大小
	FileCtime       int64  `protobuf:"varint,11,opt,name=file_ctime,json=fileCtime,proto3" json:"file_ctime,omitempty"`                    // 进程文件创建时间
	FileMtime       int64  `protobuf:"varint,12,opt,name=file_mtime,json=fileMtime,proto3" json:"file_mtime,omitempty"`                    // 进程文件修改时间
	FileAtime       int64  `protobuf:"varint,13,opt,name=file_atime,json=fileAtime,proto3" json:"file_atime,omitempty"`                    // 进程文件最后访问时间
	Owner           string `protobuf:"bytes,14,opt,name=owner,proto3" json:"owner,omitempty"`                                              // 进程创建用户
}

func (x *EvidenceProcessInfo) Reset() {
	*x = EvidenceProcessInfo{}
	mi := &file_conan_evidence_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceProcessInfo) ProtoMessage() {}

func (x *EvidenceProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceProcessInfo.ProtoReflect.Descriptor instead.
func (*EvidenceProcessInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{42}
}

func (x *EvidenceProcessInfo) GetPid() int64 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *EvidenceProcessInfo) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *EvidenceProcessInfo) GetPpid() int64 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *EvidenceProcessInfo) GetParentStartTime() int64 {
	if x != nil {
		return x.ParentStartTime
	}
	return 0
}

func (x *EvidenceProcessInfo) GetProcessName() string {
	if x != nil {
		return x.ProcessName
	}
	return ""
}

func (x *EvidenceProcessInfo) GetCmd() string {
	if x != nil {
		return x.Cmd
	}
	return ""
}

func (x *EvidenceProcessInfo) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *EvidenceProcessInfo) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *EvidenceProcessInfo) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *EvidenceProcessInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *EvidenceProcessInfo) GetFileCtime() int64 {
	if x != nil {
		return x.FileCtime
	}
	return 0
}

func (x *EvidenceProcessInfo) GetFileMtime() int64 {
	if x != nil {
		return x.FileMtime
	}
	return 0
}

func (x *EvidenceProcessInfo) GetFileAtime() int64 {
	if x != nil {
		return x.FileAtime
	}
	return 0
}

func (x *EvidenceProcessInfo) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

// 文件证据内容
type FileEvidenceContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filepath string `protobuf:"bytes,1,opt,name=filepath,proto3" json:"filepath,omitempty"`
	Url      string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5Sum   string `protobuf:"bytes,3,opt,name=md5sum,proto3" json:"md5sum,omitempty"`
}

func (x *FileEvidenceContent) Reset() {
	*x = FileEvidenceContent{}
	mi := &file_conan_evidence_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileEvidenceContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileEvidenceContent) ProtoMessage() {}

func (x *FileEvidenceContent) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileEvidenceContent.ProtoReflect.Descriptor instead.
func (*FileEvidenceContent) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{43}
}

func (x *FileEvidenceContent) GetFilepath() string {
	if x != nil {
		return x.Filepath
	}
	return ""
}

func (x *FileEvidenceContent) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FileEvidenceContent) GetMd5Sum() string {
	if x != nil {
		return x.Md5Sum
	}
	return ""
}

// 通用证据内容
type CommonEvidenceContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filename string `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`
	Url      string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *CommonEvidenceContent) Reset() {
	*x = CommonEvidenceContent{}
	mi := &file_conan_evidence_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonEvidenceContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonEvidenceContent) ProtoMessage() {}

func (x *CommonEvidenceContent) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonEvidenceContent.ProtoReflect.Descriptor instead.
func (*CommonEvidenceContent) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{44}
}

func (x *CommonEvidenceContent) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *CommonEvidenceContent) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// 日志证据内容
type LogEvidenceContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Common *CommonEvidenceContent `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	LogBit int32                  `protobuf:"varint,2,opt,name=log_bit,json=logBit,proto3" json:"log_bit,omitempty"`
}

func (x *LogEvidenceContent) Reset() {
	*x = LogEvidenceContent{}
	mi := &file_conan_evidence_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEvidenceContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEvidenceContent) ProtoMessage() {}

func (x *LogEvidenceContent) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEvidenceContent.ProtoReflect.Descriptor instead.
func (*LogEvidenceContent) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{45}
}

func (x *LogEvidenceContent) GetCommon() *CommonEvidenceContent {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *LogEvidenceContent) GetLogBit() int32 {
	if x != nil {
		return x.LogBit
	}
	return 0
}

// 取证结果信息
type EvidenceResultInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId           int64                  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	Status           EvidenceStatus         `protobuf:"varint,2,opt,name=status,proto3,enum=conan.EvidenceStatus" json:"status,omitempty"`
	ExpiredAt        int64                  `protobuf:"varint,3,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	ClueKey          string                 `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	TerminalInfo     *TerminalInfo          `protobuf:"bytes,5,opt,name=terminal_info,json=terminalInfo,proto3" json:"terminal_info,omitempty"`
	ProcessChainInfo []*EvidenceProcessInfo `protobuf:"bytes,6,rep,name=process_chain_info,json=processChainInfo,proto3" json:"process_chain_info,omitempty"`
	File             *FileEvidenceContent   `protobuf:"bytes,7,opt,name=file,proto3" json:"file,omitempty"`
	ScriptFile       *FileEvidenceContent   `protobuf:"bytes,8,opt,name=script_file,json=scriptFile,proto3" json:"script_file,omitempty"`
	MemoryDump       *CommonEvidenceContent `protobuf:"bytes,9,opt,name=memory_dump,json=memoryDump,proto3" json:"memory_dump,omitempty"`
	MiniMemoryDump   *CommonEvidenceContent `protobuf:"bytes,10,opt,name=mini_memory_dump,json=miniMemoryDump,proto3" json:"mini_memory_dump,omitempty"`
	MemorySegment    *CommonEvidenceContent `protobuf:"bytes,11,opt,name=memory_segment,json=memorySegment,proto3" json:"memory_segment,omitempty"`
	LogFile          *LogEvidenceContent    `protobuf:"bytes,12,opt,name=log_file,json=logFile,proto3" json:"log_file,omitempty"`
}

func (x *EvidenceResultInfo) Reset() {
	*x = EvidenceResultInfo{}
	mi := &file_conan_evidence_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceResultInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceResultInfo) ProtoMessage() {}

func (x *EvidenceResultInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceResultInfo.ProtoReflect.Descriptor instead.
func (*EvidenceResultInfo) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{46}
}

func (x *EvidenceResultInfo) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *EvidenceResultInfo) GetStatus() EvidenceStatus {
	if x != nil {
		return x.Status
	}
	return EvidenceStatus_EVIDENCE_STATUS_UNSPECIFIED
}

func (x *EvidenceResultInfo) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *EvidenceResultInfo) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *EvidenceResultInfo) GetTerminalInfo() *TerminalInfo {
	if x != nil {
		return x.TerminalInfo
	}
	return nil
}

func (x *EvidenceResultInfo) GetProcessChainInfo() []*EvidenceProcessInfo {
	if x != nil {
		return x.ProcessChainInfo
	}
	return nil
}

func (x *EvidenceResultInfo) GetFile() *FileEvidenceContent {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *EvidenceResultInfo) GetScriptFile() *FileEvidenceContent {
	if x != nil {
		return x.ScriptFile
	}
	return nil
}

func (x *EvidenceResultInfo) GetMemoryDump() *CommonEvidenceContent {
	if x != nil {
		return x.MemoryDump
	}
	return nil
}

func (x *EvidenceResultInfo) GetMiniMemoryDump() *CommonEvidenceContent {
	if x != nil {
		return x.MiniMemoryDump
	}
	return nil
}

func (x *EvidenceResultInfo) GetMemorySegment() *CommonEvidenceContent {
	if x != nil {
		return x.MemorySegment
	}
	return nil
}

func (x *EvidenceResultInfo) GetLogFile() *LogEvidenceContent {
	if x != nil {
		return x.LogFile
	}
	return nil
}

// 批量获取取证结果信息请求
type BatchGetEvidenceResultInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskIds []int64 `protobuf:"varint,1,rep,packed,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"`
}

func (x *BatchGetEvidenceResultInfoRequest) Reset() {
	*x = BatchGetEvidenceResultInfoRequest{}
	mi := &file_conan_evidence_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetEvidenceResultInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetEvidenceResultInfoRequest) ProtoMessage() {}

func (x *BatchGetEvidenceResultInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetEvidenceResultInfoRequest.ProtoReflect.Descriptor instead.
func (*BatchGetEvidenceResultInfoRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{47}
}

func (x *BatchGetEvidenceResultInfoRequest) GetTaskIds() []int64 {
	if x != nil {
		return x.TaskIds
	}
	return nil
}

// 批量取证结果信息响应
type BatchEvidenceResultInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Results []*EvidenceResultInfo `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
}

func (x *BatchEvidenceResultInfoResponse) Reset() {
	*x = BatchEvidenceResultInfoResponse{}
	mi := &file_conan_evidence_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchEvidenceResultInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchEvidenceResultInfoResponse) ProtoMessage() {}

func (x *BatchEvidenceResultInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchEvidenceResultInfoResponse.ProtoReflect.Descriptor instead.
func (*BatchEvidenceResultInfoResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{48}
}

func (x *BatchEvidenceResultInfoResponse) GetResults() []*EvidenceResultInfo {
	if x != nil {
		return x.Results
	}
	return nil
}

// 获取取证存储信息请求
type GetEvidenceStorageInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *GetEvidenceStorageInfoRequest) Reset() {
	*x = GetEvidenceStorageInfoRequest{}
	mi := &file_conan_evidence_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEvidenceStorageInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvidenceStorageInfoRequest) ProtoMessage() {}

func (x *GetEvidenceStorageInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvidenceStorageInfoRequest.ProtoReflect.Descriptor instead.
func (*GetEvidenceStorageInfoRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{49}
}

func (x *GetEvidenceStorageInfoRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// 取证存储信息
type EvidenceStorage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mode      EvidenceStorageMode `protobuf:"varint,1,opt,name=mode,proto3,enum=conan.EvidenceStorageMode" json:"mode,omitempty"`
	Content   string              `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	Size      int64               `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	Md5       string              `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	CreatedAt int64               `protobuf:"varint,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *EvidenceStorage) Reset() {
	*x = EvidenceStorage{}
	mi := &file_conan_evidence_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceStorage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceStorage) ProtoMessage() {}

func (x *EvidenceStorage) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceStorage.ProtoReflect.Descriptor instead.
func (*EvidenceStorage) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{50}
}

func (x *EvidenceStorage) GetMode() EvidenceStorageMode {
	if x != nil {
		return x.Mode
	}
	return EvidenceStorageMode_EVIDENCE_STORAGE_MODE_UNDEFINED
}

func (x *EvidenceStorage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *EvidenceStorage) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *EvidenceStorage) GetMd5() string {
	if x != nil {
		return x.Md5
	}
	return ""
}

func (x *EvidenceStorage) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// 获取已获取证据计数请求
type GetObtainedEvidenceCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DateRange []int64 `protobuf:"varint,1,rep,packed,name=date_range,json=dateRange,proto3" json:"date_range,omitempty"`
}

func (x *GetObtainedEvidenceCountRequest) Reset() {
	*x = GetObtainedEvidenceCountRequest{}
	mi := &file_conan_evidence_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetObtainedEvidenceCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetObtainedEvidenceCountRequest) ProtoMessage() {}

func (x *GetObtainedEvidenceCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetObtainedEvidenceCountRequest.ProtoReflect.Descriptor instead.
func (*GetObtainedEvidenceCountRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{51}
}

func (x *GetObtainedEvidenceCountRequest) GetDateRange() []int64 {
	if x != nil {
		return x.DateRange
	}
	return nil
}

// 已获取证据计数响应
type ObtainEvidenceCountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total             int64 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	EvidenceNetCount  int64 `protobuf:"varint,2,opt,name=evidence_net_count,json=evidenceNetCount,proto3" json:"evidence_net_count,omitempty"`
	EvidenceFileCount int64 `protobuf:"varint,3,opt,name=evidence_file_count,json=evidenceFileCount,proto3" json:"evidence_file_count,omitempty"`
	EvidenceDumpCount int64 `protobuf:"varint,4,opt,name=evidence_dump_count,json=evidenceDumpCount,proto3" json:"evidence_dump_count,omitempty"`
}

func (x *ObtainEvidenceCountResponse) Reset() {
	*x = ObtainEvidenceCountResponse{}
	mi := &file_conan_evidence_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ObtainEvidenceCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ObtainEvidenceCountResponse) ProtoMessage() {}

func (x *ObtainEvidenceCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ObtainEvidenceCountResponse.ProtoReflect.Descriptor instead.
func (*ObtainEvidenceCountResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{52}
}

func (x *ObtainEvidenceCountResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ObtainEvidenceCountResponse) GetEvidenceNetCount() int64 {
	if x != nil {
		return x.EvidenceNetCount
	}
	return 0
}

func (x *ObtainEvidenceCountResponse) GetEvidenceFileCount() int64 {
	if x != nil {
		return x.EvidenceFileCount
	}
	return 0
}

func (x *ObtainEvidenceCountResponse) GetEvidenceDumpCount() int64 {
	if x != nil {
		return x.EvidenceDumpCount
	}
	return 0
}

// 获取取证来源配置信息请求
type GetEvidenceSourceConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *GetEvidenceSourceConfigRequest) Reset() {
	*x = GetEvidenceSourceConfigRequest{}
	mi := &file_conan_evidence_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEvidenceSourceConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEvidenceSourceConfigRequest) ProtoMessage() {}

func (x *GetEvidenceSourceConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEvidenceSourceConfigRequest.ProtoReflect.Descriptor instead.
func (*GetEvidenceSourceConfigRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{53}
}

func (x *GetEvidenceSourceConfigRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// 取证来源配置信息
type EvidenceSourceConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform     string            `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	ReturnWay    EvidenceReturnWay `protobuf:"varint,2,opt,name=return_way,json=returnWay,proto3,enum=conan.EvidenceReturnWay" json:"return_way,omitempty"`
	CallbackPath string            `protobuf:"bytes,3,opt,name=callback_path,json=callbackPath,proto3" json:"callback_path,omitempty"` // 取证结果回调路径,"/xxx/abc""
	Description  string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *EvidenceSourceConfigResponse) Reset() {
	*x = EvidenceSourceConfigResponse{}
	mi := &file_conan_evidence_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceSourceConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceSourceConfigResponse) ProtoMessage() {}

func (x *EvidenceSourceConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceSourceConfigResponse.ProtoReflect.Descriptor instead.
func (*EvidenceSourceConfigResponse) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{54}
}

func (x *EvidenceSourceConfigResponse) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *EvidenceSourceConfigResponse) GetReturnWay() EvidenceReturnWay {
	if x != nil {
		return x.ReturnWay
	}
	return EvidenceReturnWay_EVIDENCE_RETURN_WAY_UNSPECIFIED
}

func (x *EvidenceSourceConfigResponse) GetCallbackPath() string {
	if x != nil {
		return x.CallbackPath
	}
	return ""
}

func (x *EvidenceSourceConfigResponse) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 添加取证源配置请求
type EvidenceSourceConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform     string            `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	ReturnWay    EvidenceReturnWay `protobuf:"varint,2,opt,name=return_way,json=returnWay,proto3,enum=conan.EvidenceReturnWay" json:"return_way,omitempty"`
	CallbackPath string            `protobuf:"bytes,3,opt,name=callback_path,json=callbackPath,proto3" json:"callback_path,omitempty"` // 取证结果回调路径,"/xxx/abc""
	Description  string            `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *EvidenceSourceConfigRequest) Reset() {
	*x = EvidenceSourceConfigRequest{}
	mi := &file_conan_evidence_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EvidenceSourceConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvidenceSourceConfigRequest) ProtoMessage() {}

func (x *EvidenceSourceConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvidenceSourceConfigRequest.ProtoReflect.Descriptor instead.
func (*EvidenceSourceConfigRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{55}
}

func (x *EvidenceSourceConfigRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *EvidenceSourceConfigRequest) GetReturnWay() EvidenceReturnWay {
	if x != nil {
		return x.ReturnWay
	}
	return EvidenceReturnWay_EVIDENCE_RETURN_WAY_UNSPECIFIED
}

func (x *EvidenceSourceConfigRequest) GetCallbackPath() string {
	if x != nil {
		return x.CallbackPath
	}
	return ""
}

func (x *EvidenceSourceConfigRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// 删除取证源配置请求
type DeleteEvidenceSourceConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *DeleteEvidenceSourceConfigRequest) Reset() {
	*x = DeleteEvidenceSourceConfigRequest{}
	mi := &file_conan_evidence_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteEvidenceSourceConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEvidenceSourceConfigRequest) ProtoMessage() {}

func (x *DeleteEvidenceSourceConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEvidenceSourceConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteEvidenceSourceConfigRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{56}
}

func (x *DeleteEvidenceSourceConfigRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

// 更新取证来源配置信息(自动取证线索子类型)请求
type UpdateAutoObtainEvidenceClueSubTypeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Platform     string  `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	SubTypeSlice []int32 `protobuf:"varint,2,rep,packed,name=sub_type_slice,json=subTypeSlice,proto3" json:"sub_type_slice,omitempty"`
}

func (x *UpdateAutoObtainEvidenceClueSubTypeRequest) Reset() {
	*x = UpdateAutoObtainEvidenceClueSubTypeRequest{}
	mi := &file_conan_evidence_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAutoObtainEvidenceClueSubTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAutoObtainEvidenceClueSubTypeRequest) ProtoMessage() {}

func (x *UpdateAutoObtainEvidenceClueSubTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_evidence_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAutoObtainEvidenceClueSubTypeRequest.ProtoReflect.Descriptor instead.
func (*UpdateAutoObtainEvidenceClueSubTypeRequest) Descriptor() ([]byte, []int) {
	return file_conan_evidence_proto_rawDescGZIP(), []int{57}
}

func (x *UpdateAutoObtainEvidenceClueSubTypeRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *UpdateAutoObtainEvidenceClueSubTypeRequest) GetSubTypeSlice() []int32 {
	if x != nil {
		return x.SubTypeSlice
	}
	return nil
}

var File_conan_evidence_proto protoreflect.FileDescriptor

var file_conan_evidence_proto_rawDesc = []byte{
	0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91,
	0x01, 0x0a, 0x1c, 0x43, 0x6c, 0x75, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x38, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xc0, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x61, 0x73, 0x73,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65,
	0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x65, 0x6d, 0x70, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x65, 0x6d, 0x70, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x74, 0x22, 0x61, 0x0a, 0x2c, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7c, 0x0a, 0x18, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2d, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x22, 0x74, 0x0a, 0x0a, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x84, 0x03, 0x0a,
	0x23, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0b, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x75, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f,
	0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x61, 0x77, 0x46, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x12, 0x31, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x0b, 0x66, 0x6f, 0x72, 0x6d, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x66, 0x6f, 0x72, 0x6d, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x1a, 0x3d, 0x0a, 0x0f, 0x46, 0x6f, 0x72, 0x6d, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x2c, 0x0a, 0x16, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x72, 0x0a, 0x12, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x48, 0x0a, 0x0c, 0x48, 0x6f, 0x73, 0x74, 0x43, 0x6c, 0x75,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x22,
	0xcb, 0x01, 0x0a, 0x1d, 0x43, 0x6c, 0x75, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x6c, 0x75, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8a, 0x02,
	0x0a, 0x10, 0x45, 0x76, 0x69, 0x63, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x61, 0x77, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x31, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x14, 0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x22, 0xe5, 0x01, 0x0a, 0x0c, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x65,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61,
	0x74, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x32,
	0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x22, 0x9b, 0x01, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x72, 0x61, 0x77, 0x46, 0x69, 0x6c, 0x65,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x14,
	0x0a, 0x05, 0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x61,
	0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x74, 0x69, 0x6d, 0x65,
	0x22, 0xba, 0x02, 0x0a, 0x1a, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x39, 0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x48, 0x6f, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x68, 0x6f,
	0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4b, 0x65, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x72,
	0x61, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x11, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x52, 0x61, 0x69, 0x73, 0x65, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x38, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x8a, 0x01,
	0x0a, 0x22, 0x42, 0x61, 0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69,
	0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x21, 0x0a, 0x1f, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x97, 0x01,
	0x0a, 0x11, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x73, 0x74, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09,
	0x64, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x72, 0x63,
	0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x72, 0x63, 0x49, 0x70,
	0x12, 0x19, 0x0a, 0x08, 0x73, 0x72, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x07, 0x73, 0x72, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x77, 0x0a, 0x14, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x4f, 0x75, 0x74, 0x52, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x41, 0x0a, 0x0f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x22, 0x84, 0x02, 0x0a, 0x19, 0x4e, 0x65, 0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39,
	0x0a, 0x0e, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x48,
	0x6f, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x68, 0x6f, 0x73,
	0x74, 0x43, 0x6c, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x40, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4f, 0x75, 0x74,
	0x52, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78, 0x69, 0x73, 0x74,
	0x65, 0x64, 0x5f, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64, 0x52, 0x61, 0x69,
	0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xe0, 0x01, 0x0a, 0x1c, 0x4c, 0x6f, 0x67, 0x4f,
	0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x67, 0x5f, 0x62, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c, 0x6f, 0x67, 0x42, 0x69, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x78,
	0x69, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x61, 0x69, 0x73, 0x65, 0x5f, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x65, 0x78, 0x69, 0x73, 0x74, 0x65, 0x64,
	0x52, 0x61, 0x69, 0x73, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x35, 0x0a, 0x1a, 0x52, 0x65,
	0x6e, 0x65, 0x77, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x22, 0xbb, 0x01, 0x0a, 0x18, 0x43, 0x6c, 0x75, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c,
	0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x5f, 0x7a, 0x69, 0x70, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x72, 0x6c, 0x5a, 0x69, 0x70, 0x22,
	0x94, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61,
	0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x7a, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x55, 0x72, 0x6c, 0x5a, 0x69, 0x70, 0x22, 0x5d, 0x0a, 0x20, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x07, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x07, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x18, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x0d, 0x65,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x4f, 0x0a, 0x0e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x7a, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x55, 0x72, 0x6c, 0x5a, 0x69, 0x70, 0x22, 0x8b, 0x01, 0x0a, 0x17, 0x4e, 0x65, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x26, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x73, 0x0a, 0x12, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xba, 0x03, 0x0a, 0x0f, 0x4e, 0x65,
	0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a,
	0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69,
	0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49,
	0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x49, 0x70, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61,
	0x69, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x33,
	0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x74, 0x0a, 0x1b, 0x4e, 0x65, 0x74, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x8c, 0x01, 0x0a,
	0x18, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2d, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x42, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xfb, 0x02, 0x0a, 0x10,
	0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x63, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x76, 0x0a, 0x1c, 0x46, 0x69, 0x6c,
	0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2d, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x22, 0x8f, 0x01, 0x0a, 0x1b, 0x4d, 0x65, 0x6d, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2d, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0xb3, 0x03, 0x0a, 0x13, 0x4d, 0x65, 0x6d, 0x44, 0x75, 0x6d, 0x70, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x6d, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x6d, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x7c, 0x0a, 0x1f, 0x4d, 0x65, 0x6d,
	0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x27,
	0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x58, 0x0a, 0x11, 0x4c, 0x6f, 0x67, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49,
	0x64, 0x53, 0x6c, 0x69, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x8e, 0x01, 0x0a, 0x17, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0xa7, 0x02, 0x0a, 0x0f, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x2d,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a,
	0x07, 0x6c, 0x6f, 0x67, 0x5f, 0x62, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x6c, 0x6f, 0x67, 0x42, 0x69, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x07, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f, 0x77,
	0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x22, 0x74, 0x0a, 0x1b,
	0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2c, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x37, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x79, 0x0a, 0x0c, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07, 0x68,
	0x6f, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x68, 0x6f,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70,
	0x12, 0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x6f, 0x73,
	0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xa3, 0x03, 0x0a, 0x13, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x70, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70,
	0x70, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x6d, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x63, 0x6d, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68,
	0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x43, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6d, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x4d, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x61, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x66, 0x69, 0x6c,
	0x65, 0x41, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x22, 0x5b, 0x0a, 0x13,
	0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x70, 0x61, 0x74, 0x68, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6d, 0x64, 0x35, 0x73, 0x75, 0x6d, 0x22, 0x45, 0x0a, 0x15, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x22, 0x63, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07,
	0x6c, 0x6f, 0x67, 0x5f, 0x62, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6c,
	0x6f, 0x67, 0x42, 0x69, 0x74, 0x22, 0x89, 0x05, 0x0a, 0x12, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x38,
	0x0a, 0x0d, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x74, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x12, 0x70, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x10, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x04, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x66, 0x69,
	0x6c, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x66, 0x69, 0x6c,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x3d, 0x0a, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x75, 0x6d, 0x70, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x44, 0x75, 0x6d, 0x70, 0x12, 0x46,
	0x0a, 0x10, 0x6d, 0x69, 0x6e, 0x69, 0x5f, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x64, 0x75,
	0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x69, 0x4d, 0x65, 0x6d, 0x6f,
	0x72, 0x79, 0x44, 0x75, 0x6d, 0x70, 0x12, 0x43, 0x0a, 0x0e, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x5f, 0x73, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x6d, 0x65,
	0x6d, 0x6f, 0x72, 0x79, 0x53, 0x65, 0x67, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x6c,
	0x6f, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x46, 0x69, 0x6c,
	0x65, 0x22, 0x3e, 0x0a, 0x21, 0x42, 0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x73, 0x22, 0x56, 0x0a, 0x1f, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0x38, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x0f, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x40, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4f, 0x62, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x64, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x1b, 0x4f, 0x62, 0x74,
	0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2c,
	0x0a, 0x12, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x65, 0x74, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x65, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x4e, 0x65, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x65, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x0a, 0x13,
	0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x75, 0x6d, 0x70, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x65, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x44, 0x75, 0x6d, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x3c, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22, 0xba, 0x01, 0x0a, 0x1c, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x37, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x77, 0x61, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x57, 0x61, 0x79, 0x52, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x57, 0x61, 0x79,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x50, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb9, 0x01, 0x0a, 0x1b, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66,
	0x6f, 0x72, 0x6d, 0x12, 0x37, 0x0a, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x77, 0x61,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x57, 0x61,
	0x79, 0x52, 0x09, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x57, 0x61, 0x79, 0x12, 0x23, 0x0a, 0x0d,
	0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x3f, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x22, 0x6e, 0x0a, 0x2a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75,
	0x74, 0x6f, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x43, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12, 0x24,
	0x0a, 0x0e, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x73, 0x6c, 0x69, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0c, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x6c, 0x69, 0x63, 0x65, 0x2a, 0xfb, 0x01, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x52,
	0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x52,
	0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x52, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a,
	0x19, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x21, 0x0a, 0x1d,
	0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x4e, 0x49, 0x53, 0x48, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x20, 0x0a, 0x1c, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10,
	0x05, 0x12, 0x1f, 0x0a, 0x1b, 0x52, 0x45, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x41,
	0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c,
	0x10, 0x06, 0x2a, 0x87, 0x04, 0x0a, 0x0e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x50, 0x41, 0x52,
	0x45, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03,
	0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x4c, 0x49, 0x53, 0x54,
	0x10, 0x04, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x4f,
	0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x45, 0x4e, 0x44,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x56, 0x49,
	0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x54, 0x49, 0x4d,
	0x45, 0x4f, 0x55, 0x54, 0x10, 0x08, 0x12, 0x27, 0x0a, 0x23, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x53,
	0x49, 0x5a, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45, 0x10, 0x09, 0x12,
	0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e,
	0x44, 0x10, 0x0a, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x0b, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53,
	0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x0d, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x4c, 0x4f,
	0x41, 0x44, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x63, 0x2a, 0x9b, 0x01, 0x0a,
	0x12, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x44, 0x45,
	0x46, 0x49, 0x4e, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x56, 0x49, 0x44, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x55, 0x54, 0x4f, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56, 0x49, 0x44, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x03, 0x2a, 0xaa, 0x02, 0x0a, 0x0c, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x45,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x50,
	0x54, 0x59, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x43, 0x48,
	0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x12, 0x1d, 0x0a,
	0x19, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19,
	0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45,
	0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x44, 0x55, 0x4d, 0x50, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x45,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x47,
	0x10, 0x10, 0x12, 0x22, 0x0a, 0x1e, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x5f, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f,
	0x44, 0x55, 0x4d, 0x50, 0x10, 0x20, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x53,
	0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x40, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x4c, 0x4c, 0x10, 0x5f, 0x12,
	0x16, 0x0a, 0x12, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x41, 0x55, 0x54, 0x4f, 0x10, 0x47, 0x2a, 0x7d, 0x0a, 0x13, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x23,
	0x0a, 0x1f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x52, 0x41,
	0x47, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f,
	0x53, 0x54, 0x4f, 0x52, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x11, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x57, 0x61, 0x79, 0x12, 0x23, 0x0a, 0x1f, 0x45,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x57,
	0x41, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x54,
	0x55, 0x52, 0x4e, 0x5f, 0x57, 0x41, 0x59, 0x5f, 0x50, 0x4f, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45,
	0x54, 0x55, 0x52, 0x4e, 0x5f, 0x57, 0x41, 0x59, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x42, 0x41, 0x43,
	0x4b, 0x10, 0x02, 0x32, 0xf1, 0x11, 0x0a, 0x0f, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x7a, 0x0a, 0x24, 0x4f, 0x62, 0x74, 0x61, 0x69,
	0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x12,
	0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x50, 0x61, 0x73,
	0x73, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x41, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x1c, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4e, 0x6f, 0x74,
	0x69, 0x66, 0x79, 0x12, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x65, 0x64, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x51, 0x0a, 0x18, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x66, 0x0a, 0x19, 0x41, 0x64,
	0x64, 0x43, 0x6c, 0x75, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x43, 0x6c, 0x75, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x5f, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74,
	0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e,
	0x66, 0x6f, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69,
	0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x73, 0x0a, 0x1e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x41, 0x64, 0x64, 0x46,
	0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x46, 0x69,
	0x6c, 0x65, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x4e,
	0x65, 0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74,
	0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f,
	0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x60, 0x0a, 0x18, 0x41, 0x64, 0x64, 0x4c, 0x6f,
	0x67, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x4f,
	0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x13, 0x52, 0x65, 0x6e,
	0x65, 0x77, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x4f, 0x62,
	0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x5a, 0x0a, 0x14,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4e, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4e, 0x65, 0x74,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5d, 0x0a, 0x15, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75, 0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x69, 0x0a, 0x1b, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d,
	0x65, 0x6d, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x44, 0x75, 0x6d, 0x70, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x57,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x6e, 0x0a, 0x1a, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x47, 0x65, 0x74, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x56, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12,
	0x66, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x64, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x62, 0x74, 0x61,
	0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55,
	0x0a, 0x17, 0x41, 0x64, 0x64, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x70, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x75, 0x74, 0x6f, 0x4f, 0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63,
	0x65, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x75, 0x74, 0x6f, 0x4f,
	0x62, 0x74, 0x61, 0x69, 0x6e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6c, 0x75,
	0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61,
	0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conan_evidence_proto_rawDescOnce sync.Once
	file_conan_evidence_proto_rawDescData = file_conan_evidence_proto_rawDesc
)

func file_conan_evidence_proto_rawDescGZIP() []byte {
	file_conan_evidence_proto_rawDescOnce.Do(func() {
		file_conan_evidence_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_evidence_proto_rawDescData)
	})
	return file_conan_evidence_proto_rawDescData
}

var file_conan_evidence_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_conan_evidence_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_conan_evidence_proto_goTypes = []any{
	(ResourceTaskStatus)(0),                              // 0: conan.ResourceTaskStatus
	(EvidenceStatus)(0),                                  // 1: conan.EvidenceStatus
	(EvidenceSourceType)(0),                              // 2: conan.EvidenceSourceType
	(EvidenceType)(0),                                    // 3: conan.EvidenceType
	(EvidenceStorageMode)(0),                             // 4: conan.EvidenceStorageMode
	(EvidenceReturnWay)(0),                               // 5: conan.EvidenceReturnWay
	(*ClueObtainEvidenceTaskStatus)(nil),                 // 6: conan.ClueObtainEvidenceTaskStatus
	(*LimitPassNotifyRequest)(nil),                       // 7: conan.LimitPassNotifyRequest
	(*ObtainEvidenceChannelAvailableNotifyResponse)(nil), // 8: conan.ObtainEvidenceChannelAvailableNotifyResponse
	(*ObtainEvidenceTaskStatus)(nil),                     // 9: conan.ObtainEvidenceTaskStatus
	(*UploadInfo)(nil),                                   // 10: conan.UploadInfo
	(*ObtainEvidenceFinishedNotifyRequest)(nil),          // 11: conan.ObtainEvidenceFinishedNotifyRequest
	(*ObtainEvidenceResponse)(nil),                       // 12: conan.ObtainEvidenceResponse
	(*EvidenceSourceInfo)(nil),                           // 13: conan.EvidenceSourceInfo
	(*HostClueInfo)(nil),                                 // 14: conan.HostClueInfo
	(*ClueObtainEvidenceTaskRequest)(nil),                // 15: conan.ClueObtainEvidenceTaskRequest
	(*EvicenceFileInfo)(nil),                             // 16: conan.EvicenceFileInfo
	(*EvidenceInfo)(nil),                                 // 17: conan.EvidenceInfo
	(*FileDetail)(nil),                                   // 18: conan.FileDetail
	(*FileObtainEvidenceTaskInfo)(nil),                   // 19: conan.FileObtainEvidenceTaskInfo
	(*BatchFileObtainEvidenceTaskRequest)(nil),           // 20: conan.BatchFileObtainEvidenceTaskRequest
	(*BatchFileObtainEvidenceResponse)(nil),              // 21: conan.BatchFileObtainEvidenceResponse
	(*NetConnectionInfo)(nil),                            // 22: conan.NetConnectionInfo
	(*EvidenceOutReachInfo)(nil),                         // 23: conan.EvidenceOutReachInfo
	(*NetObtainEvidenceTaskInfo)(nil),                    // 24: conan.NetObtainEvidenceTaskInfo
	(*LogObtainEvidenceTaskRequest)(nil),                 // 25: conan.LogObtainEvidenceTaskRequest
	(*RenewObtainEvidenceRequest)(nil),                   // 26: conan.RenewObtainEvidenceRequest
	(*ClueEvidenceQueryRequest)(nil),                     // 27: conan.ClueEvidenceQueryRequest
	(*GetFileEvidenceRequest)(nil),                       // 28: conan.GetFileEvidenceRequest
	(*EvidenceExtractionResultResponse)(nil),             // 29: conan.EvidenceExtractionResultResponse
	(*EvidenceExtractionResult)(nil),                     // 30: conan.EvidenceExtractionResult
	(*EvidenceFilter)(nil),                               // 31: conan.EvidenceFilter
	(*NetEvidenceQueryRequest)(nil),                      // 32: conan.NetEvidenceQueryRequest
	(*EvidenceSourceItem)(nil),                           // 33: conan.EvidenceSourceItem
	(*NetEvidenceInfo)(nil),                              // 34: conan.NetEvidenceInfo
	(*NetEvidenceInfoListResponse)(nil),                  // 35: conan.NetEvidenceInfoListResponse
	(*FileEvidenceQueryRequest)(nil),                     // 36: conan.FileEvidenceQueryRequest
	(*FileEvidenceInfo)(nil),                             // 37: conan.FileEvidenceInfo
	(*FileEvidenceInfoListResponse)(nil),                 // 38: conan.FileEvidenceInfoListResponse
	(*MemDumpEvidenceQueryRequest)(nil),                  // 39: conan.MemDumpEvidenceQueryRequest
	(*MemDumpEvidenceInfo)(nil),                          // 40: conan.MemDumpEvidenceInfo
	(*MemDumpEvidenceInfoListResponse)(nil),              // 41: conan.MemDumpEvidenceInfoListResponse
	(*LogEvidenceFilter)(nil),                            // 42: conan.LogEvidenceFilter
	(*LogEvidenceQueryRequest)(nil),                      // 43: conan.LogEvidenceQueryRequest
	(*LogEvidenceInfo)(nil),                              // 44: conan.LogEvidenceInfo
	(*LogEvidenceInfoListResponse)(nil),                  // 45: conan.LogEvidenceInfoListResponse
	(*GetEvidenceResultInfoRequest)(nil),                 // 46: conan.GetEvidenceResultInfoRequest
	(*TerminalInfo)(nil),                                 // 47: conan.TerminalInfo
	(*EvidenceProcessInfo)(nil),                          // 48: conan.EvidenceProcessInfo
	(*FileEvidenceContent)(nil),                          // 49: conan.FileEvidenceContent
	(*CommonEvidenceContent)(nil),                        // 50: conan.CommonEvidenceContent
	(*LogEvidenceContent)(nil),                           // 51: conan.LogEvidenceContent
	(*EvidenceResultInfo)(nil),                           // 52: conan.EvidenceResultInfo
	(*BatchGetEvidenceResultInfoRequest)(nil),            // 53: conan.BatchGetEvidenceResultInfoRequest
	(*BatchEvidenceResultInfoResponse)(nil),              // 54: conan.BatchEvidenceResultInfoResponse
	(*GetEvidenceStorageInfoRequest)(nil),                // 55: conan.GetEvidenceStorageInfoRequest
	(*EvidenceStorage)(nil),                              // 56: conan.EvidenceStorage
	(*GetObtainedEvidenceCountRequest)(nil),              // 57: conan.GetObtainedEvidenceCountRequest
	(*ObtainEvidenceCountResponse)(nil),                  // 58: conan.ObtainEvidenceCountResponse
	(*GetEvidenceSourceConfigRequest)(nil),               // 59: conan.GetEvidenceSourceConfigRequest
	(*EvidenceSourceConfigResponse)(nil),                 // 60: conan.EvidenceSourceConfigResponse
	(*EvidenceSourceConfigRequest)(nil),                  // 61: conan.EvidenceSourceConfigRequest
	(*DeleteEvidenceSourceConfigRequest)(nil),            // 62: conan.DeleteEvidenceSourceConfigRequest
	(*UpdateAutoObtainEvidenceClueSubTypeRequest)(nil),   // 63: conan.UpdateAutoObtainEvidenceClueSubTypeRequest
	nil,                   // 64: conan.ObtainEvidenceFinishedNotifyRequest.FormParamsEntry
	(ClueType)(0),         // 65: conan.ClueType
	(*PageRequest)(nil),   // 66: conan.PageRequest
	(*PageResponse)(nil),  // 67: conan.PageResponse
	(*emptypb.Empty)(nil), // 68: google.protobuf.Empty
}
var file_conan_evidence_proto_depIdxs = []int32{
	3,  // 0: conan.ClueObtainEvidenceTaskStatus.evidence_type:type_name -> conan.EvidenceType
	9,  // 1: conan.ClueObtainEvidenceTaskStatus.status:type_name -> conan.ObtainEvidenceTaskStatus
	0,  // 2: conan.ObtainEvidenceChannelAvailableNotifyResponse.status:type_name -> conan.ResourceTaskStatus
	1,  // 3: conan.ObtainEvidenceTaskStatus.status:type_name -> conan.EvidenceStatus
	10, // 4: conan.ObtainEvidenceFinishedNotifyRequest.upload_info:type_name -> conan.UploadInfo
	0,  // 5: conan.ObtainEvidenceFinishedNotifyRequest.status:type_name -> conan.ResourceTaskStatus
	64, // 6: conan.ObtainEvidenceFinishedNotifyRequest.form_params:type_name -> conan.ObtainEvidenceFinishedNotifyRequest.FormParamsEntry
	13, // 7: conan.ClueObtainEvidenceTaskRequest.source_info:type_name -> conan.EvidenceSourceInfo
	3,  // 8: conan.ClueObtainEvidenceTaskRequest.evidence_type:type_name -> conan.EvidenceType
	18, // 9: conan.EvidenceInfo.file_detail:type_name -> conan.FileDetail
	14, // 10: conan.FileObtainEvidenceTaskInfo.host_clue_info:type_name -> conan.HostClueInfo
	13, // 11: conan.FileObtainEvidenceTaskInfo.source_info:type_name -> conan.EvidenceSourceInfo
	17, // 12: conan.FileObtainEvidenceTaskInfo.evidence_info:type_name -> conan.EvidenceInfo
	13, // 13: conan.BatchFileObtainEvidenceTaskRequest.source_info:type_name -> conan.EvidenceSourceInfo
	22, // 14: conan.EvidenceOutReachInfo.connection_info:type_name -> conan.NetConnectionInfo
	14, // 15: conan.NetObtainEvidenceTaskInfo.host_clue_info:type_name -> conan.HostClueInfo
	13, // 16: conan.NetObtainEvidenceTaskInfo.source_info:type_name -> conan.EvidenceSourceInfo
	23, // 17: conan.NetObtainEvidenceTaskInfo.outreach_info:type_name -> conan.EvidenceOutReachInfo
	13, // 18: conan.LogObtainEvidenceTaskRequest.source_info:type_name -> conan.EvidenceSourceInfo
	65, // 19: conan.ClueEvidenceQueryRequest.clue_type:type_name -> conan.ClueType
	3,  // 20: conan.ClueEvidenceQueryRequest.evidence_type:type_name -> conan.EvidenceType
	30, // 21: conan.EvidenceExtractionResultResponse.results:type_name -> conan.EvidenceExtractionResult
	3,  // 22: conan.EvidenceExtractionResult.evidence_type:type_name -> conan.EvidenceType
	1,  // 23: conan.EvidenceExtractionResult.status:type_name -> conan.EvidenceStatus
	31, // 24: conan.NetEvidenceQueryRequest.filter:type_name -> conan.EvidenceFilter
	66, // 25: conan.NetEvidenceQueryRequest.page:type_name -> conan.PageRequest
	1,  // 26: conan.NetEvidenceInfo.status:type_name -> conan.EvidenceStatus
	33, // 27: conan.NetEvidenceInfo.sources:type_name -> conan.EvidenceSourceItem
	34, // 28: conan.NetEvidenceInfoListResponse.items:type_name -> conan.NetEvidenceInfo
	67, // 29: conan.NetEvidenceInfoListResponse.page:type_name -> conan.PageResponse
	31, // 30: conan.FileEvidenceQueryRequest.filter:type_name -> conan.EvidenceFilter
	66, // 31: conan.FileEvidenceQueryRequest.page:type_name -> conan.PageRequest
	1,  // 32: conan.FileEvidenceInfo.status:type_name -> conan.EvidenceStatus
	16, // 33: conan.FileEvidenceInfo.file_info:type_name -> conan.EvicenceFileInfo
	33, // 34: conan.FileEvidenceInfo.sources:type_name -> conan.EvidenceSourceItem
	37, // 35: conan.FileEvidenceInfoListResponse.items:type_name -> conan.FileEvidenceInfo
	67, // 36: conan.FileEvidenceInfoListResponse.page:type_name -> conan.PageResponse
	31, // 37: conan.MemDumpEvidenceQueryRequest.filter:type_name -> conan.EvidenceFilter
	66, // 38: conan.MemDumpEvidenceQueryRequest.page:type_name -> conan.PageRequest
	1,  // 39: conan.MemDumpEvidenceInfo.status:type_name -> conan.EvidenceStatus
	33, // 40: conan.MemDumpEvidenceInfo.sources:type_name -> conan.EvidenceSourceItem
	40, // 41: conan.MemDumpEvidenceInfoListResponse.items:type_name -> conan.MemDumpEvidenceInfo
	67, // 42: conan.MemDumpEvidenceInfoListResponse.page:type_name -> conan.PageResponse
	42, // 43: conan.LogEvidenceQueryRequest.filter:type_name -> conan.LogEvidenceFilter
	66, // 44: conan.LogEvidenceQueryRequest.page:type_name -> conan.PageRequest
	1,  // 45: conan.LogEvidenceInfo.status:type_name -> conan.EvidenceStatus
	33, // 46: conan.LogEvidenceInfo.sources:type_name -> conan.EvidenceSourceItem
	44, // 47: conan.LogEvidenceInfoListResponse.items:type_name -> conan.LogEvidenceInfo
	67, // 48: conan.LogEvidenceInfoListResponse.page:type_name -> conan.PageResponse
	50, // 49: conan.LogEvidenceContent.common:type_name -> conan.CommonEvidenceContent
	1,  // 50: conan.EvidenceResultInfo.status:type_name -> conan.EvidenceStatus
	47, // 51: conan.EvidenceResultInfo.terminal_info:type_name -> conan.TerminalInfo
	48, // 52: conan.EvidenceResultInfo.process_chain_info:type_name -> conan.EvidenceProcessInfo
	49, // 53: conan.EvidenceResultInfo.file:type_name -> conan.FileEvidenceContent
	49, // 54: conan.EvidenceResultInfo.script_file:type_name -> conan.FileEvidenceContent
	50, // 55: conan.EvidenceResultInfo.memory_dump:type_name -> conan.CommonEvidenceContent
	50, // 56: conan.EvidenceResultInfo.mini_memory_dump:type_name -> conan.CommonEvidenceContent
	50, // 57: conan.EvidenceResultInfo.memory_segment:type_name -> conan.CommonEvidenceContent
	51, // 58: conan.EvidenceResultInfo.log_file:type_name -> conan.LogEvidenceContent
	52, // 59: conan.BatchEvidenceResultInfoResponse.results:type_name -> conan.EvidenceResultInfo
	4,  // 60: conan.EvidenceStorage.mode:type_name -> conan.EvidenceStorageMode
	5,  // 61: conan.EvidenceSourceConfigResponse.return_way:type_name -> conan.EvidenceReturnWay
	5,  // 62: conan.EvidenceSourceConfigRequest.return_way:type_name -> conan.EvidenceReturnWay
	7,  // 63: conan.EvidenceService.ObtainEvidenceChannelAvailableNotify:input_type -> conan.LimitPassNotifyRequest
	11, // 64: conan.EvidenceService.ObtainEvidenceFinishedNotify:input_type -> conan.ObtainEvidenceFinishedNotifyRequest
	12, // 65: conan.EvidenceService.ReportObtainEvidenceInfo:input_type -> conan.ObtainEvidenceResponse
	15, // 66: conan.EvidenceService.AddClueObtainEvidenceTask:input_type -> conan.ClueObtainEvidenceTaskRequest
	19, // 67: conan.EvidenceService.AddFileObtainEvidenceTask:input_type -> conan.FileObtainEvidenceTaskInfo
	20, // 68: conan.EvidenceService.BatchAddFileObtainEvidenceTask:input_type -> conan.BatchFileObtainEvidenceTaskRequest
	24, // 69: conan.EvidenceService.AddNetObtainEvidenceTask:input_type -> conan.NetObtainEvidenceTaskInfo
	25, // 70: conan.EvidenceService.AddLogObtainEvidenceTask:input_type -> conan.LogObtainEvidenceTaskRequest
	26, // 71: conan.EvidenceService.RenewObtainEvidence:input_type -> conan.RenewObtainEvidenceRequest
	27, // 72: conan.EvidenceService.GetClueEvidenceInfo:input_type -> conan.ClueEvidenceQueryRequest
	28, // 73: conan.EvidenceService.GetFileEvidenceInfo:input_type -> conan.GetFileEvidenceRequest
	32, // 74: conan.EvidenceService.QueryNetEvidenceInfo:input_type -> conan.NetEvidenceQueryRequest
	36, // 75: conan.EvidenceService.QueryFileEvidenceInfo:input_type -> conan.FileEvidenceQueryRequest
	39, // 76: conan.EvidenceService.QueryMemoryDumpEvidenceInfo:input_type -> conan.MemDumpEvidenceQueryRequest
	43, // 77: conan.EvidenceService.QueryLogEvidenceInfo:input_type -> conan.LogEvidenceQueryRequest
	46, // 78: conan.EvidenceService.GetEvidenceResultInfo:input_type -> conan.GetEvidenceResultInfoRequest
	53, // 79: conan.EvidenceService.BatchGetEvidenceResultInfo:input_type -> conan.BatchGetEvidenceResultInfoRequest
	55, // 80: conan.EvidenceService.GetEvidenceStorageInfo:input_type -> conan.GetEvidenceStorageInfoRequest
	57, // 81: conan.EvidenceService.GetObtainedEvidenceCount:input_type -> conan.GetObtainedEvidenceCountRequest
	59, // 82: conan.EvidenceService.GetEvidenceSourceConfig:input_type -> conan.GetEvidenceSourceConfigRequest
	61, // 83: conan.EvidenceService.AddEvidenceSourceConfig:input_type -> conan.EvidenceSourceConfigRequest
	62, // 84: conan.EvidenceService.DeleteEvidenceSourceConfig:input_type -> conan.DeleteEvidenceSourceConfigRequest
	63, // 85: conan.EvidenceService.UpdateAutoObtainEvidenceClueSubType:input_type -> conan.UpdateAutoObtainEvidenceClueSubTypeRequest
	8,  // 86: conan.EvidenceService.ObtainEvidenceChannelAvailableNotify:output_type -> conan.ObtainEvidenceChannelAvailableNotifyResponse
	68, // 87: conan.EvidenceService.ObtainEvidenceFinishedNotify:output_type -> google.protobuf.Empty
	68, // 88: conan.EvidenceService.ReportObtainEvidenceInfo:output_type -> google.protobuf.Empty
	6,  // 89: conan.EvidenceService.AddClueObtainEvidenceTask:output_type -> conan.ClueObtainEvidenceTaskStatus
	9,  // 90: conan.EvidenceService.AddFileObtainEvidenceTask:output_type -> conan.ObtainEvidenceTaskStatus
	21, // 91: conan.EvidenceService.BatchAddFileObtainEvidenceTask:output_type -> conan.BatchFileObtainEvidenceResponse
	9,  // 92: conan.EvidenceService.AddNetObtainEvidenceTask:output_type -> conan.ObtainEvidenceTaskStatus
	9,  // 93: conan.EvidenceService.AddLogObtainEvidenceTask:output_type -> conan.ObtainEvidenceTaskStatus
	9,  // 94: conan.EvidenceService.RenewObtainEvidence:output_type -> conan.ObtainEvidenceTaskStatus
	29, // 95: conan.EvidenceService.GetClueEvidenceInfo:output_type -> conan.EvidenceExtractionResultResponse
	30, // 96: conan.EvidenceService.GetFileEvidenceInfo:output_type -> conan.EvidenceExtractionResult
	35, // 97: conan.EvidenceService.QueryNetEvidenceInfo:output_type -> conan.NetEvidenceInfoListResponse
	38, // 98: conan.EvidenceService.QueryFileEvidenceInfo:output_type -> conan.FileEvidenceInfoListResponse
	41, // 99: conan.EvidenceService.QueryMemoryDumpEvidenceInfo:output_type -> conan.MemDumpEvidenceInfoListResponse
	45, // 100: conan.EvidenceService.QueryLogEvidenceInfo:output_type -> conan.LogEvidenceInfoListResponse
	52, // 101: conan.EvidenceService.GetEvidenceResultInfo:output_type -> conan.EvidenceResultInfo
	54, // 102: conan.EvidenceService.BatchGetEvidenceResultInfo:output_type -> conan.BatchEvidenceResultInfoResponse
	56, // 103: conan.EvidenceService.GetEvidenceStorageInfo:output_type -> conan.EvidenceStorage
	58, // 104: conan.EvidenceService.GetObtainedEvidenceCount:output_type -> conan.ObtainEvidenceCountResponse
	60, // 105: conan.EvidenceService.GetEvidenceSourceConfig:output_type -> conan.EvidenceSourceConfigResponse
	68, // 106: conan.EvidenceService.AddEvidenceSourceConfig:output_type -> google.protobuf.Empty
	68, // 107: conan.EvidenceService.DeleteEvidenceSourceConfig:output_type -> google.protobuf.Empty
	68, // 108: conan.EvidenceService.UpdateAutoObtainEvidenceClueSubType:output_type -> google.protobuf.Empty
	86, // [86:109] is the sub-list for method output_type
	63, // [63:86] is the sub-list for method input_type
	63, // [63:63] is the sub-list for extension type_name
	63, // [63:63] is the sub-list for extension extendee
	0,  // [0:63] is the sub-list for field type_name
}

func init() { file_conan_evidence_proto_init() }
func file_conan_evidence_proto_init() {
	if File_conan_evidence_proto != nil {
		return
	}
	file_conan_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_evidence_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_evidence_proto_goTypes,
		DependencyIndexes: file_conan_evidence_proto_depIdxs,
		EnumInfos:         file_conan_evidence_proto_enumTypes,
		MessageInfos:      file_conan_evidence_proto_msgTypes,
	}.Build()
	File_conan_evidence_proto = out.File
	file_conan_evidence_proto_rawDesc = nil
	file_conan_evidence_proto_goTypes = nil
	file_conan_evidence_proto_depIdxs = nil
}
