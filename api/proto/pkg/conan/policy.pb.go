// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/policy.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IgnoreStatusRequest_IgnoreAction int32

const (
	IgnoreStatusRequest_IGNORE_ACTION_UNSPECIFIED IgnoreStatusRequest_IgnoreAction = 0
	IgnoreStatusRequest_IGNORE_ACTION_CANCEL      IgnoreStatusRequest_IgnoreAction = 1 // 取消忽略
	IgnoreStatusRequest_IGNORE_ACTION_SET         IgnoreStatusRequest_IgnoreAction = 2 // 设置忽略
)

// Enum value maps for IgnoreStatusRequest_IgnoreAction.
var (
	IgnoreStatusRequest_IgnoreAction_name = map[int32]string{
		0: "IGNORE_ACTION_UNSPECIFIED",
		1: "IGNORE_ACTION_CANCEL",
		2: "IGNORE_ACTION_SET",
	}
	IgnoreStatusRequest_IgnoreAction_value = map[string]int32{
		"IGNORE_ACTION_UNSPECIFIED": 0,
		"IGNORE_ACTION_CANCEL":      1,
		"IGNORE_ACTION_SET":         2,
	}
)

func (x IgnoreStatusRequest_IgnoreAction) Enum() *IgnoreStatusRequest_IgnoreAction {
	p := new(IgnoreStatusRequest_IgnoreAction)
	*p = x
	return p
}

func (x IgnoreStatusRequest_IgnoreAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IgnoreStatusRequest_IgnoreAction) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_policy_proto_enumTypes[0].Descriptor()
}

func (IgnoreStatusRequest_IgnoreAction) Type() protoreflect.EnumType {
	return &file_conan_policy_proto_enumTypes[0]
}

func (x IgnoreStatusRequest_IgnoreAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IgnoreStatusRequest_IgnoreAction.Descriptor instead.
func (IgnoreStatusRequest_IgnoreAction) EnumDescriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{2, 0}
}

// AttributeInfoRequest 获取属性信息请求
type AttributeInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      ClueType `protobuf:"varint,1,opt,name=type,proto3,enum=conan.ClueType" json:"type,omitempty"` // 线索类型
	Attribute string   `protobuf:"bytes,2,opt,name=attribute,proto3" json:"attribute,omitempty"`            // 属性名称
	ClueKey   string   `protobuf:"bytes,3,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"` // 线索唯一标识(可选,用于获取具体属性值)
}

func (x *AttributeInfoRequest) Reset() {
	*x = AttributeInfoRequest{}
	mi := &file_conan_policy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttributeInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttributeInfoRequest) ProtoMessage() {}

func (x *AttributeInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttributeInfoRequest.ProtoReflect.Descriptor instead.
func (*AttributeInfoRequest) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{0}
}

func (x *AttributeInfoRequest) GetType() ClueType {
	if x != nil {
		return x.Type
	}
	return ClueType_CT_UNKNOWN
}

func (x *AttributeInfoRequest) GetAttribute() string {
	if x != nil {
		return x.Attribute
	}
	return ""
}

func (x *AttributeInfoRequest) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

// AttributeInfoResponse 属性信息响应
type AttributeInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attributes []string `protobuf:"bytes,1,rep,name=attributes,proto3" json:"attributes,omitempty"` // 属性名称列表
	Rules      []int32  `protobuf:"varint,2,rep,packed,name=rules,proto3" json:"rules,omitempty"`   // 规则列表
	Values     []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`         // 属性值列表
}

func (x *AttributeInfoResponse) Reset() {
	*x = AttributeInfoResponse{}
	mi := &file_conan_policy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttributeInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttributeInfoResponse) ProtoMessage() {}

func (x *AttributeInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttributeInfoResponse.ProtoReflect.Descriptor instead.
func (*AttributeInfoResponse) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{1}
}

func (x *AttributeInfoResponse) GetAttributes() []string {
	if x != nil {
		return x.Attributes
	}
	return nil
}

func (x *AttributeInfoResponse) GetRules() []int32 {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *AttributeInfoResponse) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// IgnoreStatusRequest 设置忽略状态请求
type IgnoreStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter      *ListAttackCluesFilter           `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`                                              // 过滤条件
	Type        ClueType                         `protobuf:"varint,2,opt,name=type,proto3,enum=conan.ClueType" json:"type,omitempty"`                             // 线索类型
	IsSelectAll bool                             `protobuf:"varint,3,opt,name=is_select_all,json=isSelectAll,proto3" json:"is_select_all,omitempty"`              // 是否全选(true:ids为排除项,false:ids为选中项)
	Ids         []int64                          `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`                                            // 线索ID列表
	Action      IgnoreStatusRequest_IgnoreAction `protobuf:"varint,5,opt,name=action,proto3,enum=conan.IgnoreStatusRequest_IgnoreAction" json:"action,omitempty"` // 忽略动作
}

func (x *IgnoreStatusRequest) Reset() {
	*x = IgnoreStatusRequest{}
	mi := &file_conan_policy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IgnoreStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreStatusRequest) ProtoMessage() {}

func (x *IgnoreStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreStatusRequest.ProtoReflect.Descriptor instead.
func (*IgnoreStatusRequest) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{2}
}

func (x *IgnoreStatusRequest) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *IgnoreStatusRequest) GetType() ClueType {
	if x != nil {
		return x.Type
	}
	return ClueType_CT_UNKNOWN
}

func (x *IgnoreStatusRequest) GetIsSelectAll() bool {
	if x != nil {
		return x.IsSelectAll
	}
	return false
}

func (x *IgnoreStatusRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *IgnoreStatusRequest) GetAction() IgnoreStatusRequest_IgnoreAction {
	if x != nil {
		return x.Action
	}
	return IgnoreStatusRequest_IGNORE_ACTION_UNSPECIFIED
}

// IgnoreStatusResponse 设置忽略状态响应
type IgnoreStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AffectedCount int32 `protobuf:"varint,1,opt,name=affected_count,json=affectedCount,proto3" json:"affected_count,omitempty"` // 受影响的记录数
}

func (x *IgnoreStatusResponse) Reset() {
	*x = IgnoreStatusResponse{}
	mi := &file_conan_policy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IgnoreStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnoreStatusResponse) ProtoMessage() {}

func (x *IgnoreStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnoreStatusResponse.ProtoReflect.Descriptor instead.
func (*IgnoreStatusResponse) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{3}
}

func (x *IgnoreStatusResponse) GetAffectedCount() int32 {
	if x != nil {
		return x.AffectedCount
	}
	return 0
}

// PolicyRule 策略规则
type BWPolicyRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Attribute string   `protobuf:"bytes,1,opt,name=attribute,proto3" json:"attribute,omitempty"`  // 属性名(如:md5、进程名、路径等)
	Condition int32    `protobuf:"varint,2,opt,name=condition,proto3" json:"condition,omitempty"` // 匹配条件
	Scope     int32    `protobuf:"varint,3,opt,name=scope,proto3" json:"scope,omitempty"`         // 匹配范围
	Values    []string `protobuf:"bytes,4,rep,name=values,proto3" json:"values,omitempty"`        // 属性值列表
}

func (x *BWPolicyRule) Reset() {
	*x = BWPolicyRule{}
	mi := &file_conan_policy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BWPolicyRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BWPolicyRule) ProtoMessage() {}

func (x *BWPolicyRule) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BWPolicyRule.ProtoReflect.Descriptor instead.
func (*BWPolicyRule) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{4}
}

func (x *BWPolicyRule) GetAttribute() string {
	if x != nil {
		return x.Attribute
	}
	return ""
}

func (x *BWPolicyRule) GetCondition() int32 {
	if x != nil {
		return x.Condition
	}
	return 0
}

func (x *BWPolicyRule) GetScope() int32 {
	if x != nil {
		return x.Scope
	}
	return 0
}

func (x *BWPolicyRule) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// BWPolicy 策略定义
type BWPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                           // 策略ID
	Description     string  `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`                                          // 策略描述
	MachineGroupIds []int64 `protobuf:"varint,3,rep,packed,name=machine_group_ids,json=machineGroupIds,proto3" json:"machine_group_ids,omitempty"` // 机器分组ID列表
	// 这个设计不是很合理，很容易引起歧义，应该传递两个参数表示一级和二级线索类型,
	// 暂时兼容前端的传递方式。文件威胁和外联告警时表示一级线索类型，内存告警和系统攻击时表示二级线索类型
	RiskTypes  []uint32               `protobuf:"varint,4,rep,packed,name=risk_types,json=riskTypes,proto3" json:"risk_types,omitempty"` // 风险类型列表,
	Rules      []*BWPolicyRule        `protobuf:"bytes,5,rep,name=rules,proto3" json:"rules,omitempty"`                                  // 规则列表
	CreatorId  int64                  `protobuf:"varint,6,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`        // 创建人ID
	UpdaterId  int64                  `protobuf:"varint,7,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`        // 更新人ID
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`      // 创建时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`      // 更新时间
}

func (x *BWPolicy) Reset() {
	*x = BWPolicy{}
	mi := &file_conan_policy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BWPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BWPolicy) ProtoMessage() {}

func (x *BWPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BWPolicy.ProtoReflect.Descriptor instead.
func (*BWPolicy) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{5}
}

func (x *BWPolicy) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BWPolicy) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *BWPolicy) GetMachineGroupIds() []int64 {
	if x != nil {
		return x.MachineGroupIds
	}
	return nil
}

func (x *BWPolicy) GetRiskTypes() []uint32 {
	if x != nil {
		return x.RiskTypes
	}
	return nil
}

func (x *BWPolicy) GetRules() []*BWPolicyRule {
	if x != nil {
		return x.Rules
	}
	return nil
}

func (x *BWPolicy) GetCreatorId() int64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *BWPolicy) GetUpdaterId() int64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *BWPolicy) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *BWPolicy) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type CreateBWPolicyResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Policies []*BWPolicy `protobuf:"bytes,1,rep,name=policies,proto3" json:"policies,omitempty"`
}

func (x *CreateBWPolicyResp) Reset() {
	*x = CreateBWPolicyResp{}
	mi := &file_conan_policy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBWPolicyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBWPolicyResp) ProtoMessage() {}

func (x *CreateBWPolicyResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBWPolicyResp.ProtoReflect.Descriptor instead.
func (*CreateBWPolicyResp) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{6}
}

func (x *CreateBWPolicyResp) GetPolicies() []*BWPolicy {
	if x != nil {
		return x.Policies
	}
	return nil
}

type DeleteBWPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *DeleteBWPolicyReq) Reset() {
	*x = DeleteBWPolicyReq{}
	mi := &file_conan_policy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteBWPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBWPolicyReq) ProtoMessage() {}

func (x *DeleteBWPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBWPolicyReq.ProtoReflect.Descriptor instead.
func (*DeleteBWPolicyReq) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteBWPolicyReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type UpdateBWPolicyReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewPolicies []*BWPolicy `protobuf:"bytes,1,rep,name=new_policies,json=newPolicies,proto3" json:"new_policies,omitempty"`
}

func (x *UpdateBWPolicyReq) Reset() {
	*x = UpdateBWPolicyReq{}
	mi := &file_conan_policy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBWPolicyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBWPolicyReq) ProtoMessage() {}

func (x *UpdateBWPolicyReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBWPolicyReq.ProtoReflect.Descriptor instead.
func (*UpdateBWPolicyReq) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateBWPolicyReq) GetNewPolicies() []*BWPolicy {
	if x != nil {
		return x.NewPolicies
	}
	return nil
}

type RiskCategory struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id     int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ZhName string `protobuf:"bytes,3,opt,name=zh_name,json=zhName,proto3" json:"zh_name,omitempty"`
}

func (x *RiskCategory) Reset() {
	*x = RiskCategory{}
	mi := &file_conan_policy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCategory) ProtoMessage() {}

func (x *RiskCategory) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCategory.ProtoReflect.Descriptor instead.
func (*RiskCategory) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{9}
}

func (x *RiskCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RiskCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RiskCategory) GetZhName() string {
	if x != nil {
		return x.ZhName
	}
	return ""
}

type SetRiskCategoriesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Categories []*RiskCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *SetRiskCategoriesReq) Reset() {
	*x = SetRiskCategoriesReq{}
	mi := &file_conan_policy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetRiskCategoriesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRiskCategoriesReq) ProtoMessage() {}

func (x *SetRiskCategoriesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRiskCategoriesReq.ProtoReflect.Descriptor instead.
func (*SetRiskCategoriesReq) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{10}
}

func (x *SetRiskCategoriesReq) GetCategories() []*RiskCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

type SetRiskCategoriesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Categories []*RiskCategory `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
}

func (x *SetRiskCategoriesResp) Reset() {
	*x = SetRiskCategoriesResp{}
	mi := &file_conan_policy_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetRiskCategoriesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRiskCategoriesResp) ProtoMessage() {}

func (x *SetRiskCategoriesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_policy_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRiskCategoriesResp.ProtoReflect.Descriptor instead.
func (*SetRiskCategoriesResp) Descriptor() ([]byte, []int) {
	return file_conan_policy_proto_rawDescGZIP(), []int{11}
}

func (x *SetRiskCategoriesResp) GetCategories() []*RiskCategory {
	if x != nil {
		return x.Categories
	}
	return nil
}

var File_conan_policy_proto protoreflect.FileDescriptor

var file_conan_policy_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x74, 0x0a,
	0x14, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65,
	0x4b, 0x65, 0x79, 0x22, 0x65, 0x0a, 0x15, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a,
	0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0a, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05,
	0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x05, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xc7, 0x02, 0x0a, 0x13, 0x49,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43,
	0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a,
	0x0d, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x61, 0x6c, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x41, 0x6c,
	0x6c, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03,
	0x69, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x49, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5e, 0x0a, 0x0c, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x10, 0x01, 0x12, 0x15, 0x0a,
	0x11, 0x49, 0x47, 0x4e, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53,
	0x45, 0x54, 0x10, 0x02, 0x22, 0x3d, 0x0a, 0x14, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0x78, 0x0a, 0x0c, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xea, 0x02,
	0x0a, 0x08, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0d, 0x52, 0x09, 0x72, 0x69,
	0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x29, 0x0a, 0x05, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42,
	0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x05, 0x72, 0x75, 0x6c,
	0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x41, 0x0a, 0x12, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x2b, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x57, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x22, 0x25, 0x0a,
	0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x03, 0x69, 0x64, 0x73, 0x22, 0x47, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x57,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x12, 0x32, 0x0a, 0x0c, 0x6e, 0x65, 0x77,
	0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x22, 0x4b, 0x0a,
	0x0c, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x7a, 0x68, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x7a, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x4b, 0x0a, 0x14, 0x53, 0x65,
	0x74, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x33, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52,
	0x69, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x22, 0x4c, 0x0a, 0x15, 0x53, 0x65, 0x74, 0x52, 0x69,
	0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x33, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x32, 0xb7, 0x03, 0x0a, 0x0d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4a, 0x0a, 0x0f, 0x53, 0x65, 0x74, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x49, 0x67,
	0x6e, 0x6f, 0x72, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x3c, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x57, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x57, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x42, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x39, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x57,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42,
	0x57, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x4e, 0x0a, 0x11, 0x53, 0x65, 0x74, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x74,
	0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x65, 0x74, 0x52, 0x69, 0x73,
	0x6b, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x42,
	0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70,
	0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_conan_policy_proto_rawDescOnce sync.Once
	file_conan_policy_proto_rawDescData = file_conan_policy_proto_rawDesc
)

func file_conan_policy_proto_rawDescGZIP() []byte {
	file_conan_policy_proto_rawDescOnce.Do(func() {
		file_conan_policy_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_policy_proto_rawDescData)
	})
	return file_conan_policy_proto_rawDescData
}

var file_conan_policy_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_conan_policy_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_conan_policy_proto_goTypes = []any{
	(IgnoreStatusRequest_IgnoreAction)(0), // 0: conan.IgnoreStatusRequest.IgnoreAction
	(*AttributeInfoRequest)(nil),          // 1: conan.AttributeInfoRequest
	(*AttributeInfoResponse)(nil),         // 2: conan.AttributeInfoResponse
	(*IgnoreStatusRequest)(nil),           // 3: conan.IgnoreStatusRequest
	(*IgnoreStatusResponse)(nil),          // 4: conan.IgnoreStatusResponse
	(*BWPolicyRule)(nil),                  // 5: conan.BWPolicyRule
	(*BWPolicy)(nil),                      // 6: conan.BWPolicy
	(*CreateBWPolicyResp)(nil),            // 7: conan.CreateBWPolicyResp
	(*DeleteBWPolicyReq)(nil),             // 8: conan.DeleteBWPolicyReq
	(*UpdateBWPolicyReq)(nil),             // 9: conan.UpdateBWPolicyReq
	(*RiskCategory)(nil),                  // 10: conan.RiskCategory
	(*SetRiskCategoriesReq)(nil),          // 11: conan.SetRiskCategoriesReq
	(*SetRiskCategoriesResp)(nil),         // 12: conan.SetRiskCategoriesResp
	(ClueType)(0),                         // 13: conan.ClueType
	(*ListAttackCluesFilter)(nil),         // 14: conan.ListAttackCluesFilter
	(*timestamppb.Timestamp)(nil),         // 15: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                 // 16: google.protobuf.Empty
}
var file_conan_policy_proto_depIdxs = []int32{
	13, // 0: conan.AttributeInfoRequest.type:type_name -> conan.ClueType
	14, // 1: conan.IgnoreStatusRequest.filter:type_name -> conan.ListAttackCluesFilter
	13, // 2: conan.IgnoreStatusRequest.type:type_name -> conan.ClueType
	0,  // 3: conan.IgnoreStatusRequest.action:type_name -> conan.IgnoreStatusRequest.IgnoreAction
	5,  // 4: conan.BWPolicy.rules:type_name -> conan.BWPolicyRule
	15, // 5: conan.BWPolicy.create_time:type_name -> google.protobuf.Timestamp
	15, // 6: conan.BWPolicy.update_time:type_name -> google.protobuf.Timestamp
	6,  // 7: conan.CreateBWPolicyResp.policies:type_name -> conan.BWPolicy
	6,  // 8: conan.UpdateBWPolicyReq.new_policies:type_name -> conan.BWPolicy
	10, // 9: conan.SetRiskCategoriesReq.categories:type_name -> conan.RiskCategory
	10, // 10: conan.SetRiskCategoriesResp.categories:type_name -> conan.RiskCategory
	1,  // 11: conan.PolicyService.GetAttributeInfo:input_type -> conan.AttributeInfoRequest
	3,  // 12: conan.PolicyService.SetIgnoreStatus:input_type -> conan.IgnoreStatusRequest
	6,  // 13: conan.PolicyService.CreateBWPolicy:input_type -> conan.BWPolicy
	8,  // 14: conan.PolicyService.DeleteBWPolicy:input_type -> conan.DeleteBWPolicyReq
	6,  // 15: conan.PolicyService.UpdateBWPolicy:input_type -> conan.BWPolicy
	11, // 16: conan.PolicyService.SetRiskCategories:input_type -> conan.SetRiskCategoriesReq
	2,  // 17: conan.PolicyService.GetAttributeInfo:output_type -> conan.AttributeInfoResponse
	4,  // 18: conan.PolicyService.SetIgnoreStatus:output_type -> conan.IgnoreStatusResponse
	7,  // 19: conan.PolicyService.CreateBWPolicy:output_type -> conan.CreateBWPolicyResp
	16, // 20: conan.PolicyService.DeleteBWPolicy:output_type -> google.protobuf.Empty
	16, // 21: conan.PolicyService.UpdateBWPolicy:output_type -> google.protobuf.Empty
	12, // 22: conan.PolicyService.SetRiskCategories:output_type -> conan.SetRiskCategoriesResp
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_conan_policy_proto_init() }
func file_conan_policy_proto_init() {
	if File_conan_policy_proto != nil {
		return
	}
	file_conan_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_policy_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_policy_proto_goTypes,
		DependencyIndexes: file_conan_policy_proto_depIdxs,
		EnumInfos:         file_conan_policy_proto_enumTypes,
		MessageInfos:      file_conan_policy_proto_msgTypes,
	}.Build()
	File_conan_policy_proto = out.File
	file_conan_policy_proto_rawDesc = nil
	file_conan_policy_proto_goTypes = nil
	file_conan_policy_proto_depIdxs = nil
}
