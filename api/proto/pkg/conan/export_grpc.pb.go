// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/export.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ExportService_ExportMemoryAttackClues_FullMethodName    = "/conan.ExportService/ExportMemoryAttackClues"
	ExportService_ExportIllegalOutreachClues_FullMethodName = "/conan.ExportService/ExportIllegalOutreachClues"
	ExportService_ExportFileThreatClues_FullMethodName      = "/conan.ExportService/ExportFileThreatClues"
	ExportService_ExportSystemAttackClues_FullMethodName    = "/conan.ExportService/ExportSystemAttackClues"
)

// ExportServiceClient is the client API for ExportService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ExportService provides streaming export functionality for various clue types
type ExportServiceClient interface {
	// ExportMemoryAttackClues streams memory attack clues for export
	ExportMemoryAttackClues(ctx context.Context, in *ExportMemoryAttackCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportMemoryAttackCluesResp], error)
	// ExportIllegalOutreachClues streams illegal outreach clues for export
	ExportIllegalOutreachClues(ctx context.Context, in *ExportIllegalOutreachCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportIllegalOutreachCluesResp], error)
	// ExportFileThreatClues streams file threat clues for export
	ExportFileThreatClues(ctx context.Context, in *ExportFileThreatCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportFileThreatCluesResp], error)
	// ExportSystemAttackClues streams system attack clues for export
	ExportSystemAttackClues(ctx context.Context, in *ExportSystemAttackCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportSystemAttackCluesResp], error)
}

type exportServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewExportServiceClient(cc grpc.ClientConnInterface) ExportServiceClient {
	return &exportServiceClient{cc}
}

func (c *exportServiceClient) ExportMemoryAttackClues(ctx context.Context, in *ExportMemoryAttackCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportMemoryAttackCluesResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ExportService_ServiceDesc.Streams[0], ExportService_ExportMemoryAttackClues_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ExportMemoryAttackCluesReq, ExportMemoryAttackCluesResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportMemoryAttackCluesClient = grpc.ServerStreamingClient[ExportMemoryAttackCluesResp]

func (c *exportServiceClient) ExportIllegalOutreachClues(ctx context.Context, in *ExportIllegalOutreachCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportIllegalOutreachCluesResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ExportService_ServiceDesc.Streams[1], ExportService_ExportIllegalOutreachClues_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ExportIllegalOutreachCluesReq, ExportIllegalOutreachCluesResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportIllegalOutreachCluesClient = grpc.ServerStreamingClient[ExportIllegalOutreachCluesResp]

func (c *exportServiceClient) ExportFileThreatClues(ctx context.Context, in *ExportFileThreatCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportFileThreatCluesResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ExportService_ServiceDesc.Streams[2], ExportService_ExportFileThreatClues_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ExportFileThreatCluesReq, ExportFileThreatCluesResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportFileThreatCluesClient = grpc.ServerStreamingClient[ExportFileThreatCluesResp]

func (c *exportServiceClient) ExportSystemAttackClues(ctx context.Context, in *ExportSystemAttackCluesReq, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ExportSystemAttackCluesResp], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ExportService_ServiceDesc.Streams[3], ExportService_ExportSystemAttackClues_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ExportSystemAttackCluesReq, ExportSystemAttackCluesResp]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportSystemAttackCluesClient = grpc.ServerStreamingClient[ExportSystemAttackCluesResp]

// ExportServiceServer is the server API for ExportService service.
// All implementations must embed UnimplementedExportServiceServer
// for forward compatibility.
//
// ExportService provides streaming export functionality for various clue types
type ExportServiceServer interface {
	// ExportMemoryAttackClues streams memory attack clues for export
	ExportMemoryAttackClues(*ExportMemoryAttackCluesReq, grpc.ServerStreamingServer[ExportMemoryAttackCluesResp]) error
	// ExportIllegalOutreachClues streams illegal outreach clues for export
	ExportIllegalOutreachClues(*ExportIllegalOutreachCluesReq, grpc.ServerStreamingServer[ExportIllegalOutreachCluesResp]) error
	// ExportFileThreatClues streams file threat clues for export
	ExportFileThreatClues(*ExportFileThreatCluesReq, grpc.ServerStreamingServer[ExportFileThreatCluesResp]) error
	// ExportSystemAttackClues streams system attack clues for export
	ExportSystemAttackClues(*ExportSystemAttackCluesReq, grpc.ServerStreamingServer[ExportSystemAttackCluesResp]) error
	mustEmbedUnimplementedExportServiceServer()
}

// UnimplementedExportServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedExportServiceServer struct{}

func (UnimplementedExportServiceServer) ExportMemoryAttackClues(*ExportMemoryAttackCluesReq, grpc.ServerStreamingServer[ExportMemoryAttackCluesResp]) error {
	return status.Errorf(codes.Unimplemented, "method ExportMemoryAttackClues not implemented")
}
func (UnimplementedExportServiceServer) ExportIllegalOutreachClues(*ExportIllegalOutreachCluesReq, grpc.ServerStreamingServer[ExportIllegalOutreachCluesResp]) error {
	return status.Errorf(codes.Unimplemented, "method ExportIllegalOutreachClues not implemented")
}
func (UnimplementedExportServiceServer) ExportFileThreatClues(*ExportFileThreatCluesReq, grpc.ServerStreamingServer[ExportFileThreatCluesResp]) error {
	return status.Errorf(codes.Unimplemented, "method ExportFileThreatClues not implemented")
}
func (UnimplementedExportServiceServer) ExportSystemAttackClues(*ExportSystemAttackCluesReq, grpc.ServerStreamingServer[ExportSystemAttackCluesResp]) error {
	return status.Errorf(codes.Unimplemented, "method ExportSystemAttackClues not implemented")
}
func (UnimplementedExportServiceServer) mustEmbedUnimplementedExportServiceServer() {}
func (UnimplementedExportServiceServer) testEmbeddedByValue()                       {}

// UnsafeExportServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExportServiceServer will
// result in compilation errors.
type UnsafeExportServiceServer interface {
	mustEmbedUnimplementedExportServiceServer()
}

func RegisterExportServiceServer(s grpc.ServiceRegistrar, srv ExportServiceServer) {
	// If the following call pancis, it indicates UnimplementedExportServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ExportService_ServiceDesc, srv)
}

func _ExportService_ExportMemoryAttackClues_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ExportMemoryAttackCluesReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ExportServiceServer).ExportMemoryAttackClues(m, &grpc.GenericServerStream[ExportMemoryAttackCluesReq, ExportMemoryAttackCluesResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportMemoryAttackCluesServer = grpc.ServerStreamingServer[ExportMemoryAttackCluesResp]

func _ExportService_ExportIllegalOutreachClues_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ExportIllegalOutreachCluesReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ExportServiceServer).ExportIllegalOutreachClues(m, &grpc.GenericServerStream[ExportIllegalOutreachCluesReq, ExportIllegalOutreachCluesResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportIllegalOutreachCluesServer = grpc.ServerStreamingServer[ExportIllegalOutreachCluesResp]

func _ExportService_ExportFileThreatClues_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ExportFileThreatCluesReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ExportServiceServer).ExportFileThreatClues(m, &grpc.GenericServerStream[ExportFileThreatCluesReq, ExportFileThreatCluesResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportFileThreatCluesServer = grpc.ServerStreamingServer[ExportFileThreatCluesResp]

func _ExportService_ExportSystemAttackClues_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ExportSystemAttackCluesReq)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ExportServiceServer).ExportSystemAttackClues(m, &grpc.GenericServerStream[ExportSystemAttackCluesReq, ExportSystemAttackCluesResp]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ExportService_ExportSystemAttackCluesServer = grpc.ServerStreamingServer[ExportSystemAttackCluesResp]

// ExportService_ServiceDesc is the grpc.ServiceDesc for ExportService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExportService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.ExportService",
	HandlerType: (*ExportServiceServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "ExportMemoryAttackClues",
			Handler:       _ExportService_ExportMemoryAttackClues_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ExportIllegalOutreachClues",
			Handler:       _ExportService_ExportIllegalOutreachClues_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ExportFileThreatClues",
			Handler:       _ExportService_ExportFileThreatClues_Handler,
			ServerStreams: true,
		},
		{
			StreamName:    "ExportSystemAttackClues",
			Handler:       _ExportService_ExportSystemAttackClues_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "conan/export.proto",
}
