// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/agent_file.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HandleAgentFileReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleAgentFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleAgentFileReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleAgentFileReqMultiError, or nil if none found.
func (m *HandleAgentFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleAgentFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for FileSha256

	// no validation rules for FileKey

	// no validation rules for Online

	// no validation rules for OperateType

	// no validation rules for EvidenceTaskId

	if len(errors) > 0 {
		return HandleAgentFileReqMultiError(errors)
	}

	return nil
}

// HandleAgentFileReqMultiError is an error wrapping multiple validation errors
// returned by HandleAgentFileReq.ValidateAll() if the designated constraints
// aren't met.
type HandleAgentFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleAgentFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleAgentFileReqMultiError) AllErrors() []error { return m }

// HandleAgentFileReqValidationError is the validation error returned by
// HandleAgentFileReq.Validate if the designated constraints aren't met.
type HandleAgentFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleAgentFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleAgentFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleAgentFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleAgentFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleAgentFileReqValidationError) ErrorName() string {
	return "HandleAgentFileReqValidationError"
}

// Error satisfies the builtin error interface
func (e HandleAgentFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleAgentFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleAgentFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleAgentFileReqValidationError{}

// Validate checks the field values on HandleAgentFileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HandleAgentFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HandleAgentFileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HandleAgentFileRespMultiError, or nil if none found.
func (m *HandleAgentFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *HandleAgentFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HandleAgentFileRespMultiError(errors)
	}

	return nil
}

// HandleAgentFileRespMultiError is an error wrapping multiple validation
// errors returned by HandleAgentFileResp.ValidateAll() if the designated
// constraints aren't met.
type HandleAgentFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HandleAgentFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HandleAgentFileRespMultiError) AllErrors() []error { return m }

// HandleAgentFileRespValidationError is the validation error returned by
// HandleAgentFileResp.Validate if the designated constraints aren't met.
type HandleAgentFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HandleAgentFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HandleAgentFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HandleAgentFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HandleAgentFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HandleAgentFileRespValidationError) ErrorName() string {
	return "HandleAgentFileRespValidationError"
}

// Error satisfies the builtin error interface
func (e HandleAgentFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHandleAgentFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HandleAgentFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HandleAgentFileRespValidationError{}

// Validate checks the field values on BatchHandleAgentFileReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchHandleAgentFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchHandleAgentFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchHandleAgentFileReqMultiError, or nil if none found.
func (m *BatchHandleAgentFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHandleAgentFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchHandleAgentFileReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchHandleAgentFileReqValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchHandleAgentFileReqValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchHandleAgentFileReqMultiError(errors)
	}

	return nil
}

// BatchHandleAgentFileReqMultiError is an error wrapping multiple validation
// errors returned by BatchHandleAgentFileReq.ValidateAll() if the designated
// constraints aren't met.
type BatchHandleAgentFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHandleAgentFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHandleAgentFileReqMultiError) AllErrors() []error { return m }

// BatchHandleAgentFileReqValidationError is the validation error returned by
// BatchHandleAgentFileReq.Validate if the designated constraints aren't met.
type BatchHandleAgentFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHandleAgentFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHandleAgentFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHandleAgentFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHandleAgentFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHandleAgentFileReqValidationError) ErrorName() string {
	return "BatchHandleAgentFileReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHandleAgentFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHandleAgentFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHandleAgentFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHandleAgentFileReqValidationError{}

// Validate checks the field values on BatchHandleAgentFileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchHandleAgentFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchHandleAgentFileResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchHandleAgentFileRespMultiError, or nil if none found.
func (m *BatchHandleAgentFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchHandleAgentFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchHandleAgentFileRespMultiError(errors)
	}

	return nil
}

// BatchHandleAgentFileRespMultiError is an error wrapping multiple validation
// errors returned by BatchHandleAgentFileResp.ValidateAll() if the designated
// constraints aren't met.
type BatchHandleAgentFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchHandleAgentFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchHandleAgentFileRespMultiError) AllErrors() []error { return m }

// BatchHandleAgentFileRespValidationError is the validation error returned by
// BatchHandleAgentFileResp.Validate if the designated constraints aren't met.
type BatchHandleAgentFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchHandleAgentFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchHandleAgentFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchHandleAgentFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchHandleAgentFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchHandleAgentFileRespValidationError) ErrorName() string {
	return "BatchHandleAgentFileRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchHandleAgentFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchHandleAgentFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchHandleAgentFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchHandleAgentFileRespValidationError{}

// Validate checks the field values on RetryHandleAgentFileReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryHandleAgentFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryHandleAgentFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetryHandleAgentFileReqMultiError, or nil if none found.
func (m *RetryHandleAgentFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryHandleAgentFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for FileSha256

	// no validation rules for FileKey

	// no validation rules for Online

	// no validation rules for OperateType

	// no validation rules for Password

	if len(errors) > 0 {
		return RetryHandleAgentFileReqMultiError(errors)
	}

	return nil
}

// RetryHandleAgentFileReqMultiError is an error wrapping multiple validation
// errors returned by RetryHandleAgentFileReq.ValidateAll() if the designated
// constraints aren't met.
type RetryHandleAgentFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryHandleAgentFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryHandleAgentFileReqMultiError) AllErrors() []error { return m }

// RetryHandleAgentFileReqValidationError is the validation error returned by
// RetryHandleAgentFileReq.Validate if the designated constraints aren't met.
type RetryHandleAgentFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryHandleAgentFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryHandleAgentFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryHandleAgentFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryHandleAgentFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryHandleAgentFileReqValidationError) ErrorName() string {
	return "RetryHandleAgentFileReqValidationError"
}

// Error satisfies the builtin error interface
func (e RetryHandleAgentFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryHandleAgentFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryHandleAgentFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryHandleAgentFileReqValidationError{}

// Validate checks the field values on RetryHandleAgentFileResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RetryHandleAgentFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryHandleAgentFileResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RetryHandleAgentFileRespMultiError, or nil if none found.
func (m *RetryHandleAgentFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryHandleAgentFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Password

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RetryHandleAgentFileRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RetryHandleAgentFileRespValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RetryHandleAgentFileRespValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RetryHandleAgentFileRespMultiError(errors)
	}

	return nil
}

// RetryHandleAgentFileRespMultiError is an error wrapping multiple validation
// errors returned by RetryHandleAgentFileResp.ValidateAll() if the designated
// constraints aren't met.
type RetryHandleAgentFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryHandleAgentFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryHandleAgentFileRespMultiError) AllErrors() []error { return m }

// RetryHandleAgentFileRespValidationError is the validation error returned by
// RetryHandleAgentFileResp.Validate if the designated constraints aren't met.
type RetryHandleAgentFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryHandleAgentFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryHandleAgentFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryHandleAgentFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryHandleAgentFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryHandleAgentFileRespValidationError) ErrorName() string {
	return "RetryHandleAgentFileRespValidationError"
}

// Error satisfies the builtin error interface
func (e RetryHandleAgentFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryHandleAgentFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryHandleAgentFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryHandleAgentFileRespValidationError{}

// Validate checks the field values on OperatedThreatenFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OperatedThreatenFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OperatedThreatenFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OperatedThreatenFileMultiError, or nil if none found.
func (m *OperatedThreatenFile) ValidateAll() error {
	return m.validate(true)
}

func (m *OperatedThreatenFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for ClueStatus

	// no validation rules for MachineId

	// no validation rules for FileKey

	// no validation rules for FileMd5

	// no validation rules for FileSha256

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileType

	// no validation rules for FileSize

	// no validation rules for OperationType

	// no validation rules for OperationStatus

	// no validation rules for OperationFailure

	// no validation rules for FileSource

	// no validation rules for EvidenceTaskId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OperatedThreatenFileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OperatedThreatenFileValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OperatedThreatenFileValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OperatedThreatenFileMultiError(errors)
	}

	return nil
}

// OperatedThreatenFileMultiError is an error wrapping multiple validation
// errors returned by OperatedThreatenFile.ValidateAll() if the designated
// constraints aren't met.
type OperatedThreatenFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OperatedThreatenFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OperatedThreatenFileMultiError) AllErrors() []error { return m }

// OperatedThreatenFileValidationError is the validation error returned by
// OperatedThreatenFile.Validate if the designated constraints aren't met.
type OperatedThreatenFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OperatedThreatenFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OperatedThreatenFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OperatedThreatenFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OperatedThreatenFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OperatedThreatenFileValidationError) ErrorName() string {
	return "OperatedThreatenFileValidationError"
}

// Error satisfies the builtin error interface
func (e OperatedThreatenFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOperatedThreatenFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OperatedThreatenFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OperatedThreatenFileValidationError{}

// Validate checks the field values on ListOperatedThreatenFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOperatedThreatenFileReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOperatedThreatenFileReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOperatedThreatenFileReqMultiError, or nil if none found.
func (m *ListOperatedThreatenFileReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOperatedThreatenFileReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileSha256

	// no validation rules for Filename

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOperatedThreatenFileReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOperatedThreatenFileReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOperatedThreatenFileReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOperatedThreatenFileReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOperatedThreatenFileReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOperatedThreatenFileReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDeleteSuccess

	if len(errors) > 0 {
		return ListOperatedThreatenFileReqMultiError(errors)
	}

	return nil
}

// ListOperatedThreatenFileReqMultiError is an error wrapping multiple
// validation errors returned by ListOperatedThreatenFileReq.ValidateAll() if
// the designated constraints aren't met.
type ListOperatedThreatenFileReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOperatedThreatenFileReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOperatedThreatenFileReqMultiError) AllErrors() []error { return m }

// ListOperatedThreatenFileReqValidationError is the validation error returned
// by ListOperatedThreatenFileReq.Validate if the designated constraints
// aren't met.
type ListOperatedThreatenFileReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOperatedThreatenFileReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOperatedThreatenFileReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOperatedThreatenFileReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOperatedThreatenFileReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOperatedThreatenFileReqValidationError) ErrorName() string {
	return "ListOperatedThreatenFileReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListOperatedThreatenFileReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOperatedThreatenFileReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOperatedThreatenFileReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOperatedThreatenFileReqValidationError{}

// Validate checks the field values on ListOperatedThreatenFileResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListOperatedThreatenFileResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOperatedThreatenFileResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListOperatedThreatenFileRespMultiError, or nil if none found.
func (m *ListOperatedThreatenFileResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOperatedThreatenFileResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListOperatedThreatenFileRespValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListOperatedThreatenFileRespValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListOperatedThreatenFileRespValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOperatedThreatenFileRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOperatedThreatenFileRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOperatedThreatenFileRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListOperatedThreatenFileRespMultiError(errors)
	}

	return nil
}

// ListOperatedThreatenFileRespMultiError is an error wrapping multiple
// validation errors returned by ListOperatedThreatenFileResp.ValidateAll() if
// the designated constraints aren't met.
type ListOperatedThreatenFileRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOperatedThreatenFileRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOperatedThreatenFileRespMultiError) AllErrors() []error { return m }

// ListOperatedThreatenFileRespValidationError is the validation error returned
// by ListOperatedThreatenFileResp.Validate if the designated constraints
// aren't met.
type ListOperatedThreatenFileRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOperatedThreatenFileRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOperatedThreatenFileRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOperatedThreatenFileRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOperatedThreatenFileRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOperatedThreatenFileRespValidationError) ErrorName() string {
	return "ListOperatedThreatenFileRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListOperatedThreatenFileRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOperatedThreatenFileResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOperatedThreatenFileRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOperatedThreatenFileRespValidationError{}

// Validate checks the field values on BatchDeleteFileOperationsFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteFileOperationsFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteFileOperationsFilter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchDeleteFileOperationsFilterMultiError, or nil if none found.
func (m *BatchDeleteFileOperationsFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteFileOperationsFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for FileSha256

	if len(errors) > 0 {
		return BatchDeleteFileOperationsFilterMultiError(errors)
	}

	return nil
}

// BatchDeleteFileOperationsFilterMultiError is an error wrapping multiple
// validation errors returned by BatchDeleteFileOperationsFilter.ValidateAll()
// if the designated constraints aren't met.
type BatchDeleteFileOperationsFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteFileOperationsFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteFileOperationsFilterMultiError) AllErrors() []error { return m }

// BatchDeleteFileOperationsFilterValidationError is the validation error
// returned by BatchDeleteFileOperationsFilter.Validate if the designated
// constraints aren't met.
type BatchDeleteFileOperationsFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteFileOperationsFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteFileOperationsFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteFileOperationsFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteFileOperationsFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteFileOperationsFilterValidationError) ErrorName() string {
	return "BatchDeleteFileOperationsFilterValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteFileOperationsFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteFileOperationsFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteFileOperationsFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteFileOperationsFilterValidationError{}

// Validate checks the field values on BatchDeleteFileOperationsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteFileOperationsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteFileOperationsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchDeleteFileOperationsReqMultiError, or nil if none found.
func (m *BatchDeleteFileOperationsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteFileOperationsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFilters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchDeleteFileOperationsReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchDeleteFileOperationsReqValidationError{
						field:  fmt.Sprintf("Filters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchDeleteFileOperationsReqValidationError{
					field:  fmt.Sprintf("Filters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchDeleteFileOperationsReqMultiError(errors)
	}

	return nil
}

// BatchDeleteFileOperationsReqMultiError is an error wrapping multiple
// validation errors returned by BatchDeleteFileOperationsReq.ValidateAll() if
// the designated constraints aren't met.
type BatchDeleteFileOperationsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteFileOperationsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteFileOperationsReqMultiError) AllErrors() []error { return m }

// BatchDeleteFileOperationsReqValidationError is the validation error returned
// by BatchDeleteFileOperationsReq.Validate if the designated constraints
// aren't met.
type BatchDeleteFileOperationsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteFileOperationsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteFileOperationsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteFileOperationsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteFileOperationsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteFileOperationsReqValidationError) ErrorName() string {
	return "BatchDeleteFileOperationsReqValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteFileOperationsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteFileOperationsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteFileOperationsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteFileOperationsReqValidationError{}

// Validate checks the field values on BatchDeleteFileOperationsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchDeleteFileOperationsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDeleteFileOperationsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchDeleteFileOperationsRespMultiError, or nil if none found.
func (m *BatchDeleteFileOperationsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDeleteFileOperationsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DeletedCount

	if len(errors) > 0 {
		return BatchDeleteFileOperationsRespMultiError(errors)
	}

	return nil
}

// BatchDeleteFileOperationsRespMultiError is an error wrapping multiple
// validation errors returned by BatchDeleteFileOperationsResp.ValidateAll()
// if the designated constraints aren't met.
type BatchDeleteFileOperationsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDeleteFileOperationsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDeleteFileOperationsRespMultiError) AllErrors() []error { return m }

// BatchDeleteFileOperationsRespValidationError is the validation error
// returned by BatchDeleteFileOperationsResp.Validate if the designated
// constraints aren't met.
type BatchDeleteFileOperationsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDeleteFileOperationsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDeleteFileOperationsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDeleteFileOperationsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDeleteFileOperationsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDeleteFileOperationsRespValidationError) ErrorName() string {
	return "BatchDeleteFileOperationsRespValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDeleteFileOperationsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDeleteFileOperationsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDeleteFileOperationsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDeleteFileOperationsRespValidationError{}

// Validate checks the field values on ListAgentFileOperationReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAgentFileOperationReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAgentFileOperationReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAgentFileOperationReqMultiError, or nil if none found.
func (m *ListAgentFileOperationReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAgentFileOperationReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for FileSha256

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAgentFileOperationReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAgentFileOperationReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAgentFileOperationReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAgentFileOperationReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAgentFileOperationReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAgentFileOperationReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAgentFileOperationReqMultiError(errors)
	}

	return nil
}

// ListAgentFileOperationReqMultiError is an error wrapping multiple validation
// errors returned by ListAgentFileOperationReq.ValidateAll() if the
// designated constraints aren't met.
type ListAgentFileOperationReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAgentFileOperationReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAgentFileOperationReqMultiError) AllErrors() []error { return m }

// ListAgentFileOperationReqValidationError is the validation error returned by
// ListAgentFileOperationReq.Validate if the designated constraints aren't met.
type ListAgentFileOperationReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAgentFileOperationReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAgentFileOperationReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAgentFileOperationReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAgentFileOperationReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAgentFileOperationReqValidationError) ErrorName() string {
	return "ListAgentFileOperationReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListAgentFileOperationReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAgentFileOperationReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAgentFileOperationReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAgentFileOperationReqValidationError{}

// Validate checks the field values on ListAgentFileOperationResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAgentFileOperationResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAgentFileOperationResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAgentFileOperationRespMultiError, or nil if none found.
func (m *ListAgentFileOperationResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAgentFileOperationResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFileOperations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAgentFileOperationRespValidationError{
						field:  fmt.Sprintf("FileOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAgentFileOperationRespValidationError{
						field:  fmt.Sprintf("FileOperations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAgentFileOperationRespValidationError{
					field:  fmt.Sprintf("FileOperations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAgentFileOperationRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAgentFileOperationRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAgentFileOperationRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAgentFileOperationRespMultiError(errors)
	}

	return nil
}

// ListAgentFileOperationRespMultiError is an error wrapping multiple
// validation errors returned by ListAgentFileOperationResp.ValidateAll() if
// the designated constraints aren't met.
type ListAgentFileOperationRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAgentFileOperationRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAgentFileOperationRespMultiError) AllErrors() []error { return m }

// ListAgentFileOperationRespValidationError is the validation error returned
// by ListAgentFileOperationResp.Validate if the designated constraints aren't met.
type ListAgentFileOperationRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAgentFileOperationRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAgentFileOperationRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAgentFileOperationRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAgentFileOperationRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAgentFileOperationRespValidationError) ErrorName() string {
	return "ListAgentFileOperationRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListAgentFileOperationRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAgentFileOperationResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAgentFileOperationRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAgentFileOperationRespValidationError{}

// Validate checks the field values on AgentFileOperation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgentFileOperation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentFileOperation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgentFileOperationMultiError, or nil if none found.
func (m *AgentFileOperation) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentFileOperation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for FileSha256

	// no validation rules for FilePath

	// no validation rules for FileKey

	// no validation rules for OperationType

	// no validation rules for OperationStatus

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AgentFileOperationValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AgentFileOperationValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AgentFileOperationValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AgentFileOperationMultiError(errors)
	}

	return nil
}

// AgentFileOperationMultiError is an error wrapping multiple validation errors
// returned by AgentFileOperation.ValidateAll() if the designated constraints
// aren't met.
type AgentFileOperationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentFileOperationMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentFileOperationMultiError) AllErrors() []error { return m }

// AgentFileOperationValidationError is the validation error returned by
// AgentFileOperation.Validate if the designated constraints aren't met.
type AgentFileOperationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentFileOperationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentFileOperationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentFileOperationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentFileOperationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentFileOperationValidationError) ErrorName() string {
	return "AgentFileOperationValidationError"
}

// Error satisfies the builtin error interface
func (e AgentFileOperationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentFileOperation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentFileOperationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentFileOperationValidationError{}
