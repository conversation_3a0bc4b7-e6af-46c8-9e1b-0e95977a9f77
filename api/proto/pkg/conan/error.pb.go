// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/error.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 50001 ~ 50999 为conan定义的错误码
// 线索 - 50001 - 50100
// 处置/白名单 - 50101 - 50200
// 取证 - 50201 - 50300
type Codes int32

const (
	Codes_UNDEFINED                    Codes = 0
	Codes_CLUE_NOT_FOUND               Codes = 50001
	Codes_BWPolicyNotFound             Codes = 50101 // 白名单策略不存在
	Codes_EVIDENCE_TASK_NOT_FOUND      Codes = 50201 // 取证任务不存在
	Codes_EVIDENCE_TASK_FILE_TOO_LARGE Codes = 50202 // 取证任务文件太大
	Codes_EVIDENCE_TASK_ALREADY_EXISTS Codes = 50203 // 取证任务已存在
	Codes_ADD_EVIDENCE_TASK_FAILED     Codes = 50204 // 添加取证任务失败
	Codes_CLUE_EVIDENCE_NOT_FOUND      Codes = 50205 // 线索证据不存在
	Codes_TIME_OUT_OF_RANGE            Codes = 50206 // 参数错误, 传入时间为未来的时间
	Codes_EXTRACTION_TIME_HAS_EXPIRED  Codes = 50207 // 证据已过期
	Codes_NOT_SUPPORT_EVIDENCE_TYPE    Codes = 50208 // 不支持的取证类型
	Codes_EVIDENCE_RESULT_PUSH_FAILED  Codes = 50209 // 证据结果推送失败
	Codes_EVIDENCE_HOST_NOT_FOUND      Codes = 50210 // 取证任务未关联终端
	Codes_EVIDENCE_ALREADY_EXISTS      Codes = 50211 // 证据已存在
	Codes_HOST_NOT_FOUND               Codes = 50212 // 终端不存在
	Codes_HOST_OFFLINE                 Codes = 50213 // 终端已离线
	Codes_NO_CLUE_TYPE_FOR_X01         Codes = 50214 // 未找到与X01对应的线索类型
	Codes_PARSE_CSV_FAILED             Codes = 50215 // 解析csv文件失败
	Codes_BATCH_FILE_EVIDENCE_EXISTS   Codes = 50216 // 导入的csv文件对应的所有文件证据均已存在
)

// Enum value maps for Codes.
var (
	Codes_name = map[int32]string{
		0:     "UNDEFINED",
		50001: "CLUE_NOT_FOUND",
		50101: "BWPolicyNotFound",
		50201: "EVIDENCE_TASK_NOT_FOUND",
		50202: "EVIDENCE_TASK_FILE_TOO_LARGE",
		50203: "EVIDENCE_TASK_ALREADY_EXISTS",
		50204: "ADD_EVIDENCE_TASK_FAILED",
		50205: "CLUE_EVIDENCE_NOT_FOUND",
		50206: "TIME_OUT_OF_RANGE",
		50207: "EXTRACTION_TIME_HAS_EXPIRED",
		50208: "NOT_SUPPORT_EVIDENCE_TYPE",
		50209: "EVIDENCE_RESULT_PUSH_FAILED",
		50210: "EVIDENCE_HOST_NOT_FOUND",
		50211: "EVIDENCE_ALREADY_EXISTS",
		50212: "HOST_NOT_FOUND",
		50213: "HOST_OFFLINE",
		50214: "NO_CLUE_TYPE_FOR_X01",
		50215: "PARSE_CSV_FAILED",
		50216: "BATCH_FILE_EVIDENCE_EXISTS",
	}
	Codes_value = map[string]int32{
		"UNDEFINED":                    0,
		"CLUE_NOT_FOUND":               50001,
		"BWPolicyNotFound":             50101,
		"EVIDENCE_TASK_NOT_FOUND":      50201,
		"EVIDENCE_TASK_FILE_TOO_LARGE": 50202,
		"EVIDENCE_TASK_ALREADY_EXISTS": 50203,
		"ADD_EVIDENCE_TASK_FAILED":     50204,
		"CLUE_EVIDENCE_NOT_FOUND":      50205,
		"TIME_OUT_OF_RANGE":            50206,
		"EXTRACTION_TIME_HAS_EXPIRED":  50207,
		"NOT_SUPPORT_EVIDENCE_TYPE":    50208,
		"EVIDENCE_RESULT_PUSH_FAILED":  50209,
		"EVIDENCE_HOST_NOT_FOUND":      50210,
		"EVIDENCE_ALREADY_EXISTS":      50211,
		"HOST_NOT_FOUND":               50212,
		"HOST_OFFLINE":                 50213,
		"NO_CLUE_TYPE_FOR_X01":         50214,
		"PARSE_CSV_FAILED":             50215,
		"BATCH_FILE_EVIDENCE_EXISTS":   50216,
	}
)

func (x Codes) Enum() *Codes {
	p := new(Codes)
	*p = x
	return p
}

func (x Codes) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Codes) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_error_proto_enumTypes[0].Descriptor()
}

func (Codes) Type() protoreflect.EnumType {
	return &file_conan_error_proto_enumTypes[0]
}

func (x Codes) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Codes.Descriptor instead.
func (Codes) EnumDescriptor() ([]byte, []int) {
	return file_conan_error_proto_rawDescGZIP(), []int{0}
}

var File_conan_error_proto protoreflect.FileDescriptor

var file_conan_error_proto_rawDesc = []byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2a, 0xa8, 0x04, 0x0a, 0x05, 0x43,
	0x6f, 0x64, 0x65, 0x73, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x44, 0x45, 0x46, 0x49, 0x4e, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x0e, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xd1, 0x86, 0x03, 0x12, 0x16, 0x0a, 0x10, 0x42, 0x57, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x6f, 0x74, 0x46, 0x6f, 0x75, 0x6e, 0x64, 0x10, 0xb5, 0x87,
	0x03, 0x12, 0x1d, 0x0a, 0x17, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x41,
	0x53, 0x4b, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x99, 0x88, 0x03,
	0x12, 0x22, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53,
	0x4b, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x54, 0x4f, 0x4f, 0x5f, 0x4c, 0x41, 0x52, 0x47, 0x45,
	0x10, 0x9a, 0x88, 0x03, 0x12, 0x22, 0x0a, 0x1c, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45,
	0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58,
	0x49, 0x53, 0x54, 0x53, 0x10, 0x9b, 0x88, 0x03, 0x12, 0x1e, 0x0a, 0x18, 0x41, 0x44, 0x44, 0x5f,
	0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x9c, 0x88, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x43, 0x4c, 0x55, 0x45,
	0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f,
	0x55, 0x4e, 0x44, 0x10, 0x9d, 0x88, 0x03, 0x12, 0x17, 0x0a, 0x11, 0x54, 0x49, 0x4d, 0x45, 0x5f,
	0x4f, 0x55, 0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x9e, 0x88, 0x03,
	0x12, 0x21, 0x0a, 0x1b, 0x45, 0x58, 0x54, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54,
	0x49, 0x4d, 0x45, 0x5f, 0x48, 0x41, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10,
	0x9f, 0x88, 0x03, 0x12, 0x1f, 0x0a, 0x19, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x10, 0xa0, 0x88, 0x03, 0x12, 0x21, 0x0a, 0x1b, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45,
	0x5f, 0x52, 0x45, 0x53, 0x55, 0x4c, 0x54, 0x5f, 0x50, 0x55, 0x53, 0x48, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0xa1, 0x88, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x45, 0x56, 0x49, 0x44, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55,
	0x4e, 0x44, 0x10, 0xa2, 0x88, 0x03, 0x12, 0x1d, 0x0a, 0x17, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x43, 0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x53, 0x10, 0xa3, 0x88, 0x03, 0x12, 0x14, 0x0a, 0x0e, 0x48, 0x4f, 0x53, 0x54, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0xa4, 0x88, 0x03, 0x12, 0x12, 0x0a, 0x0c, 0x48,
	0x4f, 0x53, 0x54, 0x5f, 0x4f, 0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0xa5, 0x88, 0x03, 0x12,
	0x1a, 0x0a, 0x14, 0x4e, 0x4f, 0x5f, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x58, 0x30, 0x31, 0x10, 0xa6, 0x88, 0x03, 0x12, 0x16, 0x0a, 0x10, 0x50,
	0x41, 0x52, 0x53, 0x45, 0x5f, 0x43, 0x53, 0x56, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0xa7, 0x88, 0x03, 0x12, 0x20, 0x0a, 0x1a, 0x42, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x45, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x43, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x53, 0x10, 0xa8, 0x88, 0x03, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78,
	0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conan_error_proto_rawDescOnce sync.Once
	file_conan_error_proto_rawDescData = file_conan_error_proto_rawDesc
)

func file_conan_error_proto_rawDescGZIP() []byte {
	file_conan_error_proto_rawDescOnce.Do(func() {
		file_conan_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_error_proto_rawDescData)
	})
	return file_conan_error_proto_rawDescData
}

var file_conan_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_conan_error_proto_goTypes = []any{
	(Codes)(0), // 0: conan.Codes
}
var file_conan_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_conan_error_proto_init() }
func file_conan_error_proto_init() {
	if File_conan_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conan_error_proto_goTypes,
		DependencyIndexes: file_conan_error_proto_depIdxs,
		EnumInfos:         file_conan_error_proto_enumTypes,
	}.Build()
	File_conan_error_proto = out.File
	file_conan_error_proto_rawDesc = nil
	file_conan_error_proto_goTypes = nil
	file_conan_error_proto_depIdxs = nil
}
