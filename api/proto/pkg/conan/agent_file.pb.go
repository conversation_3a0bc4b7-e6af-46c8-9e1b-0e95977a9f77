// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/agent_file.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FileOperationType int32

const (
	FileOperationType_FOT_UNKNOWN FileOperationType = 0
	FileOperationType_ISOLATE     FileOperationType = 1
	FileOperationType_DELETE      FileOperationType = 2
	FileOperationType_RESTORE     FileOperationType = 3
	FileOperationType_EXPIRE      FileOperationType = 4
)

// Enum value maps for FileOperationType.
var (
	FileOperationType_name = map[int32]string{
		0: "FOT_UNKNOWN",
		1: "ISOLATE",
		2: "DELETE",
		3: "RESTORE",
		4: "EXPIRE",
	}
	FileOperationType_value = map[string]int32{
		"FOT_UNKNOWN": 0,
		"ISOLATE":     1,
		"DELETE":      2,
		"RESTORE":     3,
		"EXPIRE":      4,
	}
)

func (x FileOperationType) Enum() *FileOperationType {
	p := new(FileOperationType)
	*p = x
	return p
}

func (x FileOperationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileOperationType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_agent_file_proto_enumTypes[0].Descriptor()
}

func (FileOperationType) Type() protoreflect.EnumType {
	return &file_conan_agent_file_proto_enumTypes[0]
}

func (x FileOperationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileOperationType.Descriptor instead.
func (FileOperationType) EnumDescriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{0}
}

type FileOperateStatus int32

const (
	FileOperateStatus_FOS_UNKNOWN FileOperateStatus = 0
	FileOperateStatus_PENDING     FileOperateStatus = 1
	FileOperateStatus_PROCESSING  FileOperateStatus = 2
	FileOperateStatus_SUCCESS     FileOperateStatus = 3
	FileOperateStatus_FAILED      FileOperateStatus = 4
)

// Enum value maps for FileOperateStatus.
var (
	FileOperateStatus_name = map[int32]string{
		0: "FOS_UNKNOWN",
		1: "PENDING",
		2: "PROCESSING",
		3: "SUCCESS",
		4: "FAILED",
	}
	FileOperateStatus_value = map[string]int32{
		"FOS_UNKNOWN": 0,
		"PENDING":     1,
		"PROCESSING":  2,
		"SUCCESS":     3,
		"FAILED":      4,
	}
)

func (x FileOperateStatus) Enum() *FileOperateStatus {
	p := new(FileOperateStatus)
	*p = x
	return p
}

func (x FileOperateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileOperateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_agent_file_proto_enumTypes[1].Descriptor()
}

func (FileOperateStatus) Type() protoreflect.EnumType {
	return &file_conan_agent_file_proto_enumTypes[1]
}

func (x FileOperateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileOperateStatus.Descriptor instead.
func (FileOperateStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{1}
}

type HandleAgentFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId      string            `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileSha256     string            `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	FileKey        string            `protobuf:"bytes,3,opt,name=file_key,json=fileKey,proto3" json:"file_key,omitempty"`
	Online         int64             `protobuf:"varint,4,opt,name=online,proto3" json:"online,omitempty"`
	OperateType    FileOperationType `protobuf:"varint,5,opt,name=operate_type,json=operateType,proto3,enum=conan.FileOperationType" json:"operate_type,omitempty"`
	EvidenceTaskId int64             `protobuf:"varint,6,opt,name=evidence_task_id,json=evidenceTaskId,proto3" json:"evidence_task_id,omitempty"`
}

func (x *HandleAgentFileReq) Reset() {
	*x = HandleAgentFileReq{}
	mi := &file_conan_agent_file_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleAgentFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleAgentFileReq) ProtoMessage() {}

func (x *HandleAgentFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleAgentFileReq.ProtoReflect.Descriptor instead.
func (*HandleAgentFileReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{0}
}

func (x *HandleAgentFileReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *HandleAgentFileReq) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *HandleAgentFileReq) GetFileKey() string {
	if x != nil {
		return x.FileKey
	}
	return ""
}

func (x *HandleAgentFileReq) GetOnline() int64 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *HandleAgentFileReq) GetOperateType() FileOperationType {
	if x != nil {
		return x.OperateType
	}
	return FileOperationType_FOT_UNKNOWN
}

func (x *HandleAgentFileReq) GetEvidenceTaskId() int64 {
	if x != nil {
		return x.EvidenceTaskId
	}
	return 0
}

type HandleAgentFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HandleAgentFileResp) Reset() {
	*x = HandleAgentFileResp{}
	mi := &file_conan_agent_file_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleAgentFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleAgentFileResp) ProtoMessage() {}

func (x *HandleAgentFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleAgentFileResp.ProtoReflect.Descriptor instead.
func (*HandleAgentFileResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{1}
}

type BatchHandleAgentFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*HandleAgentFileReq `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *BatchHandleAgentFileReq) Reset() {
	*x = BatchHandleAgentFileReq{}
	mi := &file_conan_agent_file_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchHandleAgentFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHandleAgentFileReq) ProtoMessage() {}

func (x *BatchHandleAgentFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHandleAgentFileReq.ProtoReflect.Descriptor instead.
func (*BatchHandleAgentFileReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{2}
}

func (x *BatchHandleAgentFileReq) GetList() []*HandleAgentFileReq {
	if x != nil {
		return x.List
	}
	return nil
}

type BatchHandleAgentFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BatchHandleAgentFileResp) Reset() {
	*x = BatchHandleAgentFileResp{}
	mi := &file_conan_agent_file_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchHandleAgentFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchHandleAgentFileResp) ProtoMessage() {}

func (x *BatchHandleAgentFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchHandleAgentFileResp.ProtoReflect.Descriptor instead.
func (*BatchHandleAgentFileResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{3}
}

type RetryHandleAgentFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId   string            `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileSha256  string            `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	FileKey     string            `protobuf:"bytes,3,opt,name=file_key,json=fileKey,proto3" json:"file_key,omitempty"`
	Online      int64             `protobuf:"varint,4,opt,name=online,proto3" json:"online,omitempty"`
	OperateType FileOperationType `protobuf:"varint,5,opt,name=operate_type,json=operateType,proto3,enum=conan.FileOperationType" json:"operate_type,omitempty"`
	Password    string            `protobuf:"bytes,6,opt,name=password,proto3" json:"password,omitempty"`
}

func (x *RetryHandleAgentFileReq) Reset() {
	*x = RetryHandleAgentFileReq{}
	mi := &file_conan_agent_file_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryHandleAgentFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryHandleAgentFileReq) ProtoMessage() {}

func (x *RetryHandleAgentFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryHandleAgentFileReq.ProtoReflect.Descriptor instead.
func (*RetryHandleAgentFileReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{4}
}

func (x *RetryHandleAgentFileReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *RetryHandleAgentFileReq) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *RetryHandleAgentFileReq) GetFileKey() string {
	if x != nil {
		return x.FileKey
	}
	return ""
}

func (x *RetryHandleAgentFileReq) GetOnline() int64 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *RetryHandleAgentFileReq) GetOperateType() FileOperationType {
	if x != nil {
		return x.OperateType
	}
	return FileOperationType_FOT_UNKNOWN
}

func (x *RetryHandleAgentFileReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type RetryHandleAgentFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Password string                `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty"`
	List     []*HandleAgentFileReq `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *RetryHandleAgentFileResp) Reset() {
	*x = RetryHandleAgentFileResp{}
	mi := &file_conan_agent_file_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryHandleAgentFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryHandleAgentFileResp) ProtoMessage() {}

func (x *RetryHandleAgentFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryHandleAgentFileResp.ProtoReflect.Descriptor instead.
func (*RetryHandleAgentFileResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{5}
}

func (x *RetryHandleAgentFileResp) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RetryHandleAgentFileResp) GetList() []*HandleAgentFileReq {
	if x != nil {
		return x.List
	}
	return nil
}

type OperatedThreatenFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClueType         ClueType               `protobuf:"varint,1,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	ClueSubType      int32                  `protobuf:"varint,2,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	ClueStatus       int32                  `protobuf:"varint,3,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	MachineId        string                 `protobuf:"bytes,4,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileKey          string                 `protobuf:"bytes,5,opt,name=file_key,json=fileKey,proto3" json:"file_key,omitempty"`
	FileMd5          string                 `protobuf:"bytes,6,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	FileSha256       string                 `protobuf:"bytes,7,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	FileName         string                 `protobuf:"bytes,8,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FilePath         string                 `protobuf:"bytes,9,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileType         int32                  `protobuf:"varint,10,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileSize         int32                  `protobuf:"varint,11,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	OperationType    int32                  `protobuf:"varint,12,opt,name=operation_type,json=operationType,proto3" json:"operation_type,omitempty"`
	OperationStatus  int32                  `protobuf:"varint,13,opt,name=operation_status,json=operationStatus,proto3" json:"operation_status,omitempty"`
	OperationFailure int32                  `protobuf:"varint,14,opt,name=operation_failure,json=operationFailure,proto3" json:"operation_failure,omitempty"`
	FileSource       int32                  `protobuf:"varint,15,opt,name=file_source,json=fileSource,proto3" json:"file_source,omitempty"`
	EvidenceTaskId   int64                  `protobuf:"varint,16,opt,name=evidence_task_id,json=evidenceTaskId,proto3" json:"evidence_task_id,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *OperatedThreatenFile) Reset() {
	*x = OperatedThreatenFile{}
	mi := &file_conan_agent_file_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OperatedThreatenFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OperatedThreatenFile) ProtoMessage() {}

func (x *OperatedThreatenFile) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OperatedThreatenFile.ProtoReflect.Descriptor instead.
func (*OperatedThreatenFile) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{6}
}

func (x *OperatedThreatenFile) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *OperatedThreatenFile) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *OperatedThreatenFile) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *OperatedThreatenFile) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *OperatedThreatenFile) GetFileKey() string {
	if x != nil {
		return x.FileKey
	}
	return ""
}

func (x *OperatedThreatenFile) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *OperatedThreatenFile) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *OperatedThreatenFile) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *OperatedThreatenFile) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *OperatedThreatenFile) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *OperatedThreatenFile) GetFileSize() int32 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *OperatedThreatenFile) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *OperatedThreatenFile) GetOperationStatus() int32 {
	if x != nil {
		return x.OperationStatus
	}
	return 0
}

func (x *OperatedThreatenFile) GetOperationFailure() int32 {
	if x != nil {
		return x.OperationFailure
	}
	return 0
}

func (x *OperatedThreatenFile) GetFileSource() int32 {
	if x != nil {
		return x.FileSource
	}
	return 0
}

func (x *OperatedThreatenFile) GetEvidenceTaskId() int64 {
	if x != nil {
		return x.EvidenceTaskId
	}
	return 0
}

func (x *OperatedThreatenFile) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type ListOperatedThreatenFileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineIds []string     `protobuf:"bytes,1,rep,name=machine_ids,json=machineIds,proto3" json:"machine_ids,omitempty"`
	FileSha256 string       `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	Filename   string       `protobuf:"bytes,3,opt,name=filename,proto3" json:"filename,omitempty"`
	TimeRange  *TimeRange   `protobuf:"bytes,4,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Page       *PageRequest `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty"`
	// TRUE: 查询删除成功的文件
	// FALSE: 查询除删除成功外的其他文件
	IsDeleteSuccess bool `protobuf:"varint,6,opt,name=is_delete_success,json=isDeleteSuccess,proto3" json:"is_delete_success,omitempty"`
}

func (x *ListOperatedThreatenFileReq) Reset() {
	*x = ListOperatedThreatenFileReq{}
	mi := &file_conan_agent_file_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOperatedThreatenFileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOperatedThreatenFileReq) ProtoMessage() {}

func (x *ListOperatedThreatenFileReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOperatedThreatenFileReq.ProtoReflect.Descriptor instead.
func (*ListOperatedThreatenFileReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{7}
}

func (x *ListOperatedThreatenFileReq) GetMachineIds() []string {
	if x != nil {
		return x.MachineIds
	}
	return nil
}

func (x *ListOperatedThreatenFileReq) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *ListOperatedThreatenFileReq) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *ListOperatedThreatenFileReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListOperatedThreatenFileReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListOperatedThreatenFileReq) GetIsDeleteSuccess() bool {
	if x != nil {
		return x.IsDeleteSuccess
	}
	return false
}

type ListOperatedThreatenFileResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Files []*OperatedThreatenFile `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
	Page  *PageResponse           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListOperatedThreatenFileResp) Reset() {
	*x = ListOperatedThreatenFileResp{}
	mi := &file_conan_agent_file_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOperatedThreatenFileResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOperatedThreatenFileResp) ProtoMessage() {}

func (x *ListOperatedThreatenFileResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOperatedThreatenFileResp.ProtoReflect.Descriptor instead.
func (*ListOperatedThreatenFileResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{8}
}

func (x *ListOperatedThreatenFileResp) GetFiles() []*OperatedThreatenFile {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *ListOperatedThreatenFileResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type BatchDeleteFileOperationsFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId  string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileSha256 string `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
}

func (x *BatchDeleteFileOperationsFilter) Reset() {
	*x = BatchDeleteFileOperationsFilter{}
	mi := &file_conan_agent_file_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDeleteFileOperationsFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteFileOperationsFilter) ProtoMessage() {}

func (x *BatchDeleteFileOperationsFilter) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteFileOperationsFilter.ProtoReflect.Descriptor instead.
func (*BatchDeleteFileOperationsFilter) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{9}
}

func (x *BatchDeleteFileOperationsFilter) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *BatchDeleteFileOperationsFilter) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

type BatchDeleteFileOperationsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters []*BatchDeleteFileOperationsFilter `protobuf:"bytes,1,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *BatchDeleteFileOperationsReq) Reset() {
	*x = BatchDeleteFileOperationsReq{}
	mi := &file_conan_agent_file_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDeleteFileOperationsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteFileOperationsReq) ProtoMessage() {}

func (x *BatchDeleteFileOperationsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteFileOperationsReq.ProtoReflect.Descriptor instead.
func (*BatchDeleteFileOperationsReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{10}
}

func (x *BatchDeleteFileOperationsReq) GetFilters() []*BatchDeleteFileOperationsFilter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type BatchDeleteFileOperationsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DeletedCount int64 `protobuf:"varint,1,opt,name=deleted_count,json=deletedCount,proto3" json:"deleted_count,omitempty"`
}

func (x *BatchDeleteFileOperationsResp) Reset() {
	*x = BatchDeleteFileOperationsResp{}
	mi := &file_conan_agent_file_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchDeleteFileOperationsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDeleteFileOperationsResp) ProtoMessage() {}

func (x *BatchDeleteFileOperationsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDeleteFileOperationsResp.ProtoReflect.Descriptor instead.
func (*BatchDeleteFileOperationsResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{11}
}

func (x *BatchDeleteFileOperationsResp) GetDeletedCount() int64 {
	if x != nil {
		return x.DeletedCount
	}
	return 0
}

type ListAgentFileOperationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId  string       `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileSha256 string       `protobuf:"bytes,2,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	TimeRange  *TimeRange   `protobuf:"bytes,4,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Page       *PageRequest `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListAgentFileOperationReq) Reset() {
	*x = ListAgentFileOperationReq{}
	mi := &file_conan_agent_file_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentFileOperationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentFileOperationReq) ProtoMessage() {}

func (x *ListAgentFileOperationReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentFileOperationReq.ProtoReflect.Descriptor instead.
func (*ListAgentFileOperationReq) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{12}
}

func (x *ListAgentFileOperationReq) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *ListAgentFileOperationReq) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *ListAgentFileOperationReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListAgentFileOperationReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListAgentFileOperationResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileOperations []*AgentFileOperation `protobuf:"bytes,1,rep,name=file_operations,json=fileOperations,proto3" json:"file_operations,omitempty"`
	Page           *PageResponse         `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListAgentFileOperationResp) Reset() {
	*x = ListAgentFileOperationResp{}
	mi := &file_conan_agent_file_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAgentFileOperationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAgentFileOperationResp) ProtoMessage() {}

func (x *ListAgentFileOperationResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAgentFileOperationResp.ProtoReflect.Descriptor instead.
func (*ListAgentFileOperationResp) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{13}
}

func (x *ListAgentFileOperationResp) GetFileOperations() []*AgentFileOperation {
	if x != nil {
		return x.FileOperations
	}
	return nil
}

func (x *ListAgentFileOperationResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AgentFileOperation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId       string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	FileSha256      string                 `protobuf:"bytes,3,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	FilePath        string                 `protobuf:"bytes,4,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	FileKey         string                 `protobuf:"bytes,5,opt,name=file_key,json=fileKey,proto3" json:"file_key,omitempty"`
	OperationType   FileOperationType      `protobuf:"varint,6,opt,name=operation_type,json=operationType,proto3,enum=conan.FileOperationType" json:"operation_type,omitempty"`
	OperationStatus FileOperateStatus      `protobuf:"varint,7,opt,name=operation_status,json=operationStatus,proto3,enum=conan.FileOperateStatus" json:"operation_status,omitempty"`
	CreateTime      *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *AgentFileOperation) Reset() {
	*x = AgentFileOperation{}
	mi := &file_conan_agent_file_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentFileOperation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentFileOperation) ProtoMessage() {}

func (x *AgentFileOperation) ProtoReflect() protoreflect.Message {
	mi := &file_conan_agent_file_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentFileOperation.ProtoReflect.Descriptor instead.
func (*AgentFileOperation) Descriptor() ([]byte, []int) {
	return file_conan_agent_file_proto_rawDescGZIP(), []int{14}
}

func (x *AgentFileOperation) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AgentFileOperation) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AgentFileOperation) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *AgentFileOperation) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *AgentFileOperation) GetFileKey() string {
	if x != nil {
		return x.FileKey
	}
	return ""
}

func (x *AgentFileOperation) GetOperationType() FileOperationType {
	if x != nil {
		return x.OperationType
	}
	return FileOperationType_FOT_UNKNOWN
}

func (x *AgentFileOperation) GetOperationStatus() FileOperateStatus {
	if x != nil {
		return x.OperationStatus
	}
	return FileOperateStatus_FOS_UNKNOWN
}

func (x *AgentFileOperation) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

var File_conan_agent_file_proto protoreflect.FileDescriptor

var file_conan_agent_file_proto_rawDesc = []byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a,
	0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x01, 0x0a, 0x12, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x19, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66,
	0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x3b,
	0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65,
	0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x22, 0x48, 0x0a, 0x17,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x48, 0x61,
	0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x1a, 0x0a, 0x18, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48,
	0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x22, 0xe5, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x74, 0x72, 0x79, 0x48, 0x61, 0x6e, 0x64,
	0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x3b, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x65, 0x0a, 0x18, 0x52, 0x65,
	0x74, 0x72, 0x79, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x2d, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xf8, 0x04, 0x0a, 0x14, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c,
	0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6d, 0x64, 0x35, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d,
	0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1b, 0x0a,
	0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29,
	0x0a, 0x10, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x66, 0x69, 0x6c,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0e, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x80, 0x02, 0x0a,
	0x1b, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x68, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x1f, 0x0a, 0x0b,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1a,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x5f, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x69, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22,
	0x7a, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x31, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x61, 0x0a, 0x1f, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x22, 0x60,
	0x0a, 0x1c, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x12, 0x40,
	0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73,
	0x22, 0x44, 0x0a, 0x1d, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb4, 0x01, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x89, 0x01,
	0x0a, 0x1a, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x42, 0x0a, 0x0f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xdf, 0x02, 0x0a, 0x12, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x19, 0x0a,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x66, 0x69, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x3f, 0x0a, 0x0e, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0f, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x2a, 0x56, 0x0a, 0x11, 0x46,
	0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x53, 0x4f, 0x4c, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x0a,
	0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45,
	0x53, 0x54, 0x4f, 0x52, 0x45, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x58, 0x50, 0x49, 0x52,
	0x45, 0x10, 0x04, 0x2a, 0x5a, 0x0a, 0x11, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x4f, 0x53, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x43, 0x45, 0x53,
	0x53, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x32,
	0xbc, 0x04, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x48, 0x0a, 0x0f, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x48, 0x61, 0x6e, 0x64, 0x6c,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57,
	0x0a, 0x14, 0x42, 0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x57, 0x0a, 0x14, 0x52, 0x65, 0x74, 0x72, 0x79,
	0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x65, 0x74, 0x72, 0x79, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x64, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x22, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x66, 0x0a, 0x19, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x5e,
	0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2a,
	0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_conan_agent_file_proto_rawDescOnce sync.Once
	file_conan_agent_file_proto_rawDescData = file_conan_agent_file_proto_rawDesc
)

func file_conan_agent_file_proto_rawDescGZIP() []byte {
	file_conan_agent_file_proto_rawDescOnce.Do(func() {
		file_conan_agent_file_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_agent_file_proto_rawDescData)
	})
	return file_conan_agent_file_proto_rawDescData
}

var file_conan_agent_file_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_conan_agent_file_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_conan_agent_file_proto_goTypes = []any{
	(FileOperationType)(0),                  // 0: conan.FileOperationType
	(FileOperateStatus)(0),                  // 1: conan.FileOperateStatus
	(*HandleAgentFileReq)(nil),              // 2: conan.HandleAgentFileReq
	(*HandleAgentFileResp)(nil),             // 3: conan.HandleAgentFileResp
	(*BatchHandleAgentFileReq)(nil),         // 4: conan.BatchHandleAgentFileReq
	(*BatchHandleAgentFileResp)(nil),        // 5: conan.BatchHandleAgentFileResp
	(*RetryHandleAgentFileReq)(nil),         // 6: conan.RetryHandleAgentFileReq
	(*RetryHandleAgentFileResp)(nil),        // 7: conan.RetryHandleAgentFileResp
	(*OperatedThreatenFile)(nil),            // 8: conan.OperatedThreatenFile
	(*ListOperatedThreatenFileReq)(nil),     // 9: conan.ListOperatedThreatenFileReq
	(*ListOperatedThreatenFileResp)(nil),    // 10: conan.ListOperatedThreatenFileResp
	(*BatchDeleteFileOperationsFilter)(nil), // 11: conan.BatchDeleteFileOperationsFilter
	(*BatchDeleteFileOperationsReq)(nil),    // 12: conan.BatchDeleteFileOperationsReq
	(*BatchDeleteFileOperationsResp)(nil),   // 13: conan.BatchDeleteFileOperationsResp
	(*ListAgentFileOperationReq)(nil),       // 14: conan.ListAgentFileOperationReq
	(*ListAgentFileOperationResp)(nil),      // 15: conan.ListAgentFileOperationResp
	(*AgentFileOperation)(nil),              // 16: conan.AgentFileOperation
	(ClueType)(0),                           // 17: conan.ClueType
	(*timestamppb.Timestamp)(nil),           // 18: google.protobuf.Timestamp
	(*TimeRange)(nil),                       // 19: conan.TimeRange
	(*PageRequest)(nil),                     // 20: conan.PageRequest
	(*PageResponse)(nil),                    // 21: conan.PageResponse
}
var file_conan_agent_file_proto_depIdxs = []int32{
	0,  // 0: conan.HandleAgentFileReq.operate_type:type_name -> conan.FileOperationType
	2,  // 1: conan.BatchHandleAgentFileReq.list:type_name -> conan.HandleAgentFileReq
	0,  // 2: conan.RetryHandleAgentFileReq.operate_type:type_name -> conan.FileOperationType
	2,  // 3: conan.RetryHandleAgentFileResp.list:type_name -> conan.HandleAgentFileReq
	17, // 4: conan.OperatedThreatenFile.clue_type:type_name -> conan.ClueType
	18, // 5: conan.OperatedThreatenFile.created_at:type_name -> google.protobuf.Timestamp
	19, // 6: conan.ListOperatedThreatenFileReq.time_range:type_name -> conan.TimeRange
	20, // 7: conan.ListOperatedThreatenFileReq.page:type_name -> conan.PageRequest
	8,  // 8: conan.ListOperatedThreatenFileResp.files:type_name -> conan.OperatedThreatenFile
	21, // 9: conan.ListOperatedThreatenFileResp.page:type_name -> conan.PageResponse
	11, // 10: conan.BatchDeleteFileOperationsReq.filters:type_name -> conan.BatchDeleteFileOperationsFilter
	19, // 11: conan.ListAgentFileOperationReq.time_range:type_name -> conan.TimeRange
	20, // 12: conan.ListAgentFileOperationReq.page:type_name -> conan.PageRequest
	16, // 13: conan.ListAgentFileOperationResp.file_operations:type_name -> conan.AgentFileOperation
	21, // 14: conan.ListAgentFileOperationResp.page:type_name -> conan.PageResponse
	0,  // 15: conan.AgentFileOperation.operation_type:type_name -> conan.FileOperationType
	1,  // 16: conan.AgentFileOperation.operation_status:type_name -> conan.FileOperateStatus
	18, // 17: conan.AgentFileOperation.create_time:type_name -> google.protobuf.Timestamp
	2,  // 18: conan.AgentFileService.HandleAgentFile:input_type -> conan.HandleAgentFileReq
	4,  // 19: conan.AgentFileService.BatchHandleAgentFile:input_type -> conan.BatchHandleAgentFileReq
	6,  // 20: conan.AgentFileService.RetryHandleAgentFile:input_type -> conan.RetryHandleAgentFileReq
	9,  // 21: conan.AgentFileService.ListOperatedThreatenFiles:input_type -> conan.ListOperatedThreatenFileReq
	12, // 22: conan.AgentFileService.BatchDeleteFileOperations:input_type -> conan.BatchDeleteFileOperationsReq
	14, // 23: conan.AgentFileService.ListAgentFileOperations:input_type -> conan.ListAgentFileOperationReq
	3,  // 24: conan.AgentFileService.HandleAgentFile:output_type -> conan.HandleAgentFileResp
	5,  // 25: conan.AgentFileService.BatchHandleAgentFile:output_type -> conan.BatchHandleAgentFileResp
	7,  // 26: conan.AgentFileService.RetryHandleAgentFile:output_type -> conan.RetryHandleAgentFileResp
	10, // 27: conan.AgentFileService.ListOperatedThreatenFiles:output_type -> conan.ListOperatedThreatenFileResp
	13, // 28: conan.AgentFileService.BatchDeleteFileOperations:output_type -> conan.BatchDeleteFileOperationsResp
	15, // 29: conan.AgentFileService.ListAgentFileOperations:output_type -> conan.ListAgentFileOperationResp
	24, // [24:30] is the sub-list for method output_type
	18, // [18:24] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_conan_agent_file_proto_init() }
func file_conan_agent_file_proto_init() {
	if File_conan_agent_file_proto != nil {
		return
	}
	file_conan_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_agent_file_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_agent_file_proto_goTypes,
		DependencyIndexes: file_conan_agent_file_proto_depIdxs,
		EnumInfos:         file_conan_agent_file_proto_enumTypes,
		MessageInfos:      file_conan_agent_file_proto_msgTypes,
	}.Build()
	File_conan_agent_file_proto = out.File
	file_conan_agent_file_proto_rawDesc = nil
	file_conan_agent_file_proto_goTypes = nil
	file_conan_agent_file_proto_depIdxs = nil
}
