// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/export.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExportMemoryAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportMemoryAttackCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportMemoryAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportMemoryAttackCluesReqMultiError, or nil if none found.
func (m *ExportMemoryAttackCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportMemoryAttackCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportMemoryAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportMemoryAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportMemoryAttackCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportMemoryAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportMemoryAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportMemoryAttackCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExportMemoryAttackCluesReqMultiError(errors)
	}

	return nil
}

// ExportMemoryAttackCluesReqMultiError is an error wrapping multiple
// validation errors returned by ExportMemoryAttackCluesReq.ValidateAll() if
// the designated constraints aren't met.
type ExportMemoryAttackCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportMemoryAttackCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportMemoryAttackCluesReqMultiError) AllErrors() []error { return m }

// ExportMemoryAttackCluesReqValidationError is the validation error returned
// by ExportMemoryAttackCluesReq.Validate if the designated constraints aren't met.
type ExportMemoryAttackCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportMemoryAttackCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportMemoryAttackCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportMemoryAttackCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportMemoryAttackCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportMemoryAttackCluesReqValidationError) ErrorName() string {
	return "ExportMemoryAttackCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ExportMemoryAttackCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportMemoryAttackCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportMemoryAttackCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportMemoryAttackCluesReqValidationError{}

// Validate checks the field values on ExportMemoryAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportMemoryAttackCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportMemoryAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportMemoryAttackCluesRespMultiError, or nil if none found.
func (m *ExportMemoryAttackCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportMemoryAttackCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExportMemoryAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExportMemoryAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExportMemoryAttackCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClueType

	// no validation rules for BatchSize

	if len(errors) > 0 {
		return ExportMemoryAttackCluesRespMultiError(errors)
	}

	return nil
}

// ExportMemoryAttackCluesRespMultiError is an error wrapping multiple
// validation errors returned by ExportMemoryAttackCluesResp.ValidateAll() if
// the designated constraints aren't met.
type ExportMemoryAttackCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportMemoryAttackCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportMemoryAttackCluesRespMultiError) AllErrors() []error { return m }

// ExportMemoryAttackCluesRespValidationError is the validation error returned
// by ExportMemoryAttackCluesResp.Validate if the designated constraints
// aren't met.
type ExportMemoryAttackCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportMemoryAttackCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportMemoryAttackCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportMemoryAttackCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportMemoryAttackCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportMemoryAttackCluesRespValidationError) ErrorName() string {
	return "ExportMemoryAttackCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ExportMemoryAttackCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportMemoryAttackCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportMemoryAttackCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportMemoryAttackCluesRespValidationError{}

// Validate checks the field values on ExportIllegalOutreachCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportIllegalOutreachCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportIllegalOutreachCluesReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExportIllegalOutreachCluesReqMultiError, or nil if none found.
func (m *ExportIllegalOutreachCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportIllegalOutreachCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportIllegalOutreachCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportIllegalOutreachCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportIllegalOutreachCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportIllegalOutreachCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportIllegalOutreachCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportIllegalOutreachCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExportIllegalOutreachCluesReqMultiError(errors)
	}

	return nil
}

// ExportIllegalOutreachCluesReqMultiError is an error wrapping multiple
// validation errors returned by ExportIllegalOutreachCluesReq.ValidateAll()
// if the designated constraints aren't met.
type ExportIllegalOutreachCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportIllegalOutreachCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportIllegalOutreachCluesReqMultiError) AllErrors() []error { return m }

// ExportIllegalOutreachCluesReqValidationError is the validation error
// returned by ExportIllegalOutreachCluesReq.Validate if the designated
// constraints aren't met.
type ExportIllegalOutreachCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportIllegalOutreachCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportIllegalOutreachCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportIllegalOutreachCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportIllegalOutreachCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportIllegalOutreachCluesReqValidationError) ErrorName() string {
	return "ExportIllegalOutreachCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ExportIllegalOutreachCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportIllegalOutreachCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportIllegalOutreachCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportIllegalOutreachCluesReqValidationError{}

// Validate checks the field values on ExportIllegalOutreachCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportIllegalOutreachCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportIllegalOutreachCluesResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExportIllegalOutreachCluesRespMultiError, or nil if none found.
func (m *ExportIllegalOutreachCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportIllegalOutreachCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExportIllegalOutreachCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExportIllegalOutreachCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExportIllegalOutreachCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClueType

	// no validation rules for BatchSize

	if len(errors) > 0 {
		return ExportIllegalOutreachCluesRespMultiError(errors)
	}

	return nil
}

// ExportIllegalOutreachCluesRespMultiError is an error wrapping multiple
// validation errors returned by ExportIllegalOutreachCluesResp.ValidateAll()
// if the designated constraints aren't met.
type ExportIllegalOutreachCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportIllegalOutreachCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportIllegalOutreachCluesRespMultiError) AllErrors() []error { return m }

// ExportIllegalOutreachCluesRespValidationError is the validation error
// returned by ExportIllegalOutreachCluesResp.Validate if the designated
// constraints aren't met.
type ExportIllegalOutreachCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportIllegalOutreachCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportIllegalOutreachCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportIllegalOutreachCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportIllegalOutreachCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportIllegalOutreachCluesRespValidationError) ErrorName() string {
	return "ExportIllegalOutreachCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ExportIllegalOutreachCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportIllegalOutreachCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportIllegalOutreachCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportIllegalOutreachCluesRespValidationError{}

// Validate checks the field values on ExportFileThreatCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportFileThreatCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportFileThreatCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportFileThreatCluesReqMultiError, or nil if none found.
func (m *ExportFileThreatCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportFileThreatCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportFileThreatCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportFileThreatCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportFileThreatCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportFileThreatCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportFileThreatCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportFileThreatCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExportFileThreatCluesReqMultiError(errors)
	}

	return nil
}

// ExportFileThreatCluesReqMultiError is an error wrapping multiple validation
// errors returned by ExportFileThreatCluesReq.ValidateAll() if the designated
// constraints aren't met.
type ExportFileThreatCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportFileThreatCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportFileThreatCluesReqMultiError) AllErrors() []error { return m }

// ExportFileThreatCluesReqValidationError is the validation error returned by
// ExportFileThreatCluesReq.Validate if the designated constraints aren't met.
type ExportFileThreatCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportFileThreatCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportFileThreatCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportFileThreatCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportFileThreatCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportFileThreatCluesReqValidationError) ErrorName() string {
	return "ExportFileThreatCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ExportFileThreatCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportFileThreatCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportFileThreatCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportFileThreatCluesReqValidationError{}

// Validate checks the field values on ExportFileThreatCluesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportFileThreatCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportFileThreatCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportFileThreatCluesRespMultiError, or nil if none found.
func (m *ExportFileThreatCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportFileThreatCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportFileThreatCluesRespValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportFileThreatCluesRespValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportFileThreatCluesRespValidationError{
				field:  "Clue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClueType

	if len(errors) > 0 {
		return ExportFileThreatCluesRespMultiError(errors)
	}

	return nil
}

// ExportFileThreatCluesRespMultiError is an error wrapping multiple validation
// errors returned by ExportFileThreatCluesResp.ValidateAll() if the
// designated constraints aren't met.
type ExportFileThreatCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportFileThreatCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportFileThreatCluesRespMultiError) AllErrors() []error { return m }

// ExportFileThreatCluesRespValidationError is the validation error returned by
// ExportFileThreatCluesResp.Validate if the designated constraints aren't met.
type ExportFileThreatCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportFileThreatCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportFileThreatCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportFileThreatCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportFileThreatCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportFileThreatCluesRespValidationError) ErrorName() string {
	return "ExportFileThreatCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ExportFileThreatCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportFileThreatCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportFileThreatCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportFileThreatCluesRespValidationError{}

// Validate checks the field values on ExportSystemAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportSystemAttackCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportSystemAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportSystemAttackCluesReqMultiError, or nil if none found.
func (m *ExportSystemAttackCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportSystemAttackCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportSystemAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportSystemAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportSystemAttackCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ExportSystemAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ExportSystemAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ExportSystemAttackCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ExportSystemAttackCluesReqMultiError(errors)
	}

	return nil
}

// ExportSystemAttackCluesReqMultiError is an error wrapping multiple
// validation errors returned by ExportSystemAttackCluesReq.ValidateAll() if
// the designated constraints aren't met.
type ExportSystemAttackCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportSystemAttackCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportSystemAttackCluesReqMultiError) AllErrors() []error { return m }

// ExportSystemAttackCluesReqValidationError is the validation error returned
// by ExportSystemAttackCluesReq.Validate if the designated constraints aren't met.
type ExportSystemAttackCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportSystemAttackCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportSystemAttackCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportSystemAttackCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportSystemAttackCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportSystemAttackCluesReqValidationError) ErrorName() string {
	return "ExportSystemAttackCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ExportSystemAttackCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportSystemAttackCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportSystemAttackCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportSystemAttackCluesReqValidationError{}

// Validate checks the field values on ExportSystemAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExportSystemAttackCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExportSystemAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExportSystemAttackCluesRespMultiError, or nil if none found.
func (m *ExportSystemAttackCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ExportSystemAttackCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExportSystemAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExportSystemAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExportSystemAttackCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClueType

	// no validation rules for BatchSize

	if len(errors) > 0 {
		return ExportSystemAttackCluesRespMultiError(errors)
	}

	return nil
}

// ExportSystemAttackCluesRespMultiError is an error wrapping multiple
// validation errors returned by ExportSystemAttackCluesResp.ValidateAll() if
// the designated constraints aren't met.
type ExportSystemAttackCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExportSystemAttackCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExportSystemAttackCluesRespMultiError) AllErrors() []error { return m }

// ExportSystemAttackCluesRespValidationError is the validation error returned
// by ExportSystemAttackCluesResp.Validate if the designated constraints
// aren't met.
type ExportSystemAttackCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExportSystemAttackCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExportSystemAttackCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExportSystemAttackCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExportSystemAttackCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExportSystemAttackCluesRespValidationError) ErrorName() string {
	return "ExportSystemAttackCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ExportSystemAttackCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExportSystemAttackCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExportSystemAttackCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExportSystemAttackCluesRespValidationError{}
