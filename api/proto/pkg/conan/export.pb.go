// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/export.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Export request for memory attack clues
type ExportMemoryAttackCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ExportMemoryAttackCluesReq) Reset() {
	*x = ExportMemoryAttackCluesReq{}
	mi := &file_conan_export_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportMemoryAttackCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportMemoryAttackCluesReq) ProtoMessage() {}

func (x *ExportMemoryAttackCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportMemoryAttackCluesReq.ProtoReflect.Descriptor instead.
func (*ExportMemoryAttackCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{0}
}

func (x *ExportMemoryAttackCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ExportMemoryAttackCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// Export response for memory attack clues
type ExportMemoryAttackCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues     []*ClueDetail `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	ClueType  ClueType      `protobuf:"varint,2,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	BatchSize int64         `protobuf:"varint,3,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
}

func (x *ExportMemoryAttackCluesResp) Reset() {
	*x = ExportMemoryAttackCluesResp{}
	mi := &file_conan_export_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportMemoryAttackCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportMemoryAttackCluesResp) ProtoMessage() {}

func (x *ExportMemoryAttackCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportMemoryAttackCluesResp.ProtoReflect.Descriptor instead.
func (*ExportMemoryAttackCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{1}
}

func (x *ExportMemoryAttackCluesResp) GetClues() []*ClueDetail {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ExportMemoryAttackCluesResp) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ExportMemoryAttackCluesResp) GetBatchSize() int64 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

// Export request for illegal outreach clues
type ExportIllegalOutreachCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ExportIllegalOutreachCluesReq) Reset() {
	*x = ExportIllegalOutreachCluesReq{}
	mi := &file_conan_export_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportIllegalOutreachCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportIllegalOutreachCluesReq) ProtoMessage() {}

func (x *ExportIllegalOutreachCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportIllegalOutreachCluesReq.ProtoReflect.Descriptor instead.
func (*ExportIllegalOutreachCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{2}
}

func (x *ExportIllegalOutreachCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ExportIllegalOutreachCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// Export response for illegal outreach clues
type ExportIllegalOutreachCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues     []*ClueDetail `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	ClueType  ClueType      `protobuf:"varint,2,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	BatchSize int64         `protobuf:"varint,3,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
}

func (x *ExportIllegalOutreachCluesResp) Reset() {
	*x = ExportIllegalOutreachCluesResp{}
	mi := &file_conan_export_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportIllegalOutreachCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportIllegalOutreachCluesResp) ProtoMessage() {}

func (x *ExportIllegalOutreachCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportIllegalOutreachCluesResp.ProtoReflect.Descriptor instead.
func (*ExportIllegalOutreachCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{3}
}

func (x *ExportIllegalOutreachCluesResp) GetClues() []*ClueDetail {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ExportIllegalOutreachCluesResp) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ExportIllegalOutreachCluesResp) GetBatchSize() int64 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

// Export request for file threat clues
type ExportFileThreatCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ExportFileThreatCluesReq) Reset() {
	*x = ExportFileThreatCluesReq{}
	mi := &file_conan_export_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportFileThreatCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportFileThreatCluesReq) ProtoMessage() {}

func (x *ExportFileThreatCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportFileThreatCluesReq.ProtoReflect.Descriptor instead.
func (*ExportFileThreatCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{4}
}

func (x *ExportFileThreatCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ExportFileThreatCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// Export response for file threat clues
type ExportFileThreatCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clue     *ClueDetail `protobuf:"bytes,1,opt,name=clue,proto3" json:"clue,omitempty"`
	ClueType ClueType    `protobuf:"varint,2,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
}

func (x *ExportFileThreatCluesResp) Reset() {
	*x = ExportFileThreatCluesResp{}
	mi := &file_conan_export_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportFileThreatCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportFileThreatCluesResp) ProtoMessage() {}

func (x *ExportFileThreatCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportFileThreatCluesResp.ProtoReflect.Descriptor instead.
func (*ExportFileThreatCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{5}
}

func (x *ExportFileThreatCluesResp) GetClue() *ClueDetail {
	if x != nil {
		return x.Clue
	}
	return nil
}

func (x *ExportFileThreatCluesResp) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

// Export request for system attack clues
type ExportSystemAttackCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ExportSystemAttackCluesReq) Reset() {
	*x = ExportSystemAttackCluesReq{}
	mi := &file_conan_export_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportSystemAttackCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportSystemAttackCluesReq) ProtoMessage() {}

func (x *ExportSystemAttackCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportSystemAttackCluesReq.ProtoReflect.Descriptor instead.
func (*ExportSystemAttackCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{6}
}

func (x *ExportSystemAttackCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ExportSystemAttackCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// Export response for system attack clues
type ExportSystemAttackCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues     []*ClueDetail `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	ClueType  ClueType      `protobuf:"varint,2,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	BatchSize int64         `protobuf:"varint,3,opt,name=batch_size,json=batchSize,proto3" json:"batch_size,omitempty"`
}

func (x *ExportSystemAttackCluesResp) Reset() {
	*x = ExportSystemAttackCluesResp{}
	mi := &file_conan_export_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExportSystemAttackCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExportSystemAttackCluesResp) ProtoMessage() {}

func (x *ExportSystemAttackCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_export_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExportSystemAttackCluesResp.ProtoReflect.Descriptor instead.
func (*ExportSystemAttackCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_export_proto_rawDescGZIP(), []int{7}
}

func (x *ExportSystemAttackCluesResp) GetClues() []*ClueDetail {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ExportSystemAttackCluesResp) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ExportSystemAttackCluesResp) GetBatchSize() int64 {
	if x != nil {
		return x.BatchSize
	}
	return 0
}

var File_conan_export_proto protoreflect.FileDescriptor

var file_conan_export_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x65, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x12, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x10, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6c, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x7a, 0x0a, 0x1a, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x93, 0x01,
	0x0a, 0x1b, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a,
	0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x7d, 0x0a, 0x1d, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6c, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x22, 0x96, 0x01, 0x0a, 0x1e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6c, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2c,
	0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x78, 0x0a, 0x18, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x70, 0x0a, 0x19, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x25, 0x0a, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c, 0x75,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63,
	0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0x7a, 0x0a, 0x1a, 0x45, 0x78, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x1b, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79,
	0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x09,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x53, 0x69, 0x7a, 0x65, 0x32, 0xa2, 0x03, 0x0a, 0x0d, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x62, 0x0a, 0x17, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x30, 0x01, 0x12,
	0x6b, 0x0a, 0x1a, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x24, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x49, 0x6c, 0x6c, 0x65,
	0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x78, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x30, 0x01, 0x12, 0x5c, 0x0a, 0x15,
	0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45,
	0x78, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x30, 0x01, 0x12, 0x62, 0x0a, 0x17, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x21, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x45, 0x78,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x45, 0x78, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x30, 0x01, 0x42, 0x2a,
	0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e, 0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_conan_export_proto_rawDescOnce sync.Once
	file_conan_export_proto_rawDescData = file_conan_export_proto_rawDesc
)

func file_conan_export_proto_rawDescGZIP() []byte {
	file_conan_export_proto_rawDescOnce.Do(func() {
		file_conan_export_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_export_proto_rawDescData)
	})
	return file_conan_export_proto_rawDescData
}

var file_conan_export_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_conan_export_proto_goTypes = []any{
	(*ExportMemoryAttackCluesReq)(nil),     // 0: conan.ExportMemoryAttackCluesReq
	(*ExportMemoryAttackCluesResp)(nil),    // 1: conan.ExportMemoryAttackCluesResp
	(*ExportIllegalOutreachCluesReq)(nil),  // 2: conan.ExportIllegalOutreachCluesReq
	(*ExportIllegalOutreachCluesResp)(nil), // 3: conan.ExportIllegalOutreachCluesResp
	(*ExportFileThreatCluesReq)(nil),       // 4: conan.ExportFileThreatCluesReq
	(*ExportFileThreatCluesResp)(nil),      // 5: conan.ExportFileThreatCluesResp
	(*ExportSystemAttackCluesReq)(nil),     // 6: conan.ExportSystemAttackCluesReq
	(*ExportSystemAttackCluesResp)(nil),    // 7: conan.ExportSystemAttackCluesResp
	(*ListAttackCluesFilter)(nil),          // 8: conan.ListAttackCluesFilter
	(*PageRequest)(nil),                    // 9: conan.PageRequest
	(*ClueDetail)(nil),                     // 10: conan.ClueDetail
	(ClueType)(0),                          // 11: conan.ClueType
}
var file_conan_export_proto_depIdxs = []int32{
	8,  // 0: conan.ExportMemoryAttackCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	9,  // 1: conan.ExportMemoryAttackCluesReq.page:type_name -> conan.PageRequest
	10, // 2: conan.ExportMemoryAttackCluesResp.clues:type_name -> conan.ClueDetail
	11, // 3: conan.ExportMemoryAttackCluesResp.clue_type:type_name -> conan.ClueType
	8,  // 4: conan.ExportIllegalOutreachCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	9,  // 5: conan.ExportIllegalOutreachCluesReq.page:type_name -> conan.PageRequest
	10, // 6: conan.ExportIllegalOutreachCluesResp.clues:type_name -> conan.ClueDetail
	11, // 7: conan.ExportIllegalOutreachCluesResp.clue_type:type_name -> conan.ClueType
	8,  // 8: conan.ExportFileThreatCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	9,  // 9: conan.ExportFileThreatCluesReq.page:type_name -> conan.PageRequest
	10, // 10: conan.ExportFileThreatCluesResp.clue:type_name -> conan.ClueDetail
	11, // 11: conan.ExportFileThreatCluesResp.clue_type:type_name -> conan.ClueType
	8,  // 12: conan.ExportSystemAttackCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	9,  // 13: conan.ExportSystemAttackCluesReq.page:type_name -> conan.PageRequest
	10, // 14: conan.ExportSystemAttackCluesResp.clues:type_name -> conan.ClueDetail
	11, // 15: conan.ExportSystemAttackCluesResp.clue_type:type_name -> conan.ClueType
	0,  // 16: conan.ExportService.ExportMemoryAttackClues:input_type -> conan.ExportMemoryAttackCluesReq
	2,  // 17: conan.ExportService.ExportIllegalOutreachClues:input_type -> conan.ExportIllegalOutreachCluesReq
	4,  // 18: conan.ExportService.ExportFileThreatClues:input_type -> conan.ExportFileThreatCluesReq
	6,  // 19: conan.ExportService.ExportSystemAttackClues:input_type -> conan.ExportSystemAttackCluesReq
	1,  // 20: conan.ExportService.ExportMemoryAttackClues:output_type -> conan.ExportMemoryAttackCluesResp
	3,  // 21: conan.ExportService.ExportIllegalOutreachClues:output_type -> conan.ExportIllegalOutreachCluesResp
	5,  // 22: conan.ExportService.ExportFileThreatClues:output_type -> conan.ExportFileThreatCluesResp
	7,  // 23: conan.ExportService.ExportSystemAttackClues:output_type -> conan.ExportSystemAttackCluesResp
	20, // [20:24] is the sub-list for method output_type
	16, // [16:20] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_conan_export_proto_init() }
func file_conan_export_proto_init() {
	if File_conan_export_proto != nil {
		return
	}
	file_conan_common_proto_init()
	file_conan_clue_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_export_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_export_proto_goTypes,
		DependencyIndexes: file_conan_export_proto_depIdxs,
		MessageInfos:      file_conan_export_proto_msgTypes,
	}.Build()
	File_conan_export_proto = out.File
	file_conan_export_proto_rawDesc = nil
	file_conan_export_proto_goTypes = nil
	file_conan_export_proto_depIdxs = nil
}
