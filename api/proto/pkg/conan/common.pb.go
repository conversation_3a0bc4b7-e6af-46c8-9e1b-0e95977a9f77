// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/common.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 线索类型枚举定义
type ClueType int32

const (
	ClueType_CT_UNKNOWN       ClueType = 0
	ClueType_MALICIOUS_FILE   ClueType = 1
	ClueType_ILLEGAL_OUTREACH ClueType = 2
	ClueType_MEMORY_ATTACK    ClueType = 3
	ClueType_SCRIPT_ATTACK    ClueType = 4
)

// Enum value maps for ClueType.
var (
	ClueType_name = map[int32]string{
		0: "CT_UNKNOWN",
		1: "MALICIOUS_FILE",
		2: "ILLEGAL_OUTREACH",
		3: "MEMORY_ATTACK",
		4: "SCRIPT_ATTACK",
	}
	ClueType_value = map[string]int32{
		"CT_UNKNOWN":       0,
		"MALICIOUS_FILE":   1,
		"ILLEGAL_OUTREACH": 2,
		"MEMORY_ATTACK":    3,
		"SCRIPT_ATTACK":    4,
	}
)

func (x ClueType) Enum() *ClueType {
	p := new(ClueType)
	*p = x
	return p
}

func (x ClueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClueType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[0].Descriptor()
}

func (ClueType) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[0]
}

func (x ClueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClueType.Descriptor instead.
func (ClueType) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{0}
}

type OutreachType int32

const (
	OutreachType_OT_UNKNOWN OutreachType = 0
	OutreachType_IP         OutreachType = 1
	OutreachType_DOMAIN     OutreachType = 2
	OutreachType_URL        OutreachType = 3
)

// Enum value maps for OutreachType.
var (
	OutreachType_name = map[int32]string{
		0: "OT_UNKNOWN",
		1: "IP",
		2: "DOMAIN",
		3: "URL",
	}
	OutreachType_value = map[string]int32{
		"OT_UNKNOWN": 0,
		"IP":         1,
		"DOMAIN":     2,
		"URL":        3,
	}
)

func (x OutreachType) Enum() *OutreachType {
	p := new(OutreachType)
	*p = x
	return p
}

func (x OutreachType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OutreachType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[1].Descriptor()
}

func (OutreachType) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[1]
}

func (x OutreachType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OutreachType.Descriptor instead.
func (OutreachType) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{1}
}

// 外联命中来源
type HitSource int32

const (
	HitSource_HS_UNKNOWN HitSource = 0 // protobuf 建议枚举第一个值为0
	HitSource_HS_PUBLIC  HitSource = 1 // 命中共有情报
	HitSource_HS_PRIVATE HitSource = 2 // 命中私有情报
)

// Enum value maps for HitSource.
var (
	HitSource_name = map[int32]string{
		0: "HS_UNKNOWN",
		1: "HS_PUBLIC",
		2: "HS_PRIVATE",
	}
	HitSource_value = map[string]int32{
		"HS_UNKNOWN": 0,
		"HS_PUBLIC":  1,
		"HS_PRIVATE": 2,
	}
)

func (x HitSource) Enum() *HitSource {
	p := new(HitSource)
	*p = x
	return p
}

func (x HitSource) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitSource) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[2].Descriptor()
}

func (HitSource) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[2]
}

func (x HitSource) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitSource.Descriptor instead.
func (HitSource) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{2}
}

// APT命中状态
type AptStatus int32

const (
	AptStatus_AS_UNKNOWN AptStatus = 0
	AptStatus_AS_HIT     AptStatus = 1 // 命中
	AptStatus_AS_NOT_HIT AptStatus = 2 // 未命中
)

// Enum value maps for AptStatus.
var (
	AptStatus_name = map[int32]string{
		0: "AS_UNKNOWN",
		1: "AS_HIT",
		2: "AS_NOT_HIT",
	}
	AptStatus_value = map[string]int32{
		"AS_UNKNOWN": 0,
		"AS_HIT":     1,
		"AS_NOT_HIT": 2,
	}
)

func (x AptStatus) Enum() *AptStatus {
	p := new(AptStatus)
	*p = x
	return p
}

func (x AptStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AptStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[3].Descriptor()
}

func (AptStatus) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[3]
}

func (x AptStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AptStatus.Descriptor instead.
func (AptStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3}
}

type ListAttackCluesFilter_ClueLevel int32

const (
	ListAttackCluesFilter_CL_UNKNOWN ListAttackCluesFilter_ClueLevel = 0
	ListAttackCluesFilter_LOW        ListAttackCluesFilter_ClueLevel = 1
	ListAttackCluesFilter_MEDIUM     ListAttackCluesFilter_ClueLevel = 2
	ListAttackCluesFilter_HIGH       ListAttackCluesFilter_ClueLevel = 3
)

// Enum value maps for ListAttackCluesFilter_ClueLevel.
var (
	ListAttackCluesFilter_ClueLevel_name = map[int32]string{
		0: "CL_UNKNOWN",
		1: "LOW",
		2: "MEDIUM",
		3: "HIGH",
	}
	ListAttackCluesFilter_ClueLevel_value = map[string]int32{
		"CL_UNKNOWN": 0,
		"LOW":        1,
		"MEDIUM":     2,
		"HIGH":       3,
	}
)

func (x ListAttackCluesFilter_ClueLevel) Enum() *ListAttackCluesFilter_ClueLevel {
	p := new(ListAttackCluesFilter_ClueLevel)
	*p = x
	return p
}

func (x ListAttackCluesFilter_ClueLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAttackCluesFilter_ClueLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[4].Descriptor()
}

func (ListAttackCluesFilter_ClueLevel) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[4]
}

func (x ListAttackCluesFilter_ClueLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAttackCluesFilter_ClueLevel.Descriptor instead.
func (ListAttackCluesFilter_ClueLevel) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3, 0}
}

type ListAttackCluesFilter_DisposeStatus int32

const (
	ListAttackCluesFilter_DS_UNKNOWN  ListAttackCluesFilter_DisposeStatus = 0
	ListAttackCluesFilter_PROCESSED   ListAttackCluesFilter_DisposeStatus = 1
	ListAttackCluesFilter_UNPROCESSED ListAttackCluesFilter_DisposeStatus = 2
)

// Enum value maps for ListAttackCluesFilter_DisposeStatus.
var (
	ListAttackCluesFilter_DisposeStatus_name = map[int32]string{
		0: "DS_UNKNOWN",
		1: "PROCESSED",
		2: "UNPROCESSED",
	}
	ListAttackCluesFilter_DisposeStatus_value = map[string]int32{
		"DS_UNKNOWN":  0,
		"PROCESSED":   1,
		"UNPROCESSED": 2,
	}
)

func (x ListAttackCluesFilter_DisposeStatus) Enum() *ListAttackCluesFilter_DisposeStatus {
	p := new(ListAttackCluesFilter_DisposeStatus)
	*p = x
	return p
}

func (x ListAttackCluesFilter_DisposeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAttackCluesFilter_DisposeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[5].Descriptor()
}

func (ListAttackCluesFilter_DisposeStatus) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[5]
}

func (x ListAttackCluesFilter_DisposeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAttackCluesFilter_DisposeStatus.Descriptor instead.
func (ListAttackCluesFilter_DisposeStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3, 1}
}

// 搜索参数
type ListAttackCluesFilter_SearchType int32

const (
	ListAttackCluesFilter_ST_UNKNOWN  ListAttackCluesFilter_SearchType = 0
	ListAttackCluesFilter_BY_IP       ListAttackCluesFilter_SearchType = 1
	ListAttackCluesFilter_BY_MD5      ListAttackCluesFilter_SearchType = 2
	ListAttackCluesFilter_BY_HOSTNAME ListAttackCluesFilter_SearchType = 3
	ListAttackCluesFilter_BY_SHA256   ListAttackCluesFilter_SearchType = 4
)

// Enum value maps for ListAttackCluesFilter_SearchType.
var (
	ListAttackCluesFilter_SearchType_name = map[int32]string{
		0: "ST_UNKNOWN",
		1: "BY_IP",
		2: "BY_MD5",
		3: "BY_HOSTNAME",
		4: "BY_SHA256",
	}
	ListAttackCluesFilter_SearchType_value = map[string]int32{
		"ST_UNKNOWN":  0,
		"BY_IP":       1,
		"BY_MD5":      2,
		"BY_HOSTNAME": 3,
		"BY_SHA256":   4,
	}
)

func (x ListAttackCluesFilter_SearchType) Enum() *ListAttackCluesFilter_SearchType {
	p := new(ListAttackCluesFilter_SearchType)
	*p = x
	return p
}

func (x ListAttackCluesFilter_SearchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAttackCluesFilter_SearchType) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[6].Descriptor()
}

func (ListAttackCluesFilter_SearchType) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[6]
}

func (x ListAttackCluesFilter_SearchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAttackCluesFilter_SearchType.Descriptor instead.
func (ListAttackCluesFilter_SearchType) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3, 2}
}

// 文件分类
type ListAttackCluesFilter_FileCategory int32

const (
	ListAttackCluesFilter_FC_UNKNOWN  ListAttackCluesFilter_FileCategory = 0
	ListAttackCluesFilter_BLACK       ListAttackCluesFilter_FileCategory = 1
	ListAttackCluesFilter_WHITE       ListAttackCluesFilter_FileCategory = 2
	ListAttackCluesFilter_GRAY        ListAttackCluesFilter_FileCategory = 3
	ListAttackCluesFilter_QUASI_WHITE ListAttackCluesFilter_FileCategory = 4
)

// Enum value maps for ListAttackCluesFilter_FileCategory.
var (
	ListAttackCluesFilter_FileCategory_name = map[int32]string{
		0: "FC_UNKNOWN",
		1: "BLACK",
		2: "WHITE",
		3: "GRAY",
		4: "QUASI_WHITE",
	}
	ListAttackCluesFilter_FileCategory_value = map[string]int32{
		"FC_UNKNOWN":  0,
		"BLACK":       1,
		"WHITE":       2,
		"GRAY":        3,
		"QUASI_WHITE": 4,
	}
)

func (x ListAttackCluesFilter_FileCategory) Enum() *ListAttackCluesFilter_FileCategory {
	p := new(ListAttackCluesFilter_FileCategory)
	*p = x
	return p
}

func (x ListAttackCluesFilter_FileCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAttackCluesFilter_FileCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[7].Descriptor()
}

func (ListAttackCluesFilter_FileCategory) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[7]
}

func (x ListAttackCluesFilter_FileCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAttackCluesFilter_FileCategory.Descriptor instead.
func (ListAttackCluesFilter_FileCategory) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3, 3}
}

// 在线状态
type ListAttackCluesFilter_OnlineStatus int32

const (
	ListAttackCluesFilter_ONLINE  ListAttackCluesFilter_OnlineStatus = 0
	ListAttackCluesFilter_OFFLINE ListAttackCluesFilter_OnlineStatus = 1
)

// Enum value maps for ListAttackCluesFilter_OnlineStatus.
var (
	ListAttackCluesFilter_OnlineStatus_name = map[int32]string{
		0: "ONLINE",
		1: "OFFLINE",
	}
	ListAttackCluesFilter_OnlineStatus_value = map[string]int32{
		"ONLINE":  0,
		"OFFLINE": 1,
	}
)

func (x ListAttackCluesFilter_OnlineStatus) Enum() *ListAttackCluesFilter_OnlineStatus {
	p := new(ListAttackCluesFilter_OnlineStatus)
	*p = x
	return p
}

func (x ListAttackCluesFilter_OnlineStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAttackCluesFilter_OnlineStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_common_proto_enumTypes[8].Descriptor()
}

func (ListAttackCluesFilter_OnlineStatus) Type() protoreflect.EnumType {
	return &file_conan_common_proto_enumTypes[8]
}

func (x ListAttackCluesFilter_OnlineStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAttackCluesFilter_OnlineStatus.Descriptor instead.
func (ListAttackCluesFilter_OnlineStatus) EnumDescriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3, 4}
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	mi := &file_conan_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_conan_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{0}
}

func (x *TimeRange) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *TimeRange) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

// 分页请求
type PageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *PageRequest) Reset() {
	*x = PageRequest{}
	mi := &file_conan_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageRequest) ProtoMessage() {}

func (x *PageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageRequest.ProtoReflect.Descriptor instead.
func (*PageRequest) Descriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{1}
}

func (x *PageRequest) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *PageRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// 分页响应
type PageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageIndex int32 `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize  int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Total     int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PageResponse) Reset() {
	*x = PageResponse{}
	mi := &file_conan_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageResponse) ProtoMessage() {}

func (x *PageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageResponse.ProtoReflect.Descriptor instead.
func (*PageResponse) Descriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{2}
}

func (x *PageResponse) GetPageIndex() int32 {
	if x != nil {
		return x.PageIndex
	}
	return 0
}

func (x *PageResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 过滤条件
type ListAttackCluesFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetType   int32 `protobuf:"varint,1,opt,name=target_type,json=targetType,proto3" json:"target_type,omitempty"`       // 目标类型
	HitStatus    int32 `protobuf:"varint,2,opt,name=hit_status,json=hitStatus,proto3" json:"hit_status,omitempty"`          // 命中状态
	IsApt        int32 `protobuf:"varint,3,opt,name=is_apt,json=isApt,proto3" json:"is_apt,omitempty"`                      // APT情报 (0-否 1-是)
	ClueSubType  int32 `protobuf:"varint,4,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`  // 威胁类型
	HasMalicious int32 `protobuf:"varint,5,opt,name=has_malicious,json=hasMalicious,proto3" json:"has_malicious,omitempty"` // 是否存在恶意代码 (0-无 1-有)
	// 终端信息
	Ip            string                              `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"` // 终端IP
	ClueType      ClueType                            `protobuf:"varint,8,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	ClueLevel     ListAttackCluesFilter_ClueLevel     `protobuf:"varint,9,opt,name=clue_level,json=clueLevel,proto3,enum=conan.ListAttackCluesFilter_ClueLevel" json:"clue_level,omitempty"`
	DisposeStatus ListAttackCluesFilter_DisposeStatus `protobuf:"varint,10,opt,name=dispose_status,json=disposeStatus,proto3,enum=conan.ListAttackCluesFilter_DisposeStatus" json:"dispose_status,omitempty"`
	SearchType    ListAttackCluesFilter_SearchType    `protobuf:"varint,11,opt,name=search_type,json=searchType,proto3,enum=conan.ListAttackCluesFilter_SearchType" json:"search_type,omitempty"`
	SearchData    string                              `protobuf:"bytes,12,opt,name=search_data,json=searchData,proto3" json:"search_data,omitempty"`
	FileType      ListAttackCluesFilter_FileCategory  `protobuf:"varint,13,opt,name=file_type,json=fileType,proto3,enum=conan.ListAttackCluesFilter_FileCategory" json:"file_type,omitempty"`
	// 分析参数
	Confidence int32  `protobuf:"varint,14,opt,name=confidence,proto3" json:"confidence,omitempty"` // 置信度 0-100
	Direction  string `protobuf:"bytes,15,opt,name=direction,proto3" json:"direction,omitempty"`    // 流量方向
	// 攻击信息
	AttackSrc string `protobuf:"bytes,16,opt,name=attack_src,json=attackSrc,proto3" json:"attack_src,omitempty"` // 攻击源
	// 分组信息
	GroupId []int64                            `protobuf:"varint,17,rep,packed,name=group_id,json=groupId,proto3" json:"group_id,omitempty"` // 分组ID列表
	Online  ListAttackCluesFilter_OnlineStatus `protobuf:"varint,18,opt,name=online,proto3,enum=conan.ListAttackCluesFilter_OnlineStatus" json:"online,omitempty"`
	// 文件特征
	FileName        string `protobuf:"bytes,19,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                      // 文件名
	OutreachAddress string `protobuf:"bytes,20,opt,name=outreach_address,json=outreachAddress,proto3" json:"outreach_address,omitempty"` // 外联地址
	// 记录标识
	Id          int64      `protobuf:"varint,21,opt,name=id,proto3" json:"id,omitempty"`                                     // 记录ID
	Report      bool       `protobuf:"varint,22,opt,name=report,proto3" json:"report,omitempty"`                             // 是否生成报告
	FileSha256S []string   `protobuf:"bytes,23,rep,name=file_sha256s,json=fileSha256s,proto3" json:"file_sha256s,omitempty"` // internal used by file threat clues
	TimeRange   *TimeRange `protobuf:"bytes,24,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`       // 时间范围
}

func (x *ListAttackCluesFilter) Reset() {
	*x = ListAttackCluesFilter{}
	mi := &file_conan_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAttackCluesFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAttackCluesFilter) ProtoMessage() {}

func (x *ListAttackCluesFilter) ProtoReflect() protoreflect.Message {
	mi := &file_conan_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAttackCluesFilter.ProtoReflect.Descriptor instead.
func (*ListAttackCluesFilter) Descriptor() ([]byte, []int) {
	return file_conan_common_proto_rawDescGZIP(), []int{3}
}

func (x *ListAttackCluesFilter) GetTargetType() int32 {
	if x != nil {
		return x.TargetType
	}
	return 0
}

func (x *ListAttackCluesFilter) GetHitStatus() int32 {
	if x != nil {
		return x.HitStatus
	}
	return 0
}

func (x *ListAttackCluesFilter) GetIsApt() int32 {
	if x != nil {
		return x.IsApt
	}
	return 0
}

func (x *ListAttackCluesFilter) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *ListAttackCluesFilter) GetHasMalicious() int32 {
	if x != nil {
		return x.HasMalicious
	}
	return 0
}

func (x *ListAttackCluesFilter) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListAttackCluesFilter) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ListAttackCluesFilter) GetClueLevel() ListAttackCluesFilter_ClueLevel {
	if x != nil {
		return x.ClueLevel
	}
	return ListAttackCluesFilter_CL_UNKNOWN
}

func (x *ListAttackCluesFilter) GetDisposeStatus() ListAttackCluesFilter_DisposeStatus {
	if x != nil {
		return x.DisposeStatus
	}
	return ListAttackCluesFilter_DS_UNKNOWN
}

func (x *ListAttackCluesFilter) GetSearchType() ListAttackCluesFilter_SearchType {
	if x != nil {
		return x.SearchType
	}
	return ListAttackCluesFilter_ST_UNKNOWN
}

func (x *ListAttackCluesFilter) GetSearchData() string {
	if x != nil {
		return x.SearchData
	}
	return ""
}

func (x *ListAttackCluesFilter) GetFileType() ListAttackCluesFilter_FileCategory {
	if x != nil {
		return x.FileType
	}
	return ListAttackCluesFilter_FC_UNKNOWN
}

func (x *ListAttackCluesFilter) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *ListAttackCluesFilter) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

func (x *ListAttackCluesFilter) GetAttackSrc() string {
	if x != nil {
		return x.AttackSrc
	}
	return ""
}

func (x *ListAttackCluesFilter) GetGroupId() []int64 {
	if x != nil {
		return x.GroupId
	}
	return nil
}

func (x *ListAttackCluesFilter) GetOnline() ListAttackCluesFilter_OnlineStatus {
	if x != nil {
		return x.Online
	}
	return ListAttackCluesFilter_ONLINE
}

func (x *ListAttackCluesFilter) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ListAttackCluesFilter) GetOutreachAddress() string {
	if x != nil {
		return x.OutreachAddress
	}
	return ""
}

func (x *ListAttackCluesFilter) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListAttackCluesFilter) GetReport() bool {
	if x != nil {
		return x.Report
	}
	return false
}

func (x *ListAttackCluesFilter) GetFileSha256S() []string {
	if x != nil {
		return x.FileSha256S
	}
	return nil
}

func (x *ListAttackCluesFilter) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

var File_conan_common_proto protoreflect.FileDescriptor

var file_conan_common_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7d, 0x0a, 0x09,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x49, 0x0a, 0x0b, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x60, 0x0a, 0x0c, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x8d, 0x0a, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x70, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x69, 0x73, 0x41, 0x70, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75,
	0x65, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x68, 0x61, 0x73, 0x4d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x70, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c,
	0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x09, 0x63, 0x6c,
	0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x51, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x44, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x64, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x0b, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x44, 0x61, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x11, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x64, 0x12, 0x41, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x2e, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x73, 0x18, 0x17, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x73, 0x12, 0x2f, 0x0a, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x3a, 0x0a, 0x09,
	0x43, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4c, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x4c, 0x4f, 0x57,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x45, 0x44, 0x49, 0x55, 0x4d, 0x10, 0x02, 0x12, 0x08,
	0x0a, 0x04, 0x48, 0x49, 0x47, 0x48, 0x10, 0x03, 0x22, 0x3f, 0x0a, 0x0d, 0x44, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x53, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f,
	0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x53, 0x53, 0x45, 0x44, 0x10, 0x02, 0x22, 0x53, 0x0a, 0x0a, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x5f, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x59, 0x5f, 0x49, 0x50,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x42, 0x59, 0x5f, 0x4d, 0x44, 0x35, 0x10, 0x02, 0x12, 0x0f,
	0x0a, 0x0b, 0x42, 0x59, 0x5f, 0x48, 0x4f, 0x53, 0x54, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x03, 0x12,
	0x0d, 0x0a, 0x09, 0x42, 0x59, 0x5f, 0x53, 0x48, 0x41, 0x32, 0x35, 0x36, 0x10, 0x04, 0x22, 0x4f,
	0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e,
	0x0a, 0x0a, 0x46, 0x43, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x42, 0x4c, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x57, 0x48, 0x49,
	0x54, 0x45, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x47, 0x52, 0x41, 0x59, 0x10, 0x03, 0x12, 0x0f,
	0x0a, 0x0b, 0x51, 0x55, 0x41, 0x53, 0x49, 0x5f, 0x57, 0x48, 0x49, 0x54, 0x45, 0x10, 0x04, 0x22,
	0x27, 0x0a, 0x0c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x0a, 0x0a, 0x06, 0x4f, 0x4e, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f,
	0x46, 0x46, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x2a, 0x6a, 0x0a, 0x08, 0x43, 0x6c, 0x75, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x41, 0x4c, 0x49, 0x43, 0x49, 0x4f, 0x55,
	0x53, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4c, 0x4c, 0x45,
	0x47, 0x41, 0x4c, 0x5f, 0x4f, 0x55, 0x54, 0x52, 0x45, 0x41, 0x43, 0x48, 0x10, 0x02, 0x12, 0x11,
	0x0a, 0x0d, 0x4d, 0x45, 0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x54, 0x54, 0x41, 0x43, 0x4b, 0x10,
	0x03, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x5f, 0x41, 0x54, 0x54, 0x41,
	0x43, 0x4b, 0x10, 0x04, 0x2a, 0x3b, 0x0a, 0x0c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4f, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f,
	0x57, 0x4e, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x50, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x44, 0x4f, 0x4d, 0x41, 0x49, 0x4e, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x52, 0x4c, 0x10,
	0x03, 0x2a, 0x3a, 0x0a, 0x09, 0x48, 0x69, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x0e,
	0x0a, 0x0a, 0x48, 0x53, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x48, 0x53, 0x5f, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x48, 0x53, 0x5f, 0x50, 0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x10, 0x02, 0x2a, 0x37, 0x0a,
	0x09, 0x41, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x53,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x53,
	0x5f, 0x48, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x53, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x48, 0x49, 0x54, 0x10, 0x02, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conan_common_proto_rawDescOnce sync.Once
	file_conan_common_proto_rawDescData = file_conan_common_proto_rawDesc
)

func file_conan_common_proto_rawDescGZIP() []byte {
	file_conan_common_proto_rawDescOnce.Do(func() {
		file_conan_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_common_proto_rawDescData)
	})
	return file_conan_common_proto_rawDescData
}

var file_conan_common_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_conan_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_conan_common_proto_goTypes = []any{
	(ClueType)(0),                            // 0: conan.ClueType
	(OutreachType)(0),                        // 1: conan.OutreachType
	(HitSource)(0),                           // 2: conan.HitSource
	(AptStatus)(0),                           // 3: conan.AptStatus
	(ListAttackCluesFilter_ClueLevel)(0),     // 4: conan.ListAttackCluesFilter.ClueLevel
	(ListAttackCluesFilter_DisposeStatus)(0), // 5: conan.ListAttackCluesFilter.DisposeStatus
	(ListAttackCluesFilter_SearchType)(0),    // 6: conan.ListAttackCluesFilter.SearchType
	(ListAttackCluesFilter_FileCategory)(0),  // 7: conan.ListAttackCluesFilter.FileCategory
	(ListAttackCluesFilter_OnlineStatus)(0),  // 8: conan.ListAttackCluesFilter.OnlineStatus
	(*TimeRange)(nil),                        // 9: conan.TimeRange
	(*PageRequest)(nil),                      // 10: conan.PageRequest
	(*PageResponse)(nil),                     // 11: conan.PageResponse
	(*ListAttackCluesFilter)(nil),            // 12: conan.ListAttackCluesFilter
	(*timestamppb.Timestamp)(nil),            // 13: google.protobuf.Timestamp
}
var file_conan_common_proto_depIdxs = []int32{
	13, // 0: conan.TimeRange.start_time:type_name -> google.protobuf.Timestamp
	13, // 1: conan.TimeRange.end_time:type_name -> google.protobuf.Timestamp
	0,  // 2: conan.ListAttackCluesFilter.clue_type:type_name -> conan.ClueType
	4,  // 3: conan.ListAttackCluesFilter.clue_level:type_name -> conan.ListAttackCluesFilter.ClueLevel
	5,  // 4: conan.ListAttackCluesFilter.dispose_status:type_name -> conan.ListAttackCluesFilter.DisposeStatus
	6,  // 5: conan.ListAttackCluesFilter.search_type:type_name -> conan.ListAttackCluesFilter.SearchType
	7,  // 6: conan.ListAttackCluesFilter.file_type:type_name -> conan.ListAttackCluesFilter.FileCategory
	8,  // 7: conan.ListAttackCluesFilter.online:type_name -> conan.ListAttackCluesFilter.OnlineStatus
	9,  // 8: conan.ListAttackCluesFilter.time_range:type_name -> conan.TimeRange
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_conan_common_proto_init() }
func file_conan_common_proto_init() {
	if File_conan_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_common_proto_rawDesc,
			NumEnums:      9,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_conan_common_proto_goTypes,
		DependencyIndexes: file_conan_common_proto_depIdxs,
		EnumInfos:         file_conan_common_proto_enumTypes,
		MessageInfos:      file_conan_common_proto_msgTypes,
	}.Build()
	File_conan_common_proto = out.File
	file_conan_common_proto_rawDesc = nil
	file_conan_common_proto_goTypes = nil
	file_conan_common_proto_depIdxs = nil
}
