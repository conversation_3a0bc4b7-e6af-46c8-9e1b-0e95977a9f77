// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/clue.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListMemoryAttackCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListMemoryAttackCluesReq) Reset() {
	*x = ListMemoryAttackCluesReq{}
	mi := &file_conan_clue_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoryAttackCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoryAttackCluesReq) ProtoMessage() {}

func (x *ListMemoryAttackCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoryAttackCluesReq.ProtoReflect.Descriptor instead.
func (*ListMemoryAttackCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{0}
}

func (x *ListMemoryAttackCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListMemoryAttackCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type MemoryAttackClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId     string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	UniqueFlag    string                 `protobuf:"bytes,3,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ClueKey       string                 `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus    int32                  `protobuf:"varint,5,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueLevel     int32                  `protobuf:"varint,6,opt,name=clue_level,json=clueLevel,proto3" json:"clue_level,omitempty"`
	ClueType      int32                  `protobuf:"varint,7,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	ClueSubType   int32                  `protobuf:"varint,8,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	Disposition   int32                  `protobuf:"varint,9,opt,name=disposition,proto3" json:"disposition,omitempty"`
	HasIgnore     int32                  `protobuf:"varint,10,opt,name=has_ignore,json=hasIgnore,proto3" json:"has_ignore,omitempty"`
	AttackSrc     string                 `protobuf:"bytes,11,opt,name=attack_src,json=attackSrc,proto3" json:"attack_src,omitempty"`
	OccurCount    int64                  `protobuf:"varint,12,opt,name=occur_count,json=occurCount,proto3" json:"occur_count,omitempty"`
	ClientVersion string                 `protobuf:"bytes,13,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DetectedAt    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=detected_at,json=detectedAt,proto3" json:"detected_at,omitempty"`
}

func (x *MemoryAttackClue) Reset() {
	*x = MemoryAttackClue{}
	mi := &file_conan_clue_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryAttackClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryAttackClue) ProtoMessage() {}

func (x *MemoryAttackClue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryAttackClue.ProtoReflect.Descriptor instead.
func (*MemoryAttackClue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{1}
}

func (x *MemoryAttackClue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MemoryAttackClue) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *MemoryAttackClue) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *MemoryAttackClue) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *MemoryAttackClue) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *MemoryAttackClue) GetClueLevel() int32 {
	if x != nil {
		return x.ClueLevel
	}
	return 0
}

func (x *MemoryAttackClue) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *MemoryAttackClue) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *MemoryAttackClue) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *MemoryAttackClue) GetHasIgnore() int32 {
	if x != nil {
		return x.HasIgnore
	}
	return 0
}

func (x *MemoryAttackClue) GetAttackSrc() string {
	if x != nil {
		return x.AttackSrc
	}
	return ""
}

func (x *MemoryAttackClue) GetOccurCount() int64 {
	if x != nil {
		return x.OccurCount
	}
	return 0
}

func (x *MemoryAttackClue) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *MemoryAttackClue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *MemoryAttackClue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *MemoryAttackClue) GetDetectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DetectedAt
	}
	return nil
}

type SystemAttackClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId     string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	UniqueFlag    string                 `protobuf:"bytes,3,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ClueKey       string                 `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus    int32                  `protobuf:"varint,5,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueLevel     int32                  `protobuf:"varint,6,opt,name=clue_level,json=clueLevel,proto3" json:"clue_level,omitempty"`
	ClueType      int32                  `protobuf:"varint,7,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	ClueSubType   int32                  `protobuf:"varint,8,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	Disposition   int32                  `protobuf:"varint,9,opt,name=disposition,proto3" json:"disposition,omitempty"`
	HasIgnore     int32                  `protobuf:"varint,10,opt,name=has_ignore,json=hasIgnore,proto3" json:"has_ignore,omitempty"`
	AttackSrc     string                 `protobuf:"bytes,11,opt,name=attack_src,json=attackSrc,proto3" json:"attack_src,omitempty"`
	OccurCount    int64                  `protobuf:"varint,12,opt,name=occur_count,json=occurCount,proto3" json:"occur_count,omitempty"`
	ClientVersion string                 `protobuf:"bytes,13,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DetectedAt    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=detected_at,json=detectedAt,proto3" json:"detected_at,omitempty"`
}

func (x *SystemAttackClue) Reset() {
	*x = SystemAttackClue{}
	mi := &file_conan_clue_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemAttackClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemAttackClue) ProtoMessage() {}

func (x *SystemAttackClue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemAttackClue.ProtoReflect.Descriptor instead.
func (*SystemAttackClue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{2}
}

func (x *SystemAttackClue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SystemAttackClue) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *SystemAttackClue) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *SystemAttackClue) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *SystemAttackClue) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *SystemAttackClue) GetClueLevel() int32 {
	if x != nil {
		return x.ClueLevel
	}
	return 0
}

func (x *SystemAttackClue) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *SystemAttackClue) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *SystemAttackClue) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *SystemAttackClue) GetHasIgnore() int32 {
	if x != nil {
		return x.HasIgnore
	}
	return 0
}

func (x *SystemAttackClue) GetAttackSrc() string {
	if x != nil {
		return x.AttackSrc
	}
	return ""
}

func (x *SystemAttackClue) GetOccurCount() int64 {
	if x != nil {
		return x.OccurCount
	}
	return 0
}

func (x *SystemAttackClue) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *SystemAttackClue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SystemAttackClue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SystemAttackClue) GetDetectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DetectedAt
	}
	return nil
}

type FileThreatClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId        string `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	UniqueFlag       string `protobuf:"bytes,3,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ClueKey          string `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus       int32  `protobuf:"varint,5,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueLevel        int32  `protobuf:"varint,6,opt,name=clue_level,json=clueLevel,proto3" json:"clue_level,omitempty"`
	ClueType         int32  `protobuf:"varint,7,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	ClueSubType      int32  `protobuf:"varint,8,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	Disposition      int32  `protobuf:"varint,9,opt,name=disposition,proto3" json:"disposition,omitempty"`
	HasIgnore        int32  `protobuf:"varint,10,opt,name=has_ignore,json=hasIgnore,proto3" json:"has_ignore,omitempty"`
	Filename         string `protobuf:"bytes,11,opt,name=filename,proto3" json:"filename,omitempty"`
	FileType         int32  `protobuf:"varint,12,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileMd5          string `protobuf:"bytes,13,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	FileSha256       string `protobuf:"bytes,14,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	IsolateCount     int32  `protobuf:"varint,15,opt,name=isolate_count,json=isolateCount,proto3" json:"isolate_count,omitempty"`
	OperationType    int32  `protobuf:"varint,16,opt,name=operation_type,json=operationType,proto3" json:"operation_type,omitempty"`
	OperationStatus  int32  `protobuf:"varint,17,opt,name=operation_status,json=operationStatus,proto3" json:"operation_status,omitempty"`
	OperationFailure int32  `protobuf:"varint,18,opt,name=operation_failure,json=operationFailure,proto3" json:"operation_failure,omitempty"`
	CanIsolate       bool   `protobuf:"varint,19,opt,name=can_isolate,json=canIsolate,proto3" json:"can_isolate,omitempty"`
	AttackSrc        string `protobuf:"bytes,20,opt,name=attack_src,json=attackSrc,proto3" json:"attack_src,omitempty"`
	OccurCount       int64  `protobuf:"varint,21,opt,name=occur_count,json=occurCount,proto3" json:"occur_count,omitempty"`
	DisposedCount    int64  `protobuf:"varint,22,opt,name=disposed_count,json=disposedCount,proto3" json:"disposed_count,omitempty"`
	// The number of file clues grouped by file_sha256
	FileSha256Count int64                  `protobuf:"varint,23,opt,name=file_sha256_count,json=fileSha256Count,proto3" json:"file_sha256_count,omitempty"`
	ExtraInfo       string                 `protobuf:"bytes,24,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	ClientVersion   string                 `protobuf:"bytes,25,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	AttackScene     int32                  `protobuf:"varint,26,opt,name=attack_scene,json=attackScene,proto3" json:"attack_scene,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,28,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	DetectedAt      *timestamppb.Timestamp `protobuf:"bytes,29,opt,name=detected_at,json=detectedAt,proto3" json:"detected_at,omitempty"`
}

func (x *FileThreatClue) Reset() {
	*x = FileThreatClue{}
	mi := &file_conan_clue_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileThreatClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileThreatClue) ProtoMessage() {}

func (x *FileThreatClue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileThreatClue.ProtoReflect.Descriptor instead.
func (*FileThreatClue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{3}
}

func (x *FileThreatClue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FileThreatClue) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *FileThreatClue) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *FileThreatClue) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *FileThreatClue) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *FileThreatClue) GetClueLevel() int32 {
	if x != nil {
		return x.ClueLevel
	}
	return 0
}

func (x *FileThreatClue) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *FileThreatClue) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *FileThreatClue) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *FileThreatClue) GetHasIgnore() int32 {
	if x != nil {
		return x.HasIgnore
	}
	return 0
}

func (x *FileThreatClue) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *FileThreatClue) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *FileThreatClue) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *FileThreatClue) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *FileThreatClue) GetIsolateCount() int32 {
	if x != nil {
		return x.IsolateCount
	}
	return 0
}

func (x *FileThreatClue) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *FileThreatClue) GetOperationStatus() int32 {
	if x != nil {
		return x.OperationStatus
	}
	return 0
}

func (x *FileThreatClue) GetOperationFailure() int32 {
	if x != nil {
		return x.OperationFailure
	}
	return 0
}

func (x *FileThreatClue) GetCanIsolate() bool {
	if x != nil {
		return x.CanIsolate
	}
	return false
}

func (x *FileThreatClue) GetAttackSrc() string {
	if x != nil {
		return x.AttackSrc
	}
	return ""
}

func (x *FileThreatClue) GetOccurCount() int64 {
	if x != nil {
		return x.OccurCount
	}
	return 0
}

func (x *FileThreatClue) GetDisposedCount() int64 {
	if x != nil {
		return x.DisposedCount
	}
	return 0
}

func (x *FileThreatClue) GetFileSha256Count() int64 {
	if x != nil {
		return x.FileSha256Count
	}
	return 0
}

func (x *FileThreatClue) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *FileThreatClue) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *FileThreatClue) GetAttackScene() int32 {
	if x != nil {
		return x.AttackScene
	}
	return 0
}

func (x *FileThreatClue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *FileThreatClue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *FileThreatClue) GetDetectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DetectedAt
	}
	return nil
}

type IllegalOutreachClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId       string    `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	UniqueFlag      string    `protobuf:"bytes,3,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ClueKey         string    `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus      int32     `protobuf:"varint,5,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueLevel       int32     `protobuf:"varint,6,opt,name=clue_level,json=clueLevel,proto3" json:"clue_level,omitempty"`
	ClueType        int32     `protobuf:"varint,7,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	ClueSubType     int32     `protobuf:"varint,8,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	AttackSrc       string    `protobuf:"bytes,9,opt,name=attack_src,json=attackSrc,proto3" json:"attack_src,omitempty"`
	OccurCount      int64     `protobuf:"varint,10,opt,name=occur_count,json=occurCount,proto3" json:"occur_count,omitempty"`
	OutreachType    int32     `protobuf:"varint,11,opt,name=outreach_type,json=outreachType,proto3" json:"outreach_type,omitempty"`
	OutreachAddress string    `protobuf:"bytes,12,opt,name=outreach_address,json=outreachAddress,proto3" json:"outreach_address,omitempty"`
	MaliciousCode   string    `protobuf:"bytes,13,opt,name=malicious_code,json=maliciousCode,proto3" json:"malicious_code,omitempty"`
	HitSource       HitSource `protobuf:"varint,14,opt,name=hit_source,json=hitSource,proto3,enum=conan.HitSource" json:"hit_source,omitempty"` // 改为枚举类型
	IsApt           AptStatus `protobuf:"varint,15,opt,name=is_apt,json=isApt,proto3,enum=conan.AptStatus" json:"is_apt,omitempty"`             // 改为枚举类型
	HasIgnore       int32     `protobuf:"varint,16,opt,name=has_ignore,json=hasIgnore,proto3" json:"has_ignore,omitempty"`
	Disposition     int32     `protobuf:"varint,17,opt,name=disposition,proto3" json:"disposition,omitempty"`
	ClientVersion   string    `protobuf:"bytes,18,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	// JSON marshalled string, type: map[string]any
	ExtraInfo  string                 `protobuf:"bytes,19,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	DetectedAt *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=detected_at,json=detectedAt,proto3" json:"detected_at,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// means the number of outreach clues grouped by outreach_address
	OutreachAddressCount int64      `protobuf:"varint,23,opt,name=outreach_address_count,json=outreachAddressCount,proto3" json:"outreach_address_count,omitempty"`
	LlmResult            *LLMResult `protobuf:"bytes,24,opt,name=llm_result,json=llmResult,proto3" json:"llm_result,omitempty"`
}

func (x *IllegalOutreachClue) Reset() {
	*x = IllegalOutreachClue{}
	mi := &file_conan_clue_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IllegalOutreachClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IllegalOutreachClue) ProtoMessage() {}

func (x *IllegalOutreachClue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IllegalOutreachClue.ProtoReflect.Descriptor instead.
func (*IllegalOutreachClue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{4}
}

func (x *IllegalOutreachClue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *IllegalOutreachClue) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *IllegalOutreachClue) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *IllegalOutreachClue) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *IllegalOutreachClue) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *IllegalOutreachClue) GetClueLevel() int32 {
	if x != nil {
		return x.ClueLevel
	}
	return 0
}

func (x *IllegalOutreachClue) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *IllegalOutreachClue) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *IllegalOutreachClue) GetAttackSrc() string {
	if x != nil {
		return x.AttackSrc
	}
	return ""
}

func (x *IllegalOutreachClue) GetOccurCount() int64 {
	if x != nil {
		return x.OccurCount
	}
	return 0
}

func (x *IllegalOutreachClue) GetOutreachType() int32 {
	if x != nil {
		return x.OutreachType
	}
	return 0
}

func (x *IllegalOutreachClue) GetOutreachAddress() string {
	if x != nil {
		return x.OutreachAddress
	}
	return ""
}

func (x *IllegalOutreachClue) GetMaliciousCode() string {
	if x != nil {
		return x.MaliciousCode
	}
	return ""
}

func (x *IllegalOutreachClue) GetHitSource() HitSource {
	if x != nil {
		return x.HitSource
	}
	return HitSource_HS_UNKNOWN
}

func (x *IllegalOutreachClue) GetIsApt() AptStatus {
	if x != nil {
		return x.IsApt
	}
	return AptStatus_AS_UNKNOWN
}

func (x *IllegalOutreachClue) GetHasIgnore() int32 {
	if x != nil {
		return x.HasIgnore
	}
	return 0
}

func (x *IllegalOutreachClue) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *IllegalOutreachClue) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *IllegalOutreachClue) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *IllegalOutreachClue) GetDetectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DetectedAt
	}
	return nil
}

func (x *IllegalOutreachClue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *IllegalOutreachClue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *IllegalOutreachClue) GetOutreachAddressCount() int64 {
	if x != nil {
		return x.OutreachAddressCount
	}
	return 0
}

func (x *IllegalOutreachClue) GetLlmResult() *LLMResult {
	if x != nil {
		return x.LlmResult
	}
	return nil
}

// TODO(pz): to be removed
type UniversalAttackClue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                   int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId            string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	UniqueFlag           string                 `protobuf:"bytes,3,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ClueKey              string                 `protobuf:"bytes,4,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus           int32                  `protobuf:"varint,5,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueLevel            int32                  `protobuf:"varint,6,opt,name=clue_level,json=clueLevel,proto3" json:"clue_level,omitempty"`
	ClueType             int32                  `protobuf:"varint,7,opt,name=clue_type,json=clueType,proto3" json:"clue_type,omitempty"`
	ClueSubType          int32                  `protobuf:"varint,8,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	OccurCount           int64                  `protobuf:"varint,9,opt,name=occur_count,json=occurCount,proto3" json:"occur_count,omitempty"`
	Disposition          int32                  `protobuf:"varint,10,opt,name=disposition,proto3" json:"disposition,omitempty"`
	FileMd5              string                 `protobuf:"bytes,11,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	FileSha256           string                 `protobuf:"bytes,12,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	CanIsolate           bool                   `protobuf:"varint,13,opt,name=can_isolate,json=canIsolate,proto3" json:"can_isolate,omitempty"`
	FileOperationStatus  int32                  `protobuf:"varint,14,opt,name=file_operation_status,json=fileOperationStatus,proto3" json:"file_operation_status,omitempty"`
	FileOperationFailure int32                  `protobuf:"varint,15,opt,name=file_operation_failure,json=fileOperationFailure,proto3" json:"file_operation_failure,omitempty"`
	CreatedAt            *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt            *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DetectedAt           *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=detected_at,json=detectedAt,proto3" json:"detected_at,omitempty"`
}

func (x *UniversalAttackClue) Reset() {
	*x = UniversalAttackClue{}
	mi := &file_conan_clue_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UniversalAttackClue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UniversalAttackClue) ProtoMessage() {}

func (x *UniversalAttackClue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UniversalAttackClue.ProtoReflect.Descriptor instead.
func (*UniversalAttackClue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{5}
}

func (x *UniversalAttackClue) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UniversalAttackClue) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *UniversalAttackClue) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *UniversalAttackClue) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *UniversalAttackClue) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *UniversalAttackClue) GetClueLevel() int32 {
	if x != nil {
		return x.ClueLevel
	}
	return 0
}

func (x *UniversalAttackClue) GetClueType() int32 {
	if x != nil {
		return x.ClueType
	}
	return 0
}

func (x *UniversalAttackClue) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *UniversalAttackClue) GetOccurCount() int64 {
	if x != nil {
		return x.OccurCount
	}
	return 0
}

func (x *UniversalAttackClue) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *UniversalAttackClue) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *UniversalAttackClue) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *UniversalAttackClue) GetCanIsolate() bool {
	if x != nil {
		return x.CanIsolate
	}
	return false
}

func (x *UniversalAttackClue) GetFileOperationStatus() int32 {
	if x != nil {
		return x.FileOperationStatus
	}
	return 0
}

func (x *UniversalAttackClue) GetFileOperationFailure() int32 {
	if x != nil {
		return x.FileOperationFailure
	}
	return 0
}

func (x *UniversalAttackClue) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *UniversalAttackClue) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *UniversalAttackClue) GetDetectedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DetectedAt
	}
	return nil
}

type ListMemoryAttackCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues []*MemoryAttackClue `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	Page  *PageResponse       `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListMemoryAttackCluesResp) Reset() {
	*x = ListMemoryAttackCluesResp{}
	mi := &file_conan_clue_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMemoryAttackCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMemoryAttackCluesResp) ProtoMessage() {}

func (x *ListMemoryAttackCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMemoryAttackCluesResp.ProtoReflect.Descriptor instead.
func (*ListMemoryAttackCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{6}
}

func (x *ListMemoryAttackCluesResp) GetClues() []*MemoryAttackClue {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ListMemoryAttackCluesResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListFileThreatCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListFileThreatCluesReq) Reset() {
	*x = ListFileThreatCluesReq{}
	mi := &file_conan_clue_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFileThreatCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFileThreatCluesReq) ProtoMessage() {}

func (x *ListFileThreatCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFileThreatCluesReq.ProtoReflect.Descriptor instead.
func (*ListFileThreatCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{7}
}

func (x *ListFileThreatCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListFileThreatCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListFileThreatCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues []*FileThreatClue `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	Page  *PageResponse     `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListFileThreatCluesResp) Reset() {
	*x = ListFileThreatCluesResp{}
	mi := &file_conan_clue_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFileThreatCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFileThreatCluesResp) ProtoMessage() {}

func (x *ListFileThreatCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFileThreatCluesResp.ProtoReflect.Descriptor instead.
func (*ListFileThreatCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{8}
}

func (x *ListFileThreatCluesResp) GetClues() []*FileThreatClue {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ListFileThreatCluesResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListSystemAttackCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListSystemAttackCluesReq) Reset() {
	*x = ListSystemAttackCluesReq{}
	mi := &file_conan_clue_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSystemAttackCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSystemAttackCluesReq) ProtoMessage() {}

func (x *ListSystemAttackCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSystemAttackCluesReq.ProtoReflect.Descriptor instead.
func (*ListSystemAttackCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{9}
}

func (x *ListSystemAttackCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListSystemAttackCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListIllegalOutreachCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filter *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Page   *PageRequest           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListIllegalOutreachCluesReq) Reset() {
	*x = ListIllegalOutreachCluesReq{}
	mi := &file_conan_clue_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIllegalOutreachCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIllegalOutreachCluesReq) ProtoMessage() {}

func (x *ListIllegalOutreachCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIllegalOutreachCluesReq.ProtoReflect.Descriptor instead.
func (*ListIllegalOutreachCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{10}
}

func (x *ListIllegalOutreachCluesReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListIllegalOutreachCluesReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListIllegalOutreachCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues []*IllegalOutreachClue `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	Page  *PageResponse          `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListIllegalOutreachCluesResp) Reset() {
	*x = ListIllegalOutreachCluesResp{}
	mi := &file_conan_clue_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListIllegalOutreachCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIllegalOutreachCluesResp) ProtoMessage() {}

func (x *ListIllegalOutreachCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIllegalOutreachCluesResp.ProtoReflect.Descriptor instead.
func (*ListIllegalOutreachCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{11}
}

func (x *ListIllegalOutreachCluesResp) GetClues() []*IllegalOutreachClue {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ListIllegalOutreachCluesResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListSystemAttackCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues []*SystemAttackClue `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	Page  *PageResponse       `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListSystemAttackCluesResp) Reset() {
	*x = ListSystemAttackCluesResp{}
	mi := &file_conan_clue_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSystemAttackCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSystemAttackCluesResp) ProtoMessage() {}

func (x *ListSystemAttackCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSystemAttackCluesResp.ProtoReflect.Descriptor instead.
func (*ListSystemAttackCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{12}
}

func (x *ListSystemAttackCluesResp) GetClues() []*SystemAttackClue {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ListSystemAttackCluesResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type CountCluesBySha256Req struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CountCluesBySha256Req) Reset() {
	*x = CountCluesBySha256Req{}
	mi := &file_conan_clue_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesBySha256Req) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesBySha256Req) ProtoMessage() {}

func (x *CountCluesBySha256Req) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesBySha256Req.ProtoReflect.Descriptor instead.
func (*CountCluesBySha256Req) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{13}
}

type CountCluesBySha256Resp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total  int64          `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Counts []*Sha256Count `protobuf:"bytes,2,rep,name=counts,proto3" json:"counts,omitempty"`
}

func (x *CountCluesBySha256Resp) Reset() {
	*x = CountCluesBySha256Resp{}
	mi := &file_conan_clue_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesBySha256Resp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesBySha256Resp) ProtoMessage() {}

func (x *CountCluesBySha256Resp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesBySha256Resp.ProtoReflect.Descriptor instead.
func (*CountCluesBySha256Resp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{14}
}

func (x *CountCluesBySha256Resp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *CountCluesBySha256Resp) GetCounts() []*Sha256Count {
	if x != nil {
		return x.Counts
	}
	return nil
}

// The number of file clues corresponding to the file SHA256
type Sha256Count struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileSha256 string `protobuf:"bytes,1,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	Count      int64  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *Sha256Count) Reset() {
	*x = Sha256Count{}
	mi := &file_conan_clue_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Sha256Count) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Sha256Count) ProtoMessage() {}

func (x *Sha256Count) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Sha256Count.ProtoReflect.Descriptor instead.
func (*Sha256Count) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{15}
}

func (x *Sha256Count) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *Sha256Count) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ListTopMachineCluesReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Limit     int32      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ListTopMachineCluesReq) Reset() {
	*x = ListTopMachineCluesReq{}
	mi := &file_conan_clue_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopMachineCluesReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopMachineCluesReq) ProtoMessage() {}

func (x *ListTopMachineCluesReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopMachineCluesReq.ProtoReflect.Descriptor instead.
func (*ListTopMachineCluesReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{16}
}

func (x *ListTopMachineCluesReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListTopMachineCluesReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListTopMachineCluesResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Machines []*MachineClueCount `protobuf:"bytes,1,rep,name=machines,proto3" json:"machines,omitempty"`
}

func (x *ListTopMachineCluesResp) Reset() {
	*x = ListTopMachineCluesResp{}
	mi := &file_conan_clue_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopMachineCluesResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopMachineCluesResp) ProtoMessage() {}

func (x *ListTopMachineCluesResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopMachineCluesResp.ProtoReflect.Descriptor instead.
func (*ListTopMachineCluesResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{17}
}

func (x *ListTopMachineCluesResp) GetMachines() []*MachineClueCount {
	if x != nil {
		return x.Machines
	}
	return nil
}

// The number of clues counted by machine dimension
type MachineClueCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MachineId    string `protobuf:"bytes,1,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	MachineIp    string `protobuf:"bytes,2,opt,name=machine_ip,json=machineIp,proto3" json:"machine_ip,omitempty"`
	MachineName  string `protobuf:"bytes,3,opt,name=machine_name,json=machineName,proto3" json:"machine_name,omitempty"`
	MachineGroup string `protobuf:"bytes,4,opt,name=machine_group,json=machineGroup,proto3" json:"machine_group,omitempty"`
	ClueCount    int64  `protobuf:"varint,5,opt,name=clue_count,json=clueCount,proto3" json:"clue_count,omitempty"`
	Online       int32  `protobuf:"varint,6,opt,name=online,proto3" json:"online,omitempty"` // 1: 在线, 2: 离线
}

func (x *MachineClueCount) Reset() {
	*x = MachineClueCount{}
	mi := &file_conan_clue_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MachineClueCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MachineClueCount) ProtoMessage() {}

func (x *MachineClueCount) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MachineClueCount.ProtoReflect.Descriptor instead.
func (*MachineClueCount) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{18}
}

func (x *MachineClueCount) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *MachineClueCount) GetMachineIp() string {
	if x != nil {
		return x.MachineIp
	}
	return ""
}

func (x *MachineClueCount) GetMachineName() string {
	if x != nil {
		return x.MachineName
	}
	return ""
}

func (x *MachineClueCount) GetMachineGroup() string {
	if x != nil {
		return x.MachineGroup
	}
	return ""
}

func (x *MachineClueCount) GetClueCount() int64 {
	if x != nil {
		return x.ClueCount
	}
	return 0
}

func (x *MachineClueCount) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

type GetClueStatsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetClueStatsReq) Reset() {
	*x = GetClueStatsReq{}
	mi := &file_conan_clue_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClueStatsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClueStatsReq) ProtoMessage() {}

func (x *GetClueStatsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClueStatsReq.ProtoReflect.Descriptor instead.
func (*GetClueStatsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{19}
}

// Clue stats
type ClueStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The total number of attack clues
	ClueCount int64 `protobuf:"varint,1,opt,name=clue_count,json=clueCount,proto3" json:"clue_count,omitempty"`
	// The number of clues that have been disposed
	DisposedCount int64 `protobuf:"varint,2,opt,name=disposed_count,json=disposedCount,proto3" json:"disposed_count,omitempty"`
	// The number of clues that have been collected
	EvidenceCount int64 `protobuf:"varint,3,opt,name=evidence_count,json=evidenceCount,proto3" json:"evidence_count,omitempty"`
	// The number of affected terminals
	AffectedTerminalCount int64 `protobuf:"varint,4,opt,name=affected_terminal_count,json=affectedTerminalCount,proto3" json:"affected_terminal_count,omitempty"`
	// The number of waiting for disposal
	WaitingForDisposalCount int64 `protobuf:"varint,5,opt,name=waiting_for_disposal_count,json=waitingForDisposalCount,proto3" json:"waiting_for_disposal_count,omitempty"`
}

func (x *ClueStats) Reset() {
	*x = ClueStats{}
	mi := &file_conan_clue_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueStats) ProtoMessage() {}

func (x *ClueStats) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueStats.ProtoReflect.Descriptor instead.
func (*ClueStats) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{20}
}

func (x *ClueStats) GetClueCount() int64 {
	if x != nil {
		return x.ClueCount
	}
	return 0
}

func (x *ClueStats) GetDisposedCount() int64 {
	if x != nil {
		return x.DisposedCount
	}
	return 0
}

func (x *ClueStats) GetEvidenceCount() int64 {
	if x != nil {
		return x.EvidenceCount
	}
	return 0
}

func (x *ClueStats) GetAffectedTerminalCount() int64 {
	if x != nil {
		return x.AffectedTerminalCount
	}
	return 0
}

func (x *ClueStats) GetWaitingForDisposalCount() int64 {
	if x != nil {
		return x.WaitingForDisposalCount
	}
	return 0
}

type ListTopMachineClueCountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Limit     int32      `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (x *ListTopMachineClueCountsReq) Reset() {
	*x = ListTopMachineClueCountsReq{}
	mi := &file_conan_clue_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopMachineClueCountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopMachineClueCountsReq) ProtoMessage() {}

func (x *ListTopMachineClueCountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopMachineClueCountsReq.ProtoReflect.Descriptor instead.
func (*ListTopMachineClueCountsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{21}
}

func (x *ListTopMachineClueCountsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListTopMachineClueCountsReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListTopMachineClueCountsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListTopMachineClueCountsResp) Reset() {
	*x = ListTopMachineClueCountsResp{}
	mi := &file_conan_clue_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopMachineClueCountsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopMachineClueCountsResp) ProtoMessage() {}

func (x *ListTopMachineClueCountsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopMachineClueCountsResp.ProtoReflect.Descriptor instead.
func (*ListTopMachineClueCountsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{22}
}

type ListOutreachAffectedTerminalsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OutreachType    int32        `protobuf:"varint,1,opt,name=outreach_type,json=outreachType,proto3" json:"outreach_type,omitempty"`
	OutreachAddress string       `protobuf:"bytes,2,opt,name=outreach_address,json=outreachAddress,proto3" json:"outreach_address,omitempty"`
	Ip              string       `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	TimeRange       *TimeRange   `protobuf:"bytes,4,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Page            *PageRequest `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListOutreachAffectedTerminalsReq) Reset() {
	*x = ListOutreachAffectedTerminalsReq{}
	mi := &file_conan_clue_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOutreachAffectedTerminalsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOutreachAffectedTerminalsReq) ProtoMessage() {}

func (x *ListOutreachAffectedTerminalsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOutreachAffectedTerminalsReq.ProtoReflect.Descriptor instead.
func (*ListOutreachAffectedTerminalsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{23}
}

func (x *ListOutreachAffectedTerminalsReq) GetOutreachType() int32 {
	if x != nil {
		return x.OutreachType
	}
	return 0
}

func (x *ListOutreachAffectedTerminalsReq) GetOutreachAddress() string {
	if x != nil {
		return x.OutreachAddress
	}
	return ""
}

func (x *ListOutreachAffectedTerminalsReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListOutreachAffectedTerminalsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListOutreachAffectedTerminalsReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListOutreachAffectedTerminalsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Terminals []*OutreachAffectedTerminal `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
	Page      *PageResponse               `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListOutreachAffectedTerminalsResp) Reset() {
	*x = ListOutreachAffectedTerminalsResp{}
	mi := &file_conan_clue_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListOutreachAffectedTerminalsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListOutreachAffectedTerminalsResp) ProtoMessage() {}

func (x *ListOutreachAffectedTerminalsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListOutreachAffectedTerminalsResp.ProtoReflect.Descriptor instead.
func (*ListOutreachAffectedTerminalsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{24}
}

func (x *ListOutreachAffectedTerminalsResp) GetTerminals() []*OutreachAffectedTerminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *ListOutreachAffectedTerminalsResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type OutreachAffectedTerminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId     string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	ClueKey       string                 `protobuf:"bytes,3,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	UniqueFlagMd5 string                 `protobuf:"bytes,4,opt,name=unique_flag_md5,json=uniqueFlagMd5,proto3" json:"unique_flag_md5,omitempty"`
	ExtraInfo     string                 `protobuf:"bytes,5,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *OutreachAffectedTerminal) Reset() {
	*x = OutreachAffectedTerminal{}
	mi := &file_conan_clue_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachAffectedTerminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachAffectedTerminal) ProtoMessage() {}

func (x *OutreachAffectedTerminal) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachAffectedTerminal.ProtoReflect.Descriptor instead.
func (*OutreachAffectedTerminal) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{25}
}

func (x *OutreachAffectedTerminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *OutreachAffectedTerminal) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *OutreachAffectedTerminal) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *OutreachAffectedTerminal) GetUniqueFlagMd5() string {
	if x != nil {
		return x.UniqueFlagMd5
	}
	return ""
}

func (x *OutreachAffectedTerminal) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *OutreachAffectedTerminal) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

type CountOutreachCluesByTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CountOutreachCluesByTypeReq) Reset() {
	*x = CountOutreachCluesByTypeReq{}
	mi := &file_conan_clue_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountOutreachCluesByTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOutreachCluesByTypeReq) ProtoMessage() {}

func (x *CountOutreachCluesByTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOutreachCluesByTypeReq.ProtoReflect.Descriptor instead.
func (*CountOutreachCluesByTypeReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{26}
}

type CountOutreachCluesByTypeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Counts []*OutreachTypeCount `protobuf:"bytes,1,rep,name=counts,proto3" json:"counts,omitempty"`
}

func (x *CountOutreachCluesByTypeResp) Reset() {
	*x = CountOutreachCluesByTypeResp{}
	mi := &file_conan_clue_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountOutreachCluesByTypeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountOutreachCluesByTypeResp) ProtoMessage() {}

func (x *CountOutreachCluesByTypeResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountOutreachCluesByTypeResp.ProtoReflect.Descriptor instead.
func (*CountOutreachCluesByTypeResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{27}
}

func (x *CountOutreachCluesByTypeResp) GetCounts() []*OutreachTypeCount {
	if x != nil {
		return x.Counts
	}
	return nil
}

type OutreachTypeCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  OutreachType `protobuf:"varint,1,opt,name=type,proto3,enum=conan.OutreachType" json:"type,omitempty"`
	Count int64        `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *OutreachTypeCount) Reset() {
	*x = OutreachTypeCount{}
	mi := &file_conan_clue_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachTypeCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachTypeCount) ProtoMessage() {}

func (x *OutreachTypeCount) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachTypeCount.ProtoReflect.Descriptor instead.
func (*OutreachTypeCount) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{28}
}

func (x *OutreachTypeCount) GetType() OutreachType {
	if x != nil {
		return x.Type
	}
	return OutreachType_OT_UNKNOWN
}

func (x *OutreachTypeCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ListClueTypeCountsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClueType  ClueType   `protobuf:"varint,1,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	TimeRange *TimeRange `protobuf:"bytes,2,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *ListClueTypeCountsReq) Reset() {
	*x = ListClueTypeCountsReq{}
	mi := &file_conan_clue_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClueTypeCountsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClueTypeCountsReq) ProtoMessage() {}

func (x *ListClueTypeCountsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClueTypeCountsReq.ProtoReflect.Descriptor instead.
func (*ListClueTypeCountsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{29}
}

func (x *ListClueTypeCountsReq) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ListClueTypeCountsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type ListClueTypeCountsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Counts []*ClueTypeCount `protobuf:"bytes,1,rep,name=counts,proto3" json:"counts,omitempty"`
}

func (x *ListClueTypeCountsResp) Reset() {
	*x = ListClueTypeCountsResp{}
	mi := &file_conan_clue_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClueTypeCountsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClueTypeCountsResp) ProtoMessage() {}

func (x *ListClueTypeCountsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClueTypeCountsResp.ProtoReflect.Descriptor instead.
func (*ListClueTypeCountsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{30}
}

func (x *ListClueTypeCountsResp) GetCounts() []*ClueTypeCount {
	if x != nil {
		return x.Counts
	}
	return nil
}

type ClueTypeCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClueType         ClueType               `protobuf:"varint,1,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	ClueSubType      int32                  `protobuf:"varint,2,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	Count            int64                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	LatestCreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=latest_create_time,json=latestCreateTime,proto3" json:"latest_create_time,omitempty"`
}

func (x *ClueTypeCount) Reset() {
	*x = ClueTypeCount{}
	mi := &file_conan_clue_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueTypeCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueTypeCount) ProtoMessage() {}

func (x *ClueTypeCount) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueTypeCount.ProtoReflect.Descriptor instead.
func (*ClueTypeCount) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{31}
}

func (x *ClueTypeCount) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ClueTypeCount) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *ClueTypeCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ClueTypeCount) GetLatestCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestCreateTime
	}
	return nil
}

// ProcessInfo represents process-related information in an attack
type ProcessInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pid                  int32  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`                                                                   // Process ID
	Ppid                 int32  `protobuf:"varint,2,opt,name=ppid,proto3" json:"ppid,omitempty"`                                                                 // Parent Process ID
	ProcName             string `protobuf:"bytes,3,opt,name=proc_name,json=procName,proto3" json:"proc_name,omitempty"`                                          // Process name
	ProcPath             string `protobuf:"bytes,4,opt,name=proc_path,json=procPath,proto3" json:"proc_path,omitempty"`                                          // Process path
	DstProcPath          string `protobuf:"bytes,5,opt,name=dst_proc_path,json=dstProcPath,proto3" json:"dst_proc_path,omitempty"`                               // Destination process path
	ParentProcPath       string `protobuf:"bytes,6,opt,name=parent_proc_path,json=parentProcPath,proto3" json:"parent_proc_path,omitempty"`                      // Parent process path
	ParentProcCreateTime int64  `protobuf:"varint,7,opt,name=parent_proc_create_time,json=parentProcCreateTime,proto3" json:"parent_proc_create_time,omitempty"` // Parent process creation time
	ProcUser             string `protobuf:"bytes,8,opt,name=proc_user,json=procUser,proto3" json:"proc_user,omitempty"`                                          // Process user
	ProcMd5              string `protobuf:"bytes,9,opt,name=proc_md5,json=procMd5,proto3" json:"proc_md5,omitempty"`                                             // Process MD5
	ProcSha256           string `protobuf:"bytes,10,opt,name=proc_sha256,json=procSha256,proto3" json:"proc_sha256,omitempty"`                                   // Process SHA256
	ProcCommand          string `protobuf:"bytes,11,opt,name=proc_command,json=procCommand,proto3" json:"proc_command,omitempty"`                                // Process command line
	ProcPermission       string `protobuf:"bytes,12,opt,name=proc_permission,json=procPermission,proto3" json:"proc_permission,omitempty"`                       // Process permissions
	ProcStartTime        int64  `protobuf:"varint,13,opt,name=proc_start_time,json=procStartTime,proto3" json:"proc_start_time,omitempty"`                       // Process start time
	IsX86Process         uint32 `protobuf:"varint,14,opt,name=is_x86_process,json=isX86Process,proto3" json:"is_x86_process,omitempty"`                          // Whether it's an x86 process
	SubProcName          string `protobuf:"bytes,15,opt,name=sub_proc_name,json=subProcName,proto3" json:"sub_proc_name,omitempty"`                              // Sub-process name
	SubProcPath          string `protobuf:"bytes,16,opt,name=sub_proc_path,json=subProcPath,proto3" json:"sub_proc_path,omitempty"`                              // Sub-process path
	SubProcMd5           string `protobuf:"bytes,17,opt,name=sub_proc_md5,json=subProcMd5,proto3" json:"sub_proc_md5,omitempty"`                                 // Sub-process MD5
	SubProcCommand       string `protobuf:"bytes,18,opt,name=sub_proc_command,json=subProcCommand,proto3" json:"sub_proc_command,omitempty"`                     // Sub-process command line
	FileAccessTime       int64  `protobuf:"varint,19,opt,name=file_access_time,json=fileAccessTime,proto3" json:"file_access_time,omitempty"`                    // File access time
	FileModifyTime       int64  `protobuf:"varint,20,opt,name=file_modify_time,json=fileModifyTime,proto3" json:"file_modify_time,omitempty"`                    // File modification time
	FileCreateTime       int64  `protobuf:"varint,21,opt,name=file_create_time,json=fileCreateTime,proto3" json:"file_create_time,omitempty"`                    // File creation time
	FileSize             int64  `protobuf:"varint,22,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                                        // File size
	FilePath             string `protobuf:"bytes,23,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                                         // File path
	FileCompanyName      string `protobuf:"bytes,24,opt,name=file_company_name,json=fileCompanyName,proto3" json:"file_company_name,omitempty"`                  // Process file vendor
	FileVersion          string `protobuf:"bytes,25,opt,name=file_version,json=fileVersion,proto3" json:"file_version,omitempty"`                                // Process file version
	Euid                 int64  `protobuf:"varint,26,opt,name=euid,proto3" json:"euid,omitempty"`                                                                // Effective UID
	CurProcInfo          string `protobuf:"bytes,27,opt,name=cur_proc_info,json=curProcInfo,proto3" json:"cur_proc_info,omitempty"`                              // Current process info
	ParentProcInfo       string `protobuf:"bytes,28,opt,name=parent_proc_info,json=parentProcInfo,proto3" json:"parent_proc_info,omitempty"`                     // Parent process info
	TargetProcInfo       string `protobuf:"bytes,29,opt,name=target_proc_info,json=targetProcInfo,proto3" json:"target_proc_info,omitempty"`                     // Target process info
}

func (x *ProcessInfo) Reset() {
	*x = ProcessInfo{}
	mi := &file_conan_clue_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessInfo) ProtoMessage() {}

func (x *ProcessInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessInfo.ProtoReflect.Descriptor instead.
func (*ProcessInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{32}
}

func (x *ProcessInfo) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *ProcessInfo) GetPpid() int32 {
	if x != nil {
		return x.Ppid
	}
	return 0
}

func (x *ProcessInfo) GetProcName() string {
	if x != nil {
		return x.ProcName
	}
	return ""
}

func (x *ProcessInfo) GetProcPath() string {
	if x != nil {
		return x.ProcPath
	}
	return ""
}

func (x *ProcessInfo) GetDstProcPath() string {
	if x != nil {
		return x.DstProcPath
	}
	return ""
}

func (x *ProcessInfo) GetParentProcPath() string {
	if x != nil {
		return x.ParentProcPath
	}
	return ""
}

func (x *ProcessInfo) GetParentProcCreateTime() int64 {
	if x != nil {
		return x.ParentProcCreateTime
	}
	return 0
}

func (x *ProcessInfo) GetProcUser() string {
	if x != nil {
		return x.ProcUser
	}
	return ""
}

func (x *ProcessInfo) GetProcMd5() string {
	if x != nil {
		return x.ProcMd5
	}
	return ""
}

func (x *ProcessInfo) GetProcSha256() string {
	if x != nil {
		return x.ProcSha256
	}
	return ""
}

func (x *ProcessInfo) GetProcCommand() string {
	if x != nil {
		return x.ProcCommand
	}
	return ""
}

func (x *ProcessInfo) GetProcPermission() string {
	if x != nil {
		return x.ProcPermission
	}
	return ""
}

func (x *ProcessInfo) GetProcStartTime() int64 {
	if x != nil {
		return x.ProcStartTime
	}
	return 0
}

func (x *ProcessInfo) GetIsX86Process() uint32 {
	if x != nil {
		return x.IsX86Process
	}
	return 0
}

func (x *ProcessInfo) GetSubProcName() string {
	if x != nil {
		return x.SubProcName
	}
	return ""
}

func (x *ProcessInfo) GetSubProcPath() string {
	if x != nil {
		return x.SubProcPath
	}
	return ""
}

func (x *ProcessInfo) GetSubProcMd5() string {
	if x != nil {
		return x.SubProcMd5
	}
	return ""
}

func (x *ProcessInfo) GetSubProcCommand() string {
	if x != nil {
		return x.SubProcCommand
	}
	return ""
}

func (x *ProcessInfo) GetFileAccessTime() int64 {
	if x != nil {
		return x.FileAccessTime
	}
	return 0
}

func (x *ProcessInfo) GetFileModifyTime() int64 {
	if x != nil {
		return x.FileModifyTime
	}
	return 0
}

func (x *ProcessInfo) GetFileCreateTime() int64 {
	if x != nil {
		return x.FileCreateTime
	}
	return 0
}

func (x *ProcessInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *ProcessInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ProcessInfo) GetFileCompanyName() string {
	if x != nil {
		return x.FileCompanyName
	}
	return ""
}

func (x *ProcessInfo) GetFileVersion() string {
	if x != nil {
		return x.FileVersion
	}
	return ""
}

func (x *ProcessInfo) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *ProcessInfo) GetCurProcInfo() string {
	if x != nil {
		return x.CurProcInfo
	}
	return ""
}

func (x *ProcessInfo) GetParentProcInfo() string {
	if x != nil {
		return x.ParentProcInfo
	}
	return ""
}

func (x *ProcessInfo) GetTargetProcInfo() string {
	if x != nil {
		return x.TargetProcInfo
	}
	return ""
}

// FileInfo represents file-related information in an attack
type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileType       int32  `protobuf:"varint,1,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`                      // File type
	FileName       string `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`                       // File name
	FilePath       string `protobuf:"bytes,3,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                       // File path
	FileMd5        string `protobuf:"bytes,4,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`                          // File MD5
	FileSha256     string `protobuf:"bytes,5,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`                 // File SHA256
	FileSha1       string `protobuf:"bytes,6,opt,name=file_sha1,json=fileSha1,proto3" json:"file_sha1,omitempty"`                       // File SHA1
	FileAccessTime int64  `protobuf:"varint,7,opt,name=file_access_time,json=fileAccessTime,proto3" json:"file_access_time,omitempty"`  // File access time
	FileModifyTime int64  `protobuf:"varint,8,opt,name=file_modify_time,json=fileModifyTime,proto3" json:"file_modify_time,omitempty"`  // File modify time
	FileCreateTime int64  `protobuf:"varint,9,opt,name=file_create_time,json=fileCreateTime,proto3" json:"file_create_time,omitempty"`  // File creation time
	FileSize       int64  `protobuf:"varint,10,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`                     // File size
	FilePermission string `protobuf:"bytes,11,opt,name=file_permission,json=filePermission,proto3" json:"file_permission,omitempty"`    // File permissions
	FileUser       string `protobuf:"bytes,12,opt,name=file_user,json=fileUser,proto3" json:"file_user,omitempty"`                      // File owner
	FileClueSource uint32 `protobuf:"varint,13,opt,name=file_clue_source,json=fileClueSource,proto3" json:"file_clue_source,omitempty"` // File clue source: 1.agent 2.server
	SignatureInfo  string `protobuf:"bytes,14,opt,name=signature_info,json=signatureInfo,proto3" json:"signature_info,omitempty"`       // Signature information
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_conan_clue_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{33}
}

func (x *FileInfo) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *FileInfo) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *FileInfo) GetFileMd5() string {
	if x != nil {
		return x.FileMd5
	}
	return ""
}

func (x *FileInfo) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *FileInfo) GetFileSha1() string {
	if x != nil {
		return x.FileSha1
	}
	return ""
}

func (x *FileInfo) GetFileAccessTime() int64 {
	if x != nil {
		return x.FileAccessTime
	}
	return 0
}

func (x *FileInfo) GetFileModifyTime() int64 {
	if x != nil {
		return x.FileModifyTime
	}
	return 0
}

func (x *FileInfo) GetFileCreateTime() int64 {
	if x != nil {
		return x.FileCreateTime
	}
	return 0
}

func (x *FileInfo) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileInfo) GetFilePermission() string {
	if x != nil {
		return x.FilePermission
	}
	return ""
}

func (x *FileInfo) GetFileUser() string {
	if x != nil {
		return x.FileUser
	}
	return ""
}

func (x *FileInfo) GetFileClueSource() uint32 {
	if x != nil {
		return x.FileClueSource
	}
	return 0
}

func (x *FileInfo) GetSignatureInfo() string {
	if x != nil {
		return x.SignatureInfo
	}
	return ""
}

// DriverInfo represents driver-related information in an attack
type DriverInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DriverName      string `protobuf:"bytes,1,opt,name=driver_name,json=driverName,proto3" json:"driver_name,omitempty"`                  // Kernel driver name
	DriverPath      string `protobuf:"bytes,2,opt,name=driver_path,json=driverPath,proto3" json:"driver_path,omitempty"`                  // Kernel driver path
	DriverRiskType  int32  `protobuf:"varint,3,opt,name=driver_risk_type,json=driverRiskType,proto3" json:"driver_risk_type,omitempty"`   // Kernel risk type
	KernelMod       string `protobuf:"bytes,4,opt,name=kernel_mod,json=kernelMod,proto3" json:"kernel_mod,omitempty"`                     // Kernel module
	RemoteMod       string `protobuf:"bytes,5,opt,name=remote_mod,json=remoteMod,proto3" json:"remote_mod,omitempty"`                     // Remote module
	FirstDriverInfo string `protobuf:"bytes,6,opt,name=first_driver_info,json=firstDriverInfo,proto3" json:"first_driver_info,omitempty"` // First driver process info, just used in def.MemKernelLeakDriverThreadType
}

func (x *DriverInfo) Reset() {
	*x = DriverInfo{}
	mi := &file_conan_clue_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DriverInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DriverInfo) ProtoMessage() {}

func (x *DriverInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DriverInfo.ProtoReflect.Descriptor instead.
func (*DriverInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{34}
}

func (x *DriverInfo) GetDriverName() string {
	if x != nil {
		return x.DriverName
	}
	return ""
}

func (x *DriverInfo) GetDriverPath() string {
	if x != nil {
		return x.DriverPath
	}
	return ""
}

func (x *DriverInfo) GetDriverRiskType() int32 {
	if x != nil {
		return x.DriverRiskType
	}
	return 0
}

func (x *DriverInfo) GetKernelMod() string {
	if x != nil {
		return x.KernelMod
	}
	return ""
}

func (x *DriverInfo) GetRemoteMod() string {
	if x != nil {
		return x.RemoteMod
	}
	return ""
}

func (x *DriverInfo) GetFirstDriverInfo() string {
	if x != nil {
		return x.FirstDriverInfo
	}
	return ""
}

// MaliciousInfo represents malicious code information
type MaliciousInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MaliciousCode          string `protobuf:"bytes,1,opt,name=malicious_code,json=maliciousCode,proto3" json:"malicious_code,omitempty"`                                // Malicious code family
	MaliciousTypes         string `protobuf:"bytes,2,opt,name=malicious_types,json=maliciousTypes,proto3" json:"malicious_types,omitempty"`                             // Suspicious types
	MaliciousDde           string `protobuf:"bytes,3,opt,name=malicious_dde,json=maliciousDde,proto3" json:"malicious_dde,omitempty"`                                   // Suspicious DDE code
	MaliciousVba           string `protobuf:"bytes,4,opt,name=malicious_vba,json=maliciousVba,proto3" json:"malicious_vba,omitempty"`                                   // Suspicious VBA code
	MaliciousLnkTargetPath string `protobuf:"bytes,5,opt,name=malicious_lnk_target_path,json=maliciousLnkTargetPath,proto3" json:"malicious_lnk_target_path,omitempty"` // Suspicious LNK target path
	MaliciousLnkWorkingDir string `protobuf:"bytes,6,opt,name=malicious_lnk_working_dir,json=maliciousLnkWorkingDir,proto3" json:"malicious_lnk_working_dir,omitempty"` // Suspicious LNK working directory
	MaliciousLnkCmdLine    string `protobuf:"bytes,7,opt,name=malicious_lnk_cmd_line,json=maliciousLnkCmdLine,proto3" json:"malicious_lnk_cmd_line,omitempty"`          // Suspicious LNK command line
	MaliciousLnkIconPath   string `protobuf:"bytes,8,opt,name=malicious_lnk_icon_path,json=maliciousLnkIconPath,proto3" json:"malicious_lnk_icon_path,omitempty"`       // Suspicious LNK icon path
	MaliciousUrl           string `protobuf:"bytes,9,opt,name=malicious_url,json=maliciousUrl,proto3" json:"malicious_url,omitempty"`                                   // Suspicious icon URL
	SlRuleName             string `protobuf:"bytes,10,opt,name=sl_rule_name,json=slRuleName,proto3" json:"sl_rule_name,omitempty"`                                      // SL rule name
	SlRuleDetail           string `protobuf:"bytes,11,opt,name=sl_rule_detail,json=slRuleDetail,proto3" json:"sl_rule_detail,omitempty"`                                // SL rule details
}

func (x *MaliciousInfo) Reset() {
	*x = MaliciousInfo{}
	mi := &file_conan_clue_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MaliciousInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MaliciousInfo) ProtoMessage() {}

func (x *MaliciousInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MaliciousInfo.ProtoReflect.Descriptor instead.
func (*MaliciousInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{35}
}

func (x *MaliciousInfo) GetMaliciousCode() string {
	if x != nil {
		return x.MaliciousCode
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousTypes() string {
	if x != nil {
		return x.MaliciousTypes
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousDde() string {
	if x != nil {
		return x.MaliciousDde
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousVba() string {
	if x != nil {
		return x.MaliciousVba
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousLnkTargetPath() string {
	if x != nil {
		return x.MaliciousLnkTargetPath
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousLnkWorkingDir() string {
	if x != nil {
		return x.MaliciousLnkWorkingDir
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousLnkCmdLine() string {
	if x != nil {
		return x.MaliciousLnkCmdLine
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousLnkIconPath() string {
	if x != nil {
		return x.MaliciousLnkIconPath
	}
	return ""
}

func (x *MaliciousInfo) GetMaliciousUrl() string {
	if x != nil {
		return x.MaliciousUrl
	}
	return ""
}

func (x *MaliciousInfo) GetSlRuleName() string {
	if x != nil {
		return x.SlRuleName
	}
	return ""
}

func (x *MaliciousInfo) GetSlRuleDetail() string {
	if x != nil {
		return x.SlRuleDetail
	}
	return ""
}

// OutreachInfo represents external connection information
type OutreachInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Euid            int64  `protobuf:"varint,1,opt,name=euid,proto3" json:"euid,omitempty"`                                             // Effective UID
	OutreachIp      string `protobuf:"bytes,2,opt,name=outreach_ip,json=outreachIp,proto3" json:"outreach_ip,omitempty"`                // External IP
	OutreachType    uint32 `protobuf:"varint,3,opt,name=outreach_type,json=outreachType,proto3" json:"outreach_type,omitempty"`         // External connection type
	OutreachPort    int32  `protobuf:"varint,4,opt,name=outreach_port,json=outreachPort,proto3" json:"outreach_port,omitempty"`         // External port
	OutreachDomain  string `protobuf:"bytes,5,opt,name=outreach_domain,json=outreachDomain,proto3" json:"outreach_domain,omitempty"`    // External domain
	OutreachAddress string `protobuf:"bytes,6,opt,name=outreach_address,json=outreachAddress,proto3" json:"outreach_address,omitempty"` // External address
	IpVersion       string `protobuf:"bytes,7,opt,name=ip_version,json=ipVersion,proto3" json:"ip_version,omitempty"`                   // IP version
	Direction       string `protobuf:"bytes,8,opt,name=direction,proto3" json:"direction,omitempty"`                                    // Directional attack type
	Confidence      int32  `protobuf:"varint,9,opt,name=confidence,proto3" json:"confidence,omitempty"`                                 // Confidence level
	IsApt           int32  `protobuf:"varint,10,opt,name=is_apt,json=isApt,proto3" json:"is_apt,omitempty"`                             // Whether hit APT, 1: hit, 2: not hit
}

func (x *OutreachInfo) Reset() {
	*x = OutreachInfo{}
	mi := &file_conan_clue_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachInfo) ProtoMessage() {}

func (x *OutreachInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachInfo.ProtoReflect.Descriptor instead.
func (*OutreachInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{36}
}

func (x *OutreachInfo) GetEuid() int64 {
	if x != nil {
		return x.Euid
	}
	return 0
}

func (x *OutreachInfo) GetOutreachIp() string {
	if x != nil {
		return x.OutreachIp
	}
	return ""
}

func (x *OutreachInfo) GetOutreachType() uint32 {
	if x != nil {
		return x.OutreachType
	}
	return 0
}

func (x *OutreachInfo) GetOutreachPort() int32 {
	if x != nil {
		return x.OutreachPort
	}
	return 0
}

func (x *OutreachInfo) GetOutreachDomain() string {
	if x != nil {
		return x.OutreachDomain
	}
	return ""
}

func (x *OutreachInfo) GetOutreachAddress() string {
	if x != nil {
		return x.OutreachAddress
	}
	return ""
}

func (x *OutreachInfo) GetIpVersion() string {
	if x != nil {
		return x.IpVersion
	}
	return ""
}

func (x *OutreachInfo) GetDirection() string {
	if x != nil {
		return x.Direction
	}
	return ""
}

func (x *OutreachInfo) GetConfidence() int32 {
	if x != nil {
		return x.Confidence
	}
	return 0
}

func (x *OutreachInfo) GetIsApt() int32 {
	if x != nil {
		return x.IsApt
	}
	return 0
}

// AttackExtendInfo represents extended attack information
type AttackExtendInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReportId      int64  `protobuf:"varint,1,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`               // Report ID
	ProcLineFlag  string `protobuf:"bytes,2,opt,name=proc_line_flag,json=procLineFlag,proto3" json:"proc_line_flag,omitempty"`  // Process line flag
	DumpFlag      string `protobuf:"bytes,3,opt,name=dump_flag,json=dumpFlag,proto3" json:"dump_flag,omitempty"`                // Dump flag
	ScriptFlag    string `protobuf:"bytes,4,opt,name=script_flag,json=scriptFlag,proto3" json:"script_flag,omitempty"`          // Script flag
	SignatureInfo string `protobuf:"bytes,5,opt,name=signature_info,json=signatureInfo,proto3" json:"signature_info,omitempty"` // Signature information
	ScriptType    int64  `protobuf:"varint,6,opt,name=script_type,json=scriptType,proto3" json:"script_type,omitempty"`         // Script type (only exists in NoFileAttack)
	Cve           string `protobuf:"bytes,7,opt,name=cve,proto3" json:"cve,omitempty"`                                          // CVE number
	AttackIp      string `protobuf:"bytes,8,opt,name=attack_ip,json=attackIp,proto3" json:"attack_ip,omitempty"`                // Attack IP
	Crontab       string `protobuf:"bytes,9,opt,name=crontab,proto3" json:"crontab,omitempty"`                                  // Crontab
	LocalIp       string `protobuf:"bytes,10,opt,name=local_ip,json=localIp,proto3" json:"local_ip,omitempty"`                  // Local IP
	LocalPort     int32  `protobuf:"varint,11,opt,name=local_port,json=localPort,proto3" json:"local_port,omitempty"`           // Local port
	RemoteIp      string `protobuf:"bytes,12,opt,name=remote_ip,json=remoteIp,proto3" json:"remote_ip,omitempty"`               // Remote IP
	RemotePort    int32  `protobuf:"varint,13,opt,name=remote_port,json=remotePort,proto3" json:"remote_port,omitempty"`        // Remote port
	HitSource     int32  `protobuf:"varint,14,opt,name=hit_source,json=hitSource,proto3" json:"hit_source,omitempty"`           // External hit source 1: public intel 2: private intel
	Protocol      string `protobuf:"bytes,15,opt,name=protocol,proto3" json:"protocol,omitempty"`                               // Protocol
}

func (x *AttackExtendInfo) Reset() {
	*x = AttackExtendInfo{}
	mi := &file_conan_clue_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttackExtendInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttackExtendInfo) ProtoMessage() {}

func (x *AttackExtendInfo) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttackExtendInfo.ProtoReflect.Descriptor instead.
func (*AttackExtendInfo) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{37}
}

func (x *AttackExtendInfo) GetReportId() int64 {
	if x != nil {
		return x.ReportId
	}
	return 0
}

func (x *AttackExtendInfo) GetProcLineFlag() string {
	if x != nil {
		return x.ProcLineFlag
	}
	return ""
}

func (x *AttackExtendInfo) GetDumpFlag() string {
	if x != nil {
		return x.DumpFlag
	}
	return ""
}

func (x *AttackExtendInfo) GetScriptFlag() string {
	if x != nil {
		return x.ScriptFlag
	}
	return ""
}

func (x *AttackExtendInfo) GetSignatureInfo() string {
	if x != nil {
		return x.SignatureInfo
	}
	return ""
}

func (x *AttackExtendInfo) GetScriptType() int64 {
	if x != nil {
		return x.ScriptType
	}
	return 0
}

func (x *AttackExtendInfo) GetCve() string {
	if x != nil {
		return x.Cve
	}
	return ""
}

func (x *AttackExtendInfo) GetAttackIp() string {
	if x != nil {
		return x.AttackIp
	}
	return ""
}

func (x *AttackExtendInfo) GetCrontab() string {
	if x != nil {
		return x.Crontab
	}
	return ""
}

func (x *AttackExtendInfo) GetLocalIp() string {
	if x != nil {
		return x.LocalIp
	}
	return ""
}

func (x *AttackExtendInfo) GetLocalPort() int32 {
	if x != nil {
		return x.LocalPort
	}
	return 0
}

func (x *AttackExtendInfo) GetRemoteIp() string {
	if x != nil {
		return x.RemoteIp
	}
	return ""
}

func (x *AttackExtendInfo) GetRemotePort() int32 {
	if x != nil {
		return x.RemotePort
	}
	return 0
}

func (x *AttackExtendInfo) GetHitSource() int32 {
	if x != nil {
		return x.HitSource
	}
	return 0
}

func (x *AttackExtendInfo) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

type Evidence struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EvidenceSize int64  `protobuf:"varint,1,opt,name=evidence_size,json=evidenceSize,proto3" json:"evidence_size,omitempty"`
	EvidenceList string `protobuf:"bytes,2,opt,name=evidence_list,json=evidenceList,proto3" json:"evidence_list,omitempty"`
}

func (x *Evidence) Reset() {
	*x = Evidence{}
	mi := &file_conan_clue_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Evidence) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Evidence) ProtoMessage() {}

func (x *Evidence) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Evidence.ProtoReflect.Descriptor instead.
func (*Evidence) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{38}
}

func (x *Evidence) GetEvidenceSize() int64 {
	if x != nil {
		return x.EvidenceSize
	}
	return 0
}

func (x *Evidence) GetEvidenceList() string {
	if x != nil {
		return x.EvidenceList
	}
	return ""
}

type AttackIndicatorContextValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Value:
	//
	//	*AttackIndicatorContextValue_StringValue
	//	*AttackIndicatorContextValue_Int64Value
	Value isAttackIndicatorContextValue_Value `protobuf_oneof:"value"`
}

func (x *AttackIndicatorContextValue) Reset() {
	*x = AttackIndicatorContextValue{}
	mi := &file_conan_clue_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttackIndicatorContextValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttackIndicatorContextValue) ProtoMessage() {}

func (x *AttackIndicatorContextValue) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttackIndicatorContextValue.ProtoReflect.Descriptor instead.
func (*AttackIndicatorContextValue) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{39}
}

func (m *AttackIndicatorContextValue) GetValue() isAttackIndicatorContextValue_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *AttackIndicatorContextValue) GetStringValue() string {
	if x, ok := x.GetValue().(*AttackIndicatorContextValue_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *AttackIndicatorContextValue) GetInt64Value() int64 {
	if x, ok := x.GetValue().(*AttackIndicatorContextValue_Int64Value); ok {
		return x.Int64Value
	}
	return 0
}

type isAttackIndicatorContextValue_Value interface {
	isAttackIndicatorContextValue_Value()
}

type AttackIndicatorContextValue_StringValue struct {
	StringValue string `protobuf:"bytes,1,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type AttackIndicatorContextValue_Int64Value struct {
	Int64Value int64 `protobuf:"varint,2,opt,name=int64_value,json=int64Value,proto3,oneof"`
}

func (*AttackIndicatorContextValue_StringValue) isAttackIndicatorContextValue_Value() {}

func (*AttackIndicatorContextValue_Int64Value) isAttackIndicatorContextValue_Value() {}

// AttackDetail represents details about an attack event
type AttackDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProcessInfo       *ProcessInfo                            `protobuf:"bytes,1,opt,name=process_info,json=processInfo,proto3" json:"process_info,omitempty"`                                                                                                               // Process related information
	FileInfo          *FileInfo                               `protobuf:"bytes,2,opt,name=file_info,json=fileInfo,proto3" json:"file_info,omitempty"`                                                                                                                        // File related information
	DriverInfo        *DriverInfo                             `protobuf:"bytes,3,opt,name=driver_info,json=driverInfo,proto3" json:"driver_info,omitempty"`                                                                                                                  // Driver related information
	MaliciousInfo     *MaliciousInfo                          `protobuf:"bytes,4,opt,name=malicious_info,json=maliciousInfo,proto3" json:"malicious_info,omitempty"`                                                                                                         // Malicious code information
	OutreachInfo      *OutreachInfo                           `protobuf:"bytes,5,opt,name=outreach_info,json=outreachInfo,proto3" json:"outreach_info,omitempty"`                                                                                                            // External connection information
	EvidenceInfo      *Evidence                               `protobuf:"bytes,6,opt,name=evidence_info,json=evidenceInfo,proto3" json:"evidence_info,omitempty"`                                                                                                            // Evidence information
	AttackContext     map[string]*AttackIndicatorContextValue `protobuf:"bytes,7,rep,name=attack_context,json=attackContext,proto3" json:"attack_context,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`                 // Dynamic attack context data
	ExtendInfo        *AttackExtendInfo                       `protobuf:"bytes,8,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`                                                                                                                  // Extended information
	ClueTypeExtraInfo map[string]*AttackIndicatorContextValue `protobuf:"bytes,9,rep,name=clue_type_extra_info,json=clueTypeExtraInfo,proto3" json:"clue_type_extra_info,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` // Clue type extra info
}

func (x *AttackDetail) Reset() {
	*x = AttackDetail{}
	mi := &file_conan_clue_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttackDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttackDetail) ProtoMessage() {}

func (x *AttackDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttackDetail.ProtoReflect.Descriptor instead.
func (*AttackDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{40}
}

func (x *AttackDetail) GetProcessInfo() *ProcessInfo {
	if x != nil {
		return x.ProcessInfo
	}
	return nil
}

func (x *AttackDetail) GetFileInfo() *FileInfo {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

func (x *AttackDetail) GetDriverInfo() *DriverInfo {
	if x != nil {
		return x.DriverInfo
	}
	return nil
}

func (x *AttackDetail) GetMaliciousInfo() *MaliciousInfo {
	if x != nil {
		return x.MaliciousInfo
	}
	return nil
}

func (x *AttackDetail) GetOutreachInfo() *OutreachInfo {
	if x != nil {
		return x.OutreachInfo
	}
	return nil
}

func (x *AttackDetail) GetEvidenceInfo() *Evidence {
	if x != nil {
		return x.EvidenceInfo
	}
	return nil
}

func (x *AttackDetail) GetAttackContext() map[string]*AttackIndicatorContextValue {
	if x != nil {
		return x.AttackContext
	}
	return nil
}

func (x *AttackDetail) GetExtendInfo() *AttackExtendInfo {
	if x != nil {
		return x.ExtendInfo
	}
	return nil
}

func (x *AttackDetail) GetClueTypeExtraInfo() map[string]*AttackIndicatorContextValue {
	if x != nil {
		return x.ClueTypeExtraInfo
	}
	return nil
}

type ClueDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Clue:
	//
	//	*ClueDetail_MemoryAttackClueDetail
	//	*ClueDetail_FileThreatClueDetail
	//	*ClueDetail_SystemAttackClueDetail
	//	*ClueDetail_IllegalOutreachClueDetail
	Clue     isClueDetail_Clue `protobuf_oneof:"clue"`
	ClueType ClueType          `protobuf:"varint,5,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"` // The type of the clue
}

func (x *ClueDetail) Reset() {
	*x = ClueDetail{}
	mi := &file_conan_clue_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClueDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClueDetail) ProtoMessage() {}

func (x *ClueDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClueDetail.ProtoReflect.Descriptor instead.
func (*ClueDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{41}
}

func (m *ClueDetail) GetClue() isClueDetail_Clue {
	if m != nil {
		return m.Clue
	}
	return nil
}

func (x *ClueDetail) GetMemoryAttackClueDetail() *MemoryAttackClueDetail {
	if x, ok := x.GetClue().(*ClueDetail_MemoryAttackClueDetail); ok {
		return x.MemoryAttackClueDetail
	}
	return nil
}

func (x *ClueDetail) GetFileThreatClueDetail() *FileThreatClueDetail {
	if x, ok := x.GetClue().(*ClueDetail_FileThreatClueDetail); ok {
		return x.FileThreatClueDetail
	}
	return nil
}

func (x *ClueDetail) GetSystemAttackClueDetail() *SystemAttackClueDetail {
	if x, ok := x.GetClue().(*ClueDetail_SystemAttackClueDetail); ok {
		return x.SystemAttackClueDetail
	}
	return nil
}

func (x *ClueDetail) GetIllegalOutreachClueDetail() *IllegalOutreachClueDetail {
	if x, ok := x.GetClue().(*ClueDetail_IllegalOutreachClueDetail); ok {
		return x.IllegalOutreachClueDetail
	}
	return nil
}

func (x *ClueDetail) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

type isClueDetail_Clue interface {
	isClueDetail_Clue()
}

type ClueDetail_MemoryAttackClueDetail struct {
	MemoryAttackClueDetail *MemoryAttackClueDetail `protobuf:"bytes,1,opt,name=memory_attack_clue_detail,json=memoryAttackClueDetail,proto3,oneof"`
}

type ClueDetail_FileThreatClueDetail struct {
	FileThreatClueDetail *FileThreatClueDetail `protobuf:"bytes,2,opt,name=file_threat_clue_detail,json=fileThreatClueDetail,proto3,oneof"`
}

type ClueDetail_SystemAttackClueDetail struct {
	SystemAttackClueDetail *SystemAttackClueDetail `protobuf:"bytes,3,opt,name=system_attack_clue_detail,json=systemAttackClueDetail,proto3,oneof"`
}

type ClueDetail_IllegalOutreachClueDetail struct {
	IllegalOutreachClueDetail *IllegalOutreachClueDetail `protobuf:"bytes,4,opt,name=illegal_outreach_clue_detail,json=illegalOutreachClueDetail,proto3,oneof"`
}

func (*ClueDetail_MemoryAttackClueDetail) isClueDetail_Clue() {}

func (*ClueDetail_FileThreatClueDetail) isClueDetail_Clue() {}

func (*ClueDetail_SystemAttackClueDetail) isClueDetail_Clue() {}

func (*ClueDetail_IllegalOutreachClueDetail) isClueDetail_Clue() {}

type MemoryAttackClueDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clue   *MemoryAttackClue `protobuf:"bytes,1,opt,name=clue,proto3" json:"clue,omitempty"`
	Detail *AttackDetail     `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *MemoryAttackClueDetail) Reset() {
	*x = MemoryAttackClueDetail{}
	mi := &file_conan_clue_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MemoryAttackClueDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MemoryAttackClueDetail) ProtoMessage() {}

func (x *MemoryAttackClueDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MemoryAttackClueDetail.ProtoReflect.Descriptor instead.
func (*MemoryAttackClueDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{42}
}

func (x *MemoryAttackClueDetail) GetClue() *MemoryAttackClue {
	if x != nil {
		return x.Clue
	}
	return nil
}

func (x *MemoryAttackClueDetail) GetDetail() *AttackDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type FileThreatClueDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clue   *FileThreatClue `protobuf:"bytes,1,opt,name=clue,proto3" json:"clue,omitempty"`
	Detail *AttackDetail   `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *FileThreatClueDetail) Reset() {
	*x = FileThreatClueDetail{}
	mi := &file_conan_clue_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileThreatClueDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileThreatClueDetail) ProtoMessage() {}

func (x *FileThreatClueDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileThreatClueDetail.ProtoReflect.Descriptor instead.
func (*FileThreatClueDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{43}
}

func (x *FileThreatClueDetail) GetClue() *FileThreatClue {
	if x != nil {
		return x.Clue
	}
	return nil
}

func (x *FileThreatClueDetail) GetDetail() *AttackDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type SystemAttackClueDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clue   *SystemAttackClue `protobuf:"bytes,1,opt,name=clue,proto3" json:"clue,omitempty"`
	Detail *AttackDetail     `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *SystemAttackClueDetail) Reset() {
	*x = SystemAttackClueDetail{}
	mi := &file_conan_clue_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemAttackClueDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemAttackClueDetail) ProtoMessage() {}

func (x *SystemAttackClueDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemAttackClueDetail.ProtoReflect.Descriptor instead.
func (*SystemAttackClueDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{44}
}

func (x *SystemAttackClueDetail) GetClue() *SystemAttackClue {
	if x != nil {
		return x.Clue
	}
	return nil
}

func (x *SystemAttackClueDetail) GetDetail() *AttackDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type IllegalOutreachClueDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clue   *IllegalOutreachClue `protobuf:"bytes,1,opt,name=clue,proto3" json:"clue,omitempty"`
	Detail *AttackDetail        `protobuf:"bytes,2,opt,name=detail,proto3" json:"detail,omitempty"`
}

func (x *IllegalOutreachClueDetail) Reset() {
	*x = IllegalOutreachClueDetail{}
	mi := &file_conan_clue_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IllegalOutreachClueDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IllegalOutreachClueDetail) ProtoMessage() {}

func (x *IllegalOutreachClueDetail) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IllegalOutreachClueDetail.ProtoReflect.Descriptor instead.
func (*IllegalOutreachClueDetail) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{45}
}

func (x *IllegalOutreachClueDetail) GetClue() *IllegalOutreachClue {
	if x != nil {
		return x.Clue
	}
	return nil
}

func (x *IllegalOutreachClueDetail) GetDetail() *AttackDetail {
	if x != nil {
		return x.Detail
	}
	return nil
}

type GetClueDetailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ClueType ClueType `protobuf:"varint,2,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
}

func (x *GetClueDetailReq) Reset() {
	*x = GetClueDetailReq{}
	mi := &file_conan_clue_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClueDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClueDetailReq) ProtoMessage() {}

func (x *GetClueDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClueDetailReq.ProtoReflect.Descriptor instead.
func (*GetClueDetailReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{46}
}

func (x *GetClueDetailReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetClueDetailReq) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

type LLMResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url                  string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	ReadUrl              string `protobuf:"bytes,2,opt,name=read_url,json=readUrl,proto3" json:"read_url,omitempty"`
	Rank                 int64  `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	LlmAnswer            string `protobuf:"bytes,4,opt,name=llm_answer,json=llmAnswer,proto3" json:"llm_answer,omitempty"`
	LlmAnswerDescription string `protobuf:"bytes,5,opt,name=llm_answer_description,json=llmAnswerDescription,proto3" json:"llm_answer_description,omitempty"`
	Screenshot           string `protobuf:"bytes,6,opt,name=screenshot,proto3" json:"screenshot,omitempty"`
}

func (x *LLMResult) Reset() {
	*x = LLMResult{}
	mi := &file_conan_clue_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LLMResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LLMResult) ProtoMessage() {}

func (x *LLMResult) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LLMResult.ProtoReflect.Descriptor instead.
func (*LLMResult) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{47}
}

func (x *LLMResult) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *LLMResult) GetReadUrl() string {
	if x != nil {
		return x.ReadUrl
	}
	return ""
}

func (x *LLMResult) GetRank() int64 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *LLMResult) GetLlmAnswer() string {
	if x != nil {
		return x.LlmAnswer
	}
	return ""
}

func (x *LLMResult) GetLlmAnswerDescription() string {
	if x != nil {
		return x.LlmAnswerDescription
	}
	return ""
}

func (x *LLMResult) GetScreenshot() string {
	if x != nil {
		return x.Screenshot
	}
	return ""
}

type ListClueDetailsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filters和ids至少提供一个， filter需要配合page使用
	Filter   *ListAttackCluesFilter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	Ids      []int64                `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	ClueType ClueType               `protobuf:"varint,3,opt,name=clue_type,json=clueType,proto3,enum=conan.ClueType" json:"clue_type,omitempty"`
	Page     *PageRequest           `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListClueDetailsReq) Reset() {
	*x = ListClueDetailsReq{}
	mi := &file_conan_clue_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClueDetailsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClueDetailsReq) ProtoMessage() {}

func (x *ListClueDetailsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClueDetailsReq.ProtoReflect.Descriptor instead.
func (*ListClueDetailsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{48}
}

func (x *ListClueDetailsReq) GetFilter() *ListAttackCluesFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListClueDetailsReq) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListClueDetailsReq) GetClueType() ClueType {
	if x != nil {
		return x.ClueType
	}
	return ClueType_CT_UNKNOWN
}

func (x *ListClueDetailsReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListClueDetailsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Clues []*ClueDetail `protobuf:"bytes,1,rep,name=clues,proto3" json:"clues,omitempty"`
	Page  *PageResponse `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListClueDetailsResp) Reset() {
	*x = ListClueDetailsResp{}
	mi := &file_conan_clue_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListClueDetailsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListClueDetailsResp) ProtoMessage() {}

func (x *ListClueDetailsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListClueDetailsResp.ProtoReflect.Descriptor instead.
func (*ListClueDetailsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{49}
}

func (x *ListClueDetailsResp) GetClues() []*ClueDetail {
	if x != nil {
		return x.Clues
	}
	return nil
}

func (x *ListClueDetailsResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListAffectedOutreachTerminalsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type            OutreachType `protobuf:"varint,1,opt,name=type,proto3,enum=conan.OutreachType" json:"type,omitempty"`
	OutreachAddress string       `protobuf:"bytes,2,opt,name=outreach_address,json=outreachAddress,proto3" json:"outreach_address,omitempty"`
	Ip              string       `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Page            *PageRequest `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty"`
	Range           *TimeRange   `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *ListAffectedOutreachTerminalsReq) Reset() {
	*x = ListAffectedOutreachTerminalsReq{}
	mi := &file_conan_clue_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAffectedOutreachTerminalsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAffectedOutreachTerminalsReq) ProtoMessage() {}

func (x *ListAffectedOutreachTerminalsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAffectedOutreachTerminalsReq.ProtoReflect.Descriptor instead.
func (*ListAffectedOutreachTerminalsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{50}
}

func (x *ListAffectedOutreachTerminalsReq) GetType() OutreachType {
	if x != nil {
		return x.Type
	}
	return OutreachType_OT_UNKNOWN
}

func (x *ListAffectedOutreachTerminalsReq) GetOutreachAddress() string {
	if x != nil {
		return x.OutreachAddress
	}
	return ""
}

func (x *ListAffectedOutreachTerminalsReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ListAffectedOutreachTerminalsReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ListAffectedOutreachTerminalsReq) GetRange() *TimeRange {
	if x != nil {
		return x.Range
	}
	return nil
}

type ListAffectedOutreachTerminalsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Terminals []*AffectedOutreachTerminal `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
	Page      *PageResponse               `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListAffectedOutreachTerminalsResp) Reset() {
	*x = ListAffectedOutreachTerminalsResp{}
	mi := &file_conan_clue_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAffectedOutreachTerminalsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAffectedOutreachTerminalsResp) ProtoMessage() {}

func (x *ListAffectedOutreachTerminalsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAffectedOutreachTerminalsResp.ProtoReflect.Descriptor instead.
func (*ListAffectedOutreachTerminalsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{51}
}

func (x *ListAffectedOutreachTerminalsResp) GetTerminals() []*AffectedOutreachTerminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *ListAffectedOutreachTerminalsResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AffectedOutreachTerminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId     string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	ClueKey       string                 `protobuf:"bytes,3,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	UniqueFlagMd5 string                 `protobuf:"bytes,4,opt,name=unique_flag_md5,json=uniqueFlagMd5,proto3" json:"unique_flag_md5,omitempty"`
	ExtraInfo     string                 `protobuf:"bytes,5,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *AffectedOutreachTerminal) Reset() {
	*x = AffectedOutreachTerminal{}
	mi := &file_conan_clue_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffectedOutreachTerminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffectedOutreachTerminal) ProtoMessage() {}

func (x *AffectedOutreachTerminal) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffectedOutreachTerminal.ProtoReflect.Descriptor instead.
func (*AffectedOutreachTerminal) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{52}
}

func (x *AffectedOutreachTerminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AffectedOutreachTerminal) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AffectedOutreachTerminal) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *AffectedOutreachTerminal) GetUniqueFlagMd5() string {
	if x != nil {
		return x.UniqueFlagMd5
	}
	return ""
}

func (x *AffectedOutreachTerminal) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *AffectedOutreachTerminal) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

type ListAffectedFileTerminalsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileSha256 string       `protobuf:"bytes,1,opt,name=file_sha256,json=fileSha256,proto3" json:"file_sha256,omitempty"`
	TimeRange  *TimeRange   `protobuf:"bytes,2,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	Page       *PageRequest `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListAffectedFileTerminalsReq) Reset() {
	*x = ListAffectedFileTerminalsReq{}
	mi := &file_conan_clue_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAffectedFileTerminalsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAffectedFileTerminalsReq) ProtoMessage() {}

func (x *ListAffectedFileTerminalsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAffectedFileTerminalsReq.ProtoReflect.Descriptor instead.
func (*ListAffectedFileTerminalsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{53}
}

func (x *ListAffectedFileTerminalsReq) GetFileSha256() string {
	if x != nil {
		return x.FileSha256
	}
	return ""
}

func (x *ListAffectedFileTerminalsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *ListAffectedFileTerminalsReq) GetPage() *PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ListAffectedFileTerminalsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Terminals []*AffectedFileTerminal `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
	Page      *PageResponse           `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty"`
}

func (x *ListAffectedFileTerminalsResp) Reset() {
	*x = ListAffectedFileTerminalsResp{}
	mi := &file_conan_clue_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAffectedFileTerminalsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAffectedFileTerminalsResp) ProtoMessage() {}

func (x *ListAffectedFileTerminalsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAffectedFileTerminalsResp.ProtoReflect.Descriptor instead.
func (*ListAffectedFileTerminalsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{54}
}

func (x *ListAffectedFileTerminalsResp) GetTerminals() []*AffectedFileTerminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

func (x *ListAffectedFileTerminalsResp) GetPage() *PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AffectedFileTerminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId   string                 `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	ClueKey     string                 `protobuf:"bytes,3,opt,name=clue_key,json=clueKey,proto3" json:"clue_key,omitempty"`
	ClueStatus  int32                  `protobuf:"varint,4,opt,name=clue_status,json=clueStatus,proto3" json:"clue_status,omitempty"`
	ClueSubType int32                  `protobuf:"varint,5,opt,name=clue_sub_type,json=clueSubType,proto3" json:"clue_sub_type,omitempty"`
	FileName    string                 `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileType    int32                  `protobuf:"varint,7,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	UniqueFlag  string                 `protobuf:"bytes,8,opt,name=unique_flag,json=uniqueFlag,proto3" json:"unique_flag,omitempty"`
	ExtraInfo   string                 `protobuf:"bytes,9,opt,name=extra_info,json=extraInfo,proto3" json:"extra_info,omitempty"`
	Disposition int32                  `protobuf:"varint,10,opt,name=disposition,proto3" json:"disposition,omitempty"`
	CreateTime  *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
}

func (x *AffectedFileTerminal) Reset() {
	*x = AffectedFileTerminal{}
	mi := &file_conan_clue_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AffectedFileTerminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AffectedFileTerminal) ProtoMessage() {}

func (x *AffectedFileTerminal) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AffectedFileTerminal.ProtoReflect.Descriptor instead.
func (*AffectedFileTerminal) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{55}
}

func (x *AffectedFileTerminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AffectedFileTerminal) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *AffectedFileTerminal) GetClueKey() string {
	if x != nil {
		return x.ClueKey
	}
	return ""
}

func (x *AffectedFileTerminal) GetClueStatus() int32 {
	if x != nil {
		return x.ClueStatus
	}
	return 0
}

func (x *AffectedFileTerminal) GetClueSubType() int32 {
	if x != nil {
		return x.ClueSubType
	}
	return 0
}

func (x *AffectedFileTerminal) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *AffectedFileTerminal) GetFileType() int32 {
	if x != nil {
		return x.FileType
	}
	return 0
}

func (x *AffectedFileTerminal) GetUniqueFlag() string {
	if x != nil {
		return x.UniqueFlag
	}
	return ""
}

func (x *AffectedFileTerminal) GetExtraInfo() string {
	if x != nil {
		return x.ExtraInfo
	}
	return ""
}

func (x *AffectedFileTerminal) GetDisposition() int32 {
	if x != nil {
		return x.Disposition
	}
	return 0
}

func (x *AffectedFileTerminal) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

type GetOutreachStatisticsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetOutreachStatisticsReq) Reset() {
	*x = GetOutreachStatisticsReq{}
	mi := &file_conan_clue_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOutreachStatisticsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOutreachStatisticsReq) ProtoMessage() {}

func (x *GetOutreachStatisticsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOutreachStatisticsReq.ProtoReflect.Descriptor instead.
func (*GetOutreachStatisticsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{56}
}

func (x *GetOutreachStatisticsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type GetOutreachStatisticsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount   int64 `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`       // 外联日志数量
	IllegalCount int64 `protobuf:"varint,2,opt,name=illegal_count,json=illegalCount,proto3" json:"illegal_count,omitempty"` // 非法外联数量
	IpCount      int64 `protobuf:"varint,3,opt,name=ip_count,json=ipCount,proto3" json:"ip_count,omitempty"`                // IP数量
	DomainCount  int64 `protobuf:"varint,4,opt,name=domain_count,json=domainCount,proto3" json:"domain_count,omitempty"`    // 域名数量
}

func (x *GetOutreachStatisticsResp) Reset() {
	*x = GetOutreachStatisticsResp{}
	mi := &file_conan_clue_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOutreachStatisticsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOutreachStatisticsResp) ProtoMessage() {}

func (x *GetOutreachStatisticsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOutreachStatisticsResp.ProtoReflect.Descriptor instead.
func (*GetOutreachStatisticsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{57}
}

func (x *GetOutreachStatisticsResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetOutreachStatisticsResp) GetIllegalCount() int64 {
	if x != nil {
		return x.IllegalCount
	}
	return 0
}

func (x *GetOutreachStatisticsResp) GetIpCount() int64 {
	if x != nil {
		return x.IpCount
	}
	return 0
}

func (x *GetOutreachStatisticsResp) GetDomainCount() int64 {
	if x != nil {
		return x.DomainCount
	}
	return 0
}

type GetThreatenFileStatisticsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetThreatenFileStatisticsReq) Reset() {
	*x = GetThreatenFileStatisticsReq{}
	mi := &file_conan_clue_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetThreatenFileStatisticsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetThreatenFileStatisticsReq) ProtoMessage() {}

func (x *GetThreatenFileStatisticsReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetThreatenFileStatisticsReq.ProtoReflect.Descriptor instead.
func (*GetThreatenFileStatisticsReq) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{58}
}

func (x *GetThreatenFileStatisticsReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type GetThreatenFileStatisticsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount    int64 `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`          // 文件威胁告警数量
	TerminalCount int64 `protobuf:"varint,2,opt,name=terminal_count,json=terminalCount,proto3" json:"terminal_count,omitempty"` // 影响终端数量
	// key: mq.CluleDetectEngine
	CheckEngineCounts map[int32]int64 `protobuf:"bytes,3,rep,name=check_engine_counts,json=checkEngineCounts,proto3" json:"check_engine_counts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 检测引擎统计数量
}

func (x *GetThreatenFileStatisticsResp) Reset() {
	*x = GetThreatenFileStatisticsResp{}
	mi := &file_conan_clue_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetThreatenFileStatisticsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetThreatenFileStatisticsResp) ProtoMessage() {}

func (x *GetThreatenFileStatisticsResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_clue_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetThreatenFileStatisticsResp.ProtoReflect.Descriptor instead.
func (*GetThreatenFileStatisticsResp) Descriptor() ([]byte, []int) {
	return file_conan_clue_proto_rawDescGZIP(), []int{59}
}

func (x *GetThreatenFileStatisticsResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *GetThreatenFileStatisticsResp) GetTerminalCount() int64 {
	if x != nil {
		return x.TerminalCount
	}
	return 0
}

func (x *GetThreatenFileStatisticsResp) GetCheckEngineCounts() map[int32]int64 {
	if x != nil {
		return x.CheckEngineCounts
	}
	return nil
}

var File_conan_clue_proto protoreflect.FileDescriptor

var file_conan_clue_proto_rawDesc = []byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6c, 0x75, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x78,
	0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75,
	0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xd9, 0x04, 0x0a, 0x10, 0x4d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a,
	0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a,
	0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63,
	0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75,
	0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63,
	0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x75,
	0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c,
	0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x68,
	0x61, 0x73, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x68, 0x61, 0x73, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x63, 0x63,
	0x75, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x6f, 0x63, 0x63, 0x75, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xd9, 0x04, 0x0a, 0x10, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71,
	0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75,
	0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75,
	0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x69,
	0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x61, 0x73,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x5f, 0x73, 0x72, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0xa6, 0x08, 0x0a, 0x0e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63,
	0x6c, 0x75, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x61, 0x73, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c,
	0x65, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61,
	0x32, 0x35, 0x36, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x73,
	0x6f, 0x6c, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x11,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6e,
	0x5f, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x63, 0x61, 0x6e, 0x49, 0x73, 0x6f, 0x6c, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x63, 0x63,
	0x75, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x6f, 0x63, 0x63, 0x75, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69,
	0x73, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x66, 0x69,
	0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x18, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x63,
	0x65, 0x6e, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b,
	0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb3, 0x07, 0x0a, 0x13, 0x49, 0x6c,
	0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75,
	0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75,
	0x65, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x73, 0x72, 0x63, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x53, 0x72, 0x63, 0x12, 0x1f, 0x0a, 0x0b,
	0x6f, 0x63, 0x63, 0x75, 0x72, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x2f, 0x0a, 0x0a, 0x68, 0x69, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x48, 0x69, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x09, 0x68, 0x69, 0x74, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x69, 0x73, 0x5f, 0x61, 0x70, 0x74, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x70,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x05, 0x69, 0x73, 0x41, 0x70, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x68, 0x61, 0x73, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72,
	0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x34, 0x0a, 0x16, 0x6f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x17, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f,
	0x0a, 0x0a, 0x6c, 0x6c, 0x6d, 0x5f, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x18, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x4c, 0x4d, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x52, 0x09, 0x6c, 0x6c, 0x6d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0xbe, 0x05, 0x0a, 0x13, 0x55, 0x6e, 0x69, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69,
	0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f,
	0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b,
	0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64,
	0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x61, 0x6e, 0x5f, 0x69, 0x73, 0x6f, 0x6c, 0x61, 0x74,
	0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x63, 0x61, 0x6e, 0x49, 0x73, 0x6f, 0x6c,
	0x61, 0x74, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x16, 0x66, 0x69, 0x6c, 0x65, 0x5f,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x66, 0x69, 0x6c, 0x65, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x22, 0x73, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a,
	0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x76, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12,
	0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x6f, 0x0a,
	0x17, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x05, 0x63, 0x6c, 0x75, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x05,
	0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x78,
	0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75,
	0x65, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x7b, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74,
	0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43,
	0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x26, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x79, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6c, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x30, 0x0a, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x49, 0x6c, 0x6c,
	0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65,
	0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0x73, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2d, 0x0a,
	0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x63, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c,
	0x75, 0x65, 0x73, 0x42, 0x79, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x52, 0x65, 0x71, 0x22, 0x5a,
	0x0a, 0x16, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x53, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a,
	0x0a, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x44, 0x0a, 0x0b, 0x53, 0x68,
	0x61, 0x32, 0x35, 0x36, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x5f, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x4d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x22, 0x4e, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x4d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x33, 0x0a, 0x08,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c,
	0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x08, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x73, 0x22, 0xcf, 0x01, 0x0a, 0x10, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e,
	0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x61, 0x63, 0x68,
	0x69, 0x6e, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x22, 0xed, 0x01, 0x0a, 0x09, 0x43, 0x6c, 0x75, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x65, 0x64, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x64, 0x69, 0x73,
	0x70, 0x6f, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x76,
	0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x36, 0x0a, 0x17, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x15, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x77, 0x61, 0x69,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61,
	0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x17, 0x77,
	0x61, 0x69, 0x74, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x44, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x61,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x64, 0x0a, 0x1b, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x70, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0x1e, 0x0a, 0x1c,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c,
	0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x22, 0xdb, 0x01, 0x0a,
	0x20, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x70, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x21, 0x4c,
	0x69, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x3d, 0x0a, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12,
	0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x18, 0x4f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69,
	0x6e, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12,
	0x26, 0x0a, 0x0f, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x5f, 0x6d,
	0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65,
	0x46, 0x6c, 0x61, 0x67, 0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x65, 0x71, 0x22, 0x50, 0x0a, 0x1c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x30, 0x0a, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x22, 0x52, 0x0a, 0x11, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x76, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x22, 0x46, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2c, 0x0a, 0x06, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0xc1, 0x01, 0x0a, 0x0d, 0x43, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c,
	0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x48, 0x0a, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c, 0x61, 0x74, 0x65,
	0x73, 0x74, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x8c, 0x08, 0x0a,
	0x0b, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x70, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x70, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x70,
	0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x22, 0x0a, 0x0d,
	0x64, 0x73, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f,
	0x70, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x35, 0x0a, 0x17, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x55, 0x73, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x70, 0x72, 0x6f, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x6f,
	0x63, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x63, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0d, 0x70, 0x72, 0x6f, 0x63, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x69, 0x73, 0x5f, 0x78, 0x38, 0x36, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x69, 0x73, 0x58, 0x38, 0x36, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x72, 0x6f, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x75, 0x62,
	0x50, 0x72, 0x6f, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75, 0x62, 0x5f,
	0x70, 0x72, 0x6f, 0x63, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x50, 0x61, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0c,
	0x73, 0x75, 0x62, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f, 0x63, 0x4d, 0x64, 0x35, 0x12, 0x28,
	0x0a, 0x10, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x50, 0x72, 0x6f,
	0x63, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x69, 0x6c,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x12, 0x0a, 0x04, 0x65, 0x75, 0x69, 0x64, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65,
	0x75, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x50,
	0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x63,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xec, 0x03, 0x0a, 0x08,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x19, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x4d, 0x64, 0x35, 0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1b, 0x0a, 0x09, 0x66,
	0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x31, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x66, 0x69, 0x6c,
	0x65, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x67,
	0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe2, 0x01, 0x0a, 0x0a, 0x44,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x72,
	0x69, 0x76, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x50, 0x61, 0x74, 0x68, 0x12, 0x28, 0x0a, 0x10, 0x64,
	0x72, 0x69, 0x76, 0x65, 0x72, 0x5f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x52, 0x69, 0x73,
	0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6b, 0x65, 0x72, 0x6e, 0x65, 0x6c, 0x5f,
	0x6d, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x65, 0x72, 0x6e, 0x65,
	0x6c, 0x4d, 0x6f, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x6d,
	0x6f, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65,
	0x4d, 0x6f, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x64, 0x72, 0x69,
	0x76, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0xf8, 0x03, 0x0a, 0x0d, 0x4d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x61, 0x6c, 0x69,
	0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x64,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x44, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x76, 0x62, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d,
	0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x56, 0x62, 0x61, 0x12, 0x39, 0x0a, 0x19, 0x6d,
	0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16,
	0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x39, 0x0a, 0x19, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x64, 0x69, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x6d, 0x61, 0x6c, 0x69, 0x63,
	0x69, 0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x57, 0x6f, 0x72, 0x6b, 0x69, 0x6e, 0x67, 0x44, 0x69,
	0x72, 0x12, 0x33, 0x0a, 0x16, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x6c,
	0x6e, 0x6b, 0x5f, 0x63, 0x6d, 0x64, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x13, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x43,
	0x6d, 0x64, 0x4c, 0x69, 0x6e, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69,
	0x6f, 0x75, 0x73, 0x5f, 0x6c, 0x6e, 0x6b, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f,
	0x75, 0x73, 0x4c, 0x6e, 0x6b, 0x49, 0x63, 0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x55,
	0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x6c, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6c, 0x52, 0x75, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6c, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6c,
	0x52, 0x75, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0xd5, 0x02, 0x0a, 0x0c, 0x4f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x65,
	0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x65, 0x75, 0x69, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x69, 0x70, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49, 0x70,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x44, 0x6f, 0x6d,
	0x61, 0x69, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1d,
	0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x69,
	0x73, 0x5f, 0x61, 0x70, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x73, 0x41,
	0x70, 0x74, 0x22, 0xd7, 0x03, 0x0a, 0x10, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x5f, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x6f, 0x63, 0x4c, 0x69, 0x6e, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x75,
	0x6d, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x75, 0x6d, 0x70, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x67, 0x6e,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x63, 0x76, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63,
	0x76, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x49, 0x70, 0x12,
	0x18, 0x0a, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x63, 0x72, 0x6f, 0x6e, 0x74, 0x61, 0x62, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x63,
	0x61, 0x6c, 0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x70, 0x6f,
	0x72, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x50,
	0x6f, 0x72, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x69, 0x70,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x49, 0x70,
	0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x50, 0x6f, 0x72,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x69, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x68, 0x69, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x22, 0x54, 0x0a, 0x08,
	0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x22, 0x6e, 0x0a, 0x1b, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x64, 0x69,
	0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x69,
	0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x8a, 0x06, 0x0a, 0x0c, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x35, 0x0a, 0x0c, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x09, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x0b, 0x64, 0x72, 0x69, 0x76,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x44, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0a, 0x64, 0x72, 0x69, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0e,
	0x6d, 0x61, 0x6c, 0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x61, 0x6c,
	0x69, 0x63, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x6d, 0x61, 0x6c, 0x69,
	0x63, 0x69, 0x6f, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0d, 0x6f, 0x75, 0x74,
	0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x0d, 0x65, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x45, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0c, 0x65, 0x76, 0x69,
	0x64, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4d, 0x0a, 0x0e, 0x61, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x38, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x5b, 0x0a, 0x14, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f,
	0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78,
	0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x6c,
	0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x64, 0x0a, 0x12, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x6b, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x68, 0x0a, 0x16, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x45, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x49,
	0x6e, 0x64, 0x69, 0x63, 0x61, 0x74, 0x6f, 0x72, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xb5, 0x03, 0x0a, 0x0a, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x5a,
	0x0a, 0x19, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x5f,
	0x63, 0x6c, 0x75, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79,
	0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x48, 0x00, 0x52, 0x16, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x54, 0x0a, 0x17, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c,
	0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x14, 0x66, 0x69, 0x6c, 0x65,
	0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x5a, 0x0a, 0x19, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x61, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x48, 0x00, 0x52, 0x16, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x63, 0x0a, 0x1c,
	0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x5f, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x49, 0x6c, 0x6c, 0x65, 0x67,
	0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x48, 0x00, 0x52, 0x19, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x2c, 0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x06, 0x0a, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x22, 0x72, 0x0a, 0x16, 0x4d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x12, 0x2b,
	0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x6e, 0x0a, 0x14, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x12, 0x29, 0x0a, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68,
	0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x12, 0x2b,
	0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x72, 0x0a, 0x16, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6c,
	0x75, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22,
	0x78, 0x0a, 0x19, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x0a, 0x04,
	0x63, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x52, 0x04, 0x63, 0x6c, 0x75, 0x65, 0x12, 0x2b, 0x0a, 0x06,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x22, 0x50, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2c, 0x0a,
	0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x09,
	0x4c, 0x4c, 0x4d, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x65, 0x61, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x65, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x72, 0x61, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6c,
	0x6d, 0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6c, 0x6c, 0x6d, 0x41, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x6c, 0x6c, 0x6d,
	0x5f, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x6c, 0x6c, 0x6d, 0x41, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x1e, 0x0a, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x68, 0x6f, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x73, 0x68, 0x6f, 0x74, 0x22,
	0xb2, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x2c,
	0x0a, 0x09, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0x67, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x27, 0x0a, 0x05, 0x63,
	0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x05, 0x63,
	0x6c, 0x75, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0xd6, 0x01,
	0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x27, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x6f,
	0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x26,
	0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x8b, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x41,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3d, 0x0a, 0x09,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x22, 0xe8, 0x01, 0x0a, 0x18, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x26, 0x0a, 0x0f, 0x75,
	0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x5f, 0x6d, 0x64, 0x35, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46, 0x6c, 0x61, 0x67,
	0x4d, 0x64, 0x35, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22,
	0x98, 0x01, 0x0a, 0x1c, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x1f, 0x0a, 0x0b, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x73, 0x68, 0x61, 0x32, 0x35, 0x36, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x68, 0x61, 0x32, 0x35,
	0x36, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x83, 0x01, 0x0a, 0x1d, 0x4c,
	0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x09,
	0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65,
	0x22, 0xfe, 0x02, 0x0a, 0x14, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63,
	0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x63, 0x6c, 0x75, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6c, 0x75, 0x65,
	0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x63, 0x6c, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x75, 0x65, 0x5f, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x63, 0x6c, 0x75,
	0x65, 0x53, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x66, 0x6c, 0x61,
	0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x46,
	0x6c, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x22, 0x4b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x12, 0x2f, 0x0a,
	0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x9f,
	0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a,
	0x0d, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x69, 0x70, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x4f, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71,
	0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0x9a, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x74, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6b, 0x0a, 0x13, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0x44, 0x0a, 0x16, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xc5,
	0x0b, 0x0a, 0x0b, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x5a,
	0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63, 0x6b,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x54, 0x0a, 0x13, 0x4c, 0x69,
	0x73, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x12, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x69, 0x6c,
	0x65, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x5a, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x61,
	0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74,
	0x61, 0x63, 0x6b, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x18,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x51, 0x0a, 0x12, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42,
	0x79, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x53, 0x68, 0x61, 0x32,
	0x35, 0x36, 0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x53, 0x68, 0x61, 0x32, 0x35, 0x36,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x4d,
	0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x70,
	0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x6f, 0x70, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x43, 0x6c, 0x75, 0x65, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0c, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x1a, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x73, 0x12, 0x72, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65,
	0x61, 0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x73, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x28, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61,
	0x63, 0x68, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x63, 0x0a, 0x18, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x43, 0x6c, 0x75,
	0x65, 0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x51, 0x0a, 0x12,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x12, 0x1c, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x1d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x3b, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x11, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x0f,
	0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x19, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x72, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66,
	0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x27, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x72,
	0x65, 0x61, 0x63, 0x68, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x28, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x66, 0x0a, 0x19, 0x4c, 0x69,
	0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x65,
	0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x5a, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x1f, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x53,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x20, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x66,
	0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x12, 0x23, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x65, 0x6e, 0x46,
	0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71,
	0x1a, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x68, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conan_clue_proto_rawDescOnce sync.Once
	file_conan_clue_proto_rawDescData = file_conan_clue_proto_rawDesc
)

func file_conan_clue_proto_rawDescGZIP() []byte {
	file_conan_clue_proto_rawDescOnce.Do(func() {
		file_conan_clue_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_clue_proto_rawDescData)
	})
	return file_conan_clue_proto_rawDescData
}

var file_conan_clue_proto_msgTypes = make([]protoimpl.MessageInfo, 63)
var file_conan_clue_proto_goTypes = []any{
	(*ListMemoryAttackCluesReq)(nil),          // 0: conan.ListMemoryAttackCluesReq
	(*MemoryAttackClue)(nil),                  // 1: conan.MemoryAttackClue
	(*SystemAttackClue)(nil),                  // 2: conan.SystemAttackClue
	(*FileThreatClue)(nil),                    // 3: conan.FileThreatClue
	(*IllegalOutreachClue)(nil),               // 4: conan.IllegalOutreachClue
	(*UniversalAttackClue)(nil),               // 5: conan.UniversalAttackClue
	(*ListMemoryAttackCluesResp)(nil),         // 6: conan.ListMemoryAttackCluesResp
	(*ListFileThreatCluesReq)(nil),            // 7: conan.ListFileThreatCluesReq
	(*ListFileThreatCluesResp)(nil),           // 8: conan.ListFileThreatCluesResp
	(*ListSystemAttackCluesReq)(nil),          // 9: conan.ListSystemAttackCluesReq
	(*ListIllegalOutreachCluesReq)(nil),       // 10: conan.ListIllegalOutreachCluesReq
	(*ListIllegalOutreachCluesResp)(nil),      // 11: conan.ListIllegalOutreachCluesResp
	(*ListSystemAttackCluesResp)(nil),         // 12: conan.ListSystemAttackCluesResp
	(*CountCluesBySha256Req)(nil),             // 13: conan.CountCluesBySha256Req
	(*CountCluesBySha256Resp)(nil),            // 14: conan.CountCluesBySha256Resp
	(*Sha256Count)(nil),                       // 15: conan.Sha256Count
	(*ListTopMachineCluesReq)(nil),            // 16: conan.ListTopMachineCluesReq
	(*ListTopMachineCluesResp)(nil),           // 17: conan.ListTopMachineCluesResp
	(*MachineClueCount)(nil),                  // 18: conan.MachineClueCount
	(*GetClueStatsReq)(nil),                   // 19: conan.GetClueStatsReq
	(*ClueStats)(nil),                         // 20: conan.ClueStats
	(*ListTopMachineClueCountsReq)(nil),       // 21: conan.ListTopMachineClueCountsReq
	(*ListTopMachineClueCountsResp)(nil),      // 22: conan.ListTopMachineClueCountsResp
	(*ListOutreachAffectedTerminalsReq)(nil),  // 23: conan.ListOutreachAffectedTerminalsReq
	(*ListOutreachAffectedTerminalsResp)(nil), // 24: conan.ListOutreachAffectedTerminalsResp
	(*OutreachAffectedTerminal)(nil),          // 25: conan.OutreachAffectedTerminal
	(*CountOutreachCluesByTypeReq)(nil),       // 26: conan.CountOutreachCluesByTypeReq
	(*CountOutreachCluesByTypeResp)(nil),      // 27: conan.CountOutreachCluesByTypeResp
	(*OutreachTypeCount)(nil),                 // 28: conan.OutreachTypeCount
	(*ListClueTypeCountsReq)(nil),             // 29: conan.ListClueTypeCountsReq
	(*ListClueTypeCountsResp)(nil),            // 30: conan.ListClueTypeCountsResp
	(*ClueTypeCount)(nil),                     // 31: conan.ClueTypeCount
	(*ProcessInfo)(nil),                       // 32: conan.ProcessInfo
	(*FileInfo)(nil),                          // 33: conan.FileInfo
	(*DriverInfo)(nil),                        // 34: conan.DriverInfo
	(*MaliciousInfo)(nil),                     // 35: conan.MaliciousInfo
	(*OutreachInfo)(nil),                      // 36: conan.OutreachInfo
	(*AttackExtendInfo)(nil),                  // 37: conan.AttackExtendInfo
	(*Evidence)(nil),                          // 38: conan.Evidence
	(*AttackIndicatorContextValue)(nil),       // 39: conan.AttackIndicatorContextValue
	(*AttackDetail)(nil),                      // 40: conan.AttackDetail
	(*ClueDetail)(nil),                        // 41: conan.ClueDetail
	(*MemoryAttackClueDetail)(nil),            // 42: conan.MemoryAttackClueDetail
	(*FileThreatClueDetail)(nil),              // 43: conan.FileThreatClueDetail
	(*SystemAttackClueDetail)(nil),            // 44: conan.SystemAttackClueDetail
	(*IllegalOutreachClueDetail)(nil),         // 45: conan.IllegalOutreachClueDetail
	(*GetClueDetailReq)(nil),                  // 46: conan.GetClueDetailReq
	(*LLMResult)(nil),                         // 47: conan.LLMResult
	(*ListClueDetailsReq)(nil),                // 48: conan.ListClueDetailsReq
	(*ListClueDetailsResp)(nil),               // 49: conan.ListClueDetailsResp
	(*ListAffectedOutreachTerminalsReq)(nil),  // 50: conan.ListAffectedOutreachTerminalsReq
	(*ListAffectedOutreachTerminalsResp)(nil), // 51: conan.ListAffectedOutreachTerminalsResp
	(*AffectedOutreachTerminal)(nil),          // 52: conan.AffectedOutreachTerminal
	(*ListAffectedFileTerminalsReq)(nil),      // 53: conan.ListAffectedFileTerminalsReq
	(*ListAffectedFileTerminalsResp)(nil),     // 54: conan.ListAffectedFileTerminalsResp
	(*AffectedFileTerminal)(nil),              // 55: conan.AffectedFileTerminal
	(*GetOutreachStatisticsReq)(nil),          // 56: conan.GetOutreachStatisticsReq
	(*GetOutreachStatisticsResp)(nil),         // 57: conan.GetOutreachStatisticsResp
	(*GetThreatenFileStatisticsReq)(nil),      // 58: conan.GetThreatenFileStatisticsReq
	(*GetThreatenFileStatisticsResp)(nil),     // 59: conan.GetThreatenFileStatisticsResp
	nil,                                       // 60: conan.AttackDetail.AttackContextEntry
	nil,                                       // 61: conan.AttackDetail.ClueTypeExtraInfoEntry
	nil,                                       // 62: conan.GetThreatenFileStatisticsResp.CheckEngineCountsEntry
	(*ListAttackCluesFilter)(nil),             // 63: conan.ListAttackCluesFilter
	(*PageRequest)(nil),                       // 64: conan.PageRequest
	(*timestamppb.Timestamp)(nil),             // 65: google.protobuf.Timestamp
	(HitSource)(0),                            // 66: conan.HitSource
	(AptStatus)(0),                            // 67: conan.AptStatus
	(*PageResponse)(nil),                      // 68: conan.PageResponse
	(*TimeRange)(nil),                         // 69: conan.TimeRange
	(OutreachType)(0),                         // 70: conan.OutreachType
	(ClueType)(0),                             // 71: conan.ClueType
}
var file_conan_clue_proto_depIdxs = []int32{
	63,  // 0: conan.ListMemoryAttackCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	64,  // 1: conan.ListMemoryAttackCluesReq.page:type_name -> conan.PageRequest
	65,  // 2: conan.MemoryAttackClue.created_at:type_name -> google.protobuf.Timestamp
	65,  // 3: conan.MemoryAttackClue.updated_at:type_name -> google.protobuf.Timestamp
	65,  // 4: conan.MemoryAttackClue.detected_at:type_name -> google.protobuf.Timestamp
	65,  // 5: conan.SystemAttackClue.created_at:type_name -> google.protobuf.Timestamp
	65,  // 6: conan.SystemAttackClue.updated_at:type_name -> google.protobuf.Timestamp
	65,  // 7: conan.SystemAttackClue.detected_at:type_name -> google.protobuf.Timestamp
	65,  // 8: conan.FileThreatClue.updated_at:type_name -> google.protobuf.Timestamp
	65,  // 9: conan.FileThreatClue.created_at:type_name -> google.protobuf.Timestamp
	65,  // 10: conan.FileThreatClue.detected_at:type_name -> google.protobuf.Timestamp
	66,  // 11: conan.IllegalOutreachClue.hit_source:type_name -> conan.HitSource
	67,  // 12: conan.IllegalOutreachClue.is_apt:type_name -> conan.AptStatus
	65,  // 13: conan.IllegalOutreachClue.detected_at:type_name -> google.protobuf.Timestamp
	65,  // 14: conan.IllegalOutreachClue.created_at:type_name -> google.protobuf.Timestamp
	65,  // 15: conan.IllegalOutreachClue.updated_at:type_name -> google.protobuf.Timestamp
	47,  // 16: conan.IllegalOutreachClue.llm_result:type_name -> conan.LLMResult
	65,  // 17: conan.UniversalAttackClue.created_at:type_name -> google.protobuf.Timestamp
	65,  // 18: conan.UniversalAttackClue.updated_at:type_name -> google.protobuf.Timestamp
	65,  // 19: conan.UniversalAttackClue.detected_at:type_name -> google.protobuf.Timestamp
	1,   // 20: conan.ListMemoryAttackCluesResp.clues:type_name -> conan.MemoryAttackClue
	68,  // 21: conan.ListMemoryAttackCluesResp.page:type_name -> conan.PageResponse
	63,  // 22: conan.ListFileThreatCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	64,  // 23: conan.ListFileThreatCluesReq.page:type_name -> conan.PageRequest
	3,   // 24: conan.ListFileThreatCluesResp.clues:type_name -> conan.FileThreatClue
	68,  // 25: conan.ListFileThreatCluesResp.page:type_name -> conan.PageResponse
	63,  // 26: conan.ListSystemAttackCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	64,  // 27: conan.ListSystemAttackCluesReq.page:type_name -> conan.PageRequest
	63,  // 28: conan.ListIllegalOutreachCluesReq.filter:type_name -> conan.ListAttackCluesFilter
	64,  // 29: conan.ListIllegalOutreachCluesReq.page:type_name -> conan.PageRequest
	4,   // 30: conan.ListIllegalOutreachCluesResp.clues:type_name -> conan.IllegalOutreachClue
	68,  // 31: conan.ListIllegalOutreachCluesResp.page:type_name -> conan.PageResponse
	2,   // 32: conan.ListSystemAttackCluesResp.clues:type_name -> conan.SystemAttackClue
	68,  // 33: conan.ListSystemAttackCluesResp.page:type_name -> conan.PageResponse
	15,  // 34: conan.CountCluesBySha256Resp.counts:type_name -> conan.Sha256Count
	69,  // 35: conan.ListTopMachineCluesReq.time_range:type_name -> conan.TimeRange
	18,  // 36: conan.ListTopMachineCluesResp.machines:type_name -> conan.MachineClueCount
	69,  // 37: conan.ListTopMachineClueCountsReq.time_range:type_name -> conan.TimeRange
	69,  // 38: conan.ListOutreachAffectedTerminalsReq.time_range:type_name -> conan.TimeRange
	64,  // 39: conan.ListOutreachAffectedTerminalsReq.page:type_name -> conan.PageRequest
	25,  // 40: conan.ListOutreachAffectedTerminalsResp.terminals:type_name -> conan.OutreachAffectedTerminal
	68,  // 41: conan.ListOutreachAffectedTerminalsResp.page:type_name -> conan.PageResponse
	65,  // 42: conan.OutreachAffectedTerminal.create_time:type_name -> google.protobuf.Timestamp
	28,  // 43: conan.CountOutreachCluesByTypeResp.counts:type_name -> conan.OutreachTypeCount
	70,  // 44: conan.OutreachTypeCount.type:type_name -> conan.OutreachType
	71,  // 45: conan.ListClueTypeCountsReq.clue_type:type_name -> conan.ClueType
	69,  // 46: conan.ListClueTypeCountsReq.time_range:type_name -> conan.TimeRange
	31,  // 47: conan.ListClueTypeCountsResp.counts:type_name -> conan.ClueTypeCount
	71,  // 48: conan.ClueTypeCount.clue_type:type_name -> conan.ClueType
	65,  // 49: conan.ClueTypeCount.latest_create_time:type_name -> google.protobuf.Timestamp
	32,  // 50: conan.AttackDetail.process_info:type_name -> conan.ProcessInfo
	33,  // 51: conan.AttackDetail.file_info:type_name -> conan.FileInfo
	34,  // 52: conan.AttackDetail.driver_info:type_name -> conan.DriverInfo
	35,  // 53: conan.AttackDetail.malicious_info:type_name -> conan.MaliciousInfo
	36,  // 54: conan.AttackDetail.outreach_info:type_name -> conan.OutreachInfo
	38,  // 55: conan.AttackDetail.evidence_info:type_name -> conan.Evidence
	60,  // 56: conan.AttackDetail.attack_context:type_name -> conan.AttackDetail.AttackContextEntry
	37,  // 57: conan.AttackDetail.extend_info:type_name -> conan.AttackExtendInfo
	61,  // 58: conan.AttackDetail.clue_type_extra_info:type_name -> conan.AttackDetail.ClueTypeExtraInfoEntry
	42,  // 59: conan.ClueDetail.memory_attack_clue_detail:type_name -> conan.MemoryAttackClueDetail
	43,  // 60: conan.ClueDetail.file_threat_clue_detail:type_name -> conan.FileThreatClueDetail
	44,  // 61: conan.ClueDetail.system_attack_clue_detail:type_name -> conan.SystemAttackClueDetail
	45,  // 62: conan.ClueDetail.illegal_outreach_clue_detail:type_name -> conan.IllegalOutreachClueDetail
	71,  // 63: conan.ClueDetail.clue_type:type_name -> conan.ClueType
	1,   // 64: conan.MemoryAttackClueDetail.clue:type_name -> conan.MemoryAttackClue
	40,  // 65: conan.MemoryAttackClueDetail.detail:type_name -> conan.AttackDetail
	3,   // 66: conan.FileThreatClueDetail.clue:type_name -> conan.FileThreatClue
	40,  // 67: conan.FileThreatClueDetail.detail:type_name -> conan.AttackDetail
	2,   // 68: conan.SystemAttackClueDetail.clue:type_name -> conan.SystemAttackClue
	40,  // 69: conan.SystemAttackClueDetail.detail:type_name -> conan.AttackDetail
	4,   // 70: conan.IllegalOutreachClueDetail.clue:type_name -> conan.IllegalOutreachClue
	40,  // 71: conan.IllegalOutreachClueDetail.detail:type_name -> conan.AttackDetail
	71,  // 72: conan.GetClueDetailReq.clue_type:type_name -> conan.ClueType
	63,  // 73: conan.ListClueDetailsReq.filter:type_name -> conan.ListAttackCluesFilter
	71,  // 74: conan.ListClueDetailsReq.clue_type:type_name -> conan.ClueType
	64,  // 75: conan.ListClueDetailsReq.page:type_name -> conan.PageRequest
	41,  // 76: conan.ListClueDetailsResp.clues:type_name -> conan.ClueDetail
	68,  // 77: conan.ListClueDetailsResp.page:type_name -> conan.PageResponse
	70,  // 78: conan.ListAffectedOutreachTerminalsReq.type:type_name -> conan.OutreachType
	64,  // 79: conan.ListAffectedOutreachTerminalsReq.page:type_name -> conan.PageRequest
	69,  // 80: conan.ListAffectedOutreachTerminalsReq.range:type_name -> conan.TimeRange
	52,  // 81: conan.ListAffectedOutreachTerminalsResp.terminals:type_name -> conan.AffectedOutreachTerminal
	68,  // 82: conan.ListAffectedOutreachTerminalsResp.page:type_name -> conan.PageResponse
	65,  // 83: conan.AffectedOutreachTerminal.create_time:type_name -> google.protobuf.Timestamp
	69,  // 84: conan.ListAffectedFileTerminalsReq.time_range:type_name -> conan.TimeRange
	64,  // 85: conan.ListAffectedFileTerminalsReq.page:type_name -> conan.PageRequest
	55,  // 86: conan.ListAffectedFileTerminalsResp.terminals:type_name -> conan.AffectedFileTerminal
	68,  // 87: conan.ListAffectedFileTerminalsResp.page:type_name -> conan.PageResponse
	65,  // 88: conan.AffectedFileTerminal.create_time:type_name -> google.protobuf.Timestamp
	69,  // 89: conan.GetOutreachStatisticsReq.time_range:type_name -> conan.TimeRange
	69,  // 90: conan.GetThreatenFileStatisticsReq.time_range:type_name -> conan.TimeRange
	62,  // 91: conan.GetThreatenFileStatisticsResp.check_engine_counts:type_name -> conan.GetThreatenFileStatisticsResp.CheckEngineCountsEntry
	39,  // 92: conan.AttackDetail.AttackContextEntry.value:type_name -> conan.AttackIndicatorContextValue
	39,  // 93: conan.AttackDetail.ClueTypeExtraInfoEntry.value:type_name -> conan.AttackIndicatorContextValue
	0,   // 94: conan.ClueService.ListMemoryAttackClues:input_type -> conan.ListMemoryAttackCluesReq
	7,   // 95: conan.ClueService.ListFileThreatClues:input_type -> conan.ListFileThreatCluesReq
	9,   // 96: conan.ClueService.ListSystemAttackClues:input_type -> conan.ListSystemAttackCluesReq
	10,  // 97: conan.ClueService.ListIllegalOutreachClues:input_type -> conan.ListIllegalOutreachCluesReq
	13,  // 98: conan.ClueService.CountCluesBySha256:input_type -> conan.CountCluesBySha256Req
	21,  // 99: conan.ClueService.ListTopMachineClueCounts:input_type -> conan.ListTopMachineClueCountsReq
	19,  // 100: conan.ClueService.GetClueStats:input_type -> conan.GetClueStatsReq
	23,  // 101: conan.ClueService.ListOutreachAffectedTerminals:input_type -> conan.ListOutreachAffectedTerminalsReq
	26,  // 102: conan.ClueService.CountOutreachCluesByType:input_type -> conan.CountOutreachCluesByTypeReq
	29,  // 103: conan.ClueService.ListClueTypeCounts:input_type -> conan.ListClueTypeCountsReq
	46,  // 104: conan.ClueService.GetClueDetail:input_type -> conan.GetClueDetailReq
	48,  // 105: conan.ClueService.ListClueDetails:input_type -> conan.ListClueDetailsReq
	50,  // 106: conan.ClueService.ListAffectedOutreachTerminals:input_type -> conan.ListAffectedOutreachTerminalsReq
	53,  // 107: conan.ClueService.ListAffectedFileTerminals:input_type -> conan.ListAffectedFileTerminalsReq
	56,  // 108: conan.ClueService.GetOutreachStatistics:input_type -> conan.GetOutreachStatisticsReq
	58,  // 109: conan.ClueService.GetThreatenFileStatistics:input_type -> conan.GetThreatenFileStatisticsReq
	6,   // 110: conan.ClueService.ListMemoryAttackClues:output_type -> conan.ListMemoryAttackCluesResp
	8,   // 111: conan.ClueService.ListFileThreatClues:output_type -> conan.ListFileThreatCluesResp
	12,  // 112: conan.ClueService.ListSystemAttackClues:output_type -> conan.ListSystemAttackCluesResp
	11,  // 113: conan.ClueService.ListIllegalOutreachClues:output_type -> conan.ListIllegalOutreachCluesResp
	14,  // 114: conan.ClueService.CountCluesBySha256:output_type -> conan.CountCluesBySha256Resp
	22,  // 115: conan.ClueService.ListTopMachineClueCounts:output_type -> conan.ListTopMachineClueCountsResp
	20,  // 116: conan.ClueService.GetClueStats:output_type -> conan.ClueStats
	24,  // 117: conan.ClueService.ListOutreachAffectedTerminals:output_type -> conan.ListOutreachAffectedTerminalsResp
	27,  // 118: conan.ClueService.CountOutreachCluesByType:output_type -> conan.CountOutreachCluesByTypeResp
	30,  // 119: conan.ClueService.ListClueTypeCounts:output_type -> conan.ListClueTypeCountsResp
	41,  // 120: conan.ClueService.GetClueDetail:output_type -> conan.ClueDetail
	49,  // 121: conan.ClueService.ListClueDetails:output_type -> conan.ListClueDetailsResp
	51,  // 122: conan.ClueService.ListAffectedOutreachTerminals:output_type -> conan.ListAffectedOutreachTerminalsResp
	54,  // 123: conan.ClueService.ListAffectedFileTerminals:output_type -> conan.ListAffectedFileTerminalsResp
	57,  // 124: conan.ClueService.GetOutreachStatistics:output_type -> conan.GetOutreachStatisticsResp
	59,  // 125: conan.ClueService.GetThreatenFileStatistics:output_type -> conan.GetThreatenFileStatisticsResp
	110, // [110:126] is the sub-list for method output_type
	94,  // [94:110] is the sub-list for method input_type
	94,  // [94:94] is the sub-list for extension type_name
	94,  // [94:94] is the sub-list for extension extendee
	0,   // [0:94] is the sub-list for field type_name
}

func init() { file_conan_clue_proto_init() }
func file_conan_clue_proto_init() {
	if File_conan_clue_proto != nil {
		return
	}
	file_conan_common_proto_init()
	file_conan_clue_proto_msgTypes[39].OneofWrappers = []any{
		(*AttackIndicatorContextValue_StringValue)(nil),
		(*AttackIndicatorContextValue_Int64Value)(nil),
	}
	file_conan_clue_proto_msgTypes[41].OneofWrappers = []any{
		(*ClueDetail_MemoryAttackClueDetail)(nil),
		(*ClueDetail_FileThreatClueDetail)(nil),
		(*ClueDetail_SystemAttackClueDetail)(nil),
		(*ClueDetail_IllegalOutreachClueDetail)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_clue_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   63,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_clue_proto_goTypes,
		DependencyIndexes: file_conan_clue_proto_depIdxs,
		MessageInfos:      file_conan_clue_proto_msgTypes,
	}.Build()
	File_conan_clue_proto = out.File
	file_conan_clue_proto_rawDesc = nil
	file_conan_clue_proto_goTypes = nil
	file_conan_clue_proto_depIdxs = nil
}
