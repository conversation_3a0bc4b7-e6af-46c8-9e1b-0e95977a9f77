// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/policy.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AttributeInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttributeInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttributeInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttributeInfoRequestMultiError, or nil if none found.
func (m *AttributeInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AttributeInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Attribute

	// no validation rules for ClueKey

	if len(errors) > 0 {
		return AttributeInfoRequestMultiError(errors)
	}

	return nil
}

// AttributeInfoRequestMultiError is an error wrapping multiple validation
// errors returned by AttributeInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type AttributeInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttributeInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttributeInfoRequestMultiError) AllErrors() []error { return m }

// AttributeInfoRequestValidationError is the validation error returned by
// AttributeInfoRequest.Validate if the designated constraints aren't met.
type AttributeInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttributeInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttributeInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttributeInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttributeInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttributeInfoRequestValidationError) ErrorName() string {
	return "AttributeInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AttributeInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttributeInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttributeInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttributeInfoRequestValidationError{}

// Validate checks the field values on AttributeInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttributeInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttributeInfoResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttributeInfoResponseMultiError, or nil if none found.
func (m *AttributeInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AttributeInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AttributeInfoResponseMultiError(errors)
	}

	return nil
}

// AttributeInfoResponseMultiError is an error wrapping multiple validation
// errors returned by AttributeInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type AttributeInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttributeInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttributeInfoResponseMultiError) AllErrors() []error { return m }

// AttributeInfoResponseValidationError is the validation error returned by
// AttributeInfoResponse.Validate if the designated constraints aren't met.
type AttributeInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttributeInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttributeInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttributeInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttributeInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttributeInfoResponseValidationError) ErrorName() string {
	return "AttributeInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AttributeInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttributeInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttributeInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttributeInfoResponseValidationError{}

// Validate checks the field values on IgnoreStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IgnoreStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IgnoreStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IgnoreStatusRequestMultiError, or nil if none found.
func (m *IgnoreStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IgnoreStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IgnoreStatusRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IgnoreStatusRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IgnoreStatusRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for IsSelectAll

	// no validation rules for Action

	if len(errors) > 0 {
		return IgnoreStatusRequestMultiError(errors)
	}

	return nil
}

// IgnoreStatusRequestMultiError is an error wrapping multiple validation
// errors returned by IgnoreStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type IgnoreStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IgnoreStatusRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IgnoreStatusRequestMultiError) AllErrors() []error { return m }

// IgnoreStatusRequestValidationError is the validation error returned by
// IgnoreStatusRequest.Validate if the designated constraints aren't met.
type IgnoreStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IgnoreStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IgnoreStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IgnoreStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IgnoreStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IgnoreStatusRequestValidationError) ErrorName() string {
	return "IgnoreStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IgnoreStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIgnoreStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IgnoreStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IgnoreStatusRequestValidationError{}

// Validate checks the field values on IgnoreStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IgnoreStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IgnoreStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IgnoreStatusResponseMultiError, or nil if none found.
func (m *IgnoreStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IgnoreStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AffectedCount

	if len(errors) > 0 {
		return IgnoreStatusResponseMultiError(errors)
	}

	return nil
}

// IgnoreStatusResponseMultiError is an error wrapping multiple validation
// errors returned by IgnoreStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type IgnoreStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IgnoreStatusResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IgnoreStatusResponseMultiError) AllErrors() []error { return m }

// IgnoreStatusResponseValidationError is the validation error returned by
// IgnoreStatusResponse.Validate if the designated constraints aren't met.
type IgnoreStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IgnoreStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IgnoreStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IgnoreStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IgnoreStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IgnoreStatusResponseValidationError) ErrorName() string {
	return "IgnoreStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IgnoreStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIgnoreStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IgnoreStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IgnoreStatusResponseValidationError{}

// Validate checks the field values on BWPolicyRule with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BWPolicyRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BWPolicyRule with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BWPolicyRuleMultiError, or
// nil if none found.
func (m *BWPolicyRule) ValidateAll() error {
	return m.validate(true)
}

func (m *BWPolicyRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Attribute

	// no validation rules for Condition

	// no validation rules for Scope

	if len(errors) > 0 {
		return BWPolicyRuleMultiError(errors)
	}

	return nil
}

// BWPolicyRuleMultiError is an error wrapping multiple validation errors
// returned by BWPolicyRule.ValidateAll() if the designated constraints aren't met.
type BWPolicyRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BWPolicyRuleMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BWPolicyRuleMultiError) AllErrors() []error { return m }

// BWPolicyRuleValidationError is the validation error returned by
// BWPolicyRule.Validate if the designated constraints aren't met.
type BWPolicyRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BWPolicyRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BWPolicyRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BWPolicyRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BWPolicyRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BWPolicyRuleValidationError) ErrorName() string { return "BWPolicyRuleValidationError" }

// Error satisfies the builtin error interface
func (e BWPolicyRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBWPolicyRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BWPolicyRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BWPolicyRuleValidationError{}

// Validate checks the field values on BWPolicy with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BWPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BWPolicy with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BWPolicyMultiError, or nil
// if none found.
func (m *BWPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *BWPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Description

	for idx, item := range m.GetRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BWPolicyValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BWPolicyValidationError{
						field:  fmt.Sprintf("Rules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BWPolicyValidationError{
					field:  fmt.Sprintf("Rules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CreatorId

	// no validation rules for UpdaterId

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BWPolicyValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BWPolicyValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BWPolicyValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BWPolicyValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BWPolicyValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BWPolicyValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BWPolicyMultiError(errors)
	}

	return nil
}

// BWPolicyMultiError is an error wrapping multiple validation errors returned
// by BWPolicy.ValidateAll() if the designated constraints aren't met.
type BWPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BWPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BWPolicyMultiError) AllErrors() []error { return m }

// BWPolicyValidationError is the validation error returned by
// BWPolicy.Validate if the designated constraints aren't met.
type BWPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BWPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BWPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BWPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BWPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BWPolicyValidationError) ErrorName() string { return "BWPolicyValidationError" }

// Error satisfies the builtin error interface
func (e BWPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBWPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BWPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BWPolicyValidationError{}

// Validate checks the field values on CreateBWPolicyResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBWPolicyResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBWPolicyResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBWPolicyRespMultiError, or nil if none found.
func (m *CreateBWPolicyResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBWPolicyResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateBWPolicyRespValidationError{
						field:  fmt.Sprintf("Policies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateBWPolicyRespValidationError{
						field:  fmt.Sprintf("Policies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateBWPolicyRespValidationError{
					field:  fmt.Sprintf("Policies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateBWPolicyRespMultiError(errors)
	}

	return nil
}

// CreateBWPolicyRespMultiError is an error wrapping multiple validation errors
// returned by CreateBWPolicyResp.ValidateAll() if the designated constraints
// aren't met.
type CreateBWPolicyRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBWPolicyRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBWPolicyRespMultiError) AllErrors() []error { return m }

// CreateBWPolicyRespValidationError is the validation error returned by
// CreateBWPolicyResp.Validate if the designated constraints aren't met.
type CreateBWPolicyRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBWPolicyRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBWPolicyRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBWPolicyRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBWPolicyRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBWPolicyRespValidationError) ErrorName() string {
	return "CreateBWPolicyRespValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBWPolicyRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBWPolicyResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBWPolicyRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBWPolicyRespValidationError{}

// Validate checks the field values on DeleteBWPolicyReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteBWPolicyReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteBWPolicyReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteBWPolicyReqMultiError, or nil if none found.
func (m *DeleteBWPolicyReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteBWPolicyReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteBWPolicyReqMultiError(errors)
	}

	return nil
}

// DeleteBWPolicyReqMultiError is an error wrapping multiple validation errors
// returned by DeleteBWPolicyReq.ValidateAll() if the designated constraints
// aren't met.
type DeleteBWPolicyReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteBWPolicyReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteBWPolicyReqMultiError) AllErrors() []error { return m }

// DeleteBWPolicyReqValidationError is the validation error returned by
// DeleteBWPolicyReq.Validate if the designated constraints aren't met.
type DeleteBWPolicyReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteBWPolicyReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteBWPolicyReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteBWPolicyReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteBWPolicyReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteBWPolicyReqValidationError) ErrorName() string {
	return "DeleteBWPolicyReqValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteBWPolicyReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteBWPolicyReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteBWPolicyReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteBWPolicyReqValidationError{}

// Validate checks the field values on UpdateBWPolicyReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateBWPolicyReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBWPolicyReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBWPolicyReqMultiError, or nil if none found.
func (m *UpdateBWPolicyReq) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBWPolicyReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNewPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateBWPolicyReqValidationError{
						field:  fmt.Sprintf("NewPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateBWPolicyReqValidationError{
						field:  fmt.Sprintf("NewPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateBWPolicyReqValidationError{
					field:  fmt.Sprintf("NewPolicies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateBWPolicyReqMultiError(errors)
	}

	return nil
}

// UpdateBWPolicyReqMultiError is an error wrapping multiple validation errors
// returned by UpdateBWPolicyReq.ValidateAll() if the designated constraints
// aren't met.
type UpdateBWPolicyReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBWPolicyReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBWPolicyReqMultiError) AllErrors() []error { return m }

// UpdateBWPolicyReqValidationError is the validation error returned by
// UpdateBWPolicyReq.Validate if the designated constraints aren't met.
type UpdateBWPolicyReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBWPolicyReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBWPolicyReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBWPolicyReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBWPolicyReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBWPolicyReqValidationError) ErrorName() string {
	return "UpdateBWPolicyReqValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBWPolicyReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBWPolicyReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBWPolicyReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBWPolicyReqValidationError{}

// Validate checks the field values on RiskCategory with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskCategory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskCategory with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskCategoryMultiError, or
// nil if none found.
func (m *RiskCategory) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskCategory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ZhName

	if len(errors) > 0 {
		return RiskCategoryMultiError(errors)
	}

	return nil
}

// RiskCategoryMultiError is an error wrapping multiple validation errors
// returned by RiskCategory.ValidateAll() if the designated constraints aren't met.
type RiskCategoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskCategoryMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskCategoryMultiError) AllErrors() []error { return m }

// RiskCategoryValidationError is the validation error returned by
// RiskCategory.Validate if the designated constraints aren't met.
type RiskCategoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskCategoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskCategoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskCategoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskCategoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskCategoryValidationError) ErrorName() string { return "RiskCategoryValidationError" }

// Error satisfies the builtin error interface
func (e RiskCategoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskCategory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskCategoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskCategoryValidationError{}

// Validate checks the field values on SetRiskCategoriesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRiskCategoriesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRiskCategoriesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRiskCategoriesReqMultiError, or nil if none found.
func (m *SetRiskCategoriesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRiskCategoriesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetRiskCategoriesReqValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetRiskCategoriesReqValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetRiskCategoriesReqValidationError{
					field:  fmt.Sprintf("Categories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetRiskCategoriesReqMultiError(errors)
	}

	return nil
}

// SetRiskCategoriesReqMultiError is an error wrapping multiple validation
// errors returned by SetRiskCategoriesReq.ValidateAll() if the designated
// constraints aren't met.
type SetRiskCategoriesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRiskCategoriesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRiskCategoriesReqMultiError) AllErrors() []error { return m }

// SetRiskCategoriesReqValidationError is the validation error returned by
// SetRiskCategoriesReq.Validate if the designated constraints aren't met.
type SetRiskCategoriesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRiskCategoriesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRiskCategoriesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRiskCategoriesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRiskCategoriesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRiskCategoriesReqValidationError) ErrorName() string {
	return "SetRiskCategoriesReqValidationError"
}

// Error satisfies the builtin error interface
func (e SetRiskCategoriesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRiskCategoriesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRiskCategoriesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRiskCategoriesReqValidationError{}

// Validate checks the field values on SetRiskCategoriesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SetRiskCategoriesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SetRiskCategoriesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SetRiskCategoriesRespMultiError, or nil if none found.
func (m *SetRiskCategoriesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *SetRiskCategoriesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCategories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SetRiskCategoriesRespValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SetRiskCategoriesRespValidationError{
						field:  fmt.Sprintf("Categories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SetRiskCategoriesRespValidationError{
					field:  fmt.Sprintf("Categories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SetRiskCategoriesRespMultiError(errors)
	}

	return nil
}

// SetRiskCategoriesRespMultiError is an error wrapping multiple validation
// errors returned by SetRiskCategoriesResp.ValidateAll() if the designated
// constraints aren't met.
type SetRiskCategoriesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SetRiskCategoriesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SetRiskCategoriesRespMultiError) AllErrors() []error { return m }

// SetRiskCategoriesRespValidationError is the validation error returned by
// SetRiskCategoriesResp.Validate if the designated constraints aren't met.
type SetRiskCategoriesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SetRiskCategoriesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SetRiskCategoriesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SetRiskCategoriesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SetRiskCategoriesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SetRiskCategoriesRespValidationError) ErrorName() string {
	return "SetRiskCategoriesRespValidationError"
}

// Error satisfies the builtin error interface
func (e SetRiskCategoriesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSetRiskCategoriesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SetRiskCategoriesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SetRiskCategoriesRespValidationError{}
