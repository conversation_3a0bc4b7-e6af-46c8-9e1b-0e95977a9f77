// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/dashboard.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DashboardService_GetTerminalStats_FullMethodName         = "/conan.DashboardService/GetTerminalStats"
	DashboardService_ListTopRiskTerminals_FullMethodName     = "/conan.DashboardService/ListTopRiskTerminals"
	DashboardService_ListRecentThreatTrends_FullMethodName   = "/conan.DashboardService/ListRecentThreatTrends"
	DashboardService_GetFileArchiveStats_FullMethodName      = "/conan.DashboardService/GetFileArchiveStats"
	DashboardService_ListRecentOutreachTrends_FullMethodName = "/conan.DashboardService/ListRecentOutreachTrends"
	DashboardService_CountCluesByLevel_FullMethodName        = "/conan.DashboardService/CountCluesByLevel"
	DashboardService_CountCluesByType_FullMethodName         = "/conan.DashboardService/CountCluesByType"
)

// DashboardServiceClient is the client API for DashboardService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 仪表盘服务
// 统计最近7天数据，离线统计
type DashboardServiceClient interface {
	// 终端统计
	GetTerminalStats(ctx context.Context, in *GetTerminalStatsRequest, opts ...grpc.CallOption) (*TerminalStats, error)
	// 风险终端列表
	ListTopRiskTerminals(ctx context.Context, in *ListTopRiskTerminalsRequest, opts ...grpc.CallOption) (*ListTopRiskTerminalsResponse, error)
	// 最近N天的威胁趋势
	ListRecentThreatTrends(ctx context.Context, in *ListRecentThreatTrendsRequest, opts ...grpc.CallOption) (*ListRecentThreatTrendsResponse, error)
	// 文件归档统计
	GetFileArchiveStats(ctx context.Context, in *GetFileArchiveStatsRequest, opts ...grpc.CallOption) (*FileArchiveStats, error)
	// 最近N天的外联趋势
	ListRecentOutreachTrends(ctx context.Context, in *ListRecentOutreachTrendsRequest, opts ...grpc.CallOption) (*ListRecentOutreachTrendsResponse, error)
	// 获取按照威胁等级和威胁类型统计的线索数量
	CountCluesByLevel(ctx context.Context, in *CountCluesByLevelReq, opts ...grpc.CallOption) (*CountCluesByLevelResp, error)
	// 获取按照威胁类型统计的线索数量
	CountCluesByType(ctx context.Context, in *CountCluesByTypeReq, opts ...grpc.CallOption) (*CountCluesByTypeResp, error)
}

type dashboardServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDashboardServiceClient(cc grpc.ClientConnInterface) DashboardServiceClient {
	return &dashboardServiceClient{cc}
}

func (c *dashboardServiceClient) GetTerminalStats(ctx context.Context, in *GetTerminalStatsRequest, opts ...grpc.CallOption) (*TerminalStats, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TerminalStats)
	err := c.cc.Invoke(ctx, DashboardService_GetTerminalStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) ListTopRiskTerminals(ctx context.Context, in *ListTopRiskTerminalsRequest, opts ...grpc.CallOption) (*ListTopRiskTerminalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTopRiskTerminalsResponse)
	err := c.cc.Invoke(ctx, DashboardService_ListTopRiskTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) ListRecentThreatTrends(ctx context.Context, in *ListRecentThreatTrendsRequest, opts ...grpc.CallOption) (*ListRecentThreatTrendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListRecentThreatTrendsResponse)
	err := c.cc.Invoke(ctx, DashboardService_ListRecentThreatTrends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) GetFileArchiveStats(ctx context.Context, in *GetFileArchiveStatsRequest, opts ...grpc.CallOption) (*FileArchiveStats, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileArchiveStats)
	err := c.cc.Invoke(ctx, DashboardService_GetFileArchiveStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) ListRecentOutreachTrends(ctx context.Context, in *ListRecentOutreachTrendsRequest, opts ...grpc.CallOption) (*ListRecentOutreachTrendsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListRecentOutreachTrendsResponse)
	err := c.cc.Invoke(ctx, DashboardService_ListRecentOutreachTrends_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) CountCluesByLevel(ctx context.Context, in *CountCluesByLevelReq, opts ...grpc.CallOption) (*CountCluesByLevelResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountCluesByLevelResp)
	err := c.cc.Invoke(ctx, DashboardService_CountCluesByLevel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dashboardServiceClient) CountCluesByType(ctx context.Context, in *CountCluesByTypeReq, opts ...grpc.CallOption) (*CountCluesByTypeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountCluesByTypeResp)
	err := c.cc.Invoke(ctx, DashboardService_CountCluesByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DashboardServiceServer is the server API for DashboardService service.
// All implementations must embed UnimplementedDashboardServiceServer
// for forward compatibility.
//
// 仪表盘服务
// 统计最近7天数据，离线统计
type DashboardServiceServer interface {
	// 终端统计
	GetTerminalStats(context.Context, *GetTerminalStatsRequest) (*TerminalStats, error)
	// 风险终端列表
	ListTopRiskTerminals(context.Context, *ListTopRiskTerminalsRequest) (*ListTopRiskTerminalsResponse, error)
	// 最近N天的威胁趋势
	ListRecentThreatTrends(context.Context, *ListRecentThreatTrendsRequest) (*ListRecentThreatTrendsResponse, error)
	// 文件归档统计
	GetFileArchiveStats(context.Context, *GetFileArchiveStatsRequest) (*FileArchiveStats, error)
	// 最近N天的外联趋势
	ListRecentOutreachTrends(context.Context, *ListRecentOutreachTrendsRequest) (*ListRecentOutreachTrendsResponse, error)
	// 获取按照威胁等级和威胁类型统计的线索数量
	CountCluesByLevel(context.Context, *CountCluesByLevelReq) (*CountCluesByLevelResp, error)
	// 获取按照威胁类型统计的线索数量
	CountCluesByType(context.Context, *CountCluesByTypeReq) (*CountCluesByTypeResp, error)
	mustEmbedUnimplementedDashboardServiceServer()
}

// UnimplementedDashboardServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDashboardServiceServer struct{}

func (UnimplementedDashboardServiceServer) GetTerminalStats(context.Context, *GetTerminalStatsRequest) (*TerminalStats, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTerminalStats not implemented")
}
func (UnimplementedDashboardServiceServer) ListTopRiskTerminals(context.Context, *ListTopRiskTerminalsRequest) (*ListTopRiskTerminalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTopRiskTerminals not implemented")
}
func (UnimplementedDashboardServiceServer) ListRecentThreatTrends(context.Context, *ListRecentThreatTrendsRequest) (*ListRecentThreatTrendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecentThreatTrends not implemented")
}
func (UnimplementedDashboardServiceServer) GetFileArchiveStats(context.Context, *GetFileArchiveStatsRequest) (*FileArchiveStats, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileArchiveStats not implemented")
}
func (UnimplementedDashboardServiceServer) ListRecentOutreachTrends(context.Context, *ListRecentOutreachTrendsRequest) (*ListRecentOutreachTrendsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListRecentOutreachTrends not implemented")
}
func (UnimplementedDashboardServiceServer) CountCluesByLevel(context.Context, *CountCluesByLevelReq) (*CountCluesByLevelResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountCluesByLevel not implemented")
}
func (UnimplementedDashboardServiceServer) CountCluesByType(context.Context, *CountCluesByTypeReq) (*CountCluesByTypeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountCluesByType not implemented")
}
func (UnimplementedDashboardServiceServer) mustEmbedUnimplementedDashboardServiceServer() {}
func (UnimplementedDashboardServiceServer) testEmbeddedByValue()                          {}

// UnsafeDashboardServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DashboardServiceServer will
// result in compilation errors.
type UnsafeDashboardServiceServer interface {
	mustEmbedUnimplementedDashboardServiceServer()
}

func RegisterDashboardServiceServer(s grpc.ServiceRegistrar, srv DashboardServiceServer) {
	// If the following call pancis, it indicates UnimplementedDashboardServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DashboardService_ServiceDesc, srv)
}

func _DashboardService_GetTerminalStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTerminalStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).GetTerminalStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_GetTerminalStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).GetTerminalStats(ctx, req.(*GetTerminalStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_ListTopRiskTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTopRiskTerminalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).ListTopRiskTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_ListTopRiskTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).ListTopRiskTerminals(ctx, req.(*ListTopRiskTerminalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_ListRecentThreatTrends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecentThreatTrendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).ListRecentThreatTrends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_ListRecentThreatTrends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).ListRecentThreatTrends(ctx, req.(*ListRecentThreatTrendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_GetFileArchiveStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileArchiveStatsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).GetFileArchiveStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_GetFileArchiveStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).GetFileArchiveStats(ctx, req.(*GetFileArchiveStatsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_ListRecentOutreachTrends_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListRecentOutreachTrendsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).ListRecentOutreachTrends(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_ListRecentOutreachTrends_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).ListRecentOutreachTrends(ctx, req.(*ListRecentOutreachTrendsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_CountCluesByLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountCluesByLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).CountCluesByLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_CountCluesByLevel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).CountCluesByLevel(ctx, req.(*CountCluesByLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DashboardService_CountCluesByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountCluesByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DashboardServiceServer).CountCluesByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DashboardService_CountCluesByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DashboardServiceServer).CountCluesByType(ctx, req.(*CountCluesByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DashboardService_ServiceDesc is the grpc.ServiceDesc for DashboardService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DashboardService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.DashboardService",
	HandlerType: (*DashboardServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTerminalStats",
			Handler:    _DashboardService_GetTerminalStats_Handler,
		},
		{
			MethodName: "ListTopRiskTerminals",
			Handler:    _DashboardService_ListTopRiskTerminals_Handler,
		},
		{
			MethodName: "ListRecentThreatTrends",
			Handler:    _DashboardService_ListRecentThreatTrends_Handler,
		},
		{
			MethodName: "GetFileArchiveStats",
			Handler:    _DashboardService_GetFileArchiveStats_Handler,
		},
		{
			MethodName: "ListRecentOutreachTrends",
			Handler:    _DashboardService_ListRecentOutreachTrends_Handler,
		},
		{
			MethodName: "CountCluesByLevel",
			Handler:    _DashboardService_CountCluesByLevel_Handler,
		},
		{
			MethodName: "CountCluesByType",
			Handler:    _DashboardService_CountCluesByType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conan/dashboard.proto",
}
