// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/dashboard.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetTerminalStatsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTerminalStatsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTerminalStatsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTerminalStatsRequestMultiError, or nil if none found.
func (m *GetTerminalStatsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTerminalStatsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTerminalStatsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTerminalStatsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTerminalStatsRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTerminalStatsRequestMultiError(errors)
	}

	return nil
}

// GetTerminalStatsRequestMultiError is an error wrapping multiple validation
// errors returned by GetTerminalStatsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTerminalStatsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTerminalStatsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTerminalStatsRequestMultiError) AllErrors() []error { return m }

// GetTerminalStatsRequestValidationError is the validation error returned by
// GetTerminalStatsRequest.Validate if the designated constraints aren't met.
type GetTerminalStatsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTerminalStatsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTerminalStatsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTerminalStatsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTerminalStatsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTerminalStatsRequestValidationError) ErrorName() string {
	return "GetTerminalStatsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTerminalStatsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTerminalStatsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTerminalStatsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTerminalStatsRequestValidationError{}

// Validate checks the field values on TerminalStats with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TerminalStats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TerminalStats with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TerminalStatsMultiError, or
// nil if none found.
func (m *TerminalStats) ValidateAll() error {
	return m.validate(true)
}

func (m *TerminalStats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for Online

	// no validation rules for Offline

	// no validation rules for Risk

	// no validation rules for OnlineRate

	if len(errors) > 0 {
		return TerminalStatsMultiError(errors)
	}

	return nil
}

// TerminalStatsMultiError is an error wrapping multiple validation errors
// returned by TerminalStats.ValidateAll() if the designated constraints
// aren't met.
type TerminalStatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TerminalStatsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TerminalStatsMultiError) AllErrors() []error { return m }

// TerminalStatsValidationError is the validation error returned by
// TerminalStats.Validate if the designated constraints aren't met.
type TerminalStatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TerminalStatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TerminalStatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TerminalStatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TerminalStatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TerminalStatsValidationError) ErrorName() string { return "TerminalStatsValidationError" }

// Error satisfies the builtin error interface
func (e TerminalStatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTerminalStats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TerminalStatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TerminalStatsValidationError{}

// Validate checks the field values on RiskTerminal with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RiskTerminal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskTerminal with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RiskTerminalMultiError, or
// nil if none found.
func (m *RiskTerminal) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskTerminal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for Ip

	// no validation rules for Name

	// no validation rules for Group

	// no validation rules for ThreatCount

	if len(errors) > 0 {
		return RiskTerminalMultiError(errors)
	}

	return nil
}

// RiskTerminalMultiError is an error wrapping multiple validation errors
// returned by RiskTerminal.ValidateAll() if the designated constraints aren't met.
type RiskTerminalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskTerminalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskTerminalMultiError) AllErrors() []error { return m }

// RiskTerminalValidationError is the validation error returned by
// RiskTerminal.Validate if the designated constraints aren't met.
type RiskTerminalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskTerminalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskTerminalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskTerminalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskTerminalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskTerminalValidationError) ErrorName() string { return "RiskTerminalValidationError" }

// Error satisfies the builtin error interface
func (e RiskTerminalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskTerminal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskTerminalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskTerminalValidationError{}

// Validate checks the field values on ListTopRiskTerminalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopRiskTerminalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopRiskTerminalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopRiskTerminalsRequestMultiError, or nil if none found.
func (m *ListTopRiskTerminalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopRiskTerminalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTopRiskTerminalsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTopRiskTerminalsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTopRiskTerminalsRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTopRiskTerminalsRequestMultiError(errors)
	}

	return nil
}

// ListTopRiskTerminalsRequestMultiError is an error wrapping multiple
// validation errors returned by ListTopRiskTerminalsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListTopRiskTerminalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopRiskTerminalsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopRiskTerminalsRequestMultiError) AllErrors() []error { return m }

// ListTopRiskTerminalsRequestValidationError is the validation error returned
// by ListTopRiskTerminalsRequest.Validate if the designated constraints
// aren't met.
type ListTopRiskTerminalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopRiskTerminalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopRiskTerminalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopRiskTerminalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopRiskTerminalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopRiskTerminalsRequestValidationError) ErrorName() string {
	return "ListTopRiskTerminalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopRiskTerminalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopRiskTerminalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopRiskTerminalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopRiskTerminalsRequestValidationError{}

// Validate checks the field values on ListTopRiskTerminalsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopRiskTerminalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopRiskTerminalsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopRiskTerminalsResponseMultiError, or nil if none found.
func (m *ListTopRiskTerminalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopRiskTerminalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTerminals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTopRiskTerminalsResponseValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTopRiskTerminalsResponseValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTopRiskTerminalsResponseValidationError{
					field:  fmt.Sprintf("Terminals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTopRiskTerminalsResponseMultiError(errors)
	}

	return nil
}

// ListTopRiskTerminalsResponseMultiError is an error wrapping multiple
// validation errors returned by ListTopRiskTerminalsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListTopRiskTerminalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopRiskTerminalsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopRiskTerminalsResponseMultiError) AllErrors() []error { return m }

// ListTopRiskTerminalsResponseValidationError is the validation error returned
// by ListTopRiskTerminalsResponse.Validate if the designated constraints
// aren't met.
type ListTopRiskTerminalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopRiskTerminalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopRiskTerminalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopRiskTerminalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopRiskTerminalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopRiskTerminalsResponseValidationError) ErrorName() string {
	return "ListTopRiskTerminalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopRiskTerminalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopRiskTerminalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopRiskTerminalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopRiskTerminalsResponseValidationError{}

// Validate checks the field values on ThreatTrendItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ThreatTrendItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ThreatTrendItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ThreatTrendItemMultiError, or nil if none found.
func (m *ThreatTrendItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ThreatTrendItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	// no validation rules for LevelStats

	// no validation rules for ThreatCount

	if len(errors) > 0 {
		return ThreatTrendItemMultiError(errors)
	}

	return nil
}

// ThreatTrendItemMultiError is an error wrapping multiple validation errors
// returned by ThreatTrendItem.ValidateAll() if the designated constraints
// aren't met.
type ThreatTrendItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ThreatTrendItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ThreatTrendItemMultiError) AllErrors() []error { return m }

// ThreatTrendItemValidationError is the validation error returned by
// ThreatTrendItem.Validate if the designated constraints aren't met.
type ThreatTrendItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ThreatTrendItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ThreatTrendItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ThreatTrendItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ThreatTrendItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ThreatTrendItemValidationError) ErrorName() string { return "ThreatTrendItemValidationError" }

// Error satisfies the builtin error interface
func (e ThreatTrendItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sThreatTrendItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ThreatTrendItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ThreatTrendItemValidationError{}

// Validate checks the field values on ListRecentThreatTrendsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListRecentThreatTrendsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRecentThreatTrendsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListRecentThreatTrendsRequestMultiError, or nil if none found.
func (m *ListRecentThreatTrendsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRecentThreatTrendsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListRecentThreatTrendsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListRecentThreatTrendsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListRecentThreatTrendsRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListRecentThreatTrendsRequestMultiError(errors)
	}

	return nil
}

// ListRecentThreatTrendsRequestMultiError is an error wrapping multiple
// validation errors returned by ListRecentThreatTrendsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListRecentThreatTrendsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRecentThreatTrendsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRecentThreatTrendsRequestMultiError) AllErrors() []error { return m }

// ListRecentThreatTrendsRequestValidationError is the validation error
// returned by ListRecentThreatTrendsRequest.Validate if the designated
// constraints aren't met.
type ListRecentThreatTrendsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRecentThreatTrendsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRecentThreatTrendsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRecentThreatTrendsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRecentThreatTrendsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRecentThreatTrendsRequestValidationError) ErrorName() string {
	return "ListRecentThreatTrendsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListRecentThreatTrendsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRecentThreatTrendsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRecentThreatTrendsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRecentThreatTrendsRequestValidationError{}

// Validate checks the field values on ListRecentThreatTrendsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListRecentThreatTrendsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRecentThreatTrendsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListRecentThreatTrendsResponseMultiError, or nil if none found.
func (m *ListRecentThreatTrendsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRecentThreatTrendsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTrends() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRecentThreatTrendsResponseValidationError{
						field:  fmt.Sprintf("Trends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRecentThreatTrendsResponseValidationError{
						field:  fmt.Sprintf("Trends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRecentThreatTrendsResponseValidationError{
					field:  fmt.Sprintf("Trends[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListRecentThreatTrendsResponseMultiError(errors)
	}

	return nil
}

// ListRecentThreatTrendsResponseMultiError is an error wrapping multiple
// validation errors returned by ListRecentThreatTrendsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListRecentThreatTrendsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRecentThreatTrendsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRecentThreatTrendsResponseMultiError) AllErrors() []error { return m }

// ListRecentThreatTrendsResponseValidationError is the validation error
// returned by ListRecentThreatTrendsResponse.Validate if the designated
// constraints aren't met.
type ListRecentThreatTrendsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRecentThreatTrendsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRecentThreatTrendsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRecentThreatTrendsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRecentThreatTrendsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRecentThreatTrendsResponseValidationError) ErrorName() string {
	return "ListRecentThreatTrendsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListRecentThreatTrendsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRecentThreatTrendsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRecentThreatTrendsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRecentThreatTrendsResponseValidationError{}

// Validate checks the field values on GetFileArchiveStatsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileArchiveStatsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileArchiveStatsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileArchiveStatsRequestMultiError, or nil if none found.
func (m *GetFileArchiveStatsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileArchiveStatsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetFileArchiveStatsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetFileArchiveStatsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetFileArchiveStatsRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetFileArchiveStatsRequestMultiError(errors)
	}

	return nil
}

// GetFileArchiveStatsRequestMultiError is an error wrapping multiple
// validation errors returned by GetFileArchiveStatsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetFileArchiveStatsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileArchiveStatsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileArchiveStatsRequestMultiError) AllErrors() []error { return m }

// GetFileArchiveStatsRequestValidationError is the validation error returned
// by GetFileArchiveStatsRequest.Validate if the designated constraints aren't met.
type GetFileArchiveStatsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileArchiveStatsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileArchiveStatsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileArchiveStatsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileArchiveStatsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileArchiveStatsRequestValidationError) ErrorName() string {
	return "GetFileArchiveStatsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileArchiveStatsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileArchiveStatsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileArchiveStatsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileArchiveStatsRequestValidationError{}

// Validate checks the field values on FileArchiveStats with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileArchiveStats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileArchiveStats with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileArchiveStatsMultiError, or nil if none found.
func (m *FileArchiveStats) ValidateAll() error {
	return m.validate(true)
}

func (m *FileArchiveStats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Black

	// no validation rules for White

	// no validation rules for Gray

	// no validation rules for Unknown

	if len(errors) > 0 {
		return FileArchiveStatsMultiError(errors)
	}

	return nil
}

// FileArchiveStatsMultiError is an error wrapping multiple validation errors
// returned by FileArchiveStats.ValidateAll() if the designated constraints
// aren't met.
type FileArchiveStatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileArchiveStatsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileArchiveStatsMultiError) AllErrors() []error { return m }

// FileArchiveStatsValidationError is the validation error returned by
// FileArchiveStats.Validate if the designated constraints aren't met.
type FileArchiveStatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileArchiveStatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileArchiveStatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileArchiveStatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileArchiveStatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileArchiveStatsValidationError) ErrorName() string { return "FileArchiveStatsValidationError" }

// Error satisfies the builtin error interface
func (e FileArchiveStatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileArchiveStats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileArchiveStatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileArchiveStatsValidationError{}

// Validate checks the field values on ListRecentOutreachTrendsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListRecentOutreachTrendsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRecentOutreachTrendsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListRecentOutreachTrendsRequestMultiError, or nil if none found.
func (m *ListRecentOutreachTrendsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRecentOutreachTrendsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListRecentOutreachTrendsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListRecentOutreachTrendsRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListRecentOutreachTrendsRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListRecentOutreachTrendsRequestMultiError(errors)
	}

	return nil
}

// ListRecentOutreachTrendsRequestMultiError is an error wrapping multiple
// validation errors returned by ListRecentOutreachTrendsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListRecentOutreachTrendsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRecentOutreachTrendsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRecentOutreachTrendsRequestMultiError) AllErrors() []error { return m }

// ListRecentOutreachTrendsRequestValidationError is the validation error
// returned by ListRecentOutreachTrendsRequest.Validate if the designated
// constraints aren't met.
type ListRecentOutreachTrendsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRecentOutreachTrendsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRecentOutreachTrendsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRecentOutreachTrendsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRecentOutreachTrendsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRecentOutreachTrendsRequestValidationError) ErrorName() string {
	return "ListRecentOutreachTrendsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListRecentOutreachTrendsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRecentOutreachTrendsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRecentOutreachTrendsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRecentOutreachTrendsRequestValidationError{}

// Validate checks the field values on ListRecentOutreachTrendsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListRecentOutreachTrendsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListRecentOutreachTrendsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListRecentOutreachTrendsResponseMultiError, or nil if none found.
func (m *ListRecentOutreachTrendsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListRecentOutreachTrendsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTrends() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListRecentOutreachTrendsResponseValidationError{
						field:  fmt.Sprintf("Trends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListRecentOutreachTrendsResponseValidationError{
						field:  fmt.Sprintf("Trends[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListRecentOutreachTrendsResponseValidationError{
					field:  fmt.Sprintf("Trends[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListRecentOutreachTrendsResponseMultiError(errors)
	}

	return nil
}

// ListRecentOutreachTrendsResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListRecentOutreachTrendsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListRecentOutreachTrendsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListRecentOutreachTrendsResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListRecentOutreachTrendsResponseMultiError) AllErrors() []error { return m }

// ListRecentOutreachTrendsResponseValidationError is the validation error
// returned by ListRecentOutreachTrendsResponse.Validate if the designated
// constraints aren't met.
type ListRecentOutreachTrendsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListRecentOutreachTrendsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListRecentOutreachTrendsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListRecentOutreachTrendsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListRecentOutreachTrendsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListRecentOutreachTrendsResponseValidationError) ErrorName() string {
	return "ListRecentOutreachTrendsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListRecentOutreachTrendsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListRecentOutreachTrendsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListRecentOutreachTrendsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListRecentOutreachTrendsResponseValidationError{}

// Validate checks the field values on OutreachTrendItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachTrendItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachTrendItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachTrendItemMultiError, or nil if none found.
func (m *OutreachTrendItem) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachTrendItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Day

	// no validation rules for TotalCount

	// no validation rules for IllegalCount

	if len(errors) > 0 {
		return OutreachTrendItemMultiError(errors)
	}

	return nil
}

// OutreachTrendItemMultiError is an error wrapping multiple validation errors
// returned by OutreachTrendItem.ValidateAll() if the designated constraints
// aren't met.
type OutreachTrendItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachTrendItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachTrendItemMultiError) AllErrors() []error { return m }

// OutreachTrendItemValidationError is the validation error returned by
// OutreachTrendItem.Validate if the designated constraints aren't met.
type OutreachTrendItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachTrendItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachTrendItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachTrendItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachTrendItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachTrendItemValidationError) ErrorName() string {
	return "OutreachTrendItemValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachTrendItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachTrendItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachTrendItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachTrendItemValidationError{}

// Validate checks the field values on CountCluesByLevelReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesByLevelReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesByLevelReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesByLevelReqMultiError, or nil if none found.
func (m *CountCluesByLevelReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesByLevelReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountCluesByLevelReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountCluesByLevelReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountCluesByLevelReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountCluesByLevelReqMultiError(errors)
	}

	return nil
}

// CountCluesByLevelReqMultiError is an error wrapping multiple validation
// errors returned by CountCluesByLevelReq.ValidateAll() if the designated
// constraints aren't met.
type CountCluesByLevelReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesByLevelReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesByLevelReqMultiError) AllErrors() []error { return m }

// CountCluesByLevelReqValidationError is the validation error returned by
// CountCluesByLevelReq.Validate if the designated constraints aren't met.
type CountCluesByLevelReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesByLevelReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesByLevelReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesByLevelReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesByLevelReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesByLevelReqValidationError) ErrorName() string {
	return "CountCluesByLevelReqValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesByLevelReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesByLevelReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesByLevelReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesByLevelReqValidationError{}

// Validate checks the field values on CountCluesByLevelResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesByLevelResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesByLevelResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesByLevelRespMultiError, or nil if none found.
func (m *CountCluesByLevelResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesByLevelResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalCount

	// no validation rules for LevelCounts

	for idx, item := range m.GetKindCounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CountCluesByLevelRespValidationError{
						field:  fmt.Sprintf("KindCounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CountCluesByLevelRespValidationError{
						field:  fmt.Sprintf("KindCounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CountCluesByLevelRespValidationError{
					field:  fmt.Sprintf("KindCounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CountCluesByLevelRespMultiError(errors)
	}

	return nil
}

// CountCluesByLevelRespMultiError is an error wrapping multiple validation
// errors returned by CountCluesByLevelResp.ValidateAll() if the designated
// constraints aren't met.
type CountCluesByLevelRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesByLevelRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesByLevelRespMultiError) AllErrors() []error { return m }

// CountCluesByLevelRespValidationError is the validation error returned by
// CountCluesByLevelResp.Validate if the designated constraints aren't met.
type CountCluesByLevelRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesByLevelRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesByLevelRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesByLevelRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesByLevelRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesByLevelRespValidationError) ErrorName() string {
	return "CountCluesByLevelRespValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesByLevelRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesByLevelResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesByLevelRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesByLevelRespValidationError{}

// Validate checks the field values on CountCluesByTypeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesByTypeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesByTypeReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesByTypeReqMultiError, or nil if none found.
func (m *CountCluesByTypeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesByTypeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountCluesByTypeReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountCluesByTypeReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountCluesByTypeReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountCluesByTypeReqMultiError(errors)
	}

	return nil
}

// CountCluesByTypeReqMultiError is an error wrapping multiple validation
// errors returned by CountCluesByTypeReq.ValidateAll() if the designated
// constraints aren't met.
type CountCluesByTypeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesByTypeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesByTypeReqMultiError) AllErrors() []error { return m }

// CountCluesByTypeReqValidationError is the validation error returned by
// CountCluesByTypeReq.Validate if the designated constraints aren't met.
type CountCluesByTypeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesByTypeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesByTypeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesByTypeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesByTypeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesByTypeReqValidationError) ErrorName() string {
	return "CountCluesByTypeReqValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesByTypeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesByTypeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesByTypeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesByTypeReqValidationError{}

// Validate checks the field values on CountCluesByTypeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesByTypeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesByTypeResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesByTypeRespMultiError, or nil if none found.
func (m *CountCluesByTypeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesByTypeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Counts

	// no validation rules for TotalCount

	if len(errors) > 0 {
		return CountCluesByTypeRespMultiError(errors)
	}

	return nil
}

// CountCluesByTypeRespMultiError is an error wrapping multiple validation
// errors returned by CountCluesByTypeResp.ValidateAll() if the designated
// constraints aren't met.
type CountCluesByTypeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesByTypeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesByTypeRespMultiError) AllErrors() []error { return m }

// CountCluesByTypeRespValidationError is the validation error returned by
// CountCluesByTypeResp.Validate if the designated constraints aren't met.
type CountCluesByTypeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesByTypeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesByTypeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesByTypeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesByTypeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesByTypeRespValidationError) ErrorName() string {
	return "CountCluesByTypeRespValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesByTypeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesByTypeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesByTypeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesByTypeRespValidationError{}

// Validate checks the field values on CountCluesByLevelResp_ClueKindCount with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CountCluesByLevelResp_ClueKindCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesByLevelResp_ClueKindCount
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CountCluesByLevelResp_ClueKindCountMultiError, or nil if none found.
func (m *CountCluesByLevelResp_ClueKindCount) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesByLevelResp_ClueKindCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Kind

	// no validation rules for Count

	if all {
		switch v := interface{}(m.GetLatestCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountCluesByLevelResp_ClueKindCountValidationError{
					field:  "LatestCreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountCluesByLevelResp_ClueKindCountValidationError{
					field:  "LatestCreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountCluesByLevelResp_ClueKindCountValidationError{
				field:  "LatestCreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountCluesByLevelResp_ClueKindCountMultiError(errors)
	}

	return nil
}

// CountCluesByLevelResp_ClueKindCountMultiError is an error wrapping multiple
// validation errors returned by
// CountCluesByLevelResp_ClueKindCount.ValidateAll() if the designated
// constraints aren't met.
type CountCluesByLevelResp_ClueKindCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesByLevelResp_ClueKindCountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesByLevelResp_ClueKindCountMultiError) AllErrors() []error { return m }

// CountCluesByLevelResp_ClueKindCountValidationError is the validation error
// returned by CountCluesByLevelResp_ClueKindCount.Validate if the designated
// constraints aren't met.
type CountCluesByLevelResp_ClueKindCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesByLevelResp_ClueKindCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesByLevelResp_ClueKindCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesByLevelResp_ClueKindCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesByLevelResp_ClueKindCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesByLevelResp_ClueKindCountValidationError) ErrorName() string {
	return "CountCluesByLevelResp_ClueKindCountValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesByLevelResp_ClueKindCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesByLevelResp_ClueKindCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesByLevelResp_ClueKindCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesByLevelResp_ClueKindCountValidationError{}
