// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/evidence.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EvidenceService_ObtainEvidenceChannelAvailableNotify_FullMethodName = "/conan.EvidenceService/ObtainEvidenceChannelAvailableNotify"
	EvidenceService_ObtainEvidenceFinishedNotify_FullMethodName         = "/conan.EvidenceService/ObtainEvidenceFinishedNotify"
	EvidenceService_ReportObtainEvidenceInfo_FullMethodName             = "/conan.EvidenceService/ReportObtainEvidenceInfo"
	EvidenceService_AddClueObtainEvidenceTask_FullMethodName            = "/conan.EvidenceService/AddClueObtainEvidenceTask"
	EvidenceService_AddFileObtainEvidenceTask_FullMethodName            = "/conan.EvidenceService/AddFileObtainEvidenceTask"
	EvidenceService_BatchAddFileObtainEvidenceTask_FullMethodName       = "/conan.EvidenceService/BatchAddFileObtainEvidenceTask"
	EvidenceService_AddNetObtainEvidenceTask_FullMethodName             = "/conan.EvidenceService/AddNetObtainEvidenceTask"
	EvidenceService_AddLogObtainEvidenceTask_FullMethodName             = "/conan.EvidenceService/AddLogObtainEvidenceTask"
	EvidenceService_RenewObtainEvidence_FullMethodName                  = "/conan.EvidenceService/RenewObtainEvidence"
	EvidenceService_GetClueEvidenceInfo_FullMethodName                  = "/conan.EvidenceService/GetClueEvidenceInfo"
	EvidenceService_GetFileEvidenceInfo_FullMethodName                  = "/conan.EvidenceService/GetFileEvidenceInfo"
	EvidenceService_QueryNetEvidenceInfo_FullMethodName                 = "/conan.EvidenceService/QueryNetEvidenceInfo"
	EvidenceService_QueryFileEvidenceInfo_FullMethodName                = "/conan.EvidenceService/QueryFileEvidenceInfo"
	EvidenceService_QueryMemoryDumpEvidenceInfo_FullMethodName          = "/conan.EvidenceService/QueryMemoryDumpEvidenceInfo"
	EvidenceService_QueryLogEvidenceInfo_FullMethodName                 = "/conan.EvidenceService/QueryLogEvidenceInfo"
	EvidenceService_GetEvidenceResultInfo_FullMethodName                = "/conan.EvidenceService/GetEvidenceResultInfo"
	EvidenceService_BatchGetEvidenceResultInfo_FullMethodName           = "/conan.EvidenceService/BatchGetEvidenceResultInfo"
	EvidenceService_GetEvidenceStorageInfo_FullMethodName               = "/conan.EvidenceService/GetEvidenceStorageInfo"
	EvidenceService_GetObtainedEvidenceCount_FullMethodName             = "/conan.EvidenceService/GetObtainedEvidenceCount"
	EvidenceService_GetEvidenceSourceConfig_FullMethodName              = "/conan.EvidenceService/GetEvidenceSourceConfig"
	EvidenceService_AddEvidenceSourceConfig_FullMethodName              = "/conan.EvidenceService/AddEvidenceSourceConfig"
	EvidenceService_DeleteEvidenceSourceConfig_FullMethodName           = "/conan.EvidenceService/DeleteEvidenceSourceConfig"
	EvidenceService_UpdateAutoObtainEvidenceClueSubType_FullMethodName  = "/conan.EvidenceService/UpdateAutoObtainEvidenceClueSubType"
)

// EvidenceServiceClient is the client API for EvidenceService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 证据管理服务
type EvidenceServiceClient interface {
	// 资源通知
	// 取证通道可用通知
	ObtainEvidenceChannelAvailableNotify(ctx context.Context, in *LimitPassNotifyRequest, opts ...grpc.CallOption) (*ObtainEvidenceChannelAvailableNotifyResponse, error)
	// 取证完成通知
	ObtainEvidenceFinishedNotify(ctx context.Context, in *ObtainEvidenceFinishedNotifyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// illaoi 通知
	// 上报取证结果信息
	ReportObtainEvidenceInfo(ctx context.Context, in *ObtainEvidenceResponse, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 添加取证任务
	// 添加线索取证任务
	AddClueObtainEvidenceTask(ctx context.Context, in *ClueObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*ClueObtainEvidenceTaskStatus, error)
	// 添加文件取证任务
	AddFileObtainEvidenceTask(ctx context.Context, in *FileObtainEvidenceTaskInfo, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error)
	// 批量添加文件取证任务
	BatchAddFileObtainEvidenceTask(ctx context.Context, in *BatchFileObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*BatchFileObtainEvidenceResponse, error)
	// 添加网络取证任务
	AddNetObtainEvidenceTask(ctx context.Context, in *NetObtainEvidenceTaskInfo, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error)
	// 添加日志取证任务
	AddLogObtainEvidenceTask(ctx context.Context, in *LogObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error)
	// 重新提取证据
	RenewObtainEvidence(ctx context.Context, in *RenewObtainEvidenceRequest, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error)
	// 查询取证任务
	// 根据线索获取证据信息
	GetClueEvidenceInfo(ctx context.Context, in *ClueEvidenceQueryRequest, opts ...grpc.CallOption) (*EvidenceExtractionResultResponse, error)
	// 获取文件证据信息
	GetFileEvidenceInfo(ctx context.Context, in *GetFileEvidenceRequest, opts ...grpc.CallOption) (*EvidenceExtractionResult, error)
	// 查询网络外联取证信息
	QueryNetEvidenceInfo(ctx context.Context, in *NetEvidenceQueryRequest, opts ...grpc.CallOption) (*NetEvidenceInfoListResponse, error)
	// 查询文件取证信息
	QueryFileEvidenceInfo(ctx context.Context, in *FileEvidenceQueryRequest, opts ...grpc.CallOption) (*FileEvidenceInfoListResponse, error)
	// 查询内存取证信息
	QueryMemoryDumpEvidenceInfo(ctx context.Context, in *MemDumpEvidenceQueryRequest, opts ...grpc.CallOption) (*MemDumpEvidenceInfoListResponse, error)
	// 查询日志取证信息
	QueryLogEvidenceInfo(ctx context.Context, in *LogEvidenceQueryRequest, opts ...grpc.CallOption) (*LogEvidenceInfoListResponse, error)
	// 获取取证结果信息
	GetEvidenceResultInfo(ctx context.Context, in *GetEvidenceResultInfoRequest, opts ...grpc.CallOption) (*EvidenceResultInfo, error)
	// 批量获取取证结果信息
	BatchGetEvidenceResultInfo(ctx context.Context, in *BatchGetEvidenceResultInfoRequest, opts ...grpc.CallOption) (*BatchEvidenceResultInfoResponse, error)
	// 获取取证存储信息
	GetEvidenceStorageInfo(ctx context.Context, in *GetEvidenceStorageInfoRequest, opts ...grpc.CallOption) (*EvidenceStorage, error)
	// 获取已获取证据计数
	GetObtainedEvidenceCount(ctx context.Context, in *GetObtainedEvidenceCountRequest, opts ...grpc.CallOption) (*ObtainEvidenceCountResponse, error)
	// 取证配置信息
	// 获取取证来源配置信息
	GetEvidenceSourceConfig(ctx context.Context, in *GetEvidenceSourceConfigRequest, opts ...grpc.CallOption) (*EvidenceSourceConfigResponse, error)
	// 添加取证源配置
	AddEvidenceSourceConfig(ctx context.Context, in *EvidenceSourceConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 删除取证源配置
	DeleteEvidenceSourceConfig(ctx context.Context, in *DeleteEvidenceSourceConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新取证来源配置信息(自动取证线索子类型)
	UpdateAutoObtainEvidenceClueSubType(ctx context.Context, in *UpdateAutoObtainEvidenceClueSubTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type evidenceServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEvidenceServiceClient(cc grpc.ClientConnInterface) EvidenceServiceClient {
	return &evidenceServiceClient{cc}
}

func (c *evidenceServiceClient) ObtainEvidenceChannelAvailableNotify(ctx context.Context, in *LimitPassNotifyRequest, opts ...grpc.CallOption) (*ObtainEvidenceChannelAvailableNotifyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceChannelAvailableNotifyResponse)
	err := c.cc.Invoke(ctx, EvidenceService_ObtainEvidenceChannelAvailableNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) ObtainEvidenceFinishedNotify(ctx context.Context, in *ObtainEvidenceFinishedNotifyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EvidenceService_ObtainEvidenceFinishedNotify_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) ReportObtainEvidenceInfo(ctx context.Context, in *ObtainEvidenceResponse, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EvidenceService_ReportObtainEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) AddClueObtainEvidenceTask(ctx context.Context, in *ClueObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*ClueObtainEvidenceTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClueObtainEvidenceTaskStatus)
	err := c.cc.Invoke(ctx, EvidenceService_AddClueObtainEvidenceTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) AddFileObtainEvidenceTask(ctx context.Context, in *FileObtainEvidenceTaskInfo, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceTaskStatus)
	err := c.cc.Invoke(ctx, EvidenceService_AddFileObtainEvidenceTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) BatchAddFileObtainEvidenceTask(ctx context.Context, in *BatchFileObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*BatchFileObtainEvidenceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchFileObtainEvidenceResponse)
	err := c.cc.Invoke(ctx, EvidenceService_BatchAddFileObtainEvidenceTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) AddNetObtainEvidenceTask(ctx context.Context, in *NetObtainEvidenceTaskInfo, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceTaskStatus)
	err := c.cc.Invoke(ctx, EvidenceService_AddNetObtainEvidenceTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) AddLogObtainEvidenceTask(ctx context.Context, in *LogObtainEvidenceTaskRequest, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceTaskStatus)
	err := c.cc.Invoke(ctx, EvidenceService_AddLogObtainEvidenceTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) RenewObtainEvidence(ctx context.Context, in *RenewObtainEvidenceRequest, opts ...grpc.CallOption) (*ObtainEvidenceTaskStatus, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceTaskStatus)
	err := c.cc.Invoke(ctx, EvidenceService_RenewObtainEvidence_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetClueEvidenceInfo(ctx context.Context, in *ClueEvidenceQueryRequest, opts ...grpc.CallOption) (*EvidenceExtractionResultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EvidenceExtractionResultResponse)
	err := c.cc.Invoke(ctx, EvidenceService_GetClueEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetFileEvidenceInfo(ctx context.Context, in *GetFileEvidenceRequest, opts ...grpc.CallOption) (*EvidenceExtractionResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EvidenceExtractionResult)
	err := c.cc.Invoke(ctx, EvidenceService_GetFileEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) QueryNetEvidenceInfo(ctx context.Context, in *NetEvidenceQueryRequest, opts ...grpc.CallOption) (*NetEvidenceInfoListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NetEvidenceInfoListResponse)
	err := c.cc.Invoke(ctx, EvidenceService_QueryNetEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) QueryFileEvidenceInfo(ctx context.Context, in *FileEvidenceQueryRequest, opts ...grpc.CallOption) (*FileEvidenceInfoListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileEvidenceInfoListResponse)
	err := c.cc.Invoke(ctx, EvidenceService_QueryFileEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) QueryMemoryDumpEvidenceInfo(ctx context.Context, in *MemDumpEvidenceQueryRequest, opts ...grpc.CallOption) (*MemDumpEvidenceInfoListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MemDumpEvidenceInfoListResponse)
	err := c.cc.Invoke(ctx, EvidenceService_QueryMemoryDumpEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) QueryLogEvidenceInfo(ctx context.Context, in *LogEvidenceQueryRequest, opts ...grpc.CallOption) (*LogEvidenceInfoListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogEvidenceInfoListResponse)
	err := c.cc.Invoke(ctx, EvidenceService_QueryLogEvidenceInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetEvidenceResultInfo(ctx context.Context, in *GetEvidenceResultInfoRequest, opts ...grpc.CallOption) (*EvidenceResultInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EvidenceResultInfo)
	err := c.cc.Invoke(ctx, EvidenceService_GetEvidenceResultInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) BatchGetEvidenceResultInfo(ctx context.Context, in *BatchGetEvidenceResultInfoRequest, opts ...grpc.CallOption) (*BatchEvidenceResultInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchEvidenceResultInfoResponse)
	err := c.cc.Invoke(ctx, EvidenceService_BatchGetEvidenceResultInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetEvidenceStorageInfo(ctx context.Context, in *GetEvidenceStorageInfoRequest, opts ...grpc.CallOption) (*EvidenceStorage, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EvidenceStorage)
	err := c.cc.Invoke(ctx, EvidenceService_GetEvidenceStorageInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetObtainedEvidenceCount(ctx context.Context, in *GetObtainedEvidenceCountRequest, opts ...grpc.CallOption) (*ObtainEvidenceCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ObtainEvidenceCountResponse)
	err := c.cc.Invoke(ctx, EvidenceService_GetObtainedEvidenceCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) GetEvidenceSourceConfig(ctx context.Context, in *GetEvidenceSourceConfigRequest, opts ...grpc.CallOption) (*EvidenceSourceConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(EvidenceSourceConfigResponse)
	err := c.cc.Invoke(ctx, EvidenceService_GetEvidenceSourceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) AddEvidenceSourceConfig(ctx context.Context, in *EvidenceSourceConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EvidenceService_AddEvidenceSourceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) DeleteEvidenceSourceConfig(ctx context.Context, in *DeleteEvidenceSourceConfigRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EvidenceService_DeleteEvidenceSourceConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *evidenceServiceClient) UpdateAutoObtainEvidenceClueSubType(ctx context.Context, in *UpdateAutoObtainEvidenceClueSubTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, EvidenceService_UpdateAutoObtainEvidenceClueSubType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EvidenceServiceServer is the server API for EvidenceService service.
// All implementations must embed UnimplementedEvidenceServiceServer
// for forward compatibility.
//
// 证据管理服务
type EvidenceServiceServer interface {
	// 资源通知
	// 取证通道可用通知
	ObtainEvidenceChannelAvailableNotify(context.Context, *LimitPassNotifyRequest) (*ObtainEvidenceChannelAvailableNotifyResponse, error)
	// 取证完成通知
	ObtainEvidenceFinishedNotify(context.Context, *ObtainEvidenceFinishedNotifyRequest) (*emptypb.Empty, error)
	// illaoi 通知
	// 上报取证结果信息
	ReportObtainEvidenceInfo(context.Context, *ObtainEvidenceResponse) (*emptypb.Empty, error)
	// 添加取证任务
	// 添加线索取证任务
	AddClueObtainEvidenceTask(context.Context, *ClueObtainEvidenceTaskRequest) (*ClueObtainEvidenceTaskStatus, error)
	// 添加文件取证任务
	AddFileObtainEvidenceTask(context.Context, *FileObtainEvidenceTaskInfo) (*ObtainEvidenceTaskStatus, error)
	// 批量添加文件取证任务
	BatchAddFileObtainEvidenceTask(context.Context, *BatchFileObtainEvidenceTaskRequest) (*BatchFileObtainEvidenceResponse, error)
	// 添加网络取证任务
	AddNetObtainEvidenceTask(context.Context, *NetObtainEvidenceTaskInfo) (*ObtainEvidenceTaskStatus, error)
	// 添加日志取证任务
	AddLogObtainEvidenceTask(context.Context, *LogObtainEvidenceTaskRequest) (*ObtainEvidenceTaskStatus, error)
	// 重新提取证据
	RenewObtainEvidence(context.Context, *RenewObtainEvidenceRequest) (*ObtainEvidenceTaskStatus, error)
	// 查询取证任务
	// 根据线索获取证据信息
	GetClueEvidenceInfo(context.Context, *ClueEvidenceQueryRequest) (*EvidenceExtractionResultResponse, error)
	// 获取文件证据信息
	GetFileEvidenceInfo(context.Context, *GetFileEvidenceRequest) (*EvidenceExtractionResult, error)
	// 查询网络外联取证信息
	QueryNetEvidenceInfo(context.Context, *NetEvidenceQueryRequest) (*NetEvidenceInfoListResponse, error)
	// 查询文件取证信息
	QueryFileEvidenceInfo(context.Context, *FileEvidenceQueryRequest) (*FileEvidenceInfoListResponse, error)
	// 查询内存取证信息
	QueryMemoryDumpEvidenceInfo(context.Context, *MemDumpEvidenceQueryRequest) (*MemDumpEvidenceInfoListResponse, error)
	// 查询日志取证信息
	QueryLogEvidenceInfo(context.Context, *LogEvidenceQueryRequest) (*LogEvidenceInfoListResponse, error)
	// 获取取证结果信息
	GetEvidenceResultInfo(context.Context, *GetEvidenceResultInfoRequest) (*EvidenceResultInfo, error)
	// 批量获取取证结果信息
	BatchGetEvidenceResultInfo(context.Context, *BatchGetEvidenceResultInfoRequest) (*BatchEvidenceResultInfoResponse, error)
	// 获取取证存储信息
	GetEvidenceStorageInfo(context.Context, *GetEvidenceStorageInfoRequest) (*EvidenceStorage, error)
	// 获取已获取证据计数
	GetObtainedEvidenceCount(context.Context, *GetObtainedEvidenceCountRequest) (*ObtainEvidenceCountResponse, error)
	// 取证配置信息
	// 获取取证来源配置信息
	GetEvidenceSourceConfig(context.Context, *GetEvidenceSourceConfigRequest) (*EvidenceSourceConfigResponse, error)
	// 添加取证源配置
	AddEvidenceSourceConfig(context.Context, *EvidenceSourceConfigRequest) (*emptypb.Empty, error)
	// 删除取证源配置
	DeleteEvidenceSourceConfig(context.Context, *DeleteEvidenceSourceConfigRequest) (*emptypb.Empty, error)
	// 更新取证来源配置信息(自动取证线索子类型)
	UpdateAutoObtainEvidenceClueSubType(context.Context, *UpdateAutoObtainEvidenceClueSubTypeRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedEvidenceServiceServer()
}

// UnimplementedEvidenceServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEvidenceServiceServer struct{}

func (UnimplementedEvidenceServiceServer) ObtainEvidenceChannelAvailableNotify(context.Context, *LimitPassNotifyRequest) (*ObtainEvidenceChannelAvailableNotifyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ObtainEvidenceChannelAvailableNotify not implemented")
}
func (UnimplementedEvidenceServiceServer) ObtainEvidenceFinishedNotify(context.Context, *ObtainEvidenceFinishedNotifyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ObtainEvidenceFinishedNotify not implemented")
}
func (UnimplementedEvidenceServiceServer) ReportObtainEvidenceInfo(context.Context, *ObtainEvidenceResponse) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportObtainEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) AddClueObtainEvidenceTask(context.Context, *ClueObtainEvidenceTaskRequest) (*ClueObtainEvidenceTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddClueObtainEvidenceTask not implemented")
}
func (UnimplementedEvidenceServiceServer) AddFileObtainEvidenceTask(context.Context, *FileObtainEvidenceTaskInfo) (*ObtainEvidenceTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFileObtainEvidenceTask not implemented")
}
func (UnimplementedEvidenceServiceServer) BatchAddFileObtainEvidenceTask(context.Context, *BatchFileObtainEvidenceTaskRequest) (*BatchFileObtainEvidenceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchAddFileObtainEvidenceTask not implemented")
}
func (UnimplementedEvidenceServiceServer) AddNetObtainEvidenceTask(context.Context, *NetObtainEvidenceTaskInfo) (*ObtainEvidenceTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddNetObtainEvidenceTask not implemented")
}
func (UnimplementedEvidenceServiceServer) AddLogObtainEvidenceTask(context.Context, *LogObtainEvidenceTaskRequest) (*ObtainEvidenceTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddLogObtainEvidenceTask not implemented")
}
func (UnimplementedEvidenceServiceServer) RenewObtainEvidence(context.Context, *RenewObtainEvidenceRequest) (*ObtainEvidenceTaskStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewObtainEvidence not implemented")
}
func (UnimplementedEvidenceServiceServer) GetClueEvidenceInfo(context.Context, *ClueEvidenceQueryRequest) (*EvidenceExtractionResultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClueEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) GetFileEvidenceInfo(context.Context, *GetFileEvidenceRequest) (*EvidenceExtractionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) QueryNetEvidenceInfo(context.Context, *NetEvidenceQueryRequest) (*NetEvidenceInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryNetEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) QueryFileEvidenceInfo(context.Context, *FileEvidenceQueryRequest) (*FileEvidenceInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryFileEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) QueryMemoryDumpEvidenceInfo(context.Context, *MemDumpEvidenceQueryRequest) (*MemDumpEvidenceInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryMemoryDumpEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) QueryLogEvidenceInfo(context.Context, *LogEvidenceQueryRequest) (*LogEvidenceInfoListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryLogEvidenceInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) GetEvidenceResultInfo(context.Context, *GetEvidenceResultInfoRequest) (*EvidenceResultInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvidenceResultInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) BatchGetEvidenceResultInfo(context.Context, *BatchGetEvidenceResultInfoRequest) (*BatchEvidenceResultInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetEvidenceResultInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) GetEvidenceStorageInfo(context.Context, *GetEvidenceStorageInfoRequest) (*EvidenceStorage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvidenceStorageInfo not implemented")
}
func (UnimplementedEvidenceServiceServer) GetObtainedEvidenceCount(context.Context, *GetObtainedEvidenceCountRequest) (*ObtainEvidenceCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetObtainedEvidenceCount not implemented")
}
func (UnimplementedEvidenceServiceServer) GetEvidenceSourceConfig(context.Context, *GetEvidenceSourceConfigRequest) (*EvidenceSourceConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEvidenceSourceConfig not implemented")
}
func (UnimplementedEvidenceServiceServer) AddEvidenceSourceConfig(context.Context, *EvidenceSourceConfigRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddEvidenceSourceConfig not implemented")
}
func (UnimplementedEvidenceServiceServer) DeleteEvidenceSourceConfig(context.Context, *DeleteEvidenceSourceConfigRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEvidenceSourceConfig not implemented")
}
func (UnimplementedEvidenceServiceServer) UpdateAutoObtainEvidenceClueSubType(context.Context, *UpdateAutoObtainEvidenceClueSubTypeRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAutoObtainEvidenceClueSubType not implemented")
}
func (UnimplementedEvidenceServiceServer) mustEmbedUnimplementedEvidenceServiceServer() {}
func (UnimplementedEvidenceServiceServer) testEmbeddedByValue()                         {}

// UnsafeEvidenceServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EvidenceServiceServer will
// result in compilation errors.
type UnsafeEvidenceServiceServer interface {
	mustEmbedUnimplementedEvidenceServiceServer()
}

func RegisterEvidenceServiceServer(s grpc.ServiceRegistrar, srv EvidenceServiceServer) {
	// If the following call pancis, it indicates UnimplementedEvidenceServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EvidenceService_ServiceDesc, srv)
}

func _EvidenceService_ObtainEvidenceChannelAvailableNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LimitPassNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).ObtainEvidenceChannelAvailableNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_ObtainEvidenceChannelAvailableNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).ObtainEvidenceChannelAvailableNotify(ctx, req.(*LimitPassNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_ObtainEvidenceFinishedNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ObtainEvidenceFinishedNotifyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).ObtainEvidenceFinishedNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_ObtainEvidenceFinishedNotify_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).ObtainEvidenceFinishedNotify(ctx, req.(*ObtainEvidenceFinishedNotifyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_ReportObtainEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ObtainEvidenceResponse)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).ReportObtainEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_ReportObtainEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).ReportObtainEvidenceInfo(ctx, req.(*ObtainEvidenceResponse))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_AddClueObtainEvidenceTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClueObtainEvidenceTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).AddClueObtainEvidenceTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_AddClueObtainEvidenceTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).AddClueObtainEvidenceTask(ctx, req.(*ClueObtainEvidenceTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_AddFileObtainEvidenceTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileObtainEvidenceTaskInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).AddFileObtainEvidenceTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_AddFileObtainEvidenceTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).AddFileObtainEvidenceTask(ctx, req.(*FileObtainEvidenceTaskInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_BatchAddFileObtainEvidenceTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchFileObtainEvidenceTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).BatchAddFileObtainEvidenceTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_BatchAddFileObtainEvidenceTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).BatchAddFileObtainEvidenceTask(ctx, req.(*BatchFileObtainEvidenceTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_AddNetObtainEvidenceTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NetObtainEvidenceTaskInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).AddNetObtainEvidenceTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_AddNetObtainEvidenceTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).AddNetObtainEvidenceTask(ctx, req.(*NetObtainEvidenceTaskInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_AddLogObtainEvidenceTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogObtainEvidenceTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).AddLogObtainEvidenceTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_AddLogObtainEvidenceTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).AddLogObtainEvidenceTask(ctx, req.(*LogObtainEvidenceTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_RenewObtainEvidence_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewObtainEvidenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).RenewObtainEvidence(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_RenewObtainEvidence_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).RenewObtainEvidence(ctx, req.(*RenewObtainEvidenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetClueEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClueEvidenceQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetClueEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetClueEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetClueEvidenceInfo(ctx, req.(*ClueEvidenceQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetFileEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileEvidenceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetFileEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetFileEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetFileEvidenceInfo(ctx, req.(*GetFileEvidenceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_QueryNetEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NetEvidenceQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).QueryNetEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_QueryNetEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).QueryNetEvidenceInfo(ctx, req.(*NetEvidenceQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_QueryFileEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileEvidenceQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).QueryFileEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_QueryFileEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).QueryFileEvidenceInfo(ctx, req.(*FileEvidenceQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_QueryMemoryDumpEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MemDumpEvidenceQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).QueryMemoryDumpEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_QueryMemoryDumpEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).QueryMemoryDumpEvidenceInfo(ctx, req.(*MemDumpEvidenceQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_QueryLogEvidenceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogEvidenceQueryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).QueryLogEvidenceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_QueryLogEvidenceInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).QueryLogEvidenceInfo(ctx, req.(*LogEvidenceQueryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetEvidenceResultInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvidenceResultInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetEvidenceResultInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetEvidenceResultInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetEvidenceResultInfo(ctx, req.(*GetEvidenceResultInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_BatchGetEvidenceResultInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetEvidenceResultInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).BatchGetEvidenceResultInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_BatchGetEvidenceResultInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).BatchGetEvidenceResultInfo(ctx, req.(*BatchGetEvidenceResultInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetEvidenceStorageInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvidenceStorageInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetEvidenceStorageInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetEvidenceStorageInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetEvidenceStorageInfo(ctx, req.(*GetEvidenceStorageInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetObtainedEvidenceCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetObtainedEvidenceCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetObtainedEvidenceCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetObtainedEvidenceCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetObtainedEvidenceCount(ctx, req.(*GetObtainedEvidenceCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_GetEvidenceSourceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEvidenceSourceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).GetEvidenceSourceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_GetEvidenceSourceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).GetEvidenceSourceConfig(ctx, req.(*GetEvidenceSourceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_AddEvidenceSourceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvidenceSourceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).AddEvidenceSourceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_AddEvidenceSourceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).AddEvidenceSourceConfig(ctx, req.(*EvidenceSourceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_DeleteEvidenceSourceConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEvidenceSourceConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).DeleteEvidenceSourceConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_DeleteEvidenceSourceConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).DeleteEvidenceSourceConfig(ctx, req.(*DeleteEvidenceSourceConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EvidenceService_UpdateAutoObtainEvidenceClueSubType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAutoObtainEvidenceClueSubTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EvidenceServiceServer).UpdateAutoObtainEvidenceClueSubType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EvidenceService_UpdateAutoObtainEvidenceClueSubType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EvidenceServiceServer).UpdateAutoObtainEvidenceClueSubType(ctx, req.(*UpdateAutoObtainEvidenceClueSubTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EvidenceService_ServiceDesc is the grpc.ServiceDesc for EvidenceService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EvidenceService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.EvidenceService",
	HandlerType: (*EvidenceServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ObtainEvidenceChannelAvailableNotify",
			Handler:    _EvidenceService_ObtainEvidenceChannelAvailableNotify_Handler,
		},
		{
			MethodName: "ObtainEvidenceFinishedNotify",
			Handler:    _EvidenceService_ObtainEvidenceFinishedNotify_Handler,
		},
		{
			MethodName: "ReportObtainEvidenceInfo",
			Handler:    _EvidenceService_ReportObtainEvidenceInfo_Handler,
		},
		{
			MethodName: "AddClueObtainEvidenceTask",
			Handler:    _EvidenceService_AddClueObtainEvidenceTask_Handler,
		},
		{
			MethodName: "AddFileObtainEvidenceTask",
			Handler:    _EvidenceService_AddFileObtainEvidenceTask_Handler,
		},
		{
			MethodName: "BatchAddFileObtainEvidenceTask",
			Handler:    _EvidenceService_BatchAddFileObtainEvidenceTask_Handler,
		},
		{
			MethodName: "AddNetObtainEvidenceTask",
			Handler:    _EvidenceService_AddNetObtainEvidenceTask_Handler,
		},
		{
			MethodName: "AddLogObtainEvidenceTask",
			Handler:    _EvidenceService_AddLogObtainEvidenceTask_Handler,
		},
		{
			MethodName: "RenewObtainEvidence",
			Handler:    _EvidenceService_RenewObtainEvidence_Handler,
		},
		{
			MethodName: "GetClueEvidenceInfo",
			Handler:    _EvidenceService_GetClueEvidenceInfo_Handler,
		},
		{
			MethodName: "GetFileEvidenceInfo",
			Handler:    _EvidenceService_GetFileEvidenceInfo_Handler,
		},
		{
			MethodName: "QueryNetEvidenceInfo",
			Handler:    _EvidenceService_QueryNetEvidenceInfo_Handler,
		},
		{
			MethodName: "QueryFileEvidenceInfo",
			Handler:    _EvidenceService_QueryFileEvidenceInfo_Handler,
		},
		{
			MethodName: "QueryMemoryDumpEvidenceInfo",
			Handler:    _EvidenceService_QueryMemoryDumpEvidenceInfo_Handler,
		},
		{
			MethodName: "QueryLogEvidenceInfo",
			Handler:    _EvidenceService_QueryLogEvidenceInfo_Handler,
		},
		{
			MethodName: "GetEvidenceResultInfo",
			Handler:    _EvidenceService_GetEvidenceResultInfo_Handler,
		},
		{
			MethodName: "BatchGetEvidenceResultInfo",
			Handler:    _EvidenceService_BatchGetEvidenceResultInfo_Handler,
		},
		{
			MethodName: "GetEvidenceStorageInfo",
			Handler:    _EvidenceService_GetEvidenceStorageInfo_Handler,
		},
		{
			MethodName: "GetObtainedEvidenceCount",
			Handler:    _EvidenceService_GetObtainedEvidenceCount_Handler,
		},
		{
			MethodName: "GetEvidenceSourceConfig",
			Handler:    _EvidenceService_GetEvidenceSourceConfig_Handler,
		},
		{
			MethodName: "AddEvidenceSourceConfig",
			Handler:    _EvidenceService_AddEvidenceSourceConfig_Handler,
		},
		{
			MethodName: "DeleteEvidenceSourceConfig",
			Handler:    _EvidenceService_DeleteEvidenceSourceConfig_Handler,
		},
		{
			MethodName: "UpdateAutoObtainEvidenceClueSubType",
			Handler:    _EvidenceService_UpdateAutoObtainEvidenceClueSubType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conan/evidence.proto",
}
