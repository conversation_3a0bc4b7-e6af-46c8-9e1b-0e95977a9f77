// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/policy.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PolicyService_GetAttributeInfo_FullMethodName  = "/conan.PolicyService/GetAttributeInfo"
	PolicyService_SetIgnoreStatus_FullMethodName   = "/conan.PolicyService/SetIgnoreStatus"
	PolicyService_CreateBWPolicy_FullMethodName    = "/conan.PolicyService/CreateBWPolicy"
	PolicyService_DeleteBWPolicy_FullMethodName    = "/conan.PolicyService/DeleteBWPolicy"
	PolicyService_UpdateBWPolicy_FullMethodName    = "/conan.PolicyService/UpdateBWPolicy"
	PolicyService_SetRiskCategories_FullMethodName = "/conan.PolicyService/SetRiskCategories"
)

// PolicyServiceClient is the client API for PolicyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PolicyService 策略管理服务
// 负责处理黑白名单、忽略规则等策略相关操作
type PolicyServiceClient interface {
	// GetAttributeInfo 获取线索属性信息
	GetAttributeInfo(ctx context.Context, in *AttributeInfoRequest, opts ...grpc.CallOption) (*AttributeInfoResponse, error)
	// SetIgnoreStatus 设置线索忽略状态
	SetIgnoreStatus(ctx context.Context, in *IgnoreStatusRequest, opts ...grpc.CallOption) (*IgnoreStatusResponse, error)
	// CreateBWPolicy 创建白名单策略(黑名单暂时不支持)
	CreateBWPolicy(ctx context.Context, in *BWPolicy, opts ...grpc.CallOption) (*CreateBWPolicyResp, error)
	// DeleteBWPolicy 删除白名单策略(黑名单暂时不支持)
	DeleteBWPolicy(ctx context.Context, in *DeleteBWPolicyReq, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// UpdateBWPolicy 更新白名单策略(黑名单暂时不支持)
	UpdateBWPolicy(ctx context.Context, in *BWPolicy, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// SetRiskCategories create or update risk categories.
	SetRiskCategories(ctx context.Context, in *SetRiskCategoriesReq, opts ...grpc.CallOption) (*SetRiskCategoriesResp, error)
}

type policyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPolicyServiceClient(cc grpc.ClientConnInterface) PolicyServiceClient {
	return &policyServiceClient{cc}
}

func (c *policyServiceClient) GetAttributeInfo(ctx context.Context, in *AttributeInfoRequest, opts ...grpc.CallOption) (*AttributeInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AttributeInfoResponse)
	err := c.cc.Invoke(ctx, PolicyService_GetAttributeInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policyServiceClient) SetIgnoreStatus(ctx context.Context, in *IgnoreStatusRequest, opts ...grpc.CallOption) (*IgnoreStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IgnoreStatusResponse)
	err := c.cc.Invoke(ctx, PolicyService_SetIgnoreStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policyServiceClient) CreateBWPolicy(ctx context.Context, in *BWPolicy, opts ...grpc.CallOption) (*CreateBWPolicyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateBWPolicyResp)
	err := c.cc.Invoke(ctx, PolicyService_CreateBWPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policyServiceClient) DeleteBWPolicy(ctx context.Context, in *DeleteBWPolicyReq, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PolicyService_DeleteBWPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policyServiceClient) UpdateBWPolicy(ctx context.Context, in *BWPolicy, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PolicyService_UpdateBWPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *policyServiceClient) SetRiskCategories(ctx context.Context, in *SetRiskCategoriesReq, opts ...grpc.CallOption) (*SetRiskCategoriesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetRiskCategoriesResp)
	err := c.cc.Invoke(ctx, PolicyService_SetRiskCategories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PolicyServiceServer is the server API for PolicyService service.
// All implementations must embed UnimplementedPolicyServiceServer
// for forward compatibility.
//
// PolicyService 策略管理服务
// 负责处理黑白名单、忽略规则等策略相关操作
type PolicyServiceServer interface {
	// GetAttributeInfo 获取线索属性信息
	GetAttributeInfo(context.Context, *AttributeInfoRequest) (*AttributeInfoResponse, error)
	// SetIgnoreStatus 设置线索忽略状态
	SetIgnoreStatus(context.Context, *IgnoreStatusRequest) (*IgnoreStatusResponse, error)
	// CreateBWPolicy 创建白名单策略(黑名单暂时不支持)
	CreateBWPolicy(context.Context, *BWPolicy) (*CreateBWPolicyResp, error)
	// DeleteBWPolicy 删除白名单策略(黑名单暂时不支持)
	DeleteBWPolicy(context.Context, *DeleteBWPolicyReq) (*emptypb.Empty, error)
	// UpdateBWPolicy 更新白名单策略(黑名单暂时不支持)
	UpdateBWPolicy(context.Context, *BWPolicy) (*emptypb.Empty, error)
	// SetRiskCategories create or update risk categories.
	SetRiskCategories(context.Context, *SetRiskCategoriesReq) (*SetRiskCategoriesResp, error)
	mustEmbedUnimplementedPolicyServiceServer()
}

// UnimplementedPolicyServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPolicyServiceServer struct{}

func (UnimplementedPolicyServiceServer) GetAttributeInfo(context.Context, *AttributeInfoRequest) (*AttributeInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAttributeInfo not implemented")
}
func (UnimplementedPolicyServiceServer) SetIgnoreStatus(context.Context, *IgnoreStatusRequest) (*IgnoreStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetIgnoreStatus not implemented")
}
func (UnimplementedPolicyServiceServer) CreateBWPolicy(context.Context, *BWPolicy) (*CreateBWPolicyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBWPolicy not implemented")
}
func (UnimplementedPolicyServiceServer) DeleteBWPolicy(context.Context, *DeleteBWPolicyReq) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBWPolicy not implemented")
}
func (UnimplementedPolicyServiceServer) UpdateBWPolicy(context.Context, *BWPolicy) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBWPolicy not implemented")
}
func (UnimplementedPolicyServiceServer) SetRiskCategories(context.Context, *SetRiskCategoriesReq) (*SetRiskCategoriesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetRiskCategories not implemented")
}
func (UnimplementedPolicyServiceServer) mustEmbedUnimplementedPolicyServiceServer() {}
func (UnimplementedPolicyServiceServer) testEmbeddedByValue()                       {}

// UnsafePolicyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PolicyServiceServer will
// result in compilation errors.
type UnsafePolicyServiceServer interface {
	mustEmbedUnimplementedPolicyServiceServer()
}

func RegisterPolicyServiceServer(s grpc.ServiceRegistrar, srv PolicyServiceServer) {
	// If the following call pancis, it indicates UnimplementedPolicyServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PolicyService_ServiceDesc, srv)
}

func _PolicyService_GetAttributeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AttributeInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).GetAttributeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_GetAttributeInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).GetAttributeInfo(ctx, req.(*AttributeInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolicyService_SetIgnoreStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IgnoreStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).SetIgnoreStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_SetIgnoreStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).SetIgnoreStatus(ctx, req.(*IgnoreStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolicyService_CreateBWPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BWPolicy)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).CreateBWPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_CreateBWPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).CreateBWPolicy(ctx, req.(*BWPolicy))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolicyService_DeleteBWPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBWPolicyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).DeleteBWPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_DeleteBWPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).DeleteBWPolicy(ctx, req.(*DeleteBWPolicyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolicyService_UpdateBWPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BWPolicy)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).UpdateBWPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_UpdateBWPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).UpdateBWPolicy(ctx, req.(*BWPolicy))
	}
	return interceptor(ctx, in, info, handler)
}

func _PolicyService_SetRiskCategories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRiskCategoriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PolicyServiceServer).SetRiskCategories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PolicyService_SetRiskCategories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PolicyServiceServer).SetRiskCategories(ctx, req.(*SetRiskCategoriesReq))
	}
	return interceptor(ctx, in, info, handler)
}

// PolicyService_ServiceDesc is the grpc.ServiceDesc for PolicyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PolicyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.PolicyService",
	HandlerType: (*PolicyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAttributeInfo",
			Handler:    _PolicyService_GetAttributeInfo_Handler,
		},
		{
			MethodName: "SetIgnoreStatus",
			Handler:    _PolicyService_SetIgnoreStatus_Handler,
		},
		{
			MethodName: "CreateBWPolicy",
			Handler:    _PolicyService_CreateBWPolicy_Handler,
		},
		{
			MethodName: "DeleteBWPolicy",
			Handler:    _PolicyService_DeleteBWPolicy_Handler,
		},
		{
			MethodName: "UpdateBWPolicy",
			Handler:    _PolicyService_UpdateBWPolicy_Handler,
		},
		{
			MethodName: "SetRiskCategories",
			Handler:    _PolicyService_SetRiskCategories_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conan/policy.proto",
}
