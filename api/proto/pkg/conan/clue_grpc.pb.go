// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/clue.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ClueService_ListMemoryAttackClues_FullMethodName         = "/conan.ClueService/ListMemoryAttackClues"
	ClueService_ListFileThreatClues_FullMethodName           = "/conan.ClueService/ListFileThreatClues"
	ClueService_ListSystemAttackClues_FullMethodName         = "/conan.ClueService/ListSystemAttackClues"
	ClueService_ListIllegalOutreachClues_FullMethodName      = "/conan.ClueService/ListIllegalOutreachClues"
	ClueService_CountCluesBySha256_FullMethodName            = "/conan.ClueService/CountCluesBySha256"
	ClueService_ListTopMachineClueCounts_FullMethodName      = "/conan.ClueService/ListTopMachineClueCounts"
	ClueService_GetClueStats_FullMethodName                  = "/conan.ClueService/GetClueStats"
	ClueService_ListOutreachAffectedTerminals_FullMethodName = "/conan.ClueService/ListOutreachAffectedTerminals"
	ClueService_CountOutreachCluesByType_FullMethodName      = "/conan.ClueService/CountOutreachCluesByType"
	ClueService_ListClueTypeCounts_FullMethodName            = "/conan.ClueService/ListClueTypeCounts"
	ClueService_GetClueDetail_FullMethodName                 = "/conan.ClueService/GetClueDetail"
	ClueService_ListClueDetails_FullMethodName               = "/conan.ClueService/ListClueDetails"
	ClueService_ListAffectedOutreachTerminals_FullMethodName = "/conan.ClueService/ListAffectedOutreachTerminals"
	ClueService_ListAffectedFileTerminals_FullMethodName     = "/conan.ClueService/ListAffectedFileTerminals"
	ClueService_GetOutreachStatistics_FullMethodName         = "/conan.ClueService/GetOutreachStatistics"
	ClueService_GetThreatenFileStatistics_FullMethodName     = "/conan.ClueService/GetThreatenFileStatistics"
)

// ClueServiceClient is the client API for ClueService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ClueServiceClient interface {
	// ListMemoryAttackClues lists all memory attack clues
	ListMemoryAttackClues(ctx context.Context, in *ListMemoryAttackCluesReq, opts ...grpc.CallOption) (*ListMemoryAttackCluesResp, error)
	// ListFileThreatClues lists all file threat clues
	ListFileThreatClues(ctx context.Context, in *ListFileThreatCluesReq, opts ...grpc.CallOption) (*ListFileThreatCluesResp, error)
	// ListSystemAttackClues lists all system attack clues
	ListSystemAttackClues(ctx context.Context, in *ListSystemAttackCluesReq, opts ...grpc.CallOption) (*ListSystemAttackCluesResp, error)
	// ListIllegalOutreachClues lists all illegal outreach clues
	ListIllegalOutreachClues(ctx context.Context, in *ListIllegalOutreachCluesReq, opts ...grpc.CallOption) (*ListIllegalOutreachCluesResp, error)
	// CountCluesBySha256 counts the number of file threat clues corresponding to
	// the file sha256
	CountCluesBySha256(ctx context.Context, in *CountCluesBySha256Req, opts ...grpc.CallOption) (*CountCluesBySha256Resp, error)
	// ListTopMachineClueCounts lists the top N machine clue counts
	ListTopMachineClueCounts(ctx context.Context, in *ListTopMachineClueCountsReq, opts ...grpc.CallOption) (*ListTopMachineClueCountsResp, error)
	// GetClueStats gets the clue stats
	GetClueStats(ctx context.Context, in *GetClueStatsReq, opts ...grpc.CallOption) (*ClueStats, error)
	// ListOutreachAffectedTerminals lists the affected terminals of outreach
	ListOutreachAffectedTerminals(ctx context.Context, in *ListOutreachAffectedTerminalsReq, opts ...grpc.CallOption) (*ListOutreachAffectedTerminalsResp, error)
	// CountOutreachCluesByType counts the number of outreach clues grouped by
	// outreach type
	CountOutreachCluesByType(ctx context.Context, in *CountOutreachCluesByTypeReq, opts ...grpc.CallOption) (*CountOutreachCluesByTypeResp, error)
	// ListClueTypeCounts return the count of clues based on the input clue_type
	// When clue_type = 0, returns counts grouped by clue_type
	// When clue_type > 0, returns counts grouped by clue_sub_type for the
	// specified clue_type
	ListClueTypeCounts(ctx context.Context, in *ListClueTypeCountsReq, opts ...grpc.CallOption) (*ListClueTypeCountsResp, error)
	// GetClueDetail gets the details of a clue
	// When clue_type > 0, returns details of the specified clue_type
	GetClueDetail(ctx context.Context, in *GetClueDetailReq, opts ...grpc.CallOption) (*ClueDetail, error)
	// ListClueDetails lists the details of multiple clues
	// The interface is only used by file clue type.
	ListClueDetails(ctx context.Context, in *ListClueDetailsReq, opts ...grpc.CallOption) (*ListClueDetailsResp, error)
	// ListAffectedOutreachTerminals lists the affected terminals of outreach
	ListAffectedOutreachTerminals(ctx context.Context, in *ListAffectedOutreachTerminalsReq, opts ...grpc.CallOption) (*ListAffectedOutreachTerminalsResp, error)
	// ListAffectedFileTerminals lists the affected terminals of file threats
	ListAffectedFileTerminals(ctx context.Context, in *ListAffectedFileTerminalsReq, opts ...grpc.CallOption) (*ListAffectedFileTerminalsResp, error)
	// GetOutreachStatistics gets outreach statistics
	GetOutreachStatistics(ctx context.Context, in *GetOutreachStatisticsReq, opts ...grpc.CallOption) (*GetOutreachStatisticsResp, error)
	// GetThreatenFileStatistics gets threaten file statistics
	GetThreatenFileStatistics(ctx context.Context, in *GetThreatenFileStatisticsReq, opts ...grpc.CallOption) (*GetThreatenFileStatisticsResp, error)
}

type clueServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewClueServiceClient(cc grpc.ClientConnInterface) ClueServiceClient {
	return &clueServiceClient{cc}
}

func (c *clueServiceClient) ListMemoryAttackClues(ctx context.Context, in *ListMemoryAttackCluesReq, opts ...grpc.CallOption) (*ListMemoryAttackCluesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMemoryAttackCluesResp)
	err := c.cc.Invoke(ctx, ClueService_ListMemoryAttackClues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListFileThreatClues(ctx context.Context, in *ListFileThreatCluesReq, opts ...grpc.CallOption) (*ListFileThreatCluesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListFileThreatCluesResp)
	err := c.cc.Invoke(ctx, ClueService_ListFileThreatClues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListSystemAttackClues(ctx context.Context, in *ListSystemAttackCluesReq, opts ...grpc.CallOption) (*ListSystemAttackCluesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListSystemAttackCluesResp)
	err := c.cc.Invoke(ctx, ClueService_ListSystemAttackClues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListIllegalOutreachClues(ctx context.Context, in *ListIllegalOutreachCluesReq, opts ...grpc.CallOption) (*ListIllegalOutreachCluesResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListIllegalOutreachCluesResp)
	err := c.cc.Invoke(ctx, ClueService_ListIllegalOutreachClues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) CountCluesBySha256(ctx context.Context, in *CountCluesBySha256Req, opts ...grpc.CallOption) (*CountCluesBySha256Resp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountCluesBySha256Resp)
	err := c.cc.Invoke(ctx, ClueService_CountCluesBySha256_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListTopMachineClueCounts(ctx context.Context, in *ListTopMachineClueCountsReq, opts ...grpc.CallOption) (*ListTopMachineClueCountsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTopMachineClueCountsResp)
	err := c.cc.Invoke(ctx, ClueService_ListTopMachineClueCounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) GetClueStats(ctx context.Context, in *GetClueStatsReq, opts ...grpc.CallOption) (*ClueStats, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClueStats)
	err := c.cc.Invoke(ctx, ClueService_GetClueStats_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListOutreachAffectedTerminals(ctx context.Context, in *ListOutreachAffectedTerminalsReq, opts ...grpc.CallOption) (*ListOutreachAffectedTerminalsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListOutreachAffectedTerminalsResp)
	err := c.cc.Invoke(ctx, ClueService_ListOutreachAffectedTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) CountOutreachCluesByType(ctx context.Context, in *CountOutreachCluesByTypeReq, opts ...grpc.CallOption) (*CountOutreachCluesByTypeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountOutreachCluesByTypeResp)
	err := c.cc.Invoke(ctx, ClueService_CountOutreachCluesByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListClueTypeCounts(ctx context.Context, in *ListClueTypeCountsReq, opts ...grpc.CallOption) (*ListClueTypeCountsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClueTypeCountsResp)
	err := c.cc.Invoke(ctx, ClueService_ListClueTypeCounts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) GetClueDetail(ctx context.Context, in *GetClueDetailReq, opts ...grpc.CallOption) (*ClueDetail, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ClueDetail)
	err := c.cc.Invoke(ctx, ClueService_GetClueDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListClueDetails(ctx context.Context, in *ListClueDetailsReq, opts ...grpc.CallOption) (*ListClueDetailsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClueDetailsResp)
	err := c.cc.Invoke(ctx, ClueService_ListClueDetails_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListAffectedOutreachTerminals(ctx context.Context, in *ListAffectedOutreachTerminalsReq, opts ...grpc.CallOption) (*ListAffectedOutreachTerminalsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAffectedOutreachTerminalsResp)
	err := c.cc.Invoke(ctx, ClueService_ListAffectedOutreachTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) ListAffectedFileTerminals(ctx context.Context, in *ListAffectedFileTerminalsReq, opts ...grpc.CallOption) (*ListAffectedFileTerminalsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAffectedFileTerminalsResp)
	err := c.cc.Invoke(ctx, ClueService_ListAffectedFileTerminals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) GetOutreachStatistics(ctx context.Context, in *GetOutreachStatisticsReq, opts ...grpc.CallOption) (*GetOutreachStatisticsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOutreachStatisticsResp)
	err := c.cc.Invoke(ctx, ClueService_GetOutreachStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clueServiceClient) GetThreatenFileStatistics(ctx context.Context, in *GetThreatenFileStatisticsReq, opts ...grpc.CallOption) (*GetThreatenFileStatisticsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetThreatenFileStatisticsResp)
	err := c.cc.Invoke(ctx, ClueService_GetThreatenFileStatistics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClueServiceServer is the server API for ClueService service.
// All implementations must embed UnimplementedClueServiceServer
// for forward compatibility.
type ClueServiceServer interface {
	// ListMemoryAttackClues lists all memory attack clues
	ListMemoryAttackClues(context.Context, *ListMemoryAttackCluesReq) (*ListMemoryAttackCluesResp, error)
	// ListFileThreatClues lists all file threat clues
	ListFileThreatClues(context.Context, *ListFileThreatCluesReq) (*ListFileThreatCluesResp, error)
	// ListSystemAttackClues lists all system attack clues
	ListSystemAttackClues(context.Context, *ListSystemAttackCluesReq) (*ListSystemAttackCluesResp, error)
	// ListIllegalOutreachClues lists all illegal outreach clues
	ListIllegalOutreachClues(context.Context, *ListIllegalOutreachCluesReq) (*ListIllegalOutreachCluesResp, error)
	// CountCluesBySha256 counts the number of file threat clues corresponding to
	// the file sha256
	CountCluesBySha256(context.Context, *CountCluesBySha256Req) (*CountCluesBySha256Resp, error)
	// ListTopMachineClueCounts lists the top N machine clue counts
	ListTopMachineClueCounts(context.Context, *ListTopMachineClueCountsReq) (*ListTopMachineClueCountsResp, error)
	// GetClueStats gets the clue stats
	GetClueStats(context.Context, *GetClueStatsReq) (*ClueStats, error)
	// ListOutreachAffectedTerminals lists the affected terminals of outreach
	ListOutreachAffectedTerminals(context.Context, *ListOutreachAffectedTerminalsReq) (*ListOutreachAffectedTerminalsResp, error)
	// CountOutreachCluesByType counts the number of outreach clues grouped by
	// outreach type
	CountOutreachCluesByType(context.Context, *CountOutreachCluesByTypeReq) (*CountOutreachCluesByTypeResp, error)
	// ListClueTypeCounts return the count of clues based on the input clue_type
	// When clue_type = 0, returns counts grouped by clue_type
	// When clue_type > 0, returns counts grouped by clue_sub_type for the
	// specified clue_type
	ListClueTypeCounts(context.Context, *ListClueTypeCountsReq) (*ListClueTypeCountsResp, error)
	// GetClueDetail gets the details of a clue
	// When clue_type > 0, returns details of the specified clue_type
	GetClueDetail(context.Context, *GetClueDetailReq) (*ClueDetail, error)
	// ListClueDetails lists the details of multiple clues
	// The interface is only used by file clue type.
	ListClueDetails(context.Context, *ListClueDetailsReq) (*ListClueDetailsResp, error)
	// ListAffectedOutreachTerminals lists the affected terminals of outreach
	ListAffectedOutreachTerminals(context.Context, *ListAffectedOutreachTerminalsReq) (*ListAffectedOutreachTerminalsResp, error)
	// ListAffectedFileTerminals lists the affected terminals of file threats
	ListAffectedFileTerminals(context.Context, *ListAffectedFileTerminalsReq) (*ListAffectedFileTerminalsResp, error)
	// GetOutreachStatistics gets outreach statistics
	GetOutreachStatistics(context.Context, *GetOutreachStatisticsReq) (*GetOutreachStatisticsResp, error)
	// GetThreatenFileStatistics gets threaten file statistics
	GetThreatenFileStatistics(context.Context, *GetThreatenFileStatisticsReq) (*GetThreatenFileStatisticsResp, error)
	mustEmbedUnimplementedClueServiceServer()
}

// UnimplementedClueServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedClueServiceServer struct{}

func (UnimplementedClueServiceServer) ListMemoryAttackClues(context.Context, *ListMemoryAttackCluesReq) (*ListMemoryAttackCluesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMemoryAttackClues not implemented")
}
func (UnimplementedClueServiceServer) ListFileThreatClues(context.Context, *ListFileThreatCluesReq) (*ListFileThreatCluesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFileThreatClues not implemented")
}
func (UnimplementedClueServiceServer) ListSystemAttackClues(context.Context, *ListSystemAttackCluesReq) (*ListSystemAttackCluesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSystemAttackClues not implemented")
}
func (UnimplementedClueServiceServer) ListIllegalOutreachClues(context.Context, *ListIllegalOutreachCluesReq) (*ListIllegalOutreachCluesResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListIllegalOutreachClues not implemented")
}
func (UnimplementedClueServiceServer) CountCluesBySha256(context.Context, *CountCluesBySha256Req) (*CountCluesBySha256Resp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountCluesBySha256 not implemented")
}
func (UnimplementedClueServiceServer) ListTopMachineClueCounts(context.Context, *ListTopMachineClueCountsReq) (*ListTopMachineClueCountsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTopMachineClueCounts not implemented")
}
func (UnimplementedClueServiceServer) GetClueStats(context.Context, *GetClueStatsReq) (*ClueStats, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClueStats not implemented")
}
func (UnimplementedClueServiceServer) ListOutreachAffectedTerminals(context.Context, *ListOutreachAffectedTerminalsReq) (*ListOutreachAffectedTerminalsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOutreachAffectedTerminals not implemented")
}
func (UnimplementedClueServiceServer) CountOutreachCluesByType(context.Context, *CountOutreachCluesByTypeReq) (*CountOutreachCluesByTypeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountOutreachCluesByType not implemented")
}
func (UnimplementedClueServiceServer) ListClueTypeCounts(context.Context, *ListClueTypeCountsReq) (*ListClueTypeCountsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClueTypeCounts not implemented")
}
func (UnimplementedClueServiceServer) GetClueDetail(context.Context, *GetClueDetailReq) (*ClueDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClueDetail not implemented")
}
func (UnimplementedClueServiceServer) ListClueDetails(context.Context, *ListClueDetailsReq) (*ListClueDetailsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClueDetails not implemented")
}
func (UnimplementedClueServiceServer) ListAffectedOutreachTerminals(context.Context, *ListAffectedOutreachTerminalsReq) (*ListAffectedOutreachTerminalsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAffectedOutreachTerminals not implemented")
}
func (UnimplementedClueServiceServer) ListAffectedFileTerminals(context.Context, *ListAffectedFileTerminalsReq) (*ListAffectedFileTerminalsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAffectedFileTerminals not implemented")
}
func (UnimplementedClueServiceServer) GetOutreachStatistics(context.Context, *GetOutreachStatisticsReq) (*GetOutreachStatisticsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOutreachStatistics not implemented")
}
func (UnimplementedClueServiceServer) GetThreatenFileStatistics(context.Context, *GetThreatenFileStatisticsReq) (*GetThreatenFileStatisticsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetThreatenFileStatistics not implemented")
}
func (UnimplementedClueServiceServer) mustEmbedUnimplementedClueServiceServer() {}
func (UnimplementedClueServiceServer) testEmbeddedByValue()                     {}

// UnsafeClueServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ClueServiceServer will
// result in compilation errors.
type UnsafeClueServiceServer interface {
	mustEmbedUnimplementedClueServiceServer()
}

func RegisterClueServiceServer(s grpc.ServiceRegistrar, srv ClueServiceServer) {
	// If the following call pancis, it indicates UnimplementedClueServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ClueService_ServiceDesc, srv)
}

func _ClueService_ListMemoryAttackClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMemoryAttackCluesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListMemoryAttackClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListMemoryAttackClues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListMemoryAttackClues(ctx, req.(*ListMemoryAttackCluesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListFileThreatClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListFileThreatCluesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListFileThreatClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListFileThreatClues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListFileThreatClues(ctx, req.(*ListFileThreatCluesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListSystemAttackClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSystemAttackCluesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListSystemAttackClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListSystemAttackClues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListSystemAttackClues(ctx, req.(*ListSystemAttackCluesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListIllegalOutreachClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListIllegalOutreachCluesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListIllegalOutreachClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListIllegalOutreachClues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListIllegalOutreachClues(ctx, req.(*ListIllegalOutreachCluesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_CountCluesBySha256_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountCluesBySha256Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).CountCluesBySha256(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_CountCluesBySha256_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).CountCluesBySha256(ctx, req.(*CountCluesBySha256Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListTopMachineClueCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTopMachineClueCountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListTopMachineClueCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListTopMachineClueCounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListTopMachineClueCounts(ctx, req.(*ListTopMachineClueCountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_GetClueStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClueStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).GetClueStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_GetClueStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).GetClueStats(ctx, req.(*GetClueStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListOutreachAffectedTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOutreachAffectedTerminalsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListOutreachAffectedTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListOutreachAffectedTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListOutreachAffectedTerminals(ctx, req.(*ListOutreachAffectedTerminalsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_CountOutreachCluesByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountOutreachCluesByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).CountOutreachCluesByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_CountOutreachCluesByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).CountOutreachCluesByType(ctx, req.(*CountOutreachCluesByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListClueTypeCounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClueTypeCountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListClueTypeCounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListClueTypeCounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListClueTypeCounts(ctx, req.(*ListClueTypeCountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_GetClueDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClueDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).GetClueDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_GetClueDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).GetClueDetail(ctx, req.(*GetClueDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListClueDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClueDetailsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListClueDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListClueDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListClueDetails(ctx, req.(*ListClueDetailsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListAffectedOutreachTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAffectedOutreachTerminalsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListAffectedOutreachTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListAffectedOutreachTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListAffectedOutreachTerminals(ctx, req.(*ListAffectedOutreachTerminalsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_ListAffectedFileTerminals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAffectedFileTerminalsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).ListAffectedFileTerminals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_ListAffectedFileTerminals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).ListAffectedFileTerminals(ctx, req.(*ListAffectedFileTerminalsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_GetOutreachStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOutreachStatisticsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).GetOutreachStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_GetOutreachStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).GetOutreachStatistics(ctx, req.(*GetOutreachStatisticsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClueService_GetThreatenFileStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetThreatenFileStatisticsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClueServiceServer).GetThreatenFileStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClueService_GetThreatenFileStatistics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClueServiceServer).GetThreatenFileStatistics(ctx, req.(*GetThreatenFileStatisticsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ClueService_ServiceDesc is the grpc.ServiceDesc for ClueService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ClueService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.ClueService",
	HandlerType: (*ClueServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListMemoryAttackClues",
			Handler:    _ClueService_ListMemoryAttackClues_Handler,
		},
		{
			MethodName: "ListFileThreatClues",
			Handler:    _ClueService_ListFileThreatClues_Handler,
		},
		{
			MethodName: "ListSystemAttackClues",
			Handler:    _ClueService_ListSystemAttackClues_Handler,
		},
		{
			MethodName: "ListIllegalOutreachClues",
			Handler:    _ClueService_ListIllegalOutreachClues_Handler,
		},
		{
			MethodName: "CountCluesBySha256",
			Handler:    _ClueService_CountCluesBySha256_Handler,
		},
		{
			MethodName: "ListTopMachineClueCounts",
			Handler:    _ClueService_ListTopMachineClueCounts_Handler,
		},
		{
			MethodName: "GetClueStats",
			Handler:    _ClueService_GetClueStats_Handler,
		},
		{
			MethodName: "ListOutreachAffectedTerminals",
			Handler:    _ClueService_ListOutreachAffectedTerminals_Handler,
		},
		{
			MethodName: "CountOutreachCluesByType",
			Handler:    _ClueService_CountOutreachCluesByType_Handler,
		},
		{
			MethodName: "ListClueTypeCounts",
			Handler:    _ClueService_ListClueTypeCounts_Handler,
		},
		{
			MethodName: "GetClueDetail",
			Handler:    _ClueService_GetClueDetail_Handler,
		},
		{
			MethodName: "ListClueDetails",
			Handler:    _ClueService_ListClueDetails_Handler,
		},
		{
			MethodName: "ListAffectedOutreachTerminals",
			Handler:    _ClueService_ListAffectedOutreachTerminals_Handler,
		},
		{
			MethodName: "ListAffectedFileTerminals",
			Handler:    _ClueService_ListAffectedFileTerminals_Handler,
		},
		{
			MethodName: "GetOutreachStatistics",
			Handler:    _ClueService_GetOutreachStatistics_Handler,
		},
		{
			MethodName: "GetThreatenFileStatistics",
			Handler:    _ClueService_GetThreatenFileStatistics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conan/clue.proto",
}
