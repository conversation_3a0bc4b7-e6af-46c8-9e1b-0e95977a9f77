// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/evidence.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ClueObtainEvidenceTaskStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClueObtainEvidenceTaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueObtainEvidenceTaskStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClueObtainEvidenceTaskStatusMultiError, or nil if none found.
func (m *ClueObtainEvidenceTaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueObtainEvidenceTaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EvidenceType

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClueObtainEvidenceTaskStatusValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClueObtainEvidenceTaskStatusValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClueObtainEvidenceTaskStatusValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ClueObtainEvidenceTaskStatusMultiError(errors)
	}

	return nil
}

// ClueObtainEvidenceTaskStatusMultiError is an error wrapping multiple
// validation errors returned by ClueObtainEvidenceTaskStatus.ValidateAll() if
// the designated constraints aren't met.
type ClueObtainEvidenceTaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueObtainEvidenceTaskStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueObtainEvidenceTaskStatusMultiError) AllErrors() []error { return m }

// ClueObtainEvidenceTaskStatusValidationError is the validation error returned
// by ClueObtainEvidenceTaskStatus.Validate if the designated constraints
// aren't met.
type ClueObtainEvidenceTaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueObtainEvidenceTaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueObtainEvidenceTaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueObtainEvidenceTaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueObtainEvidenceTaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueObtainEvidenceTaskStatusValidationError) ErrorName() string {
	return "ClueObtainEvidenceTaskStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ClueObtainEvidenceTaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueObtainEvidenceTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueObtainEvidenceTaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueObtainEvidenceTaskStatusValidationError{}

// Validate checks the field values on LimitPassNotifyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LimitPassNotifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LimitPassNotifyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LimitPassNotifyRequestMultiError, or nil if none found.
func (m *LimitPassNotifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LimitPassNotifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Channel

	// no validation rules for Category

	// no validation rules for TempToken

	// no validation rules for Identifier

	// no validation rules for Content

	if len(errors) > 0 {
		return LimitPassNotifyRequestMultiError(errors)
	}

	return nil
}

// LimitPassNotifyRequestMultiError is an error wrapping multiple validation
// errors returned by LimitPassNotifyRequest.ValidateAll() if the designated
// constraints aren't met.
type LimitPassNotifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LimitPassNotifyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LimitPassNotifyRequestMultiError) AllErrors() []error { return m }

// LimitPassNotifyRequestValidationError is the validation error returned by
// LimitPassNotifyRequest.Validate if the designated constraints aren't met.
type LimitPassNotifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LimitPassNotifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LimitPassNotifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LimitPassNotifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LimitPassNotifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LimitPassNotifyRequestValidationError) ErrorName() string {
	return "LimitPassNotifyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LimitPassNotifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLimitPassNotifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LimitPassNotifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LimitPassNotifyRequestValidationError{}

// Validate checks the field values on
// ObtainEvidenceChannelAvailableNotifyResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceChannelAvailableNotifyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ObtainEvidenceChannelAvailableNotifyResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ObtainEvidenceChannelAvailableNotifyResponseMultiError, or nil if none found.
func (m *ObtainEvidenceChannelAvailableNotifyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceChannelAvailableNotifyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if len(errors) > 0 {
		return ObtainEvidenceChannelAvailableNotifyResponseMultiError(errors)
	}

	return nil
}

// ObtainEvidenceChannelAvailableNotifyResponseMultiError is an error wrapping
// multiple validation errors returned by
// ObtainEvidenceChannelAvailableNotifyResponse.ValidateAll() if the
// designated constraints aren't met.
type ObtainEvidenceChannelAvailableNotifyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceChannelAvailableNotifyResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceChannelAvailableNotifyResponseMultiError) AllErrors() []error { return m }

// ObtainEvidenceChannelAvailableNotifyResponseValidationError is the
// validation error returned by
// ObtainEvidenceChannelAvailableNotifyResponse.Validate if the designated
// constraints aren't met.
type ObtainEvidenceChannelAvailableNotifyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) ErrorName() string {
	return "ObtainEvidenceChannelAvailableNotifyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceChannelAvailableNotifyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceChannelAvailableNotifyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceChannelAvailableNotifyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceChannelAvailableNotifyResponseValidationError{}

// Validate checks the field values on ObtainEvidenceTaskStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceTaskStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceTaskStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObtainEvidenceTaskStatusMultiError, or nil if none found.
func (m *ObtainEvidenceTaskStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceTaskStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Status

	// no validation rules for Existed

	if len(errors) > 0 {
		return ObtainEvidenceTaskStatusMultiError(errors)
	}

	return nil
}

// ObtainEvidenceTaskStatusMultiError is an error wrapping multiple validation
// errors returned by ObtainEvidenceTaskStatus.ValidateAll() if the designated
// constraints aren't met.
type ObtainEvidenceTaskStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceTaskStatusMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceTaskStatusMultiError) AllErrors() []error { return m }

// ObtainEvidenceTaskStatusValidationError is the validation error returned by
// ObtainEvidenceTaskStatus.Validate if the designated constraints aren't met.
type ObtainEvidenceTaskStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceTaskStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceTaskStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceTaskStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceTaskStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceTaskStatusValidationError) ErrorName() string {
	return "ObtainEvidenceTaskStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceTaskStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceTaskStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceTaskStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceTaskStatusValidationError{}

// Validate checks the field values on UploadInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UploadInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UploadInfoMultiError, or
// nil if none found.
func (m *UploadInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BucketName

	// no validation rules for ObjectName

	// no validation rules for Md5

	// no validation rules for Size

	if len(errors) > 0 {
		return UploadInfoMultiError(errors)
	}

	return nil
}

// UploadInfoMultiError is an error wrapping multiple validation errors
// returned by UploadInfo.ValidateAll() if the designated constraints aren't met.
type UploadInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadInfoMultiError) AllErrors() []error { return m }

// UploadInfoValidationError is the validation error returned by
// UploadInfo.Validate if the designated constraints aren't met.
type UploadInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadInfoValidationError) ErrorName() string { return "UploadInfoValidationError" }

// Error satisfies the builtin error interface
func (e UploadInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadInfoValidationError{}

// Validate checks the field values on ObtainEvidenceFinishedNotifyRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ObtainEvidenceFinishedNotifyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceFinishedNotifyRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ObtainEvidenceFinishedNotifyRequestMultiError, or nil if none found.
func (m *ObtainEvidenceFinishedNotifyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceFinishedNotifyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUploadInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ObtainEvidenceFinishedNotifyRequestValidationError{
					field:  "UploadInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ObtainEvidenceFinishedNotifyRequestValidationError{
					field:  "UploadInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUploadInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ObtainEvidenceFinishedNotifyRequestValidationError{
				field:  "UploadInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawFilename

	// no validation rules for TaskId

	// no validation rules for Identifier

	// no validation rules for Status

	// no validation rules for FormParams

	if len(errors) > 0 {
		return ObtainEvidenceFinishedNotifyRequestMultiError(errors)
	}

	return nil
}

// ObtainEvidenceFinishedNotifyRequestMultiError is an error wrapping multiple
// validation errors returned by
// ObtainEvidenceFinishedNotifyRequest.ValidateAll() if the designated
// constraints aren't met.
type ObtainEvidenceFinishedNotifyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceFinishedNotifyRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceFinishedNotifyRequestMultiError) AllErrors() []error { return m }

// ObtainEvidenceFinishedNotifyRequestValidationError is the validation error
// returned by ObtainEvidenceFinishedNotifyRequest.Validate if the designated
// constraints aren't met.
type ObtainEvidenceFinishedNotifyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceFinishedNotifyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceFinishedNotifyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceFinishedNotifyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceFinishedNotifyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceFinishedNotifyRequestValidationError) ErrorName() string {
	return "ObtainEvidenceFinishedNotifyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceFinishedNotifyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceFinishedNotifyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceFinishedNotifyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceFinishedNotifyRequestValidationError{}

// Validate checks the field values on ObtainEvidenceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObtainEvidenceResponseMultiError, or nil if none found.
func (m *ObtainEvidenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Data

	if len(errors) > 0 {
		return ObtainEvidenceResponseMultiError(errors)
	}

	return nil
}

// ObtainEvidenceResponseMultiError is an error wrapping multiple validation
// errors returned by ObtainEvidenceResponse.ValidateAll() if the designated
// constraints aren't met.
type ObtainEvidenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceResponseMultiError) AllErrors() []error { return m }

// ObtainEvidenceResponseValidationError is the validation error returned by
// ObtainEvidenceResponse.Validate if the designated constraints aren't met.
type ObtainEvidenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceResponseValidationError) ErrorName() string {
	return "ObtainEvidenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceResponseValidationError{}

// Validate checks the field values on EvidenceSourceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceSourceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceSourceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceSourceInfoMultiError, or nil if none found.
func (m *EvidenceSourceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceSourceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceType

	// no validation rules for SourceId

	// no validation rules for Identifier

	if len(errors) > 0 {
		return EvidenceSourceInfoMultiError(errors)
	}

	return nil
}

// EvidenceSourceInfoMultiError is an error wrapping multiple validation errors
// returned by EvidenceSourceInfo.ValidateAll() if the designated constraints
// aren't met.
type EvidenceSourceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceSourceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceSourceInfoMultiError) AllErrors() []error { return m }

// EvidenceSourceInfoValidationError is the validation error returned by
// EvidenceSourceInfo.Validate if the designated constraints aren't met.
type EvidenceSourceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceSourceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceSourceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceSourceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceSourceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceSourceInfoValidationError) ErrorName() string {
	return "EvidenceSourceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceSourceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceSourceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceSourceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceSourceInfoValidationError{}

// Validate checks the field values on HostClueInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HostClueInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HostClueInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HostClueInfoMultiError, or
// nil if none found.
func (m *HostClueInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *HostClueInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for ClueKey

	if len(errors) > 0 {
		return HostClueInfoMultiError(errors)
	}

	return nil
}

// HostClueInfoMultiError is an error wrapping multiple validation errors
// returned by HostClueInfo.ValidateAll() if the designated constraints aren't met.
type HostClueInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HostClueInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HostClueInfoMultiError) AllErrors() []error { return m }

// HostClueInfoValidationError is the validation error returned by
// HostClueInfo.Validate if the designated constraints aren't met.
type HostClueInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HostClueInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HostClueInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HostClueInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HostClueInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HostClueInfoValidationError) ErrorName() string { return "HostClueInfoValidationError" }

// Error satisfies the builtin error interface
func (e HostClueInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHostClueInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HostClueInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HostClueInfoValidationError{}

// Validate checks the field values on ClueObtainEvidenceTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClueObtainEvidenceTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueObtainEvidenceTaskRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ClueObtainEvidenceTaskRequestMultiError, or nil if none found.
func (m *ClueObtainEvidenceTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueObtainEvidenceTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSourceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClueObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClueObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClueObtainEvidenceTaskRequestValidationError{
				field:  "SourceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClueId

	// no validation rules for ClueType

	// no validation rules for EvidenceType

	if len(errors) > 0 {
		return ClueObtainEvidenceTaskRequestMultiError(errors)
	}

	return nil
}

// ClueObtainEvidenceTaskRequestMultiError is an error wrapping multiple
// validation errors returned by ClueObtainEvidenceTaskRequest.ValidateAll()
// if the designated constraints aren't met.
type ClueObtainEvidenceTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueObtainEvidenceTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueObtainEvidenceTaskRequestMultiError) AllErrors() []error { return m }

// ClueObtainEvidenceTaskRequestValidationError is the validation error
// returned by ClueObtainEvidenceTaskRequest.Validate if the designated
// constraints aren't met.
type ClueObtainEvidenceTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueObtainEvidenceTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueObtainEvidenceTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueObtainEvidenceTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueObtainEvidenceTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueObtainEvidenceTaskRequestValidationError) ErrorName() string {
	return "ClueObtainEvidenceTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ClueObtainEvidenceTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueObtainEvidenceTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueObtainEvidenceTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueObtainEvidenceTaskRequestValidationError{}

// Validate checks the field values on EvicenceFileInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EvicenceFileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvicenceFileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvicenceFileInfoMultiError, or nil if none found.
func (m *EvicenceFileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvicenceFileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for RawFilesize

	// no validation rules for FileMd5

	// no validation rules for FileSha1

	// no validation rules for FileSha256

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	if len(errors) > 0 {
		return EvicenceFileInfoMultiError(errors)
	}

	return nil
}

// EvicenceFileInfoMultiError is an error wrapping multiple validation errors
// returned by EvicenceFileInfo.ValidateAll() if the designated constraints
// aren't met.
type EvicenceFileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvicenceFileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvicenceFileInfoMultiError) AllErrors() []error { return m }

// EvicenceFileInfoValidationError is the validation error returned by
// EvicenceFileInfo.Validate if the designated constraints aren't met.
type EvicenceFileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvicenceFileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvicenceFileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvicenceFileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvicenceFileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvicenceFileInfoValidationError) ErrorName() string { return "EvicenceFileInfoValidationError" }

// Error satisfies the builtin error interface
func (e EvicenceFileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvicenceFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvicenceFileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvicenceFileInfoValidationError{}

// Validate checks the field values on EvidenceInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EvidenceInfoMultiError, or
// nil if none found.
func (m *EvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EvidenceType

	// no validation rules for UniqueFlag

	// no validation rules for EvidenceSize

	// no validation rules for Filepath

	// no validation rules for Filename

	if all {
		switch v := interface{}(m.GetFileDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceInfoValidationError{
					field:  "FileDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceInfoValidationError{
					field:  "FileDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceInfoValidationError{
				field:  "FileDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EvidenceInfoMultiError(errors)
	}

	return nil
}

// EvidenceInfoMultiError is an error wrapping multiple validation errors
// returned by EvidenceInfo.ValidateAll() if the designated constraints aren't met.
type EvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceInfoMultiError) AllErrors() []error { return m }

// EvidenceInfoValidationError is the validation error returned by
// EvidenceInfo.Validate if the designated constraints aren't met.
type EvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceInfoValidationError) ErrorName() string { return "EvidenceInfoValidationError" }

// Error satisfies the builtin error interface
func (e EvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceInfoValidationError{}

// Validate checks the field values on FileDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileDetailMultiError, or
// nil if none found.
func (m *FileDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *FileDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RawFilesize

	// no validation rules for Md5

	// no validation rules for Sha256

	// no validation rules for Atime

	// no validation rules for Mtime

	// no validation rules for Ctime

	if len(errors) > 0 {
		return FileDetailMultiError(errors)
	}

	return nil
}

// FileDetailMultiError is an error wrapping multiple validation errors
// returned by FileDetail.ValidateAll() if the designated constraints aren't met.
type FileDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileDetailMultiError) AllErrors() []error { return m }

// FileDetailValidationError is the validation error returned by
// FileDetail.Validate if the designated constraints aren't met.
type FileDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileDetailValidationError) ErrorName() string { return "FileDetailValidationError" }

// Error satisfies the builtin error interface
func (e FileDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileDetailValidationError{}

// Validate checks the field values on FileObtainEvidenceTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileObtainEvidenceTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileObtainEvidenceTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileObtainEvidenceTaskInfoMultiError, or nil if none found.
func (m *FileObtainEvidenceTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileObtainEvidenceTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHostClueInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "HostClueInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "HostClueInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHostClueInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileObtainEvidenceTaskInfoValidationError{
				field:  "HostClueInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileObtainEvidenceTaskInfoValidationError{
				field:  "SourceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SearchKey

	// no validation rules for Timestamp

	// no validation rules for ExistedRaiseError

	if all {
		switch v := interface{}(m.GetEvidenceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "EvidenceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileObtainEvidenceTaskInfoValidationError{
					field:  "EvidenceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvidenceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileObtainEvidenceTaskInfoValidationError{
				field:  "EvidenceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileObtainEvidenceTaskInfoMultiError(errors)
	}

	return nil
}

// FileObtainEvidenceTaskInfoMultiError is an error wrapping multiple
// validation errors returned by FileObtainEvidenceTaskInfo.ValidateAll() if
// the designated constraints aren't met.
type FileObtainEvidenceTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileObtainEvidenceTaskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileObtainEvidenceTaskInfoMultiError) AllErrors() []error { return m }

// FileObtainEvidenceTaskInfoValidationError is the validation error returned
// by FileObtainEvidenceTaskInfo.Validate if the designated constraints aren't met.
type FileObtainEvidenceTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileObtainEvidenceTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileObtainEvidenceTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileObtainEvidenceTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileObtainEvidenceTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileObtainEvidenceTaskInfoValidationError) ErrorName() string {
	return "FileObtainEvidenceTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FileObtainEvidenceTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileObtainEvidenceTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileObtainEvidenceTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileObtainEvidenceTaskInfoValidationError{}

// Validate checks the field values on BatchFileObtainEvidenceTaskRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchFileObtainEvidenceTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchFileObtainEvidenceTaskRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchFileObtainEvidenceTaskRequestMultiError, or nil if none found.
func (m *BatchFileObtainEvidenceTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchFileObtainEvidenceTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSourceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchFileObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchFileObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchFileObtainEvidenceTaskRequestValidationError{
				field:  "SourceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FileObjectName

	if len(errors) > 0 {
		return BatchFileObtainEvidenceTaskRequestMultiError(errors)
	}

	return nil
}

// BatchFileObtainEvidenceTaskRequestMultiError is an error wrapping multiple
// validation errors returned by
// BatchFileObtainEvidenceTaskRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchFileObtainEvidenceTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchFileObtainEvidenceTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchFileObtainEvidenceTaskRequestMultiError) AllErrors() []error { return m }

// BatchFileObtainEvidenceTaskRequestValidationError is the validation error
// returned by BatchFileObtainEvidenceTaskRequest.Validate if the designated
// constraints aren't met.
type BatchFileObtainEvidenceTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchFileObtainEvidenceTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchFileObtainEvidenceTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchFileObtainEvidenceTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchFileObtainEvidenceTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchFileObtainEvidenceTaskRequestValidationError) ErrorName() string {
	return "BatchFileObtainEvidenceTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchFileObtainEvidenceTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchFileObtainEvidenceTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchFileObtainEvidenceTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchFileObtainEvidenceTaskRequestValidationError{}

// Validate checks the field values on BatchFileObtainEvidenceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchFileObtainEvidenceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchFileObtainEvidenceResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchFileObtainEvidenceResponseMultiError, or nil if none found.
func (m *BatchFileObtainEvidenceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchFileObtainEvidenceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchFileObtainEvidenceResponseMultiError(errors)
	}

	return nil
}

// BatchFileObtainEvidenceResponseMultiError is an error wrapping multiple
// validation errors returned by BatchFileObtainEvidenceResponse.ValidateAll()
// if the designated constraints aren't met.
type BatchFileObtainEvidenceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchFileObtainEvidenceResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchFileObtainEvidenceResponseMultiError) AllErrors() []error { return m }

// BatchFileObtainEvidenceResponseValidationError is the validation error
// returned by BatchFileObtainEvidenceResponse.Validate if the designated
// constraints aren't met.
type BatchFileObtainEvidenceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchFileObtainEvidenceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchFileObtainEvidenceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchFileObtainEvidenceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchFileObtainEvidenceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchFileObtainEvidenceResponseValidationError) ErrorName() string {
	return "BatchFileObtainEvidenceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchFileObtainEvidenceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchFileObtainEvidenceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchFileObtainEvidenceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchFileObtainEvidenceResponseValidationError{}

// Validate checks the field values on NetConnectionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NetConnectionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetConnectionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetConnectionInfoMultiError, or nil if none found.
func (m *NetConnectionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetConnectionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DestIp

	// no validation rules for DestPort

	// no validation rules for SrcIp

	// no validation rules for SrcPort

	// no validation rules for Protocol

	if len(errors) > 0 {
		return NetConnectionInfoMultiError(errors)
	}

	return nil
}

// NetConnectionInfoMultiError is an error wrapping multiple validation errors
// returned by NetConnectionInfo.ValidateAll() if the designated constraints
// aren't met.
type NetConnectionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetConnectionInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetConnectionInfoMultiError) AllErrors() []error { return m }

// NetConnectionInfoValidationError is the validation error returned by
// NetConnectionInfo.Validate if the designated constraints aren't met.
type NetConnectionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetConnectionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetConnectionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetConnectionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetConnectionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetConnectionInfoValidationError) ErrorName() string {
	return "NetConnectionInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetConnectionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetConnectionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetConnectionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetConnectionInfoValidationError{}

// Validate checks the field values on EvidenceOutReachInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceOutReachInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceOutReachInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceOutReachInfoMultiError, or nil if none found.
func (m *EvidenceOutReachInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceOutReachInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConnectionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceOutReachInfoValidationError{
					field:  "ConnectionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceOutReachInfoValidationError{
					field:  "ConnectionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConnectionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceOutReachInfoValidationError{
				field:  "ConnectionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Timestamp

	if len(errors) > 0 {
		return EvidenceOutReachInfoMultiError(errors)
	}

	return nil
}

// EvidenceOutReachInfoMultiError is an error wrapping multiple validation
// errors returned by EvidenceOutReachInfo.ValidateAll() if the designated
// constraints aren't met.
type EvidenceOutReachInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceOutReachInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceOutReachInfoMultiError) AllErrors() []error { return m }

// EvidenceOutReachInfoValidationError is the validation error returned by
// EvidenceOutReachInfo.Validate if the designated constraints aren't met.
type EvidenceOutReachInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceOutReachInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceOutReachInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceOutReachInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceOutReachInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceOutReachInfoValidationError) ErrorName() string {
	return "EvidenceOutReachInfoValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceOutReachInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceOutReachInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceOutReachInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceOutReachInfoValidationError{}

// Validate checks the field values on NetObtainEvidenceTaskInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetObtainEvidenceTaskInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetObtainEvidenceTaskInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetObtainEvidenceTaskInfoMultiError, or nil if none found.
func (m *NetObtainEvidenceTaskInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetObtainEvidenceTaskInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHostClueInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "HostClueInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "HostClueInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHostClueInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetObtainEvidenceTaskInfoValidationError{
				field:  "HostClueInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSourceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetObtainEvidenceTaskInfoValidationError{
				field:  "SourceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutreachInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "OutreachInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetObtainEvidenceTaskInfoValidationError{
					field:  "OutreachInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutreachInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetObtainEvidenceTaskInfoValidationError{
				field:  "OutreachInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExistedRaiseError

	if len(errors) > 0 {
		return NetObtainEvidenceTaskInfoMultiError(errors)
	}

	return nil
}

// NetObtainEvidenceTaskInfoMultiError is an error wrapping multiple validation
// errors returned by NetObtainEvidenceTaskInfo.ValidateAll() if the
// designated constraints aren't met.
type NetObtainEvidenceTaskInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetObtainEvidenceTaskInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetObtainEvidenceTaskInfoMultiError) AllErrors() []error { return m }

// NetObtainEvidenceTaskInfoValidationError is the validation error returned by
// NetObtainEvidenceTaskInfo.Validate if the designated constraints aren't met.
type NetObtainEvidenceTaskInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetObtainEvidenceTaskInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetObtainEvidenceTaskInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetObtainEvidenceTaskInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetObtainEvidenceTaskInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetObtainEvidenceTaskInfoValidationError) ErrorName() string {
	return "NetObtainEvidenceTaskInfoValidationError"
}

// Error satisfies the builtin error interface
func (e NetObtainEvidenceTaskInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetObtainEvidenceTaskInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetObtainEvidenceTaskInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetObtainEvidenceTaskInfoValidationError{}

// Validate checks the field values on LogObtainEvidenceTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogObtainEvidenceTaskRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogObtainEvidenceTaskRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogObtainEvidenceTaskRequestMultiError, or nil if none found.
func (m *LogObtainEvidenceTaskRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LogObtainEvidenceTaskRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	if all {
		switch v := interface{}(m.GetSourceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogObtainEvidenceTaskRequestValidationError{
					field:  "SourceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSourceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogObtainEvidenceTaskRequestValidationError{
				field:  "SourceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LogBit

	// no validation rules for Timestamp

	// no validation rules for ExistedRaiseError

	if len(errors) > 0 {
		return LogObtainEvidenceTaskRequestMultiError(errors)
	}

	return nil
}

// LogObtainEvidenceTaskRequestMultiError is an error wrapping multiple
// validation errors returned by LogObtainEvidenceTaskRequest.ValidateAll() if
// the designated constraints aren't met.
type LogObtainEvidenceTaskRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogObtainEvidenceTaskRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogObtainEvidenceTaskRequestMultiError) AllErrors() []error { return m }

// LogObtainEvidenceTaskRequestValidationError is the validation error returned
// by LogObtainEvidenceTaskRequest.Validate if the designated constraints
// aren't met.
type LogObtainEvidenceTaskRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogObtainEvidenceTaskRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogObtainEvidenceTaskRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogObtainEvidenceTaskRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogObtainEvidenceTaskRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogObtainEvidenceTaskRequestValidationError) ErrorName() string {
	return "LogObtainEvidenceTaskRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LogObtainEvidenceTaskRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogObtainEvidenceTaskRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogObtainEvidenceTaskRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogObtainEvidenceTaskRequestValidationError{}

// Validate checks the field values on RenewObtainEvidenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewObtainEvidenceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewObtainEvidenceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewObtainEvidenceRequestMultiError, or nil if none found.
func (m *RenewObtainEvidenceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewObtainEvidenceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return RenewObtainEvidenceRequestMultiError(errors)
	}

	return nil
}

// RenewObtainEvidenceRequestMultiError is an error wrapping multiple
// validation errors returned by RenewObtainEvidenceRequest.ValidateAll() if
// the designated constraints aren't met.
type RenewObtainEvidenceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewObtainEvidenceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewObtainEvidenceRequestMultiError) AllErrors() []error { return m }

// RenewObtainEvidenceRequestValidationError is the validation error returned
// by RenewObtainEvidenceRequest.Validate if the designated constraints aren't met.
type RenewObtainEvidenceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewObtainEvidenceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewObtainEvidenceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewObtainEvidenceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewObtainEvidenceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewObtainEvidenceRequestValidationError) ErrorName() string {
	return "RenewObtainEvidenceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RenewObtainEvidenceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewObtainEvidenceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewObtainEvidenceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewObtainEvidenceRequestValidationError{}

// Validate checks the field values on ClueEvidenceQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClueEvidenceQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClueEvidenceQueryRequestMultiError, or nil if none found.
func (m *ClueEvidenceQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueEvidenceQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueType

	// no validation rules for ClueKey

	// no validation rules for EvidenceType

	// no validation rules for IsUrlZip

	if len(errors) > 0 {
		return ClueEvidenceQueryRequestMultiError(errors)
	}

	return nil
}

// ClueEvidenceQueryRequestMultiError is an error wrapping multiple validation
// errors returned by ClueEvidenceQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type ClueEvidenceQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueEvidenceQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueEvidenceQueryRequestMultiError) AllErrors() []error { return m }

// ClueEvidenceQueryRequestValidationError is the validation error returned by
// ClueEvidenceQueryRequest.Validate if the designated constraints aren't met.
type ClueEvidenceQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueEvidenceQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueEvidenceQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueEvidenceQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueEvidenceQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueEvidenceQueryRequestValidationError) ErrorName() string {
	return "ClueEvidenceQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ClueEvidenceQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueEvidenceQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueEvidenceQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueEvidenceQueryRequestValidationError{}

// Validate checks the field values on GetFileEvidenceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileEvidenceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileEvidenceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileEvidenceRequestMultiError, or nil if none found.
func (m *GetFileEvidenceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileEvidenceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for SearchData

	// no validation rules for Timestamp

	// no validation rules for IsUrlZip

	if len(errors) > 0 {
		return GetFileEvidenceRequestMultiError(errors)
	}

	return nil
}

// GetFileEvidenceRequestMultiError is an error wrapping multiple validation
// errors returned by GetFileEvidenceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFileEvidenceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileEvidenceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileEvidenceRequestMultiError) AllErrors() []error { return m }

// GetFileEvidenceRequestValidationError is the validation error returned by
// GetFileEvidenceRequest.Validate if the designated constraints aren't met.
type GetFileEvidenceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileEvidenceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileEvidenceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileEvidenceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileEvidenceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileEvidenceRequestValidationError) ErrorName() string {
	return "GetFileEvidenceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileEvidenceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileEvidenceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileEvidenceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileEvidenceRequestValidationError{}

// Validate checks the field values on EvidenceExtractionResultResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *EvidenceExtractionResultResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceExtractionResultResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// EvidenceExtractionResultResponseMultiError, or nil if none found.
func (m *EvidenceExtractionResultResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceExtractionResultResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EvidenceExtractionResultResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EvidenceExtractionResultResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EvidenceExtractionResultResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EvidenceExtractionResultResponseMultiError(errors)
	}

	return nil
}

// EvidenceExtractionResultResponseMultiError is an error wrapping multiple
// validation errors returned by
// EvidenceExtractionResultResponse.ValidateAll() if the designated
// constraints aren't met.
type EvidenceExtractionResultResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceExtractionResultResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceExtractionResultResponseMultiError) AllErrors() []error { return m }

// EvidenceExtractionResultResponseValidationError is the validation error
// returned by EvidenceExtractionResultResponse.Validate if the designated
// constraints aren't met.
type EvidenceExtractionResultResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceExtractionResultResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceExtractionResultResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceExtractionResultResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceExtractionResultResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceExtractionResultResponseValidationError) ErrorName() string {
	return "EvidenceExtractionResultResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceExtractionResultResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceExtractionResultResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceExtractionResultResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceExtractionResultResponseValidationError{}

// Validate checks the field values on EvidenceExtractionResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceExtractionResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceExtractionResult with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceExtractionResultMultiError, or nil if none found.
func (m *EvidenceExtractionResult) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceExtractionResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for EvidenceType

	// no validation rules for Status

	// no validation rules for Url

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return EvidenceExtractionResultMultiError(errors)
	}

	return nil
}

// EvidenceExtractionResultMultiError is an error wrapping multiple validation
// errors returned by EvidenceExtractionResult.ValidateAll() if the designated
// constraints aren't met.
type EvidenceExtractionResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceExtractionResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceExtractionResultMultiError) AllErrors() []error { return m }

// EvidenceExtractionResultValidationError is the validation error returned by
// EvidenceExtractionResult.Validate if the designated constraints aren't met.
type EvidenceExtractionResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceExtractionResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceExtractionResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceExtractionResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceExtractionResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceExtractionResultValidationError) ErrorName() string {
	return "EvidenceExtractionResultValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceExtractionResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceExtractionResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceExtractionResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceExtractionResultValidationError{}

// Validate checks the field values on EvidenceFilter with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EvidenceFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EvidenceFilterMultiError,
// or nil if none found.
func (m *EvidenceFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchData

	// no validation rules for IsUrlZip

	if len(errors) > 0 {
		return EvidenceFilterMultiError(errors)
	}

	return nil
}

// EvidenceFilterMultiError is an error wrapping multiple validation errors
// returned by EvidenceFilter.ValidateAll() if the designated constraints
// aren't met.
type EvidenceFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceFilterMultiError) AllErrors() []error { return m }

// EvidenceFilterValidationError is the validation error returned by
// EvidenceFilter.Validate if the designated constraints aren't met.
type EvidenceFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceFilterValidationError) ErrorName() string { return "EvidenceFilterValidationError" }

// Error satisfies the builtin error interface
func (e EvidenceFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceFilterValidationError{}

// Validate checks the field values on NetEvidenceQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetEvidenceQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetEvidenceQueryRequestMultiError, or nil if none found.
func (m *NetEvidenceQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *NetEvidenceQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetEvidenceQueryRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderBy

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetEvidenceQueryRequestValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetEvidenceQueryRequestMultiError(errors)
	}

	return nil
}

// NetEvidenceQueryRequestMultiError is an error wrapping multiple validation
// errors returned by NetEvidenceQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type NetEvidenceQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetEvidenceQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetEvidenceQueryRequestMultiError) AllErrors() []error { return m }

// NetEvidenceQueryRequestValidationError is the validation error returned by
// NetEvidenceQueryRequest.Validate if the designated constraints aren't met.
type NetEvidenceQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetEvidenceQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetEvidenceQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetEvidenceQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetEvidenceQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetEvidenceQueryRequestValidationError) ErrorName() string {
	return "NetEvidenceQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e NetEvidenceQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetEvidenceQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetEvidenceQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetEvidenceQueryRequestValidationError{}

// Validate checks the field values on EvidenceSourceItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceSourceItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceSourceItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceSourceItemMultiError, or nil if none found.
func (m *EvidenceSourceItem) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceSourceItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceType

	// no validation rules for SourceId

	// no validation rules for SourceName

	if len(errors) > 0 {
		return EvidenceSourceItemMultiError(errors)
	}

	return nil
}

// EvidenceSourceItemMultiError is an error wrapping multiple validation errors
// returned by EvidenceSourceItem.ValidateAll() if the designated constraints
// aren't met.
type EvidenceSourceItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceSourceItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceSourceItemMultiError) AllErrors() []error { return m }

// EvidenceSourceItemValidationError is the validation error returned by
// EvidenceSourceItem.Validate if the designated constraints aren't met.
type EvidenceSourceItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceSourceItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceSourceItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceSourceItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceSourceItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceSourceItemValidationError) ErrorName() string {
	return "EvidenceSourceItemValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceSourceItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceSourceItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceSourceItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceSourceItemValidationError{}

// Validate checks the field values on NetEvidenceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NetEvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetEvidenceInfoMultiError, or nil if none found.
func (m *NetEvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetEvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for MachineId

	// no validation rules for Status

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	// no validation rules for Protocol

	// no validation rules for Domain

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetSources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetEvidenceInfoValidationError{
					field:  fmt.Sprintf("Sources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return NetEvidenceInfoMultiError(errors)
	}

	return nil
}

// NetEvidenceInfoMultiError is an error wrapping multiple validation errors
// returned by NetEvidenceInfo.ValidateAll() if the designated constraints
// aren't met.
type NetEvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetEvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetEvidenceInfoMultiError) AllErrors() []error { return m }

// NetEvidenceInfoValidationError is the validation error returned by
// NetEvidenceInfo.Validate if the designated constraints aren't met.
type NetEvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetEvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetEvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetEvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetEvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetEvidenceInfoValidationError) ErrorName() string { return "NetEvidenceInfoValidationError" }

// Error satisfies the builtin error interface
func (e NetEvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetEvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetEvidenceInfoValidationError{}

// Validate checks the field values on NetEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NetEvidenceInfoListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NetEvidenceInfoListResponseMultiError, or nil if none found.
func (m *NetEvidenceInfoListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *NetEvidenceInfoListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NetEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NetEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NetEvidenceInfoListResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NetEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NetEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NetEvidenceInfoListResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NetEvidenceInfoListResponseMultiError(errors)
	}

	return nil
}

// NetEvidenceInfoListResponseMultiError is an error wrapping multiple
// validation errors returned by NetEvidenceInfoListResponse.ValidateAll() if
// the designated constraints aren't met.
type NetEvidenceInfoListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetEvidenceInfoListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetEvidenceInfoListResponseMultiError) AllErrors() []error { return m }

// NetEvidenceInfoListResponseValidationError is the validation error returned
// by NetEvidenceInfoListResponse.Validate if the designated constraints
// aren't met.
type NetEvidenceInfoListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetEvidenceInfoListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetEvidenceInfoListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetEvidenceInfoListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetEvidenceInfoListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetEvidenceInfoListResponseValidationError) ErrorName() string {
	return "NetEvidenceInfoListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e NetEvidenceInfoListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetEvidenceInfoListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetEvidenceInfoListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetEvidenceInfoListResponseValidationError{}

// Validate checks the field values on FileEvidenceQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileEvidenceQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileEvidenceQueryRequestMultiError, or nil if none found.
func (m *FileEvidenceQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FileEvidenceQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileEvidenceQueryRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderBy

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileEvidenceQueryRequestValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileEvidenceQueryRequestMultiError(errors)
	}

	return nil
}

// FileEvidenceQueryRequestMultiError is an error wrapping multiple validation
// errors returned by FileEvidenceQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type FileEvidenceQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileEvidenceQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileEvidenceQueryRequestMultiError) AllErrors() []error { return m }

// FileEvidenceQueryRequestValidationError is the validation error returned by
// FileEvidenceQueryRequest.Validate if the designated constraints aren't met.
type FileEvidenceQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileEvidenceQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileEvidenceQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileEvidenceQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileEvidenceQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileEvidenceQueryRequestValidationError) ErrorName() string {
	return "FileEvidenceQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FileEvidenceQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileEvidenceQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileEvidenceQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileEvidenceQueryRequestValidationError{}

// Validate checks the field values on FileEvidenceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FileEvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileEvidenceInfoMultiError, or nil if none found.
func (m *FileEvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileEvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Category

	// no validation rules for MachineId

	// no validation rules for Status

	// no validation rules for Isolate

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileEvidenceInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileEvidenceInfoValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileEvidenceInfoValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetSources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileEvidenceInfoValidationError{
					field:  fmt.Sprintf("Sources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return FileEvidenceInfoMultiError(errors)
	}

	return nil
}

// FileEvidenceInfoMultiError is an error wrapping multiple validation errors
// returned by FileEvidenceInfo.ValidateAll() if the designated constraints
// aren't met.
type FileEvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileEvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileEvidenceInfoMultiError) AllErrors() []error { return m }

// FileEvidenceInfoValidationError is the validation error returned by
// FileEvidenceInfo.Validate if the designated constraints aren't met.
type FileEvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileEvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileEvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileEvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileEvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileEvidenceInfoValidationError) ErrorName() string { return "FileEvidenceInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileEvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileEvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileEvidenceInfoValidationError{}

// Validate checks the field values on FileEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileEvidenceInfoListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileEvidenceInfoListResponseMultiError, or nil if none found.
func (m *FileEvidenceInfoListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FileEvidenceInfoListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FileEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FileEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FileEvidenceInfoListResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileEvidenceInfoListResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileEvidenceInfoListResponseMultiError(errors)
	}

	return nil
}

// FileEvidenceInfoListResponseMultiError is an error wrapping multiple
// validation errors returned by FileEvidenceInfoListResponse.ValidateAll() if
// the designated constraints aren't met.
type FileEvidenceInfoListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileEvidenceInfoListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileEvidenceInfoListResponseMultiError) AllErrors() []error { return m }

// FileEvidenceInfoListResponseValidationError is the validation error returned
// by FileEvidenceInfoListResponse.Validate if the designated constraints
// aren't met.
type FileEvidenceInfoListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileEvidenceInfoListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileEvidenceInfoListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileEvidenceInfoListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileEvidenceInfoListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileEvidenceInfoListResponseValidationError) ErrorName() string {
	return "FileEvidenceInfoListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FileEvidenceInfoListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileEvidenceInfoListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileEvidenceInfoListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileEvidenceInfoListResponseValidationError{}

// Validate checks the field values on MemDumpEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemDumpEvidenceQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemDumpEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemDumpEvidenceQueryRequestMultiError, or nil if none found.
func (m *MemDumpEvidenceQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *MemDumpEvidenceQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemDumpEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemDumpEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemDumpEvidenceQueryRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderBy

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemDumpEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemDumpEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemDumpEvidenceQueryRequestValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MemDumpEvidenceQueryRequestMultiError(errors)
	}

	return nil
}

// MemDumpEvidenceQueryRequestMultiError is an error wrapping multiple
// validation errors returned by MemDumpEvidenceQueryRequest.ValidateAll() if
// the designated constraints aren't met.
type MemDumpEvidenceQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemDumpEvidenceQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemDumpEvidenceQueryRequestMultiError) AllErrors() []error { return m }

// MemDumpEvidenceQueryRequestValidationError is the validation error returned
// by MemDumpEvidenceQueryRequest.Validate if the designated constraints
// aren't met.
type MemDumpEvidenceQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemDumpEvidenceQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemDumpEvidenceQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemDumpEvidenceQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemDumpEvidenceQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemDumpEvidenceQueryRequestValidationError) ErrorName() string {
	return "MemDumpEvidenceQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e MemDumpEvidenceQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemDumpEvidenceQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemDumpEvidenceQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemDumpEvidenceQueryRequestValidationError{}

// Validate checks the field values on MemDumpEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemDumpEvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemDumpEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemDumpEvidenceInfoMultiError, or nil if none found.
func (m *MemDumpEvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MemDumpEvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Category

	// no validation rules for MachineId

	// no validation rules for Status

	// no validation rules for Pid

	// no validation rules for Name

	// no validation rules for Filepath

	// no validation rules for FileMd5

	// no validation rules for Cmd

	// no validation rules for Owner

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetSources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemDumpEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemDumpEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemDumpEvidenceInfoValidationError{
					field:  fmt.Sprintf("Sources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return MemDumpEvidenceInfoMultiError(errors)
	}

	return nil
}

// MemDumpEvidenceInfoMultiError is an error wrapping multiple validation
// errors returned by MemDumpEvidenceInfo.ValidateAll() if the designated
// constraints aren't met.
type MemDumpEvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemDumpEvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemDumpEvidenceInfoMultiError) AllErrors() []error { return m }

// MemDumpEvidenceInfoValidationError is the validation error returned by
// MemDumpEvidenceInfo.Validate if the designated constraints aren't met.
type MemDumpEvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemDumpEvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemDumpEvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemDumpEvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemDumpEvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemDumpEvidenceInfoValidationError) ErrorName() string {
	return "MemDumpEvidenceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e MemDumpEvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemDumpEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemDumpEvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemDumpEvidenceInfoValidationError{}

// Validate checks the field values on MemDumpEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemDumpEvidenceInfoListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemDumpEvidenceInfoListResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// MemDumpEvidenceInfoListResponseMultiError, or nil if none found.
func (m *MemDumpEvidenceInfoListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *MemDumpEvidenceInfoListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MemDumpEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MemDumpEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MemDumpEvidenceInfoListResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemDumpEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemDumpEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemDumpEvidenceInfoListResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MemDumpEvidenceInfoListResponseMultiError(errors)
	}

	return nil
}

// MemDumpEvidenceInfoListResponseMultiError is an error wrapping multiple
// validation errors returned by MemDumpEvidenceInfoListResponse.ValidateAll()
// if the designated constraints aren't met.
type MemDumpEvidenceInfoListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemDumpEvidenceInfoListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemDumpEvidenceInfoListResponseMultiError) AllErrors() []error { return m }

// MemDumpEvidenceInfoListResponseValidationError is the validation error
// returned by MemDumpEvidenceInfoListResponse.Validate if the designated
// constraints aren't met.
type MemDumpEvidenceInfoListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemDumpEvidenceInfoListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemDumpEvidenceInfoListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemDumpEvidenceInfoListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemDumpEvidenceInfoListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemDumpEvidenceInfoListResponseValidationError) ErrorName() string {
	return "MemDumpEvidenceInfoListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e MemDumpEvidenceInfoListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemDumpEvidenceInfoListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemDumpEvidenceInfoListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemDumpEvidenceInfoListResponseValidationError{}

// Validate checks the field values on LogEvidenceFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LogEvidenceFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogEvidenceFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogEvidenceFilterMultiError, or nil if none found.
func (m *LogEvidenceFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *LogEvidenceFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LogType

	if len(errors) > 0 {
		return LogEvidenceFilterMultiError(errors)
	}

	return nil
}

// LogEvidenceFilterMultiError is an error wrapping multiple validation errors
// returned by LogEvidenceFilter.ValidateAll() if the designated constraints
// aren't met.
type LogEvidenceFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogEvidenceFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogEvidenceFilterMultiError) AllErrors() []error { return m }

// LogEvidenceFilterValidationError is the validation error returned by
// LogEvidenceFilter.Validate if the designated constraints aren't met.
type LogEvidenceFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogEvidenceFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogEvidenceFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogEvidenceFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogEvidenceFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogEvidenceFilterValidationError) ErrorName() string {
	return "LogEvidenceFilterValidationError"
}

// Error satisfies the builtin error interface
func (e LogEvidenceFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogEvidenceFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogEvidenceFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogEvidenceFilterValidationError{}

// Validate checks the field values on LogEvidenceQueryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogEvidenceQueryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogEvidenceQueryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogEvidenceQueryRequestMultiError, or nil if none found.
func (m *LogEvidenceQueryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LogEvidenceQueryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogEvidenceQueryRequestValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogEvidenceQueryRequestValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderBy

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogEvidenceQueryRequestValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogEvidenceQueryRequestValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogEvidenceQueryRequestMultiError(errors)
	}

	return nil
}

// LogEvidenceQueryRequestMultiError is an error wrapping multiple validation
// errors returned by LogEvidenceQueryRequest.ValidateAll() if the designated
// constraints aren't met.
type LogEvidenceQueryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogEvidenceQueryRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogEvidenceQueryRequestMultiError) AllErrors() []error { return m }

// LogEvidenceQueryRequestValidationError is the validation error returned by
// LogEvidenceQueryRequest.Validate if the designated constraints aren't met.
type LogEvidenceQueryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogEvidenceQueryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogEvidenceQueryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogEvidenceQueryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogEvidenceQueryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogEvidenceQueryRequestValidationError) ErrorName() string {
	return "LogEvidenceQueryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e LogEvidenceQueryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogEvidenceQueryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogEvidenceQueryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogEvidenceQueryRequestValidationError{}

// Validate checks the field values on LogEvidenceInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LogEvidenceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogEvidenceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogEvidenceInfoMultiError, or nil if none found.
func (m *LogEvidenceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LogEvidenceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for MachineId

	// no validation rules for Status

	// no validation rules for LogBit

	// no validation rules for CreatedAt

	// no validation rules for UpdatedAt

	for idx, item := range m.GetSources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LogEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LogEvidenceInfoValidationError{
						field:  fmt.Sprintf("Sources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LogEvidenceInfoValidationError{
					field:  fmt.Sprintf("Sources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for DownloadUrl

	if len(errors) > 0 {
		return LogEvidenceInfoMultiError(errors)
	}

	return nil
}

// LogEvidenceInfoMultiError is an error wrapping multiple validation errors
// returned by LogEvidenceInfo.ValidateAll() if the designated constraints
// aren't met.
type LogEvidenceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogEvidenceInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogEvidenceInfoMultiError) AllErrors() []error { return m }

// LogEvidenceInfoValidationError is the validation error returned by
// LogEvidenceInfo.Validate if the designated constraints aren't met.
type LogEvidenceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogEvidenceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogEvidenceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogEvidenceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogEvidenceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogEvidenceInfoValidationError) ErrorName() string { return "LogEvidenceInfoValidationError" }

// Error satisfies the builtin error interface
func (e LogEvidenceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogEvidenceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogEvidenceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogEvidenceInfoValidationError{}

// Validate checks the field values on LogEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogEvidenceInfoListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogEvidenceInfoListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogEvidenceInfoListResponseMultiError, or nil if none found.
func (m *LogEvidenceInfoListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *LogEvidenceInfoListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LogEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LogEvidenceInfoListResponseValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LogEvidenceInfoListResponseValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogEvidenceInfoListResponseValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogEvidenceInfoListResponseValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LogEvidenceInfoListResponseMultiError(errors)
	}

	return nil
}

// LogEvidenceInfoListResponseMultiError is an error wrapping multiple
// validation errors returned by LogEvidenceInfoListResponse.ValidateAll() if
// the designated constraints aren't met.
type LogEvidenceInfoListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogEvidenceInfoListResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogEvidenceInfoListResponseMultiError) AllErrors() []error { return m }

// LogEvidenceInfoListResponseValidationError is the validation error returned
// by LogEvidenceInfoListResponse.Validate if the designated constraints
// aren't met.
type LogEvidenceInfoListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogEvidenceInfoListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogEvidenceInfoListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogEvidenceInfoListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogEvidenceInfoListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogEvidenceInfoListResponseValidationError) ErrorName() string {
	return "LogEvidenceInfoListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e LogEvidenceInfoListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogEvidenceInfoListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogEvidenceInfoListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogEvidenceInfoListResponseValidationError{}

// Validate checks the field values on GetEvidenceResultInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEvidenceResultInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEvidenceResultInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEvidenceResultInfoRequestMultiError, or nil if none found.
func (m *GetEvidenceResultInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEvidenceResultInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return GetEvidenceResultInfoRequestMultiError(errors)
	}

	return nil
}

// GetEvidenceResultInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetEvidenceResultInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetEvidenceResultInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEvidenceResultInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEvidenceResultInfoRequestMultiError) AllErrors() []error { return m }

// GetEvidenceResultInfoRequestValidationError is the validation error returned
// by GetEvidenceResultInfoRequest.Validate if the designated constraints
// aren't met.
type GetEvidenceResultInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEvidenceResultInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEvidenceResultInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEvidenceResultInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEvidenceResultInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEvidenceResultInfoRequestValidationError) ErrorName() string {
	return "GetEvidenceResultInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEvidenceResultInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEvidenceResultInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEvidenceResultInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEvidenceResultInfoRequestValidationError{}

// Validate checks the field values on TerminalInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TerminalInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TerminalInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TerminalInfoMultiError, or
// nil if none found.
func (m *TerminalInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TerminalInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HostId

	// no validation rules for Hostname

	// no validation rules for Ip

	// no validation rules for Os

	// no validation rules for Group

	if len(errors) > 0 {
		return TerminalInfoMultiError(errors)
	}

	return nil
}

// TerminalInfoMultiError is an error wrapping multiple validation errors
// returned by TerminalInfo.ValidateAll() if the designated constraints aren't met.
type TerminalInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TerminalInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TerminalInfoMultiError) AllErrors() []error { return m }

// TerminalInfoValidationError is the validation error returned by
// TerminalInfo.Validate if the designated constraints aren't met.
type TerminalInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TerminalInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TerminalInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TerminalInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TerminalInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TerminalInfoValidationError) ErrorName() string { return "TerminalInfoValidationError" }

// Error satisfies the builtin error interface
func (e TerminalInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTerminalInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TerminalInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TerminalInfoValidationError{}

// Validate checks the field values on EvidenceProcessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceProcessInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceProcessInfoMultiError, or nil if none found.
func (m *EvidenceProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for StartTime

	// no validation rules for Ppid

	// no validation rules for ParentStartTime

	// no validation rules for ProcessName

	// no validation rules for Cmd

	// no validation rules for Filepath

	// no validation rules for FileMd5

	// no validation rules for FileSha256

	// no validation rules for FileSize

	// no validation rules for FileCtime

	// no validation rules for FileMtime

	// no validation rules for FileAtime

	// no validation rules for Owner

	if len(errors) > 0 {
		return EvidenceProcessInfoMultiError(errors)
	}

	return nil
}

// EvidenceProcessInfoMultiError is an error wrapping multiple validation
// errors returned by EvidenceProcessInfo.ValidateAll() if the designated
// constraints aren't met.
type EvidenceProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceProcessInfoMultiError) AllErrors() []error { return m }

// EvidenceProcessInfoValidationError is the validation error returned by
// EvidenceProcessInfo.Validate if the designated constraints aren't met.
type EvidenceProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceProcessInfoValidationError) ErrorName() string {
	return "EvidenceProcessInfoValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceProcessInfoValidationError{}

// Validate checks the field values on FileEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileEvidenceContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileEvidenceContentMultiError, or nil if none found.
func (m *FileEvidenceContent) ValidateAll() error {
	return m.validate(true)
}

func (m *FileEvidenceContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filepath

	// no validation rules for Url

	// no validation rules for Md5Sum

	if len(errors) > 0 {
		return FileEvidenceContentMultiError(errors)
	}

	return nil
}

// FileEvidenceContentMultiError is an error wrapping multiple validation
// errors returned by FileEvidenceContent.ValidateAll() if the designated
// constraints aren't met.
type FileEvidenceContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileEvidenceContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileEvidenceContentMultiError) AllErrors() []error { return m }

// FileEvidenceContentValidationError is the validation error returned by
// FileEvidenceContent.Validate if the designated constraints aren't met.
type FileEvidenceContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileEvidenceContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileEvidenceContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileEvidenceContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileEvidenceContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileEvidenceContentValidationError) ErrorName() string {
	return "FileEvidenceContentValidationError"
}

// Error satisfies the builtin error interface
func (e FileEvidenceContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileEvidenceContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileEvidenceContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileEvidenceContentValidationError{}

// Validate checks the field values on CommonEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CommonEvidenceContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonEvidenceContentMultiError, or nil if none found.
func (m *CommonEvidenceContent) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonEvidenceContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filename

	// no validation rules for Url

	if len(errors) > 0 {
		return CommonEvidenceContentMultiError(errors)
	}

	return nil
}

// CommonEvidenceContentMultiError is an error wrapping multiple validation
// errors returned by CommonEvidenceContent.ValidateAll() if the designated
// constraints aren't met.
type CommonEvidenceContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonEvidenceContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonEvidenceContentMultiError) AllErrors() []error { return m }

// CommonEvidenceContentValidationError is the validation error returned by
// CommonEvidenceContent.Validate if the designated constraints aren't met.
type CommonEvidenceContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonEvidenceContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonEvidenceContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonEvidenceContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonEvidenceContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonEvidenceContentValidationError) ErrorName() string {
	return "CommonEvidenceContentValidationError"
}

// Error satisfies the builtin error interface
func (e CommonEvidenceContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonEvidenceContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonEvidenceContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonEvidenceContentValidationError{}

// Validate checks the field values on LogEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LogEvidenceContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LogEvidenceContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LogEvidenceContentMultiError, or nil if none found.
func (m *LogEvidenceContent) ValidateAll() error {
	return m.validate(true)
}

func (m *LogEvidenceContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LogEvidenceContentValidationError{
					field:  "Common",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LogEvidenceContentValidationError{
					field:  "Common",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LogEvidenceContentValidationError{
				field:  "Common",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LogBit

	if len(errors) > 0 {
		return LogEvidenceContentMultiError(errors)
	}

	return nil
}

// LogEvidenceContentMultiError is an error wrapping multiple validation errors
// returned by LogEvidenceContent.ValidateAll() if the designated constraints
// aren't met.
type LogEvidenceContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LogEvidenceContentMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LogEvidenceContentMultiError) AllErrors() []error { return m }

// LogEvidenceContentValidationError is the validation error returned by
// LogEvidenceContent.Validate if the designated constraints aren't met.
type LogEvidenceContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LogEvidenceContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LogEvidenceContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LogEvidenceContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LogEvidenceContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LogEvidenceContentValidationError) ErrorName() string {
	return "LogEvidenceContentValidationError"
}

// Error satisfies the builtin error interface
func (e LogEvidenceContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLogEvidenceContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LogEvidenceContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LogEvidenceContentValidationError{}

// Validate checks the field values on EvidenceResultInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceResultInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceResultInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceResultInfoMultiError, or nil if none found.
func (m *EvidenceResultInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceResultInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	// no validation rules for Status

	// no validation rules for ExpiredAt

	// no validation rules for ClueKey

	if all {
		switch v := interface{}(m.GetTerminalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "TerminalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "TerminalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTerminalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "TerminalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetProcessChainInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EvidenceResultInfoValidationError{
						field:  fmt.Sprintf("ProcessChainInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EvidenceResultInfoValidationError{
						field:  fmt.Sprintf("ProcessChainInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EvidenceResultInfoValidationError{
					field:  fmt.Sprintf("ProcessChainInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "File",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "File",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScriptFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "ScriptFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "ScriptFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScriptFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "ScriptFile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMemoryDump()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MemoryDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MemoryDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMemoryDump()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "MemoryDump",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMiniMemoryDump()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MiniMemoryDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MiniMemoryDump",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMiniMemoryDump()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "MiniMemoryDump",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMemorySegment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MemorySegment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "MemorySegment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMemorySegment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "MemorySegment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLogFile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "LogFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvidenceResultInfoValidationError{
					field:  "LogFile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLogFile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvidenceResultInfoValidationError{
				field:  "LogFile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EvidenceResultInfoMultiError(errors)
	}

	return nil
}

// EvidenceResultInfoMultiError is an error wrapping multiple validation errors
// returned by EvidenceResultInfo.ValidateAll() if the designated constraints
// aren't met.
type EvidenceResultInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceResultInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceResultInfoMultiError) AllErrors() []error { return m }

// EvidenceResultInfoValidationError is the validation error returned by
// EvidenceResultInfo.Validate if the designated constraints aren't met.
type EvidenceResultInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceResultInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceResultInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceResultInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceResultInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceResultInfoValidationError) ErrorName() string {
	return "EvidenceResultInfoValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceResultInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceResultInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceResultInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceResultInfoValidationError{}

// Validate checks the field values on BatchGetEvidenceResultInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchGetEvidenceResultInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchGetEvidenceResultInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BatchGetEvidenceResultInfoRequestMultiError, or nil if none found.
func (m *BatchGetEvidenceResultInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchGetEvidenceResultInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchGetEvidenceResultInfoRequestMultiError(errors)
	}

	return nil
}

// BatchGetEvidenceResultInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// BatchGetEvidenceResultInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type BatchGetEvidenceResultInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchGetEvidenceResultInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchGetEvidenceResultInfoRequestMultiError) AllErrors() []error { return m }

// BatchGetEvidenceResultInfoRequestValidationError is the validation error
// returned by BatchGetEvidenceResultInfoRequest.Validate if the designated
// constraints aren't met.
type BatchGetEvidenceResultInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchGetEvidenceResultInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchGetEvidenceResultInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchGetEvidenceResultInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchGetEvidenceResultInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchGetEvidenceResultInfoRequestValidationError) ErrorName() string {
	return "BatchGetEvidenceResultInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BatchGetEvidenceResultInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchGetEvidenceResultInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchGetEvidenceResultInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchGetEvidenceResultInfoRequestValidationError{}

// Validate checks the field values on BatchEvidenceResultInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchEvidenceResultInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchEvidenceResultInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchEvidenceResultInfoResponseMultiError, or nil if none found.
func (m *BatchEvidenceResultInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchEvidenceResultInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchEvidenceResultInfoResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchEvidenceResultInfoResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchEvidenceResultInfoResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchEvidenceResultInfoResponseMultiError(errors)
	}

	return nil
}

// BatchEvidenceResultInfoResponseMultiError is an error wrapping multiple
// validation errors returned by BatchEvidenceResultInfoResponse.ValidateAll()
// if the designated constraints aren't met.
type BatchEvidenceResultInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchEvidenceResultInfoResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchEvidenceResultInfoResponseMultiError) AllErrors() []error { return m }

// BatchEvidenceResultInfoResponseValidationError is the validation error
// returned by BatchEvidenceResultInfoResponse.Validate if the designated
// constraints aren't met.
type BatchEvidenceResultInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchEvidenceResultInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchEvidenceResultInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchEvidenceResultInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchEvidenceResultInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchEvidenceResultInfoResponseValidationError) ErrorName() string {
	return "BatchEvidenceResultInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BatchEvidenceResultInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchEvidenceResultInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchEvidenceResultInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchEvidenceResultInfoResponseValidationError{}

// Validate checks the field values on GetEvidenceStorageInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEvidenceStorageInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEvidenceStorageInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEvidenceStorageInfoRequestMultiError, or nil if none found.
func (m *GetEvidenceStorageInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEvidenceStorageInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskId

	if len(errors) > 0 {
		return GetEvidenceStorageInfoRequestMultiError(errors)
	}

	return nil
}

// GetEvidenceStorageInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetEvidenceStorageInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type GetEvidenceStorageInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEvidenceStorageInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEvidenceStorageInfoRequestMultiError) AllErrors() []error { return m }

// GetEvidenceStorageInfoRequestValidationError is the validation error
// returned by GetEvidenceStorageInfoRequest.Validate if the designated
// constraints aren't met.
type GetEvidenceStorageInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEvidenceStorageInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEvidenceStorageInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEvidenceStorageInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEvidenceStorageInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEvidenceStorageInfoRequestValidationError) ErrorName() string {
	return "GetEvidenceStorageInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEvidenceStorageInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEvidenceStorageInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEvidenceStorageInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEvidenceStorageInfoRequestValidationError{}

// Validate checks the field values on EvidenceStorage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EvidenceStorage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceStorage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceStorageMultiError, or nil if none found.
func (m *EvidenceStorage) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceStorage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Mode

	// no validation rules for Content

	// no validation rules for Size

	// no validation rules for Md5

	// no validation rules for CreatedAt

	if len(errors) > 0 {
		return EvidenceStorageMultiError(errors)
	}

	return nil
}

// EvidenceStorageMultiError is an error wrapping multiple validation errors
// returned by EvidenceStorage.ValidateAll() if the designated constraints
// aren't met.
type EvidenceStorageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceStorageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceStorageMultiError) AllErrors() []error { return m }

// EvidenceStorageValidationError is the validation error returned by
// EvidenceStorage.Validate if the designated constraints aren't met.
type EvidenceStorageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceStorageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceStorageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceStorageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceStorageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceStorageValidationError) ErrorName() string { return "EvidenceStorageValidationError" }

// Error satisfies the builtin error interface
func (e EvidenceStorageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceStorage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceStorageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceStorageValidationError{}

// Validate checks the field values on GetObtainedEvidenceCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetObtainedEvidenceCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetObtainedEvidenceCountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetObtainedEvidenceCountRequestMultiError, or nil if none found.
func (m *GetObtainedEvidenceCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetObtainedEvidenceCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetObtainedEvidenceCountRequestMultiError(errors)
	}

	return nil
}

// GetObtainedEvidenceCountRequestMultiError is an error wrapping multiple
// validation errors returned by GetObtainedEvidenceCountRequest.ValidateAll()
// if the designated constraints aren't met.
type GetObtainedEvidenceCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetObtainedEvidenceCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetObtainedEvidenceCountRequestMultiError) AllErrors() []error { return m }

// GetObtainedEvidenceCountRequestValidationError is the validation error
// returned by GetObtainedEvidenceCountRequest.Validate if the designated
// constraints aren't met.
type GetObtainedEvidenceCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetObtainedEvidenceCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetObtainedEvidenceCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetObtainedEvidenceCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetObtainedEvidenceCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetObtainedEvidenceCountRequestValidationError) ErrorName() string {
	return "GetObtainedEvidenceCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetObtainedEvidenceCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetObtainedEvidenceCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetObtainedEvidenceCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetObtainedEvidenceCountRequestValidationError{}

// Validate checks the field values on ObtainEvidenceCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ObtainEvidenceCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ObtainEvidenceCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ObtainEvidenceCountResponseMultiError, or nil if none found.
func (m *ObtainEvidenceCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ObtainEvidenceCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	// no validation rules for EvidenceNetCount

	// no validation rules for EvidenceFileCount

	// no validation rules for EvidenceDumpCount

	if len(errors) > 0 {
		return ObtainEvidenceCountResponseMultiError(errors)
	}

	return nil
}

// ObtainEvidenceCountResponseMultiError is an error wrapping multiple
// validation errors returned by ObtainEvidenceCountResponse.ValidateAll() if
// the designated constraints aren't met.
type ObtainEvidenceCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ObtainEvidenceCountResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ObtainEvidenceCountResponseMultiError) AllErrors() []error { return m }

// ObtainEvidenceCountResponseValidationError is the validation error returned
// by ObtainEvidenceCountResponse.Validate if the designated constraints
// aren't met.
type ObtainEvidenceCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ObtainEvidenceCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ObtainEvidenceCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ObtainEvidenceCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ObtainEvidenceCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ObtainEvidenceCountResponseValidationError) ErrorName() string {
	return "ObtainEvidenceCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ObtainEvidenceCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sObtainEvidenceCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ObtainEvidenceCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ObtainEvidenceCountResponseValidationError{}

// Validate checks the field values on GetEvidenceSourceConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEvidenceSourceConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEvidenceSourceConfigRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEvidenceSourceConfigRequestMultiError, or nil if none found.
func (m *GetEvidenceSourceConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEvidenceSourceConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	if len(errors) > 0 {
		return GetEvidenceSourceConfigRequestMultiError(errors)
	}

	return nil
}

// GetEvidenceSourceConfigRequestMultiError is an error wrapping multiple
// validation errors returned by GetEvidenceSourceConfigRequest.ValidateAll()
// if the designated constraints aren't met.
type GetEvidenceSourceConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEvidenceSourceConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEvidenceSourceConfigRequestMultiError) AllErrors() []error { return m }

// GetEvidenceSourceConfigRequestValidationError is the validation error
// returned by GetEvidenceSourceConfigRequest.Validate if the designated
// constraints aren't met.
type GetEvidenceSourceConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEvidenceSourceConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEvidenceSourceConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEvidenceSourceConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEvidenceSourceConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEvidenceSourceConfigRequestValidationError) ErrorName() string {
	return "GetEvidenceSourceConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEvidenceSourceConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEvidenceSourceConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEvidenceSourceConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEvidenceSourceConfigRequestValidationError{}

// Validate checks the field values on EvidenceSourceConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceSourceConfigResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceSourceConfigResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceSourceConfigResponseMultiError, or nil if none found.
func (m *EvidenceSourceConfigResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceSourceConfigResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	// no validation rules for ReturnWay

	// no validation rules for CallbackPath

	// no validation rules for Description

	if len(errors) > 0 {
		return EvidenceSourceConfigResponseMultiError(errors)
	}

	return nil
}

// EvidenceSourceConfigResponseMultiError is an error wrapping multiple
// validation errors returned by EvidenceSourceConfigResponse.ValidateAll() if
// the designated constraints aren't met.
type EvidenceSourceConfigResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceSourceConfigResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceSourceConfigResponseMultiError) AllErrors() []error { return m }

// EvidenceSourceConfigResponseValidationError is the validation error returned
// by EvidenceSourceConfigResponse.Validate if the designated constraints
// aren't met.
type EvidenceSourceConfigResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceSourceConfigResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceSourceConfigResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceSourceConfigResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceSourceConfigResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceSourceConfigResponseValidationError) ErrorName() string {
	return "EvidenceSourceConfigResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceSourceConfigResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceSourceConfigResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceSourceConfigResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceSourceConfigResponseValidationError{}

// Validate checks the field values on EvidenceSourceConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EvidenceSourceConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EvidenceSourceConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EvidenceSourceConfigRequestMultiError, or nil if none found.
func (m *EvidenceSourceConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EvidenceSourceConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	// no validation rules for ReturnWay

	// no validation rules for CallbackPath

	// no validation rules for Description

	if len(errors) > 0 {
		return EvidenceSourceConfigRequestMultiError(errors)
	}

	return nil
}

// EvidenceSourceConfigRequestMultiError is an error wrapping multiple
// validation errors returned by EvidenceSourceConfigRequest.ValidateAll() if
// the designated constraints aren't met.
type EvidenceSourceConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceSourceConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceSourceConfigRequestMultiError) AllErrors() []error { return m }

// EvidenceSourceConfigRequestValidationError is the validation error returned
// by EvidenceSourceConfigRequest.Validate if the designated constraints
// aren't met.
type EvidenceSourceConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceSourceConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceSourceConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceSourceConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceSourceConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceSourceConfigRequestValidationError) ErrorName() string {
	return "EvidenceSourceConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EvidenceSourceConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidenceSourceConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceSourceConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceSourceConfigRequestValidationError{}

// Validate checks the field values on DeleteEvidenceSourceConfigRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *DeleteEvidenceSourceConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteEvidenceSourceConfigRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DeleteEvidenceSourceConfigRequestMultiError, or nil if none found.
func (m *DeleteEvidenceSourceConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteEvidenceSourceConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	if len(errors) > 0 {
		return DeleteEvidenceSourceConfigRequestMultiError(errors)
	}

	return nil
}

// DeleteEvidenceSourceConfigRequestMultiError is an error wrapping multiple
// validation errors returned by
// DeleteEvidenceSourceConfigRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteEvidenceSourceConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteEvidenceSourceConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteEvidenceSourceConfigRequestMultiError) AllErrors() []error { return m }

// DeleteEvidenceSourceConfigRequestValidationError is the validation error
// returned by DeleteEvidenceSourceConfigRequest.Validate if the designated
// constraints aren't met.
type DeleteEvidenceSourceConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteEvidenceSourceConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteEvidenceSourceConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteEvidenceSourceConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteEvidenceSourceConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteEvidenceSourceConfigRequestValidationError) ErrorName() string {
	return "DeleteEvidenceSourceConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteEvidenceSourceConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteEvidenceSourceConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteEvidenceSourceConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteEvidenceSourceConfigRequestValidationError{}

// Validate checks the field values on
// UpdateAutoObtainEvidenceClueSubTypeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateAutoObtainEvidenceClueSubTypeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateAutoObtainEvidenceClueSubTypeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateAutoObtainEvidenceClueSubTypeRequestMultiError, or nil if none found.
func (m *UpdateAutoObtainEvidenceClueSubTypeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAutoObtainEvidenceClueSubTypeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Platform

	if len(errors) > 0 {
		return UpdateAutoObtainEvidenceClueSubTypeRequestMultiError(errors)
	}

	return nil
}

// UpdateAutoObtainEvidenceClueSubTypeRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateAutoObtainEvidenceClueSubTypeRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAutoObtainEvidenceClueSubTypeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAutoObtainEvidenceClueSubTypeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAutoObtainEvidenceClueSubTypeRequestMultiError) AllErrors() []error { return m }

// UpdateAutoObtainEvidenceClueSubTypeRequestValidationError is the validation
// error returned by UpdateAutoObtainEvidenceClueSubTypeRequest.Validate if
// the designated constraints aren't met.
type UpdateAutoObtainEvidenceClueSubTypeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) ErrorName() string {
	return "UpdateAutoObtainEvidenceClueSubTypeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAutoObtainEvidenceClueSubTypeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAutoObtainEvidenceClueSubTypeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAutoObtainEvidenceClueSubTypeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAutoObtainEvidenceClueSubTypeRequestValidationError{}
