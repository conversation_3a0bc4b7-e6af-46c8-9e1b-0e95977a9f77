// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.3
// source: conan/dashboard.proto

package conan

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CountCluesByLevelResp_ClueLevel int32

const (
	CountCluesByLevelResp_ClueLevelUnknown CountCluesByLevelResp_ClueLevel = 0
	CountCluesByLevelResp_ClueLevelLow     CountCluesByLevelResp_ClueLevel = 1
	CountCluesByLevelResp_ClueLevelMedium  CountCluesByLevelResp_ClueLevel = 2
	CountCluesByLevelResp_ClueLevelHigh    CountCluesByLevelResp_ClueLevel = 3
)

// Enum value maps for CountCluesByLevelResp_ClueLevel.
var (
	CountCluesByLevelResp_ClueLevel_name = map[int32]string{
		0: "ClueLevelUnknown",
		1: "ClueLevelLow",
		2: "ClueLevelMedium",
		3: "ClueLevelHigh",
	}
	CountCluesByLevelResp_ClueLevel_value = map[string]int32{
		"ClueLevelUnknown": 0,
		"ClueLevelLow":     1,
		"ClueLevelMedium":  2,
		"ClueLevelHigh":    3,
	}
)

func (x CountCluesByLevelResp_ClueLevel) Enum() *CountCluesByLevelResp_ClueLevel {
	p := new(CountCluesByLevelResp_ClueLevel)
	*p = x
	return p
}

func (x CountCluesByLevelResp_ClueLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CountCluesByLevelResp_ClueLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_dashboard_proto_enumTypes[0].Descriptor()
}

func (CountCluesByLevelResp_ClueLevel) Type() protoreflect.EnumType {
	return &file_conan_dashboard_proto_enumTypes[0]
}

func (x CountCluesByLevelResp_ClueLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CountCluesByLevelResp_ClueLevel.Descriptor instead.
func (CountCluesByLevelResp_ClueLevel) EnumDescriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{14, 0}
}

type CountCluesByLevelResp_ClueKind int32

const (
	// 未指定类型
	CountCluesByLevelResp_CLUE_KIND_UNSPECIFIED CountCluesByLevelResp_ClueKind = 0
	// APT攻击
	CountCluesByLevelResp_CLUE_KIND_APT CountCluesByLevelResp_ClueKind = 1
	// 勒索攻击
	CountCluesByLevelResp_CLUE_KIND_EXTORTION CountCluesByLevelResp_ClueKind = 2
	// 挖矿
	CountCluesByLevelResp_CLUE_KIND_MINING CountCluesByLevelResp_ClueKind = 3
	// 僵尸网络
	CountCluesByLevelResp_CLUE_KIND_ZOMBIE CountCluesByLevelResp_ClueKind = 4
	// 漏洞利用
	CountCluesByLevelResp_CLUE_KIND_USE_LEAK CountCluesByLevelResp_ClueKind = 5
	// 后门
	CountCluesByLevelResp_CLUE_KIND_BACKDOOR CountCluesByLevelResp_ClueKind = 6
	// 间谍软件
	CountCluesByLevelResp_CLUE_KIND_SPY CountCluesByLevelResp_ClueKind = 7
	// 外联
	CountCluesByLevelResp_CLUE_KIND_OUTREACH CountCluesByLevelResp_ClueKind = 8
	// 病毒
	CountCluesByLevelResp_CLUE_KIND_VIRUS CountCluesByLevelResp_ClueKind = 9
	// 蠕虫
	CountCluesByLevelResp_CLUE_KIND_WORM CountCluesByLevelResp_ClueKind = 10
	// 脚本
	CountCluesByLevelResp_CLUE_KIND_SCRIPT CountCluesByLevelResp_ClueKind = 11
	// 木马
	CountCluesByLevelResp_CLUE_KIND_TROJANS CountCluesByLevelResp_ClueKind = 12
	// 其他
	CountCluesByLevelResp_CLUE_KIND_OTHER CountCluesByLevelResp_ClueKind = 13
)

// Enum value maps for CountCluesByLevelResp_ClueKind.
var (
	CountCluesByLevelResp_ClueKind_name = map[int32]string{
		0:  "CLUE_KIND_UNSPECIFIED",
		1:  "CLUE_KIND_APT",
		2:  "CLUE_KIND_EXTORTION",
		3:  "CLUE_KIND_MINING",
		4:  "CLUE_KIND_ZOMBIE",
		5:  "CLUE_KIND_USE_LEAK",
		6:  "CLUE_KIND_BACKDOOR",
		7:  "CLUE_KIND_SPY",
		8:  "CLUE_KIND_OUTREACH",
		9:  "CLUE_KIND_VIRUS",
		10: "CLUE_KIND_WORM",
		11: "CLUE_KIND_SCRIPT",
		12: "CLUE_KIND_TROJANS",
		13: "CLUE_KIND_OTHER",
	}
	CountCluesByLevelResp_ClueKind_value = map[string]int32{
		"CLUE_KIND_UNSPECIFIED": 0,
		"CLUE_KIND_APT":         1,
		"CLUE_KIND_EXTORTION":   2,
		"CLUE_KIND_MINING":      3,
		"CLUE_KIND_ZOMBIE":      4,
		"CLUE_KIND_USE_LEAK":    5,
		"CLUE_KIND_BACKDOOR":    6,
		"CLUE_KIND_SPY":         7,
		"CLUE_KIND_OUTREACH":    8,
		"CLUE_KIND_VIRUS":       9,
		"CLUE_KIND_WORM":        10,
		"CLUE_KIND_SCRIPT":      11,
		"CLUE_KIND_TROJANS":     12,
		"CLUE_KIND_OTHER":       13,
	}
)

func (x CountCluesByLevelResp_ClueKind) Enum() *CountCluesByLevelResp_ClueKind {
	p := new(CountCluesByLevelResp_ClueKind)
	*p = x
	return p
}

func (x CountCluesByLevelResp_ClueKind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CountCluesByLevelResp_ClueKind) Descriptor() protoreflect.EnumDescriptor {
	return file_conan_dashboard_proto_enumTypes[1].Descriptor()
}

func (CountCluesByLevelResp_ClueKind) Type() protoreflect.EnumType {
	return &file_conan_dashboard_proto_enumTypes[1]
}

func (x CountCluesByLevelResp_ClueKind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CountCluesByLevelResp_ClueKind.Descriptor instead.
func (CountCluesByLevelResp_ClueKind) EnumDescriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{14, 1}
}

type GetTerminalStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetTerminalStatsRequest) Reset() {
	*x = GetTerminalStatsRequest{}
	mi := &file_conan_dashboard_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTerminalStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTerminalStatsRequest) ProtoMessage() {}

func (x *GetTerminalStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTerminalStatsRequest.ProtoReflect.Descriptor instead.
func (*GetTerminalStatsRequest) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{0}
}

func (x *GetTerminalStatsRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type TerminalStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total      int32   `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Online     int32   `protobuf:"varint,2,opt,name=online,proto3" json:"online,omitempty"`
	Offline    int32   `protobuf:"varint,3,opt,name=offline,proto3" json:"offline,omitempty"`
	Risk       int32   `protobuf:"varint,4,opt,name=risk,proto3" json:"risk,omitempty"`
	OnlineRate float64 `protobuf:"fixed64,5,opt,name=online_rate,json=onlineRate,proto3" json:"online_rate,omitempty"`
}

func (x *TerminalStats) Reset() {
	*x = TerminalStats{}
	mi := &file_conan_dashboard_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TerminalStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TerminalStats) ProtoMessage() {}

func (x *TerminalStats) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TerminalStats.ProtoReflect.Descriptor instead.
func (*TerminalStats) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{1}
}

func (x *TerminalStats) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *TerminalStats) GetOnline() int32 {
	if x != nil {
		return x.Online
	}
	return 0
}

func (x *TerminalStats) GetOffline() int32 {
	if x != nil {
		return x.Offline
	}
	return 0
}

func (x *TerminalStats) GetRisk() int32 {
	if x != nil {
		return x.Risk
	}
	return 0
}

func (x *TerminalStats) GetOnlineRate() float64 {
	if x != nil {
		return x.OnlineRate
	}
	return 0
}

type RiskTerminal struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MachineId   string `protobuf:"bytes,2,opt,name=machine_id,json=machineId,proto3" json:"machine_id,omitempty"`
	Ip          string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Name        string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Group       string `protobuf:"bytes,5,opt,name=group,proto3" json:"group,omitempty"`
	ThreatCount int32  `protobuf:"varint,6,opt,name=threat_count,json=threatCount,proto3" json:"threat_count,omitempty"`
}

func (x *RiskTerminal) Reset() {
	*x = RiskTerminal{}
	mi := &file_conan_dashboard_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RiskTerminal) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskTerminal) ProtoMessage() {}

func (x *RiskTerminal) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskTerminal.ProtoReflect.Descriptor instead.
func (*RiskTerminal) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{2}
}

func (x *RiskTerminal) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RiskTerminal) GetMachineId() string {
	if x != nil {
		return x.MachineId
	}
	return ""
}

func (x *RiskTerminal) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *RiskTerminal) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RiskTerminal) GetGroup() string {
	if x != nil {
		return x.Group
	}
	return ""
}

func (x *RiskTerminal) GetThreatCount() int32 {
	if x != nil {
		return x.ThreatCount
	}
	return 0
}

type ListTopRiskTerminalsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *ListTopRiskTerminalsRequest) Reset() {
	*x = ListTopRiskTerminalsRequest{}
	mi := &file_conan_dashboard_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopRiskTerminalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopRiskTerminalsRequest) ProtoMessage() {}

func (x *ListTopRiskTerminalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopRiskTerminalsRequest.ProtoReflect.Descriptor instead.
func (*ListTopRiskTerminalsRequest) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{3}
}

func (x *ListTopRiskTerminalsRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type ListTopRiskTerminalsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Terminals []*RiskTerminal `protobuf:"bytes,1,rep,name=terminals,proto3" json:"terminals,omitempty"`
}

func (x *ListTopRiskTerminalsResponse) Reset() {
	*x = ListTopRiskTerminalsResponse{}
	mi := &file_conan_dashboard_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopRiskTerminalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopRiskTerminalsResponse) ProtoMessage() {}

func (x *ListTopRiskTerminalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopRiskTerminalsResponse.ProtoReflect.Descriptor instead.
func (*ListTopRiskTerminalsResponse) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{4}
}

func (x *ListTopRiskTerminalsResponse) GetTerminals() []*RiskTerminal {
	if x != nil {
		return x.Terminals
	}
	return nil
}

type ThreatTrendItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day         string          `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"` // format example: 2025-01-01
	LevelStats  map[int32]int32 `protobuf:"bytes,2,rep,name=level_stats,json=levelStats,proto3" json:"level_stats,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ThreatCount int32           `protobuf:"varint,3,opt,name=threat_count,json=threatCount,proto3" json:"threat_count,omitempty"`
}

func (x *ThreatTrendItem) Reset() {
	*x = ThreatTrendItem{}
	mi := &file_conan_dashboard_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ThreatTrendItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ThreatTrendItem) ProtoMessage() {}

func (x *ThreatTrendItem) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ThreatTrendItem.ProtoReflect.Descriptor instead.
func (*ThreatTrendItem) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{5}
}

func (x *ThreatTrendItem) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *ThreatTrendItem) GetLevelStats() map[int32]int32 {
	if x != nil {
		return x.LevelStats
	}
	return nil
}

func (x *ThreatTrendItem) GetThreatCount() int32 {
	if x != nil {
		return x.ThreatCount
	}
	return 0
}

type ListRecentThreatTrendsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *ListRecentThreatTrendsRequest) Reset() {
	*x = ListRecentThreatTrendsRequest{}
	mi := &file_conan_dashboard_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRecentThreatTrendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecentThreatTrendsRequest) ProtoMessage() {}

func (x *ListRecentThreatTrendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecentThreatTrendsRequest.ProtoReflect.Descriptor instead.
func (*ListRecentThreatTrendsRequest) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{6}
}

func (x *ListRecentThreatTrendsRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type ListRecentThreatTrendsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trends []*ThreatTrendItem `protobuf:"bytes,1,rep,name=trends,proto3" json:"trends,omitempty"`
}

func (x *ListRecentThreatTrendsResponse) Reset() {
	*x = ListRecentThreatTrendsResponse{}
	mi := &file_conan_dashboard_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRecentThreatTrendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecentThreatTrendsResponse) ProtoMessage() {}

func (x *ListRecentThreatTrendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecentThreatTrendsResponse.ProtoReflect.Descriptor instead.
func (*ListRecentThreatTrendsResponse) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{7}
}

func (x *ListRecentThreatTrendsResponse) GetTrends() []*ThreatTrendItem {
	if x != nil {
		return x.Trends
	}
	return nil
}

type GetFileArchiveStatsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetFileArchiveStatsRequest) Reset() {
	*x = GetFileArchiveStatsRequest{}
	mi := &file_conan_dashboard_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileArchiveStatsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileArchiveStatsRequest) ProtoMessage() {}

func (x *GetFileArchiveStatsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileArchiveStatsRequest.ProtoReflect.Descriptor instead.
func (*GetFileArchiveStatsRequest) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{8}
}

func (x *GetFileArchiveStatsRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type FileArchiveStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Black   int32 `protobuf:"varint,1,opt,name=black,proto3" json:"black,omitempty"`
	White   int32 `protobuf:"varint,2,opt,name=white,proto3" json:"white,omitempty"`
	Gray    int32 `protobuf:"varint,3,opt,name=gray,proto3" json:"gray,omitempty"`
	Unknown int32 `protobuf:"varint,4,opt,name=unknown,proto3" json:"unknown,omitempty"`
}

func (x *FileArchiveStats) Reset() {
	*x = FileArchiveStats{}
	mi := &file_conan_dashboard_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileArchiveStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileArchiveStats) ProtoMessage() {}

func (x *FileArchiveStats) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileArchiveStats.ProtoReflect.Descriptor instead.
func (*FileArchiveStats) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{9}
}

func (x *FileArchiveStats) GetBlack() int32 {
	if x != nil {
		return x.Black
	}
	return 0
}

func (x *FileArchiveStats) GetWhite() int32 {
	if x != nil {
		return x.White
	}
	return 0
}

func (x *FileArchiveStats) GetGray() int32 {
	if x != nil {
		return x.Gray
	}
	return 0
}

func (x *FileArchiveStats) GetUnknown() int32 {
	if x != nil {
		return x.Unknown
	}
	return 0
}

type ListRecentOutreachTrendsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *ListRecentOutreachTrendsRequest) Reset() {
	*x = ListRecentOutreachTrendsRequest{}
	mi := &file_conan_dashboard_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRecentOutreachTrendsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecentOutreachTrendsRequest) ProtoMessage() {}

func (x *ListRecentOutreachTrendsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecentOutreachTrendsRequest.ProtoReflect.Descriptor instead.
func (*ListRecentOutreachTrendsRequest) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{10}
}

func (x *ListRecentOutreachTrendsRequest) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type ListRecentOutreachTrendsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Trends []*OutreachTrendItem `protobuf:"bytes,1,rep,name=trends,proto3" json:"trends,omitempty"`
}

func (x *ListRecentOutreachTrendsResponse) Reset() {
	*x = ListRecentOutreachTrendsResponse{}
	mi := &file_conan_dashboard_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRecentOutreachTrendsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRecentOutreachTrendsResponse) ProtoMessage() {}

func (x *ListRecentOutreachTrendsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRecentOutreachTrendsResponse.ProtoReflect.Descriptor instead.
func (*ListRecentOutreachTrendsResponse) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{11}
}

func (x *ListRecentOutreachTrendsResponse) GetTrends() []*OutreachTrendItem {
	if x != nil {
		return x.Trends
	}
	return nil
}

type OutreachTrendItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Day          string `protobuf:"bytes,1,opt,name=day,proto3" json:"day,omitempty"` // format example: 2025-01-01
	TotalCount   int32  `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	IllegalCount int32  `protobuf:"varint,3,opt,name=illegal_count,json=illegalCount,proto3" json:"illegal_count,omitempty"`
}

func (x *OutreachTrendItem) Reset() {
	*x = OutreachTrendItem{}
	mi := &file_conan_dashboard_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutreachTrendItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutreachTrendItem) ProtoMessage() {}

func (x *OutreachTrendItem) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutreachTrendItem.ProtoReflect.Descriptor instead.
func (*OutreachTrendItem) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{12}
}

func (x *OutreachTrendItem) GetDay() string {
	if x != nil {
		return x.Day
	}
	return ""
}

func (x *OutreachTrendItem) GetTotalCount() int32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *OutreachTrendItem) GetIllegalCount() int32 {
	if x != nil {
		return x.IllegalCount
	}
	return 0
}

type CountCluesByLevelReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *CountCluesByLevelReq) Reset() {
	*x = CountCluesByLevelReq{}
	mi := &file_conan_dashboard_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesByLevelReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesByLevelReq) ProtoMessage() {}

func (x *CountCluesByLevelReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesByLevelReq.ProtoReflect.Descriptor instead.
func (*CountCluesByLevelReq) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{13}
}

func (x *CountCluesByLevelReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type CountCluesByLevelResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalCount int64 `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	// The number of clues by level
	// key: clue level
	LevelCounts map[int32]int64 `protobuf:"bytes,2,rep,name=level_counts,json=levelCounts,proto3" json:"level_counts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// The number of clues by kind
	KindCounts []*CountCluesByLevelResp_ClueKindCount `protobuf:"bytes,3,rep,name=kind_counts,json=kindCounts,proto3" json:"kind_counts,omitempty"`
}

func (x *CountCluesByLevelResp) Reset() {
	*x = CountCluesByLevelResp{}
	mi := &file_conan_dashboard_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesByLevelResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesByLevelResp) ProtoMessage() {}

func (x *CountCluesByLevelResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesByLevelResp.ProtoReflect.Descriptor instead.
func (*CountCluesByLevelResp) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{14}
}

func (x *CountCluesByLevelResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *CountCluesByLevelResp) GetLevelCounts() map[int32]int64 {
	if x != nil {
		return x.LevelCounts
	}
	return nil
}

func (x *CountCluesByLevelResp) GetKindCounts() []*CountCluesByLevelResp_ClueKindCount {
	if x != nil {
		return x.KindCounts
	}
	return nil
}

type CountCluesByTypeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TimeRange *TimeRange `protobuf:"bytes,1,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *CountCluesByTypeReq) Reset() {
	*x = CountCluesByTypeReq{}
	mi := &file_conan_dashboard_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesByTypeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesByTypeReq) ProtoMessage() {}

func (x *CountCluesByTypeReq) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesByTypeReq.ProtoReflect.Descriptor instead.
func (*CountCluesByTypeReq) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{15}
}

func (x *CountCluesByTypeReq) GetTimeRange() *TimeRange {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

type CountCluesByTypeResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Counts     map[int32]int64 `protobuf:"bytes,1,rep,name=counts,proto3" json:"counts,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 避免与ClueTypeCount冲突
	TotalCount int64           `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
}

func (x *CountCluesByTypeResp) Reset() {
	*x = CountCluesByTypeResp{}
	mi := &file_conan_dashboard_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesByTypeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesByTypeResp) ProtoMessage() {}

func (x *CountCluesByTypeResp) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesByTypeResp.ProtoReflect.Descriptor instead.
func (*CountCluesByTypeResp) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{16}
}

func (x *CountCluesByTypeResp) GetCounts() map[int32]int64 {
	if x != nil {
		return x.Counts
	}
	return nil
}

func (x *CountCluesByTypeResp) GetTotalCount() int64 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

type CountCluesByLevelResp_ClueKindCount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Kind             CountCluesByLevelResp_ClueKind `protobuf:"varint,1,opt,name=kind,proto3,enum=conan.CountCluesByLevelResp_ClueKind" json:"kind,omitempty"`
	Count            int64                          `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	LatestCreateTime *timestamppb.Timestamp         `protobuf:"bytes,3,opt,name=latest_create_time,json=latestCreateTime,proto3" json:"latest_create_time,omitempty"`
}

func (x *CountCluesByLevelResp_ClueKindCount) Reset() {
	*x = CountCluesByLevelResp_ClueKindCount{}
	mi := &file_conan_dashboard_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountCluesByLevelResp_ClueKindCount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountCluesByLevelResp_ClueKindCount) ProtoMessage() {}

func (x *CountCluesByLevelResp_ClueKindCount) ProtoReflect() protoreflect.Message {
	mi := &file_conan_dashboard_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountCluesByLevelResp_ClueKindCount.ProtoReflect.Descriptor instead.
func (*CountCluesByLevelResp_ClueKindCount) Descriptor() ([]byte, []int) {
	return file_conan_dashboard_proto_rawDescGZIP(), []int{14, 0}
}

func (x *CountCluesByLevelResp_ClueKindCount) GetKind() CountCluesByLevelResp_ClueKind {
	if x != nil {
		return x.Kind
	}
	return CountCluesByLevelResp_CLUE_KIND_UNSPECIFIED
}

func (x *CountCluesByLevelResp_ClueKindCount) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *CountCluesByLevelResp_ClueKindCount) GetLatestCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LatestCreateTime
	}
	return nil
}

var File_conan_dashboard_proto protoreflect.FileDescriptor

var file_conan_dashboard_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x12, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22,
	0x8c, 0x01, 0x0a, 0x0d, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x07, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x69, 0x73,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x72, 0x69, 0x73, 0x6b, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0a, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x61, 0x74, 0x65, 0x22, 0x9a,
	0x01, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x68, 0x72, 0x65,
	0x61, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b,
	0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x4e, 0x0a, 0x1b, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x51, 0x0a, 0x1c, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x09, 0x74,
	0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69,
	0x6e, 0x61, 0x6c, 0x52, 0x09, 0x74, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x22, 0xce,
	0x01, 0x0a, 0x0f, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x64, 0x61, 0x79, 0x12, 0x47, 0x0a, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65,
	0x6d, 0x2e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0a, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x74, 0x68, 0x72, 0x65, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x1a, 0x3d, 0x0a, 0x0f, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x50, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x54, 0x68, 0x72,
	0x65, 0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x22, 0x50, 0x0a, 0x1e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x06, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x68, 0x72, 0x65,
	0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x74, 0x72, 0x65,
	0x6e, 0x64, 0x73, 0x22, 0x4d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72,
	0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x22, 0x6c, 0x0a, 0x10, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68, 0x69, 0x76,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x12, 0x14, 0x0a, 0x05,
	0x77, 0x68, 0x69, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x77, 0x68, 0x69,
	0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x72, 0x61, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x67, 0x72, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x22, 0x52, 0x0a, 0x1f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x22, 0x54, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x74, 0x72, 0x65, 0x6e,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e,
	0x2e, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x06, 0x74, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x22, 0x6b, 0x0a, 0x11, 0x4f, 0x75,
	0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x10, 0x0a, 0x03, 0x64, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x61,
	0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6c, 0x6c, 0x65, 0x67, 0x61, 0x6c, 0x5f, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x69, 0x6c, 0x6c, 0x65, 0x67,
	0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x47, 0x0a, 0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x22, 0xe7, 0x06, 0x0a, 0x15, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42,
	0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x50, 0x0a, 0x0c, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2d, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43,
	0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x2e,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0b, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x4b, 0x0a,
	0x0b, 0x6b, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x2e, 0x43, 0x6c, 0x75, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0a,
	0x6b, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0xaa, 0x01, 0x0a, 0x0d, 0x43,
	0x6c, 0x75, 0x65, 0x4b, 0x69, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x04,
	0x6b, 0x69, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x6c, 0x75, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x52, 0x04, 0x6b, 0x69, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x48, 0x0a,
	0x12, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x3e, 0x0a, 0x10, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5b, 0x0a, 0x09, 0x43, 0x6c, 0x75, 0x65, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43, 0x6c,
	0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4c, 0x6f, 0x77, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x43, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x10,
	0x02, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x69,
	0x67, 0x68, 0x10, 0x03, 0x22, 0xc3, 0x02, 0x0a, 0x08, 0x43, 0x6c, 0x75, 0x65, 0x4b, 0x69, 0x6e,
	0x64, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d,
	0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x41, 0x50, 0x54, 0x10, 0x01, 0x12,
	0x17, 0x0a, 0x13, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x45, 0x58, 0x54,
	0x4f, 0x52, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4c, 0x55, 0x45,
	0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4d, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x14,
	0x0a, 0x10, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x5a, 0x4f, 0x4d, 0x42,
	0x49, 0x45, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x4c, 0x45, 0x41, 0x4b, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12,
	0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x44, 0x4f,
	0x4f, 0x52, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x53, 0x50, 0x59, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4c, 0x55, 0x45, 0x5f,
	0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x52, 0x45, 0x41, 0x43, 0x48, 0x10, 0x08, 0x12,
	0x13, 0x0a, 0x0f, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x56, 0x49, 0x52,
	0x55, 0x53, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e,
	0x44, 0x5f, 0x57, 0x4f, 0x52, 0x4d, 0x10, 0x0a, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4c, 0x55, 0x45,
	0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x10, 0x0b, 0x12, 0x15,
	0x0a, 0x11, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49, 0x4e, 0x44, 0x5f, 0x54, 0x52, 0x4f, 0x4a,
	0x41, 0x4e, 0x53, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x4c, 0x55, 0x45, 0x5f, 0x4b, 0x49,
	0x4e, 0x44, 0x5f, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x10, 0x0d, 0x22, 0x46, 0x0a, 0x13, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x2f, 0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x06, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x39, 0x0a,
	0x0b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0x8b, 0x05, 0x0a, 0x10, 0x44, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4a, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x73, 0x12, 0x1e, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x72,
	0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x14, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e,
	0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x00, 0x12, 0x61, 0x0a, 0x14, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c,
	0x73, 0x12, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f,
	0x70, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x6b, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x24, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x54, 0x68, 0x72, 0x65, 0x61, 0x74, 0x54,
	0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x54,
	0x68, 0x72, 0x65, 0x61, 0x74, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x41, 0x72, 0x63, 0x68, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x21, 0x2e, 0x63,
	0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68,
	0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x17, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x41, 0x72, 0x63, 0x68,
	0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x73, 0x22, 0x00, 0x12, 0x6d, 0x0a, 0x18, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68,
	0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x12, 0x26, 0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63,
	0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x63, 0x65, 0x6e,
	0x74, 0x4f, 0x75, 0x74, 0x72, 0x65, 0x61, 0x63, 0x68, 0x54, 0x72, 0x65, 0x6e, 0x64, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4e, 0x0a, 0x11, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1b,
	0x2e, 0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65,
	0x73, 0x42, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x63, 0x6f,
	0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x12, 0x4b, 0x0a, 0x10, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x2e,
	0x63, 0x6f, 0x6e, 0x61, 0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73,
	0x42, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x61,
	0x6e, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x75, 0x65, 0x73, 0x42, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x42, 0x2a, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x2e, 0x61, 0x6e,
	0x78, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x76, 0x30, 0x31, 0x2d, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x2f, 0x76, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x6b, 0x67, 0x2f, 0x63, 0x6f, 0x6e,
	0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_conan_dashboard_proto_rawDescOnce sync.Once
	file_conan_dashboard_proto_rawDescData = file_conan_dashboard_proto_rawDesc
)

func file_conan_dashboard_proto_rawDescGZIP() []byte {
	file_conan_dashboard_proto_rawDescOnce.Do(func() {
		file_conan_dashboard_proto_rawDescData = protoimpl.X.CompressGZIP(file_conan_dashboard_proto_rawDescData)
	})
	return file_conan_dashboard_proto_rawDescData
}

var file_conan_dashboard_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_conan_dashboard_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_conan_dashboard_proto_goTypes = []any{
	(CountCluesByLevelResp_ClueLevel)(0),        // 0: conan.CountCluesByLevelResp.ClueLevel
	(CountCluesByLevelResp_ClueKind)(0),         // 1: conan.CountCluesByLevelResp.ClueKind
	(*GetTerminalStatsRequest)(nil),             // 2: conan.GetTerminalStatsRequest
	(*TerminalStats)(nil),                       // 3: conan.TerminalStats
	(*RiskTerminal)(nil),                        // 4: conan.RiskTerminal
	(*ListTopRiskTerminalsRequest)(nil),         // 5: conan.ListTopRiskTerminalsRequest
	(*ListTopRiskTerminalsResponse)(nil),        // 6: conan.ListTopRiskTerminalsResponse
	(*ThreatTrendItem)(nil),                     // 7: conan.ThreatTrendItem
	(*ListRecentThreatTrendsRequest)(nil),       // 8: conan.ListRecentThreatTrendsRequest
	(*ListRecentThreatTrendsResponse)(nil),      // 9: conan.ListRecentThreatTrendsResponse
	(*GetFileArchiveStatsRequest)(nil),          // 10: conan.GetFileArchiveStatsRequest
	(*FileArchiveStats)(nil),                    // 11: conan.FileArchiveStats
	(*ListRecentOutreachTrendsRequest)(nil),     // 12: conan.ListRecentOutreachTrendsRequest
	(*ListRecentOutreachTrendsResponse)(nil),    // 13: conan.ListRecentOutreachTrendsResponse
	(*OutreachTrendItem)(nil),                   // 14: conan.OutreachTrendItem
	(*CountCluesByLevelReq)(nil),                // 15: conan.CountCluesByLevelReq
	(*CountCluesByLevelResp)(nil),               // 16: conan.CountCluesByLevelResp
	(*CountCluesByTypeReq)(nil),                 // 17: conan.CountCluesByTypeReq
	(*CountCluesByTypeResp)(nil),                // 18: conan.CountCluesByTypeResp
	nil,                                         // 19: conan.ThreatTrendItem.LevelStatsEntry
	(*CountCluesByLevelResp_ClueKindCount)(nil), // 20: conan.CountCluesByLevelResp.ClueKindCount
	nil,                           // 21: conan.CountCluesByLevelResp.LevelCountsEntry
	nil,                           // 22: conan.CountCluesByTypeResp.CountsEntry
	(*TimeRange)(nil),             // 23: conan.TimeRange
	(*timestamppb.Timestamp)(nil), // 24: google.protobuf.Timestamp
}
var file_conan_dashboard_proto_depIdxs = []int32{
	23, // 0: conan.GetTerminalStatsRequest.time_range:type_name -> conan.TimeRange
	23, // 1: conan.ListTopRiskTerminalsRequest.time_range:type_name -> conan.TimeRange
	4,  // 2: conan.ListTopRiskTerminalsResponse.terminals:type_name -> conan.RiskTerminal
	19, // 3: conan.ThreatTrendItem.level_stats:type_name -> conan.ThreatTrendItem.LevelStatsEntry
	23, // 4: conan.ListRecentThreatTrendsRequest.time_range:type_name -> conan.TimeRange
	7,  // 5: conan.ListRecentThreatTrendsResponse.trends:type_name -> conan.ThreatTrendItem
	23, // 6: conan.GetFileArchiveStatsRequest.time_range:type_name -> conan.TimeRange
	23, // 7: conan.ListRecentOutreachTrendsRequest.time_range:type_name -> conan.TimeRange
	14, // 8: conan.ListRecentOutreachTrendsResponse.trends:type_name -> conan.OutreachTrendItem
	23, // 9: conan.CountCluesByLevelReq.time_range:type_name -> conan.TimeRange
	21, // 10: conan.CountCluesByLevelResp.level_counts:type_name -> conan.CountCluesByLevelResp.LevelCountsEntry
	20, // 11: conan.CountCluesByLevelResp.kind_counts:type_name -> conan.CountCluesByLevelResp.ClueKindCount
	23, // 12: conan.CountCluesByTypeReq.time_range:type_name -> conan.TimeRange
	22, // 13: conan.CountCluesByTypeResp.counts:type_name -> conan.CountCluesByTypeResp.CountsEntry
	1,  // 14: conan.CountCluesByLevelResp.ClueKindCount.kind:type_name -> conan.CountCluesByLevelResp.ClueKind
	24, // 15: conan.CountCluesByLevelResp.ClueKindCount.latest_create_time:type_name -> google.protobuf.Timestamp
	2,  // 16: conan.DashboardService.GetTerminalStats:input_type -> conan.GetTerminalStatsRequest
	5,  // 17: conan.DashboardService.ListTopRiskTerminals:input_type -> conan.ListTopRiskTerminalsRequest
	8,  // 18: conan.DashboardService.ListRecentThreatTrends:input_type -> conan.ListRecentThreatTrendsRequest
	10, // 19: conan.DashboardService.GetFileArchiveStats:input_type -> conan.GetFileArchiveStatsRequest
	12, // 20: conan.DashboardService.ListRecentOutreachTrends:input_type -> conan.ListRecentOutreachTrendsRequest
	15, // 21: conan.DashboardService.CountCluesByLevel:input_type -> conan.CountCluesByLevelReq
	17, // 22: conan.DashboardService.CountCluesByType:input_type -> conan.CountCluesByTypeReq
	3,  // 23: conan.DashboardService.GetTerminalStats:output_type -> conan.TerminalStats
	6,  // 24: conan.DashboardService.ListTopRiskTerminals:output_type -> conan.ListTopRiskTerminalsResponse
	9,  // 25: conan.DashboardService.ListRecentThreatTrends:output_type -> conan.ListRecentThreatTrendsResponse
	11, // 26: conan.DashboardService.GetFileArchiveStats:output_type -> conan.FileArchiveStats
	13, // 27: conan.DashboardService.ListRecentOutreachTrends:output_type -> conan.ListRecentOutreachTrendsResponse
	16, // 28: conan.DashboardService.CountCluesByLevel:output_type -> conan.CountCluesByLevelResp
	18, // 29: conan.DashboardService.CountCluesByType:output_type -> conan.CountCluesByTypeResp
	23, // [23:30] is the sub-list for method output_type
	16, // [16:23] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_conan_dashboard_proto_init() }
func file_conan_dashboard_proto_init() {
	if File_conan_dashboard_proto != nil {
		return
	}
	file_conan_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_conan_dashboard_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_conan_dashboard_proto_goTypes,
		DependencyIndexes: file_conan_dashboard_proto_depIdxs,
		EnumInfos:         file_conan_dashboard_proto_enumTypes,
		MessageInfos:      file_conan_dashboard_proto_msgTypes,
	}.Build()
	File_conan_dashboard_proto = out.File
	file_conan_dashboard_proto_rawDesc = nil
	file_conan_dashboard_proto_goTypes = nil
	file_conan_dashboard_proto_depIdxs = nil
}
