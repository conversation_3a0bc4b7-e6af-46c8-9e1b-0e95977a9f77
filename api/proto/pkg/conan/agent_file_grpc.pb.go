// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.3
// source: conan/agent_file.proto

package conan

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AgentFileService_HandleAgentFile_FullMethodName           = "/conan.AgentFileService/HandleAgentFile"
	AgentFileService_BatchHandleAgentFile_FullMethodName      = "/conan.AgentFileService/BatchHandleAgentFile"
	AgentFileService_RetryHandleAgentFile_FullMethodName      = "/conan.AgentFileService/RetryHandleAgentFile"
	AgentFileService_ListOperatedThreatenFiles_FullMethodName = "/conan.AgentFileService/ListOperatedThreatenFiles"
	AgentFileService_BatchDeleteFileOperations_FullMethodName = "/conan.AgentFileService/BatchDeleteFileOperations"
	AgentFileService_ListAgentFileOperations_FullMethodName   = "/conan.AgentFileService/ListAgentFileOperations"
)

// AgentFileServiceClient is the client API for AgentFileService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AgentFileService is the service for agent file.
// The file operation includes: Isolate, Delete, Restore, Expire
type AgentFileServiceClient interface {
	// HandleAgentFile handles the agent file.
	HandleAgentFile(ctx context.Context, in *HandleAgentFileReq, opts ...grpc.CallOption) (*HandleAgentFileResp, error)
	// BatchHandleAgentFile handles the agent file in batch.
	BatchHandleAgentFile(ctx context.Context, in *BatchHandleAgentFileReq, opts ...grpc.CallOption) (*BatchHandleAgentFileResp, error)
	// RetryHandleAgentFile retries to handle the agent file.
	RetryHandleAgentFile(ctx context.Context, in *RetryHandleAgentFileReq, opts ...grpc.CallOption) (*RetryHandleAgentFileResp, error)
	// ListOperatedThreatenFiles lists the operated threaten files.
	ListOperatedThreatenFiles(ctx context.Context, in *ListOperatedThreatenFileReq, opts ...grpc.CallOption) (*ListOperatedThreatenFileResp, error)
	// BatchDeleteFileOperations deletes the file operation records in batch.
	BatchDeleteFileOperations(ctx context.Context, in *BatchDeleteFileOperationsReq, opts ...grpc.CallOption) (*BatchDeleteFileOperationsResp, error)
	// ListAgentFileOperations lists the agent file operations.
	ListAgentFileOperations(ctx context.Context, in *ListAgentFileOperationReq, opts ...grpc.CallOption) (*ListAgentFileOperationResp, error)
}

type agentFileServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentFileServiceClient(cc grpc.ClientConnInterface) AgentFileServiceClient {
	return &agentFileServiceClient{cc}
}

func (c *agentFileServiceClient) HandleAgentFile(ctx context.Context, in *HandleAgentFileReq, opts ...grpc.CallOption) (*HandleAgentFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleAgentFileResp)
	err := c.cc.Invoke(ctx, AgentFileService_HandleAgentFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentFileServiceClient) BatchHandleAgentFile(ctx context.Context, in *BatchHandleAgentFileReq, opts ...grpc.CallOption) (*BatchHandleAgentFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchHandleAgentFileResp)
	err := c.cc.Invoke(ctx, AgentFileService_BatchHandleAgentFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentFileServiceClient) RetryHandleAgentFile(ctx context.Context, in *RetryHandleAgentFileReq, opts ...grpc.CallOption) (*RetryHandleAgentFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RetryHandleAgentFileResp)
	err := c.cc.Invoke(ctx, AgentFileService_RetryHandleAgentFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentFileServiceClient) ListOperatedThreatenFiles(ctx context.Context, in *ListOperatedThreatenFileReq, opts ...grpc.CallOption) (*ListOperatedThreatenFileResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListOperatedThreatenFileResp)
	err := c.cc.Invoke(ctx, AgentFileService_ListOperatedThreatenFiles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentFileServiceClient) BatchDeleteFileOperations(ctx context.Context, in *BatchDeleteFileOperationsReq, opts ...grpc.CallOption) (*BatchDeleteFileOperationsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchDeleteFileOperationsResp)
	err := c.cc.Invoke(ctx, AgentFileService_BatchDeleteFileOperations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentFileServiceClient) ListAgentFileOperations(ctx context.Context, in *ListAgentFileOperationReq, opts ...grpc.CallOption) (*ListAgentFileOperationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAgentFileOperationResp)
	err := c.cc.Invoke(ctx, AgentFileService_ListAgentFileOperations_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentFileServiceServer is the server API for AgentFileService service.
// All implementations must embed UnimplementedAgentFileServiceServer
// for forward compatibility.
//
// AgentFileService is the service for agent file.
// The file operation includes: Isolate, Delete, Restore, Expire
type AgentFileServiceServer interface {
	// HandleAgentFile handles the agent file.
	HandleAgentFile(context.Context, *HandleAgentFileReq) (*HandleAgentFileResp, error)
	// BatchHandleAgentFile handles the agent file in batch.
	BatchHandleAgentFile(context.Context, *BatchHandleAgentFileReq) (*BatchHandleAgentFileResp, error)
	// RetryHandleAgentFile retries to handle the agent file.
	RetryHandleAgentFile(context.Context, *RetryHandleAgentFileReq) (*RetryHandleAgentFileResp, error)
	// ListOperatedThreatenFiles lists the operated threaten files.
	ListOperatedThreatenFiles(context.Context, *ListOperatedThreatenFileReq) (*ListOperatedThreatenFileResp, error)
	// BatchDeleteFileOperations deletes the file operation records in batch.
	BatchDeleteFileOperations(context.Context, *BatchDeleteFileOperationsReq) (*BatchDeleteFileOperationsResp, error)
	// ListAgentFileOperations lists the agent file operations.
	ListAgentFileOperations(context.Context, *ListAgentFileOperationReq) (*ListAgentFileOperationResp, error)
	mustEmbedUnimplementedAgentFileServiceServer()
}

// UnimplementedAgentFileServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAgentFileServiceServer struct{}

func (UnimplementedAgentFileServiceServer) HandleAgentFile(context.Context, *HandleAgentFileReq) (*HandleAgentFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleAgentFile not implemented")
}
func (UnimplementedAgentFileServiceServer) BatchHandleAgentFile(context.Context, *BatchHandleAgentFileReq) (*BatchHandleAgentFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchHandleAgentFile not implemented")
}
func (UnimplementedAgentFileServiceServer) RetryHandleAgentFile(context.Context, *RetryHandleAgentFileReq) (*RetryHandleAgentFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryHandleAgentFile not implemented")
}
func (UnimplementedAgentFileServiceServer) ListOperatedThreatenFiles(context.Context, *ListOperatedThreatenFileReq) (*ListOperatedThreatenFileResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListOperatedThreatenFiles not implemented")
}
func (UnimplementedAgentFileServiceServer) BatchDeleteFileOperations(context.Context, *BatchDeleteFileOperationsReq) (*BatchDeleteFileOperationsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchDeleteFileOperations not implemented")
}
func (UnimplementedAgentFileServiceServer) ListAgentFileOperations(context.Context, *ListAgentFileOperationReq) (*ListAgentFileOperationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAgentFileOperations not implemented")
}
func (UnimplementedAgentFileServiceServer) mustEmbedUnimplementedAgentFileServiceServer() {}
func (UnimplementedAgentFileServiceServer) testEmbeddedByValue()                          {}

// UnsafeAgentFileServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentFileServiceServer will
// result in compilation errors.
type UnsafeAgentFileServiceServer interface {
	mustEmbedUnimplementedAgentFileServiceServer()
}

func RegisterAgentFileServiceServer(s grpc.ServiceRegistrar, srv AgentFileServiceServer) {
	// If the following call pancis, it indicates UnimplementedAgentFileServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AgentFileService_ServiceDesc, srv)
}

func _AgentFileService_HandleAgentFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleAgentFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).HandleAgentFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_HandleAgentFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).HandleAgentFile(ctx, req.(*HandleAgentFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentFileService_BatchHandleAgentFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchHandleAgentFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).BatchHandleAgentFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_BatchHandleAgentFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).BatchHandleAgentFile(ctx, req.(*BatchHandleAgentFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentFileService_RetryHandleAgentFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryHandleAgentFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).RetryHandleAgentFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_RetryHandleAgentFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).RetryHandleAgentFile(ctx, req.(*RetryHandleAgentFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentFileService_ListOperatedThreatenFiles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListOperatedThreatenFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).ListOperatedThreatenFiles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_ListOperatedThreatenFiles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).ListOperatedThreatenFiles(ctx, req.(*ListOperatedThreatenFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentFileService_BatchDeleteFileOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteFileOperationsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).BatchDeleteFileOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_BatchDeleteFileOperations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).BatchDeleteFileOperations(ctx, req.(*BatchDeleteFileOperationsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AgentFileService_ListAgentFileOperations_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAgentFileOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentFileServiceServer).ListAgentFileOperations(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AgentFileService_ListAgentFileOperations_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentFileServiceServer).ListAgentFileOperations(ctx, req.(*ListAgentFileOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AgentFileService_ServiceDesc is the grpc.ServiceDesc for AgentFileService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AgentFileService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "conan.AgentFileService",
	HandlerType: (*AgentFileServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "HandleAgentFile",
			Handler:    _AgentFileService_HandleAgentFile_Handler,
		},
		{
			MethodName: "BatchHandleAgentFile",
			Handler:    _AgentFileService_BatchHandleAgentFile_Handler,
		},
		{
			MethodName: "RetryHandleAgentFile",
			Handler:    _AgentFileService_RetryHandleAgentFile_Handler,
		},
		{
			MethodName: "ListOperatedThreatenFiles",
			Handler:    _AgentFileService_ListOperatedThreatenFiles_Handler,
		},
		{
			MethodName: "BatchDeleteFileOperations",
			Handler:    _AgentFileService_BatchDeleteFileOperations_Handler,
		},
		{
			MethodName: "ListAgentFileOperations",
			Handler:    _AgentFileService_ListAgentFileOperations_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conan/agent_file.proto",
}
