// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/common.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on PageRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageRequestMultiError, or
// nil if none found.
func (m *PageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageIndex

	// no validation rules for PageSize

	if len(errors) > 0 {
		return PageRequestMultiError(errors)
	}

	return nil
}

// PageRequestMultiError is an error wrapping multiple validation errors
// returned by PageRequest.ValidateAll() if the designated constraints aren't met.
type PageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageRequestMultiError) AllErrors() []error { return m }

// PageRequestValidationError is the validation error returned by
// PageRequest.Validate if the designated constraints aren't met.
type PageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageRequestValidationError) ErrorName() string { return "PageRequestValidationError" }

// Error satisfies the builtin error interface
func (e PageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageRequestValidationError{}

// Validate checks the field values on PageResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageResponseMultiError, or
// nil if none found.
func (m *PageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PageIndex

	// no validation rules for PageSize

	// no validation rules for Total

	if len(errors) > 0 {
		return PageResponseMultiError(errors)
	}

	return nil
}

// PageResponseMultiError is an error wrapping multiple validation errors
// returned by PageResponse.ValidateAll() if the designated constraints aren't met.
type PageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageResponseMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageResponseMultiError) AllErrors() []error { return m }

// PageResponseValidationError is the validation error returned by
// PageResponse.Validate if the designated constraints aren't met.
type PageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageResponseValidationError) ErrorName() string { return "PageResponseValidationError" }

// Error satisfies the builtin error interface
func (e PageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageResponseValidationError{}

// Validate checks the field values on ListAttackCluesFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAttackCluesFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAttackCluesFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAttackCluesFilterMultiError, or nil if none found.
func (m *ListAttackCluesFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAttackCluesFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetType

	// no validation rules for HitStatus

	// no validation rules for IsApt

	// no validation rules for ClueSubType

	// no validation rules for HasMalicious

	// no validation rules for Ip

	// no validation rules for ClueType

	// no validation rules for ClueLevel

	// no validation rules for DisposeStatus

	// no validation rules for SearchType

	// no validation rules for SearchData

	// no validation rules for FileType

	// no validation rules for Confidence

	// no validation rules for Direction

	// no validation rules for AttackSrc

	// no validation rules for Online

	// no validation rules for FileName

	// no validation rules for OutreachAddress

	// no validation rules for Id

	// no validation rules for Report

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAttackCluesFilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAttackCluesFilterValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAttackCluesFilterValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAttackCluesFilterMultiError(errors)
	}

	return nil
}

// ListAttackCluesFilterMultiError is an error wrapping multiple validation
// errors returned by ListAttackCluesFilter.ValidateAll() if the designated
// constraints aren't met.
type ListAttackCluesFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAttackCluesFilterMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAttackCluesFilterMultiError) AllErrors() []error { return m }

// ListAttackCluesFilterValidationError is the validation error returned by
// ListAttackCluesFilter.Validate if the designated constraints aren't met.
type ListAttackCluesFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAttackCluesFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAttackCluesFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAttackCluesFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAttackCluesFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAttackCluesFilterValidationError) ErrorName() string {
	return "ListAttackCluesFilterValidationError"
}

// Error satisfies the builtin error interface
func (e ListAttackCluesFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAttackCluesFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAttackCluesFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAttackCluesFilterValidationError{}
