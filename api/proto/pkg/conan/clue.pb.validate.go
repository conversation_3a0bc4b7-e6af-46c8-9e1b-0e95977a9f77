// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: conan/clue.proto

package conan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListMemoryAttackCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMemoryAttackCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMemoryAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMemoryAttackCluesReqMultiError, or nil if none found.
func (m *ListMemoryAttackCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMemoryAttackCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMemoryAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMemoryAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMemoryAttackCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMemoryAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMemoryAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMemoryAttackCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMemoryAttackCluesReqMultiError(errors)
	}

	return nil
}

// ListMemoryAttackCluesReqMultiError is an error wrapping multiple validation
// errors returned by ListMemoryAttackCluesReq.ValidateAll() if the designated
// constraints aren't met.
type ListMemoryAttackCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMemoryAttackCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMemoryAttackCluesReqMultiError) AllErrors() []error { return m }

// ListMemoryAttackCluesReqValidationError is the validation error returned by
// ListMemoryAttackCluesReq.Validate if the designated constraints aren't met.
type ListMemoryAttackCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMemoryAttackCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMemoryAttackCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMemoryAttackCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMemoryAttackCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMemoryAttackCluesReqValidationError) ErrorName() string {
	return "ListMemoryAttackCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListMemoryAttackCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMemoryAttackCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMemoryAttackCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMemoryAttackCluesReqValidationError{}

// Validate checks the field values on MemoryAttackClue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MemoryAttackClue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemoryAttackClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemoryAttackClueMultiError, or nil if none found.
func (m *MemoryAttackClue) ValidateAll() error {
	return m.validate(true)
}

func (m *MemoryAttackClue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueLevel

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for Disposition

	// no validation rules for HasIgnore

	// no validation rules for AttackSrc

	// no validation rules for OccurCount

	// no validation rules for ClientVersion

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemoryAttackClueValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemoryAttackClueValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetectedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemoryAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemoryAttackClueValidationError{
				field:  "DetectedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MemoryAttackClueMultiError(errors)
	}

	return nil
}

// MemoryAttackClueMultiError is an error wrapping multiple validation errors
// returned by MemoryAttackClue.ValidateAll() if the designated constraints
// aren't met.
type MemoryAttackClueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemoryAttackClueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemoryAttackClueMultiError) AllErrors() []error { return m }

// MemoryAttackClueValidationError is the validation error returned by
// MemoryAttackClue.Validate if the designated constraints aren't met.
type MemoryAttackClueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemoryAttackClueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemoryAttackClueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemoryAttackClueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemoryAttackClueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemoryAttackClueValidationError) ErrorName() string { return "MemoryAttackClueValidationError" }

// Error satisfies the builtin error interface
func (e MemoryAttackClueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemoryAttackClue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemoryAttackClueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemoryAttackClueValidationError{}

// Validate checks the field values on SystemAttackClue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SystemAttackClue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemAttackClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SystemAttackClueMultiError, or nil if none found.
func (m *SystemAttackClue) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemAttackClue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueLevel

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for Disposition

	// no validation rules for HasIgnore

	// no validation rules for AttackSrc

	// no validation rules for OccurCount

	// no validation rules for ClientVersion

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemAttackClueValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemAttackClueValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetectedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemAttackClueValidationError{
				field:  "DetectedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SystemAttackClueMultiError(errors)
	}

	return nil
}

// SystemAttackClueMultiError is an error wrapping multiple validation errors
// returned by SystemAttackClue.ValidateAll() if the designated constraints
// aren't met.
type SystemAttackClueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemAttackClueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemAttackClueMultiError) AllErrors() []error { return m }

// SystemAttackClueValidationError is the validation error returned by
// SystemAttackClue.Validate if the designated constraints aren't met.
type SystemAttackClueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemAttackClueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemAttackClueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemAttackClueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemAttackClueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemAttackClueValidationError) ErrorName() string { return "SystemAttackClueValidationError" }

// Error satisfies the builtin error interface
func (e SystemAttackClueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemAttackClue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemAttackClueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemAttackClueValidationError{}

// Validate checks the field values on FileThreatClue with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileThreatClue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileThreatClue with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileThreatClueMultiError,
// or nil if none found.
func (m *FileThreatClue) ValidateAll() error {
	return m.validate(true)
}

func (m *FileThreatClue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueLevel

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for Disposition

	// no validation rules for HasIgnore

	// no validation rules for Filename

	// no validation rules for FileType

	// no validation rules for FileMd5

	// no validation rules for FileSha256

	// no validation rules for IsolateCount

	// no validation rules for OperationType

	// no validation rules for OperationStatus

	// no validation rules for OperationFailure

	// no validation rules for CanIsolate

	// no validation rules for AttackSrc

	// no validation rules for OccurCount

	// no validation rules for DisposedCount

	// no validation rules for FileSha256Count

	// no validation rules for ExtraInfo

	// no validation rules for ClientVersion

	// no validation rules for AttackScene

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatClueValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatClueValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetectedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatClueValidationError{
				field:  "DetectedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileThreatClueMultiError(errors)
	}

	return nil
}

// FileThreatClueMultiError is an error wrapping multiple validation errors
// returned by FileThreatClue.ValidateAll() if the designated constraints
// aren't met.
type FileThreatClueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileThreatClueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileThreatClueMultiError) AllErrors() []error { return m }

// FileThreatClueValidationError is the validation error returned by
// FileThreatClue.Validate if the designated constraints aren't met.
type FileThreatClueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileThreatClueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileThreatClueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileThreatClueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileThreatClueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileThreatClueValidationError) ErrorName() string { return "FileThreatClueValidationError" }

// Error satisfies the builtin error interface
func (e FileThreatClueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileThreatClue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileThreatClueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileThreatClueValidationError{}

// Validate checks the field values on IllegalOutreachClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IllegalOutreachClue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IllegalOutreachClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IllegalOutreachClueMultiError, or nil if none found.
func (m *IllegalOutreachClue) ValidateAll() error {
	return m.validate(true)
}

func (m *IllegalOutreachClue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueLevel

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for AttackSrc

	// no validation rules for OccurCount

	// no validation rules for OutreachType

	// no validation rules for OutreachAddress

	// no validation rules for MaliciousCode

	// no validation rules for HitSource

	// no validation rules for IsApt

	// no validation rules for HasIgnore

	// no validation rules for Disposition

	// no validation rules for ClientVersion

	// no validation rules for ExtraInfo

	if all {
		switch v := interface{}(m.GetDetectedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueValidationError{
				field:  "DetectedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OutreachAddressCount

	if all {
		switch v := interface{}(m.GetLlmResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "LlmResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueValidationError{
					field:  "LlmResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLlmResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueValidationError{
				field:  "LlmResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IllegalOutreachClueMultiError(errors)
	}

	return nil
}

// IllegalOutreachClueMultiError is an error wrapping multiple validation
// errors returned by IllegalOutreachClue.ValidateAll() if the designated
// constraints aren't met.
type IllegalOutreachClueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IllegalOutreachClueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IllegalOutreachClueMultiError) AllErrors() []error { return m }

// IllegalOutreachClueValidationError is the validation error returned by
// IllegalOutreachClue.Validate if the designated constraints aren't met.
type IllegalOutreachClueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IllegalOutreachClueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IllegalOutreachClueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IllegalOutreachClueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IllegalOutreachClueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IllegalOutreachClueValidationError) ErrorName() string {
	return "IllegalOutreachClueValidationError"
}

// Error satisfies the builtin error interface
func (e IllegalOutreachClueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIllegalOutreachClue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IllegalOutreachClueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IllegalOutreachClueValidationError{}

// Validate checks the field values on UniversalAttackClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UniversalAttackClue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UniversalAttackClue with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UniversalAttackClueMultiError, or nil if none found.
func (m *UniversalAttackClue) ValidateAll() error {
	return m.validate(true)
}

func (m *UniversalAttackClue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for UniqueFlag

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueLevel

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for OccurCount

	// no validation rules for Disposition

	// no validation rules for FileMd5

	// no validation rules for FileSha256

	// no validation rules for CanIsolate

	// no validation rules for FileOperationStatus

	// no validation rules for FileOperationFailure

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UniversalAttackClueValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UniversalAttackClueValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetectedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UniversalAttackClueValidationError{
					field:  "DetectedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetectedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UniversalAttackClueValidationError{
				field:  "DetectedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UniversalAttackClueMultiError(errors)
	}

	return nil
}

// UniversalAttackClueMultiError is an error wrapping multiple validation
// errors returned by UniversalAttackClue.ValidateAll() if the designated
// constraints aren't met.
type UniversalAttackClueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UniversalAttackClueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UniversalAttackClueMultiError) AllErrors() []error { return m }

// UniversalAttackClueValidationError is the validation error returned by
// UniversalAttackClue.Validate if the designated constraints aren't met.
type UniversalAttackClueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UniversalAttackClueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UniversalAttackClueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UniversalAttackClueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UniversalAttackClueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UniversalAttackClueValidationError) ErrorName() string {
	return "UniversalAttackClueValidationError"
}

// Error satisfies the builtin error interface
func (e UniversalAttackClueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUniversalAttackClue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UniversalAttackClueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UniversalAttackClueValidationError{}

// Validate checks the field values on ListMemoryAttackCluesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMemoryAttackCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMemoryAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMemoryAttackCluesRespMultiError, or nil if none found.
func (m *ListMemoryAttackCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMemoryAttackCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMemoryAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMemoryAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMemoryAttackCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMemoryAttackCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMemoryAttackCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMemoryAttackCluesRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMemoryAttackCluesRespMultiError(errors)
	}

	return nil
}

// ListMemoryAttackCluesRespMultiError is an error wrapping multiple validation
// errors returned by ListMemoryAttackCluesResp.ValidateAll() if the
// designated constraints aren't met.
type ListMemoryAttackCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMemoryAttackCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMemoryAttackCluesRespMultiError) AllErrors() []error { return m }

// ListMemoryAttackCluesRespValidationError is the validation error returned by
// ListMemoryAttackCluesResp.Validate if the designated constraints aren't met.
type ListMemoryAttackCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMemoryAttackCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMemoryAttackCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMemoryAttackCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMemoryAttackCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMemoryAttackCluesRespValidationError) ErrorName() string {
	return "ListMemoryAttackCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListMemoryAttackCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMemoryAttackCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMemoryAttackCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMemoryAttackCluesRespValidationError{}

// Validate checks the field values on ListFileThreatCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListFileThreatCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFileThreatCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFileThreatCluesReqMultiError, or nil if none found.
func (m *ListFileThreatCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFileThreatCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFileThreatCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFileThreatCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFileThreatCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFileThreatCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFileThreatCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFileThreatCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListFileThreatCluesReqMultiError(errors)
	}

	return nil
}

// ListFileThreatCluesReqMultiError is an error wrapping multiple validation
// errors returned by ListFileThreatCluesReq.ValidateAll() if the designated
// constraints aren't met.
type ListFileThreatCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFileThreatCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFileThreatCluesReqMultiError) AllErrors() []error { return m }

// ListFileThreatCluesReqValidationError is the validation error returned by
// ListFileThreatCluesReq.Validate if the designated constraints aren't met.
type ListFileThreatCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFileThreatCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFileThreatCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFileThreatCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFileThreatCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFileThreatCluesReqValidationError) ErrorName() string {
	return "ListFileThreatCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListFileThreatCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFileThreatCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFileThreatCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFileThreatCluesReqValidationError{}

// Validate checks the field values on ListFileThreatCluesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListFileThreatCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFileThreatCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListFileThreatCluesRespMultiError, or nil if none found.
func (m *ListFileThreatCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFileThreatCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFileThreatCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFileThreatCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFileThreatCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFileThreatCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFileThreatCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFileThreatCluesRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListFileThreatCluesRespMultiError(errors)
	}

	return nil
}

// ListFileThreatCluesRespMultiError is an error wrapping multiple validation
// errors returned by ListFileThreatCluesResp.ValidateAll() if the designated
// constraints aren't met.
type ListFileThreatCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFileThreatCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFileThreatCluesRespMultiError) AllErrors() []error { return m }

// ListFileThreatCluesRespValidationError is the validation error returned by
// ListFileThreatCluesResp.Validate if the designated constraints aren't met.
type ListFileThreatCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFileThreatCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFileThreatCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFileThreatCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFileThreatCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFileThreatCluesRespValidationError) ErrorName() string {
	return "ListFileThreatCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListFileThreatCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFileThreatCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFileThreatCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFileThreatCluesRespValidationError{}

// Validate checks the field values on ListSystemAttackCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSystemAttackCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSystemAttackCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSystemAttackCluesReqMultiError, or nil if none found.
func (m *ListSystemAttackCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSystemAttackCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSystemAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSystemAttackCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSystemAttackCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSystemAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSystemAttackCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSystemAttackCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListSystemAttackCluesReqMultiError(errors)
	}

	return nil
}

// ListSystemAttackCluesReqMultiError is an error wrapping multiple validation
// errors returned by ListSystemAttackCluesReq.ValidateAll() if the designated
// constraints aren't met.
type ListSystemAttackCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSystemAttackCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSystemAttackCluesReqMultiError) AllErrors() []error { return m }

// ListSystemAttackCluesReqValidationError is the validation error returned by
// ListSystemAttackCluesReq.Validate if the designated constraints aren't met.
type ListSystemAttackCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSystemAttackCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSystemAttackCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSystemAttackCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSystemAttackCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSystemAttackCluesReqValidationError) ErrorName() string {
	return "ListSystemAttackCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListSystemAttackCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSystemAttackCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSystemAttackCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSystemAttackCluesReqValidationError{}

// Validate checks the field values on ListIllegalOutreachCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListIllegalOutreachCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListIllegalOutreachCluesReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListIllegalOutreachCluesReqMultiError, or nil if none found.
func (m *ListIllegalOutreachCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListIllegalOutreachCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListIllegalOutreachCluesReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListIllegalOutreachCluesReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListIllegalOutreachCluesReqMultiError(errors)
	}

	return nil
}

// ListIllegalOutreachCluesReqMultiError is an error wrapping multiple
// validation errors returned by ListIllegalOutreachCluesReq.ValidateAll() if
// the designated constraints aren't met.
type ListIllegalOutreachCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListIllegalOutreachCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListIllegalOutreachCluesReqMultiError) AllErrors() []error { return m }

// ListIllegalOutreachCluesReqValidationError is the validation error returned
// by ListIllegalOutreachCluesReq.Validate if the designated constraints
// aren't met.
type ListIllegalOutreachCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListIllegalOutreachCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListIllegalOutreachCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListIllegalOutreachCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListIllegalOutreachCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListIllegalOutreachCluesReqValidationError) ErrorName() string {
	return "ListIllegalOutreachCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListIllegalOutreachCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListIllegalOutreachCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListIllegalOutreachCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListIllegalOutreachCluesReqValidationError{}

// Validate checks the field values on ListIllegalOutreachCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListIllegalOutreachCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListIllegalOutreachCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListIllegalOutreachCluesRespMultiError, or nil if none found.
func (m *ListIllegalOutreachCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListIllegalOutreachCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListIllegalOutreachCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListIllegalOutreachCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListIllegalOutreachCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListIllegalOutreachCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListIllegalOutreachCluesRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListIllegalOutreachCluesRespMultiError(errors)
	}

	return nil
}

// ListIllegalOutreachCluesRespMultiError is an error wrapping multiple
// validation errors returned by ListIllegalOutreachCluesResp.ValidateAll() if
// the designated constraints aren't met.
type ListIllegalOutreachCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListIllegalOutreachCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListIllegalOutreachCluesRespMultiError) AllErrors() []error { return m }

// ListIllegalOutreachCluesRespValidationError is the validation error returned
// by ListIllegalOutreachCluesResp.Validate if the designated constraints
// aren't met.
type ListIllegalOutreachCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListIllegalOutreachCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListIllegalOutreachCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListIllegalOutreachCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListIllegalOutreachCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListIllegalOutreachCluesRespValidationError) ErrorName() string {
	return "ListIllegalOutreachCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListIllegalOutreachCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListIllegalOutreachCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListIllegalOutreachCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListIllegalOutreachCluesRespValidationError{}

// Validate checks the field values on ListSystemAttackCluesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSystemAttackCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSystemAttackCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSystemAttackCluesRespMultiError, or nil if none found.
func (m *ListSystemAttackCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSystemAttackCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSystemAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSystemAttackCluesRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSystemAttackCluesRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListSystemAttackCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListSystemAttackCluesRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListSystemAttackCluesRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListSystemAttackCluesRespMultiError(errors)
	}

	return nil
}

// ListSystemAttackCluesRespMultiError is an error wrapping multiple validation
// errors returned by ListSystemAttackCluesResp.ValidateAll() if the
// designated constraints aren't met.
type ListSystemAttackCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSystemAttackCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSystemAttackCluesRespMultiError) AllErrors() []error { return m }

// ListSystemAttackCluesRespValidationError is the validation error returned by
// ListSystemAttackCluesResp.Validate if the designated constraints aren't met.
type ListSystemAttackCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSystemAttackCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSystemAttackCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSystemAttackCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSystemAttackCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSystemAttackCluesRespValidationError) ErrorName() string {
	return "ListSystemAttackCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListSystemAttackCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSystemAttackCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSystemAttackCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSystemAttackCluesRespValidationError{}

// Validate checks the field values on CountCluesBySha256Req with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesBySha256Req) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesBySha256Req with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesBySha256ReqMultiError, or nil if none found.
func (m *CountCluesBySha256Req) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesBySha256Req) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CountCluesBySha256ReqMultiError(errors)
	}

	return nil
}

// CountCluesBySha256ReqMultiError is an error wrapping multiple validation
// errors returned by CountCluesBySha256Req.ValidateAll() if the designated
// constraints aren't met.
type CountCluesBySha256ReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesBySha256ReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesBySha256ReqMultiError) AllErrors() []error { return m }

// CountCluesBySha256ReqValidationError is the validation error returned by
// CountCluesBySha256Req.Validate if the designated constraints aren't met.
type CountCluesBySha256ReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesBySha256ReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesBySha256ReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesBySha256ReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesBySha256ReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesBySha256ReqValidationError) ErrorName() string {
	return "CountCluesBySha256ReqValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesBySha256ReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesBySha256Req.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesBySha256ReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesBySha256ReqValidationError{}

// Validate checks the field values on CountCluesBySha256Resp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountCluesBySha256Resp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountCluesBySha256Resp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountCluesBySha256RespMultiError, or nil if none found.
func (m *CountCluesBySha256Resp) ValidateAll() error {
	return m.validate(true)
}

func (m *CountCluesBySha256Resp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetCounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CountCluesBySha256RespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CountCluesBySha256RespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CountCluesBySha256RespValidationError{
					field:  fmt.Sprintf("Counts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CountCluesBySha256RespMultiError(errors)
	}

	return nil
}

// CountCluesBySha256RespMultiError is an error wrapping multiple validation
// errors returned by CountCluesBySha256Resp.ValidateAll() if the designated
// constraints aren't met.
type CountCluesBySha256RespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountCluesBySha256RespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountCluesBySha256RespMultiError) AllErrors() []error { return m }

// CountCluesBySha256RespValidationError is the validation error returned by
// CountCluesBySha256Resp.Validate if the designated constraints aren't met.
type CountCluesBySha256RespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountCluesBySha256RespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountCluesBySha256RespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountCluesBySha256RespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountCluesBySha256RespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountCluesBySha256RespValidationError) ErrorName() string {
	return "CountCluesBySha256RespValidationError"
}

// Error satisfies the builtin error interface
func (e CountCluesBySha256RespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountCluesBySha256Resp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountCluesBySha256RespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountCluesBySha256RespValidationError{}

// Validate checks the field values on Sha256Count with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Sha256Count) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Sha256Count with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in Sha256CountMultiError, or
// nil if none found.
func (m *Sha256Count) ValidateAll() error {
	return m.validate(true)
}

func (m *Sha256Count) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileSha256

	// no validation rules for Count

	if len(errors) > 0 {
		return Sha256CountMultiError(errors)
	}

	return nil
}

// Sha256CountMultiError is an error wrapping multiple validation errors
// returned by Sha256Count.ValidateAll() if the designated constraints aren't met.
type Sha256CountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m Sha256CountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m Sha256CountMultiError) AllErrors() []error { return m }

// Sha256CountValidationError is the validation error returned by
// Sha256Count.Validate if the designated constraints aren't met.
type Sha256CountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e Sha256CountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e Sha256CountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e Sha256CountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e Sha256CountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e Sha256CountValidationError) ErrorName() string { return "Sha256CountValidationError" }

// Error satisfies the builtin error interface
func (e Sha256CountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSha256Count.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = Sha256CountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = Sha256CountValidationError{}

// Validate checks the field values on ListTopMachineCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopMachineCluesReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopMachineCluesReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopMachineCluesReqMultiError, or nil if none found.
func (m *ListTopMachineCluesReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopMachineCluesReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTopMachineCluesReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTopMachineCluesReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTopMachineCluesReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Limit

	if len(errors) > 0 {
		return ListTopMachineCluesReqMultiError(errors)
	}

	return nil
}

// ListTopMachineCluesReqMultiError is an error wrapping multiple validation
// errors returned by ListTopMachineCluesReq.ValidateAll() if the designated
// constraints aren't met.
type ListTopMachineCluesReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopMachineCluesReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopMachineCluesReqMultiError) AllErrors() []error { return m }

// ListTopMachineCluesReqValidationError is the validation error returned by
// ListTopMachineCluesReq.Validate if the designated constraints aren't met.
type ListTopMachineCluesReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopMachineCluesReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopMachineCluesReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopMachineCluesReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopMachineCluesReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopMachineCluesReqValidationError) ErrorName() string {
	return "ListTopMachineCluesReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopMachineCluesReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopMachineCluesReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopMachineCluesReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopMachineCluesReqValidationError{}

// Validate checks the field values on ListTopMachineCluesResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopMachineCluesResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopMachineCluesResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopMachineCluesRespMultiError, or nil if none found.
func (m *ListTopMachineCluesResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopMachineCluesResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetMachines() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTopMachineCluesRespValidationError{
						field:  fmt.Sprintf("Machines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTopMachineCluesRespValidationError{
						field:  fmt.Sprintf("Machines[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTopMachineCluesRespValidationError{
					field:  fmt.Sprintf("Machines[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTopMachineCluesRespMultiError(errors)
	}

	return nil
}

// ListTopMachineCluesRespMultiError is an error wrapping multiple validation
// errors returned by ListTopMachineCluesResp.ValidateAll() if the designated
// constraints aren't met.
type ListTopMachineCluesRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopMachineCluesRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopMachineCluesRespMultiError) AllErrors() []error { return m }

// ListTopMachineCluesRespValidationError is the validation error returned by
// ListTopMachineCluesResp.Validate if the designated constraints aren't met.
type ListTopMachineCluesRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopMachineCluesRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopMachineCluesRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopMachineCluesRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopMachineCluesRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopMachineCluesRespValidationError) ErrorName() string {
	return "ListTopMachineCluesRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopMachineCluesRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopMachineCluesResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopMachineCluesRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopMachineCluesRespValidationError{}

// Validate checks the field values on MachineClueCount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MachineClueCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MachineClueCount with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MachineClueCountMultiError, or nil if none found.
func (m *MachineClueCount) ValidateAll() error {
	return m.validate(true)
}

func (m *MachineClueCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MachineId

	// no validation rules for MachineIp

	// no validation rules for MachineName

	// no validation rules for MachineGroup

	// no validation rules for ClueCount

	// no validation rules for Online

	if len(errors) > 0 {
		return MachineClueCountMultiError(errors)
	}

	return nil
}

// MachineClueCountMultiError is an error wrapping multiple validation errors
// returned by MachineClueCount.ValidateAll() if the designated constraints
// aren't met.
type MachineClueCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MachineClueCountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MachineClueCountMultiError) AllErrors() []error { return m }

// MachineClueCountValidationError is the validation error returned by
// MachineClueCount.Validate if the designated constraints aren't met.
type MachineClueCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MachineClueCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MachineClueCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MachineClueCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MachineClueCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MachineClueCountValidationError) ErrorName() string { return "MachineClueCountValidationError" }

// Error satisfies the builtin error interface
func (e MachineClueCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMachineClueCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MachineClueCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MachineClueCountValidationError{}

// Validate checks the field values on GetClueStatsReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetClueStatsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClueStatsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClueStatsReqMultiError, or nil if none found.
func (m *GetClueStatsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClueStatsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetClueStatsReqMultiError(errors)
	}

	return nil
}

// GetClueStatsReqMultiError is an error wrapping multiple validation errors
// returned by GetClueStatsReq.ValidateAll() if the designated constraints
// aren't met.
type GetClueStatsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClueStatsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClueStatsReqMultiError) AllErrors() []error { return m }

// GetClueStatsReqValidationError is the validation error returned by
// GetClueStatsReq.Validate if the designated constraints aren't met.
type GetClueStatsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClueStatsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClueStatsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClueStatsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClueStatsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClueStatsReqValidationError) ErrorName() string { return "GetClueStatsReqValidationError" }

// Error satisfies the builtin error interface
func (e GetClueStatsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClueStatsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClueStatsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClueStatsReqValidationError{}

// Validate checks the field values on ClueStats with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClueStats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueStats with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClueStatsMultiError, or nil
// if none found.
func (m *ClueStats) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueStats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueCount

	// no validation rules for DisposedCount

	// no validation rules for EvidenceCount

	// no validation rules for AffectedTerminalCount

	// no validation rules for WaitingForDisposalCount

	if len(errors) > 0 {
		return ClueStatsMultiError(errors)
	}

	return nil
}

// ClueStatsMultiError is an error wrapping multiple validation errors returned
// by ClueStats.ValidateAll() if the designated constraints aren't met.
type ClueStatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueStatsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueStatsMultiError) AllErrors() []error { return m }

// ClueStatsValidationError is the validation error returned by
// ClueStats.Validate if the designated constraints aren't met.
type ClueStatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueStatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueStatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueStatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueStatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueStatsValidationError) ErrorName() string { return "ClueStatsValidationError" }

// Error satisfies the builtin error interface
func (e ClueStatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueStats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueStatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueStatsValidationError{}

// Validate checks the field values on ListTopMachineClueCountsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopMachineClueCountsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopMachineClueCountsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopMachineClueCountsReqMultiError, or nil if none found.
func (m *ListTopMachineClueCountsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopMachineClueCountsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTopMachineClueCountsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTopMachineClueCountsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTopMachineClueCountsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Limit

	if len(errors) > 0 {
		return ListTopMachineClueCountsReqMultiError(errors)
	}

	return nil
}

// ListTopMachineClueCountsReqMultiError is an error wrapping multiple
// validation errors returned by ListTopMachineClueCountsReq.ValidateAll() if
// the designated constraints aren't met.
type ListTopMachineClueCountsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopMachineClueCountsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopMachineClueCountsReqMultiError) AllErrors() []error { return m }

// ListTopMachineClueCountsReqValidationError is the validation error returned
// by ListTopMachineClueCountsReq.Validate if the designated constraints
// aren't met.
type ListTopMachineClueCountsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopMachineClueCountsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopMachineClueCountsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopMachineClueCountsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopMachineClueCountsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopMachineClueCountsReqValidationError) ErrorName() string {
	return "ListTopMachineClueCountsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopMachineClueCountsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopMachineClueCountsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopMachineClueCountsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopMachineClueCountsReqValidationError{}

// Validate checks the field values on ListTopMachineClueCountsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTopMachineClueCountsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTopMachineClueCountsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTopMachineClueCountsRespMultiError, or nil if none found.
func (m *ListTopMachineClueCountsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTopMachineClueCountsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListTopMachineClueCountsRespMultiError(errors)
	}

	return nil
}

// ListTopMachineClueCountsRespMultiError is an error wrapping multiple
// validation errors returned by ListTopMachineClueCountsResp.ValidateAll() if
// the designated constraints aren't met.
type ListTopMachineClueCountsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTopMachineClueCountsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTopMachineClueCountsRespMultiError) AllErrors() []error { return m }

// ListTopMachineClueCountsRespValidationError is the validation error returned
// by ListTopMachineClueCountsResp.Validate if the designated constraints
// aren't met.
type ListTopMachineClueCountsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTopMachineClueCountsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTopMachineClueCountsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTopMachineClueCountsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTopMachineClueCountsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTopMachineClueCountsRespValidationError) ErrorName() string {
	return "ListTopMachineClueCountsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListTopMachineClueCountsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTopMachineClueCountsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTopMachineClueCountsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTopMachineClueCountsRespValidationError{}

// Validate checks the field values on ListOutreachAffectedTerminalsReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListOutreachAffectedTerminalsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOutreachAffectedTerminalsReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListOutreachAffectedTerminalsReqMultiError, or nil if none found.
func (m *ListOutreachAffectedTerminalsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOutreachAffectedTerminalsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OutreachType

	// no validation rules for OutreachAddress

	// no validation rules for Ip

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutreachAffectedTerminalsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutreachAffectedTerminalsReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListOutreachAffectedTerminalsReqMultiError(errors)
	}

	return nil
}

// ListOutreachAffectedTerminalsReqMultiError is an error wrapping multiple
// validation errors returned by
// ListOutreachAffectedTerminalsReq.ValidateAll() if the designated
// constraints aren't met.
type ListOutreachAffectedTerminalsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOutreachAffectedTerminalsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOutreachAffectedTerminalsReqMultiError) AllErrors() []error { return m }

// ListOutreachAffectedTerminalsReqValidationError is the validation error
// returned by ListOutreachAffectedTerminalsReq.Validate if the designated
// constraints aren't met.
type ListOutreachAffectedTerminalsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOutreachAffectedTerminalsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOutreachAffectedTerminalsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOutreachAffectedTerminalsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOutreachAffectedTerminalsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOutreachAffectedTerminalsReqValidationError) ErrorName() string {
	return "ListOutreachAffectedTerminalsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListOutreachAffectedTerminalsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOutreachAffectedTerminalsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOutreachAffectedTerminalsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOutreachAffectedTerminalsReqValidationError{}

// Validate checks the field values on ListOutreachAffectedTerminalsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListOutreachAffectedTerminalsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListOutreachAffectedTerminalsResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListOutreachAffectedTerminalsRespMultiError, or nil if none found.
func (m *ListOutreachAffectedTerminalsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListOutreachAffectedTerminalsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTerminals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListOutreachAffectedTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListOutreachAffectedTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListOutreachAffectedTerminalsRespValidationError{
					field:  fmt.Sprintf("Terminals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListOutreachAffectedTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListOutreachAffectedTerminalsRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListOutreachAffectedTerminalsRespMultiError(errors)
	}

	return nil
}

// ListOutreachAffectedTerminalsRespMultiError is an error wrapping multiple
// validation errors returned by
// ListOutreachAffectedTerminalsResp.ValidateAll() if the designated
// constraints aren't met.
type ListOutreachAffectedTerminalsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListOutreachAffectedTerminalsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListOutreachAffectedTerminalsRespMultiError) AllErrors() []error { return m }

// ListOutreachAffectedTerminalsRespValidationError is the validation error
// returned by ListOutreachAffectedTerminalsResp.Validate if the designated
// constraints aren't met.
type ListOutreachAffectedTerminalsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListOutreachAffectedTerminalsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListOutreachAffectedTerminalsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListOutreachAffectedTerminalsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListOutreachAffectedTerminalsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListOutreachAffectedTerminalsRespValidationError) ErrorName() string {
	return "ListOutreachAffectedTerminalsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListOutreachAffectedTerminalsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListOutreachAffectedTerminalsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListOutreachAffectedTerminalsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListOutreachAffectedTerminalsRespValidationError{}

// Validate checks the field values on OutreachAffectedTerminal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OutreachAffectedTerminal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachAffectedTerminal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachAffectedTerminalMultiError, or nil if none found.
func (m *OutreachAffectedTerminal) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachAffectedTerminal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for ClueKey

	// no validation rules for UniqueFlagMd5

	// no validation rules for ExtraInfo

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OutreachAffectedTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OutreachAffectedTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OutreachAffectedTerminalValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OutreachAffectedTerminalMultiError(errors)
	}

	return nil
}

// OutreachAffectedTerminalMultiError is an error wrapping multiple validation
// errors returned by OutreachAffectedTerminal.ValidateAll() if the designated
// constraints aren't met.
type OutreachAffectedTerminalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachAffectedTerminalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachAffectedTerminalMultiError) AllErrors() []error { return m }

// OutreachAffectedTerminalValidationError is the validation error returned by
// OutreachAffectedTerminal.Validate if the designated constraints aren't met.
type OutreachAffectedTerminalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachAffectedTerminalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachAffectedTerminalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachAffectedTerminalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachAffectedTerminalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachAffectedTerminalValidationError) ErrorName() string {
	return "OutreachAffectedTerminalValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachAffectedTerminalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachAffectedTerminal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachAffectedTerminalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachAffectedTerminalValidationError{}

// Validate checks the field values on CountOutreachCluesByTypeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountOutreachCluesByTypeReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountOutreachCluesByTypeReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountOutreachCluesByTypeReqMultiError, or nil if none found.
func (m *CountOutreachCluesByTypeReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CountOutreachCluesByTypeReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CountOutreachCluesByTypeReqMultiError(errors)
	}

	return nil
}

// CountOutreachCluesByTypeReqMultiError is an error wrapping multiple
// validation errors returned by CountOutreachCluesByTypeReq.ValidateAll() if
// the designated constraints aren't met.
type CountOutreachCluesByTypeReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountOutreachCluesByTypeReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountOutreachCluesByTypeReqMultiError) AllErrors() []error { return m }

// CountOutreachCluesByTypeReqValidationError is the validation error returned
// by CountOutreachCluesByTypeReq.Validate if the designated constraints
// aren't met.
type CountOutreachCluesByTypeReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountOutreachCluesByTypeReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountOutreachCluesByTypeReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountOutreachCluesByTypeReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountOutreachCluesByTypeReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountOutreachCluesByTypeReqValidationError) ErrorName() string {
	return "CountOutreachCluesByTypeReqValidationError"
}

// Error satisfies the builtin error interface
func (e CountOutreachCluesByTypeReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountOutreachCluesByTypeReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountOutreachCluesByTypeReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountOutreachCluesByTypeReqValidationError{}

// Validate checks the field values on CountOutreachCluesByTypeResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountOutreachCluesByTypeResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountOutreachCluesByTypeResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountOutreachCluesByTypeRespMultiError, or nil if none found.
func (m *CountOutreachCluesByTypeResp) ValidateAll() error {
	return m.validate(true)
}

func (m *CountOutreachCluesByTypeResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CountOutreachCluesByTypeRespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CountOutreachCluesByTypeRespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CountOutreachCluesByTypeRespValidationError{
					field:  fmt.Sprintf("Counts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CountOutreachCluesByTypeRespMultiError(errors)
	}

	return nil
}

// CountOutreachCluesByTypeRespMultiError is an error wrapping multiple
// validation errors returned by CountOutreachCluesByTypeResp.ValidateAll() if
// the designated constraints aren't met.
type CountOutreachCluesByTypeRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountOutreachCluesByTypeRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountOutreachCluesByTypeRespMultiError) AllErrors() []error { return m }

// CountOutreachCluesByTypeRespValidationError is the validation error returned
// by CountOutreachCluesByTypeResp.Validate if the designated constraints
// aren't met.
type CountOutreachCluesByTypeRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountOutreachCluesByTypeRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountOutreachCluesByTypeRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountOutreachCluesByTypeRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountOutreachCluesByTypeRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountOutreachCluesByTypeRespValidationError) ErrorName() string {
	return "CountOutreachCluesByTypeRespValidationError"
}

// Error satisfies the builtin error interface
func (e CountOutreachCluesByTypeRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountOutreachCluesByTypeResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountOutreachCluesByTypeRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountOutreachCluesByTypeRespValidationError{}

// Validate checks the field values on OutreachTypeCount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OutreachTypeCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachTypeCount with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OutreachTypeCountMultiError, or nil if none found.
func (m *OutreachTypeCount) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachTypeCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Count

	if len(errors) > 0 {
		return OutreachTypeCountMultiError(errors)
	}

	return nil
}

// OutreachTypeCountMultiError is an error wrapping multiple validation errors
// returned by OutreachTypeCount.ValidateAll() if the designated constraints
// aren't met.
type OutreachTypeCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachTypeCountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachTypeCountMultiError) AllErrors() []error { return m }

// OutreachTypeCountValidationError is the validation error returned by
// OutreachTypeCount.Validate if the designated constraints aren't met.
type OutreachTypeCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachTypeCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachTypeCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachTypeCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachTypeCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachTypeCountValidationError) ErrorName() string {
	return "OutreachTypeCountValidationError"
}

// Error satisfies the builtin error interface
func (e OutreachTypeCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachTypeCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachTypeCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachTypeCountValidationError{}

// Validate checks the field values on ListClueTypeCountsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClueTypeCountsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClueTypeCountsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClueTypeCountsReqMultiError, or nil if none found.
func (m *ListClueTypeCountsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClueTypeCountsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueType

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClueTypeCountsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClueTypeCountsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClueTypeCountsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListClueTypeCountsReqMultiError(errors)
	}

	return nil
}

// ListClueTypeCountsReqMultiError is an error wrapping multiple validation
// errors returned by ListClueTypeCountsReq.ValidateAll() if the designated
// constraints aren't met.
type ListClueTypeCountsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClueTypeCountsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClueTypeCountsReqMultiError) AllErrors() []error { return m }

// ListClueTypeCountsReqValidationError is the validation error returned by
// ListClueTypeCountsReq.Validate if the designated constraints aren't met.
type ListClueTypeCountsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClueTypeCountsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClueTypeCountsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClueTypeCountsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClueTypeCountsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClueTypeCountsReqValidationError) ErrorName() string {
	return "ListClueTypeCountsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListClueTypeCountsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClueTypeCountsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClueTypeCountsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClueTypeCountsReqValidationError{}

// Validate checks the field values on ListClueTypeCountsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClueTypeCountsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClueTypeCountsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClueTypeCountsRespMultiError, or nil if none found.
func (m *ListClueTypeCountsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClueTypeCountsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClueTypeCountsRespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClueTypeCountsRespValidationError{
						field:  fmt.Sprintf("Counts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClueTypeCountsRespValidationError{
					field:  fmt.Sprintf("Counts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListClueTypeCountsRespMultiError(errors)
	}

	return nil
}

// ListClueTypeCountsRespMultiError is an error wrapping multiple validation
// errors returned by ListClueTypeCountsResp.ValidateAll() if the designated
// constraints aren't met.
type ListClueTypeCountsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClueTypeCountsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClueTypeCountsRespMultiError) AllErrors() []error { return m }

// ListClueTypeCountsRespValidationError is the validation error returned by
// ListClueTypeCountsResp.Validate if the designated constraints aren't met.
type ListClueTypeCountsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClueTypeCountsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClueTypeCountsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClueTypeCountsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClueTypeCountsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClueTypeCountsRespValidationError) ErrorName() string {
	return "ListClueTypeCountsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListClueTypeCountsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClueTypeCountsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClueTypeCountsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClueTypeCountsRespValidationError{}

// Validate checks the field values on ClueTypeCount with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClueTypeCount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueTypeCount with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClueTypeCountMultiError, or
// nil if none found.
func (m *ClueTypeCount) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueTypeCount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueType

	// no validation rules for ClueSubType

	// no validation rules for Count

	if all {
		switch v := interface{}(m.GetLatestCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClueTypeCountValidationError{
					field:  "LatestCreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClueTypeCountValidationError{
					field:  "LatestCreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClueTypeCountValidationError{
				field:  "LatestCreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ClueTypeCountMultiError(errors)
	}

	return nil
}

// ClueTypeCountMultiError is an error wrapping multiple validation errors
// returned by ClueTypeCount.ValidateAll() if the designated constraints
// aren't met.
type ClueTypeCountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueTypeCountMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueTypeCountMultiError) AllErrors() []error { return m }

// ClueTypeCountValidationError is the validation error returned by
// ClueTypeCount.Validate if the designated constraints aren't met.
type ClueTypeCountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueTypeCountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueTypeCountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueTypeCountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueTypeCountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueTypeCountValidationError) ErrorName() string { return "ClueTypeCountValidationError" }

// Error satisfies the builtin error interface
func (e ClueTypeCountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueTypeCount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueTypeCountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueTypeCountValidationError{}

// Validate checks the field values on ProcessInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProcessInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ProcessInfoMultiError, or
// nil if none found.
func (m *ProcessInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pid

	// no validation rules for Ppid

	// no validation rules for ProcName

	// no validation rules for ProcPath

	// no validation rules for DstProcPath

	// no validation rules for ParentProcPath

	// no validation rules for ParentProcCreateTime

	// no validation rules for ProcUser

	// no validation rules for ProcMd5

	// no validation rules for ProcSha256

	// no validation rules for ProcCommand

	// no validation rules for ProcPermission

	// no validation rules for ProcStartTime

	// no validation rules for IsX86Process

	// no validation rules for SubProcName

	// no validation rules for SubProcPath

	// no validation rules for SubProcMd5

	// no validation rules for SubProcCommand

	// no validation rules for FileAccessTime

	// no validation rules for FileModifyTime

	// no validation rules for FileCreateTime

	// no validation rules for FileSize

	// no validation rules for FilePath

	// no validation rules for FileCompanyName

	// no validation rules for FileVersion

	// no validation rules for Euid

	// no validation rules for CurProcInfo

	// no validation rules for ParentProcInfo

	// no validation rules for TargetProcInfo

	if len(errors) > 0 {
		return ProcessInfoMultiError(errors)
	}

	return nil
}

// ProcessInfoMultiError is an error wrapping multiple validation errors
// returned by ProcessInfo.ValidateAll() if the designated constraints aren't met.
type ProcessInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessInfoMultiError) AllErrors() []error { return m }

// ProcessInfoValidationError is the validation error returned by
// ProcessInfo.Validate if the designated constraints aren't met.
type ProcessInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessInfoValidationError) ErrorName() string { return "ProcessInfoValidationError" }

// Error satisfies the builtin error interface
func (e ProcessInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessInfoValidationError{}

// Validate checks the field values on FileInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileInfoMultiError, or nil
// if none found.
func (m *FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileType

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileMd5

	// no validation rules for FileSha256

	// no validation rules for FileSha1

	// no validation rules for FileAccessTime

	// no validation rules for FileModifyTime

	// no validation rules for FileCreateTime

	// no validation rules for FileSize

	// no validation rules for FilePermission

	// no validation rules for FileUser

	// no validation rules for FileClueSource

	// no validation rules for SignatureInfo

	if len(errors) > 0 {
		return FileInfoMultiError(errors)
	}

	return nil
}

// FileInfoMultiError is an error wrapping multiple validation errors returned
// by FileInfo.ValidateAll() if the designated constraints aren't met.
type FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileInfoMultiError) AllErrors() []error { return m }

// FileInfoValidationError is the validation error returned by
// FileInfo.Validate if the designated constraints aren't met.
type FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileInfoValidationError) ErrorName() string { return "FileInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileInfoValidationError{}

// Validate checks the field values on DriverInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DriverInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DriverInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DriverInfoMultiError, or
// nil if none found.
func (m *DriverInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DriverInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DriverName

	// no validation rules for DriverPath

	// no validation rules for DriverRiskType

	// no validation rules for KernelMod

	// no validation rules for RemoteMod

	// no validation rules for FirstDriverInfo

	if len(errors) > 0 {
		return DriverInfoMultiError(errors)
	}

	return nil
}

// DriverInfoMultiError is an error wrapping multiple validation errors
// returned by DriverInfo.ValidateAll() if the designated constraints aren't met.
type DriverInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DriverInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DriverInfoMultiError) AllErrors() []error { return m }

// DriverInfoValidationError is the validation error returned by
// DriverInfo.Validate if the designated constraints aren't met.
type DriverInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DriverInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DriverInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DriverInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DriverInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DriverInfoValidationError) ErrorName() string { return "DriverInfoValidationError" }

// Error satisfies the builtin error interface
func (e DriverInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDriverInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DriverInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DriverInfoValidationError{}

// Validate checks the field values on MaliciousInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MaliciousInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MaliciousInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MaliciousInfoMultiError, or
// nil if none found.
func (m *MaliciousInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *MaliciousInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaliciousCode

	// no validation rules for MaliciousTypes

	// no validation rules for MaliciousDde

	// no validation rules for MaliciousVba

	// no validation rules for MaliciousLnkTargetPath

	// no validation rules for MaliciousLnkWorkingDir

	// no validation rules for MaliciousLnkCmdLine

	// no validation rules for MaliciousLnkIconPath

	// no validation rules for MaliciousUrl

	// no validation rules for SlRuleName

	// no validation rules for SlRuleDetail

	if len(errors) > 0 {
		return MaliciousInfoMultiError(errors)
	}

	return nil
}

// MaliciousInfoMultiError is an error wrapping multiple validation errors
// returned by MaliciousInfo.ValidateAll() if the designated constraints
// aren't met.
type MaliciousInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MaliciousInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MaliciousInfoMultiError) AllErrors() []error { return m }

// MaliciousInfoValidationError is the validation error returned by
// MaliciousInfo.Validate if the designated constraints aren't met.
type MaliciousInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MaliciousInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MaliciousInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MaliciousInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MaliciousInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MaliciousInfoValidationError) ErrorName() string { return "MaliciousInfoValidationError" }

// Error satisfies the builtin error interface
func (e MaliciousInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMaliciousInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MaliciousInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MaliciousInfoValidationError{}

// Validate checks the field values on OutreachInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OutreachInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OutreachInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OutreachInfoMultiError, or
// nil if none found.
func (m *OutreachInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OutreachInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Euid

	// no validation rules for OutreachIp

	// no validation rules for OutreachType

	// no validation rules for OutreachPort

	// no validation rules for OutreachDomain

	// no validation rules for OutreachAddress

	// no validation rules for IpVersion

	// no validation rules for Direction

	// no validation rules for Confidence

	// no validation rules for IsApt

	if len(errors) > 0 {
		return OutreachInfoMultiError(errors)
	}

	return nil
}

// OutreachInfoMultiError is an error wrapping multiple validation errors
// returned by OutreachInfo.ValidateAll() if the designated constraints aren't met.
type OutreachInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OutreachInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OutreachInfoMultiError) AllErrors() []error { return m }

// OutreachInfoValidationError is the validation error returned by
// OutreachInfo.Validate if the designated constraints aren't met.
type OutreachInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OutreachInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OutreachInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OutreachInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OutreachInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OutreachInfoValidationError) ErrorName() string { return "OutreachInfoValidationError" }

// Error satisfies the builtin error interface
func (e OutreachInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOutreachInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OutreachInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OutreachInfoValidationError{}

// Validate checks the field values on AttackExtendInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AttackExtendInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttackExtendInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttackExtendInfoMultiError, or nil if none found.
func (m *AttackExtendInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AttackExtendInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReportId

	// no validation rules for ProcLineFlag

	// no validation rules for DumpFlag

	// no validation rules for ScriptFlag

	// no validation rules for SignatureInfo

	// no validation rules for ScriptType

	// no validation rules for Cve

	// no validation rules for AttackIp

	// no validation rules for Crontab

	// no validation rules for LocalIp

	// no validation rules for LocalPort

	// no validation rules for RemoteIp

	// no validation rules for RemotePort

	// no validation rules for HitSource

	// no validation rules for Protocol

	if len(errors) > 0 {
		return AttackExtendInfoMultiError(errors)
	}

	return nil
}

// AttackExtendInfoMultiError is an error wrapping multiple validation errors
// returned by AttackExtendInfo.ValidateAll() if the designated constraints
// aren't met.
type AttackExtendInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttackExtendInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttackExtendInfoMultiError) AllErrors() []error { return m }

// AttackExtendInfoValidationError is the validation error returned by
// AttackExtendInfo.Validate if the designated constraints aren't met.
type AttackExtendInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttackExtendInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttackExtendInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttackExtendInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttackExtendInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttackExtendInfoValidationError) ErrorName() string { return "AttackExtendInfoValidationError" }

// Error satisfies the builtin error interface
func (e AttackExtendInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttackExtendInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttackExtendInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttackExtendInfoValidationError{}

// Validate checks the field values on Evidence with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Evidence) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Evidence with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EvidenceMultiError, or nil
// if none found.
func (m *Evidence) ValidateAll() error {
	return m.validate(true)
}

func (m *Evidence) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EvidenceSize

	// no validation rules for EvidenceList

	if len(errors) > 0 {
		return EvidenceMultiError(errors)
	}

	return nil
}

// EvidenceMultiError is an error wrapping multiple validation errors returned
// by Evidence.ValidateAll() if the designated constraints aren't met.
type EvidenceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvidenceMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvidenceMultiError) AllErrors() []error { return m }

// EvidenceValidationError is the validation error returned by
// Evidence.Validate if the designated constraints aren't met.
type EvidenceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvidenceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvidenceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvidenceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvidenceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvidenceValidationError) ErrorName() string { return "EvidenceValidationError" }

// Error satisfies the builtin error interface
func (e EvidenceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvidence.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvidenceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvidenceValidationError{}

// Validate checks the field values on AttackIndicatorContextValue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttackIndicatorContextValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttackIndicatorContextValue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttackIndicatorContextValueMultiError, or nil if none found.
func (m *AttackIndicatorContextValue) ValidateAll() error {
	return m.validate(true)
}

func (m *AttackIndicatorContextValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Value.(type) {
	case *AttackIndicatorContextValue_StringValue:
		if v == nil {
			err := AttackIndicatorContextValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for StringValue
	case *AttackIndicatorContextValue_Int64Value:
		if v == nil {
			err := AttackIndicatorContextValueValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Int64Value
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AttackIndicatorContextValueMultiError(errors)
	}

	return nil
}

// AttackIndicatorContextValueMultiError is an error wrapping multiple
// validation errors returned by AttackIndicatorContextValue.ValidateAll() if
// the designated constraints aren't met.
type AttackIndicatorContextValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttackIndicatorContextValueMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttackIndicatorContextValueMultiError) AllErrors() []error { return m }

// AttackIndicatorContextValueValidationError is the validation error returned
// by AttackIndicatorContextValue.Validate if the designated constraints
// aren't met.
type AttackIndicatorContextValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttackIndicatorContextValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttackIndicatorContextValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttackIndicatorContextValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttackIndicatorContextValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttackIndicatorContextValueValidationError) ErrorName() string {
	return "AttackIndicatorContextValueValidationError"
}

// Error satisfies the builtin error interface
func (e AttackIndicatorContextValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttackIndicatorContextValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttackIndicatorContextValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttackIndicatorContextValueValidationError{}

// Validate checks the field values on AttackDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttackDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttackDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttackDetailMultiError, or
// nil if none found.
func (m *AttackDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *AttackDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProcessInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "ProcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "ProcessInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "ProcessInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFileInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "FileInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFileInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "FileInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDriverInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "DriverInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "DriverInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDriverInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "DriverInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaliciousInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "MaliciousInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "MaliciousInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaliciousInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "MaliciousInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOutreachInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "OutreachInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "OutreachInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutreachInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "OutreachInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEvidenceInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "EvidenceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "EvidenceInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvidenceInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "EvidenceInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetAttackContext()))
		i := 0
		for key := range m.GetAttackContext() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetAttackContext()[key]
			_ = val

			// no validation rules for AttackContext[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AttackDetailValidationError{
							field:  fmt.Sprintf("AttackContext[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AttackDetailValidationError{
							field:  fmt.Sprintf("AttackContext[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AttackDetailValidationError{
						field:  fmt.Sprintf("AttackContext[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if all {
		switch v := interface{}(m.GetExtendInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "ExtendInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AttackDetailValidationError{
					field:  "ExtendInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExtendInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AttackDetailValidationError{
				field:  "ExtendInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	{
		sorted_keys := make([]string, len(m.GetClueTypeExtraInfo()))
		i := 0
		for key := range m.GetClueTypeExtraInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetClueTypeExtraInfo()[key]
			_ = val

			// no validation rules for ClueTypeExtraInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, AttackDetailValidationError{
							field:  fmt.Sprintf("ClueTypeExtraInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, AttackDetailValidationError{
							field:  fmt.Sprintf("ClueTypeExtraInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return AttackDetailValidationError{
						field:  fmt.Sprintf("ClueTypeExtraInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return AttackDetailMultiError(errors)
	}

	return nil
}

// AttackDetailMultiError is an error wrapping multiple validation errors
// returned by AttackDetail.ValidateAll() if the designated constraints aren't met.
type AttackDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttackDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttackDetailMultiError) AllErrors() []error { return m }

// AttackDetailValidationError is the validation error returned by
// AttackDetail.Validate if the designated constraints aren't met.
type AttackDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttackDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttackDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttackDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttackDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttackDetailValidationError) ErrorName() string { return "AttackDetailValidationError" }

// Error satisfies the builtin error interface
func (e AttackDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttackDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttackDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttackDetailValidationError{}

// Validate checks the field values on ClueDetail with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClueDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClueDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClueDetailMultiError, or
// nil if none found.
func (m *ClueDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ClueDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClueType

	switch v := m.Clue.(type) {
	case *ClueDetail_MemoryAttackClueDetail:
		if v == nil {
			err := ClueDetailValidationError{
				field:  "Clue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMemoryAttackClueDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "MemoryAttackClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "MemoryAttackClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMemoryAttackClueDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClueDetailValidationError{
					field:  "MemoryAttackClueDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ClueDetail_FileThreatClueDetail:
		if v == nil {
			err := ClueDetailValidationError{
				field:  "Clue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFileThreatClueDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "FileThreatClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "FileThreatClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFileThreatClueDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClueDetailValidationError{
					field:  "FileThreatClueDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ClueDetail_SystemAttackClueDetail:
		if v == nil {
			err := ClueDetailValidationError{
				field:  "Clue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSystemAttackClueDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "SystemAttackClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "SystemAttackClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSystemAttackClueDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClueDetailValidationError{
					field:  "SystemAttackClueDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ClueDetail_IllegalOutreachClueDetail:
		if v == nil {
			err := ClueDetailValidationError{
				field:  "Clue",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIllegalOutreachClueDetail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "IllegalOutreachClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClueDetailValidationError{
						field:  "IllegalOutreachClueDetail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIllegalOutreachClueDetail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClueDetailValidationError{
					field:  "IllegalOutreachClueDetail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ClueDetailMultiError(errors)
	}

	return nil
}

// ClueDetailMultiError is an error wrapping multiple validation errors
// returned by ClueDetail.ValidateAll() if the designated constraints aren't met.
type ClueDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClueDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClueDetailMultiError) AllErrors() []error { return m }

// ClueDetailValidationError is the validation error returned by
// ClueDetail.Validate if the designated constraints aren't met.
type ClueDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClueDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClueDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClueDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClueDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClueDetailValidationError) ErrorName() string { return "ClueDetailValidationError" }

// Error satisfies the builtin error interface
func (e ClueDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClueDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClueDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClueDetailValidationError{}

// Validate checks the field values on MemoryAttackClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MemoryAttackClueDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MemoryAttackClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MemoryAttackClueDetailMultiError, or nil if none found.
func (m *MemoryAttackClueDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *MemoryAttackClueDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemoryAttackClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemoryAttackClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemoryAttackClueDetailValidationError{
				field:  "Clue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MemoryAttackClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MemoryAttackClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MemoryAttackClueDetailValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MemoryAttackClueDetailMultiError(errors)
	}

	return nil
}

// MemoryAttackClueDetailMultiError is an error wrapping multiple validation
// errors returned by MemoryAttackClueDetail.ValidateAll() if the designated
// constraints aren't met.
type MemoryAttackClueDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MemoryAttackClueDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MemoryAttackClueDetailMultiError) AllErrors() []error { return m }

// MemoryAttackClueDetailValidationError is the validation error returned by
// MemoryAttackClueDetail.Validate if the designated constraints aren't met.
type MemoryAttackClueDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MemoryAttackClueDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MemoryAttackClueDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MemoryAttackClueDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MemoryAttackClueDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MemoryAttackClueDetailValidationError) ErrorName() string {
	return "MemoryAttackClueDetailValidationError"
}

// Error satisfies the builtin error interface
func (e MemoryAttackClueDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMemoryAttackClueDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MemoryAttackClueDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MemoryAttackClueDetailValidationError{}

// Validate checks the field values on FileThreatClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileThreatClueDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileThreatClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FileThreatClueDetailMultiError, or nil if none found.
func (m *FileThreatClueDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *FileThreatClueDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatClueDetailValidationError{
				field:  "Clue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FileThreatClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FileThreatClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FileThreatClueDetailValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FileThreatClueDetailMultiError(errors)
	}

	return nil
}

// FileThreatClueDetailMultiError is an error wrapping multiple validation
// errors returned by FileThreatClueDetail.ValidateAll() if the designated
// constraints aren't met.
type FileThreatClueDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileThreatClueDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileThreatClueDetailMultiError) AllErrors() []error { return m }

// FileThreatClueDetailValidationError is the validation error returned by
// FileThreatClueDetail.Validate if the designated constraints aren't met.
type FileThreatClueDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileThreatClueDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileThreatClueDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileThreatClueDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileThreatClueDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileThreatClueDetailValidationError) ErrorName() string {
	return "FileThreatClueDetailValidationError"
}

// Error satisfies the builtin error interface
func (e FileThreatClueDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileThreatClueDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileThreatClueDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileThreatClueDetailValidationError{}

// Validate checks the field values on SystemAttackClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SystemAttackClueDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SystemAttackClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SystemAttackClueDetailMultiError, or nil if none found.
func (m *SystemAttackClueDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *SystemAttackClueDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemAttackClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemAttackClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemAttackClueDetailValidationError{
				field:  "Clue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SystemAttackClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SystemAttackClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SystemAttackClueDetailValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SystemAttackClueDetailMultiError(errors)
	}

	return nil
}

// SystemAttackClueDetailMultiError is an error wrapping multiple validation
// errors returned by SystemAttackClueDetail.ValidateAll() if the designated
// constraints aren't met.
type SystemAttackClueDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SystemAttackClueDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SystemAttackClueDetailMultiError) AllErrors() []error { return m }

// SystemAttackClueDetailValidationError is the validation error returned by
// SystemAttackClueDetail.Validate if the designated constraints aren't met.
type SystemAttackClueDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SystemAttackClueDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SystemAttackClueDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SystemAttackClueDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SystemAttackClueDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SystemAttackClueDetailValidationError) ErrorName() string {
	return "SystemAttackClueDetailValidationError"
}

// Error satisfies the builtin error interface
func (e SystemAttackClueDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSystemAttackClueDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SystemAttackClueDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SystemAttackClueDetailValidationError{}

// Validate checks the field values on IllegalOutreachClueDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IllegalOutreachClueDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IllegalOutreachClueDetail with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IllegalOutreachClueDetailMultiError, or nil if none found.
func (m *IllegalOutreachClueDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *IllegalOutreachClueDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetClue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueDetailValidationError{
					field:  "Clue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueDetailValidationError{
				field:  "Clue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IllegalOutreachClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IllegalOutreachClueDetailValidationError{
					field:  "Detail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IllegalOutreachClueDetailValidationError{
				field:  "Detail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IllegalOutreachClueDetailMultiError(errors)
	}

	return nil
}

// IllegalOutreachClueDetailMultiError is an error wrapping multiple validation
// errors returned by IllegalOutreachClueDetail.ValidateAll() if the
// designated constraints aren't met.
type IllegalOutreachClueDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IllegalOutreachClueDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IllegalOutreachClueDetailMultiError) AllErrors() []error { return m }

// IllegalOutreachClueDetailValidationError is the validation error returned by
// IllegalOutreachClueDetail.Validate if the designated constraints aren't met.
type IllegalOutreachClueDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IllegalOutreachClueDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IllegalOutreachClueDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IllegalOutreachClueDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IllegalOutreachClueDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IllegalOutreachClueDetailValidationError) ErrorName() string {
	return "IllegalOutreachClueDetailValidationError"
}

// Error satisfies the builtin error interface
func (e IllegalOutreachClueDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIllegalOutreachClueDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IllegalOutreachClueDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IllegalOutreachClueDetailValidationError{}

// Validate checks the field values on GetClueDetailReq with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetClueDetailReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetClueDetailReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetClueDetailReqMultiError, or nil if none found.
func (m *GetClueDetailReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClueDetailReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClueType

	if len(errors) > 0 {
		return GetClueDetailReqMultiError(errors)
	}

	return nil
}

// GetClueDetailReqMultiError is an error wrapping multiple validation errors
// returned by GetClueDetailReq.ValidateAll() if the designated constraints
// aren't met.
type GetClueDetailReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClueDetailReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClueDetailReqMultiError) AllErrors() []error { return m }

// GetClueDetailReqValidationError is the validation error returned by
// GetClueDetailReq.Validate if the designated constraints aren't met.
type GetClueDetailReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClueDetailReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClueDetailReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClueDetailReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClueDetailReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClueDetailReqValidationError) ErrorName() string { return "GetClueDetailReqValidationError" }

// Error satisfies the builtin error interface
func (e GetClueDetailReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClueDetailReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClueDetailReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClueDetailReqValidationError{}

// Validate checks the field values on LLMResult with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LLMResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LLMResult with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LLMResultMultiError, or nil
// if none found.
func (m *LLMResult) ValidateAll() error {
	return m.validate(true)
}

func (m *LLMResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	// no validation rules for ReadUrl

	// no validation rules for Rank

	// no validation rules for LlmAnswer

	// no validation rules for LlmAnswerDescription

	// no validation rules for Screenshot

	if len(errors) > 0 {
		return LLMResultMultiError(errors)
	}

	return nil
}

// LLMResultMultiError is an error wrapping multiple validation errors returned
// by LLMResult.ValidateAll() if the designated constraints aren't met.
type LLMResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LLMResultMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LLMResultMultiError) AllErrors() []error { return m }

// LLMResultValidationError is the validation error returned by
// LLMResult.Validate if the designated constraints aren't met.
type LLMResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LLMResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LLMResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LLMResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LLMResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LLMResultValidationError) ErrorName() string { return "LLMResultValidationError" }

// Error satisfies the builtin error interface
func (e LLMResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLLMResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LLMResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LLMResultValidationError{}

// Validate checks the field values on ListClueDetailsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClueDetailsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClueDetailsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClueDetailsReqMultiError, or nil if none found.
func (m *ListClueDetailsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClueDetailsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClueDetailsReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClueDetailsReqValidationError{
					field:  "Filter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClueDetailsReqValidationError{
				field:  "Filter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClueType

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClueDetailsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClueDetailsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClueDetailsReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListClueDetailsReqMultiError(errors)
	}

	return nil
}

// ListClueDetailsReqMultiError is an error wrapping multiple validation errors
// returned by ListClueDetailsReq.ValidateAll() if the designated constraints
// aren't met.
type ListClueDetailsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClueDetailsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClueDetailsReqMultiError) AllErrors() []error { return m }

// ListClueDetailsReqValidationError is the validation error returned by
// ListClueDetailsReq.Validate if the designated constraints aren't met.
type ListClueDetailsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClueDetailsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClueDetailsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClueDetailsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClueDetailsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClueDetailsReqValidationError) ErrorName() string {
	return "ListClueDetailsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListClueDetailsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClueDetailsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClueDetailsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClueDetailsReqValidationError{}

// Validate checks the field values on ListClueDetailsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListClueDetailsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListClueDetailsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListClueDetailsRespMultiError, or nil if none found.
func (m *ListClueDetailsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListClueDetailsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListClueDetailsRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListClueDetailsRespValidationError{
						field:  fmt.Sprintf("Clues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListClueDetailsRespValidationError{
					field:  fmt.Sprintf("Clues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListClueDetailsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListClueDetailsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListClueDetailsRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListClueDetailsRespMultiError(errors)
	}

	return nil
}

// ListClueDetailsRespMultiError is an error wrapping multiple validation
// errors returned by ListClueDetailsResp.ValidateAll() if the designated
// constraints aren't met.
type ListClueDetailsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListClueDetailsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListClueDetailsRespMultiError) AllErrors() []error { return m }

// ListClueDetailsRespValidationError is the validation error returned by
// ListClueDetailsResp.Validate if the designated constraints aren't met.
type ListClueDetailsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListClueDetailsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListClueDetailsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListClueDetailsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListClueDetailsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListClueDetailsRespValidationError) ErrorName() string {
	return "ListClueDetailsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListClueDetailsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListClueDetailsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListClueDetailsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListClueDetailsRespValidationError{}

// Validate checks the field values on ListAffectedOutreachTerminalsReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListAffectedOutreachTerminalsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAffectedOutreachTerminalsReq with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAffectedOutreachTerminalsReqMultiError, or nil if none found.
func (m *ListAffectedOutreachTerminalsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAffectedOutreachTerminalsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for OutreachAddress

	// no validation rules for Ip

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedOutreachTerminalsReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsReqValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsReqValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedOutreachTerminalsReqValidationError{
				field:  "Range",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAffectedOutreachTerminalsReqMultiError(errors)
	}

	return nil
}

// ListAffectedOutreachTerminalsReqMultiError is an error wrapping multiple
// validation errors returned by
// ListAffectedOutreachTerminalsReq.ValidateAll() if the designated
// constraints aren't met.
type ListAffectedOutreachTerminalsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAffectedOutreachTerminalsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAffectedOutreachTerminalsReqMultiError) AllErrors() []error { return m }

// ListAffectedOutreachTerminalsReqValidationError is the validation error
// returned by ListAffectedOutreachTerminalsReq.Validate if the designated
// constraints aren't met.
type ListAffectedOutreachTerminalsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAffectedOutreachTerminalsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAffectedOutreachTerminalsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAffectedOutreachTerminalsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAffectedOutreachTerminalsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAffectedOutreachTerminalsReqValidationError) ErrorName() string {
	return "ListAffectedOutreachTerminalsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListAffectedOutreachTerminalsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAffectedOutreachTerminalsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAffectedOutreachTerminalsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAffectedOutreachTerminalsReqValidationError{}

// Validate checks the field values on ListAffectedOutreachTerminalsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListAffectedOutreachTerminalsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAffectedOutreachTerminalsResp
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListAffectedOutreachTerminalsRespMultiError, or nil if none found.
func (m *ListAffectedOutreachTerminalsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAffectedOutreachTerminalsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTerminals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAffectedOutreachTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAffectedOutreachTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAffectedOutreachTerminalsRespValidationError{
					field:  fmt.Sprintf("Terminals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedOutreachTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedOutreachTerminalsRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAffectedOutreachTerminalsRespMultiError(errors)
	}

	return nil
}

// ListAffectedOutreachTerminalsRespMultiError is an error wrapping multiple
// validation errors returned by
// ListAffectedOutreachTerminalsResp.ValidateAll() if the designated
// constraints aren't met.
type ListAffectedOutreachTerminalsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAffectedOutreachTerminalsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAffectedOutreachTerminalsRespMultiError) AllErrors() []error { return m }

// ListAffectedOutreachTerminalsRespValidationError is the validation error
// returned by ListAffectedOutreachTerminalsResp.Validate if the designated
// constraints aren't met.
type ListAffectedOutreachTerminalsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAffectedOutreachTerminalsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAffectedOutreachTerminalsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAffectedOutreachTerminalsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAffectedOutreachTerminalsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAffectedOutreachTerminalsRespValidationError) ErrorName() string {
	return "ListAffectedOutreachTerminalsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListAffectedOutreachTerminalsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAffectedOutreachTerminalsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAffectedOutreachTerminalsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAffectedOutreachTerminalsRespValidationError{}

// Validate checks the field values on AffectedOutreachTerminal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AffectedOutreachTerminal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffectedOutreachTerminal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AffectedOutreachTerminalMultiError, or nil if none found.
func (m *AffectedOutreachTerminal) ValidateAll() error {
	return m.validate(true)
}

func (m *AffectedOutreachTerminal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for ClueKey

	// no validation rules for UniqueFlagMd5

	// no validation rules for ExtraInfo

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffectedOutreachTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffectedOutreachTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffectedOutreachTerminalValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AffectedOutreachTerminalMultiError(errors)
	}

	return nil
}

// AffectedOutreachTerminalMultiError is an error wrapping multiple validation
// errors returned by AffectedOutreachTerminal.ValidateAll() if the designated
// constraints aren't met.
type AffectedOutreachTerminalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffectedOutreachTerminalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffectedOutreachTerminalMultiError) AllErrors() []error { return m }

// AffectedOutreachTerminalValidationError is the validation error returned by
// AffectedOutreachTerminal.Validate if the designated constraints aren't met.
type AffectedOutreachTerminalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffectedOutreachTerminalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffectedOutreachTerminalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffectedOutreachTerminalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffectedOutreachTerminalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffectedOutreachTerminalValidationError) ErrorName() string {
	return "AffectedOutreachTerminalValidationError"
}

// Error satisfies the builtin error interface
func (e AffectedOutreachTerminalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffectedOutreachTerminal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffectedOutreachTerminalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffectedOutreachTerminalValidationError{}

// Validate checks the field values on ListAffectedFileTerminalsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAffectedFileTerminalsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAffectedFileTerminalsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListAffectedFileTerminalsReqMultiError, or nil if none found.
func (m *ListAffectedFileTerminalsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAffectedFileTerminalsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileSha256

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedFileTerminalsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsReqValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedFileTerminalsReqValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAffectedFileTerminalsReqMultiError(errors)
	}

	return nil
}

// ListAffectedFileTerminalsReqMultiError is an error wrapping multiple
// validation errors returned by ListAffectedFileTerminalsReq.ValidateAll() if
// the designated constraints aren't met.
type ListAffectedFileTerminalsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAffectedFileTerminalsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAffectedFileTerminalsReqMultiError) AllErrors() []error { return m }

// ListAffectedFileTerminalsReqValidationError is the validation error returned
// by ListAffectedFileTerminalsReq.Validate if the designated constraints
// aren't met.
type ListAffectedFileTerminalsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAffectedFileTerminalsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAffectedFileTerminalsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAffectedFileTerminalsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAffectedFileTerminalsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAffectedFileTerminalsReqValidationError) ErrorName() string {
	return "ListAffectedFileTerminalsReqValidationError"
}

// Error satisfies the builtin error interface
func (e ListAffectedFileTerminalsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAffectedFileTerminalsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAffectedFileTerminalsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAffectedFileTerminalsReqValidationError{}

// Validate checks the field values on ListAffectedFileTerminalsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListAffectedFileTerminalsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListAffectedFileTerminalsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListAffectedFileTerminalsRespMultiError, or nil if none found.
func (m *ListAffectedFileTerminalsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *ListAffectedFileTerminalsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTerminals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListAffectedFileTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListAffectedFileTerminalsRespValidationError{
						field:  fmt.Sprintf("Terminals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListAffectedFileTerminalsRespValidationError{
					field:  fmt.Sprintf("Terminals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListAffectedFileTerminalsRespValidationError{
					field:  "Page",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListAffectedFileTerminalsRespValidationError{
				field:  "Page",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListAffectedFileTerminalsRespMultiError(errors)
	}

	return nil
}

// ListAffectedFileTerminalsRespMultiError is an error wrapping multiple
// validation errors returned by ListAffectedFileTerminalsResp.ValidateAll()
// if the designated constraints aren't met.
type ListAffectedFileTerminalsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListAffectedFileTerminalsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListAffectedFileTerminalsRespMultiError) AllErrors() []error { return m }

// ListAffectedFileTerminalsRespValidationError is the validation error
// returned by ListAffectedFileTerminalsResp.Validate if the designated
// constraints aren't met.
type ListAffectedFileTerminalsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListAffectedFileTerminalsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListAffectedFileTerminalsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListAffectedFileTerminalsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListAffectedFileTerminalsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListAffectedFileTerminalsRespValidationError) ErrorName() string {
	return "ListAffectedFileTerminalsRespValidationError"
}

// Error satisfies the builtin error interface
func (e ListAffectedFileTerminalsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListAffectedFileTerminalsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListAffectedFileTerminalsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListAffectedFileTerminalsRespValidationError{}

// Validate checks the field values on AffectedFileTerminal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AffectedFileTerminal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AffectedFileTerminal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AffectedFileTerminalMultiError, or nil if none found.
func (m *AffectedFileTerminal) ValidateAll() error {
	return m.validate(true)
}

func (m *AffectedFileTerminal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MachineId

	// no validation rules for ClueKey

	// no validation rules for ClueStatus

	// no validation rules for ClueSubType

	// no validation rules for FileName

	// no validation rules for FileType

	// no validation rules for UniqueFlag

	// no validation rules for ExtraInfo

	// no validation rules for Disposition

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AffectedFileTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AffectedFileTerminalValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AffectedFileTerminalValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AffectedFileTerminalMultiError(errors)
	}

	return nil
}

// AffectedFileTerminalMultiError is an error wrapping multiple validation
// errors returned by AffectedFileTerminal.ValidateAll() if the designated
// constraints aren't met.
type AffectedFileTerminalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AffectedFileTerminalMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AffectedFileTerminalMultiError) AllErrors() []error { return m }

// AffectedFileTerminalValidationError is the validation error returned by
// AffectedFileTerminal.Validate if the designated constraints aren't met.
type AffectedFileTerminalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AffectedFileTerminalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AffectedFileTerminalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AffectedFileTerminalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AffectedFileTerminalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AffectedFileTerminalValidationError) ErrorName() string {
	return "AffectedFileTerminalValidationError"
}

// Error satisfies the builtin error interface
func (e AffectedFileTerminalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAffectedFileTerminal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AffectedFileTerminalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AffectedFileTerminalValidationError{}

// Validate checks the field values on GetOutreachStatisticsReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutreachStatisticsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutreachStatisticsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutreachStatisticsReqMultiError, or nil if none found.
func (m *GetOutreachStatisticsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutreachStatisticsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOutreachStatisticsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOutreachStatisticsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOutreachStatisticsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOutreachStatisticsReqMultiError(errors)
	}

	return nil
}

// GetOutreachStatisticsReqMultiError is an error wrapping multiple validation
// errors returned by GetOutreachStatisticsReq.ValidateAll() if the designated
// constraints aren't met.
type GetOutreachStatisticsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutreachStatisticsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutreachStatisticsReqMultiError) AllErrors() []error { return m }

// GetOutreachStatisticsReqValidationError is the validation error returned by
// GetOutreachStatisticsReq.Validate if the designated constraints aren't met.
type GetOutreachStatisticsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutreachStatisticsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutreachStatisticsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutreachStatisticsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutreachStatisticsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutreachStatisticsReqValidationError) ErrorName() string {
	return "GetOutreachStatisticsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutreachStatisticsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutreachStatisticsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutreachStatisticsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutreachStatisticsReqValidationError{}

// Validate checks the field values on GetOutreachStatisticsResp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOutreachStatisticsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOutreachStatisticsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOutreachStatisticsRespMultiError, or nil if none found.
func (m *GetOutreachStatisticsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOutreachStatisticsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalCount

	// no validation rules for IllegalCount

	// no validation rules for IpCount

	// no validation rules for DomainCount

	if len(errors) > 0 {
		return GetOutreachStatisticsRespMultiError(errors)
	}

	return nil
}

// GetOutreachStatisticsRespMultiError is an error wrapping multiple validation
// errors returned by GetOutreachStatisticsResp.ValidateAll() if the
// designated constraints aren't met.
type GetOutreachStatisticsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOutreachStatisticsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOutreachStatisticsRespMultiError) AllErrors() []error { return m }

// GetOutreachStatisticsRespValidationError is the validation error returned by
// GetOutreachStatisticsResp.Validate if the designated constraints aren't met.
type GetOutreachStatisticsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOutreachStatisticsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOutreachStatisticsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOutreachStatisticsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOutreachStatisticsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOutreachStatisticsRespValidationError) ErrorName() string {
	return "GetOutreachStatisticsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetOutreachStatisticsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOutreachStatisticsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOutreachStatisticsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOutreachStatisticsRespValidationError{}

// Validate checks the field values on GetThreatenFileStatisticsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetThreatenFileStatisticsReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetThreatenFileStatisticsReq with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetThreatenFileStatisticsReqMultiError, or nil if none found.
func (m *GetThreatenFileStatisticsReq) ValidateAll() error {
	return m.validate(true)
}

func (m *GetThreatenFileStatisticsReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetThreatenFileStatisticsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetThreatenFileStatisticsReqValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetThreatenFileStatisticsReqValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetThreatenFileStatisticsReqMultiError(errors)
	}

	return nil
}

// GetThreatenFileStatisticsReqMultiError is an error wrapping multiple
// validation errors returned by GetThreatenFileStatisticsReq.ValidateAll() if
// the designated constraints aren't met.
type GetThreatenFileStatisticsReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetThreatenFileStatisticsReqMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetThreatenFileStatisticsReqMultiError) AllErrors() []error { return m }

// GetThreatenFileStatisticsReqValidationError is the validation error returned
// by GetThreatenFileStatisticsReq.Validate if the designated constraints
// aren't met.
type GetThreatenFileStatisticsReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetThreatenFileStatisticsReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetThreatenFileStatisticsReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetThreatenFileStatisticsReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetThreatenFileStatisticsReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetThreatenFileStatisticsReqValidationError) ErrorName() string {
	return "GetThreatenFileStatisticsReqValidationError"
}

// Error satisfies the builtin error interface
func (e GetThreatenFileStatisticsReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetThreatenFileStatisticsReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetThreatenFileStatisticsReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetThreatenFileStatisticsReqValidationError{}

// Validate checks the field values on GetThreatenFileStatisticsResp with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetThreatenFileStatisticsResp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetThreatenFileStatisticsResp with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetThreatenFileStatisticsRespMultiError, or nil if none found.
func (m *GetThreatenFileStatisticsResp) ValidateAll() error {
	return m.validate(true)
}

func (m *GetThreatenFileStatisticsResp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalCount

	// no validation rules for TerminalCount

	// no validation rules for CheckEngineCounts

	if len(errors) > 0 {
		return GetThreatenFileStatisticsRespMultiError(errors)
	}

	return nil
}

// GetThreatenFileStatisticsRespMultiError is an error wrapping multiple
// validation errors returned by GetThreatenFileStatisticsResp.ValidateAll()
// if the designated constraints aren't met.
type GetThreatenFileStatisticsRespMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetThreatenFileStatisticsRespMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetThreatenFileStatisticsRespMultiError) AllErrors() []error { return m }

// GetThreatenFileStatisticsRespValidationError is the validation error
// returned by GetThreatenFileStatisticsResp.Validate if the designated
// constraints aren't met.
type GetThreatenFileStatisticsRespValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetThreatenFileStatisticsRespValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetThreatenFileStatisticsRespValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetThreatenFileStatisticsRespValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetThreatenFileStatisticsRespValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetThreatenFileStatisticsRespValidationError) ErrorName() string {
	return "GetThreatenFileStatisticsRespValidationError"
}

// Error satisfies the builtin error interface
func (e GetThreatenFileStatisticsRespValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetThreatenFileStatisticsResp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetThreatenFileStatisticsRespValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetThreatenFileStatisticsRespValidationError{}
