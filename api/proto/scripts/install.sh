#!/usr/bin/env bash

PROTO_ROOT=$(pwd)

# 安装所需工具
install_tools() {
  echo "Installing protoc and related tools..."

  rm -rf "${PROTO_ROOT:?}/bin"
  mkdir -p "$PROTO_ROOT"/bin

  # 存在网络问题自行配置代理
  export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

  PROTOC_VERSION="28.3"
  case "$(uname -s)" in
  Darwin)
    PROTOC_ZIP="protoc-${PROTOC_VERSION}-osx-aarch_64.zip"
    ;;
  Linux)
    PROTOC_ZIP="protoc-${PROTOC_VERSION}-linux-x86_64.zip"
    ;;
  CYGWIN* | MINGW* | MSYS*)
    # Windows
    PROTOC_ZIP="protoc-${PROTOC_VERSION}-win64.zip"
    ;;
  *)
    echo "Unsupported OS"
    exit 1
    ;;
  esac
  PROTOC_URL="https://github.com/protocolbuffers/protobuf/releases/download/v${PROTOC_VERSION}/${PROTOC_ZIP}"

  # 安装 protoc
  TMP_DIR=$(mktemp -d)
  curl -L $PROTOC_URL -o "$TMP_DIR"/protoc.zip
  unzip -q "$TMP_DIR"/protoc.zip -d "$TMP_DIR"
  cp "$TMP_DIR"/bin/protoc "$PROTO_ROOT"/bin/
  chmod +x "$PROTO_ROOT"/bin/protoc
  rm -rf "$TMP_DIR"

  # 安装 protoc 插件
  export GOBIN=$PROTO_ROOT/bin
  go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.35.2
  go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.5.1
  go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-grpc-gateway@v2.25.1
  go install github.com/grpc-ecosystem/grpc-gateway/v2/protoc-gen-openapiv2@v2.25.1
  go install github.com/envoyproxy/protoc-gen-validate@v1.2.1

  echo "Protoc and tools installed successfully."
}

install_tools

#  go install github.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger@v2.25.1
