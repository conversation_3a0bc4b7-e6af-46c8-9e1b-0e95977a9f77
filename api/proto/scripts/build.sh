#!/bin/bash

# 输出目录
output_path="./pkg"
# proto文件根目录
proto_root="./proto"
# 引用的三方proto文件
include_root="./include"

YELLOW="\033[33m"
RESET="\033[0m"

# output logo.
source ./scripts/logo.sh
print_logo

echo "🛠 开始编译proto文件..."
echo ""

proto_files=""
for file in $(find proto -name "*.proto"); do
  echo -e "${YELLOW}"[*]" $file${RESET}"
  proto_files="$proto_files $file"
done

# 编译proto文件，使用bin目录下工具链
export PATH="./bin:$PATH"
# 自include或项目根目录下开始导入其他文件
bin/protoc -I "$include_root":"$proto_root" \
  --go_out=$output_path \
  --go_opt=paths=source_relative \
  --go-grpc_out=$output_path \
  --go-grpc_opt=paths=source_relative \
  --grpc-gateway_out=$output_path \
  --grpc-gateway_opt=paths=source_relative \
  --validate_out=paths=source_relative,lang=go:$output_path \
  $proto_files || exit

echo ""
echo "✅ 编译完成!"

#  --openapiv2_out=$output_path \
#  --openapiv2_opt logtostderr=true \
#  --validate_out=paths=source_relative,lang=go:$output_path \

# swagger.
#for dir in $(find $output_path/proto -type d); do
#  dir_name=$(basename $dir)
#  for file in $(find $dir -type f); do
#    file_name=$(basename $file)
#    if [[ $file_name == *"swagger.json" ]]; then
#      swagger_dir="docs/openapi/$dir_name"
#      if [ ! -d $swagger_dir ]; then
#        mkdir -p $swagger_dir
#      else
#        rm -rf "$swagger_dir/*"
#      fi
#      mv $file $swagger_dir/
#    fi
#  done
#done
