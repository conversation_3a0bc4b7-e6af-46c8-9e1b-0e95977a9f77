#!/bin/bash

# figlet
print_logo() {
  local GREEN="\033[32m"
  local YELLOW="\033[33m"
  local RED="\033[31m"
  local RESET="\033[0m"

  echo -e ""
  echo -e "${GREEN} __   ____ _ _ __ (_)                  _        _            __  __            ${RESET}"
  echo -e "${GREEN} \ \ / / _  | '_ \| |  _ __  _ __ ___ | |_ ___ | |__  _   _ / _|/ _| ___ _ __  ${RESET}"
  echo -e "${GREEN}  \ V / (_| | |_) | | | '_ \| '__/ _ \| __/ _ \| '_ \| | | | |_| |_ / _ \ '__| ${RESET}"
  echo -e "${GREEN}   \_/ \__,_| .__/|_| | |_) | | | (_) | || (_) | |_) | |_| |  _|  _|  __/ |    ${RESET}"
  echo -e "${GREEN}            |_|       | .__/|_|  \___/ \__\___/|_.__/ \__,_|_| |_|  \___|_|    ${RESET}"
  echo -e "${GREEN}                      |_|                                                      ${RESET}"
  echo -e ""
  echo -e ""
}
