syntax = "proto3";
package fizz;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/fizz";

import "agent/command.proto";

// 消息中心服务
service Fizz {
  
  // 添加单播消息
  rpc AddUnicast(AddUnicastReq) returns (AddUnicastResp) {}
  // 添加组播消息
  rpc AddMulticast(AddMulticastReq) returns (AddMulticastResp) {}
  // 添加广播消息
  rpc AddBroadcast(AddBroadcastReq) returns (AddBroadcastResp) {}
  // 消息推送
  rpc PushMsg(PushMsgReq) returns (PushMsgResp) {}
  // 调试接口
  rpc Debug(DebugReq) returns (DebugResp) {}
}


message AddUnicastReq {
  string machine_id = 1; // 设备唯一标识
  MsgContent content = 2; // 消息内容
  bool allow_delete = 3; // 消息是否允许删除
}
message AddUnicastResp {
}

message AddMulticastReq {
  repeated int64 group_ids = 1; // 分组id
  OsType os = 2; // 操作系统类型
  MsgContent content = 3; // 消息内容
  bool allow_delete = 4; // 消息是否允许删除
}
message AddMulticastResp {
}

message AddBroadcastReq {
  OsType os = 1; // 操作系统类型
  MsgContent content = 2; // 消息内容
  bool allow_delete = 3; // 消息是否允许删除
}
message AddBroadcastResp {
}

message PushMsgReq {
  repeated string machine_ids = 1; // 设备列表，注意：如果为空代表广播消息
  bool failed_to_cache = 2; // 推送失败是否缓存
  bool random = 3; // 随机选取一个在线主机下发
  bool async = 4; // 是否开启异步推送（默认同步推送）
  agent.Command cmd_id = 5; // 消息类型
  bytes data = 6; // 需要推送的内容
}
message PushMsgResp {
}

message DebugReq {
  oneof cond {
    string machine_id = 1; // 设备唯一标识
    int64 group_id = 2; // 分组id
  }
}
message DebugResp {
}

// 消息内容
message MsgContent {
  string uuid = 1; // 消息唯一标识（通过uuid进行去重操作）
  MsgCategoryType category = 2; // 消息分类
  bytes content = 3; // 消息内容
  int64 size = 4; // 消息大小
}

// 操作系统类型
enum OsType {
  OS_TYPE_UNKNOWN = 0; // 类型未知
  OS_TYPE_WINDOWS = 1;
  OS_TYPE_LINUX = 2;
}

// 消息类型枚举
enum MsgContentType {
  MS_TYPE_UNKNOWN = 0; // 未知
  MS_TYPE_UNICAST = 1; // 单播消息
  MS_TYPE_MULTICAST = 2; // 组播消息
  MS_TYPE_BROADCAST = 3; // 广播消息
}

// 消息内容存储类型枚举
enum MsgStoreType {
  MS_STORE_TYPE_UNKNOWN = 0; // 未知
  MS_STORE_TYPE_DB = 1; // 数据库
  MS_STORE_TYPE_KV = 2; // kv存储
}

// 消息内容分类类型枚举
enum MsgCategoryType {
  MSG_CATEGORY_TYPE_UNKNOWN = 0;
  // 主机配置变更通知消息使用 10xx 格式
  MSG_CATEGORY_TYPE_AGENT_GROUP_ID_CHANGE = 1001; // unicast, 主机, 移动分组
  // 策略消息使用 20xx 格式
  MSG_CATEGORY_TYPE_POLICY_RISK_SWITCH = 2001; // multicast, 策略, 线索上报开关
  MSG_CATEGORY_TYPE_POLICY_GLOBAL_SWITCH = 2002; // multicast, 策略, 全局开关
  MSG_CATEGORY_TYPE_POLICY_FILE_SCAN = 2003; // multicast, 策略, 文件扫描配置
  MSG_CATEGORY_TYPE_POLICY_ABNORMAL_LOGIN = 2004; // multicast, 策略, 异常登录配置
  MSG_CATEGORY_TYPE_POLICY_UNINSTALL_PASSWORD = 2005; // broadcast, 策略, agent 卸载密码
  MSG_CATEGORY_TYPE_POLICY_WHITE_LIST = 2006; // multicast, 策略, 白名单更新/删除
  MSG_CATEGORY_TYPE_POLICY_CLOUD_WHITE_LIST = 2007; // broadcast, 策略, 云白名单更新/删除

  // 线索、文件处置、取证消息使用 30xx格式
  MSG_CATEGORY_TYPE_HANDLE_FILES = 3001; // unicast, 文件处置, 包含隔离、删除、恢复操作
  MSG_CATEGORY_TYPE_OBTAIN_EVIDENCE = 3002; // unicast, 取证
}

// 消息队列数据结构
message UnicastSubject {
  string machine_id = 1;
  int64 max_seq = 2;
  int64 content_id = 3;
  string uuid = 4; // 消息唯一标识
  MsgStoreType type = 5; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
}
message MulticastSubject {
  int64 group_id = 1;
  int64 max_seq = 2;
  int64 content_id = 3;
  string uuid = 4; // 消息唯一标识
  MsgStoreType type = 5; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
  OsType os = 6; // 操作系统类型
}
message BroadcastSubject {
  int64 max_seq = 1;
  int64 content_id = 2;
  string uuid = 3; // 消息唯一标识
  MsgStoreType type = 4; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
  OsType os = 5; // 操作系统类型
}
message MachineProfileSubject {
  string machine_id = 1;
}
message ContentProfileSubject {
  int64 content_id = 1;
}

// 设备缓存数据结构（key: machine_id, value: profile）
message MachineProfile {
  string machine_id = 1; // 设备唯一标识
  int64 max_seq = 2; // 单播消息最大序号

  message Content {
    int64 id = 1; // 消息id（消息中心数据库主键）
    int64 seq = 2; // 设备对应的序号
    string uuid = 3; // 消息唯一id（接口调用时调用方传的消息唯一标识）
    int32 type = 4; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
  }
  repeated Content contents = 3; // 单播消息内容列表（倒序）
}

// 组播消息缓存数据结构（key: group_id, value: profile）
message MulticastProfile {
  int64 group_id = 1; // 分组Id
  int64 max_seq = 2; // 组播消息最大序号

  message Content {
    int64 id = 1; // 组播消息id
    int64 seq = 2; // 组播消息对应的序号
    string uuid = 3; // 消息唯一id（接口调用时调用方传的消息唯一标识）
    int32 type = 4; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
    OsType os = 5; // 操作系统类型
  }
  repeated Content contents = 3; // 组播消息内容列表（倒序）
}

// 广播消息缓存数据结构（key: broadcast, value: profile）
message BroadcastProfile {
  int64 max_seq = 1; // 广播消息最大序号

  message Content {
    int64 id = 1; // 广播消息id
    int64 seq = 2; // 广播消息对应的序号
    string uuid = 3; // 消息唯一id（接口调用时调用方传的消息唯一标识）
    int32 type = 4; // 消息存储类型，减少缓存消耗使用int32 1:tidb 2:tikv
    OsType os = 5; // 操作系统类型
  }
  repeated Content contents = 2; // 广播消息内容列表（倒序）
}

// 消息内容缓存数据结构（key: machine_id, value: profile）
message ContentProfile {
  int32 category = 1; // 消息分类（枚举类型存入DB再取出时转换存在问题）
  int32 msg_type = 2; // 消息存储类型（枚举类型存入DB再取出时转换存在问题）
  bytes content = 3; // 消息内容
  int64 size = 4; // 消息大小
  OsType os = 5; // 消息对应的操作系统类型
}
