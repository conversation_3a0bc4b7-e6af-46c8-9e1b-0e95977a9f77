syntax = "proto3";
package yone;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/yone";

import "agent/policy.proto";
import "agent/virus_file.proto";
import "agent/acdr.proto";
import "google/protobuf/empty.proto";
import "conan/common.proto";
import "mq/detect.proto";
import "mq/clue.proto";

service Yone {
  // 依据进程唯一标识，批量获取进程链节点列表，不同进程链的节点混合同一个列表返回
  rpc GetProcessChainsNodes(ProcChainsNodesReq) returns (ProcChainsNodesResp) {}

  // 基于进程链叶子节点批量查询进程链列表
  rpc GetProcessChains(ProcChainsReq) returns (ProcChainsResp) {}

  // 检索外联信息
  rpc SearchOutreachInfo(OutreachSearchReq) returns (OutreachSearchResp) {}

  // 创建文件检测任务
  rpc CreateFileTask(CreateFileTaskReq) returns (CreateFileTaskResp) {}

  // 获取文件检测任务状态
  rpc GetFileTasksState(GetFileTasksStateReq) returns (GetFileTasksStateResp) {}

  // 文件档案列表
  rpc FileArchiveList(ArchiveListReq) returns (ArchiveListResp) {}

  // 文件档案统计数据
  rpc FileArchiveStat(ArchiveStatReq) returns (ArchiveStatResp) {}

  // 文件档案信息
  rpc FileArchiveInfo(ArchiveInfoReq) returns (ArchiveInfo) {}

  // 文件所在主机列表
  rpc FileHostList(FileHostListReq) returns (FileHostListResp) {}

  // 文件档案主机视图
  rpc HostViewList(HostViewReq) returns (HostViewResp) {}

  // 主机文件统计信息
  rpc HostFileStat(HostStatReq) returns (HostStatResp) {}

  // 主机文件列表
  rpc HostFileList(HostFileListReq) returns (HostFileListResp) {}

  // 文件搜索
  rpc FileSearch(FileSearchReq) returns (FileSearchResp) {}

  // 外联日志列表
  rpc OutreachLogList(OutreachLogReq) returns (OutreachLogResp) {}

  // 外联日志每日统计数据
  rpc OutreachDailyStat(OutreachDailyStatReq) returns (OutreachDailyStatResp) {}

  // 外联日志总数
  rpc OutreachTotal(OutreachTotalReq) returns (OutreachTotalResp) {}

  // 文件创建日志列表
  rpc FileCreateLogList(FileCreateLogReq) returns (FileCreateLogResp) {}

  // 进程日志列表
  rpc ProcessLogList(ProcessLogReq) returns (ProcessLogResp) {}

  // 获取日志检索结构
  rpc GetSearchSchemas(google.protobuf.Empty) returns (SearchSchemas) {}

  // 获取各主机上报进度统计信息
  rpc MacProgressStat(MacProgressStatReq) returns (MacProgressStatResp) {}

  // 主机上报进度分页数据
  rpc MacProgressList(MacProgressListReq) returns (MacProgressListResp) {}
}

// 日志接收服务异常状态码
enum Codes {
  CODE_UNKNOWN = 0;
  ERROR_INVALID_PARAMETERS = 20001; // 参数异常
  ERROR_FILE_ARCHIVE_NOT_FOUND = 20002;  // 文件档案未找到
}

// 文件检测结果
enum DetectResult {
  DETECT_RESULT_INVALID = 0; // 结果异常
  DETECT_RESULT_WHITE = 1; // 文件为白
  DETECT_RESULT_BLACK = 2; // 文件为黑
  DETECT_RESULT_GRAY = 3; // 文件为灰
  DETECT_RESULT_UNKNOWN = 4; // 结果未知
}

// 文件检测状态
enum FileTaskStatus {
  TASK_STATUS_NONE = 0; // 无检测结果
  TASK_STATUS_FETCH_WAIT = 1; // 待提取文件
  TASK_STATUS_FETCHING = 2; // 文件提取中
  TASK_STATUS_FETCH_SUCCESS = 3; // 文件提取成功
  TASK_STATUS_FETCH_FAILED = 4; // 文件提取失败
  TASK_STATUS_FETCH_TIMEOUT = 5; // 文件提取超时
  TASK_STATUS_DETECT_QUEUE = 6; // 文件已进入检测队列
  TASK_STATUS_DETECTING = 7; // 文件检测中
  TASK_STATUS_DETECT_SUCCESS = 8; // 文件检测成功
  TASK_STATUS_DETECT_FAILED = 9; // 文件检测失败
  TASK_STATUS_DETECT_TIMEOUT = 10; // 文件检测超时
}

message FileReceiveReq {
  string mac = 1;
  repeated agent.VirusDetectItem  files = 2; // agent同步的文件信息
}

enum OsType {
  OS_TYPE_UNKNOWN = 0; // 类型位置
  OS_TYPE_WINDOWS = 1;
  OS_TYPE_LINUX = 2;
}

message FileReceiveResp {
  int64 success_num = 1;
}

message VirusReceiveReq {
  string mac = 1;
  agent.MemProtectVirusFileInfo virus_info = 2;
}

message VirusReceiveResp {
  int64 success_num = 1;
}

message OutreachReceiveReq {
  repeated agent.InternalOutreachInfoWithMac outreaches = 1;
}

message OutreachReceiveResp {
  int64 success_num = 1;
}

message ProcChainsNodesReq {
  repeated string uniques = 1; // 进程唯一标识列表，unique生成方式和历史逻辑想通: hex(sha256("mac:pid:startTime")
  bool only_leaf = 2; // 仅查询叶子节点，不会递归向上查询父节点
}

message ProcChainsNodesResp {
  repeated ProcChainNode proc_nodes = 1; // 进程节点列表，已基于进程unique去重
  repeated ProcChainFile files = 2; // 进程链中的文件信息，已基于文件unique去重
}

message ProcChainsReq {
  repeated ProcNodeBean beans = 1; // 叶子节点列表
}

message ProcChainsResp {
  repeated ProcChain chains = 2; // 进程链列表
}

message ProcChain {
  // 使用进程链第一个节点(叶子节点)的唯一标识作为整个继承链的唯一标识
  string unique = 1;
  // 进程链包含的节点列表，进行查询的叶子节点将出现在进程链的第一个，之后依次为父进程，父父进程节点，直至无父进程节点结束
  repeated ProcChainNodeWithFile nodes = 2;
}

message ProcNodeBean {
  string mac = 1; // 主机唯一标识
  int64 pid = 2; // 进程id
  int64 start_time = 3; // 进程启动时间，纳秒级时间戳
}

message ProcChainNode {
  string unique = 1; // 进程唯一标识
  int64 pid = 2; // 进程id
  int64 start_time = 3; // 进程启动时间
  int64 ppid = 4; // 父进程id
  int64 parent_start_time = 5; // 父进程启动时间
  int64 epid = 6; // exec 源进程 pid
  int64 exec_start_time = 7; //  exec 源进程启动时间
  string name = 8; // 进程名
  string command = 9; // 启动命令
  string username = 10; // 进程用户
  int64 euid = 11; // 进程euid
  string file_unique = 12; // 进程文件唯一标识
}

message ProcChainNodeWithFile {
  string unique = 1; // 进程唯一标识
  int64 pid = 2; // 进程id
  int64 start_time = 3; // 进程启动时间
  int64 ppid = 4; // 父进程id
  int64 parent_start_time = 5; // 父进程启动时间
  int64 epid = 6; // exec 源进程 pid
  int64 exec_start_time = 7; //  exec 源进程启动时间
  string name = 8; // 进程名
  string command = 9; // 启动命令
  string username = 10; // 进程用户
  int64 euid = 11; // 进程euid
  ProcChainFile file_info = 12; // 进程文件信息
}

message ProcChainFile {
  string unique = 1; // 文件唯一标识，unique生成方式和历史逻辑想通: hex(sha256("mac:path:sha256")
  string name = 2; // 文件名
  string path = 3; // 文件路径
  string md5 = 4; // 文件md5
  string sha256 = 5; // 文件sha256
  int64 size = 6; // 文件大小
  int64 mtime = 7; // 更改时间
  int64 ctime = 8; // 创建时间
  int64 atime = 9; // 访问时间
  string permission = 10; // 文件权限
  string company_name = 11; // 文件厂商名
}

message OutreachSearchReq {
  string src_ip = 1; // 源ip
  int32 src_port = 2; // 源端口
  string dest_ip = 3; // 目的ip
  int32 dest_port = 4; // 目的端口
  string protocol = 5; // 通信协议
  int64 begin_time = 6; // 记录创建起始时间，纳秒级时间戳
  int64 end_time = 7; // 记录创建终止时间，纳秒级时间戳
  int32 limit = 8;
}

message OutreachSearchResp {
  repeated OutreachInfo outreaches = 1; // 外联信息列表
}

message CreateFileTaskReq {
  repeated string md5s = 1; // 需要创建检测任务的文件md5列表
  repeated mq.DetectEngine engines = 2; // 需要哪些引擎进行检测
}

message CreateFileTaskResp {}

message GetFileTasksStateReq {
  repeated string md5s = 1; // 需要查询检测状态的文件md5列表
}

message GetFileTasksStateResp {
  repeated FileTaskState items = 1; // 各文件检测状态
}

message FileTaskState {
  string md5 = 1;
  string sha256 = 2;
  FileTaskStatus status = 3; // 任务检测状态
  DetectResult result = 4; // 任务检测结果
  repeated mq.DetectEngine engines = 5; // 检测引擎列表
  repeated mq.ClueDetectEngine engines_clue = 6; // 基于线索的检测引擎列表
}


message OutreachInfo {
  string src_ip = 1; // 源ip
  int32 src_port = 2; // 源端口
  string dest_ip = 3; // 目的ip
  int32 dest_port = 4; // 目的端口
  string protocol = 5; // 通信协议
  int64 pid = 6; // 发起外联的进程pid
  int64 start_time = 7; // 发起外联的进程启动时间
  int64 found_time = 8; // 客户端发现时间，纳秒级时间戳
  int64 created_at = 9; // 记录创建时间，纳秒级时间戳
  string host_id = 10; // 主机唯一标识，即mac
  string hostname = 11; // 主机名
  string host_ip = 12; // 主机ip
  string host_os = 13; // 主机操作系统
  string host_group = 14; // 主机分组名
  ProcChain chain = 15; // 进程链信息
}


message ArchiveListReq {
  ArchiveListFilter filter = 1;
  Page page = 2;
  string orderby = 3;
}

message ArchiveListFilter {
  string search_data = 1; // 文件md5，sha256或文件名
  DetectResult detect_result = 2; // 检测结果
  int32 terminal_start = 3; // 终端数最小值
  int32 terminal_end = 4; // 终端数最大值
}

message Page {
  int64 page_index = 1;
  int64 page_size = 2;
}

message ArchiveListResp {
  repeated ArchiveItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message ArchiveItem {
  string md5 = 1;
  string sha256 = 2;
  string sha1 = 3;
  string file_name = 4;
  string file_size = 5;
  string file_created_at = 6; // 文件创建时间
  string file_found_at = 7; // 文件鉴定时间
  DetectResult detect_result = 8; // 文件检测结果
  int64 terminal_count = 9; // 关联终端数量
}

message ArchiveStatReq {
  conan.TimeRange time_range = 1;
}

message ArchiveStat {
  int64 total_count = 1; // 数量总量
  int64 daily_count = 2; // 今日上传量
  int64 server_count = 3; // 终端发现数量
}

message ArchiveStatResp {
  ArchiveStat black = 1;
  ArchiveStat white = 2;
  ArchiveStat gray = 3;
  ArchiveStat unknown = 4;
}

message ArchiveInfoReq {
  string md5 = 1;
}

message ArchiveInfo {
  string md5 = 1;
  string sha256 = 2;
  string sha1 = 3;
  string file_name = 4;
  string file_type = 5;
  string file_size = 6;
  string file_vendor = 7;
  string file_version = 8;
  string event_at = 9;
  DetectResult detect_result = 10;
  int64 terminal_count = 11;
  repeated SignInfo sign_info = 12;
}

// 文件签名信息
message SignInfo {
  string serial = 1; // 签名序列号
  string issuer_name = 2; // 签名颁发者
  string customer = 3; // 签名使用者
  string thumbprint = 4; // 签名指纹
  string result = 5; // 签名状态
  string description = 6; // 签名描述
  string not_after = 7; // 签名有效期起始时间
  string not_before = 8; // 签名有效期到期时间
  string sign_algorithm = 9; // 签名算法
  string sign_hash_algorithm = 10; // 签名哈希算法
  string version = 11; // 签名版本
  int32 sign_status_info = 12; // 签名状态
}

message FileHostListReq {
  FileHostFilter filter = 1;
  Page page = 2;
}

message FileHostFilter {
  string md5 = 1;
}

message FileHostListResp {
  repeated FileHostItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message FileHostItem {
  string md5 = 1;
  string sha256 = 2;
  string sha1 = 3;
  string file_name = 4;
  string file_type = 5;
  string file_size = 6;
  string file_path = 7;
  string file_created_at = 8;
  string file_found_at = 9;
  string mac = 10;
  string ip = 11;
  int32 osver = 12;
  string os = 13;
  string host_name = 14;
  int64 group_id = 15;
  string group_name = 16;
  int32 online = 17;
}

message HostViewReq {
  HostViewFilter filter = 1;
  Page page = 2;
}

message HostViewFilter {
  string ip = 1;
  int64 file_count_start = 2;
  int32 file_count_end = 3;
}

message HostViewResp {
  repeated HostViewItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message HostViewItem {
  string ip = 1;
  int32 osver = 2;
  string os = 3;
  string host_name = 4;
  int64 group_id = 5;
  string group_name = 6;
  string mac = 7;
  int64 file_count = 8;
  int32 online = 9;
}

message HostStatReq {
  repeated string macs = 1;
}

message HostStatResp {
  repeated HostFileStat hosts = 1;
}

message HostFileStat {
  string mac = 1; // 主机唯一标识
  int64 count = 2; // 文件总数
}

message HostFileListReq {
  HostFileFilter filter = 1;
  Page page = 2;
}

message HostFileFilter {
  string mac = 1; // 主机唯一标识
  string file_name = 2; // 文件名
  string md5 = 3; // 文件md5
}

message HostFileListResp {
  repeated FileItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message FileItem {
  string md5 = 1;
  string sha256 = 2;
  string sha1 = 3;
  string file_name = 4;
  string file_type = 5;
  string file_size = 6;
  string file_created_at = 7; // 文件创建时间
  string file_found_at = 8; // 文件鉴定时间
}

message FileSearchReq {
  string search_data = 1; // 文件md5,sha256,sha1或文件名
}

message FileSearchResp {
  string md5 = 1;
}

message OutreachDailyStatReq {
  conan.TimeRange time_range = 1;
}

message OutreachDailyStatResp {
  repeated OutreachDailyStat items = 1;
}

message OutreachDailyStat {
  string day = 1;  // format example: 2025-01-01
  int64 count = 2;
}

message OutreachTotalReq {
  conan.TimeRange time_range = 1;
}

message OutreachTotalResp {
  int64 total = 1;
}


message LogFilter {
  OsType os = 1;  // 系统类型 1:windows 2:linux
  string date_range = 2; // 时间范围筛选
  string search_data = 3; // 检索数据
}

message OutreachLogReq {
  LogFilter filter = 1;
  Page page = 2;
}

message OutreachLogResp {
  repeated OutreachLogItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message OutreachLogItem {
  string machine_id = 1; // 终端id
  string ip = 2; // 终端ip
  string hostname = 3; // 终端主机名称
  int32 os = 4; // 终端操作系统
  int64 pid = 5; // 外联进程pid
  string pname = 6; // 外联进程名
  int64 ppid = 7;
  string ppname = 8;
  int64 epid = 9;
  string user = 10; // 进程用户名
  int64 euid = 11;
  string cmd = 12;
  string start_at = 13;
  string path = 14; // 程序路径
  string md5 = 15; // 程序md5
  string sha256 = 16; // 程序sha256
  string sha1 = 17; // 程序sha1
  string size = 18; // 程序大小
  string ctime = 19; // 文件创建时间
  string mtime = 20; // 文件修改时间
  string atime = 21; // 文件访问时间
  string attrs = 22; // 文件属性
  string signs = 23; // 文件签名信息
  string uuid = 24; // 日志唯一标识
  string source_ip = 25; // 源ip
  int32 source_port = 26; // 源端口
  string remote_ip = 27; // 目的ip
  int32 remote_port = 28; // 目的端口
  string protocol = 29; // 通信协议
  string trigger_time = 30; // 发现时间
  string create_at = 31; // 记录创建时间
}

message FileCreateLogReq {
  LogFilter filter = 1;
  Page page = 2;
}

message FileCreateLogResp {
  repeated FileCreateLogItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message FileCreateLogItem {
  string machine_id = 1; // 终端id
  string ip = 2; // 终端ip
  string hostname = 3; // 终端主机名称
  int32 os = 4; // 终端操作系统
  int64 pid = 5; // 外联进程pid
  string start_at = 6;
  string path = 7; // 程序路径
  string md5 = 8; // 程序md5
  string sha256 = 9; // 程序sha256
  string sha1 = 10; // 程序sha1
  string size = 11; // 程序大小
  string ctime = 12; // 文件创建时间
  string mtime = 14; // 文件修改时间
  string atime = 15; // 文件访问时间
  string attrs = 16; // 文件属性
  string signs = 17; // 文件签名信息
  string permission = 18; // 文件权限信息
  string unique_key = 19; // 文件唯一标识
  string trigger_time = 20; // 发现时间
  string create_at = 21; // 记录创建时间
}

message ProcessLogReq {
  LogFilter filter = 1;
  Page page = 2;
}

message ProcessLogResp {
  repeated ProcessLogItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message ProcessLogItem {
  string machine_id = 1; // 终端id
  string ip = 2; // 终端ip
  string hostname = 3; // 终端主机名称
  int32 os = 4; // 终端操作系统
  string ppname = 5;
  int64 ppid = 6;
  int64 epid = 7;
  string pname = 8;
  int64 pid = 9;
  string user = 10;
  int64 euid = 11;
  string cmd = 12;
  string start_at = 13;
  string path = 14; // 程序路径
  string md5 = 15; // 程序md5
  string sha256 = 16; // 程序sha256
  string sha1 = 17; // 程序sha1
  string size = 18; // 程序大小
  string ctime = 19; // 文件创建时间
  string mtime = 20; // 文件修改时间
  string atime = 21; // 文件访问时间
  string attrs = 22; // 文件属性
  string signs = 23; // 文件签名信息
  string unique_key = 24; // 文件唯一标识
  string trigger_time = 25; // 发现时间
  string create_at = 26; // 记录创建时间
}

message SearchSchemas {
  repeated SearchField process_linux = 1;
  repeated SearchField process_windows = 2;
  repeated SearchField outreach_linux = 3;
  repeated SearchField outreach_windows = 4;
  repeated SearchField filecreate_linux = 5;
  repeated SearchField filecreate_windows = 6;
}

message SearchField {
  string field = 1; // 字段名, 用于搜索条件中
  string label = 2; // 字段标签, 仅用于显示
  int32 type = 3; // 字段值类型, 限制字段可比较的数据类型
  string placeholder = 4; // 字段占位符, 用于输入提示
  repeated int32 operators = 5; // 字段操作符, 限制可用的比较方式
}

enum ProgressType {
  PROGRESS_TYPE_UNKNOWN = 0;
  PROGRESS_TYPE_THREATEN = 1; // 文件威胁
  PROGRESS_TYPE_SNAPSHOT = 2; // 文件快照
}

message MacProgressStatReq {
  ProgressType type = 1;
}

message MacProgressStatResp {
  int64 progressing_count = 1; // 正在扫描中的终端数
  int64 terminal_total = 2 ; // 终端总数
  string progressing_percent = 3; // 正在扫描中的百分比
  int64 sum_found_file_count = 4; // 累计发现的文件总数
}

message MacProgressListReq {
  MacProgressFilter filter = 1;
  Page page = 2;
}

message MacProgressFilter {
  ProgressType type = 1;
  repeated int32 status = 2;
  string ip = 3;
  string host_name = 4;
}

message MacProgressListResp {
  repeated MacProgressItem items = 1;
  int64 total = 2;
  int64 page_index = 3;
}

message MacProgressItem {
  string mac = 1; // 主机ID
  string ip = 2; // 主机IP
  int32 os_type = 3; // 系统类型
  string os = 4; // 操作系统名
  string host_name = 5;// 主机名
  int64 group_id = 6; // 分组ID
  string group_name = 7; // 分组名
  int32 host_online = 8; // 主机状态
  int32 status = 9; // 采集状态 1:开始 2:扫描中 3:上传中(废弃) 4:暂停 5:完成
  string status_text = 10; // 采集状态文本
  string progress = 11; // 进度百分比
  string mac_addr = 12; // 终端mac地址
}