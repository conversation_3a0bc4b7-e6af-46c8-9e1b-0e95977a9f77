syntax = "proto3";
package nasus;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/nasus";
// import "include/google/protobuf/any.proto";
// import "include/google/protobuf/validate.proto";
// import "google/api/annotations.proto";

// 配置中心服务
service Nasus {

  // 获取业务配置
  rpc GetConf(GetConfReq) returns (GetConfResp) {};
  // 更新业务配置
  rpc UpdateConf(UpdateConfReq) returns (UpdateConfResp) {};
  // 添加配置
  rpc AddCM(AddCMReq) returns (AddCMResp) {};
  // 删除配置
  rpc DelCM(DelCMReq) returns (DelCMResp) {};
  // 更新配置
  rpc UpdateCM(UpdateCMReq) returns (UpdateCMResp) {};
  // 单条查询
  rpc QueryCM(QueryCMReq) returns (QueryCMResp) {};
  // 列表查询
  rpc ListCM(ListCMReq) returns (ListCMResp) {};
  // 监听配置
  rpc WatchCM(WatchCMReq) returns (stream WatchCMResp) {};
  // 节点列表
  rpc ListNode(ListNodeReq) returns (ListNodeResp) {};
  // 监听节点
  rpc WatchNode(WatchNodeReq) returns (stream WatchNodeResp) {};
  // 单条查询
  rpc GetEndpoints(GetEndpointsReq) returns (GetEndpointsResp) {};
  // 列表
  rpc ListEndpoints(ListEndpointsReq) returns (ListEndpointsResp) {};
  // 监听Endpoints
  rpc WatchEndpoints(WatchEndpointsReq) returns (stream WatchEndpointsResp) {};
  // 获取Pod
  rpc GetPod(GetPodReq) returns (GetPodResp) {};
  // 列表Pod
  rpc ListPod(ListPodReq) returns (ListPodResp) {};
  // 删除Pod
  rpc DelPod(DelPodReq) returns (DelPodResp) {};
  // 更新Pod
  rpc UpdatePod(UpdatePodReq) returns (UpdatePodResp) {};
  // 获取Pod日志
  rpc GetPodLogs(GetPodLogsReq) returns (GetPodLogsResp) {};
  // 订阅Pod
  rpc WatchPod(WatchPodReq) returns (stream WatchPodResp) {};
  // 扩缩容Pod
  rpc ScalePod(ScalePodReq) returns (ScalePodResp) {};
  // 获取Service
  rpc GetService(GetServiceReq) returns (GetServiceResp) {};
}

message GetConfReq {
}
message GetConfResp {
  string data = 1; // 文件内容
}

message UpdateConfReq {
  string data = 1; // 文件内容
}
message UpdateConfResp {
}

message AddCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  // map<string, google.protobuf.Any> data = 3; // 文件内容
  string data = 3; // 文件内容
}
message AddCMResp {
}

message DelCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  string data = 3; // 文件内容
}
message DelCMResp {
}

message UpdateCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  string data = 3; // 文件内容
}
message UpdateCMResp {
}

message QueryCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
}
message QueryCMResp {
  string data = 1; // 文件内容
}

message ListCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  int64 limit = 3; // 分页拉取条数
  string continue = 4; // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}
message ListCMResp {
  repeated CMItem data = 1;
  string continue = 2; // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}
message CMItem {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  string data = 3; // 文件内容
}

message WatchCMReq {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
}
message WatchCMResp {
  string name_space = 1; // 命名空间
  string file_name = 2; // 文件名
  map<string, string> data = 3; // 文件内容
}

message ListNodeReq {
  string name_space = 1; // 命名空间
  string label_selector = 2; // 标签
  int64 limit = 3; // 分页拉取条数
  string continue = 4; // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}
message ListNodeResp {
  repeated NodeItem data = 1;
  string continue = 2; // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}
message NodeItem {
  string name = 1; // 节点名
  string ip = 2; // 节点ip
  string hostname = 3; // 节点主机名
  string os = 4; // 操作系统
  string os_version = 5; // 操作系统版本
  string kernel_version = 6; // 内核版本
  string kernel_release = 7; // 内核发行版
  string kernel_arch = 8; // 内核架构
}

message WatchNodeReq {
}
message WatchNodeResp {
  repeated NodeItem data = 1;
}

message GetEndpointsReq {
  string name_space = 1; // 命名空间
  string service_name = 2; // 服务名
}
message GetEndpointsResp {
  repeated string pod_ips = 1; // endpoints pod ip 列表
}

message ListEndpointsReq {
  string name_space = 1; // 命名空间
  string service_name = 2; // 服务名
  int64 limit = 3;
  string continue = 4;
}
message ListEndpointsResp {
  repeated string pod_ips = 1; // endpoints pod ip 列表
  string continue = 2;
}

message WatchEndpointsReq {
  string name_space = 1; // 命名空间
  string service_name = 2; // 服务名
}
message WatchEndpointsResp {
  repeated string pod_ips = 1; // endpoints pod ip 列表
}

message PodItem {
  string name_space = 1; // 命名空间
  string pod_name = 2; // pod名
  string ip = 3; // pod ip
  string host_ip = 4; // pod host ip
  string status = 5; // pod状态
  string create_time = 6; // pod创建时间
  string update_time = 7; // pod更新时间
  string node_name =8; // pod所在节点名
}

message GetPodReq {
  string name_space = 1; // 命名空间
  string pod_name = 2; // pod名
}
message GetPodResp {
  PodItem data = 1;
}

message ListPodReq {
  string name_space = 1;
  string label_selector = 2;
  int64 limit = 3;
  string continue = 4;
}
message ListPodResp {
  repeated PodItem data = 1;
  string continue = 2; // 为空代表没有数据，不需要继续拉取。反之，通过continue字段继续拉取。实现分页逻辑。
}

message DelPodReq {
  string name_space = 1; // 命名空间
  string pod_name = 2; // pod名
}
message DelPodResp {
}

message GetPodLogsReq {
  string name_space = 1; // 命名空间
  string pod_name = 2; // pod名
  string container_name = 3; // 容器名
  int64 tail_lines = 4; // 读取日志条数
  string since_seconds = 5; // 读取日志时间
}
message GetPodLogsResp {
  string data = 1; // 文件内容
}

message UpdatePodReq {
  string name_space = 1; // 命名空间
  string pod_name = 2; // pod名
  string container_name = 3; // 容器名
  string image = 4; // 镜像
  string restart_policy = 5; // 重启策略
  string command = 6; // 命令
  string args = 7; // 参数
  string env = 8; //环境变量
}
message UpdatePodResp {
}

message WatchPodReq {
  string name_space = 1;
  string pod_name = 2;
}
message WatchPodResp {
}

message ScalePodReq {
  string name_space = 1;
  string pod_name = 2;
  int64 replicas = 3;
}
message ScalePodResp {
  int64 old_replicas = 1; // 原始副本数
}

message ServiceItem {
  int32 port = 1; // 端口
  int32 node_port = 2; // 节点端口
}
message GetServiceReq {
  string name_space = 1;
  string service_name = 2;
}
message GetServiceResp {
  repeated ServiceItem data = 1;
}