syntax = "proto3";
package mq;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/mq";

enum DetectStatus {
  DETECT_STATUS_UNKNOWN = 0;
  DETECT_STATUS_SUCC = 1; // 检测成功
  DETECT_STATUS_FAIL = 2; // 检测失败
}

// DetectRes 整体检测结果
enum DetectRes {
  DETECT_RESULT_UNKNOWN = 0;
  DETECT_RESULT_WHITE = 1; // 结果为白
  DETECT_RESULT_BLACK = 2; // 结果为黑
}

enum DetectEngine {
  UNKNOWN_DETECT = 0; // 未知
  AI_DETECT = 1; // AI检测
  AIPE_DETECT = 2; // AIPE检测
  CLAIMAV_DETECT = 3; // ClaimAV检测
  SANDBOX_DETECT = 4; // 沙箱检测
  CLOUD_SANDBOX_DETECT = 5; // 云沙箱检测
}

// 引擎样本检测请求
message SampleDetectFile {
  string sha256 = 1; // 文件sha256
  string md5 = 2; // 文件md5
  string type = 3; // 文件类型，例如：PY、PE、DOC、BAT等
  string bucket = 4; // 文件存储桶
  string path = 5; // 文件路径
  int32 size = 6; // 文件大小
  int32 priority = 7; // 检测优先级，0-100，默认0
  repeated DetectEngine engines = 8; // 指定检测引擎
  int32 retry = 9; // 检测失败重试次数（主要是调用每个检测引擎接口失败的情况，默认3次，可选）
  int32 timeout = 10; // 检测超时时间（单位：秒，调用每个检测引擎接口超时数值，默认3秒，可选）
  
  // 是否启用并行检测
  //（1）串行检测：会根据 engines 顺序从前向后（数组索引0>1>2>...）依次进行检测，过程中检测到病毒则流程终止。
  //（2）并行检测：会根据 engines 并发进行检测，各个引擎没有依赖关系。
  bool parallel_detect = 11;
}

// 引擎样本检测结果
message SampleDetectResult {
  string md5 = 1; // 文件md5
  string sha256 = 2; // 文件sha256
  string bucket = 3; // 文件存储桶
  string path = 4; // 文件路径
  DetectStatus status = 5 ; // 成功或失败
  DetectRes result = 6; // 逐个引擎检测后，最终结果为黑或白

  oneof detect_res {
    ClamAvResult clam_av = 7; // clamAv检测结果
    AIResult ai = 8; // ai引擎检测结果
    AIPeResult ai_pe = 9; // ai_pe检测结果
    SandboxResult sandbox = 10; // 沙箱检测结果
    WFYResult wfy = 11; // 网防云检测结果
  }
}

message ClamAvResult {
  string virus_name = 1; // 病毒名（空表示无病毒）
  string version = 2; // 引擎版本
}

message SandboxResult {
  int32 score = 1; // 沙箱检测分数，风险由低到高0-100
  repeated SandboxRiskType risk_types = 2;// 风险类型列表
  int32 task_id = 3; // 沙箱检测任务Id
}

message AIResult {
  int32 score = 1; // ai检测分值，风险由低到高0-100
}

message AIPeResult {
  int32 score = 1; // ai检测分值，风险由低到高0-100
}

message WFYResult {
  int32 confidence = 1; // 置信度（low: 1，mid: 2，high: 3）
  int32 risk_level = 2; // 风险级别（low: 1，mid: 2，high: 3）
  repeated int32 threat_types = 3; // 风险类型
  repeated string malware_families = 4; // 病毒家族名称列表
  repeated string apt_info = 5; // apt信息
}

enum SandboxRiskType {
  RISK_TYPE_NULL = 0;           // 未检测到风险
  RISK_TYPE_FISHING = 1;        // 钓鱼
  RISK_TYPE_WORM = 2;           // 蠕虫
  RISK_TYPE_RANSOM = 3;         // 勒索
  RISK_TYPE_VIRUS = 4;          // 病毒
  RISK_TYPE_BACK_DOOR = 5;      // 后门
  RISK_TYPE_BOTNET = 6;         // 僵尸网络
  RISK_TYPE_SPY = 7;            // 间谍软件
  RISK_TYPE_TROJAN = 8;         // 木马
  RISK_TYPE_EXPLOIT = 9;        // 漏洞利用
  RISK_TYPE_OTHER = 10;         // 其他
  RISK_TYPE_THEFT = 11;         // 数据窃取
  RISK_TYPE_MINING = 12;        // 挖矿
  RISK_TYPE_REMOTE = 13;        // 远控
  RISK_TYPE_WEB_SHELL = 14;     // webshell
  RISK_TYPE_RISK_SOFTWARE = 15; // 风险软件
  RISK_TYPE_SUSPECTED_APT = 16; // 疑似apt
  RISK_TYPE_CS = 17;            // CS木马
  RISK_TYPE_OUTLINE = 18;       // 外联
}
