syntax = "proto3";
package mq;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/mq";

import "google/protobuf/timestamp.proto";
import "agent/command.proto";

// Agent上报的数据
// 1、通信服务除了原始数据之外进行简单的封装，然后将按照消息规划方案分发到MQ。
// 2、非业务数据通过nats头部透传，例如：tenant_id、trace_id等。
// 3、消息规划 http://wiki.in.anxinsec.com/pages/viewpage.action?pageId=75763829
message AgentPacket {
  agent.Command cmd_id = 1; // 消息类型
  google.protobuf.Timestamp ts = 2; // 通信服务接收到agent上报数据的时间
  string machine_id = 3; // 设备信息
  bytes data = 4; // 消息内容
  int32 os_type = 5; // 操作系统类型
}
