syntax = "proto3";
package mq;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/mq";

import "google/protobuf/timestamp.proto";

// 文件分类
enum FileCategory {
  FC_UNKNOWN  = 0;
  BLACK       = 1;
  WHITE       = 2;
  GRAY        = 3;
  QUASI_WHITE = 4;
}

// 外联告警线索
message OutreachClueInfo {
  string machine_id                       = 1;   // 主机唯一标识
  string unique_flag                      = 2;   // 线索唯一标识
  int32 level                             = 3;   // 风险等级 1.低风险 2.中风险 3.高风险
  int32 risk_type                         = 4;   // 威胁类型
  int32 source                            = 5;   // 线索来源  1.命中共有情报 2.命中私有情报
  int32 outreach_type                     = 6;   // 外联类型 1.ip 2.域名
  string local_ip                         = 7;   // 内网IP，可能没有
  int32 local_port                        = 8;   // 内网端口，可能没有
  string remote_ip                        = 9;   // 外联IP
  int32 remote_port                       = 10;  // 外联端口
  string domain                           = 11;  // 外联域名
  string ip_ver                           = 12;  // ipv4 or ipv6
  string protocol                         = 13;  // 协议 udp or tcp
  string tag                              = 14;  // 标签
  int32 apt                               = 15;  // 是否命中APT 1.有 2.没有
  string malicious_code                   = 16;  // 恶意代码家族
  int32 confidence                        = 17;  // 置信度
  string direction                        = 18;  // 定向攻击类型
  int32 pid                               = 19;  // PID
  string proc_name                        = 20;  // 进程名
  string proc_path                        = 21;  // 进程路径
  int64 proc_start_time                   = 22;  // 进程启动时间
  string proc_md5                         = 23;  // 进程md5
  string proc_command                     = 24;  // 进程命令行信息
  string proc_user_permission             = 25;  // 用户权限
  string proc_user                        = 26;  // 进程所属用户
  string signature_info                   = 27;  // 进程签名信息JSON后的字符串
  string root_proc_info                   = 28;  // 根进程信息，JSON格式数据
  string current_process_info             = 29;  // 进程信息，JSON格式数据
  string parent_process_info              = 30;  // 父进程信息，JSON格式数据
  string apt_str                          = 31;  // APT组织(记录V01私有库数据)
  string black_market                     = 32;  // 黑灰产组织(记录V01私有库数据)
  string virus                            = 33;  // 病毒家族(记录V01私有库数据)
  string unique_flag_md5                  = 34;  // unique_flag md5
  string clue_key                         = 35;  // 主机线索唯一值
  int64 root_pid                          = 36;  // 根进程ID
  int64 root_start_time                   = 37;  // 根程启动时间
  int64 pid_start_time                    = 38;  // 当前进程启动时间
  string host_name                        = 39;  // 主机名
  int64 euid                              = 40;  // 进程的euid
  string group_name                       = 41;  // 主机分组名
  string os                               = 42;  // 操作系统名称
  int32 os_type                           = 43;  // 系统类型 1 windows, 2 linux
  string host_ip                          = 44;  // Agent管理主机IP
  string client_version                   = 45;  // 终端版本信息
  google.protobuf.Timestamp discover_time = 46;  // 客户端发现时间
  google.protobuf.Timestamp create_time   = 47;  // 创建时间
}

// 文件告警线索
message FileClueInfo {
  string machine_id                       = 1;   // 主机唯一标识
  string unique_flag                      = 2;   // 线索唯一标识
  int32 level                             = 3;   // 风险等级 1.低风险 2.中风险 3.高风险
  int32 risk_type                         = 4;   // 威胁类型
  string file_path                        = 5;   // 文件路径
  string file_name                        = 6;   // 文件名
  FileCategory file_category              = 7;   // 文件分类
  repeated ClueDetectEngine check_engines = 8;   // 检测引擎
  repeated string tag                     = 9;   // 标签
  int32 apt                               = 10;  // 是否有apt情报 1.没有 2.有
  string md5                              = 11;  // 文件md5
  string sha256                           = 12;  // 文件sha256
  string sha1                             = 13;  // 文件sha1
  int64 access_at                         = 14;  // 访问文件时间
  int64 modify_at                         = 15;  // 文件修改时间
  int64 create_at                         = 16;  // 文件创建时间
  string st_mode                          = 17;  // 文件权限，ge: rwxr-xr--*
  int64 file_size                         = 18;  // 文件大小，单位字节
  string file_permission                  = 19;  // 文件权限
  string file_user                        = 20;  // 文件用户
  string signature_info                   = 21;  // 文件签名信息
  int32 is_sandbox                        = 22;  // 是否经过了沙箱检测 1.没有 2.有
  string file_version                     = 23;  // 文件版本
  string file_vendor                      = 24;  // 文件厂商
  int32 confidence                        = 25;  // 置信度
  int32 file_thread_source                = 26;  // 文件线索来源 1.agent 2.server
  repeated int32 malicious_types          = 27;  // 可疑类型
  repeated string malicious_dde           = 28;  // 可疑dde代码
  repeated string malicious_vba           = 29;  // 可疑vba代码
  string malicious_lnk_target_path        = 30;  // 可疑lnk目标路径
  string malicious_lnk_working_dir        = 31;  // 可疑lnk文件工作路径
  string malicious_lnk_cmd_line           = 32;  // 可疑lnk文件命令行参数
  string malicious_lnk_icon_path          = 33;  // 可疑lnk图标路径
  string malicious_url                    = 34;  // 可疑图标url
  string sl_rule_name                     = 35;  // 扫雷规则名
  string sl_rule_detail                   = 36;  // 扫雷规则详情
  google.protobuf.Timestamp discover_time = 37;  // 客户端发现时间
}

enum ClueDetectEngine {
  ENGINE_UNKNOWN       = 0;  // 未知
  AGENT_STATIC         = 1;  // 客户端静态检测引擎
  AGENT_MACHINE_LEARN  = 2;  // 客户端机器学习
  SERVER_STATIC        = 3;  // 服务端静态检测引擎，情报服务-内部情报库
  SERVER_MACHINE_LEARN = 4;  // 服务端机器学习，claimAv
  SERVER_PE            = 5;  // 可执行文件检测引擎，ai-pe
  SERVER_DOCUMENT      = 6;  // 文档检测引擎，ai
  SERVER_SANDBOX       = 7;  // 沙箱检测引擎，本地沙箱
  CLOUD_STATIC         = 8;  // 云端静态检测引擎，情报服务-网防云情报库
  CLOUD_SANDBOX        = 9;  // 云沙箱检测引擎，网防云沙箱
}