syntax = "proto3";
package sion;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/sion";

// ClaimAV文件检测服务
service Sion {

  // 文件检测
  rpc FileDetect(stream FileDetectReq) returns (stream FileDetectResp) {}
}

message FileDetectReq {
  string filename = 1; // 仅首个包携带
  string sha256 = 2; // 文件sha256
  bytes chunk_data = 3; // 数据块
  bool eof = 4; // 是否结束标志
}
message FileDetectResp {
  string filename = 1; // 文件名
  string virus_name = 2; // 病毒名称
  string version = 3; // 引擎版本
}