syntax = "proto3";

package braum;

option go_package = "vapi/proto/braum";

// PageInput 分页输入参数
message PageInput {
  int32 page = 1;
  int32 page_size = 2;
}

// PageOutput 分页输出参数
message PageOutput {
  int32 page = 1;
  int32 page_size = 2;
  int64 total = 3;
}

// Response 通用响应
message Response {
  int32 code = 1;
  string message = 2;
  bytes data = 3;
}

// EntityFilter 实体过滤条件
message EntityFilter {
  int32 os_type = 1;       // 0: 忽略，1: linux, 2: windows
  int32 online = 2;        // 0: 忽略，1: 在线，2：离线
  int32 secure_status = 3; // 0: 忽略，1: 受攻击, 2：存在风险，3：受攻击&&存在风险, 4: 健康
  repeated int64 group_ids = 4; // 组ID集合 (全部: 空，未分组; 0)
  int64 offline_begin = 5; // 起始离线时间
  int64 offline_end = 6;   // 截止离线时间
  repeated string tags = 7; // 查询指定的标签
  string search_data = 8;  // 模糊搜索(主机IP 或 名称)
  string tid = 9;         // 终端ID (主机ID或IP)
  bool unscoped = 10;      // 用 Unscoped
  bool or_uninstalled = 11; // 在线状态含已卸载
}

// VersionInfo 版本信息
message VersionInfo {
  int32 kind = 1;    // 版本类别 1: agent, 2: 驱动, 3: 行为库
  string version = 2; // 版本号
}

// EntityExtInfo 实体扩展信息
message EntityExtInfo {
  string asset_level = 1;        // 资产等级
  string responsible_person = 2; // 负责人
  string department = 3;         // 部门
  string location = 4;           // 位置
  string asset_number = 5;       // 资产编号
  string note = 6;               // 备注
}

// ParseFeatureLibPKGInput 解析特征库包输入
message ParseFeatureLibPKGInput {
  string bucket_name = 1;
  string object_name = 2;
  int32 kind = 3;
}

// ParseFeatureLibPKGResponse 解析特征库包响应
message ParseFeatureLibPKGResponse {
  int64 package_id = 1;
  string version = 2;
  int64 size = 3;
  string md5 = 4;
  string description = 5;
}

// RevokeLibPackageRequest 撤回库包请求
message RevokeLibPackageRequest {
  int64 package_id = 1;
}

// LatestLibResponse 最新库响应
message LatestLibResponse {
  string version = 1;
  string url = 2;
  string md5 = 3;
}

// LibUploadRecord 库包上传记录
message LibUploadRecord {
  int64 id = 1;
  string version = 2;
  int64 size = 3;
  string md5 = 4;
  string description = 5;
  int32 status = 6;
  int64 created_at = 7;
  string created_by = 8;
}

// QueryLibUploadRecordResponse 查询库包上传记录响应
message QueryLibUploadRecordResponse {
  PageOutput page = 1;
  repeated LibUploadRecord items = 2;
}

message Group {
  int64 id = 1;
  int64 parent_id = 2;
  string name = 3;
  repeated PathKV group_kv_path = 4;
  int32 sequence = 5;
}

message PathKV {
  int64 id = 1;
  string name = 2;
}