syntax = "proto3";

package braum;

import "agent/license_enable.proto";
import "braum/common.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "mq/mq.proto";

option go_package = "vapi/proto/braum";

// OnOffInfo 主机上下线信息
message OnOffInfo {
  string machine_id = 1;
  uint32 group_id = 2;
  string ip = 3;
  int32 online = 4;
  int32 os_type = 5;
}

// EntityInfo 主机基本信息
message EntityInfo {
  int64 id = 1;
  string machine_id = 2;
  string machine_name = 3;
  int32 os_type = 4;
  string ip = 5;
  string os = 6;
  string kernel_version = 7;
  int32 online = 8;
  int64 group_id = 9;
  string tag = 10;
  int64 last_online_at = 11;
  int64 last_offline_at = 12;
  int64 created_at = 13;
  int64 updated_at = 14;
  int64 deleted_at = 15;
  string group_name = 16;
  string arch = 17;
  string os_info_short = 18;
  string mac_address = 19;
}

// EntityList 主机列表
message EntityList {
  PageOutput page = 1;
  repeated EntityInfo items = 2;
}

// EntitybaseInput 用于写入
message EntitybaseInput {
  string machine_id = 1;
  string ip = 2;
  int32 os_type = 3;
  string tag = 4;
  int64 group_id = 5;
  string machine_name = 6;
  string os = 7;
  string kernel_info = 8;
  string os_info_long = 9;
  string os_info_short = 10;
  string proxy_ip_port = 11;
  repeated VersionInfo version_slice = 12;
  string arch = 13;
  string os_info_display = 14;
  string mac = 15;
  string gateway = 16;
  string latest_user = 17;
  string curr_user = 18;
  string domain_user = 19;
  repeated bool curr_flag = 20;
}

// EntityStatusStatistic 资产统计信息
message EntityStatusStatistic {
  int32 total = 1;
  int32 healthy_count = 2;
  int32 risk_count = 3;
  int32 attacked_count = 4;
  int32 online_count = 5;
  int32 offline_count = 6;
  int32 uninstalled_count = 7;
  int32 windows_count = 8;
  int32 linux_count = 9;
}

// AddTagInput 添加标签输入
message AddTagInput {
  string machine_id = 1;
  string tag = 2;
}

// EntityMoveToGroupInput 按条件移动主机到指定分组
message EntityMoveToGroupInput {
  int64 group_id = 1;
  EntityFilter filter = 2;
  repeated string exclude = 3;
}

// MultiEntityMoveToGroupInput 将多个主机移动到指定分组
message MultiEntityMoveToGroupInput {
  int64 group_id = 1;
  repeated string machine_ids = 2;
}

// QueryMachineIDFilter 查询主机ID过滤条件
message QueryMachineIDFilter {
  int64 group_id = 1;
  int32 os_type = 2;
}

// QueryMachineIDInput 查询主机ID输入
message QueryMachineIDInput {
  QueryMachineIDFilter filter = 1;
  PageInput page = 2;
}

// MachineIDList 主机ID列表
message MachineIDList {
  PageOutput page = 1;
  repeated string machine_ids = 2;
}

// RiskStatusInput 用于写入攻击、风险状态
message RiskStatusInput {
  string machine_id = 1;
  int32 attack = 2;
  int32 risk = 3;
}

// EntityVersionRequest 主机版本请求
message EntityVersionRequest {
  string machine_id = 1;
  int32 ver_kind = 2;
}

// EntityVersion 主机版本信息
message EntityVersion {
  string machine_id = 1;
  int32 ver_kind = 2;
  int32 ver_type = 3;
  string version = 4;
}

// EntityDetailInput 主机详情输入
message EntityDetailInput {
  bool include_group_name = 1;
  PageInput page = 2;
}

// EntityDetail 主机详情
message EntityDetailResponse {
  EntityInfo entity_info = 1;
  repeated EntityVersion versions = 2;
}

// EntityDetailList 主机详情列表
message EntityDetailList {
  PageOutput page = 1;
  repeated EntityDetailResponse items = 2;
}

// EntityImportInput 主机导入输入
message EntityImportInput {
  int32 import_mode = 1;
  string bucket_name = 2;
  string object_name = 3;
}

// EntityImportResult 主机导入结果
message EntityImportResult {
  int32 status = 1;
  int32 success = 2;
  int32 failure = 3;
  int64 log_id = 4;
}

// EntityExtInfoInput 主机扩展信息输入
message EntityExtInfoInput {
  string machine_id = 1;
  EntityExtInfo ext_info = 2;
}

// EntityImportLog 主机导入日志
message EntityImportLog {
  int64 id = 1;
  int32 status = 2;
  int32 success = 3;
  int32 failure = 4;
  int64 admin_id = 5;
  int64 created_at = 6;
}

// EntityImportLogList 主机导入日志列表
message EntityImportLogList {
  PageOutput page = 1;
  repeated EntityImportLog items = 2;
}

// AOSObjectInfo AOS对象信息
message AOSObjectInfo {
  string bucket_name = 1;
  string object_name = 2;
  string url = 3;
}

// SimpleEntityInfo 简单主机信息
message SimpleEntityInfo {
  string mac = 1;
  string os = 2;
  int32 os_type = 3;
  string ip = 4;
  string machine_name = 5;
  string arch = 6;
  string gateway = 7;
  string curr_user = 8;
  string latest_user = 9;
  string domain_user = 10;
  string host_id = 11;
  string agent_version = 12;
  int64 first_install_at = 13;
  int64 agent_upgrade_at = 14;
  int64 latest_online_at = 15;
  int64 latest_offline_at = 16;
  string user_name = 17;
}

// UpdateEntityVulCountInput
message UpdateEntityVulCountInput {
  string mac = 1;
  int64 kernel_vul_count = 2;
  int64 app_vul_count = 3;
  int64 web_vul_count = 4;
}

// EntityComMessage
message EntityComMessage {
  string machine_id = 1;
  string type = 2;
  string method = 3;
  bytes data = 4;
}

// ModifyMachineUserNameReq
message ModifyMachineUserNameReq {
  string mac = 1;
  string user_name = 2;
}

// RemoveEntityInput
message RemoveEntityInput {
  EntityFilter filter = 1;
  repeated string exclude = 2;
}

// EntityCountResponse
message EntityCountResponse {
  int64 count = 1;
}

// CommonResponse 通用响应消息
message CommonResponse {
  bool success = 1;
  string error_message = 2;
  int32 error_code = 3;
}

// EntityService 主机服务
service EntityService {
  // ResetOnlineStatus 重置在线状态
  rpc ResetOnlineStatus(google.protobuf.Empty) returns (google.protobuf.Empty);

  // EntityOnOffLine 主机上下线
  rpc EntityOnOffLine(OnOffInfo) returns (google.protobuf.Empty);

  // UpsertEntityInfo 主机信息写入接口
  rpc UpsertEntityInfo(UpsertEntityInfoRequest) returns (google.protobuf.Empty);

  // GetUnscopeEntityInfo 获取单一主机信息(包含删除的主机)
  rpc GetUnscopeEntityInfo(EntityInfoRequest) returns (EntityInfoResponse);

  // EntityInfo 获取单一主机信息
  rpc EntityInfo(EntityInfoRequest) returns (EntityInfoResponse);

  // GetVersion 获取主机相应信息
  rpc GetVersion(EntityVersionRequest) returns (EntityVersion);

  // EntityDetail 获取单一主机详细信息(不含asset部分)
  rpc EntityDetail(EntityInfoRequest) returns (EntityDetailResponse);

  // QueryEntityDetailList 获取主机详细信息(不含asset部分)
  rpc QueryEntityDetailList(EntityDetailInput) returns (EntityDetailList);

  // BatchQueryEntityInfo 批量查询主机信息列表
  rpc BatchQueryEntityInfo(BatchQueryEntityInfoRequest) returns (EntityList);

  // BatchQueryEntityInfoByIP 批量通过IP查询主机信息列表
  rpc BatchQueryEntityInfoByIP(BatchQueryEntityInfoByIPRequest) returns (EntityList);

  // BatchQueryEntityInfoByMachineName 批量通过主机名查询主机信息列表
  rpc BatchQueryEntityInfoByMachineName(BatchQueryEntityInfoByMachineNameRequest) returns (EntityList);

  // QueryEntityInfoList 获取主机信息列表
  rpc QueryEntityInfoList(EntityInput) returns (EntityList);

  // EntityCount 主机总数
  rpc EntityCount(google.protobuf.Empty) returns (EntityCountResponse);

  // OfflineEntityCount 获取离线主机数
  rpc OfflineEntityCount(EntityFilter) returns (EntityCountResponse);

  // RemoveMultiEntity 删除多个主机
  rpc RemoveMultiEntity(RemoveMultiEntityRequest) returns (google.protobuf.Empty);

  // RemoveEntityByFilter 按条件删除主机
  rpc RemoveEntityByFilter(RemoveEntityInput) returns (google.protobuf.Empty);

  // MultiEntityMoveToGroup 将多个主机移动到指定的分组
  rpc MultiEntityMoveToGroup(MultiEntityMoveToGroupInput) returns (google.protobuf.Empty);

  // EntityMoveToGroupByFilter 按条件将主机移动到指定的分组
  rpc EntityMoveToGroupByFilter(EntityMoveToGroupInput) returns (google.protobuf.Empty);

  // AddTag 添加Tag
  rpc AddTag(AddTagInput) returns (google.protobuf.Empty);

  // QueryEntityMacList 根据IP/Hostname返回最多1000个元素的macList
  rpc QueryEntityMacList(QueryEntityMacListRequest) returns (MachineIDList);

  // QueryEntityMachineIDList 根据groupID和系统类型（win/lin/全部），分页获取machineID列
  rpc QueryEntityMachineIDList(QueryMachineIDInput) returns (MachineIDList);

  // QueryVersionList 查询版本列表
  rpc QueryVersionList(QueryVersionListRequest) returns (QueryVersionListResponse);

  // QueryEntityVersionList 获取主机版本信息(agent、driver...)
  rpc QueryEntityVersionList(EntityInfoRequest) returns (QueryEntityVersionListResponse);

  // EntityImportTemplateURL 获取主机导入模板URL
  rpc EntityImportTemplateURL(EntityImportTemplateURLRequest) returns (AOSObjectInfo);

  // BatchImportEntityGroupInfo 批量导入主机分组信息
  rpc BatchImportEntityGroupInfo(EntityImportInput) returns (EntityImportResult);

  // ModifyEntityExtInfo 修改主机扩展信息(资产编号、资产等级...)
  rpc ModifyEntityExtInfo(EntityExtInfoInput) returns (google.protobuf.Empty);

  // QueryEntityImportLogList 查询entity导入日志信息
  rpc QueryEntityImportLogList(PageInput) returns (EntityImportLogList);

  // GetEntityLogURL 获取主机导入日志文件URL
  rpc GetEntityLogURL(GetEntityLogURLRequest) returns (AOSObjectInfo);

  // RestoreDBNotify 数据库还原通知
  rpc RestoreDBNotify(google.protobuf.Empty) returns (google.protobuf.Empty);

  // ReportNoAdaptedAgent 接收上报未适配的主机信息
  rpc ReportNoAdaptedAgent(NoAdaptedAgentInfo) returns (google.protobuf.Empty);

  // GetSimpleEntityInfo 获取简单主机信息
  rpc GetSimpleEntityInfo(EntityInfoRequest) returns (SimpleEntityInfo);

  // ModifyMachineUserName 修改主机用户名
  rpc ModifyMachineUserName(ModifyMachineUserNameReq) returns (google.protobuf.Empty);

  // GetGroup 获取主机组信息
  rpc GetGroup(GetGroupRequest) returns (Group);

  rpc ApplyAccessPermission(mq.AgentPacket) returns (agent.LicenseEnableResponse);
}

// 以下是请求和响应消息类型

// UpsertEntityInfoRequest 主机信息写入请求
message UpsertEntityInfoRequest {
  repeated EntitybaseInput entities = 1;
}

// EntityInfoRequest 主机信息请求
message EntityInfoRequest {
  string machine_id = 1;
}

// EntityInfoResponse 主机信息响应
message EntityInfoResponse {
  EntityInfo entity_info = 1;
}

// BatchQueryEntityInfoRequest 批量查询主机信息请求
message BatchQueryEntityInfoRequest {
  repeated string machine_ids = 1;
}

// BatchQueryEntityInfoByIPRequest 批量通过IP查询主机信息请求
message BatchQueryEntityInfoByIPRequest {
  repeated string ips = 1;
}

// BatchQueryEntityInfoByMachineNameRequest 批量通过主机名查询主机信息请求
message BatchQueryEntityInfoByMachineNameRequest {
  repeated string machine_names = 1;
}

// EntityInput 主机输入
message EntityInput {
  EntityFilter filter = 1;
  string order_by = 2;
  PageInput page = 3;
}

// RemoveMultiEntityRequest 删除多个主机请求
message RemoveMultiEntityRequest {
  repeated string machine_ids = 1;
}

// QueryEntityMacListRequest 查询主机Mac列表请求
message QueryEntityMacListRequest {
  string search_data = 1;
}

// QueryVersionListRequest 查询版本列表请求
message QueryVersionListRequest {
  int32 version_kind = 1;
}

// QueryVersionListResponse 查询版本列表响应
message QueryVersionListResponse {
  repeated string versions = 1;
}

// QueryEntityVersionListResponse 查询主机版本列表响应
message QueryEntityVersionListResponse {
  repeated EntityVersion versions = 1;
}

// EntityImportTemplateURLRequest 主机导入模板URL请求
message EntityImportTemplateURLRequest {
  string filename = 1;
}

// GetEntityLogURLRequest 获取主机日志URL请求
message GetEntityLogURLRequest {
  int64 id = 1;
}

// NoAdaptedAgentInfo 未适配的主机信息
message NoAdaptedAgentInfo {
  string machine_id = 1;
  string machine_name = 2;
  string os_info = 3;
  string kernel_info = 4;
}

message GetGroupRequest {
  int64 id = 1;
}

// Braum 服务
service Braum {
  // LogCollectionTaskCreate 创建日志收集任务
  rpc LogCollectionTaskCreate(LogCollectionCreateReq) returns (google.protobuf.Empty);
  // LogCollectionFileUploaded 通知文件上传结果
  rpc LogCollectionFileUploaded(LogCollectionUploadedReq) returns (google.protobuf.Empty);
  // LogCollectionTaskList 查询日志收集任务列表
  rpc LogCollectionTaskList(LogCollectionListReq) returns (LogCollectionListResp);
}

// LogCollection 日志收集:
//
//   - 系统 dump 日志
//   - Agent dump 日志
//   - 系统事件日志
//   - Agent 运行日志

// LogCollectionType 同 agent.CollectLogsType
message LogCollectionType {
  enum Enum {
    Unknown = 0;
    SysDump = 1; // 系统 dump 日志
    AgentDump = 2; // Agent dump 日志
    SysEvent = 3; // 系统事件日志
    AgentRunning = 4; // 系统运行日志
  }
}

message LogCollectionStatus {
  enum Enum {
    Unknown = 0;
    Collecting = 1; // 收集中
    Collected = 2; // 收集成功
    Failed = 3; // 收集失败
  }
}

message LogCollectionFailure {
  enum Enum {
    Unknown = 0;
    Timeout = 1; // 任务超时
    FileNotExist = 2; // 文件不存在
    UploadFailed = 3; // 上传失败
    HostOffline = 4; // 主机离线
  }
}

message LogCollectionTask {
  int64 task_id = 1; // 收集任务 ID
  string host_id = 2; // 主机 ID
  int64 user_id = 3; // 用户 ID
  LogCollectionType.Enum type = 4; // 日志类型
  LogCollectionStatus.Enum status = 5; // 日志收集状态
  LogCollectionFailure.Enum failure = 6; // 日志收集失败原因
  string filename = 7; // 原始文件名
  string object_name = 8; // 存储对象名
  int64 object_size = 9; // 存储对象大小
  google.protobuf.Timestamp updated_at = 10; // 更新时间
  google.protobuf.Timestamp created_at = 11; // 创建时间
}

message LogCollectionCreateReq {
  LogCollectionTask task = 1; // 任务详细内容
  string upload_url = 2; // Agent 上传日志 URL
}

message LogCollectionUploadedReq {
  int64 task_id = 1;
  bool succeeded = 2;
  string filename = 3; // 原始文件名
  string object_name = 4; // 存储对象名
  int64 object_size = 5; // 存储对象大小
}

message LogCollectionListFilter {
  repeated int64 task_ids = 1;
  string search_data = 2; // 模糊搜索主机名/IP
  repeated string host_ids = 3; // 过滤主机 ID
  repeated LogCollectionType.Enum types = 4; // 过滤日志类型
  repeated LogCollectionStatus.Enum status = 5; // 过滤日志收集状态
  repeated google.protobuf.Timestamp date_range = 6; // 任务创建时间范围
}

message LogCollectionListReq {
  LogCollectionListFilter filter = 1;
  PageInput page = 2;
}

message LogCollectionListResp {
  repeated LogCollectionTask items = 1;
  PageOutput page = 2;
}
