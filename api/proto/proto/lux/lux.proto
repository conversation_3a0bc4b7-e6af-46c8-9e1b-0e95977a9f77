syntax = "proto3";
package lux;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/lux";

import "agent/command.proto";

// Agent通信服务
service Lux {

  // 推送
  rpc Push(PushReq) returns (PushResp) {}
  // 探测
  rpc Probe(ProbeReq) returns (ProbeResp) {}
  // 监控
  rpc Monitor(MonitorReq) returns (MonitorResp) {}
  // 调整日志级别
  rpc SetLogLevel (LogLevelReq) returns (LogLevelResp) {}
}

message PushReq {
  string machine_id = 1; // 设备信息
  agent.Command cmd_id = 2; // 命令字
  bytes data = 3; // 数据
}
message PushResp {
}

message ProbeReq {
  string machine_id = 1; // 设备信息
}
message ProbeResp {
  bool existed = 1; // 连接是否已存在
}

message MonitorReq {
}
message MonitorResp {
  int32 conn_count = 1; // 当前连接数
}

message LogLevelReq {
  string log_level = 1; // 日志级别 1:debug 2:info 3:warn 4:error
  int32 continue_ts = 2; // 修改日志级别之后持续该日志级别的时间（秒），默认 600秒
}
message LogLevelResp {
}

enum Status {
  STATUS_UNKNOWN = 0; // 状态未知
  STATUS_SUCCESS = 1; // 成功
  STATUS_FAILED = 2; // 失败（通用错误）
  STATUS_REJECTION = 3; // 拒绝（限流、熔断）
  STATUS_UNSUPPORTED = 4; // 不支持
}