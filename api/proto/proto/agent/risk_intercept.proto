syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
// 
//  文件风险 识别结果
//  对应 g_CmdMemProtectvirusFileInfocd 
//--------------------------------------------------
message MemProtectAutoInterceptInfo {
        ClientID baseInfo = 1;
        repeated AutoInterceptInfo autoInterceptList = 2;
}

message AutoInterceptInfo {
        RiskHeader  Header       = 1;
        string      detail_info  = 2; // 详细信息，json序列化后的字符串
        int64       policy_id    = 3; // 命中的策略ID
}
