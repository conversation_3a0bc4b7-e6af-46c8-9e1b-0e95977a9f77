syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

message ServerPushMessage {
  BaseDownloadInfo baseDownloadInfo = 1;  // agent 文件下载基础信息
  oneof has_driverUpgradeMessage {DriverUpgradeMessage driverUpgradeMessage = 2;};
  oneof has_hashenginefile {HashEngineFile hash_engine_file = 3;};
  oneof has_sha256enginefile {HashEngineFile  sha256_engine_file = 4;};
  oneof has_ngavfile {NgavFile  ngav_file = 5;};
  oneof has_raspfile {RaspFile rasp_file = 6;};
  oneof has_baselinefile {BaselineFile baseline_file = 7;};
  oneof has_domainwhitefile {AgentEngineLibVersion domainwhite_file = 8;};
  oneof has_filesigncomwhitefile {AgentEngineLibVersion filesigncomwhite_file = 9;};
  oneof has_driverblacklistfile {AgentEngineLibVersion driverblacklist_file = 10;};
  oneof has_acdrlibfile {AgentEngineLibVersion acdrlib_file = 11;};
  oneof has_attackwhitefile {AgentEngineLibVersion attackwhitelib_file = 12;};
  oneof has_signblacklibfile {AgentEngineLibVersion signblacklib_file = 13;}
  oneof has_sllibfile {AgentEngineLibVersion sllib_file = 14;};
}

message BaseDownloadInfo {
  bytes pkgUrl = 1; // 下载地址
  AgentDownloadWay way = 2; // 下载方式
  FileInfo fileInfo = 3; // 当way = ADW_SOCKET 有效
}

message RequestFile {
  bytes origName = 1; // 文件名
  int32 fileBlockSize = 2; // 每块的大小
  int32 fileNum = 3; // 从0开始，序号
  bytes relativePath = 4; // 服务端真实的路径（加密后）
}

message ResponseFile {
  bytes origName = 1;
  int32 fileBlockSize = 2;
  int32 fileNum = 3;
  bytes fileContent = 4; // 文件块的内容
}

message UploadFile {
  string modname = 1;
  int32 totalBlock = 2;
  int32 fileNum = 3;
  bytes fileContent = 4; // 文件块的内容
}

enum AgentDownloadWay {
  ADW_UNKNOWN = 0;
  ADW_HTTP = 1;
  ADW_SOCKET = 2;
}

message FileInfo {
  bytes origName = 1;
  int32 fileSize = 2;
  bytes fileMd5 = 3;
  bytes relativePath = 4; // 服务端绝对路径(加密后）,只有服务端会用
}

// 服务器推送升级消息
message AgentUpgradeMsg {
  bytes versionName = 1; // 版本名称
  bytes versionCode = 2; // 版本代码，检查新版本
  bytes archInfo = 3; // 系统支持信息（如x86_linux64）
  bytes pkgMd5 = 4; // 安装包MD5，下载完成后验证
  bytes pkgUrl = 5; // 下载地址
  bytes flag = 6; // flag升级紧急程度（先保留）
  AgentDownloadWay way = 7; // 下载方式
  FileInfo fileInfo = 8; // 当way = ADW_SOCKET 有效
}

enum AgentUpgradeErrorCode {
  AUEC_OK = 0; // 成功
  AUEC_DOWNLOAD_FAILED = 1; // 下载失败
  AUEC_INTEGRITY_CHECK_FAILED = 2; // 完整性校验失败
  AUEC_UNZIP_FAILED = 3; // 解压缩失败
  AUEC_FILE_OPEN_FAILED = 4; // 下载目标文件文件打开失败
  AUEC_UPGRADING = 5; // 正在升级
  AUEC_VERSION_NOT_NEW = 6; // Agent版本更新，不需要更新
  AUEC_COPY_UPGRADE_FILE_FAILED = 7; // 拷贝升级文件到临时文件夹失败
  AUEC_OTHER_ERROR = 8; // 拷贝升级文件到临时文件夹失败，正常应该用不到
  AUEC_CHECK_SIGN_FAILED = 9; // 签名校验失败
  AUEC_SAME_VERSION = 10; // 版本相等
}

message AgentUpgradeResult {
  ClientID baseInfo = 1; // 主机信息标识
  bytes versionName = 2; // 版本名称
  bytes versionCode = 3; // 版本代码，检查新版本
  AgentUpgradeErrorCode errorCode = 4; // 错误代码
}

message DriverUpgradeMessage {
  uint32 version = 1; // 保留字段
}

message HashEngineFile {
  string version = 1;
}

message NgavFile {
  string version = 1;
}

message RaspFile {
  string version = 1;
}

message BaselineFile {
  string version = 1;
}

message LinuxAgentOSInfo {
  uint32 driver_version = 1;   // 本地的驱动版本
  string agent_version = 2;   // agent 版本
  string linuxOSInfoLong = 3;   // linux 系统信息长串，仅linux
  string linuxOSInfoShort = 4;   // linux 系统信息短串，仅linux
}

enum DriverUpgradeErrorCode {
  DUEC_OK = 0; // 成功
  DUEC_DOWNLOAD_FAILED = 1; // 下载失败
  DUEC_INTEGRITY_CHECK_FAILED = 2; // 完整性校验失败
  DUEC_UNZIP_FAILED = 3; // 解压缩失败
  DUEC_FILE_OPEN_FAILED = 4; // 下载目标文件文件打开失败
  DUEC_UPGRADING = 5; // 正在升级
  DUEC_CHECK_SIGN_FAILED = 6; // 签名校验失败
  DUEC_OTHER_ERROR = 7; // 正常应该用不到
  DUEC_RENAME_FAILED = 8; // 文件重命名失败
  DUEC_REMMOD_OLD_FAILED = 9; // 卸载旧的驱动失败
  DUEC_INSMOD_NEW_FAILED = 10;// 安装新的驱动失败
}

message DriverUpgradeResult {
  uint32 driver_version = 1; // 本地的驱动版本
  DriverUpgradeErrorCode errorCode = 2; // 错误代码
  string linuxOSInfoLong = 3; // linux 系统信息长串，仅linux
  string linuxOSInfoShort = 4; // linux 系统信息短串，仅linux
}

enum HashEngineFileUpgradeErrorCode {
  HEFUEC_OK = 0; // 成功
  HEFUEC_DOWNLOAD_FAILED = 1; // 下载失败
  HEFUEC_INTEGRITY_CHECK_FAILED = 2; // 完整性校验失败
  HEFUEC_UNZIP_FAILED = 3; // 解压缩失败
  HEFUEC_FILE_OPEN_FAILED = 4; // 下载目标文件文件打开失败
  HEFUEC_UPGRADING = 5; // 正在升级
  HEFUEC_CHECK_SIGN_FAILED = 6; // 签名校验失败
  HEFUEC_OTHER_ERROR = 7; // 正常应该用不到
  HEFUEC_VERSION_MISMATCH = 8; // 版本不匹配
}

message HashEngineFileUpgradeResult {
  string version = 1; // 本地hash库版本
  HashEngineFileUpgradeErrorCode error_code = 2; // 错误代码
}

message NgavFileUpgradeResult {
  string version = 1; // 本地ngav库版本
  HashEngineFileUpgradeErrorCode error_code = 2; // 错误代码
}

message RaspFileUpgradeResult {
  string version = 1; // rasp库版本
  HashEngineFileUpgradeErrorCode error_code = 2; // 错误代码
}

message BaselineFileUpgradeResult {
  string version = 1; // 基线规则库版本
  HashEngineFileUpgradeErrorCode error_code = 2; // 错误代码
}


// v01自2023年06月29日之后(白名单之后的引擎库)的客户端引擎库的升级请求和升级结果上报走以下统一结构
// ----- V01 start-----

// 统一引擎库升级请求结构
message AgentEngineLibUpgradeRequest
{
  oneof has_upgradeRequest {
    AgentEngineLibVersion domain_white_lib = 1; // 域名白库
    AgentEngineLibVersion file_sign_com_white_lib = 2; // 文件签名公司白名单库
    AgentEngineLibVersion file_driverblacklist_lib = 3; //驱动黑库
    AgentEngineLibVersion acdr_lib = 4; // acdr库
    AgentEngineLibVersion attack_white_lib = 5; // 内存、系统攻击白库
    AgentEngineLibVersion file_sign_black_lib = 6; // 文件签名黑库
    AgentEngineLibUpgradeItem file_sl_lib  = 7; // 统一引擎库
  };
}

message AgentEngineLibVersion {
  string version = 1;          // 引擎版本
}

// 统一引擎库升级结果上报结构
message AgentEngineLibUpgradeResult
{
  oneof has_upgradeResult {
    AgentEngineLibUpgradeItem domain_white_lib = 1; // 域名白库
    AgentEngineLibUpgradeItem file_sign_com_white_lib = 2; // 文件签名公司白名单库
    AgentEngineLibUpgradeItem file_driverblacklist_lib = 3; // 驱动黑库
    AgentEngineLibUpgradeItem acdr_lib = 4; // acdr库
    AgentEngineLibUpgradeItem attack_white_lib = 5; // 内存、系统攻击白库
    AgentEngineLibUpgradeItem file_sign_black_lib = 6; // 文件签名黑库
    AgentEngineLibUpgradeItem file_sl_lib  = 7; // 统一引擎库
  };
}

message AgentEngineLibUpgradeItem {
  string version = 1;  // 引擎版本
  HashEngineFileUpgradeErrorCode error_code = 2;  // 错误代码
}

// 升级类别
enum UpgradeKind {
  UK_UNDEFINED = 0;
  UK_AGENT = 1;
  UK_DRIVER = 2;
  UK_FILE_BLACKLIB = 3;
  UK_FILE_WHITELIB = 4;
  UK_DOMAIN_WHITELIB = 5;
  UK_FILESIGN_WHITELIB = 6;
  UK_DRIVER_BLACKLIB = 7;
  UK_ACDR_LIB = 8;
  UK_ATTACK_WHITELIB = 9;
  UK_FILESIGN_BLACKLIB = 10;
  UK_SL_FILELib = 11;
}

enum UpgradeNotifyResponeCode {
  UNRC_OK = 0;
  UNRC_SAME_VERSION = 1; // 版本相等
  UNRC_NOT_SUPPORT = 2; // 不支持
  UNRC_OTHER_ERROR = 3; // 其它错误
}

//message UpgradeNotify {
//  UpgradeKind kind = 1;
//  string pkgURL = 2; // 升级包下载地址
//  string rawFileName = 3;
//  bytes fileMD5 = 4;
//}

message UpgradeNotifyRespone {
  UpgradeKind kind = 1;
  UpgradeNotifyResponeCode code = 2;
  string errMsg = 3;
}

// ----- V01 end-----