syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/policy.proto";

//--------------------------------------------------
//
//  运行状态上报
//  对应 g_CmdMemProtectStatusReport
//--------------------------------------------------
message MemProtectModulesStatus{
  repeated ModuleStatus moduleStatus = 1;
  repeated AgentStatus agentStatus   = 2;
  bool     IsBlueCountOverLimit      = 3;
  AgentCrashStatus agentCrashStatus  = 4;
  ModuleConflictStatus conflict      = 5; // 总的冲突状态, 有一个驱动冲突，结果是冲突
  bool IsAgentResOverLimitRestart    = 6; // agent资源超限重启
  repeated WarningReport warningReport = 7;
}

enum ModuleReportType {
  DEFENSE = 0;      // Windows
  MONITOR  = 1;     // Windows
  BEHAVIOR = 2;     // Windows
  AXBININJ = 3;     // Windows
  AXKRNLDEC= 4;     // Windows
  MEM_SERVICE = 100;  // linux
  MEM_NGAV_SERVICE = 101;  // linux
  MEM_NGAV_HOOK = 102;  // linux
}

enum ModuleLoadingStatus{
  MLS_UNKNOW      = 0;  //未知
  MLS_LOAD_SUCCESS = 1;  //驱动加载成功
  MLS_LOAD_FAILED = 2;  //驱动加载失败
}

message ModuleStatus {
  ModuleReportType moduleType   = 1;
  bool             isRun        = 2; // 这个先不动 现在这个和loadingstatus是一样的
  string           stopTime     = 3;
  ModuleLoadingStatus moduleLoadingStatus = 4; // 2表示驱动加载失败
  ModuleEnableStatus moduleEnableStatus = 5;
}

enum ModuleConflictStatus {
  MCS_UNKNOW     = 0;
  MCS_Conflict   = 1;  //模块有冲突
  MCS_Normal     = 2;  //模块正常，没有冲突
}

enum ModuleEnableStatus {
  MES_UNKNOW     = 0;
  MES_Enabled    = 1; //驱动正常
  MES_Disabled   = 2; //驱动暂停
}

// Agent 完整性校验
message AgentStatus {
  string fileName = 1; // 名称
  bool   status   = 2;  // 校验结果
}

// Agent 状态上报
message AgentCrashStatus {
  string stopTime = 1; // crash时间
}

// //client -> server
// const unsigned short g_CmdMemProtectStatusReport    = 60;

// //server -> client
// const unsigned short g_CmdInjectJavaOrPhpRequest    = 61;
// //client -> server
// const unsigned short g_CmdInjectJavaOrPhpResponse   = 62;

// //server -> client
// const unsigned short g_CmdUnInstallJavaOrPhpRequest  = 63;
// //client -> server
// const unsigned short g_CmdUnInstallJavaOrPhpResponse = 64;

enum Status {
  STATUS_UNKNOW = 0;
  NOT_INJECTED = 1;//未注入
  INJECTED = 2;//已注入
  IN_INJECTION = 3;//注入中
  INJECTION_FAILED = 4;//注入失败
  UNINSTALLED = 5;//已卸载
}

enum ProcessType {
  PROCESS_TYPE_UNKNOW = 0;
  JAVA = 1; //Java
  PHP = 2; //PHP
}

// 内存马注入状态异常信息
enum InjectErrorNum {
  NULL_ERROR = 0;
  SYMBOL_LINK_ERROR = 1;             // Error querying process symbolic link  查询进程符号链接错误
  PROCESS_STATUS_ERROR = 2;          // Error querying process status 查询进程状态错误
  USERNAME_ERROR = 3;                // Error querying username by effective uid 按有效uid查询用户名时出错
  INJECTION_COMMAND_ERROR = 4;       // Failed to execute injection command 执行注入命令失败
  OPEN_JAVA_POLICYFILE_ERROR = 5;    // Failed to open Java switch policy file 打开java开关策略文件失败
  CHMOD_JAVA_POLICYFILE_ERROR = 6;   // Failed to change the permission of Java switch policy file 更改java开关策略文件权限失败
  DELETE_FILE_ERROR = 7;             // Delete file exception 删除文件异常
  EXISTS_FILE_ERROR = 8;             // Determine whether there is an error in the file 判断文件是否存在出错
  EXECUTE_RESTART_ERROR = 9;         // Failed to execute restart command 执行重启命令失败
  NOTFOUND_LIB_ERROR = 10;           // No library files found 没有找到安装的库文件
  EXCEPTION_ERROR = 11;              // Client exception 客户端异常
  NOTFOUND_INI_FILE_ERROR = 12;      // Not found php.ini file   查询不到php.ini文件
  GLIBC_NOT_FOUND_ERROR = 13;        // Not found glibc    查询不到glibc版本
  GLIBC_NOT_SUPPORT_ERROR= 14;       // Not support current glibc version  不支持当前主机的glibc版本
  RETURN_RESULT_OVERTIME_ERROR = 15; // Return inject result timeout 返回注入结果超时
  SEMANAGE_NOT_FOUND_ERROR = 16;     // 缺少semanage命令，无法直接注入
  LIBSTDCPLUS_SUPPORT_ERROR = 17;    // 当前的libstdc++版本过低
  SELINUX_NOT_SUPPORT_ERROR = 18;    // 不支持selinux环境注入
}

//agent->server
message InjectionStatus {
  string processName = 1; //usr/local/PHP-7.4/sbin/php-fpm
  Status status = 2;
  string middlewareVersion = 3;//nginx/1.12.2
  uint32 ppid = 4; //父进程PID
  repeated uint32 pids = 5; //子进程PID
  ProcessType processApplication = 6;
  string applicationVersion = 7; //ProcessType版本信息
  uint64 injectionUnixTimeMsecFrom1970 = 8;//毫秒时间戳
  uint32 pid = 9;//当前进程的PID
  uint32 errorNum = 10;//错误描述
}

message InjectionStatusList {
  repeated InjectionStatus items = 1;
  ProcessType processApplication = 2;
}

message InjectionStatusMsgBody {
  repeated InjectionStatusList items = 1;
}

//server<->agent
message InjectionRequest {
  string processName = 1;
  uint32 pid = 2;//当前进程的PID
  ProcessType processApplication = 3;
}

message InjectionResponse {
  uint32 status = 1;
  string processName = 2;
  uint32 pid = 3;//当前进程的PID
  ProcessType processApplication = 4;
}

//server<->agent
message UnInstallRequest {
  string processName = 1;
  uint32 pid = 2;//当前进程的PID
  ProcessType processApplication = 3;
}

message UnInstallResponse {
  uint32 status = 1;
  string processName = 2;
  uint32 pid = 3;//当前进程的PID
  ProcessType processApplication = 4;
}

//NGAV采集器运行状态
enum NgavCollectorRunningStatus {
  NCS_TYPE_UNKNOW = 0;
  NCS_RUNNING = 1;  //运行中
  NCS_SUSPEND = 2;  //暂停
  NCS_ABNORMAL = 3; //异常
}

//NGAV采集器状态
message NgavColletorStatus {
  NGAVCollectorType collector_type = 1;
  NgavCollectorRunningStatus running_status = 2;
}

// NGAV采集器状态报告
message NgavColletorStatusReport {
  repeated NgavColletorStatus reports = 1;
}

message DriverRunningStatus {
  ModuleReportType moduleReportType   = 1;    // 驱动种类定义
  int32 enable                        = 2;    // 驱动开关状态
}

message SetDriverStatus {
  bool enable = 1;
  SwitchStatusEnum switchPlugin = 2; // >=1404 插件开关控制
  SwitchStatusEnum switchDriver = 3; // >=1404 驱动开关控制
  // v01增加管控各个驱动，服务端可以管控5个驱动运行状态
  repeated DriverRunningStatus driverRunningStatus    = 20;
}

message ReportDriverStatus {
  bool enable                                         = 1;

  // v01增加管控各个驱动，5个驱动上报各自的运行状态
  repeated DriverRunningStatus driverRunningStatus    = 20;
}

message MemProtectWarningReport{
  repeated WarningReport warningReport = 1;
}

enum WarningReportType {
  Unknow=0;                 // 未知
  ServiceInjectTimeOut=1;   // 服务重复注入崩溃
  TimeOutCheck=2;           // 模块超时
  AgentCrash=3;             // Agent崩溃
  OSCrash=4;                // 系统崩溃
  UnexpectShutdown=5;       // 意外关机
  ManyHandle=6;             // 忘记关闭句柄
  ManyMemory=7;             // 内存泄漏
  InjectDllCrash=8;         // 注入程序崩溃
  InjectDllCrashFound=9;    // 注入程序崩溃文件发现
  ReportSuppression = 10;   // 误报压制
  TriggerSelfDowngrade = 11; // agent 触发自降级通知  (+ 神甲1700版本新增)
  ResourceOverLimit = 12;    // agent 资源超过限制（+ 神甲1700版本新增）
  RestoreSelfDowngrade = 13; // agent 恢复自降级通知 (+ 神甲1700版本新增）
  DriverAbnormal = 14;      // 驱动异常
  HandleNoClose = 15;       // 句柄未关闭(oplock)
  AgentNotOK = 16;          // Agent状态异常
}

message TimeOutCheckReport {
  string moduleName = 1; // 模块名称
  int64  timeOutTime = 2; // 超时时间
}

message ServiceInjectTimeOuthReport {
  string serviceName = 1; // 服务名称
}

message UnexpectShutdownReport {
  uint64 installAgentTime = 1;  // 首次安装时间
  uint64 beforeNum = 2;         // 安装agent前意外关机次数
  uint64 recentlyNum = 3;       // 最近7天意外关机次数
}

message InjectCrashReport {
  string fileName = 1; // 应用名称
}

message InjectDllCrashFoundReport {
  string fileName = 1; // 应用名称
}

message ReportSuppressionReport {
  string name = 1;  // 压制的检测项名称
  uint64 count = 2; // 检测次数
}

enum DriverAbnormalType {
  DAT_Unknow=0;                   // 未知
  DAT_KernelBehavorTrace = 1;     // 内核行为跟踪异常
}

message ResourceOverLimitReport {
  uint64 trigger_time = 1; // 资源超限时间（发生OOM的时间）
}

message DriverAbnormalReport {
  DriverAbnormalType type = 1;
}
message WarningReport {
  WarningReportType reportType = 1;
  uint64            timeStamp  = 2; // 系统时间戳/文件时间戳/日志时间戳
  oneof reportData {
    TimeOutCheckReport timeOutCheckReport = 3;
    ServiceInjectTimeOuthReport serviceInjectTimeOuthReport = 4;
    UnexpectShutdownReport UnexpectShutdownReport = 5;
    InjectCrashReport injectCrashReport = 6;
    InjectDllCrashFoundReport injectDllCrashFoundReport = 7;
    ReportSuppressionReport reportSuppressionReport = 8;
    ResourceOverLimitReport   resOverLimitReport = 9;//（+ 神甲1700版本新增）
    DriverAbnormalReport        driverAbnormalReport          = 10;
  }
}

message NoticeUploadFile {
  string modname = 1;
  repeated IgnoreWarning IgnoreWarnings = 2;
}

message IgnoreWarning {
  WarningReportType WarningType = 1; // 异常警告类型
  uint64            timeStamp  = 2;  // 系统时间戳
}

message CollectLogsType {
  enum Enum {
    Unknown = 0;
    SysDump = 1; // 系统 dump 日志
    AgentDump = 2; // Agent dump 日志
    SysEvent = 3; // 系统事件日志
    AgentRunning = 4; // 系统运行日志
  }
}

// CollectAndUploadLogs 取代原 NoticeUploadFile
message CollectAndUploadLogs {
  CollectLogsType.Enum type = 1;
  repeated IgnoreWarning ignore_warnings = 2;
  string upload_uri = 3; // 上传 uri, 形如 "https://ip:port/path?upload_key=xxx&task_id=xxx"
}
