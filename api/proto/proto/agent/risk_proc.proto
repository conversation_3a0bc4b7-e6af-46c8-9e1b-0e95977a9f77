syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";
import "agent/assets.proto";

//--------------------------------------------------
//
//  进程风险 识别结果
//  对应 g_CmdMemProtectRiskProcInfo
//--------------------------------------------------
message MemProtectRiskProcInfo {
    ClientID                  baseInfo            = 1;
    repeated ProcHiddenSelf   procHiddenSelfList  = 2;
    repeated ProcHiddenPort   procHiddenPortList  = 3;
    repeated ProcDangerous    procDangerousList   = 4;
    repeated ProcSensitivity  procSensitivityList = 5;
    repeated BackShell        procBackShellList   = 6;
    repeated ProcRiskASR      procRiskASRList     = 7;
    repeated ProcReusePort    procRiskReuseList   = 8;
    repeated ProcSensitivity  procPuppetList      = 9; //创建傀儡进程
    repeated ProcRiskOpenPort procRiskOpenPortList= 10;
    repeated ProcSensitivity  procPrivilegeElevationList = 11; // 进程提权,subProcessPath可能不存在
    repeated ProcSensitivity  processEscalationToRoot    = 12; // linux 进程提权
    repeated TimedTaskEscalationToRoot timedTaskEscalationToRoot = 13; // linux 疑似定时任务提权
    repeated HashAntivirus    hashAntivirus       = 14; // 恶意文件哈希检测
    repeated IllegalConnect   illegalConnect      = 15;
}

// 隐身进程
message ProcHiddenSelf {
    RiskHeader      header       = 1;
    ProcInformation Process      = 2;
    bytes           FileSha256   = 3; // 文件sha256
    bytes           CommandLine  = 4; // 进程命令行
    uint32          IsX86Process = 5; // 是否是32位进程
    string          ProcessMd5   = 6;
}

// 隐藏端口进程
message ProcHiddenPort {
    RiskHeader       header   = 1;
    ProcessInfo      Process  = 2;
    uint32           port     = 3;
    InternetProtocol protocol = 4;
}

// 风险进程开放端口
message ProcRiskOpenPort {
    RiskHeader       header          = 1;
    ProcessInfo      SourceProcess   = 2;
    ProcessInfo      TargetProcess   = 3;
    string           local_ip        = 4;
    int32            local_port      = 5;
}

// 端口复用
message ProcReusePort {
    RiskHeader       header         = 1;
    ProcessInfo      SourceProcess  = 2; // 复用端口风险进程
    ProcessInfo      TargetProcess  = 3; // 被复用端口系统进程
    uint32           port           = 4; // 被复用的端口
    InternetProtocol protocol       = 5; // 端口类型
}

// 高风险进程，CPU或者IO高负载运行
message ProcDangerous {
    RiskHeader      header       = 1;
    ProcInformation Process      = 2;
    bytes           FileSha256   = 3; // 文件sha256
    uint64          ioLoadAvg    = 4; // IO负载
    bytes           CommandLine  = 5; // 进程命令行
    uint32          IsX86Process = 6; // 是否是32位进程
}

// 启动敏感子进程
message ProcSensitivity {
    RiskHeader  header                = 1;
    ProcessInfo Process               = 2;
    bytes       subProcessPath        = 3; // 子进程路径
    bytes       subProcessFileSha256  = 4; // 子进程文件sha256
    bytes       subProcessCommandLine = 5; // 子进程命令行


    repeated ProcessInfo ProcessInfoList = 20;// 进程信息
    string ProcFlag   = 21;  // 进程链标记
    string DumpFlag   = 22;  // 内存dump标记
    string ScriptFlag = 23;  // 脚本标记
    uint64 EvidenceSize = 24; // 证据大小
    repeated ReportEvidenceInfo ReportEvidenceInfoList = 25; //证据信息链
    oneof has_PuppetProcMessage {PuppetProcMessage puppeteProcMesssage = 26;};   
}

// 反弹shell（仅Linux）
message BackShell {
    RiskHeader  header    = 1;
    ProcessInfo Process   = 2;
    bytes       RemotIP   = 3; // 远端IP
    uint32      RemotPort = 4; // 远程端口
    bytes       Protocol  = 5; // 协议tcp等
}

/*
 * 高风险进程：减少攻击面(ASR Attack Surface Reduction)
 * 系统风险->进程风险  中危            默认不拦截
 * 攻击特征：发现进程风险：/bin/ps 加载/tmp/test.so
 */
message ProcRiskASR {
    RiskHeader  header   = 1;
    ProcessInfo Process  = 2; // 存在风险的进程
    bytes       RiskFile = 3; // 进程加载的风险执行文件
}


// 疑似定时任务提权
message TimedTaskEscalationToRoot {
    RiskHeader header    = 1;
    string     task_name = 2;
    string     task_path = 3;
}

// 恶意文件哈希检测
message HashAntivirus {
    RiskHeader       header         = 1;
    ProcessInfo      process        = 2;
    uint32      virus_describe_id = 3; // 病毒类型id
}

//非法外联
message IllegalConnect {
    RiskHeader  header    = 1;
    ProcessInfo Process   = 2;
    bytes       RemotIP   = 3; // 远端IP
    uint32      RemotPort = 4; // 远程端口
    bytes       Protocol  = 5; // 协议tcp等
}

// 傀儡进程补充上报信息
message PuppetProcMessage {
    uint64 module_addr = 1;// 发现傀儡进程模块地址
    string mem_str = 2; // 检测块包含的字符串
}