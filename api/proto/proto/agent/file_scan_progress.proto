syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

// 上报类型
enum ReportProgressType {
  RPT_FILE_UNKNOWN = 0;
  RPT_FILE_THREATEN = 1; // 文件威胁进度
  RPT_FILE_SNAPSHOT = 2; // 文件快照进度
}

enum ReportProgressStatus {
  STATUS_UNKNOWN = 0;
  STATUS_START = 1; // 开始
  STATUS_SCANNING = 2; // 扫描中
  STATUS_UPLOADING = 3; // 上传中
  STATUS_PENDING = 4; // 暂停
  STATUS_DONE = 5; // 完成
}

message MacProgressStatusReport {
  bytes MachineID = 1; // 主机ID
  ReportProgressType reportType = 2; // 上报进度类型
  int32 Progress = 3; // 进度百分比整数部分[0~100]
  ReportProgressStatus status = 4; // 采集状态
  int64 ReportTime = 5; // 状态上报时间戳
  int64 IncreaseID = 6; // 增长ID，防并发
  int64 JobID      = 7; // 标识唯一上报任务
}
