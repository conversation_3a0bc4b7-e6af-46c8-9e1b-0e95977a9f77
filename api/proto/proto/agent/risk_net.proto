syntax = "proto3";
package agent;  
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

message MemProtectRiskNetInfo {
    ClientID baseInfo                                   = 1;
    repeated PortScan    scan_list                      = 2;
}

//端口扫描
message PortScan {
    RiskHeader header           = 1;
    bytes remote_ip             = 4;
    InternetProtocol protocol   = 6;
}