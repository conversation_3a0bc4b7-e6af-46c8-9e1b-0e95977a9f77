syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

// server -> agent
message ObtainEvidenceReq {
  EvidenceType EvidenceType = 1;  // 证据文件类型
  string key = 2;                 // 文件路径/md5/sha1/...
  string url = 3;                 // 证据上传URL, 如果URL为空，则不需要上传，仅返回文件路径信息
  uint64 task_id = 4;             // 任务ID
}

// agent -> server （仅失败时上报）
message ObtainEvidenceResp {
  uint64 task_id = 1;               // 任务ID
  ObtainEvidenceStatus status = 2;  // 任务状态
  string filepath = 3;              // 文件路径
}

enum ObtainEvidenceStatus {
  OBTAIN_EVIDENCE_STATUS_UNKNOWN = 0;
  OBTAIN_EVIDENCE_STATUS_OK = 1;
  OBTAIN_EVIDENCE_STATUS_FILE_NOT_FOUND = 2;
  OBTAIN_EVIDENCE_STATUS_UPLOAD_FAILED = 3;
  OBTAIN_EVIDENCE_STATUS_EVIDENCE_FLAG_ERROR = 4;
}