syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

// Server的操作标志，数据库中的每条记录，都有增加项和删除项标志
enum Operate {
  ADD    = 0;
  DEL    = 1;
  MODIFY = 2;
}

enum BWType {
  WHITE = 0;
  BLACK = 1;
}

enum ModuleType {
  MEM_RISK  = 0;
  FILE_RISK = 1;
  PROC_RISK = 2;
}

enum ReqType {
  ALL   = 0; // 差异化更新涉及到删除某些项，要请求所有[增加+删除]项
  ADDED = 1; // 当第一次agent为空列表时，只需请求所有现有的增加项
}

enum FileType {
  FT_FILETYPEUNKNOWN = 0;
  FT_FILE            = 1;
  FT_DIR             = 2;
}

enum SwitchStatus {
  SS_TURN_OFF     = 0; //关闭
  SS_TURN_ON      = 1; //开启
  SS_ONLY_MONITOR = 2; //仅上报，不拦截
}

// 黑白名单项的内容
message BlackWhiteItem {
  ModuleType module     = 1; // 废弃不用
  bytes      uniqueFlag = 2;
  BWType     bwType     = 3;
  Operate    addOrDel   = 4;
}

// 首次oldTimestamp=curTimestamp,agent自己做判断要不要请求
message UpdateBlackWhitePolicy {
  uint64                  oldTimestamp   = 1;
  uint64                  curTimestamp   = 2;
  repeated BlackWhiteItem blackwhiteList = 3;
}

message FileMonitorItem {
  bytes  filepath     = 1; // 文件或者目录的全路径
  uint32 filetype     = 2; // FileType相加的值，dir下的文件或目录,包含dir本身
  uint32 operate      = 3; // FileOperate相加的值
  bytes  whitePath    = 4; // 白名单路径列表 | 分隔 每个路径都带通配符，默认*后缀，eg:c:/abc/*|d:/bcd/*
  bytes  whiteProc    = 5; // 白名单进程名列表 | 分隔
  bytes  filenamelist = 6; // 文件名列表 | 分隔 支持*？通配符
  bytes  includeExt   = 7; // 包含的扩展名列表 | 分隔 与excludeExt互斥
  bytes  excludeExt   = 8; // 排除的扩展名列表 | 分隔 与includeExt互斥
}

message UpdateFileMonitorPolicy {
  bool                     result          = 1;
  repeated FileMonitorItem fileMonitorList = 2;
}

message RequestPolicyList {
  uint64  localTimestamp = 1;
  ReqType reqType        = 2;
}

// 风险类型
enum RiskInterceptType {
  RIT_UNKNOWN                         = 0;
  // mem
  RIT_LoadingRemoteModules            = 1;  // 加载远程模块
  RIT_StackAttributeAttacks           = 2;  // 栈属性攻击
  RIT_ExecuteCodeOnTheStack           = 3;  // 栈代码执行攻击
  RIT_FilelessAttacks                 = 15; // 反射式动态库注入-无文件攻击
  RIT_HeapAttacksExploits             = 16; // 堆攻击
  RIT_RopAttacks                      = 24; // ROP攻击
  RIT_LayoutShellcode                 = 25; // 布局shellcode
  RIT_StackPivot                      = 40; // 栈翻转
  RIT_RunningShellCode                = 41; // 执行shellcode
  RIT_StartProcess                    = 42; // 启动进程
  RIT_EngineAttack                    = 43; // 引擎攻击

  // file
  RIT_Virus                           = 6;  // 文件病毒
  RIT_FH_DynamicLibraryHijacking      = 13; // 文件劫持（动态库劫持）
  RIT_FH_EnvironmentVariableHijacking = 14; // 文件劫持（环境变量劫持）
  RIT_FH_OpeSensitiveFile             = 47; // 操作敏感文件

  // sys
  RIT_RemoteThreadInjection           = 10; // 远线程注入
  RIT_MiningProcess                   = 11; // 可疑挖矿攻击
  RIT_HiddenProcess                   = 17; // 隐藏进程
  RIT_SensitiveSubProc                = 44; // 启动敏感子进程
  RIT_RegRisk                         = 45; // 注册表风险
  RIT_AppRisk                         = 46; // 应用风险
  RIT_KernelRisk                      = 48; // 内核风险
  RIT_BackShell                       = 35; // 反弹Shell
  RIT_ASR                             = 36; // 攻击面减少
  RIT_HashLogin                       = 37; // PTH
  RIT_GoldenTicket                    = 49; // 黄金票据
  RIT_DirtyCow                        = 50; // 脏牛漏洞
  RIT_Rootkit_HiddenPort              = 18; // rootkit隐藏端口
  RIT_Process_Privilege               = 51; // 进程提权
  //4无签名pe文件 5文件加壳 19高风险进程，CPU或者IO高负载运行
  RIT_KernelHideProc                  = 52; // rootkit隐藏进程
  RIT_KernelIntegrity                 = 53; // 内核完整性校验
  RIT_KernelRemoteLeakOverflow        = 54;
  RIT_KernelHideModule                = 55; // rootkit隐藏模块
  RIT_WormRisk                        = 56;
  RIT_CVELeak                         = 57;
  RIT_LnkFileDetect                   = 58;
  RIT_Rootkit_ReusePort               = 73; // 端口复用
  RIT_KernelEXRisk                    = 74;
  RIT_AccountRisk                     = 75;
  RIT_CreatePuppetProc                = 77; //创建傀儡进程
  RIT_ProcListenPortDType             = 81; // 非法监听端口
  RIT_WebShellMemory                  = 82; //内存webshell
  RIT_DomainLogClear                  = 140;
  RIT_DomainLogServiceClose           = 141;
  RIT_DomainLoginWithCred             = 142;
  RIT_DomainCreateDirSrvObj           = 143;
  RIT_DomainAddPlanTask               = 144;
  RIT_DomainAddSysSrv                 = 145;
  RIT_DomainPsLoggedOn                = 146;
  RIT_DomainModiSenseGrp              = 147;
  RIT_DomainCve42278                  = 148;
  RIT_DomainAclUpdate                 = 149;
  RIT_DomainCve42287                  = 150;
  RIT_DomainSAMRQryUsrGrp             = 151;
  RIT_DomainASREPRoast                = 152;
  RIT_ProcessEscalationToRoot         = 153; // linux 进程提权
  RIT_TimedTaskEscalationToRoot       = 154; // linux 疑似定时任务提权
  RIT_HashAntivirus                   = 155; // 恶意文件哈希检测
  RIT_DomainEventLogCheck             = 156; //事件日志检测
  RIT_DomainZeroLogon                 = 157; //ZeroLogon漏洞
  //websehll static scanning
  RIT_WebShellStaticCheck             = 158; //webshell static scanning
  RIT_AntiVirusDetect                 = 160; //anti-virus detection

  //内存马能力
  RIT_JVMLoadSo                       = 103; //JVM进程加载.So库
  RIT_JVMDragLib                      = 104; //JVM进程执行拖库行为
  RIT_JVMUploadingFile                = 105; //JVM文件上传行为
  RIT_JVMRegisterServerComponent      = 114; //JVM注册服务器组件
  RIT_JVMServerWebShellScan           = 115; //JVM应用服务器webShell扫描
  RIT_JVMServerComponentScan          = 116; //JVM应用服务器已注册组件扫描
  RIT_JVMFileOp                       = 117; //java读写敏感文件
  RIT_JVMVirCmd                       = 118; //java虚拟终端
  RIT_JVMScanPort                     = 131;
  RIT_JVMJarLoad                      = 132;
  RIT_JVMZip                          = 133;
  RIT_JVMUnzip                        = 134;
  RIT_JVMHTTPParamsCheck              = 135;
  RIT_JVMSqlInject                    = 136;
  RIT_JVMRunCmd                       = 300; //进程执行外部命令
  RIT_PHPSystemCommand                = 323; //系统命令执行
  RIT_PHPCodeExec                     = 324; //PHP代码执行
  RIT_PHPCallBackFunc                 = 325; //回调函数利用
  RIT_PHPUploadSuspiciousFiles        = 326; //上传可疑文件
  RIT_PHPSensitiveFileAccess          = 327; //敏感文件访问
  RIT_PHPExecSuspiciousFile           = 328; //执行可疑文件

  RIT_AbnormalLoginRisk               = 600; // 异常登录
  RIT_ViolentRisk                     = 601; // 暴力破解
  RIT_WeakPasswdRisk                  = 602; // 弱密码

  // V01 拦截、仅上报
  RIT_V01_FileMonitor                 = 1001; // v01文件   检测 仅上报 拦截
  RIT_V01_IllegalOutreach             = 1002; // v01 黑灰产 仅上报 拦截
  RIT_V01_CreatePuppetProc            = 1003; // v01内存攻击 傀儡进程 近上报 拦截
  RIT_V01_RopAttacks                  = 1004; // v01内存攻击 ROP攻击 近上报 拦截
  RIT_V01_BufferOverflow              = 1005; // v01内存攻击 缓冲区溢出
  RIT_V01_HeapAttacksExploits         = 1006; // v01内存攻击 堆喷射
  RIT_V01_HeapStackAttach             = 1007; // v01内存攻击 堆栈属性攻击
  RIT_V01_LayoutShellcode             = 1008; // v01内存攻击 布局shellcode
  RIT_V01_RunningShellCode            = 1009; // v01内存攻击 shellcode执行
  RIT_V01_LoadingRemoteModules        = 1010; // v01内存攻击 执行远程模块
  RIT_V01_StackPivot                  = 1011; // v01内存攻击 栈翻转
  RIT_V01_EngineAttack                = 1012; // v01内存攻击 引擎攻击防护

  RIT_V01_Shell                       = 1013; // v01脚本攻击检测 脚本型
  RIT_V01_KernelBehaviorTracing       = 1014; // v01内存攻击 内核行为跟踪
  RIT_V01_EvilDriver                  = 1015; // v01内存攻击 恶意驱动
  RIT_V01_LeakDriver                  = 1016; // v01内存攻击 具有漏洞驱动

  RIT_V01_ShellAttackPowerShell       = 1017; // v01 白名单 脚本攻击 PowerShell
  RIT_V01_ShellAttackWMI              = 1018; // v01 白名单 脚本攻击 WMI
  RIT_V01_ShellAttackVBA              = 1019; // v01 白名单 脚本攻击 VBA
  RIT_V01_ShellAttackVBS              = 1020; // v01 白名单 脚本攻击 VBS
  RIT_V01_ShellAttackOther            = 1021; // v01 白名单 脚本攻击 其他
  RIT_V01_Tunnel_ICMP                 = 1022; // v01 隧道检测 ICMP
  RIT_V01_Tunnel_DNS                  = 1023; // v01 隧道检测 DNS

  //RIT_V01_GreyTool                    = 1014; // v01脚本攻击检测 灰色工具
  //RIT_V01_Lurk                        = 1015; // v01脚本攻击检测 潜伏型
  //RIT_V01_FilelessAttacks             = 1016; // v01脚本攻击检测 反射式动态注入

  RIT_V01_ACDRSwitch                  = 1024; // v01 linux acdr 总开关
  RIT_V01_FileCreateLog               = 1025; // v01 全量文件创建 开关

  // 钓鱼增强
  RIT_V01_FILE_SUSPICIOUS_TYPE         = 1026; // v01 白名单 钓鱼文件可疑类型
  RIT_V01_FILE_DDE_CODE                = 1027; // v01 白名单 钓鱼文件DDE代码
  RIT_V01_FILE_VBA                     = 1028; // v01 白名单 钓鱼文件VBA代码
  RIT_V01_FILE_LNK_TARGET              = 1029; // v01 白名单 钓鱼文件LNK目标
  RIT_V01_FILE_LNK_WORK                = 1030; // v01 白名单 钓鱼文件LNK工作目录
  RIT_V01_FILE_LNK_CMD                 = 1031; // v01 白名单 钓鱼文件LNK命令
  RIT_V01_FILE_LNK_ICON                = 1032; // v01 白名单 钓鱼文件LNK图标
  RIT_V01_FILE_URL                     = 1033; // v01 白名单 钓鱼文件URL
}

message RiskInterceptSwitchMsg {
  repeated RiskInterceptType riskInterceptOff   = 1; // 关闭拦截的
  repeated RiskInterceptType riskOn             = 2; // 风险开关
  RiskBehaviorSwitch         riskBehaviorSwitch = 3; // 行为分析开关,仅windows
}

message RiskBehaviorSwitch {
  SwitchStatus riskSwitch = 1;
}

enum NGAVCollectorType {
  NCT_UNKNOWN = 0;
  NCT_FILE    = 1;
  NCT_NET     = 2;
  NCT_PROCESS = 3;
  NCT_MEM     = 4;
  NCT_MISC    = 5;
}

// 全局开关
message GlobalSwitch {
  bool                      PTHEventLog        = 1; // 是否记录PTH日志
  bool                      DumpLsass          = 2; // 是否开启LSASS进程防护
  bool                      NetLogon           = 3; // 权限提升
  bool                      AccRisk            = 4; // 账户风险开关
  bool                      PuppetProc         = 5; // 傀儡进程
  bool                      WebShellMemory     = 6; //内存webshell
  bool                      KernelInject       = 7; //内核注入
  bool                      SelfProtect        = 8; // 自保护
  bool                      CheckRootkit       = 9; // rootkit检测
  repeated KernelInjectList kernelInjectList   = 10; // 内核注入详细信息

  bool                      InjectTomcat       = 11;
  bool                      InjectWeblogic     = 12;
  bool                      InjectWebsphere    = 13;
  bool                      InjectJetty        = 14;
  bool                      InjectJbossWildfly = 15;
  bool                      InjectResin        = 16;
  bool                      InjectSpring       = 17;
  bool                      InjectBES          = 18;
  bool                      InjectPHP          = 19;
  bool                      Performance        = 20;
  bool                      PHPApp             = 21;
  bool                      BankCompatible     = 30; // 银行兼容模式

  // v01相关开关
  bool                      V01FileMonitor     = 31; // v01文件检测威胁开关
  bool                      V01IllegalOutreach = 32; // v01非法外联开关
  bool                      V01MemoryAttack    = 33; // v01内存攻击检测
  bool                      V01ShellAttack     = 34; // v01脚本攻击检测
  bool                      VO1CheckLdr        = 35; // v01添加对PEB.LDR的檢测
  bool                      V01ACDRSwitch      = 36; // v01 ACDR 开关
}

// PTH->pass the hash
enum PTHInterceptMode {
  PTH_Unknown         = 0; // 不拦截
  PTH_SpecifyUser     = 1; // 拦截指定用户名，如果用户名为空，不拦截
  PTH_AllUser         = 2; // 拦截所有用户
  PTH_Admin           = 3; // 拦截管理员
  PTH_SpecifyAndAdmin = 4; // 拦截指定用户名和管理员
}

message PTHPolicy {
  bool             switch       = 1; // 开关
  repeated string  hostList     = 2; // IP数组
  PTHInterceptMode pthMode      = 3; // 拦截模式
  repeated string  usernameList = 4; // 用户名数组
}

message CustomFileBlackWhitePolicy {
  repeated CustomFileBlackWhiteItem bwList = 1; // 手动添加的黑白名单
}

message CustomFileBlackWhiteItem {
  bytes  md5    = 1;
  bytes  sha1   = 2;
  BWType bwType = 3; // 黑或白
}

message DigitalSignaturePolicy {
  repeated DigitalSignatureItem dsList = 1; // 数字签名列表，只有Windows会有
}

message DigitalSignatureItem {
  bytes  signature   = 1; // 数字签名
  bytes  certificate = 2; // 证书指纹
  BWType bwType      = 3; // 黑或白
}

// 驱动注入模块应用列表
message KernelInjectItem {
  string filePath    = 1; // 进程路径
  string cmdLineArgs = 2; // 命令行参数
}

// 驱动注入模块应用列表
message KernelInjectList {
  uint32                    id               = 1; // 数据库里面对应的id
  repeated KernelInjectItem kernelInjectItem = 2;
}

message LonginTimes {
  enum Frequency {
    INVALID     = 0;
    EVERY_DAY   = 1;
    EVERY_WEAK  = 2;
    EVERY_MONTH = 3;
  }

  Frequency       longin_frequency = 1;  // 登录周期
  repeated uint32 days             = 2;  // 每日的情况不用传，每周的话传1-7的数组， 每月的话传1-31的数组
  string          start_time       = 3;  // 开始时间
  string          end_time         = 4;  // 结束时间
}

message AbnormalLoginConfig {
  enum StudyMode {
    Undefined = 0;
    AutoStudy = 1; // 自动学习
    Custom    = 2; // 自定义规则
  }

  repeated string      login_ip    = 1;  // 登录的白ip
  repeated LonginTimes login_times = 2;  // 登录的白时间段，考虑后面的扩展性，可以同时配置多个白时间段
  repeated string      login_user  = 3;  // 登录账号
  StudyMode            study_mode  = 4;  // 检测学习模式
}

message UpdateFileInfo {
  string version = 1; //版本
  bytes  md5     = 2; //md5
}

// BWRuleDetail 细项规则详细内容
// 语义化表述为:
//  - [属性] [匹配] [范围] [值...]
// 如:
//  - 文件md5 等于 以下任一 ["abcd1234", "foobar"]
//  - 进程名 包含 以下所有 ["hello", "foobar"]
message BWRuleDetail {
    enum Scope {
        Undefined = 0;
        AllOf     = 1; // 匹配所有: "且" 条件
        OneOf     = 2; // 匹配任一: "或" 条件
    }

    string          attribute   = 1; // 属性名: md5; 进程名; 全路径; ...
    uint32          condition   = 2; // 匹配条件: 等于; 包含; 前缀; ...
    Scope           scope       = 3; // 匹配范围: 1: 全部; 2: 任一
    repeated string values      = 4; // 匹配值列表
}

// BWRulePkg 规则包
// 单项规则: 由一个细项规则构成
// 组合规则: 由多个细项规则以 "且" 的关系组合而成
message BWRulePkg {
    string                  checkpoint      = 1; // 功能点
    int64                   id              = 2; // 规则包ID
    repeated BWRuleDetail   rule_details    = 3; // 此规则包包含的组合规则列表, 单项规则只有一个元素
    string                  min_ver         = 4; // 最小版本 "":无限制 或者 "V2.0.2401.001"
    string                  max_ver         = 5; // 最大版本 "":无限制
}

// BWRuleList 规则包列表
message BWRuleList {
    repeated BWRulePkg rule_list = 1; // 此 agent 应用的所有规则包列表
}

message Settings {
  //使用oneof用于变相支持has方法
  //通过如下方式检测元素是否为空 ：setting.has_usualLogonThreshold_case() == MemProtect::Setting::HAS_USUALLOGONTHRESHOLD_NOT_SET;
  oneof has_usualLogonThreshold {uint32 usualLogonThreshold = 1;}       //成功登录次数到该值后，则将该登录认定为常用地址
  oneof has_weakPasswordListTimestamp {uint64 weakPasswordListTimestamp = 2;} //记录服务器最后一次的更新弱密码表的时间，客户端如果发现和本地策略的时间不一致则进行更新弱密码表
  oneof has_riskInterceptSwitchMsg {RiskInterceptSwitchMsg riskInterceptSwitch = 3;}  // 暂时不通过这里下发
  oneof has_globalSwitch {GlobalSwitch globalSwitch = 4;}  // 暂时不通过这里下发
//  oneof has_driverstatus {SetDriverStatus driverStatus = 5;} // 暂时不通过这里下发
  oneof has_AbnormalLoginConfig {AbnormalLoginConfig abnormal_login_config = 6;}      // 异常登陆白名单策略
  oneof has_WhiteRuleList {BWRuleList white_rule_list = 8;} // 白名单列表
}

//仅客户端用配置文件结构
message AgentSettings {
  Settings serverSettings = 1;
  oneof hash_hashlibinfo {UpdateFileInfo hash_lib_info = 2;}//hash引擎库信息
  //此处可以定义agent自用的一些配置信息
  oneof has_sha256libinfo {UpdateFileInfo sha256_lib_info = 3;}
  oneof has_antiviuspolicy {AntiVirusPolicy anti_virus_policy = 4;}
  oneof has_webshellengine_ver {DetectEngineVerResp webshell_engine_ver = 5;}
  oneof has_virusengine_ver {DetectEngineVerResp virus_engine_ver = 6;}
}

message WeakPwdListReponse {
  repeated string weakPasswordList      = 1; // 弱密码表
  repeated string weakPasswordRegexList = 2; // 弱密码正则
}

message TimeItem {
  string start_time = 1;                                        // 开始执行时间
  string end_time   = 2;                                        // 截止执行时间
}
message TimeScheduled{
  enum Frequency {
    SET_INVALID     = 0;
    SET_EVERY_DAY   = 1;
    SET_EVERY_WEAK  = 2;
    SET_EVERY_MONTH = 3;
  }
  Frequency         frequency      = 1;  // 登录周期
  repeated uint32   days           = 2;  // 每日的情况不用传，每周的话传1-7的数组， 每月的话传1-31的数组
  repeated TimeItem time_item_list = 3;  // 时间段
  int64             immediately    = 4;  // 立即扫描时间戳
}

message WebshellScanItem {
  uint32        scanpathpolicy = 1;                                        // 扫描路径策略 1 只扫描webshell站点， 2 只扫描自定义目录 ， 3 扫描web site + 自定义目录
  TimeScheduled time_scheduled = 2;                                        // 定时设置选项
  bytes         monitor_path   = 3;                                        // 监控目录列表 | 分割
  bytes         ignore_path    = 4;                                        // 忽略目录列表 | 分隔 , 应该为monitorpath的子目录，优先级高于monitorpath
}
//webshell static scanning 策略下发
message UpdateWebshellScanPolicy {
  bool                      result             = 1;
  repeated WebshellScanItem webshell_scan_list = 2;
}
// Anti-Virus
message AntiVirusPolicy {
  bool                      result                  = 1;  // agent响应为true
  uint32                    scanpathpolicy          = 2;  // 0b0001: 全盘; 0b0010: 自定义; 0b0100: 推荐
  TimeScheduled             time_scheduled          = 3;  // 扫描设置实时，定期
  bytes                     monitor_path            = 4;  // 监控目录列表 | 分割
  bytes                     ignore_path             = 5;
  bool                      enable                  = 6;  // V01 新增 是否开启
  uint32                    max_filesize            = 7;  // V01 文件最大限制 单位为M
  repeated V01FileType      anti_scan_type_list     = 8;  // V01 630 新增扫描文件类型
  bool                      snapshot_enable         = 9;  // V01 新增 文件属性是否开启
  TimeScheduled             snapshot_time_scheduled = 10; // 文件属性扫描设置实时，定期
  repeated V01FileType      snapshot_scan_type_list = 11; // V01 630文件属性扫描文件类型
  uint32                    snapshot_max_filesize   = 12;
  V01FileScanConfig         anti_scan_type_conf     = 13; // 高危文件采集; 静态文件采集; 全量文件采集; 自定义采集
  bool                      phishing_detect         = 14; // 釣魚實時檢測
  bool                      real_time_scan_linux    = 15; // 实时扫描 (Linux)
  bool                      real_time_scan_windows  = 16; // 实时扫描 (Windows)
  bool                      udsik_phishing_detect   = 17; // 钓鱼实时监测->U盘钓鱼
  bool                      im_phishing_detect      = 18; // 钓鱼实时监测->im钓鱼
  bool                      email_phishing_detect   = 19; // 钓鱼实时监测->email钓鱼
}

// 扫描类型
enum TaskScanType {
  SO_UNKNOWN = 0;
  SO_CYCLE   = 1;  // 定时执行
  SO_MANUAL  = 2;  // 手动触发
}

// 检测结果
enum VirusDetectResult {
  RESULT_UNKNOWN = 0;
  RESULT_BLACK   = 1;
  RESULT_WHITE   = 2;
  RESULT_TIMEOUT = 3;
  RESULT_FAIL    = 4;
}

// 检测优先级
enum VirusDetectPriority {
  DETECT_PRIORITY_LOW    = 0;
  DETECT_PRIORITY_MIDDLE = 1;
  DETECT_PRIORITY_HIGH   = 2;
}

message VirusDetectItem {
  string                 sha256        = 1;  // sha256值
  VirusDetectResult      result        = 2;  // 威胁检测结果 黑，白，超时，失败
  int32                  risk_level    = 3;  // 威胁等级
  int32                  risk_type     = 4;  // 威胁类型
  string                 virus_name    = 5;  // 病毒名称
  string                 malware_type  = 6;  // 威胁类型
  FileTypeIdent          file_type     = 7;  // 文件类型(php ,asp,jsp,...)
  string                 file_path     = 8;  // 文件路径
  string                 server_addr   = 9;  // 服务端地址
  string                 agent_addr    = 10; // agent地址
  int32                  source        = 11; // 来源  1.rasp
  int64                  req_time      = 12; // 请求时间
  int64                  atime         = 13; // 最后一次访问文件时间
  int64                  mtime         = 14; // 最后一次文件修改时间
  int64                  ctime         = 15; // 最后一次文件改变时间
  string                 st_mode       = 16; // 文件权限
  string                 sha1          = 17; // 文件sha1
  string                 md5           = 18; // 文件md5
  int32                  file_size     = 19; // 文件大小
  bytes                  uniqueFlag    = 20; // 唯一的处理相关信息
  string                 file_version  = 21; // 文件版本
  bytes                  file_vendor   = 22; // 文件厂商
  repeated SignatureInfo signatureInfo = 23; // 进程文件签名信息
  string                 tlsh_hash     = 24; // TLSH哈希
  string                 imp_hash      = 25; // IMP哈希
  int32                  priority      = 26; // 检测优先级
}

// 请求文件结果查询接口
message SearchBySha256Req {
  TaskScanType             task_scan_type = 1; // 查询触发方式，这一版没有用，预留的
  repeated VirusDetectItem sha256_list    = 2; // 请求查询的sha256列表
}

// 请求文件结果响应接口
message SearchBySha256Resp {
  repeated VirusDetectItem virus_detect_List = 1;
}

//文件上传通信接口
message UploadFileReq {
  string              sha256   = 1;  // sha256值
  string              url      = 2;  // 上传地址的url
  map<string, string> params   = 3;  // post参数
  string              filepath = 4; // v01 新增 要上传的文件路径
}

enum FileStatus{
  FILE_NOT_FOUND = 0;
  FILE_FOUND     = 1;
}

message UploadFileResp {
  FileStatus    result    = 1;   //文件状态返回
  string        sha256    = 2;   // sha256值
  FileTypeIdent file_type = 3; //文件类型(php ,asp,jsp,...)
  string        filename  = 4; //文件名称
}


enum DetectKindIdent {
  DETECT_KIND_UNKNOWN  = 0;
  DETECT_KIND_WEBSHELL = 1;
  DETECT_KIND_VIRUS    = 2;
}

message DetectEngineVerReq {
  DetectKindIdent kind = 1;
}

message DetectEngineVerResp {
  DetectKindIdent kind              = 1;
  int64           EngineVerStamp    = 2;
  int64           EngineSwitchStamp = 3;
}

message AgentFileReq {
  int64               detail_id         = 1; // 风险详情ID，agent收到请求后的回复需要把该ID带上
  string              file_path         = 2; // 文件路径
  int64               process_id        = 3; // 进程ID
  string              class_name        = 4; // 类名
  string              class_loader_name = 5; // 所属类加载器
  string              url               = 6; // 上传地址的url
  map<string, string> params            = 7; // post参数
  AgentFileKind       kind              = 8; // 文件类型
  string              host              = 9;
}

enum AgentFileKind {
  AGENT_FILE_KIND_UNKNOWN  = 0;
  AGENT_FILE_KIND_CLASS    = 1; // 类文件
  AGENT_FILE_KIND_WEBSHELL = 2; // webshell文件
  AGENT_FILE_KIND_NGAV     = 3; // ngav文件
}

enum AgentFileStatus {
  AGENT_FILE_STATUS_UNKNOWN      = 0;
  AGENT_FILE_STATUS_NOT_FOUND    = 1;
  AGENT_FILE_STATUS_FOUND        = 2;
  AGENT_FILE_STATUS_DUMP_FAIL    = 3;
  AGENT_FILE_STATUS_UPLOAD_FAIL  = 4;
  AGENT_FILE_STATUS_SIZE_OVERRUN = 5;
}

enum V01FileType {
  V01_FILE_TYPE_UNKNOWN = 0;
  V01_FILE_TYPE_EXEC    = 1;  // EXE(可执行程序)
  V01_FILE_TYPE_DOC     = 2;  // DOC/DOCX/DOCM
  V01_FILE_TYPE_XLS     = 3;  // XLS/XLSX
  V01_FILE_TYPE_PDF     = 4;  // PDF
  V01_FILE_TYPE_PPT     = 5;  // PPT/PPTX/PPTM
  V01_FILE_TYPE_DLL     = 6;
  V01_FILE_TYPE_BAT     = 7;
  V01_FILE_TYPE_MSI     = 8;
  V01_FILE_TYPE_ODT     = 9;
  V01_FILE_TYPE_VB      = 10;
  V01_FILE_TYPE_JS      = 11;
  V01_FILE_TYPE_PS      = 12;
  V01_FILE_TYPE_PY      = 13;
  V01_FILE_TYPE_SH      = 14; // Linux V01 新增Shell类型
  V01_FILE_TYPE_LNK     = 15; // Windows V01 新增链接类型
  V01_FILE_TYPE_ARCHIVE = 16; // 新增压缩包类型
}

enum V01FileScanConfig {
    V01_FILE_SCAN_CONFIG_UNKNOWN        = 0;
    V01_FILE_SCAN_CONFIG_RECOMMENDED    = 1;
    V01_FILE_SCAN_CONFIG_STATIC         = 2;
    V01_FILE_SCAN_CONFIG_FULL           = 3;
    V01_FILE_SCAN_CONFIG_CUSTOM         = 4;
}

message AgentFileResp {
  int64           detail_id = 1;  // 风险详情ID，agent收到请求后的回复需要把该ID带上
  AgentFileKind   kind      = 2;  // 文件类型
  AgentFileStatus status    = 3;  // 文件状态返回
}


message GetProcPathPidsReq {
  int64           detail_id         = 1; // 风险详情ID，agent回复列表时需要把该ID带上
  repeated string process_path_list = 2; // 进程路径列表，告警只会有一个，但事件可能有多个
}

enum GetProcPathPidsResult {
  GET_PROC_PATH_PIDS_UNKNOWN = 0;
  GET_PROC_PATH_PIDS_SUCC    = 1;
  GET_PROC_PATH_PIDS_FAIL    = 2;
}

message ProcPathPidsItem {
  GetProcPathPidsResult result       = 1; // pid列表获取结果
  string                process_path = 2; // 进程路径
  repeated int32        pids         = 3; // pid列表
}

message GetProcPathPidsResp {
  int64                     detail_id = 1; // 风险详情ID，agent回复列表时需要把该ID带上
  repeated ProcPathPidsItem list      = 2;
}


message ThreatenHandleItem {
  string                         process_path = 1; // 进程路径
  repeated ThreatenHandleResItem list         = 2; // pid列表
}

message ThreatenHandleReq {
  int64                       detail_id   = 1; // 风险详情ID，agent回复需要把该ID带上
  int64                       handle_time = 2; // 下发处置的时间，agent回复需要把该时间带上
  repeated ThreatenHandleItem list        = 3; // 处置的列表，告警一次只会处置一个进程，但事件可能有多个
}


enum ThreatenHandleRes {
  THREATEN_HANDLE_RESULT_UNKNOWN = 0;
  THREATEN_HANDLE_RESULT_SUCC    = 1;
  THREATEN_HANDLE_RESULT_FAIL    = 2;
}

message ThreatenHandleResItem {
  ThreatenHandleRes result = 1;
  repeated int32    pids   = 2;
}

message ThreatenHandleResp {
  int64                       detail_id   = 1; // 风险详情ID，agent回复需要把该ID带上
  int64                       handle_time = 2; // 下发处置的时间，agent回复需要把该时间带上
  repeated ThreatenHandleItem list        = 3; // 处置结果的的列表
}

// webshell 和 杀毒 agent任务数据上报 -- start
enum FileVirusKind {
  FILE_KIND_NULL      = 0;
  FILE_KIND_WEBSHELL  = 1;
  FILE_KIND_ANTIVIRUS = 2;
}

message FileVirusDetectionInfo {
  FileVirusKind file_kind              = 1;   // 区分上报 webshell 还是 杀毒
  uint32        total_scanned_numbers  = 2;   // agent 总的扫描文件个数
  uint32        finish_scanned_numbers = 3;   // agent 已经检测完成的文件个数
}
// webshell 和 杀毒 agent任务数据上报 -- end

// 从神甲Linux移植过来
enum SwitchStatusEnum {
  NULL_STATUS  = 0;  // 默认无任何操作
  START_STATUS = 1;  // 开启操作标识
  STOP_STATUS  = 2;  // 关闭操作标识
}
