syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

message ClientID {
  string dateTime = 2; //事件发生的大致时间

  // v01新增
  uint64 clientTime = 20; // 客户端发现时间
  string clientVersion = 30;// 客户端版本信息
}

//--------------------------------------------------
//
//  风险识别头
//
//--------------------------------------------------
message RiskHeader {
  uint32 Level = 1; // 默认为0 风险等级 0：无风险 1：低风险
  bytes  Comment = 2; // 攻击特征描述
  uint32 operateFlag = 3; // 处理标志
  bytes  uniqueFlag = 4; // 唯一的处理相关信息
}

// eventlog与policy都要用，所以在common里面定义
enum FileOperate {
  FO_FILEOPERATEUNKNOWN = 0;
  FO_CREATEFILE = 1;  // 新建
  FO_READFILE = 2;  // 读取
  FO_WRITEFILE = 4;  // 写入
  FO_RENAMEFILE = 8;  // 改名
  FO_DELETEFILE = 16; // 删除
  FO_EXECUTEFILE = 32; // 执行
  FO_ATTRIBUTEFILE = 64; // 修改属性
}

message ProcessInfo {
  uint32 PID = 1; // 当前进程ID
  bytes  ProcessName = 2; // 当前进程名
  bytes  FilePath = 3; // 进程路径
  bytes  FileSha256 = 4; // 文件sha256
  bytes  FileMd5 = 7; // 文件md5
  bytes  CommandLine = 5; // 进程命令行
  uint32 IsX86Process = 6; // 是否是32位进程
  bytes UserName = 8; // 进程所属用户

  //linux神甲在不断迭代，所以序号20开始，已防冲突
  uint64 CurrentCreateTime = 20;  //进程启动时间 unix时间戳
  uint32 PPID = 21; // 当前进程父进程ID
  uint64 ParentCreateTime = 22;  //当前进程父进程启动时间 unix时间戳

  bytes  FileSha1 = 23; // 文件sha1

  uint64 FileCreateTime = 24;  //进程文件创建时间 unix时间戳
  uint64 FileModifyTime = 25;  //进程文件修改时间 unix时间戳
  uint64 FileLastAccessTime = 26;  //进程文件最后访问时间 unix时间戳
  uint64 FileSize = 27;  //进程文件大小
  bytes  FileCompanyName = 28; // 进程文件产商
  bytes  FileVersion = 29; // 进程文件版本
  bytes  AccessToken = 30; // 进程特权
  bytes  user = 31; //进程所属用户

  repeated SignatureInfo signatureInfo = 32; // 进程文件签名信息
}

//网络5元素上报用结构
//
message NetInfo{
  uint32 port = 1;    //端口
  string address = 2;    //地址信息
}

// IP 协议版本可以用来标识端口IP
enum InternetProtocol {
  IP_TCP = 0;
  IP_UDP = 1;
  IP_TCP6 = 2;
  IP_UDP6 = 3;
}

enum NewFileOperate {
  NFO_UNKNOWN = 0;
  NFO_READFILE = 1;
  NFO_WRITEFILE = 2;
  NFO_EXECUTEFILE = 3;
  NFO_RENAMEFILE = 4;
  NFO_HIDEFILE = 5;
  NFO_DELETEFILE = 6;
  NFO_CREATEFILE = 7;
  NFO_LINKFILE = 8;
  NFO_CHATTRFILE = 9;
  NFO_CHMODFILE = 10;
}

message OpFileInfo {
  string   FileName = 1;
  string   FilePath = 2;  //文件路径
  string   FileMd5 = 3;
  string   StMode = 4; /* 文件权限，ge: rwxr-xr--*/
  uint32   FileSize = 5;  /* 文件大小，单位字节*/
  string   atime = 6; /*最后一次访问文件时间*/
  string   mtime = 7; /* 最后一次文件修改时间*/
  string   ctime = 8; /* 最后一次文件改变时间*/
}

message SignatureInfo{
  bytes  Serial = 1; // 签名序列号
  bytes  IssuerName = 2; // 签名颁发者
  bytes  Customer = 3; // 签名使用者
  bytes  Thumbprint = 4; // 签名指纹
  bytes  Result = 5; // 签名状态
  bytes  Description = 6; // 签名描述
  bytes  NotAfter = 7; // 签名有效期起始时间
  bytes  NotBefore = 8; // 签名有效期到期时间
  bytes  SignAlgorithm = 9; // 签名算法
  bytes  SignHashAlgorithm = 10; // 签名哈希算法
  bytes  Version = 11; // 签名版本
  SignStatus SignStatusInfo = 12; //签名状态
}

enum EvidenceType {
  EVIDENCE_TYPE_UNKNOWN = 0;
  EVIDENCE_TYPE_NET = 1;
  EVIDENCE_TYPE_FILE = 2;
  EVIDENCE_TYPE_SCRIPT_FILE = 4;
  EVIDENCE_TYPE_CLUE_MEMORY_DUMP = 8;
  EVIDENCE_TYPE_SYSTEM_LOG = 16;
  EVIDENCE_TYPE_MEMORY_MINIDUMP = 32;
  EVIDENCE_TYPE_MEMORY_SEGMENT = 64;
}

message ReportEvidenceInfo{
  EvidenceType evidence_type = 1;    //证据类型
  string uniqueflag = 2;    //证据唯一标识
  uint64 evidenceSize = 3;   //证据大小
  string filepath = 4;
  string filename = 5;
  string md5 = 6;
  string sha256 = 7;
  int64 atime = 8;
  int64 mtime = 9;
  int64 ctime = 10;
  uint64 filesize = 11;
}

enum FileTypeIdent{
  FILE_TYPE_UNKNOWN = 0;
  FILE_TYPE_PHP = 1;
  FILE_TYPE_JSP = 2;
  FILE_TYPE_ASP = 3;
  FILE_TYPE_ASPX = 4;
  FILE_TYPE_PYTHON = 5;
  FILE_TYPE_PERL = 6;
  FILE_TYPE_JPG_PHP = 7;
  FILE_TYPE_GIF_PHP = 8;
  FILE_TYPE_BMP_PHP = 9;
  FILE_TYPE_PNG_PHP = 10;
  FILE_TYPE_JP2_PHP = 11;
  FILE_TYPE_ICON_PHP = 12;
  FILE_TYPE_JPG_JSP = 13;
  FILE_TYPE_GIF_JSP = 14;
  FILE_TYPE_BMP_JSP = 15;
  FILE_TYPE_PNG_JSP = 16;
  FILE_TYPE_JP2_JSP = 17;
  FILE_TYPE_ICON_JSP = 18;
  FILE_TYPE_JPG_ASP = 20;
  FILE_TYPE_GIF_ASP = 21;
  FILE_TYPE_BMP_ASP = 22;
  FILE_TYPE_PNG_ASP = 23;
  FILE_TYPE_JP2_ASP = 24;
  FILE_TYPE_ICON_ASP = 25;
  FILE_TYPE_JPG_ASPX = 26;
  FILE_TYPE_GIF_ASPX = 27;
  FILE_TYPE_BMP_ASPX = 28;
  FILE_TYPE_PNG_ASPX = 29;
  FILE_TYPE_JP2_ASPX = 30;
  FILE_TYPE_ICON_ASPX = 31;
  FILE_TYPE_JPG_PYTHON = 32;
  FILE_TYPE_GIF_PYTHON = 33;
  FILE_TYPE_BMP_PYTHON = 34;
  FILE_TYPE_PNG_PYTHON = 35;
  FILE_TYPE_JP2_PYTHON = 36;
  FILE_TYPE_ICON_PYTHON = 37;
  FILE_TYPE_JPG_PERL = 38;
  FILE_TYPE_GIF_PERL = 39;
  FILE_TYPE_BMP_PERL = 40;
  FILE_TYPE_PNG_PERL = 41;
  FILE_TYPE_JP2_PERL = 42;
  FILE_TYPE_ICON_PERL = 43;
  FILE_TYPE_ELF = 44;
  FILE_TYPE_SHELL = 45;
  FILE_TYPE_PE = 46;
  FILE_TYPE_PDF = 47;
  FILE_TYPE_DOC = 48;
  FILE_TYPE_XLS = 49;
  FILE_TYPE_PPT = 50;
  FILE_TYPE_DLL = 51;
  FILE_TYPE_BAT = 52;
  FILE_TYPE_MSI = 53;
  FILE_TYPE_ODT = 54;
  FILE_TYPE_VB = 55;
  FILE_TYPE_JS = 56;
  FILE_TYPE_PS = 57; // powershell
  FILE_TYPE_PY = 58;
  FILE_TYPE_LNK = 59;
  FILE_TYPE_ARCHIVE = 60;
}

enum SignStatus{
  SS_UnKnown = 0; //证书状态未知
  SS_NoSignature = 1; //无证书
  SS_Trusted = 2; //受信任的证书
  SS_Expired = 3; //证书已过期
  SS_Revoked = 4; //证书已被撤销
  SS_Distrust = 5; //不受信任的证书
  SS_SecuritySettings = 6; //由于本地安全策略导致证书未通过验证
  SS_BadSignature = 7; // 证书数字签名未通过验证
}

message ProcFileDetailInfo {
  string InternalName = 1;  // 内部名称
  string ProductName = 2;  // 产品名称
  string CompanyName = 3;  // 公司名称
  string Copyright = 4;  // 版权
  string Version = 5;  // 版本
  string Desc = 6;  // 描述
  string Trademark = 7;  // 商标信息
  string BuildInfo = 8;  // 构建信息
  string VerCode = 9;  // 版本号
  string OriginalInfo = 10; // 原始名称
  string SpecialBuildInfo = 11; // 特殊构建信息
}