syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

// 服务端和agent通信命令字
enum Command {
  CMD_UNKNOWN = 0; // 未定义
  CMD_ASSETS_INFO = 1; // 资产信息 agent->server->agent
  CMD_RISK_FILE_INFO = 2; // 文件类风险 agent->server->agent
  CMD_RISK_MEM_INFO = 3; // 内存类风险 agent->server->agent
  CMD_RISK_PROC_INFO = 4; // 进程类风险 agent->server->agent
  CMD_RISK_SYS_INFO = 7; // 系统类风险 agent->server->agent
  C<PERSON>_ACDR_RULE_MESSAGE_REPORT = 8; // ACDR 事件消息上报 agent->server->agent
  C<PERSON>_ACDR_RULE_ALL_MESSAGE_REPORT = 9; // ACDR 全量消息上报 agent->server->agent
  CMD_HEARTBEAT = 10; // 心跳消息 agent->server->agent
  CMD_RES_FILE = 22; // 请求文件响应消息 server->agent
  CMD_UPDATE_RISK_BLACK_WHITE_POLICY = 24; // 下发攻击风险黑白名单策略 server->agent
  CMD_LICENSE_ENABLE = 27; // 请求license agent->server
  CMD_LICENSE_ENABLE_RES = 28; // 请求license响应 server->agent
  CMD_AGENT_UPGRADE = 29; // agent升级命令 server->agent
  CMD_AGENT_UPGRADE_RES = 30; // agent升级响应 agent->server->agent
  CMD_AGENT_MIGRATE = 31; // agent迁移 server->agent
  CMD_AGENT_MIGRATE_RES = 32; // agent迁移结果上报 agent->server->agent
  CMD_MODULE_STATUS = 36; // 驱动加载状态 agent->server->agent
  CMD_CUSTOM_FILE_BLACK_WHITE_POLICY = 38; // 个性化文件黑白名单 server->agent
  CMD_DIGITAL_SIGNATURE_POLICY = 39; // 数字签名 server->agent
  CMD_ACCOUNT_RISK_CONFIG = 40; // 账户风险策略配置 server->agent
  CMD_RESET_BLUE_SCREEN_COUNT = 42; // 蓝屏避障状态重启 agent
  CMD_NOTICE_UPLOAD_FILE = 43; // 通知上传文件 server->agent
  CMD_UPLOAD_FILE = 44; // 上传文件 agent->server->agent
  CMD_SERVER_DRIVER_UPGRADE_NOTIFY = 45; // server端下发驱动升级通知
  CMD_AGENT_DRIVER_UPGRADE_REQUEST = 46; // agent上传内核版本
  CMD_AGENT_DRIVER_UPGRADE_RESULT = 47; // agent上报驱动升级结果
  CMD_SET_DRIVER_STATUS = 48; // 驱动开关 server->agent
  CMD_REPORT_DRIVER_STATUS = 49; // 驱动状态上报 agent->server->agent
  CMD_AGENT_UNINSTALL = 51; // agent卸载命令
  CMD_AGENT_UNINSTALL_RESULT = 52; // agent卸载结果响应
  CMD_HASH_ENGINE_VERSION = 53; // agent上传特征库版本
  CMD_HASH_ENGINE_UPGRADE_RESULT = 54; // agent上报特征库升级结果
  CMD_WEB_SHELL_SCAN_RESULT = 56; // webshell扫描结果
  CMD_WEB_SHELL_SCAN_UPLOAD = 57; // 文件上传
  CMD_MEM_PROTECT_VIRUS_FILE_INFO = 58; // webshell风险上报
  CMD_WEB_SHELL_REVERSE = 59; // webshell预留
  CMD_RASP_DATA_REPORT = 60; // rasp内存马数据上报
  CMD_RASP_INJECTION_REQUEST = 61; // rasp内存马注入命令请求
  CMD_RASP_INJECTION_RESPONSE = 62; // rasp内存马注入命令回复
  CMD_RASP_UNINSTALL_REQUEST = 63; // rasp内存马卸载命令请求
  CMD_RASP_UNINSTALL_RESPONSE = 64; // rasp内存马卸载命令回复
  CMD_AGENT_INFO_REPORT_NOTIFY = 66; // agent信息上报通知
  CMD_ANTI_VIRUS_POLICY = 67; // 杀毒扫描策略
  CMD_ANTI_VIRUS_RESULT = 68; // 杀毒扫描结果
  CMD_ANTI_VIRUS_UPLOAD = 69; // 杀毒文件上传
  CMD_SHA256_ENGINE_VERSION = 70; // agent上传基线库版本
  CMD_SHA256_ENGINE_UPGRADE_RESULT = 71; // agent上报基线库升级结果
  CMD_FILE_HANDLING_DELETE_FILES = 72; // 通知agent删除威胁文件
  CMD_FILES_REPORT_PROGRESS = 73; // 文件采集进度 agent->server
  CMD_POLICY_NEW_COMMON = 100; // 策略
  CMD_AGENT_ENGINE_LIB_UPGRADE_REQUEST = 101; // V01 agent引擎库统一升级请求
  CMD_AGENT_ENGINE_LIB_UPGRADE_RESULT = 102; // V01 agent引擎库统一升级结果
  CMD_FILE_UPLOAD_NOTIFY = 201; // 文件上传通知 server -> agent
  CMD_NOTIFY_UPLOAD_EVIDENCE_FILE = 202; // 通知上传证据文件 server->agent
  CMD_REPORT_OBTAIN_EVIDENCE_RESULT = 203; // 报告取证结果 agent->server
  CMD_UPGRADE_NOTIFY_RESPONSE = 204; // 升级通知应答 agent -> server
  CMD_DELETE_AGENT_FILES_HANDLING = 205; // Agent 文件隔离, 删除, 恢复
  CMD_AGENT_REPORT_OUTREACH = 206; // Agent 上报外连
  CMD_WARNING_REPORT = 207; // Agent 异常警告信息上报
  CMD_COLLECT_SYS_LOGS = 209; // 收集 Agent 运行日志 (dump, 运行日志等)
  CMD_ACK = 1000; // 通用应答
  CMD_PULL = 1001; // 拉取消息数据
}
