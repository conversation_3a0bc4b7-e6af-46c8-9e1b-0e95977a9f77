syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
//
//  文件风险 识别结果
//  对应 g_CmdMemProtectRiskFileInfo
//--------------------------------------------------
message MemProtectRiskFileInfo {
        ClientID baseInfo                                   = 1;
        repeated VirusCheckInfo    virusScannerList         = 2;
        repeated FileDllHijack     fileDllHijackList        = 3;
        repeated FileEnvHijack     fileEnvHijackList        = 4;
        repeated PEWithoutSignature  peWithoutSignatureList = 5;
        repeated PEPackerName        pePackerNameList       = 6;
        repeated FileSensitivity  fileSensitivityList       = 7;
        repeated LnkFile          lnkFileList               = 8;
        repeated ClearLog         clearLogList              = 9;
}

//没有签名的PE文件
message PEWithoutSignature {
        RiskHeader  header          = 1;
        bytes   filePath            = 2;  //文件路径
        bytes   sha256              = 3;
}

//PE加壳
message PEPackerName {
        RiskHeader  header          = 1;
        bytes   filePath            = 2;  //文件路径
        bytes   packerName          = 3;  //壳名
        bytes   sha256              = 4;  // sha256
}

//病毒扫描
message VirusCheckInfo {
        RiskHeader  header          = 1;
        bytes   filePath            = 2;  //文件路径
        bytes   virusName           = 3;  //病毒名
        bytes   sha256              = 4;
}

// DLL劫持： 进程加载的系统dll，如果不在系统路径下(system32)，认为是DLL劫持
message funName {
        bytes   name    = 1;
}
message FileDllHijack {
        RiskHeader       header       = 1;
        ProcessInfo      Process      = 2;
        bytes            DllName      = 3;  // 劫持的文件名
        bytes            DllPath      = 4;  // 全路径
        bytes            Sha256       = 5;  // sha256
        repeated funName funcNameList = 6;  // 劫持的可疑的系统函数
}

// 环境变量劫持，思路如果path里的第三方路径有系统dll文件，认为是环境变量劫持
message FileEnvHijack {
        RiskHeader  header       = 1;
        bytes       FullValue    = 2;  // Path环境变量的完整值
        bytes       RiskValue    = 3;  // 有风险的是哪个路径
        bytes       Feature      = 4;  // 判断为风险路径的特征文件名，比如lpk.dll
        bytes       Sha256       = 5;  // 特征文件的sha256，特征文件可能不只一个，如何处理？(分成多条来处理)
}

//需要与客户端策略类型值保持一致！！！
enum FileOperation {
        fgo_unset = 0;
        fgo_create = 0x10000;
        fgo_delete = 0x10003;
        fgo_write = 0x10006;
}

//敏感文件操作
message FileSensitivity {
        RiskHeader      header                 = 1;
        ProcessInfo     Process                = 2;
        bytes           fileName               = 3;  // 操作的文件名
        bytes           filePath               = 4;  // 操作的文件路径
        FileOperation   fileOp                 = 5;  // 文件操作类型
        bytes           Sha256                 = 6;  // 特征文件的sha256，特征文件可能不只一个，如何处理？(分成多条来处理)
}

message LnkFile {
        RiskHeader      header                 = 1;
        bytes           fileName               = 2;  // 文件名
        bytes           filePath               = 3;  // 件路径
        bytes           CVE_ID                 = 4;  // CVE编号
        bytes           Sha256                 = 5;  // 文件的sha256
}

//清空日志操作
message ClearLog {
        RiskHeader      header                 = 1;
        ProcessInfo     Process                = 2;  //清空日志的进程信息
        string          LogName                = 3;  //被清空的日志名称
}

//文件处置-删除文件请求
message FileHandlingDeleteFilesRequest {
        string   sha256                          = 1;
        repeated DeleteFilesInfo DeleteFilesList = 2;
        DeleteFileStage stage                    = 3; // 删除阶段: 隔离; 删除; 恢复
        DeleteFileSource source                  = 4; // 文件處置命令來源: 動態檢測, 靜態檢測, ...
}

//文件处置-删除文件结果上报
message FileHandlingDeleteFilesResponse {
        string   sha256                          = 1;
        repeated DeleteFilesResult resultList    = 2;
        DeleteFileStage stage                    = 3; // 删除阶段: 隔离; 删除; 恢复
        DeleteFileSource source                  = 4; // 文件處置命令來源: 動態檢測, 靜態檢測, ...
}

message DeleteFilesInfo {
        string filePath                          = 1;
        string macPathKey                        = 2;
}

message DeleteFilesResult {
        string           macPathKey              = 1;
        DeleteResultType result                  = 2;
        string           filePath                = 3; // 根据sha256删除时, 上报文件路径
}

enum DeleteResultType {
        DELETE_DEFAULT                          = 0; //默认值，无意义
        DELETE_SUCCESS                          = 1; //删除成功
        DELETE_FILE_NOT_EXIST                   = 2; //文件不存在
        DELETE_ACCESS_DENIED                    = 3; //无法访问
        DELETE_FILE_OCCUPIED                    = 4; //文件被占用
        DELETE_FILE_MISMATCH                    = 5; //文件不匹配
        DELETE_UNKNOWN_ERROR                    = 6; //未知错误
        QUARANT_BAD_DB                          = 7;  // 隔离区数据损坏
        QUARANT_FILE_NOT_IN_DB                  = 8;  // 恢复文件不在隔离区
        QUARANT_FILE_ENC_DEC_FAILED             = 9;  // 加密或解密文件失败
        QUARANT_FILE_ALREADY_EXIST              = 10; // 恢复时文件已存在
        QUARANT_FILE_NOT_IN_QUARANT             = 11; // 恢复的文件不在隔离区
        QUARANT_DISK_NOT_ENOUGH                 = 12; // 隔离区磁盘空间不足
        QUARANT_DB_NOT_ENOUGH                   = 13; // 隔离区数据条目超过10000
        QUARANT_TARGET_DIR_NOT_EXIST            = 14; // 文件恢复路径不存在
        QUARANT_IS_FULL                         = 15; // 隔離區大小超過限制
        QUARANT_FILE_SIZE_EXCEED                = 16; // 單個文件大小超過限制
}

enum DeleteFileStage {
        STAGE_UNKNOWN                               = 0; // 无意义
        STAGE_ISOLATE                               = 1; // 隔离文件
        STAGE_DELETE                                = 2; // 永久删除
        STAGE_RESTORE                               = 3; // 恢复文件
        STAGE_EXPIRED                               = 4; // 隔离区自动清理
}

enum DeleteFileSource {
    SOURCE_UNKNOWN              = 0; // 未知
    SOURCE_DYNAMIC              = 1; // 動態檢測
    SOURCE_STATIC_WEBSHELL      = 2; // WebShell 靜態檢測
    SOURCE_STATIC_ANTIVIRUS     = 3; // 殺毒靜態檢測
}
