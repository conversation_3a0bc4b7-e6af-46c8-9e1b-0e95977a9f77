syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

// 文件上传类型
enum FileUploadType {
  UPLOAD_TYPE_UNKNOWN = 0;
  UPLOAD_TYPE_ILLEGAL_OUTREACH = 1; // 非法外联请求
  UPLOAD_TYPE_SNAPSHOT = 2; // 文件快照采集
}

// agent -> server
message FileUploadReq {
  FileUploadType upload_type = 1;  // 文件上传类型
  string identifier = 2;           // 标识符
}

// server -> agent
message FileUploadResp {
  FileUploadType upload_type = 1;  // 证据文件类型
  string identifier = 2;           // 标识符
  string url = 3;
}