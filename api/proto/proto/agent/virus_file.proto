syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
// 
//  文件风险 识别结果
//  对应 g_CmdMemProtectvirusFileInfocd 
//--------------------------------------------------
message MemProtectVirusFileInfo {
        ClientID baseInfo                                   = 1;
        repeated VirusSendInfo    virus_list                 = 2;
}

message ReportSLInfo {
        string rule_name     = 1; // 规则名称
        string match_details = 2; // 详细信息
}

//病毒扫描 目前只有webshell 静态扫描结果上报
message VirusSendInfo {
        RiskHeader  header           = 1;
        string   filepath            = 2;  // 文件路径
        string   sha256              = 3;  // 文件sha256
        int32    risk_type           = 4;  // 风险类型 3:办公软件钓鱼 4:邮件钓鱼
        string   virus_name          = 5;  // v01不需要
        string   md5                 = 6;  // 文件md5
        int64    atime               = 7;  /* 访问文件时间 */
        int64    mtime               = 8;  /* 文件修改时间 */
        int64    ctime               = 9;  /* 文件创建时间 */
        string   st_mode             = 10; /* 文件权限 */
        uint32   file_size           = 11; /* 文件大小，单位字节*/
        uint32   detect_source       = 12; /* 1 是agent告警检测到图片后门，2 是server检测结果上报*/ // v01不需要
        uint32   detect_kind         = 13; /* 1.webshell  2.virus*/ // v01不需要
        string   malware_type        = 14; // v01不需要

        uint64   access_time         = 30; /* 访问文件时间 */
        uint64   modify_time         = 31; /* 文件修改时间 */
        uint64   create_time         = 32; /* 文件创建时间 */
        string   filename            = 33; // 文件名
        string   sha1                = 34; // 文件sha1
        string   file_version		     = 35; // 文件版本
        bytes    file_vendor	       = 36; // 文件厂商c
        repeated SignatureInfo signatureInfo = 37; // 进程文件签名信息
        FileTypeIdent file_type      = 38; // 文件类型(php ,asp,jsp,...)
        ReportMaliciousInfo report_malicious_info = 39; // 病毒扫描结果上报信息
        ReportSLInfo report_sl_info  = 40; // 扫雷结果上报信息
        string sl_detail             = 41; // 扫雷详细信息
        string file_source_list      = 42;
        SourceProcessType file_source_software = 43; // 文件来源软件
        int32 file_score             = 44; // 文件分数用于计算置信度,分数越大越可疑
}
message VirusRecvInfo {
        string   sha256              = 1;
}

// 风险类型
enum VirusType {
        DETECT_UNKNOWN          = 0;
        DETECT_WSS_FILE         = 1;   // 弃用(终端弃用)
        DETECT_ANTI_FILE        = 2;   // 黑hash
        DETECT_OFFICE_FISHING   = 3;   // 办公软件钓鱼
        DETECT_EMAIL_FISHING    = 4;   // 邮件钓鱼
        DETECT_SIGN_BLACK_LIB   = 5;   // 弃用
        DETECT_DYN_LIB_HACK     = 6;   // 弃用,dll劫持
        DETECT_SL               = 7;   // 0415弃用
        DETECT_BOT_NET          = 8;   // 僵尸网络
        DETECT_TROJAN           = 9;   // 木马
        DETECT_SPYWARE          = 10;  // 间谍软件
        DETECT_REMOTE_CONTROL   = 11;  // 远控
        DETECT_BACKDOOR         = 12;  // 后门软件
        DETECT_DATA_INTERCEPTION= 13;  // 数据窃取
        DETECT_EXP_VULN         = 14;  // 漏洞利用
        DETECT_EXTORTION        = 15;  // 勒索
        DETECT_MINING_VIRUS     = 16;  // 挖矿病毒
        DETECT_WORM             = 17;  // 蠕虫
        DETECT_APT              = 18;  // APT
        DETECT_VIRUS            = 19;  // 病毒
        DETECT_HACK_TOOL        = 20;  // 黑客工具
        DETECT_USB_PHISHING     = 21;  // USB钓鱼
}

enum MainType{
        MT_UNKNOWN      = 0;
        MT_LNK          = 1;
        MT_URL          = 2;
        MT_MACRO        = 3;
        MT_DDE          = 4;
        MT_OLE          = 5;
        MT_HTA          = 6;
        MT_CHM          = 7;
        MT_RLO          = 8;
        MT_RAR          = 9;
        MT_FILE_PATH    = 10;
        MT_PE           = 11;
        MT_VBE          = 12;
        MT_EML          = 13;
        MT_FILE_BINDLE  = 14;
}

enum SubType{
        SUBT_UNKNOWN = 0;

        // MT_LNK
        SUBT_LIK_BIG_SIZE         = 1;
        SUBT_ICON_REPLACE         = 2;
        SUBT_CMD_LINE_EXCEP       = 3;

        // MT_FILE_PATH
        SUBT_DOUBLE_SUFFIX        = 10;
        SUBT_TOO_LONG_PATH        = 11;
        SUBT_TEMPTATION_CONTENT   = 12;
}

message DdeMessage{
        repeated string dde_code = 1; // dde code
}

message VbaMessage{
        repeated string vba_code = 1; // vba code
}

message LnkMessage{
        string target_path = 1; // lnk目标路径
        string working_dir = 2; // 工作目录
        string cmd_line    = 3; // 命令行参数
        string icon_path   = 4; // 图标路径
}

message UrlMessage{
        string url = 1; // url地址
}

message ReportMaliciousInfo{
        repeated MainType main_type = 1; // 主类型
        repeated SubType sub_type   = 2; // 子类型
        DdeMessage dde_info         = 3; // 恶意dde信息
        VbaMessage vba_info         = 4; // 恶意vba信息
        LnkMessage lnk_info         = 5; // 恶意lnk信息
        UrlMessage url_info         = 6; // 恶意url信息
}

enum SourceProcessType{
        SPT_UNKNOWN              = 0;
        SPT_WEIXIN               = 1;  // 微信
        SPT_WXWORK               = 2;  // 企业微信
        SPT_DINGDING             = 3;  // 钉钉
        SPT_WELINK               = 4;  // 华为WeLink
        SPT_FOXMIAL              = 5;  // Foxmail
        SPT_MAILMASTER           = 6;  // 网易邮件大师
        SPT_OUTLOOK              = 7;  // Outlook
        SPT_CHANGYOU             = 8;  // 畅邮
        SPT_COREMAIL             = 9;  // CoreMail
        SPT_QWMAIL               = 10; // 企微邮箱
        SPT_BOSS                 = 11; // Boss直聘
        SPT_FEISHU               = 12; // 飞书
        SPT_LANXIN               = 13; // 蓝信
        SPT_UDISK                = 14; // U盘
        SPT_WEMAIL               = 15; // WeMail
}