syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
// 
//  行为风险识别结果
//  对应 g_CmdMemProtectRiskActInfo
//--------------------------------------------------
message MemProtectRiskActInfo {
        ClientID baseInfo                                    = 1;
        repeated NewAdminAccout     newAdminAccountList      = 2;
        repeated FireWallSwitch     fireWallSwitchList       = 3;
        repeated IPFireWallOFF      ipFireWallOffList        = 4;
        repeated SELinuxOFF         SELinuxOffList           = 5;
        repeated LoadDriver         loadDriverList           = 6;
        repeated RegOperateValueStartup   regSetValueStartupList   = 7;
        repeated RegOperateValueStartup   regDelStartupList        = 8;
        repeated RegOperateValueStartup   regCreateValueStartupList     = 9;
}

// 账户提权到管理员
message NewAdminAccout
{
        RiskHeader  Header          = 1;
        bytes       UserName        = 2;
}

// 防火墙开关 (win)
message FireWallSwitch
{
        RiskHeader  Header          = 1;
        uint32      Private         = 2;
        uint32      PublicOrGuest   = 3;
}

// IP防火墙被关闭 (linux)
message IPFireWallOFF
{
        RiskHeader  Header          = 1;
        ProcessInfo Process         = 2; // 执行关闭防火墙进程信息
        bytes       Comment         = 3; // 关闭防火墙命令描述
}

// SELINUX被关闭 (linux)
message SELinuxOFF
{
        RiskHeader  Header          = 1;
        ProcessInfo Process         = 2; // 执行关闭SELINUX进程信息
        bytes       Comment         = 3; // 关闭SELINUX命令描述
}

//设置注册表启动项
message RegOperateValueStartup {
        RiskHeader  Header          = 1;
        uint32  processID           = 2;  //进程Id
        bytes   ring3KeyPath        = 3;  //注册表路径
        bytes   regValueName        = 4;  //注册表值名
        uint32  regValueType        = 5;  //值类型
        bytes   regBinaryValue      = 6;  //值
}


// 驱动加载
message LoadDriver
{
        RiskHeader  Header          = 1;
        ProcessInfo SourceProcess   = 2;
        bytes       DriverPath      = 3;  // 驱动路径
        bytes       DriverSha256    = 4;  // 驱动文件的sha256
        bytes       Company         = 5; // 公司
        bytes       SignCompany     = 6; // 签名者信息
}