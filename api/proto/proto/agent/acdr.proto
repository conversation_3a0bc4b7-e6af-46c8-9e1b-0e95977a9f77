syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

enum EnumResourceType {
    RLIMIT_CPU = 0;
    RLIMIT_FSIZE = 1;
    RLIMIT_DATA = 2;
    RLIMIT_STACK = 3;
    RLIMIT_CORE = 4;
    RLIMIT_RSS = 5;
    RLIMIT_NOFILE = 7;
    RLIMIT_AS = 9;
    RLIMIT_NPROC = 6;
    RLIMIT_MEMLOCK = 8;
    RLIMIT_LOCKS = 10;
    RLIMIT_SIGPENDING = 11;
    RLIMIT_MSGQUEUE = 12;
    RLIMIT_NICE = 13;
    RLIMIT_RTPRIO = 14;
    RLIMIT_RTTIME = 15;
    RLIMIT_NLIMITS = 16;
}

message RuleInfoMessage {
    ClientID date_time                   = 1; //事件发生的大致时间
    RiskHeader header                    = 2; //风险头
    bytes rule_name                                 = 3; //规则名
    uint32 risk_level                               = 4; //风险等级
    HandleMode handle_mode                          = 5; //控制模式（结束进程、拦截、仅上报等）
    uint32 score                                    = 6; //分数
    repeated RuleActionItem rule_action_list        = 7; //规则行为数组
    repeated NgavProcessInfo process_info_list      = 8; //进程信息数组，仅第一层数组有数据，嵌套结构无该信息。
    bool not_show_process_info = 9; //是否展示进程信息，但进程之间的关系依然存在RuleActionItem中
}

enum HandleMode {
    HM_NOT_SET                      = 0;  //不设置 控制模式
    HM_TERMINATE_PROCESS            = 1;  //结束进程
    HM_TERMINATE_THREAD             = 2;  //结束线程
    HM_RESTORE_EFFECT               = 4;  //恢复行为造成的影响
    HM_RESTORE_ALL_EFFECT           = 8;  //恢复所有行为造成的影响
    HM_REFUSE_REQUEST               = 16; //拦截请求
    HM_ONLY_MONITOR                 = 32; //仅上报
    HM_NO_EFFECT_EXTERNAL_SWITCH    = 64; //不受外部开关影响
}

enum ActionType {
    AT_DEFAULT               = 0;
    AT_UNKNOWN               = 1001; //未知行为
    AT_FILE_CREATE           = 1002; //文件创建
    AT_FILE_OPEN             = 1003; //文件打开
    AT_FILE_WRITE            = 1004; //文件写
    AT_FILE_RENAME           = 1005; //文件重命名
    AT_FILE_DELETE           = 1006; //文件删除
    AT_FILE_LINK             = 1007; //创建文件连接
    AT_FILE_IOCTL_IMMUTABLE  = 1008; //通过ioctl操作文件
    AT_FILE_RESTORE_UTIME    = 1009; //恢复文件时间属性
    AT_MAKEDIR               = 1010; //创建目录
    AT_FILE_SYMLINK          = 1011; //创建符号连接
    AT_FILE_SET_UID          = 1012; //修改文件uid
    AT_NET_CONNECT           = 1100; //连接网络
    AT_NET_ACCEPT            = 1101; //X
    AT_NET_LISTEN            = 1102; //X
    AT_NET_SEND              = 1103; //X
    AT_NET_RECV              = 1104; //X
    AT_PROCESS_CREATE        = 1105; //X
    AT_THREAD_CREATE         = 1106; //X
    AT_LOAD_IMAGE            = 1107; //X
    AT_PROCESS_INFO          = 1108; //X
    AT_FILELESS_ATTACK       = 1109; //无文件攻击
    AT_NET_SNIFFER           = 1110; //网络抓包行为
    AT_ENV_HIJACK            = 1111; //环境变量劫持
    AT_SELF_DELETE           = 1112; //自删除

    AT_CALL_USERMODEHELPER   = 1114; //内核执行用户态进程。
    AT_REVERSE_SHELL         = 1115; //反弹shell
    AT_PROCESS_EXEC          = 1116; //exec
    AT_PTRACE                = 1117; //读写进程内存
    AT_BPF                   = 1118; //bpf技术
    AT_PRIVILEGE_ESCALATION  = 1119; //进程提权
    AT_FAKE_EXE_FILE         = 1120; //伪装进程执行路径
    AT_HIDE_MODULE           = 1121; //隐藏模块
    AT_KILL                  = 1122; //kill信号
}

message NgavProcessInfo {
    uint32 pid                   = 1;  //进程id
    uint32 ppid                  = 2;  //父进程id
    uint64 start_time            = 3;  //进程启动时间
    uint64 exit_time             = 4;  //进程退出时间
    bytes process_path           = 6;  //进程路径
    bytes command                = 7;  //命名行
    bool is_exist                = 8;  //进程是否存在
    bytes file_md5               = 9;  //文件MD5
    bytes user                   = 10; //进程所属用户
    bytes file_access_permission = 11; //文件访问权限
    uint32 file_size             = 12; //文件大小
    bytes file_mtime             = 13; //文件最后一次修改时间
    bytes file_ctime             = 14; //文件最后一次属性改变时间
    bytes file_atime             = 15; //文件最后一次访问时间
    bool is_killed               = 16; //进程是否被杀死
    uint64 ppid_start_time       = 17; //父进程的启动时间
}

message FileCreateContentInfo {
    bytes file_path    = 1; //文件创建路径
    bool is_exist      = 2; //文件是否存在
}

message FileOpenContentInfo {
    bytes file_path   = 1; //文件打开路径
    bool is_exist     = 2; //文件是否存在
}

message FileWriteContentInfo {
    bytes file_path   = 1; //写文件路径
    bool is_exist     = 2; //文件是否存在
}

message FileRenameContentInfo {
    bytes file_path_src    = 1; //文件重命名原路径
    bytes file_path_dest   = 2; //文件重命名目标路径
    bool is_src_exist      = 3; //原文件是否存在
    bool is_dest_exist     = 4; //目标文件是否存在
}

message FileDeleteContentInfo {
    bytes file_path     = 1; //文件删除路径
    bool is_exist       = 2; //文件是否存在
}

message FileModeChangeContentInfo {
    NodeFileInfo file_info = 1;
    bytes change_info      = 2; //改变信息
}

message FileIoctlImmutableContentInfo {
    bytes file_path     = 1; //文件路径
    bool is_exist       = 2; //文件是否存在
}

message Mkdir {
    bytes file_path     = 1; //目录路径
    bool is_exist       = 2; //文件是否存在
}

message FileSymlink {
    NodeFileInfo file_info_src  = 1;
    NodeFileInfo file_info_dest = 2;
}

message FileSetUid {
    bytes file_path          = 1; //文件路径
}


enum NetProto {
    NP_DEFAULT                = 0;
    NF_IPPROTO_TCP            = 6;
    NF_IPPROTO_UDP            = 17;
}

enum NetFamilies {
    NF_DEFAULT                = 0;
    NF_AF_INET                = 2;  //ipv4
    NF_AF_INET6               = 10; //ipv6
}

message NetContentBaseInfo {
    bytes local_ip         = 1;
    uint32 local_port      = 2;
    bytes remote_ip        = 3;
    uint32 remote_port     = 4;
    NetFamilies address_family  = 5;
    NetProto protocol        = 6;
}

message NetConnectContentInfo {
    NetContentBaseInfo net_content_base_info = 1;
    bool is_network_connected                = 2; //网络是否连接
}

message NetAcceptContentInfo {
    NetContentBaseInfo net_content_base_info = 1;
    bool is_network_connected                = 2; //网络是否连接
}

message NetListenContentInfo {
    NetContentBaseInfo net_content_base_info = 1;
    bool is_network_connected                = 2; //网络是否连接
}

message NetSendContentInfo {
    NetContentBaseInfo net_content_base_info = 1;
    bool direction_in                        = 2;
    bool is_network_connected                = 3; //网络是否连接
}

message NetRecvContentInfo {
    NetContentBaseInfo net_content_base_info = 1;
    bool direction_in                        = 2;
    bool is_network_connected                = 3; //网络是否连接
}

message FilelessAttackInfo {
    bytes file_path    = 1;
    bool is_exist      = 2; //文件是否存在
}

message NetSnifferInfo {
    bytes devname = 1;
}

message EnvHijackInfo {
    bytes env_name = 1;
}

message SelfDeleteInfo {
    bytes file_path = 1;
    bool is_exist   = 2; //文件是否存在
}

message PtraceInfo {
    uint32 tracer_pid        = 1;
    uint64 tracer_start_time = 2;
    uint32 tracee_pid        = 3;
    uint64 tracee_start_time = 4;
    bytes tracee_path        = 5;
    uint32 mode              = 6;
    uint64 addr              = 7;
}

message FileLinkInfo {
    bytes file_path_src   = 1;
    bytes file_path_dest  = 2;
    bool is_src_exist     = 3; //原文件是否存在
    bool is_dest_exist    = 4; //目标文件是否存在
}

message CallUsermodehelperInfo {
    bytes modname = 1;
}

message ReverseShellInfo{
    uint32 in_pid                 = 1;
    bytes  in_ip                  = 2;//本地IP
    uint32 in_port                = 3;
    NetFamilies in_addr_family    = 4;
    NetProto in_proto             = 5;
    uint64 in_start_time          = 6;

    uint32 out_pid                = 7;
    bytes  out_ip                 = 8;//远端ip
    uint32 out_port               = 9;
    NetFamilies out_addr_family   = 10;
    NetProto out_proto            = 11;
    uint64 out_start_time         = 12;
    bool is_network_connected     = 13; //网络是否连接
}


message ProcessExecInfo {
    bytes exepath  = 1;
}

message PrivilegeEscalation {
    bytes privilege_path  = 1; //被提升进程的路径
    bytes privilege_cmdline  = 2; //被提升进程的命令行
}

message RestoreUtime {
    bytes file_path  = 1; //被恢复时间的文件名
    bool is_exist    = 2; //文件是否存在
}

message Bpf {
    bytes type_name  = 1; //hook类型名
}

message FakeExeFileInfo {
    uint32 fake_pid    = 1; //伪装进程pid
    uint64 start_time  = 2; //进程启动时间
    bytes fake_exepath = 3; //伪装后的路径
    bytes exepath      = 4; //真实路径
    bool is_exe_exist  = 5; //真实路径文件是否存在
}

message HideModuleInfo {
    bytes modname = 1; //模块名
}

message KillInfo {
    uint32 target_pid   = 1; //信号发送的目标进程
    uint64 start_time   = 2; //目标进程启动时间
    uint32 signal       = 3; //信号
    bytes target_path   = 4; //信号发送的目标进程的路径
    bool is_exist       = 5; //文件是否存在
}

message RuleActionItem {
    ActionType action_type  = 1; //行为类型
    uint64 trigger_time     = 2; //触发该行为的时间
    uint32 pid              = 3; //进程pid
    bool is_rule            = 4; //是否是规则，为true时，只有oneof字段有效，oneof指向rule_msg。为false时oneof类型和action_type对应，所有字段均有效。
    uint64 start_time       = 5; //进程的启动时间
    bool is_refused         = 6; //该行为是否被拦截
    oneof ActionContent {
        RuleInfoMessage rule_msg                           = 9;
        FileCreateContentInfo file_create_content          = 10;
        FileOpenContentInfo file_open_content              = 11;
        FileWriteContentInfo file_write_content            = 12;
        FileRenameContentInfo file_rename_content          = 13;
        FileDeleteContentInfo file_delete_content          = 14;
        NetConnectContentInfo net_connect_content          = 15;
        NetAcceptContentInfo net_accept_content            = 16;
        NetListenContentInfo net_listen_content            = 17;
        NetSendContentInfo net_send_content                = 18;
        NetRecvContentInfo net_recv_content                = 19;
        FilelessAttackInfo fileless_attack_content         = 20;
        NetSnifferInfo net_sniffer_content                 = 21;
        EnvHijackInfo env_hijack_content                   = 22;
        SelfDeleteInfo self_delete_content                 = 23;
        PtraceInfo ptrace_content                          = 24;
        FileLinkInfo file_link_content                     = 25;
        CallUsermodehelperInfo call_usermodehelper_content = 26;
        ReverseShellInfo reverse_shell_content             = 27;
        ProcessExecInfo process_exec_content               = 28;
        RestoreUtime restore_utime_content                 = 29;
        FileIoctlImmutableContentInfo  file_ioctl_immutable_content = 30;
        Mkdir mkdir_content                                = 31;
        FileSymlink file_symlink                           = 32;
        FileSetUid file_set_uid                            = 33;
        Bpf  bpf                                           = 34;
        PrivilegeEscalation privilege_escalation           = 35;
        FakeExeFileInfo fake_exe_file_content              = 36;
        HideModuleInfo hide_module_content                 = 37;
        KillInfo kill_content                              = 38;
    }
}


enum StatusType {
    ST_UNKNOWN   = 0;    //未知状态
    ST_EXIST     = 1;    //存在
    ST_NONEXIST  = 2;    //不存在
    ST_QUERYFAILED = 3;  //查询失败
}

message StatusProcessInfo {
    uint32 pid        = 1;
    uint64 start_time = 2;
}

message StatusFileInfo {
    bytes file_path = 1;
}

message StatusNetInfo {
    NetContentBaseInfo net_content_base_info = 1;
}

message RiskStatus {
    oneof StatusContext {
        StatusProcessInfo proc_info  = 1;
        StatusNetInfo net_info       = 2;
        StatusFileInfo file_info     = 3;
    }
}

message RiskStatusListReq {
    uint64 id                            = 1;
    repeated RiskStatus risk_status_list = 2;
}

message RiskStatusListResp {
    uint64 id                       = 1;
    repeated StatusType status_type = 2; //状态类型
}

// V01 ACDR

// IP 节点
message NodeAddrInfo {
    uint32 port       = 1;
    bytes  ip         = 2;
    NetFamilies address_family = 3;
    NetProto protocol = 4;
}

message ConnectionInfo {
    NodeAddrInfo remote = 1;
    NodeAddrInfo local  = 2;
}

message DomainConnectionInfo {
    NodeRemoteUrl remote = 1;
    NodeAddrInfo  local  = 2;
}

// 文件 节点
message NodeFileInfo {
    string file_path             = 1; // 文件创建路径
    bytes file_sha256            = 2; // 文件sha256
    uint32 file_size             = 3; // 文件大小
    uint64 file_mtime            = 4; // 文件最后一次修改时间
    uint64 file_ctime            = 5; // 文件最后一次属性改变时间
    uint64 file_atime            = 6; // 文件最后一次访问时间
    bytes file_access_permission = 7; // 文件访问权限
    bytes file_md5               = 8; // 文件md5
    ProcFileDetailInfo detail_info       = 9;// 详细信息
    repeated SignatureInfo signatureInfo = 10; // 进程文件签名信息
}

// URL 节点
message NodeRemoteUrl {
    string url = 1;
}

message AcdrProcessUnique {
    uint32 pid        = 1;  // 进程id
    uint64 start_time = 2;  // 进程启动时间
}

message AcdrProcessSimple {
    uint32 pid       = 1; // 进程id
    int64 start_time = 2; // 进程启动时间，单位：linux毫秒
    bytes user_name  = 3; // 进程执行用户
    bytes command    = 4; // 命令行
    FileBaseInfo process_file_info = 5; // 进程文件信息
    uint64 euid      = 6; // 进程执行用户 euid
}

message FileBaseInfo {
    bytes md5        = 1; // md5
    bytes sha256     = 2; // sha256
    bytes file_name  = 3; // 文件名
    bytes file_path  = 4; // 文件路径
    int64 file_size  = 5; // 文件大小
    int64 file_atime = 6; // 最后一次访问时间，单位：linux毫秒
    int64 file_mtime = 7; // 最后一次修改时间，单位：linux毫秒
    int64 file_ctime = 8; // 创建时间，单位：linux毫秒
}

// 进程 节点
message NodeProcessInfo {
    AcdrProcessUnique unique_child = 1; // 子进程标识
    bool  is_root_process          = 2; // 是否是根进程
    string command                 = 3; // 命令行
    NodeFileInfo process_file_info = 4; // 进程对应文件信息
    string user_name               = 5; // 进程执行用户
    uint64 euid                    = 6; // 进程执行用户 euid
    AcdrProcessUnique exec_src     = 7; // exec 的源进程, 非 exec 则置零
}

// 父子进程关系更新
message AcdrProcessParrentRelationUpdate {
    bool hard_relation = 1; //true表示强关系，false表示弱干关系
    AcdrProcessUnique process_parrent = 2;
    AcdrProcessUnique process_chiled = 3;
}

message FileRename {
    NodeFileInfo from = 1;
    NodeFileInfo to   = 2;
}

message RegCreate {
    string reg_path = 1;
}

message RegDelete {
    string reg_path = 1;
}

message RegWrite {
    string reg_path    = 1;
    string write_value = 2;
}

message RegSetSecurity {
    string reg_path = 1;
}

message ScriptHttp {
    string url =1;
    bool is_get = 2; //是否是GET请求
}

message ScriptImageLoad {
    string image_path = 1;
}

message ScriptRunWmicCode {
    string cmd =1;
    bool is_remote = 2; //是否是GET请求
}

message ScriptScheduleCreate {
    string path =1;
    string args =2;
}

message ScriptGetApiAddr {
    string api_name =1;
}

message ScriptWmicWin32Share {
    string share_name = 1;
    string share_path = 2;
}

message ScriptWmicTerminateProcess {
    NodeProcessInfo proc = 1;
}

message ScriptWmicRegOper {
    string oper_type = 1;
    string key_path = 2;
    string value_path = 3;
}

message ScriptWmicServiceOper {
    string oper_type = 1;
    string service_name = 2;
    string service_exe_path = 3;
}

message ScriptWmicQuery {
    string query_language = 1;
    string query_cmd = 2; // 是否是GET请求
}

message ScriptAmsiByAmsiContext {
    string value = 1;
}

message ScriptAmsiDllHijack {
    NodeFileInfo path = 1;
}

message ScriptEmail {
    string sender_addr = 1;
    string recv_addr = 2;
}

message LogClear {
    string log_file = 1;
}

message CveAttack {
    string cve_name = 1;
    string attack_source= 2; //攻击源，可能是ip或者http:\\127.0.0.1\1.exe
}

message RemoteBugOverflow {
    string cve_name = 1; // 漏洞CVE名称
    NodeAddrInfo node = 2; // 攻击端信息
}

message PuppetProcessAttack{
    uint64 base_addr = 1 ; // 傀儡基地址
}

message V01PtraceInfo {
    NodeProcessInfo proc_info = 1;
    uint32 mode               = 2; // 注入模式: 0: 读; 1: 写
    uint64 addr               = 3;
}

message V01KillInfo {
    NodeProcessInfo proc_info = 1;
    uint32 signal             = 2; //信号
}

message SetRlimit {
    EnumResourceType resource  = 1; //资源类型
}

enum ProtectType {
    PT_PAGE_UNDEFINED         = 0;
    PT_PAGE_NOACCESS          = 1; // 无法访问
    PT_PAGE_READONLY          = 2; // 只读
    PT_PAGE_READWRITE         = 3; // 读写
    PT_PAGE_EXECUTE           = 4; // 执行
    PT_PAGE_EXECUTE_READ      = 5; // 执行和读取
    PT_PAGE_EXECUTE_READWRITE = 6; // 执行, 读取和写入
    PT_PAGE_EXECUTE_WRITECOPY = 7; // 执行和写入, 写时复制
}

enum AllocationType {
    AT_MEM_UNDEFINED = 0;
    AT_MEM_COMMIT    = 1; // 分配内存并将内容初始化为零
    AT_MEM_RESERVE   = 2; // 仅为内存保留地址空间, 不实际分配物理内存
    AT_MEM_RESET     = 3; // 重置已分配的页面
}

enum WriteMemRiskMode {
    WMRM_DEFAULT = 0; // 在默认位置写
    WMRM_PEB     = 1; // 在 Peb 位置写
}

message MemActionOtherInfo {
    uint64 ret_addr           = 1; // 返回指令所在地址
    string ret_module_name    = 2; // 返回指令所在模块
    string collect_point_name = 3; // 检查点名称
}

message QueueApcThread{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    uint64 apc_routine = 3; //apc地址
    MemActionOtherInfo info = 4;
}

message SetContextThread{
    NodeProcessInfo node_proc_info = 1;
    uint32 set_mode = 2;
    MemActionOtherInfo info = 3;
}

message ProtectVirtualMem{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    ProtectType old_protect  = 3;
    ProtectType new_protect  = 4;
    MemActionOtherInfo info = 5;
}

message AllocateVirtualMem{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    ProtectType protect = 3;
    AllocationType alloc_type= 4;
    MemActionOtherInfo info = 5;
}

message WriteVirtualMem{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    uint64 write_buf_addr = 3;
    uint32 write_len = 4;
    WriteMemRiskMode risk_mode = 5;
    MemActionOtherInfo info = 6;
}

message ReadVirtualMem{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    uint64 read_buf_addr = 3;
    uint32 read_len = 4;
    MemActionOtherInfo info = 5;
}

message MapViewOfSection{
    NodeProcessInfo node_proc_info = 1;
    uint64 base_addr = 2;
    ProtectType protect = 3;
    AllocationType alloc_type= 4;
    MemActionOtherInfo info = 5;
}

message WhiteAddBlack {
    enum ReportType {
        UNKNOWN     = 0;
        NO_CERT_NUM = 1; // 加载的无签名 DLL 数量大于 0 小于等于 2
        DLL_HIDE    = 2; // 加载的无签名 DLL 文件为隐藏文件
        SAME_TIME   = 3; // 加载的无签名 DLL 文件与可执行程序时间戳相同
        OLD_TIME    = 4; // 加载的无签名 DLL 文件时间戳在可执行文件之后
        EXE_DIR_NUM = 5; // 加载无签名 DLL 的可执行文件同级目录下文件数量小等于 5
    }
    ReportType report_id       = 1; // 异常点编号
    NodeFileInfo dll_file_info = 2; // DLL 文件信息
}

message OpenDeviceObject {
    string device_name = 1;
}

message CreateServiceAction {
    string service_name = 1;
    string service_path = 2; // 服务二进制文件路径, 路径包含自动启动服务的参数
}

message StartServiceAction {
    string service_name = 1;
    string service_path = 2; // 服务二进制文件路径, 路径包含自动启动服务的参数
}

message CreateTaskScheduler {
    string task_scheduler_name = 1;
    string create_author = 2; // 创建计划任务的用户, 主机名+用户, 例: DESKTOP-FVPU73C\ anxinkejiadmin
    string command = 3; // 计划任务执行的命令行
    string arguments = 4; // 计划任务执行参数
}

message StartTaskScheduler {
    string task_scheduler_name = 1;
    string create_author = 2; // 创建计划任务的用户, 主机名+用户, 例: DESKTOP-FVPU73C\ anxinkejiadmin
    NodeProcessInfo start_process = 3;
}

// 行为信息
message V01RuleActionItem {
    V01ActionType action_type        = 1; // 行为类型
    uint64 trigger_time              = 2; // 触发该行为的时间
    AcdrProcessUnique process_unique = 3; // 进程唯一信息
    NodeFileInfo process_file_info   = 4; // 进程文件信息, 仅在线索上报中有值
    oneof ActionContent {
		AcdrProcessParrentRelationUpdate process_relation_update = 10;
		NodeProcessInfo process_create = 11;
		NodeFileInfo file_create = 12;
		NodeFileInfo file_write = 13;
		NodeFileInfo file_read = 14;
		NodeFileInfo file_delete = 15;
        FileRename file_rename = 16;
		ConnectionInfo net_connect = 17;
		ConnectionInfo net_accept = 18;
        NodeAddrInfo net_listen = 19;
		// 常规行为点，直接和进程关联即可
        RegCreate reg_create = 20;
        ScriptHttp script_http = 22;
        ScriptImageLoad script_image_load = 23;
        ScriptRunWmicCode script_run_wmic_code = 26;
        ScriptScheduleCreate script_schedule_create = 27;
        ScriptGetApiAddr script_get_api_addr = 24;
        ScriptWmicWin32Share script_wmic_win32_share = 25;
        ScriptWmicTerminateProcess script_wmic_terminate_process = 28;
        ScriptWmicRegOper script_wmic_reg_oper = 29;
        ScriptWmicServiceOper script_wmic_service_oper = 30;
        ScriptWmicQuery script_wmic_query = 31;
        ScriptAmsiByAmsiContext script_amsi_by_amsi_context = 32;
        ScriptAmsiDllHijack script_amsi_dll_hijack = 33;
        ScriptEmail script_email = 34;
        LogClear log_clear = 35;
        // NodeProcessInfo queue_apc_detail = 36;          // moved to 81
        // NodeProcessInfo set_context_thread_detail = 37; // moved to 82
        NodeProcessInfo kill_process_detail = 38;
        NodeProcessInfo process_inject_detail = 39;
        RegDelete reg_delete = 40;
        RegWrite reg_write = 41;
        RegSetSecurity reg_set_security = 42;
        NodeFileInfo file_hide = 43;
        NodeFileInfo file_readonly = 44;
        NodeFileInfo load_remote_module = 47;
        WhiteAddBlack white_add_black = 48; // 白加黑
        CveAttack cve = 55;
        RemoteBugOverflow remote_bug_overflow = 56;
        PuppetProcessAttack puppet_process = 57;
        DomainConnectionInfo net_connect_domain = 58;
        NetSnifferInfo net_sniffer_content                 = 59;
        EnvHijackInfo env_hijack_content                   = 60;
        SelfDeleteInfo self_delete_content                 = 61;
        V01PtraceInfo ptrace_content                       = 62;
        FileLinkInfo file_link_content                     = 63;
        CallUsermodehelperInfo call_usermodehelper_content = 64;
        ReverseShellInfo reverse_shell_content             = 65;
        NodeProcessInfo process_exec_content               = 66;
        NodeFileInfo restore_utime_content                 = 67;
        NodeFileInfo file_ioctl_immutable_content          = 68;
        Mkdir mkdir_content                                = 69;
        FileSymlink file_symlink                           = 70;
        NodeFileInfo file_set_uid                          = 71;
        Bpf bpf                                            = 72;
        NodeProcessInfo privilege_escalation               = 73;
        FakeExeFileInfo fake_exe_file_content              = 74;
        HideModuleInfo hide_module_content                 = 75;
        V01KillInfo kill_content                           = 76;
        FileModeChangeContentInfo file_mode_change_content = 77;
        SetRlimit resoure_limit                            = 78;
        FilelessAttackInfo fileless_attack_content         = 79;
        NodeFileInfo file_open_content                     = 80;
        QueueApcThread queue_apc_detail                    = 81;
        SetContextThread set_context_thread_detail         = 82;
        ProtectVirtualMem protect_mem_detail               = 83; 
        AllocateVirtualMem allocate_mem_detail             = 84;
        WriteVirtualMem write_mem_detail                   = 85;
        ReadVirtualMem read_mem_detail                     = 86;
        MapViewOfSection map_view_section_detail           = 87;
        NodeFileInfo delete_byself                         = 88;
        OpenDeviceObject open_device_object         = 89;
        CreateServiceAction create_service          = 90;
        StartServiceAction start_service            = 91;
        CreateTaskScheduler create_task_scheduler   = 92;
        StartTaskScheduler start_task_scheduler     = 93;
    }
}

// 行为集合
message V01RuleInfoMessage {
    string rule_name                       = 1; // 规则名 eg:del_self、create_process、autorun_create
    uint32 rule_level                      = 2; // 风险等级
	bool  need_show_only0229               = 3; // 是否需要展示攻击，0229后由server自己决定
    AcdrProcessUnique root_process_unique  = 4; // 根进程唯一信息
    repeated V01RuleActionItem action_list = 5;
    string attack                          = 6; // ATT&CK 逗号分隔, "TA0002,T1059.001,TA0002,T1059.002"
    string client_version                  = 7; // 终端版本信息
}

// 规则集合 (最大100条？)
message RuleInfoMessages {
    repeated V01RuleInfoMessage rule_list = 1;
}

enum V01ActionType {
    V01AT_DEFAULT                       = 0;
    V01AT_PROCESS_CREATE                = 1;
    V01AT_PROCESS_EXIT                  = 2;
    V01AT_SCRIPT_SCHEDULE_CREATE        = 3;  // 脚本方式创建计划任务， 路径：%s 参数：%s
    V01AT_SCRIPT_GET_API_ADDR           = 11; // 脚本方式获取API地址 api名称：%s
    V01AT_SCRIPT_WIN32_SHARE            = 16; // 脚本方式共享文件，共享名称：%s 路径：%s
    V01AT_WMI_TERMINATE_PROCESS         = 30; // wmi方式结束进程 pid:%d 进程路径：%s
    V01AT_WMI_REG_OPER                  = 31; // wmi方式操作注册表, 操作方式：%s， 操作路径：%s， 设置值：%s，
    V01AT_WMI_SERVICE_OPER              = 32; // wmi方式操作服务， 服务名：%s，操作方式：%s 服务路径：%s
    V01AT_WMI_QUERY                     = 33; // 调用wmi查询命令，语言方式：%s， 查询命令：%s
    V01AT_SCRIPT_AMSI_BY_INIT_FAILED    = 34; // 无结构体，过AMSI保护-通过设置InitFailed字段
    V01AT_SCRIPT_AMSI_BY_AMSI_CONTEXT   = 35; // 过AMSI保护-通过修改AMSICONTEXT结构体
    V01AT_SCRIPT_AMSI_DLLHIJACK         = 36; // 通过AMSI方式Dll劫持,Dll名称%s
    V01AT_SCRIPT_KEY_LOGGER             = 37; // 无结构体，脚本方式进行键盘记录
    V01AT_SCRIPT_SCREEN_SHOT            = 38; // 无结构体，脚本方式进行屏幕截图
    V01AT_SCRIPT_EMAIL                  = 39; // 脚本方式发送邮件，发送者：%s ,接收者 %s
    V01AT_LOG_CLEAR                     = 40; // 清空日志文件%s
    V01AT_QUEUE_APC                     = 41; // 向进程的APC队列投递任务
    V01AT_SET_CONTEXT_THREAD            = 42; // 设置线程上下文
    V01AT_KILL_PROCESS                  = 43; // 杀死进程
    V01AT_PROCESS_INJECT                = 44; // 进程注入
    V01AT_FILE_HIDE                     = 45; // 文件隐藏
    V01AT_FILE_READONLY                 = 46; // 文件只读
    V01AT_READ_MEM_DETAIL               = 50; // 读虚拟内存
    V01AT_WRITE_MEM_DETAIL              = 51; // 写虚拟内存
    V01AT_ALLOC_MEM_DETAIL              = 52; // 虚拟内存分配
    V01AT_PROTECT_MEM_DETAIL            = 58; // 更改虚拟内存的保护
    V01AT_MAP_VIEW_SECTION_DETAIL       = 59; // 虚拟内存映射
    V01AT_DELETE_BYSELF                 = 60; // 进程自删除 (windows) 文件节点
    V01AT_MEM_HEAP_SPRAY                = 501;  // 堆喷射
    V01AT_MEM_ROP                       = 502;  // ROP攻击
    V01AT_LOAD_REMOTE_MODULE            = 503;  // 加载远程模块
    V01AT_MEM_LAYOUT_SHELL_CODE         = 504;  // 布局shellcode
    V01AT_MEM_EXEC_SHELL_CODE           = 505;  // 执行shellcode
    V01AT_MEM_ENGINE_ATTACK             = 506;  // 引擎攻击
    V01AT_STACK_ATTRIBUTE_ATTACK        = 507;  // 栈属性攻击
    V01AT_STACK_CODE_EXEC               = 508;  // 栈代码执行
    V01AT_MEM_STACK_OVERTRUN            = 509;  // 栈翻转
    V01AT_CVE_ATTACK                    = 601;  // cve
    V01AT_REMOTE_BUG_OVERFLOW           = 602;  // 远程漏洞溢出
    V01AT_PUPPET_PROCESS                = 603;  // 傀儡进程
    V01AT_WHITE_ADD_BLACK               = 604;  // 白加黑
    V01AT_SIGN_LEN_ABNORMAL             = 605;  // 签名长度异常
    V01AT_OPEN_DEVICE_OBJECT            = 610;  // 打开恶意驱动设备对象
    V01AT_CREATE_SERVICE                = 611;  // 创建服务
    V01AT_START_SERVICE                 = 612;  // 启动服务
    V01AT_CREATE_SCHEDULER              = 613;  // 创建计划任务
    V01AT_START_SCHEDULER               = 614;  // 启动计划任务
    V01AT_UNKNOWN                       = 1001; // 未知行为
    V01AT_FILE_CREATE                   = 1002; // 文件创建
    V01AT_FILE_READ                     = 1003; // 文件读取
    V01AT_FILE_WRITE                    = 1004; // 文件写
    V01AT_FILE_RENAME                   = 1005; // 文件重命名
    V01AT_FILE_DELETE                   = 1006; // 文件删除
    V01AT_FILE_LINK                     = 1007; // 创建文件连接
    V01AT_ICTL_IMMUTABLE                = 1008; // 通过ioctl操作文件
    V01AT_RESTORE_UTIME                 = 1009; // 恢复文件时间属性
    V01AT_MAKEDIR                       = 1010; // 创建目录
    V01AT_SYMLINK                       = 1011; // 创建符号链接
    V01AT_SET_UID                       = 1012; // 修改文件uid
    V01AT_MODE_CHANGE                   = 1014; // 修改文件权限
    V01AT_SCRIPT_IMAGE_LOAD             = 1100; // 脚本方式加载dll，路径：%s
    V01AT_SCRIPT_RUN_WMIC_CODE          = 1101; // 脚本方式执行wmic命令，命令：%s
    V01AT_FILELESS_ATTACK               = 1109; //无文件攻击
    V01AT_NET_SNIFFER                   = 1110; //网络抓包行为
    V01AT_ENV_HIJACK                    = 1111; //环境变量劫持
    V01AT_SELF_DELETE                   = 1112; // 自删除 (linux) 只能查到文件路径, 没有完整文件节点
    V01AT_FILE_OPEN                     = 1113; //文件打开
    V01AT_CALL_USERMODEHELPER           = 1114; //内核执行用户态进程。
    V01AT_REVERSE_SHELL                 = 1115; //反弹shell
    V01AT_PROCESS_EXEC                  = 1116; //exec
    V01AT_PTRACE                        = 1117; //读写进程内存
    V01AT_BPF                           = 1118; //bpf技术
    V01AT_PRIVILEGE_ESCALATION          = 1119; //令牌提升
    V01AT_FAKE_EXE_FILE                 = 1120; //伪装进程执行路径
    V01AT_HIDE_MODULE                   = 1121; //隐藏模块
    V01AT_KILL                          = 1122; //kill信号
    V01AT_SETRLIMIT                     = 1123; //设置资源限制
    V01AT_NET_ACCEPT                    = 1200;
    V01AT_NET_CONNECT                   = 1201;
    V01AT_NET_LISTEN                    = 1202; // 端口监听，监听地址：%s 监听端口 ：%d
    V01AT_REG_CREATE                    = 1203; // 注册表创建
    V01AT_PROCESS_PROMOTED_PRIVILEGE    = 1204; // 无结构体，当前进程（process_unique）提权
    V01AT_SCRIPT_HTTP                   = 1205; // 脚本方式发起http请求, url：%s
    V01AT_REG_DELETE                    = 1206; // 注册表删除
    V01AT_REG_WRITE                     = 1207; // 注册表写入
    V01AT_REG_SET_SECURITY              = 1208; // 设置注册表权限
    V01AT_NET_CONNECT_DOMAIN            = 1209; // 通过域名创建网络连接
}

// 上报外联信息
message OutreachInfos {
    repeated OutreachBean Beans = 1; // 外联信息列表
}

// 外联五元组、进程信息
message OutreachBean {
    uint32             EventType            = 1;  // 外联类型枚举 1:ip 2:域名
    bytes              UniqueFlag           = 2;  // 外联唯一标识
    NetContentBaseInfo NetInfo              = 3;  // 五元组
    int64              DiscoverTime         = 4;  // 外联信息发现时间，单位：linux毫秒
    AcdrProcessUnique  RootProcessUnique    = 5;  // 根进程
    AcdrProcessSimple  CurProcessUnique     = 6;  // 当前进程
    string             ClientVersion        = 7;  // 终端版本信息
}

message InternalOutreachInfos {
    repeated InternalOutreachInfoWithMac InterOutreach = 1;
}

message InternalOutreachInfoWithMac {
    bytes    MachineID    = 1; // 主机ID
    bytes    Beans        = 2; // 外联信息
}
