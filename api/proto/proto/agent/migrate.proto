syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

// 服务器推送迁移消息
message ServerMigrateMsg {
    bytes ip    = 1; // 新服务器的IP
    uint32 port = 2; // 新服务器的端口号
    bool force  = 3; // 强制迁移（先保留），不验证新服务器服务器有效性，都进行迁移
}

enum ServerMigrateErrorCode {
    SMEC_OK                      = 0; // 成功
    SMEC_CONNECT_SERVER_FAILED   = 1; // 无法连接新的服务端
    SMEC_OPEN_CONFIG_FILE_FAILED = 2; // 打开config文件失败
    SMEC_OPEN_CONFIG_NOT_EXIST   = 3; // config文件不存在
    SMEC_PORT_WRONG              = 4; // 端口号错误，大于65535
    SMEC_WRITE_CONFIG_FAILD      = 5; // 修改配置文件失败
}

message ServerMigrateResult {
    ClientID baseInfo                = 1; // 主机信息标识
    bytes ip                         = 2; // 新服务器的IP
    uint32 port                      = 3; // 新服务器的端口号
    ServerMigrateErrorCode errorCode = 4; // 成功OK, 失败返回原因ERR_reason
}
