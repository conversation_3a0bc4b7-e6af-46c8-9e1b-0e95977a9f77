syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

enum SnapshotReportType {
  SR_UNKNOWN = 0;
  SR_CREATE = 1; // 增加文件
  SR_DELETE = 2; // 删除文件
  SR_MODIFY = 3; // 修改文件
}

message FileSnapshotInfos {
  uint32 Version = 1;  // 版本
  int64 Timestamp = 2; // 采集时间戳
  int64 FileSeqNo = 3; // 文件上传序号
  int64 FileTotalNo = 4; // 文件总上传量
  bytes MachineID = 5; // 主机ID
  int32 OSType = 6; // 操作系统类型
  repeated FileSnapshotInfo infos = 7;
  map<string, SignatureInfo> SignInfoDictionary = 8; // 签名字典表
}

message FileSnapshotInfo {
  SnapshotReportType ReportType = 1; // 上报类型
  repeated DirectoryInfo DirctoryInfoList = 2; // 目录信息列表
}

message DirectoryInfo {
  string DirectoryName = 1; // 目录名, 如: C:\windows\system32
  repeated FileInfos FileInfoList = 2; // 文件信息列表
}

message FileInfos {
  string FileName = 1;
  int64  FileSize = 2;
  int64  CreateTime = 3;
  int64  ModifyTime = 4;
  int64  LastAccessTime = 5;
  bytes  FileMD5 = 6;
  bytes  FileSha1 = 7;
  bytes  FileSha256 = 8;
  FileTypeIdent FileType = 9;
  repeated string SignatureInfoSerial = 10;
  bytes  TlshHash = 11;
  bytes  ImpHash = 12;
}
