syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

// 请求、广播
message LicenseEnableRequest {
    string                 ip                     = 1; // ip，同时在线时，服务端判断machineid是否重复的依据
    bool                   enable                 = 2;
    string                 randomKey              = 3; // 32字节的随机校验码
    string                 procductName           = 4; // 企业标识
    int64                  groupid                = 5; // 组id
    string                 server_addr            = 6; // config 中配置的连接 server 端IP或域名
}
// 回复
message LicenseEnableResponse {
    bool                   needSave               = 1; // machineid是否重复，如果重复，machineid为新分配的
    bool                   enable                 = 2;
    string                 randomKey              = 3; // 32字节的随机校验码
    string                 procductName           = 4; // 企业标识
    bytes                  machineid              = 5; // machineid, 服务端返回信息
    repeated string        products               = 6; // 安芯主机端子产品列表
    int64                  groupId                = 7; // 组id
}

