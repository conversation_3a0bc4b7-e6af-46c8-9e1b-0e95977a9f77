syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
//
//  系统风险 识别结果
//--------------------------------------------------
message MemProtectRiskSystemInfo {
    repeated RiskSystemInfoOfReg RiskSystemInfoOfRegList = 1;
    repeated RiskSystemInfoOfApp RiskSystemInfoOfAppList = 2;
    repeated RiskSystemKernel    RiskSystemKernelList    = 3;
    repeated RiskAttack          RiskAttackList          = 4;
    repeated DirtyCow            DirtyCowList            = 5;
    repeated ProcessPrivilege    ProcessPrivilegeList    = 6;
    repeated RiskSystemInfoOfAccount RiskSystemInfoOfAccountList = 7;
    repeated RiskDomainEventLog  RiskDomainEventLogList  = 8;
}

message MemProtectRiskBaseInfo {
    string      dateTime = 1; // 风险发生的时间
    RiskHeader  header   = 2;
    ProcessInfo Process  = 3;

    uint64 clientTime = 50; // 客户端发现时间
}

//需要与客户端策略类型值保持一致！！！
enum RegOperation {
    rgo_unset   = 0;
    rgo_create  = 0x20001;
    rgo_delete  = 0x20002;
    rgo_delete1 = 0x20004;
    rgo_write   = 0x20006;
}

// 注册表风险
message RiskSystemInfoOfReg {
    MemProtectRiskBaseInfo basicPorcessInfo = 1;
    bytes                  regPath          = 2; // 注册表路径
    RegOperation           regOp            = 3;
    bytes                  regSetValue      = 4; // 仅RegOperation为write有效
}

// 应用风险
message RiskSystemInfoOfApp {
    MemProtectRiskBaseInfo basicPorcessInfo      = 1;
    bytes                  subProcessPath        = 2; // 子进程路径
    bytes                  subProcessFileSha256  = 3; // 子进程文件sha256
    bytes                  subProcessCommandLine = 4; // 子进程命令行
}

enum KernelRiskType {
    krt_unknown              = 0;
    krt_hiddenprocess        = 1;
    krt_integrity_failure    = 2;
    krt_remote_leak_overflow = 3;
    krt_rootkit_hidmod       = 4;
    krt_ex_krnlrisk          = 5;
    krt_cve                  = 6;
}

// 内核风险
message RiskSystemKernel {
    string         dateTime = 1; // 风险发生的时间
    RiskHeader     header   = 2;
    KernelRiskType riskType = 3;
    string clientVersion = 4; //终端版本信息
    oneof desc {
        IntegrityFailure integrityFailure = 23;
        HideMod          hidemod          = 24;
        RiskSystemKernelExs entryExs      = 25; 
        ProcessInfo      hideProcess      = 26;
        LeakOverflowInfo leakinfo         = 27;
        CveInfo          cveInfo          = 28;
    }

    uint64 clientTime = 50; // 客户端发现时间
}

message LeakOverflowInfo {
    uint32 reportID   = 1;
    string remote_addr= 2;
    string cve        = 20;
    bytes net_data = 21;    // 网络数据包
}

// CVE 风险
message CveInfo {
    string attack_source= 1; //攻击源, 可能是或者http://127.0.0.1/1.exe
}

enum AttackType {
    at_unknown       = 0;
    at_golden_ticket = 1;
    at_pth = 2; // pass the hash
    at_worm = 3; // 蠕虫风险
    at_cve2020_1472 = 4; // CVE漏洞
    at_cve2021_42287 = 5; // CVE 42287漏洞
}

// 攻击风险
message RiskAttack {
    MemProtectRiskBaseInfo basicPorcessInfo = 1;
    AttackType             riskType         = 2;
    string                 hostIP           = 3; // PTH使用这个字段
    string                 userName         = 4; // 用户名，PTH使用这个字段
    string                 computerName     = 5; // 机器名, CVE1472使用
}

// 脏牛漏洞
message DirtyCow {
    MemProtectRiskBaseInfo basicPorcessInfo      = 1;
}

// 进程提权
message ProcessPrivilege {
    MemProtectRiskBaseInfo basicPorcessInfo      = 1;
    string modname                               = 2;  // 通过哪个模块提的权
}

// 隐藏模块
message HideMod {
    string modname      = 1;
}

// 内核完整性校验失败
message IntegrityFailure {
    string modname  = 1;
}

//--------------------------------------------------
//  内核风险检测 上报
//--------------------------------------------------

//上报不属于任何模块的地址 0
message  RiskInfoCallbackNoMod {
    string  strCallBackType = 1;     //异常类型
    string  strCallBackSubType = 2;  //异常子类型 (为空则不显示)
    string  strCallBackAddr = 3;     //地址值
}

//上报不正常的值 1
message  RiskInfoValueAbnoraml {
    string  strAbnormalType = 1;     //异常类型
    string  strAbnormalSubType = 2;  //异常子类型(为空则不显示)
    string  strAbnormalAddr = 3;     //当前异常值
    string  strNormalAddr   = 4;     //正常值  (为空则不显示)
}

//上报命中黑特征 2
message  RiskInfoBlackCharacter {
    string  strCharacterType = 1;     //黑特征类型
    string  strCharacterSubType = 2;  //黑特征子类型(为空则不显示)
    string  strCharacterInfo = 3;     //黑特征具体信息
}


//上报黑模块信息 3
message RiskInfoBlackMod {
    
    string strBlackModName = 1;      //模块的名称
    string strBlackModFilePath = 2;  //模块文件路径
    string strBlackModBaseAddr = 3;  //模块内存映像的起始地址
    string strBlackModSize = 4;      //模块内存映像的大小
    string strBlackDetail = 5;       //详情(命中哪些探测规则),不建议显示
}

//上报白模块(系统模块)的篡改 4
message  RiskInfoWhiteModTamper{
    string strModName = 1;         //模块的名称
    string strModBase = 2;         //模块起始地址
    string strTamperDetail = 3;    //篡改详情
}

//子风险信息
message RiskSystemKernelEntry {
    
    enum KrnlRiskSubType{                   //上报风险类型
        e_risk_callback_nomod = 0;          //上报不属于任何模块的地址 0 
        e_risk_value_not_noraml = 1;        //上报不正常的值 1
        e_risk_black_character  = 2;        //上报命中黑特征 2
        e_risk_black_mod        = 3;        //上报黑模块信息 3
        e_risk_white_mod_tamper= 4;         //上报白模块(系统模块)的篡改 4
    }
    KrnlRiskSubType  eKrnlRiskType = 1;
    
    oneof KrnlRiskEntryDetail{                //上报风险内容 跟上面是对应关系,例如21对应11 ,根据上面的type解析下面的对应子类型信息结构.
        RiskInfoCallbackNoMod  stCallbackNoMod   = 20;
        RiskInfoValueAbnoraml  stValueAbnormal   = 21;
        RiskInfoBlackCharacter stBlackCharacter  = 22;
        RiskInfoBlackMod       stBlackMod        = 23;
        RiskInfoWhiteModTamper stWhiteModTamper  = 24;
    }
}

message RiskSystemKernelExs {
    string riskName = 1;
    repeated    RiskSystemKernelEntry RiskSystemKernelEntryList= 2;   //风险分条
}

//账户风险
message RiskSystemInfoOfAccount {
    MemProtectRiskBaseInfo basicPorcessInfo = 1;
    string remote_ip    = 2;    //远程地址，仅lt_windows_remote_auth时有效
    string user_name    = 3;    //用户名
    bool   isSuperUser  = 4;    //是否是管理员账户
    
    enum LogonType{
       lt_windows_remote_auth = 0; //远程登录
       lt_windows_local_auth = 1;  //本地登录
    }
    LogonType  logon_type = 5;     //登录方式
 
    enum AbnormalReason{                   
       ar_brute_force = 0;            //暴力破解
       ar_weak_passwd = 1;            //弱密码
       ar_unusual     = 2;            //不常见的登录
    }
    AbnormalReason  abnormal_reason = 6;     //异常原因
}


//域控风险字段
message RiskDomainEventLog{
    RiskHeader      header          = 1;        //风险头
    string          TimeCreated     = 2;        //日志创建时间
    string          ComputerName    = 3;        //计算机名
    string          UserName        = 4;        //用户名
    string          DomainName      = 5;        //域名
    uint32          EventId         = 6;        //事件类别ID
    uint64          EventRecordId   = 7;        //日志记录ID
    string          EventMessage    = 8;        //事件详细数据
    string          EventXml        = 9;        //XML数据
    DomainRiskType  RiskType        = 10;       //风险类型
    ProcessInfo     ProcessSource   = 11;       //风险源
    oneof detail{
        DomainRiskEventLogClear rs_EventLogClear                                    = 20;
        DomainRiskLoginWithCredentials rs_LoginWithCredentials                      = 21;
        DomainRiskCreateDirectoryServiceObject rs_CreateDirectoryServiceObject      = 22;
        DomainRiskAddPlanningTasks rs_DomainAddPlanningTasks                        = 23;
        DomainRiskAddSystemservice rs_DomainAddSystemservice                        = 24;
        DomainRiskPsloggedonHandler rs_PsloggedonHandler                            = 25;
        DomainRiskModifySensitiveGroupHandler rs_ModifySensitiveGroup               = 26;
        DomainRiskCVE2021_42278 rs_DomainCVE2021_42278                              = 27;
        DomainRiskAclAbnormalUpdate rs_AclAbnormalUpdate                            = 28;
        DomainRiskCVE2021_42287     rs_DomainCVE2021_42287                          = 29;
        DomainRiskCheckSensitiveGroupOrUser rs_CheckSensitiveGroupOrUser            = 30;
        DomainRiskASREPRoasting rs_ASREPRoasting                                    = 31;
    }
}

//域控风险类型
enum DomainRiskType{
    dr_unknown                          = 0;    //未知
    dr_EventLogClear                    = 1;    //1102 日志被清空
    dr_EventServiceClose                = 2;    //1100 事件记录服务关闭
    dr_LoginWithCredentials             = 3;    //4648 显示凭据登录
    dr_CreateDirectoryServiceObject     = 4;    //5137 新增组策略
    dr_DomainAddPlanningTasks           = 5;    //4698 增加计划任务
    dr_DomainAddSystemservice           = 6;    //4697 增加系统服务
    dr_PsloggedonHandler                = 7;    //5145 Psloggedon
    dr_ModifySensitiveGroup             = 8;    //[4728, 4732, 4756] 修改敏感用户组
    dr_CVE_2021_42278                   = 9;    // 4741 创建机器账户 && 4781 修改账户名称
    dr_AclAbnormalUpdate                = 10;   //5136 ACL异常修改 
    dr_CVE_2021_42287                   = 11;   // 4769 Kerberos 请求服务票证
    dr_CheckSensitiveGroupOrUser        = 12;   //4661 查看敏感用户/组
    dr_ASREPRoasting                    = 13;   //4768 AS-REP Roasting
}

//下面是域控事件日志EventData或者UserData的五大类型
message SubjectType{
    string  SubjectDomainName   = 1;
    string  SubjectUserName     = 2;
    string  SubjectUserSid      = 3;
    string  SubjectLogonId      = 4;
}

message TargetType{
    string  TargetDomainName    = 1;
    string  TargetUserName      = 2;
    string  TargetUserSid       = 3;
    string  TargetSid           = 4;
    string  TargetLogonId       = 5;
    string  TargetInfo          = 6;
    string  TargetServerName    = 7;
}

message Sourcetype{
    string WorkstationName   = 1;
    string IpAddress         = 2;
    string IpPort            = 3;
}

message TicketType{
    string  TicketEncryptionType    = 1;
    string  TicketOptions           = 2;
    string  Status                  = 3;
}

message ObjectType{
    string  ObjectDN        = 1;
    string  ObjectGUID      = 2;
    string  ObjectClass     = 3;
    string  ObjectServer    = 4;
    string  ObjectType      = 5;
    string  ObjectName      = 6;
}

//1102 日志被清空
message DomainRiskEventLogClear{
    SubjectType  subject    = 1;  
    string strIp            = 2;
}

//4648 显示凭据登录
message DomainRiskLoginWithCredentials{
    TargetType taget        = 1;
    string strIp            = 2;
    SubjectType  subject    = 3;
}

//5137 新增组策略
message DomainRiskCreateDirectoryServiceObject{
    SubjectType  subject    = 1;
    string strIp            = 2;
}

//4698 增加计划任务
message DomainRiskAddPlanningTasks{
    SubjectType  subject        = 1;
    string strTaskName          = 2;
    string strIp                = 3;
}

//4697 增加系统服务
message DomainRiskAddSystemservice{
    SubjectType  subject        = 1;
    string strServiceName       = 2;
    string strIp                = 3;
}

//5145 Psloggedon
message DomainRiskPsloggedonHandler{
    SubjectType  subject        = 1;
    string strIp                = 2;
}

//[4728, 4732, 4756]  修改敏感用户组
message DomainRiskModifySensitiveGroupHandler{
    SubjectType  subject        = 1;
    TargetType taget            = 2;
    string strIp                = 3;
    string strMemberName        = 4;
}

//4741 创建机器账户 && 4781 修改账户名称
message DomainRiskCVE2021_42278{
    TargetType  target       = 1;
    SubjectType subject      = 2;
    string oldTargetUserName = 3;
    string newTargetUserName = 4;
}

//5136 ACL异常修改
message DomainRiskAclAbnormalUpdate{
    SubjectType subject      = 1;
    string strIp             = 2;
    ObjectType object        = 3;
}

//4769 Kerberos 请求服务票证
message DomainRiskCVE2021_42287{
    TargetType  target          = 1;
    uint32      family          = 2;    //AF_INET6:23 、 AF_INET:2
    NetInfo     remoteAddress   = 3;
   }

//4661  查看敏感用户/组
message DomainRiskCheckSensitiveGroupOrUser{
    enum ModifyType{
        mt_user                    = 0;    //用户
        mt_group                   = 1;    //组
    }
    SubjectType  subject        = 1;
    string strIp                = 2;
    string strTargetName        = 3;
    ModifyType modify           = 4;
}

//4768  AS-REP Roasting
message DomainRiskASREPRoasting{
    TargetType target       = 1;
    string strIp            = 2;
    string matchTool        = 3;
}
