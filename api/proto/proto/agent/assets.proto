//说明：
// 1、所有内存和文件大小单位都是字节
// 2、百分比单位都是float,如：36.37%,上传36.37
syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

message MemProtectAssetsInfo {
  ClientID                    baseInfo        = 1;
  repeated HostInformation    hostInfoList    = 2;
  repeated AccountInformation accountInfoList = 3;
  repeated EnvInformation     envInfoList     = 4;
  repeated KernelInformation  kernelInfoList  = 5;
  repeated ServiceInformation serviceInfoList = 6;
  repeated ProcInformation    procInfoList    = 7;
  //通过如下方式检测主机是否为域控服务器 ：setting.has_usualLogonThreshold_case() == MemProtect::MemProtectAssetsInfo::HAS_DOMAININFO_NOT_SET;
  oneof has_domainInfo {DomainInfo domainInfo = 8;}
  //后面的都放oneof里面
  oneof has_web_middleware{WebMiddlewareList middleware_list = 9;}
  // web站点信息
  oneof has_web_site_info{WebSiteInfoList web_site_info_list = 10;}
  oneof has_web_frame{WebFrameList web_frame_list = 11;}

  // 软件应用信息
  oneof has_host_application{HostApplicationList host_application_list = 12;}
  // 安装包信息
  oneof has_package_info{PackageInfoList package_info_list = 13;}
  // python包信息
  oneof has_python_package_info{PythonPackageInfoList python_package_info_list = 14;}
  // Web应用信息
  oneof has_web_app{WebAppList web_app_list = 15;}
  // npm包信息
  oneof has_npm_info{NpmPackageInfoList npm_info_list = 16;}
  // 网卡及流量信息
  oneof has_eth_info{EtherNetList eth_info_list = 17;}
  // 资源占用情况
  oneof has_resource_info{ResourceUtilization resource_util_info = 18;}
  // 计划任务信息
  oneof has_job_task_info{JobTaskInfoList job_task_info_list = 19;}
  // 应用弱口令
  oneof has_app_weak_pwd_info{AppWeakPwdInfoList app_weak_pwd_info_list = 20;}
}

//资产管理：主机信息
message HostInformation {
  bytes  osInfo                = 1;    //操作系统名称
  bytes  computerName          = 2;    //计算机名称
  bytes  userName              = 3;    //所有用户名,如: root,test1,test2
  string IEVersion             = 4;    //IE版本
  string IP                    = 5;    //ip地址
  string MAC                   = 6;    //mac地址
  string IPMASK                = 7;    //子网掩码
  bytes  gateway               = 8;    //网关地址
  uint64 memSize               = 9;    //内存大小 字节
  bytes  TCPPorts              = 10;   //主机处于监听状态的TCP端口,如：23,80,443
  bytes  UDPPorts              = 11;   //主机处于监听状态的UDP端口,如：23,80,443
  float  cpuUtilization        = 12;   //CPU使用率
  float  memUtilization        = 13;   //内存使用率
  float  diskUtilization       = 14;   //磁盘使用率
  bytes  agentVer              = 15;   //agent版本名
  bytes  versionCode           = 16;   //agent版本号(1209未用)
  bytes  agentKernelVer        = 17;   //agent安装的驱动信息，仅linux
  bytes  logonUserName         = 18;   //当前登录的账户，如admin,testadmin

  // 1209 新增
  string arch                  = 19;   // 架构
  uint32 cpuCores              = 20;   // CPU核数
  uint64 diskSize              = 21;   // 磁盘大小
  string jdkVersion            = 22;   // JDK 版本
  string webMiddlewareVersion  = 23;   // web中间件版本,|分隔 eg: tomcat-5.0.1|weblogic-1.0.1
  string driverLibVersion      = 24;   // 驱动版本号
  string behaviorLibVersion    = 25;   // 行为库版本
  string envLibVersion         = 26;   // 环境库版本
  string agentJdkVersion       = 27;   // JDK版本（Agent环境）

  string linuxOSInfoLong       = 28;   // linux 系统信息长串，仅linux
  string linuxOSInfoShort      = 29;   // linux 系统信息短串，仅linux

  bytes  recentlyLogonUserName = 30;   // 最近登录的账号，如admin,testadmin
  string hashEngineVersion     = 31;
  string sha256EngineVersion   = 32;
  string ngavLibVersion        = 33;   // ngav 行为规则的版本号
  int32  hostIpPolicyType            = 34;      // 上报主机ip的策略类型， 1 默认， 2 自定义优先 ， 3 网卡优先
  int64  agentInstallTime            = 35;      // agent 首次安装时间
  int64  driverInstallTime           = 36;      // 驱动 首次安装时间
  repeated etherNetInfo ethInfoList  = 37;      // 所有网卡信息
  bool isResourceUpdate         = 38; // 主机资源信息是否需要更新
  string raspRuleVersion        = 39; // RASP规则库的版本号
  string baselineLibVersion     = 40; // 基线规则规则的版本号
  string baselineOS             = 41; // 基线检查兼容平台
  string domainWhiteLibVersion     = 42; // 域名白名单规则的版本号
  string fileSignComWhiteLibVersion     = 43; // 文件签名公司白名单规则的版本号
  bytes osInfoDisplay             = 44;    //操作系统名称，展示名称，仅windows
  bytes domainUserName         = 45;   //域的账户
  string fileDriverBlockLibVersion     = 46; // 驱动黑库版本号
  repeated bool logonUserFlag     = 47; // 当前登陆用户是否为域用户列表,true 是，false 不是
  string winOSInfoShort      = 48;   // windows 系统信息短串，仅windows
  string proxyIpPort         = 49;   // 代理信息 ip:port
}

// 网卡及流量信息
message EtherNetList {
  int32    hostIpPolicyType              = 1;  // 上报主机ip的策略类型, 1 默认 2 自定义优先 3 网卡优先
  string   host_ip                       = 2;  // 通过策略指定显示的主机ip
  repeated etherNetInfo ethInfoList      = 3;  // 所有网卡信息
}

// 主机资源占用率
message ResourceUtilization{
  int64  collection_time        = 1;   //采集时间
  float  cpu_utilization        = 2;   //CPU使用率
  float  mem_utilization        = 3;   //内存使用率
  float  disk_utilization       = 4;   //磁盘使用率

  // 资源占用告警策略
  bool   cpu_alarm_enable       = 5;   //cpu告警开关
  float  cpu_threshold          = 6;   //cpu告警阈值
  bool   mem_alarm_enable       = 7;   //内存告警开关
  float  mem_threshold          = 8;   //内存告警阈值
  bool   disk_alarm_enable      = 9;   //磁盘告警开关
  float  disk_threshold         = 10;  //磁盘告警阈值
}

message ResourceAlarmList{
  repeated ResourceAlarm alarm_list = 1;
}

// 资源使用率告警
message ResourceAlarm{
  uint32 resource_type    = 1; // 资源类型 1 CPU 2 内存 3 磁盘
  int64  alarm_time       = 2; // 告警时间
  float  utilization      = 3; // 资源使用率
  uint32 policy_threshold = 4; // 策略阈值
}

//资产管理：账户信息,不一定包含登录信息
message AccountInformation {
  bytes                        userName           = 1;    //账户名
  bytes                        userId             = 2;    //账户ID
  bytes                        groupId            = 3;    //所属组ID
  bool                         state              = 4;    //账户所属状态：“启用” “禁用”
  bytes                        expireDate         = 5;    //密码过期时间
  bool                         isSuperUser        = 6;    //管理员权限root
  bytes                        loginPort          = 7;    //登录终端类型
  bytes                        loginIP            = 8;    //登录IP
  bytes                        loginTime          = 9;    //最近一次登录时间
  bytes                        passwordChangeTime = 10;   //密码最后修改时间（格式：2019-10-15）
  bool                         weakPassword       = 11;   //是否弱口令 not implement
  bool                         interactive        = 12;   //是否允许交互登录（仅Linux）
  bytes                        homePath           = 13;   //账户home目录
  bool                         isClone            = 14;   //克隆账户（仅win）
  bool                         isHidden           = 15;   //隐藏账户（仅win）
  bytes                        accountExpireDate  = 16;   //账号过期时间

  // 1209 新增
  string                       shell              = 17;    //登录终端类型
  string                       description        = 18;    //账号描述
  bool                         isDomainAccount    = 19;    //是否是域账号

  // 1215 新增
  bytes                        weakpwdRegex       = 20;    // 弱密码命中的正则，如果命中的是字典，将上报为空
  int32                        weakpwdType        = 21;    // 1 正则 2 字典 3 内置
  repeated weakPasswordhistory weakpwdhistory     = 22;    // 历史登录信息
}

message weakPasswordhistory {
  string ip          = 1;
  int64  login_times = 2;
}

//资产管理：环境变量信息
message EnvInformation {
  bytes name  = 1;    //环境变量名称
  bytes value = 2;    //环境变量值
}

//资产管理：内核模块信息
message KernelInformation {
  bytes  name        = 1;  //内核模块名称
  bytes  path        = 2;  //文件路径
  bytes  license     = 3;  //文件版权信息
  bytes  signature   = 4;  //签名信息
  bytes  description = 5;  //描述
  bytes  services    = 6;  //对应服务名
  bool   loaded      = 7;  //是否加载
  uint32 riskLevel   = 8;  //存在风险的模块，根据签名等判断
  uint32 imageSize   = 9;  //内核镜像大小
  bytes  sha256      = 10; // 文件sha256
  bytes  company     = 11; //公司名
}

//资产管理：启动服务信息
message ServiceInformation {
  bytes  name        = 1;  //服务名称
  bytes  path        = 2;  //服务启动路径
  //状态：运行状态
  //  1:服务未运行 2:服务正在启动 3:服务正在停止 4:服务正在运行
  //  5:服务即将继续 6:服务即将暂停 7:服务已暂停
  bytes  state       = 3;
  bytes  startTime   = 4;  //服务启动时间 2019-10-15 12:50:23
  bytes  startType   = 5;  //启动类型 win特有
  bytes  company     = 6;  //文件厂商 win特有
  bytes  dllPath     = 7;  //动态链接库 win特有
  bytes  dllCompany  = 8;  //动态链接库文件厂商 win特有
  bytes  sha256      = 9;  //文件SHA256
  bytes  sha256Dll   = 10;  //dll文件SHA256 win特有
  uint32 processID   = 11;  //运行状态进程ID
  bytes  description = 12;  //描述
}

//资产管理：进程信息
message ProcInformation {
  bytes                    name           = 1;    //进程名
  bytes                    path           = 2;    //文件路径
  bytes                    type           = 3;    //进程分类 not implement
  bytes                    userName       = 4;    //运行用户
  uint32                   threadNum      = 5;    //线程数
  uint64                   memRss         = 6;    //占用主机内存大小（字节）
  uint64                   memVss         = 7;    //虚拟内存大小    （字节）
  uint64                   ioRead         = 8;    //该进程IO读取大小（字节）
  uint64                   ioWrite        = 9;    //该进程IO写入大小（字节）
  float                    cpuUtilization = 10;   //CPU使用率%
  bytes                    startTime      = 11;   //启动时间 格式：2019-10-15 12:50:23
  uint32                   processID      = 12;   //进程PID
  bytes                    commandInfo    = 13;   //命令行参数
  bytes                    description    = 14;   //描述

  // 1209 新增
  float                    memUtilization = 15;   // 内存使用率%
  repeated PortInformation Ports          = 16;  // 进程打开的端口信息
}

message PortInformation {
  uint32 Port     = 1; // 端口号
  string Protocol = 2; // 协议（TCP,UDP)
}

// 域控资产
message DomainInfo {
  string                baseDN           = 1; // 域节点名称
  string                domainName       = 2; // 域名
  string                dns              = 3; // DNS服务器地址
  string                netBIOS          = 4; // NetBIOS名称(计算机名)
  repeated NetShareInfo netShareInfoList = 5; // 共享文件夹列表
}

// 共享文件夹
message NetShareInfo {
  string name = 1;  // 文件夹名
  string path = 2;  // 路径
}

//Jar包信息
message JarInfo {
  bytes name      = 1;    // jar包名称
  bytes summary   = 2;    // 类型描述
  bool  is_exec   = 3;    // 是否可执行
  bytes version   = 4;    // 版本号
  bytes path      = 5;    // 绝对路径
  bytes busi_name = 6;    // 所属服务
  bytes pid       = 7;    // 进程id
}

//web中间件链表
message WebMiddlewareList{
  repeated WebMiddleware web_middleware = 1;
}

// web中间件 (仅Linux)
message WebMiddleware{
  bytes            web_name           = 1;    //web服务名
  bytes            web_version        = 2;    //应用版本
  bytes            binary_path        = 3;    //二进制路径
  bytes            start_user         = 4;    //启动用户
  bytes            configuration_path = 5;    //配置文件路径
  uint32           process_id         = 6;    //进程PID
  repeated JarInfo jar_info_list      = 7;    //关联的jar
}

message WebSiteAppInfo{
  bytes virtual_path = 1;    //虚拟路径
  bytes real_path    = 2;    //真实路径
  bytes author       = 3;    //所属用户
  bytes group        = 4;    //所属组
  bytes authority    = 5;    //文件权限
}

message WebSiteInfo{
  bytes                   ip                 = 1;    //ip
  bytes                   port               = 2;    //端口号
  bytes                   protocol           = 3;    //协议类型
  bytes                   user               = 4;    //用户
  bytes                   server_name        = 5;    //所属服务
  bytes                   war_dir            = 6;    //war包部署总目录
  bytes                   root_dir           = 7;    //主目录
  bytes                   root_dir_authority = 8;    //主目录权限
  uint32                  site_count         = 9;    //站点总数
  repeated WebSiteAppInfo web_site_apps      = 10;   //安装的APP
}

message WebSiteInfoList{
  repeated WebSiteInfo web_site_info = 1;
}

//web框架
message WebFrame{
  bytes frame_name                    = 1;    //框架名称
  bytes frame_version                 = 2;    //框架版本
  bytes web_server                    = 3;    //服务类型
  bytes web_path                      = 4;    //应用路径
  bytes frame_language                = 5;    //框架语言
  repeated JarInfo jar_info_list      = 6;    //关联的jar
}

message WebFrameList{
  repeated WebFrame web_frame_info = 1;
}

//软件应用关联进程信息
message RelateProcInfo {
  bytes name       = 1;  //关联进程名
  bytes version    = 2;  //关联进程版本
  bytes process_id = 3;  //PID
  bytes start_time = 4;  //启动时间
  bytes path       = 5;  //进程路径
}

message HostApplicationInfo {
  bytes                   name          = 1;  //应用名称
  int32                   type          = 2;  //应用类别 1 普通应用 2 web应用 3 系统应用
  bytes                   version       = 3;  //版本
  bytes                   user          = 4;  //启动用户
  bytes                   path          = 5;  //二进制路径
  bytes                   config_path   = 6;  //配置文件路径
  uint64                  proc_num      = 7;  //关联进程数
  repeated RelateProcInfo app_proc_list = 8;  //具体关联进程信息
  repeated JarInfo        jar_info_list = 9;  //关联的jar
}

message HostApplicationList {
  repeated HostApplicationInfo host_app_list = 1;
}

//安装包信息
message PackageInfo {
  bytes  name          = 1;  //安装包名称
  bytes  summary       = 2;  //总述
  bytes  version       = 3;  //版本
  bytes  release       = 4;  //发行号
  bytes  package_type  = 5;  //安装包类型;
  bytes  vendor        = 6;  //安装包发行厂商
  bytes  architecture  = 7;  //架构信息
  uint64 size_in_kb    = 8;  //安装包大小（kb）
  uint64 creation_time = 9;  //安装时间
}

message PackageInfoList {
  repeated PackageInfo package_info = 1;
}

//Python包信息
message PythonPackageInfo {
  bytes name                         = 1;    // Python包名称
  bytes version                      = 2;    // Python包版本
  bytes dir                          = 3;    // Python包所在目录
  bytes python_version               = 4;    // Python包所属Python版本
}

message PythonPackageInfoList {
  repeated PythonPackageInfo python_package_info     = 1;
}

//web应用
message WebApp{
  bytes app_name                        = 1;    //Web应用名称
  bytes app_version                     = 2;    //Web应用版本
  bytes web_server                      = 3;    //服务类型
  bytes web_path                        = 4;    //应用路径
  bytes frame_language                  = 5;    //框架语言
}

message WebAppList{
  repeated WebApp web_app_info        = 1;
}

// 网卡信息
message etherNetInfo {
  string eth_name                       = 1 ;   // 网卡名称
  string eth_mac                        = 2 ;   // mac地址
  repeated string eth_ipv4_list         = 3 ;   // ethIpv4
  repeated string eth_ipv6_list         = 4 ;   // ethIpv6
  repeated string eth_gateway_list      = 5 ;   // 网关地址
  repeated string dns_list              = 6 ;   // DNS
  uint32 eth_link_state                 = 7 ;   // 网卡连接状态 0 未连接 1 连接
  repeated string eth_mask_list         = 8 ;   // 子网掩码
  string eth_speed                      = 9 ;   // 网卡速度
  repeated string eth_bcast_list        = 10;   // 广播地址

  // 0125新增 网卡流量信息
  uint64  up_flow_bytes                       = 11;   // 上行流量
  uint64  down_flow_bytes                     = 12;   // 下行流量
  int64   flow_time                           = 13;   // 采集时间（不是真正的上报时间)
}

// Npm包信息
message NpmPackageInfo {
  bytes name                         = 1;    //npm名称
  bytes version                      = 2;    //npm包版本号
  bytes path                         = 3;    //npm包所在绝对路径
  bytes scope                        = 4;    //npm包所在作用域范围
  bytes pid                          = 5;    //npm包应用pid
  bytes command                      = 6;     //npm包应用操作命令
}

// Npm包信息列表
message NpmPackageInfoList {
  repeated NpmPackageInfo npm_package_info     = 1;
}

// 计划任务信息列表
message JobTaskInfoList {
  repeated JobTaskInfo job_task_info = 1;
}

// 计划任务信息
message JobTaskInfo {
  bytes name         = 1;  // 计划任务名
  bytes type         = 2;  // 任务类型
  bool  is_enable    = 3;  // 启用状态 启用/未启用
  bytes exec_time    = 4;  // 执行时间
  bytes exec_user    = 5;  // 执行用户
  bytes etc_path     = 6;  // 配置文件路径
}
// 应用弱口令
message AppWeakPwdInfo {
  bytes app_name                      = 1;  // 应用名
  bytes user_name                     = 2;  // 账户名
  int32 weak_pwd_type                 = 3;  // 1 正则 2 字典 3 内置
  int64 weak_pwd_scan_time            = 4;  // 应用弱密码扫描时间
  bytes weak_pwd_regex_rule           = 5;  // 正则内容
}

message AppWeakPwdInfoList {
  repeated AppWeakPwdInfo app_weak_pwd_info_list  = 1;    // 应用弱密码信息列表
}