syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

message PullReq {
  int64 max_unicast_seq = 1; // 当前agent端单播消息最大序号
  int64 max_multicast_seq = 2; // 当前agent端组播消息最大序号
  int64 max_broadcast_seq = 3; // 当前agent端广播消息最大序号
  int64 group_id = 4; // 当前agent分组id
}

message PullResp {
  int64 max_unicast_seq = 1; // 当前服务端单播消息最大序号
  int64 max_multicast_seq = 2; // 当前服务端组播消息最大序号
  int64 max_broadcast_seq = 3; // 当前服务端广播消息最大序号
  repeated Content contents = 4; // 消息内容
}

message Content {
  string uuid = 1; // 消息唯一标识（通过uuid进行去重操作）
  int32 category = 2; // 消息分类（枚举数值参考：fizz.MsgCategoryType）
  bytes content = 3; // 消息内容
  int64 size = 4; // 消息大小
  int64 seq = 5; // 消息序号（当前消息对应的序号）
  int32 type = 6; // 消息类型（枚举数值参考：fizz.MsgContentType）
  int32 os = 7; // 操作系统类型（枚举数值参考：fizz.OsType）
}
