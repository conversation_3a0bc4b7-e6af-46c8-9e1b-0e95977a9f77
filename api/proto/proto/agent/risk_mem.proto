syntax = "proto3";
package agent;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/agent";

import "agent/common.proto";

//--------------------------------------------------
// 
//  内存风险 识别结果
//  对应 g_CmdMemProtectRiskMemInfo
//--------------------------------------------------
message MemProtectRiskMemInfo {
  ClientID baseInfo = 1;
  repeated LoadRemoteLib                  loadRemoteLibList = 2;
  repeated VirProtectExStkSpace   virProtectExStkSpaceList = 3;
  repeated ReturnStackExec                returnStackExecList = 4;
  repeated RemoteThread                   remoteThreadInject = 5;
  repeated MemNoFileAttack                memNoFileAttackList = 6;
  repeated MemHeapSpray                   memHeapSprayList = 7;
  repeated AnalyzerVirus                  analyzerVirusList = 8;
  repeated MemRop                         memRopList = 9;
  repeated MemLayoutShellCode             memLayoutShellCodeList = 10;
  repeated MemStackPivot memStackPivotList = 11;
  repeated MemRunningShellCode memRunningShellCodeList = 12;
  repeated MemStartProcess memStartProcessList = 13;
  repeated MemEngineAttack memEngineAttackList = 14;
  repeated TunnelRiskInfo tunnelRiskList = 15;

  repeated EtwRiskInfo etwRiskList = 50;
  repeated KernelVulnerableDriverRiskInfo kernelVulnerableDriverRiskList = 51;

}

//行为分析出来的病毒
message AnalyzerVirus {
  RiskHeader header = 1;
  bytes   imagePath = 2;  //文件全路径
  bytes   virusName = 3;  //病毒名
  bytes   virusDesc = 4;  //病毒描述信息
  uint32  processID = 5;  //进程Id
  string  processMd5 = 6;  //进程MD5
}

//二进制漏洞检测:加载远程模块
message LoadRemoteLib {
  RiskHeader header = 1;
  uint32  processID = 2;  //进程Id
  string  isX86Process = 3;  //是否32位进程(Yes/No)
  bytes   imagePath = 4;  //进程全路径
  bytes   remoteLibPath = 5;  //远程模块路径
  string  processMd5 = 6;  //进程MD5
  uint32  reportID = 7;

  repeated ProcessInfo ProcessInfoList = 20;// 进程信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress       = 26;  //指令所在地址
  string moduleName       = 27;  //指令所在模块
  string hookName         = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
}

// 栈属性攻击
//二进制漏洞检测:让栈内存具有执行权限
message VirProtectExStkSpace {
  RiskHeader header = 1;
  uint32  processID = 2;  //进程Id
  string  isX86Process = 3;  //是否32位进程(Yes/No)
  bytes   imagePath = 4;  //进程全路径
  uint64  address = 5;  //修改地址
  uint32  addrSize = 6;  //修改地址大小
  string  memAttri = 7;  //新设置的内存属性
  uint32  threadID = 8;  //线程Id
  uint64  stkBeginAddr = 9;  //线程栈开始位置
  uint64  stkEndAddr = 10;  //线程栈结束位置
  string  processMd5 = 11;  //进程MD5
  uint32  reportID = 12;

  string HookFuncName = 13;         // hook函数名
  repeated ProcessInfo ProcessInfoList = 20; // 进程链信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress           = 26; //指令所在地址
  string moduleName           = 27; //指令所在模块
  string hookName             = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
}

//二进制漏洞检测:返回栈中执行代码
message ReturnStackExec {
  RiskHeader header = 1;
  uint32  processID = 2;  //进程Id
  string  isX86Process = 3;  //是否32位进程(Yes/No)
  bytes   imagePath = 4;  //进程全路径
  uint64  address = 5;  //返回执行地址
  uint32  threadID = 6;  //线程Id
  uint64  stkBeginAddr = 7;  //线程栈开始位置
  uint64  stkEndAddr = 8;  //线程栈结束位置
  string  processMd5 = 9;  //进程MD5
  uint32  reportID = 10;

  repeated ProcessInfo ProcessInfoList = 20;// 进程信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress           = 26; //指令所在地址
  string moduleName           = 27; //指令所在模块
  string hookName             = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
}

//远线程注入
message RemoteThread {
  RiskHeader header = 1;
  int32   nHostPid = 2;  //注入者进程Id
  int32   nGuestPid = 3;  //被注入进程Id
  string  strGuestImageName = 4;  //被注入进程路径
  string  strHostImageName = 5;  //注入者进程路径
  string strGuestProcessName = 6; // 进程名
  string strHostProcessName = 7;
  string strHostProcessMd5 = 8;
}

// 无文件攻击: Dll内存反射式注入
message MemNoFileAttack {
  enum MemNoFileAttackType {
	REFLECTION_INJECTION = 0;//DLL反射攻击，当类别为反射攻击时，Target进程信息有效
	RUN_REMOTE_SCRIPT = 1;//远程执行脚本，脚本文件不落地
	SCRIPT_SCHEDULE_ADD = 2;
	SCRIPT_SCHEDULE_DEL = 3;
	SCRIPT_SCHEDULE_CHANGE = 4;
	SCRIPT_FILECOPY = 5;
	SCRIPT_DIRCOPY = 6;
	SCRIPT_HTTP_GET = 7;
	SCRIPT_HTTP_POST = 8;
	SCRIPT_WMICMD = 9;
	SCRIPT_LOADDLL = 10;
	SCRIPT_GETADDRESS = 11;
	SCRIPT_FILEMOVE = 12;
	SCRIPT_DIRMOVE = 13;
	SCRIPT_LISTEN = 14;
	SCRIPT_SOCKET = 15;
	SCRIPT_WIN32_SHARE = 16;
	WEB_SHELL_FILE = 17;//发现webshell文件
	WEB_SHELL_JAVA = 18;//发现java webshell
	WMI_PERSISTENT_BACKDOOR = 19;//wmi持久后门
	WEB_SHELL_JAVA1 = 20;//发现java webshell(实时监控)
	APPROT_WEB_SHELL_FILE_WRITE = 21; //写敏感在文件，有效字段 SourceProcess、doc_name
	APPROT_WEB_SHELL_NET_LISTEN = 22; //监听端口，有效字段 SourceProcess、TargetProcess、listen_port
	APPROT_WEB_SHELL_PROCESS_RUN = 23; //启动敏感子进程，有效字段 SourceProcess、TargetProcess
	SCRIPT_AMSI_BY_INIT_FAILED = 24; //过AMSI保护-通过设置InitFailed字段     不需定义oneof detail中的结构体
	SCRIPT_AMSI_BY_AMSI_CONTEXT = 25; //过AMSI保护-通过修改AMSICONTEXT结构体
	SCRIPT_AMSI_DLLHIJACK = 26; //过AMSI保护-通过Dll劫持
	SCRIPT_KEY_LOGGER = 27; //键盘记录器                            不需定义oneof detail中的结构体
	SCRIPT_SCREEN_SHOT = 28; //屏幕截图                              不需定义oneof detail中的结构体
	SCRIPT_EMAIL = 29; //发送邮件
	WMI_TERMINATE_PROCESS = 30; //结束进程
	WMI_REG_OPER = 31; //操作注册表
	WMI_SERVICE_OPER = 32; //操作服务
	WMI_QUERY = 33; //执行查询
	PATCH_EVENTLOG_GEN_LOG = 34; //Patch Eventlog GenLog
  }

  RiskHeader header = 1;
  ProcessInfo SourceProcess = 2;
  ProcessInfo TargetProcess = 3;
  MemNoFileAttackType AttackType = 4;
  string doc_name = 5; //对应文档名称，可能为空

  oneof detail{
	NoFileAttackScriptSchedule schedule = 21;
	NoFileAttackScriptFileCopy file_copy = 22;
	NoFileAttackScriptFileCopy file_move = 27;
	NoFileAttackScriptHttp http = 23;
	NoFileAttackScriptWmiCmd wim_cmd = 24;
	NoFileAttackScriptLoadDll load_dll = 25;
	NoFileAttackScriptGetAddress get_address = 26;
	NoFileAttackScriptListen listen_port = 28;
	NoFileAttackScriptSocket socket_communication = 29;
	NoFileAttackScriptWin32Share win32_share = 30;
	NoFileAttackWebShellJava web_shell_java = 31;
	NoFileAttackWMIPersistentBackdoor wmi_persistent_backdoor = 32;
	NoFileAttackWebShellJava1 web_shell_java1 = 33;
	NoFileAttackScriptAmsiByAmsiContext amsi_by_amsi_context = 34;
	NoFileAttackScriptAmsiDllHijack amsi_dllhijack = 35;
	NoFileAttackScriptEmail email = 36;
	NoFileAttackScriptWmiTerminateProcess wmi_terminate_process = 37;
	NoFileAttackScriptWmiOperReg wmi_reg_oper = 38;
	NoFileAttackScriptWmiOperService wmi_service_oper = 39;
	NoFileAttackScriptWmiExeQuery wmi_query = 40;
  }

  repeated ProcessInfo ProcessInfoList = 200;// 进程信息
  string ProcFlag = 201;  // 进程链标记
  string DumpFlag = 202;  // 内存dump标记
  string ScriptFlag = 203;  // 脚本标记
  uint64 EvidenceSize = 204; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 205;
  string MemStr = 206;  // 内存字符串
}

// 堆喷射
message MemHeapSpray {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  string HookFuncName = 4;   // hook函数名
  uint64 address = 5;   // 修改地址
  uint32 addrSize = 6;   //修改地址大小
  string InvalidInstruction = 7;  // 无效指令
  repeated ProcessInfo ProcessInfoList = 20; // 进程链信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 HeapBase = 26;//堆喷发生时申请的堆地址
  uint64 CheckBase = 27;//滑块
  uint64 HeapSize = 28;//堆块大小
  uint64 retAddress      = 29; //指令所在地址
  string moduleName      = 30; //指令所在模块
  string hookName        = 31; //hook点
  string CodeFragment = 32; // 调用函数的代码片段
  string MemStr = 33; // 内存字符串
}

// ROP
message MemRop {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;
  string remote_ip = 4; //可能为空

  string HookFuncName = 5;         // hook函数名
  string CheckContent = 6;         // 检测的8字节内容
  repeated ProcessInfo ProcessInfoList = 20; // 进程链信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress      = 26; //指令所在地址
  string moduleName      = 27; //指令所在模块
  string hookName        = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
}

// 布局shellcode
message MemLayoutShellCode {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  repeated ProcessInfo ProcessInfoList = 20;// 进程信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress      = 26;//指令所在地址
  string moduleName      = 27;//指令所在模块
  string hookName        = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
  string MemStr = 30; // 内存字符串
}


// 栈翻转
message MemStackPivot {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  string  HookFuncName = 4;  // hook函数名
  uint64  address = 5;  //修改地址
  uint32  addrSize = 6;  //修改地址大小
  string  memAttri = 7;  //新设置的内存属性
  uint64  stkBeginAddr = 8;  //线程栈开始位置
  uint64  stkEndAddr = 9;  //线程栈结束位置
//  uint64  RetAddress = 11; // 被检测的返回地址
  uint64  EspAddress = 12; // 当前的栈顶地址
  repeated ProcessInfo ProcessInfoList = 20; // 进程链信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 OldStackBase = 26;
  uint64 NewStackBase = 27;
  uint64 retAddress      = 28;//指令所在地址
  string moduleName      = 29;//指令所在模块
  string hookName        = 30; //hook点
  string CodeFragment = 31; // 调用函数的代码片段
  string MemStr = 32; // 内存字符串
}

// 启动进程
message MemStartProcess {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  string targetProcessFilePath = 3;
  string targetProcessCommandLine = 4;
}

// 执行ShellCode
message MemRunningShellCode {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  repeated ProcessInfo ProcessInfoList = 20;// 进程信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress      = 26;//指令所在地址
  string moduleName      = 27;//指令所在模块
  string hookName        = 28; //hook点
  string CodeFragment = 29; // 调用函数
  string MemStr = 30;   // 内存字符串
}



// 引擎攻击
message MemEngineAttack {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  repeated ProcessInfo ProcessInfoList = 20;// 进程信息
  string ProcFlag = 21;  // 进程链标记
  string DumpFlag = 22;  // 内存dump标记
  string ScriptFlag = 23;  // 脚本标记
  uint64 EvidenceSize = 24; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 25;
  uint64 retAddress      = 26;//指令所在地址
  string moduleName      = 27;//指令所在模块
  string hookName        = 28; //hook点
  string CodeFragment = 29; // 调用函数的代码片段
}


//脚本方式操作计划任务
message NoFileAttackScriptSchedule {
  string path = 1;
  string args = 2;
}

//脚本方式拷贝文件
message NoFileAttackScriptFileCopy {
  string src_path = 1;
  string dest_path = 2;
}

//脚本方式操作http
message NoFileAttackScriptHttp {
  string url = 1;
}

//脚本方式操作wmi
message NoFileAttackScriptWmiCmd {
  string cmd = 1;
  string remote_ip = 2; //可能为空
  bool   is_remote = 3; //是本地还是远程
}

//wmi结束进程
message NoFileAttackScriptWmiTerminateProcess {
  bool        is_remote = 1;    //是本地还是远程
  string      remote_ip = 2;    //远程ip(可能为空)
  string      target_process_path = 3;//要被结束的进程路径
}

//wmi操作注册表
message NoFileAttackScriptWmiOperReg {
  bool        is_remote = 1;    //是本地还是远程
  string      remote_ip = 2;    //远程ip(可能为空)
  string      oper_type = 3;    //操作类型
  string      reg_key_path = 4;    //操作注册表key路径
  string      reg_value_name = 5;    //操作注册表value名(可能为空)
}

//wmi操作服务
message NoFileAttackScriptWmiOperService {
  bool        is_remote = 1;    //是本地还是远程
  string      remote_ip = 2;    //远程ip(可能为空)
  string      oper_type = 3;    //操作类型
  string      service_name = 4;    //服务名称
  string      service_exe_path = 5;  //服务exe路径
}

//wmi执行查询
message NoFileAttackScriptWmiExeQuery {
  bool        is_remote = 1;    //是本地还是远程
  string      remote_ip = 2;    //远程ip(可能为空)
  string      query_language = 3;    //查询语言
  string      query_cmd = 4;    //查询内容
}

//脚本方式加载DLL镜像
message NoFileAttackScriptLoadDll {
  string path = 1;
}

//脚本方式操作获取api地址
message NoFileAttackScriptGetAddress {
  string api_name = 1;
}

//脚本方式监听端口
message NoFileAttackScriptListen {
  string    local_ip = 1;
  int32     local_port = 2;
}

//脚本方式socket通讯
message NoFileAttackScriptSocket {
  string    remote_ip = 1;
  int32     remote_port = 2;
}

//脚本方式共享文件夹
message NoFileAttackScriptWin32Share {
  string    share_name = 1;
  string    share_path = 2;
  string    remote_ip = 3; //远程ip(可能为空)
  bool      is_remote = 4; //是本地还是远程
}

//java web shell
message NoFileAttackWebShellJava {
  string    web_shell_name = 1;//shell风险名称
  string    web_shell_detail = 2;//shell风险详情,命中黑java类的信息
}

message NoFileAttackWMIPersistentBackdoor {
  string    obj_name = 1;
  string    wmi_context = 2;
  string    remote_ip = 3; //可能为空
}

message NoFileAttackWebShellJava1 {
  enum DetailInfoType {
	JVM_RUN_CMD = 0;// JVM进程执行外部命令
	JVM_LOAD_SO = 1;// JVM进程加载.so库
	JVM_DRAG_LIB = 2;// JVM进程执行拖库行为
	JVM_UPLOADINGFILE = 3;// JVM文件上传行为
	PHP_CALLABLE = 4;// PHP回调行为
	PHP_FILE_OP = 5;// PHP文件操作
	PHP_FILEUPLOAD = 6;// PHP文件上传
	PHP_DB = 7;// PHP数据库操作
	PHP_ENV = 10;// PHP Env操作
	PHP_SENSE_API = 11;// PHP敏感API调用
	JVM_REGISTER_SERVER_COMPONENT = 12;// JVM 注册服务器组件
	JVM_SERVER_WEBSHELL_SCAN = 13;// JVM 应用服务器Webshell扫描；
	JVM_SERVER_COMPONENT_SCAN = 14;// JVM 应用服务器已注册组件扫描；
	JVM_FILE_OP = 15; // java 读写敏感文件
	JVM_VIR_CMD = 16; // java 虚拟终端
	JVM_SCAN_PORT = 17;
	JVM_JAR_LOAD = 18;
	JVM_ZIP = 19;
	JVM_UNZIP = 20;
	JVM_HTTP_PARAMS_CHECK = 21;
	JVM_SQL_INJECT = 22; // java sql inject
	JVM_XXE = 23; // XXE
	JVM_DNSLOG = 24; // DNSLog
	JVM_SSRF = 25; // SSRF
	JVM_DIRECTORY_OP = 26;
	JVM_Black_IP = 27;
	JVM_CSRF = 29;
	JVM_UNSAFE_REDIRECT = 30;
	PHP_UNKNOWN = 200; //未知
	PHP_SYSTEM_COMMAND = 201; //系统命令执行
	PHP_CODE_EXEC = 202;//PHP代码执行
	PHP_CALLBACK_FUNC = 203;//回调函数利用
	PHP_UPLOAD_SUSPICIOUS_FILES = 204;//上传可疑文件
	PHP_SENSITIVE_FILE_ACCESS = 205;//敏感文件访问
	PHP_EXEC_SUSPICIOUS_FILE = 206;//执行可疑文件
	PHP_XSS = 207;// XSS
	PHP_SQLI = 208;// SQL 注入
	PHP_SQL_DRAG = 209; // SQL拖库
	PHP_UNDEAD_HORSE = 210; // PHP不死马 (新增)
	PHP_SSRF = 211;// PHP SSRF攻击
	PHP_XXE = 212; // PHP XXE

	GO_RCE = 300;// GO RCE
  }
  repeated string detail_info = 1;
  string    remote_ip = 2;
  DetailInfoType infotype = 3;
}

//过AMSI保护-通过修改AMSICONTEXT结构体
message NoFileAttackScriptAmsiByAmsiContext {
  string    modified_value = 1;
}

//过AMSI保护-通过Dll劫持
message NoFileAttackScriptAmsiDllHijack {
  string    dll_path = 1;
}

//发送邮件
message NoFileAttackScriptEmail {
  string    sender_addr = 1;
  string    recv_addr = 2;
}

message EtwRiskInfo {
  RiskHeader header = 1;
  ProcessInfo TargetProcessInfo = 2;  // 目标进程基础信息
  repeated ProcessInfo ProcessInfoList = 3;  // 源进程基础信息链
  string ProcFlag = 4;  // 进程链标记
  string DumpFlag = 5;  // 内存dump标记
  string ScriptFlag = 6;  // 脚本标记
  uint64 EvidenceSize = 7; // 证据大小
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 8;  // 证据信息链
}

// 具有内核漏洞驱动
message KernelVulnerableDriverRiskInfo {
  enum DriverRiskType {
	DRIVER_RISK_TYPE_UNKNOWN = 0;
	DRIVER_RISK_TYPE_MALICIOUS = 1;
	DRIVER_RISK_TYPE_VULNERABLE = 2;
  }
  RiskHeader header = 1;
  string DriverName = 2; // 驱动名称
  string DriverPath = 3; // 驱动路径
  DriverRiskType RiskType = 4; // 驱动风险类型
  repeated ProcessInfo DriverFileList = 5; // 驱动文件信息，先按照数组来定义
  string OriginFileName = 6;    // 原始文件名
  ProcessInfo Process = 7;  // 加载当前驱动的进程信息
}


// 0地址利用
message NullAddressAttack {
  RiskHeader header = 1;
  string HookFuncName = 2;  // hook函数名
  uint64 address = 3;  // 修改地址
  uint32 addrSize = 4;  // 修改地址大小
  string memAttri = 5;  // 新设置的内存属性
  uint32 FuncFlags = 6;  // 函数参数flags
  repeated ProcessInfo ProcessInfoList = 20; // 进程链信息
}

// 隧道通信
message TunnelRiskInfo {
  RiskHeader header = 1;
  ProcessInfo Process = 2;
  uint32 reportID = 3;

  string src_ip = 5; // 源IP
  uint32 src_port = 6; // 源端口
  string dst_ip = 7; // 目的IP
  uint32 dst_port = 8; // 目的端口

  enum TunnelType {
	TUNNEL_TYPE_ICMP = 0; // ICMP隧道
	TUNNEL_TYPE_DNS = 1; // DNS隧道
	TUNNEL_TYPE_TCP = 2; // TCP三次握手隧道
  }
  TunnelType tunnel_type = 9; // 隧道类型

  oneof detail{
	TUNNEL_INFO_ICMP info_icmp = 10; // ICMP隧道信息
	TUNNEL_INFO_DNS info_dns = 11; // DNS隧道信息
	TUNNEL_INFO_TCP info_tcp = 12; // TCP三次握手隧道信息
  }
  repeated ProcessInfo ProcessInfoList = 20; // 进程信息
  repeated ReportEvidenceInfo ReportEvidenceInfoList = 21;
}

// icmp隧道信息
message TUNNEL_INFO_ICMP {
  uint32 type = 1; // icmp固定字段
  uint32 code = 2; // icmp固定字段
  uint32 checksum = 3; // icmp固定字段
  uint32 other = 4; // icmp可变字段，不同的type，other字段含义不同

  // 数据包内容
  string hex_data = 5; // 01020304
  string str_data = 6; // ..abc..ls..

  // 可疑信息
  enum IcmpDetectType {
	ICMP_DETECT_TYPE_ICMP_NORMAL = 0; // 0
	ICMP_DETECT_TYPE_ICMP_HIGH_FREQUENCY = 1; // 2^0 icmp高频发包
	ICMP_DETECT_TYPE_ICMP_KEYWORD = 2; // 2^1 检出关键字
	ICMP_DETECT_TYPE_ICMP_BIG_PACKET = 4; // 2^2 icmp大包
	ICMP_DETECT_TYPE_ICMP_NO_STAMP = 8; // 2^3 icmp 没有时间戳
	ICMP_DETECT_TYPE_ICMP_ABNORMAL_ENTROPY = 16; // 2^4 icmp data字段熵异常
  }
  IcmpDetectType detect_flag = 7; // 每一个bit位代表一种检测点
  repeated uint32 offest = 8; // ICMP可疑关键字从str_data的开始的偏移
  repeated uint32 len = 9; // ICMP可疑关键字长度
}

message Query {
  string name = 1; // 查询名
  uint32 type = 2; // 查询类型
  uint32 class = 3; // 查询类
}

message Answer {
  string name = 1; // 回答名
  uint32 type = 2; // 回答类型
  uint32 class = 3; // 回答类
  uint32 ttl = 4; // 回答生存时间
}

// dns隧道信息
message TUNNEL_INFO_DNS {
  uint32 id = 1; // dns固定字段
  uint32 flags = 2; // dns固定字段
  uint32 questions = 3; // dns固定字段
  uint32 answer_rrs = 4; // dns固定字段
  uint32 authority_rrs = 5; // dns固定字段
  uint32 additional_rrs = 6; // dns固定字段
  repeated Query queries = 7; // dns查询信息 可变
  repeated Answer answers = 8; // dns回答信息 可变

  // 数据包内容
  string hex_data = 9; // 01020304
  string str_data = 10; // ..abc..ls..

  // 可疑信息
  enum DnsDetectType {
	DNS_DETECT_TYPE_DNS_NORMAL = 0; // 0
	DNS_DETECT_TYPE_DNS_HIGH_FREQUENCY = 1; // 2^0 dns高频发包
	DNS_DETECT_TYPE_DNS_ANS_ALWAYS_LESS_EQUAL_1 = 2; // 2^1 dns回答数<=1
	DNS_DETECT_TYPE_DNS_QUERY_LONG = 4; // 2^2 dns查询长
  }
  DnsDetectType detect_flag = 11; // 每一个bit位代表一种检测点
  repeated uint32 offest = 12; // DNS可疑关键字偏移从str_data的开始的偏移
  repeated uint32 len = 13; // DNS可疑关键字长度
}

// tcp三次握手隧道信息
message TUNNEL_INFO_TCP {
  uint32 src_port = 1; // 源端口
  uint32 dst_port = 2; // 目的端口
  uint32 seq_num = 3; // TCP序列号
  uint32 ack_num = 4; // TCP确认号
  uint32 data_offset = 5; // 数据偏移
  uint32 flags = 6; // TCP标志
  uint32 window_size = 7; // 窗口大小
  uint32 checksum = 8; // TCP校验和
  uint32 urgent_ptr = 9; // 紧急指针
  repeated string options = 10; // TCP选项 可变

  // 数据包内容
  string hex_data = 11; // 01020304
  string str_data = 12; // ..abc..ls..

  // 可疑信息
  enum TcpDetectType {
	TCP_DETECT_TYPE_TCP_NORMAL = 0; // 0
	TCP_DETECT_TYPE_TCP_HIGH_FREQUENCY = 1; // 2^0 tcp高频发包
	TCP_DETECT_TYPE_TCP_WITH_ADDITIONAL_DATA = 2; // 2^1 tcp syn包携带额外数据
  }
  TcpDetectType detect_flag = 13; // 每一个bit位代表一种检测点
  repeated uint32 offest = 14; // TCP可疑关键字偏移从str_data的开始的偏移
  repeated uint32 len = 15; // TCP可疑关键字长度
}

