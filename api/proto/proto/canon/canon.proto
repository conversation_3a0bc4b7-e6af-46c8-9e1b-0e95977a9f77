syntax = "proto3";
package canon;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "git.anxin.com/v01-cluster/vapi/pkg/canon";

service Canon {
  // 根据指定的查询条件, 流式提取事件列表信息, 详情信息和溯源图信息
  rpc GetEventListStream(EventListStreamReq) returns (stream EventListStreamResp) {}
  // 流式上传事件列信息, 详情信息和溯源图信息, 用于导入事件数据
  rpc PutEventListStream(stream PutEventListStreamReq) returns (google.protobuf.Empty) {}
}

// 错误码
message ErrorCodes {
  // 错误码枚举
  enum Enum {
    Unknown = 0;
  }
}

// 事件查询条件
message EventListStreamReq {
  repeated string host_ids = 1;
  repeated string risk_types = 2;
  repeated uint32 risk_levels = 3;
  google.protobuf.Timestamp time_since = 4;
  google.protobuf.Timestamp time_until = 5;
  bool archived = 6; // 是否查询归档数据
}

// 单条事件信息, 包含列表摘要, 详情和溯源图数据
message EventListStreamItem {
  int64 id = 1;
  string host_id = 2;
  google.protobuf.Timestamp updated_at = 3;
  google.protobuf.Timestamp created_at = 4;
  bytes details = 5; // 详情页数据
  bytes graph_details = 6; // 溯源图数据
}

// 单个流数据包的内容结构, 包含一组事件信息
message EventListStreamResp {
  repeated EventListStreamItem items = 1;
}

message PutEventListStreamReq {
  repeated EventListStreamItem items = 1;
}
