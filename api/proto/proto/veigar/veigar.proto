syntax = "proto3";
package veigar;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/veigar";

service Veigar {
  // 基于md5批量搜索文件情报
  rpc SearchFilesIntel(SearchFilesReq) returns (SearchFilesResp) {}

  // 快速判断文件情报是否存在，接口将返回存在和不存在的md5列表，不会返回具体情报内容
  // 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
  rpc FilesIntelExistsBF(SearchFilesReq) returns (FilesIntelExistsResp) {}

  // 批量添加文件情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
  rpc AddFilesIntel(AddFilesReq) returns (AddFilesResp) {}

  // 基于ip或域名批量查询外联情报
  rpc SearchHostsIntel(SearchHostsReq) returns (SearchHostsResp) {}

  // 快速判断外联情报是否存在，接口将返回存在和不存在的host列表，不会返回具体情报内容
  // 此接口只在布隆过滤器中查询，若布隆过滤器出现问题或正在重建，将返回base.Error，code: 12001
  rpc HostsIntelExistsBF(SearchHostsReq) returns (HostsIntelExistsResp) {}

  // 批量添加外联情报，接口返回添加成功及失败的数量，添加过程中某条失败不会返回err
  rpc AddHostsIntel(AddHostsReq) returns (AddHostsResp) {}
}


// 情报中心异常状态码
enum Codes {
  CODE_UNKNOWN = 0;
  ERROR_BLOOM_FILTER_BUILDING = 10001;  // 布隆过滤器正在构建中，无法对外提供服务，client侧需妥善处理
}


message SearchFilesReq {
  repeated string md5s = 1; // 文件md5
  bool only_db = 2; // 仅在数据库中查询，文件检测服务有此需求，一般不要指定为true
}


enum FileIntelState {
  FILE_UNKNOWN = 0; // 文件情报库不存在该文件情报，结果未知
  FILE_WHITE = 1; // 文件为白
  FILE_BLACK = 2; // 文件为黑
  FILE_GRAY = 3; // 文件为灰
}

enum FileIntelSource {
  FILE_SOURCE_UNKNOWN = 0; // 默认值安全
  FILE_SOURCE_LIB = 1; // 情报库
  FILE_SOURCE_WFY = 2; // 网防云
  FILE_SOURCE_CUSTOMER = 3;// 用户自定义
  FILE_SOURCE_DETECT = 4; // 检测结果写入
}

enum Severity {
  UNKNOWN = 0;
  LOW = 1;
  MIDDLE = 2;
  HIGH = 3;
}

message FileIntel {
  string md5 = 1; // 文件md5
  FileIntelState state = 2; // 文件判断结果
  FileIntelSource source = 3; // 情报来源
  string vendor = 4; // 情报厂商
  int32 confidence = 5; // 置信度
  Severity severity = 6; // 严重程度
  repeated string malware_families = 7; // 病毒家族
  repeated int32 threat_types = 8; // 风险类型
  repeated string apt_org = 9; // APT组织
  repeated string tags = 10; // 情报标签
  string extra_info = 11; // 不同来源可设置额外信息
}

message SearchFilesResp {
  repeated FileIntel intel = 1;
}

message FilesIntelExistsResp {
  repeated string unknown = 1;
  repeated string exists = 2;
}

message AddFilesReq {
  repeated FileIntel intel = 1;
}

message AddFilesResp {
  int64 successful_num = 1;
  int64 failed_num = 2;
}


message SearchHostsReq {
  repeated string hosts = 1; // ip或域名列表
  bool only_db = 2; // 仅在数据库中查询，文件检测服务有此需求，一般不要指定为true
}

enum HostIntelState {
  HOST_UNKNOWN = 0; // 文件情报库不存在该文件情报，结果未知
  HOST_WHITE = 1; // 文件为白
  HOST_BLACK = 2; // 文件为黑
}

enum HostIntelSource {
  HOST_SOURCE_UNKNOWN = 0; // 默认值安全
  HOST_SOURCE_LIB = 1; // 情报库
  HOST_SOURCE_WFY = 2; // 网防云
  HOST_SOURCE_CUSTOMER = 3;// 用户自定义
}

message HostIntel {
  string host = 1; // ip或域名
  HostIntelState state = 2; // 文件判断结果
  HostIntelSource source = 3; // 情报来源
  repeated int32 ports = 4;
  string vendor = 5; // 情报厂商
  int32 confidence = 6; // 置信度
  Severity severity = 7; // 严重程度
  repeated string malware_families = 8; // 病毒家族
  repeated int32 threat_types = 9; // 风险类型
  repeated string apt_org = 10; // APT组织
  repeated string tags = 11; // 情报标签
  string extra_info = 12; // 不同来源可设置额外信息
}

message SearchHostsResp {
  repeated HostIntel intel = 1;
}

message HostsIntelExistsResp {
  repeated string unknown = 1;
  repeated string exists = 2;
}

message AddHostsReq {
  repeated HostIntel intel = 1;
}

message AddHostsResp {
  int64 successful_num = 1;
  int64 failed_num = 2;
}