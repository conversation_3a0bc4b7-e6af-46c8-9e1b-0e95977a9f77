syntax = "proto3";
package conan;
import "conan/common.proto";
import "google/protobuf/timestamp.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

enum FileOperationType {
  FOT_UNKNOWN = 0;
  ISOLATE     = 1;
  DELETE      = 2;
  RESTORE     = 3;
  EXPIRE      = 4;
}

enum FileOperateStatus {
  FOS_UNKNOWN = 0;
  PENDING     = 1;
  PROCESSING  = 2;
  SUCCESS     = 3;
  FAILED      = 4;
}

message HandleAgentFileReq {
  string machine_id              = 1;
  string file_sha256             = 2;
  string file_key                = 3;
  int64 online                   = 4;
  FileOperationType operate_type = 5;
  int64 evidence_task_id         = 6;
}

message HandleAgentFileResp {}

message BatchHandleAgentFileReq {
  repeated HandleAgentFileReq list = 1;
}

message BatchHandleAgentFileResp {}

message RetryHandleAgentFileReq {
  string machine_id              = 1;
  string file_sha256             = 2;
  string file_key                = 3;
  int64 online                   = 4;
  FileOperationType operate_type = 5;
  string password                = 6;
}

message RetryHandleAgentFileResp {
  string password                  = 1;
  repeated HandleAgentFileReq list = 2;
}

message OperatedThreatenFile {
  ClueType clue_type                   = 1;
  int32 clue_sub_type                  = 2;
  int32 clue_status                    = 3;
  string machine_id                    = 4;
  string file_key                      = 5;
  string file_md5                      = 6;
  string file_sha256                   = 7;
  string file_name                     = 8;
  string file_path                     = 9;
  int32 file_type                      = 10;
  int32 file_size                      = 11;
  int32 operation_type                 = 12;
  int32 operation_status               = 13;
  int32 operation_failure              = 14;
  int32 file_source                    = 15;
  int64 evidence_task_id               = 16;
  google.protobuf.Timestamp created_at = 17;
}

message ListOperatedThreatenFileReq {
  repeated string machine_ids = 1;
  string file_sha256          = 2;
  string filename             = 3;
  TimeRange time_range        = 4;
  PageRequest page            = 5;
  // TRUE: 查询删除成功的文件
  // FALSE: 查询除删除成功外的其他文件
  bool is_delete_success = 6;
}

message ListOperatedThreatenFileResp {
  repeated OperatedThreatenFile files = 1;
  PageResponse page                   = 2;
}

message BatchDeleteFileOperationsFilter {
  string machine_id  = 1;
  string file_sha256 = 2;
}

message BatchDeleteFileOperationsReq {
  repeated BatchDeleteFileOperationsFilter filters = 1;
}

message BatchDeleteFileOperationsResp {
  int64 deleted_count = 1;
}

// AgentFileService is the service for agent file.
// The file operation includes: Isolate, Delete, Restore, Expire
service AgentFileService {
  // HandleAgentFile handles the agent file.
  rpc HandleAgentFile(HandleAgentFileReq) returns (HandleAgentFileResp);

  // BatchHandleAgentFile handles the agent file in batch.
  rpc BatchHandleAgentFile(BatchHandleAgentFileReq) returns (BatchHandleAgentFileResp);

  // RetryHandleAgentFile retries to handle the agent file.
  rpc RetryHandleAgentFile(RetryHandleAgentFileReq) returns (RetryHandleAgentFileResp);

  // ListOperatedThreatenFiles lists the operated threaten files.
  rpc ListOperatedThreatenFiles(ListOperatedThreatenFileReq) returns (ListOperatedThreatenFileResp);

  // BatchDeleteFileOperations deletes the file operation records in batch.
  rpc BatchDeleteFileOperations(BatchDeleteFileOperationsReq) returns (BatchDeleteFileOperationsResp);

  // ListAgentFileOperations lists the agent file operations.
  rpc ListAgentFileOperations(ListAgentFileOperationReq) returns (ListAgentFileOperationResp);
}

message ListAgentFileOperationReq {
  string machine_id    = 1;
  string file_sha256   = 2;
  TimeRange time_range = 4;
  PageRequest page     = 5;
}

message ListAgentFileOperationResp {
  repeated AgentFileOperation file_operations = 1;
  PageResponse page                           = 2;
}

message AgentFileOperation {
  int64 id                              = 1;
  string machine_id                     = 2;
  string file_sha256                    = 3;
  string file_path                      = 4;
  string file_key                       = 5;
  FileOperationType operation_type      = 6;
  FileOperateStatus operation_status    = 7;
  google.protobuf.Timestamp create_time = 8;
}