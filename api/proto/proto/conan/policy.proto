syntax = "proto3";
package conan;
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "conan/common.proto";

option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

// PolicyService 策略管理服务
// 负责处理黑白名单、忽略规则等策略相关操作
service PolicyService {
  // GetAttributeInfo 获取线索属性信息
  rpc GetAttributeInfo(AttributeInfoRequest) returns (AttributeInfoResponse);

  // SetIgnoreStatus 设置线索忽略状态
  rpc SetIgnoreStatus(IgnoreStatusRequest) returns (IgnoreStatusResponse);

  // CreateBWPolicy 创建白名单策略(黑名单暂时不支持)
  rpc CreateBWPolicy(BWPolicy) returns (CreateBWPolicyResp);

  // DeleteBWPolicy 删除白名单策略(黑名单暂时不支持)
  rpc DeleteBWPolicy(DeleteBWPolicyReq) returns (google.protobuf.Empty);

  // UpdateBWPolicy 更新白名单策略(黑名单暂时不支持)
  rpc UpdateBWPolicy(BWPolicy) returns (google.protobuf.Empty);

  // SetRiskCategories create or update risk categories.
  rpc SetRiskCategories(SetRiskCategoriesReq) returns (SetRiskCategoriesResp);
}

// AttributeInfoRequest 获取属性信息请求
message AttributeInfoRequest {
  ClueType type    = 1;  // 线索类型
  string attribute = 2;  // 属性名称
  string clue_key  = 3;  // 线索唯一标识(可选,用于获取具体属性值)
}

// AttributeInfoResponse 属性信息响应
message AttributeInfoResponse {
  repeated string attributes = 1;  // 属性名称列表
  repeated int32 rules       = 2;  // 规则列表
  repeated string values     = 3;  // 属性值列表
}

// IgnoreStatusRequest 设置忽略状态请求
message IgnoreStatusRequest {
  ListAttackCluesFilter filter = 1;  // 过滤条件
  ClueType type                = 2;  // 线索类型
  bool is_select_all           = 3;  // 是否全选(true:ids为排除项,false:ids为选中项)
  repeated int64 ids           = 4;  // 线索ID列表
  IgnoreAction action          = 5;  // 忽略动作

  enum IgnoreAction {
    IGNORE_ACTION_UNSPECIFIED = 0;
    IGNORE_ACTION_CANCEL      = 1;  // 取消忽略
    IGNORE_ACTION_SET         = 2;  // 设置忽略
  }
}

// IgnoreStatusResponse 设置忽略状态响应
message IgnoreStatusResponse {
  int32 affected_count = 1;  // 受影响的记录数
}

// PolicyRule 策略规则
message BWPolicyRule {
  string attribute       = 1;  // 属性名(如:md5、进程名、路径等)
  int32 condition        = 2;  // 匹配条件
  int32 scope            = 3;  // 匹配范围
  repeated string values = 4;  // 属性值列表
}

// BWPolicy 策略定义
message BWPolicy {
  int64 id                         = 1;  // 策略ID
  string description               = 2;  // 策略描述
  repeated int64 machine_group_ids = 3;  // 机器分组ID列表

  // 这个设计不是很合理，很容易引起歧义，应该传递两个参数表示一级和二级线索类型,
  // 暂时兼容前端的传递方式。文件威胁和外联告警时表示一级线索类型，内存告警和系统攻击时表示二级线索类型
  repeated uint32 risk_types = 4;  // 风险类型列表,

  repeated BWPolicyRule rules           = 5;  // 规则列表
  int64 creator_id                      = 6;  // 创建人ID
  int64 updater_id                      = 7;  // 更新人ID
  google.protobuf.Timestamp create_time = 8;  // 创建时间
  google.protobuf.Timestamp update_time = 9;  // 更新时间
}

message CreateBWPolicyResp {
  repeated BWPolicy policies = 1;
}

message DeleteBWPolicyReq {
  repeated int64 ids = 1;
}

message UpdateBWPolicyReq {
  repeated BWPolicy new_policies = 1;
}

message RiskCategory {
  int64 id       = 1;
  string name    = 2;
  string zh_name = 3;
}

message SetRiskCategoriesReq {
  repeated RiskCategory categories = 1;
}

message SetRiskCategoriesResp {
  repeated RiskCategory categories = 1;
}
