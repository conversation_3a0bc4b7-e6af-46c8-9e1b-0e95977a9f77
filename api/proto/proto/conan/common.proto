syntax = "proto3";
package conan;
import "google/protobuf/timestamp.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

message TimeRange {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time   = 2;
}

// 分页请求
message PageRequest {
  int32 page_index = 1;
  int32 page_size  = 2;
}

// 分页响应
message PageResponse {
  int32 page_index = 1;
  int32 page_size  = 2;
  int64 total      = 3;
}

// 线索类型枚举定义
enum ClueType {
  CT_UNKNOWN       = 0;
  MALICIOUS_FILE   = 1;
  ILLEGAL_OUTREACH = 2;
  MEMORY_ATTACK    = 3;
  SCRIPT_ATTACK    = 4;
}

enum OutreachType {
  OT_UNKNOWN = 0;
  IP         = 1;
  DOMAIN     = 2;
  URL        = 3;
}

// 外联命中来源
enum HitSource {
  HS_UNKNOWN = 0;  // protobuf 建议枚举第一个值为0
  HS_PUBLIC  = 1;  // 命中共有情报
  HS_PRIVATE = 2;  // 命中私有情报
}

// APT命中状态
enum AptStatus {
  AS_UNKNOWN = 0;
  AS_HIT     = 1;  // 命中
  AS_NOT_HIT = 2;  // 未命中
}

// 过滤条件
message ListAttackCluesFilter {
  int32 target_type   = 1;  // 目标类型
  int32 hit_status    = 2;  // 命中状态
  int32 is_apt        = 3;  // APT情报 (0-否 1-是)
  int32 clue_sub_type = 4;  // 威胁类型
  int32 has_malicious = 5;  // 是否存在恶意代码 (0-无 1-有)

  // 终端信息
  string ip = 6;  // 终端IP

  ClueType clue_type = 8;

  enum ClueLevel {
    CL_UNKNOWN = 0;
    LOW        = 1;
    MEDIUM     = 2;
    HIGH       = 3;
  }
  ClueLevel clue_level = 9;

  enum DisposeStatus {
    DS_UNKNOWN  = 0;
    PROCESSED   = 1;
    UNPROCESSED = 2;
  }
  DisposeStatus dispose_status = 10;

  // 搜索参数
  enum SearchType {
    ST_UNKNOWN  = 0;
    BY_IP       = 1;
    BY_MD5      = 2;
    BY_HOSTNAME = 3;
    BY_SHA256   = 4;
  }
  SearchType search_type = 11;
  string search_data     = 12;

  // 文件分类
  enum FileCategory {
    FC_UNKNOWN  = 0;
    BLACK       = 1;
    WHITE       = 2;
    GRAY        = 3;
    QUASI_WHITE = 4;
  }

  FileCategory file_type = 13;

  // 分析参数
  int32 confidence = 14;  // 置信度 0-100
  string direction = 15;  // 流量方向

  // 攻击信息
  string attack_src = 16;  // 攻击源

  // 分组信息
  repeated int64 group_id = 17;  // 分组ID列表

  // 在线状态
  enum OnlineStatus {
    ONLINE  = 0;
    OFFLINE = 1;
  }
  OnlineStatus online = 18;

  // 文件特征
  string file_name        = 19;  // 文件名
  string outreach_address = 20;  // 外联地址

  // 记录标识
  int64 id                     = 21;  // 记录ID
  bool report                  = 22;  // 是否生成报告
  repeated string file_sha256s = 23;  // internal used by file threat clues
  TimeRange time_range         = 24;  // 时间范围
}
