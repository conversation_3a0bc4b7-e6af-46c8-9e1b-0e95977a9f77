syntax = "proto3";
package conan;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

// 50001 ~ 50999 为conan定义的错误码
// 线索 - 50001 - 50100
// 处置/白名单 - 50101 - 50200
// 取证 - 50201 - 50300
enum Codes {
  UNDEFINED      = 0;
  CLUE_NOT_FOUND = 50001;

  BWPolicyNotFound = 50101;  // 白名单策略不存在

  EVIDENCE_TASK_NOT_FOUND      = 50201;  // 取证任务不存在
  EVIDENCE_TASK_FILE_TOO_LARGE = 50202;  // 取证任务文件太大
  EVIDENCE_TASK_ALREADY_EXISTS = 50203;  // 取证任务已存在
  ADD_EVIDENCE_TASK_FAILED     = 50204;  // 添加取证任务失败
  CLUE_EVIDENCE_NOT_FOUND      = 50205;  // 线索证据不存在
  TIME_OUT_OF_RANGE            = 50206;  // 参数错误, 传入时间为未来的时间
  EXTRACTION_TIME_HAS_EXPIRED  = 50207;  // 证据已过期
  NOT_SUPPORT_EVIDENCE_TYPE    = 50208;  // 不支持的取证类型
  EVIDENCE_RESULT_PUSH_FAILED  = 50209;  // 证据结果推送失败
  EVIDENCE_HOST_NOT_FOUND      = 50210;  // 取证任务未关联终端
  EVIDENCE_ALREADY_EXISTS      = 50211;  // 证据已存在
  HOST_NOT_FOUND               = 50212;  // 终端不存在
  HOST_OFFLINE                 = 50213;  // 终端已离线
  NO_CLUE_TYPE_FOR_X01         = 50214;  // 未找到与X01对应的线索类型
  PARSE_CSV_FAILED             = 50215;  // 解析csv文件失败
  BATCH_FILE_EVIDENCE_EXISTS   = 50216;  // 导入的csv文件对应的所有文件证据均已存在
}
