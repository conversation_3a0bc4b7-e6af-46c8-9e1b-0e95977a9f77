syntax = "proto3";
package conan;
import "google/protobuf/timestamp.proto";
import "conan/common.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

// 仪表盘服务
// 统计最近7天数据，离线统计
service DashboardService {
  // 终端统计
  rpc GetTerminalStats(GetTerminalStatsRequest) returns (TerminalStats) {}

  // 风险终端列表
  rpc ListTopRiskTerminals(ListTopRiskTerminalsRequest) returns (ListTopRiskTerminalsResponse) {}

  // 最近N天的威胁趋势
  rpc ListRecentThreatTrends(ListRecentThreatTrendsRequest) returns (ListRecentThreatTrendsResponse) {}

  // 文件归档统计
  rpc GetFileArchiveStats(GetFileArchiveStatsRequest) returns (FileArchiveStats) {}

  // 最近N天的外联趋势
  rpc ListRecentOutreachTrends(ListRecentOutreachTrendsRequest) returns (ListRecentOutreachTrendsResponse) {}

  // 获取按照威胁等级和威胁类型统计的线索数量
  rpc CountCluesByLevel(CountCluesByLevelReq) returns (CountCluesByLevelResp);

  // 获取按照威胁类型统计的线索数量
  rpc CountCluesByType(CountCluesByTypeReq) returns (CountCluesByTypeResp);
}

message GetTerminalStatsRequest {
  TimeRange time_range = 1;
}

message TerminalStats {
  int32 total        = 1;
  int32 online       = 2;
  int32 offline      = 3;
  int32 risk         = 4;
  double online_rate = 5;
}

message RiskTerminal {
  int64 id           = 1;
  string machine_id  = 2;
  string ip          = 3;
  string name        = 4;
  string group       = 5;
  int32 threat_count = 6;
}

message ListTopRiskTerminalsRequest {
  TimeRange time_range = 1;
}

message ListTopRiskTerminalsResponse {
  repeated RiskTerminal terminals = 1;
}

message ThreatTrendItem {
  string day                    = 1;  // format example: 2025-01-01
  map<int32, int32> level_stats = 2;
  int32 threat_count            = 3;
}

message ListRecentThreatTrendsRequest {
  TimeRange time_range = 1;
}

message ListRecentThreatTrendsResponse {
  repeated ThreatTrendItem trends = 1;
}

message GetFileArchiveStatsRequest {
  TimeRange time_range = 1;
}

message FileArchiveStats {
  int32 black   = 1;
  int32 white   = 2;
  int32 gray    = 3;
  int32 unknown = 4;
}

message ListRecentOutreachTrendsRequest {
  TimeRange time_range = 1;
}

message ListRecentOutreachTrendsResponse {
  repeated OutreachTrendItem trends = 1;
}

message OutreachTrendItem {
  string day          = 1;  // format example: 2025-01-01
  int32 total_count   = 2;
  int32 illegal_count = 3;
}

message CountCluesByLevelReq {
  TimeRange time_range = 1;
}

message CountCluesByLevelResp {
  enum ClueLevel {
    ClueLevelUnknown = 0;
    ClueLevelLow     = 1;
    ClueLevelMedium  = 2;
    ClueLevelHigh    = 3;
  }
  enum ClueKind {
    // 未指定类型
    CLUE_KIND_UNSPECIFIED = 0;

    // APT攻击
    CLUE_KIND_APT = 1;

    // 勒索攻击
    CLUE_KIND_EXTORTION = 2;

    // 挖矿
    CLUE_KIND_MINING = 3;

    // 僵尸网络
    CLUE_KIND_ZOMBIE = 4;

    // 漏洞利用
    CLUE_KIND_USE_LEAK = 5;

    // 后门
    CLUE_KIND_BACKDOOR = 6;

    // 间谍软件
    CLUE_KIND_SPY = 7;

    // 外联
    CLUE_KIND_OUTREACH = 8;

    // 病毒
    CLUE_KIND_VIRUS = 9;

    // 蠕虫
    CLUE_KIND_WORM = 10;

    // 脚本
    CLUE_KIND_SCRIPT = 11;

    // 木马
    CLUE_KIND_TROJANS = 12;

    // 其他
    CLUE_KIND_OTHER = 13;
  }
  message ClueKindCount {
    ClueKind kind                                = 1;
    int64 count                                  = 2;
    google.protobuf.Timestamp latest_create_time = 3;
  }
  int64 total_count = 1;
  // The number of clues by level
  // key: clue level
  map<int32, int64> level_counts = 2;
  // The number of clues by kind
  repeated ClueKindCount kind_counts = 3;
}

message CountCluesByTypeReq {
  TimeRange time_range = 1;
}

message CountCluesByTypeResp {
  map<int32, int64> counts = 1;  // 避免与ClueTypeCount冲突
  int64 total_count        = 2;
}
