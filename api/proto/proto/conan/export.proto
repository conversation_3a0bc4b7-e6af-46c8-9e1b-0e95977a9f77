syntax = "proto3";
package conan;
import "conan/common.proto";
import "conan/clue.proto";
// import "google/protobuf/timestamp.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

// ExportService provides streaming export functionality for various clue types
service ExportService {
  // ExportMemoryAttackClues streams memory attack clues for export
  rpc ExportMemoryAttackClues(ExportMemoryAttackCluesReq) returns (stream ExportMemoryAttackCluesResp);

  // ExportIllegalOutreachClues streams illegal outreach clues for export
  rpc ExportIllegalOutreachClues(ExportIllegalOutreachCluesReq) returns (stream ExportIllegalOutreachCluesResp);

  // ExportFileThreatClues streams file threat clues for export
  rpc ExportFileThreatClues(ExportFileThreatCluesReq) returns (stream ExportFileThreatCluesResp);

  // ExportSystemAttackClues streams system attack clues for export
  rpc ExportSystemAttackClues(ExportSystemAttackCluesReq) returns (stream ExportSystemAttackCluesResp);
}

// Export request for memory attack clues
message ExportMemoryAttackCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

// Export response for memory attack clues
message ExportMemoryAttackCluesResp {
  repeated ClueDetail clues = 1;
  ClueType clue_type        = 2;
  int64 batch_size          = 3;
}

// Export request for illegal outreach clues
message ExportIllegalOutreachCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

// Export response for illegal outreach clues
message ExportIllegalOutreachCluesResp {
  repeated ClueDetail clues = 1;
  ClueType clue_type        = 2;
  int64 batch_size          = 3;
}

// Export request for file threat clues
message ExportFileThreatCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

// Export response for file threat clues
message ExportFileThreatCluesResp {
  ClueDetail clue    = 1;
  ClueType clue_type = 2;
}

// Export request for system attack clues
message ExportSystemAttackCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

// Export response for system attack clues
message ExportSystemAttackCluesResp {
  repeated ClueDetail clues = 1;
  ClueType clue_type        = 2;
  int64 batch_size          = 3;
}
