syntax = "proto3";
package conan;
import "google/protobuf/timestamp.proto";
import "conan/common.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

service ClueService {
  // ListMemoryAttackClues lists all memory attack clues
  rpc ListMemoryAttackClues(ListMemoryAttackCluesReq) returns (ListMemoryAttackCluesResp);

  // ListFileThreatClues lists all file threat clues
  rpc ListFileThreatClues(ListFileThreatCluesReq) returns (ListFileThreatCluesResp);

  // ListSystemAttackClues lists all system attack clues
  rpc ListSystemAttackClues(ListSystemAttackCluesReq) returns (ListSystemAttackCluesResp);

  // ListIllegalOutreachClues lists all illegal outreach clues
  rpc ListIllegalOutreachClues(ListIllegalOutreachCluesReq) returns (ListIllegalOutreachCluesResp);

  // CountCluesBySha256 counts the number of file threat clues corresponding to
  // the file sha256
  rpc CountCluesBySha256(CountCluesBySha256Req) returns (CountCluesBySha256Resp);

  // ListTopMachineClueCounts lists the top N machine clue counts
  rpc ListTopMachineClueCounts(ListTopMachineClueCountsReq) returns (ListTopMachineClueCountsResp);

  // GetClueStats gets the clue stats
  rpc GetClueStats(GetClueStatsReq) returns (ClueStats);

  // ListOutreachAffectedTerminals lists the affected terminals of outreach
  rpc ListOutreachAffectedTerminals(ListOutreachAffectedTerminalsReq) returns (ListOutreachAffectedTerminalsResp);

  // CountOutreachCluesByType counts the number of outreach clues grouped by
  // outreach type
  rpc CountOutreachCluesByType(CountOutreachCluesByTypeReq) returns (CountOutreachCluesByTypeResp);

  // ListClueTypeCounts return the count of clues based on the input clue_type
  // When clue_type = 0, returns counts grouped by clue_type
  // When clue_type > 0, returns counts grouped by clue_sub_type for the
  // specified clue_type
  rpc ListClueTypeCounts(ListClueTypeCountsReq) returns (ListClueTypeCountsResp);

  // GetClueDetail gets the details of a clue
  // When clue_type > 0, returns details of the specified clue_type
  rpc GetClueDetail(GetClueDetailReq) returns (ClueDetail);

  // ListClueDetails lists the details of multiple clues
  // The interface is only used by file clue type.
  rpc ListClueDetails(ListClueDetailsReq) returns (ListClueDetailsResp);

  // ListAffectedOutreachTerminals lists the affected terminals of outreach
  rpc ListAffectedOutreachTerminals(ListAffectedOutreachTerminalsReq) returns (ListAffectedOutreachTerminalsResp);

  // ListAffectedFileTerminals lists the affected terminals of file threats
  rpc ListAffectedFileTerminals(ListAffectedFileTerminalsReq) returns (ListAffectedFileTerminalsResp);

  // GetOutreachStatistics gets outreach statistics
  rpc GetOutreachStatistics(GetOutreachStatisticsReq) returns (GetOutreachStatisticsResp);

  // GetThreatenFileStatistics gets threaten file statistics
  rpc GetThreatenFileStatistics(GetThreatenFileStatisticsReq) returns (GetThreatenFileStatisticsResp);
}

message ListMemoryAttackCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

message MemoryAttackClue {
  int64 id                              = 1;
  string machine_id                     = 2;
  string unique_flag                    = 3;
  string clue_key                       = 4;
  int32 clue_status                     = 5;
  int32 clue_level                      = 6;
  int32 clue_type                       = 7;
  int32 clue_sub_type                   = 8;
  int32 disposition                     = 9;
  int32 has_ignore                      = 10;
  string attack_src                     = 11;
  int64 occur_count                     = 12;
  string client_version                 = 13;
  google.protobuf.Timestamp created_at  = 14;
  google.protobuf.Timestamp updated_at  = 15;
  google.protobuf.Timestamp detected_at = 16;
}

message SystemAttackClue {
  int64 id                              = 1;
  string machine_id                     = 2;
  string unique_flag                    = 3;
  string clue_key                       = 4;
  int32 clue_status                     = 5;
  int32 clue_level                      = 6;
  int32 clue_type                       = 7;
  int32 clue_sub_type                   = 8;
  int32 disposition                     = 9;
  int32 has_ignore                      = 10;
  string attack_src                     = 11;
  int64 occur_count                     = 12;
  string client_version                 = 13;
  google.protobuf.Timestamp created_at  = 14;
  google.protobuf.Timestamp updated_at  = 15;
  google.protobuf.Timestamp detected_at = 16;
}

message FileThreatClue {
  int64 id                = 1;
  string machine_id       = 2;
  string unique_flag      = 3;
  string clue_key         = 4;
  int32 clue_status       = 5;
  int32 clue_level        = 6;
  int32 clue_type         = 7;
  int32 clue_sub_type     = 8;
  int32 disposition       = 9;
  int32 has_ignore        = 10;
  string filename         = 11;
  int32 file_type         = 12;
  string file_md5         = 13;
  string file_sha256      = 14;
  int32 isolate_count     = 15;
  int32 operation_type    = 16;
  int32 operation_status  = 17;
  int32 operation_failure = 18;
  bool can_isolate        = 19;
  string attack_src       = 20;
  int64 occur_count       = 21;
  int64 disposed_count    = 22;
  // The number of file clues grouped by file_sha256
  int64 file_sha256_count               = 23;
  string extra_info                     = 24;
  string client_version                 = 25;
  int32 attack_scene                    = 26;
  google.protobuf.Timestamp updated_at  = 27;
  google.protobuf.Timestamp created_at  = 28;
  google.protobuf.Timestamp detected_at = 29;
}

message IllegalOutreachClue {
  int64 id                = 1;
  string machine_id       = 2;
  string unique_flag      = 3;
  string clue_key         = 4;
  int32 clue_status       = 5;
  int32 clue_level        = 6;
  int32 clue_type         = 7;
  int32 clue_sub_type     = 8;
  string attack_src       = 9;
  int64 occur_count       = 10;
  int32 outreach_type     = 11;
  string outreach_address = 12;
  string malicious_code   = 13;
  HitSource hit_source    = 14;  // 改为枚举类型
  AptStatus is_apt        = 15;  // 改为枚举类型
  int32 has_ignore        = 16;
  int32 disposition       = 17;
  string client_version   = 18;
  // JSON marshalled string, type: map[string]any
  string extra_info                     = 19;
  google.protobuf.Timestamp detected_at = 20;
  google.protobuf.Timestamp created_at  = 21;
  google.protobuf.Timestamp updated_at  = 22;

  // means the number of outreach clues grouped by outreach_address
  int64 outreach_address_count = 23;
  LLMResult llm_result         = 24;
}

// TODO(pz): to be removed
message UniversalAttackClue {
  int64 id                              = 1;
  string machine_id                     = 2;
  string unique_flag                    = 3;
  string clue_key                       = 4;
  int32 clue_status                     = 5;
  int32 clue_level                      = 6;
  int32 clue_type                       = 7;
  int32 clue_sub_type                   = 8;
  int64 occur_count                     = 9;
  int32 disposition                     = 10;
  string file_md5                       = 11;
  string file_sha256                    = 12;
  bool can_isolate                      = 13;
  int32 file_operation_status           = 14;
  int32 file_operation_failure          = 15;
  google.protobuf.Timestamp created_at  = 16;
  google.protobuf.Timestamp updated_at  = 17;
  google.protobuf.Timestamp detected_at = 18;
}

message ListMemoryAttackCluesResp {
  repeated MemoryAttackClue clues = 1;
  PageResponse page               = 2;
}

message ListFileThreatCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

message ListFileThreatCluesResp {
  repeated FileThreatClue clues = 1;
  PageResponse page             = 2;
}

message ListSystemAttackCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

message ListIllegalOutreachCluesReq {
  ListAttackCluesFilter filter = 1;
  PageRequest page             = 2;
}

message ListIllegalOutreachCluesResp {
  repeated IllegalOutreachClue clues = 1;
  PageResponse page                  = 2;
}

message ListSystemAttackCluesResp {
  repeated SystemAttackClue clues = 1;
  PageResponse page               = 2;
}

message CountCluesBySha256Req {}

message CountCluesBySha256Resp {
  int64 total                 = 1;
  repeated Sha256Count counts = 2;
}

// The number of file clues corresponding to the file SHA256
message Sha256Count {
  string file_sha256 = 1;
  int64 count        = 2;
}

message ListTopMachineCluesReq {
  TimeRange time_range = 1;
  int32 limit          = 2;
}

message ListTopMachineCluesResp {
  repeated MachineClueCount machines = 1;
}

// The number of clues counted by machine dimension
message MachineClueCount {
  string machine_id    = 1;
  string machine_ip    = 2;
  string machine_name  = 3;
  string machine_group = 4;
  int64 clue_count     = 5;
  int32 online         = 6;  // 1: 在线, 2: 离线
}

message GetClueStatsReq {}

// Clue stats
message ClueStats {
  // The total number of attack clues
  int64 clue_count = 1;
  // The number of clues that have been disposed
  int64 disposed_count = 2;
  // The number of clues that have been collected
  int64 evidence_count = 3;
  // The number of affected terminals
  int64 affected_terminal_count = 4;
  // The number of waiting for disposal
  int64 waiting_for_disposal_count = 5;
}

message ListTopMachineClueCountsReq {
  TimeRange time_range = 1;
  int32 limit          = 2;
}

message ListTopMachineClueCountsResp {}

message ListOutreachAffectedTerminalsReq {
  int32 outreach_type     = 1;
  string outreach_address = 2;
  string ip               = 3;
  TimeRange time_range    = 4;
  PageRequest page        = 5;
}

message ListOutreachAffectedTerminalsResp {
  repeated OutreachAffectedTerminal terminals = 1;
  PageResponse page                           = 2;
}

message OutreachAffectedTerminal {
  int64 id                              = 1;
  string machine_id                     = 2;
  string clue_key                       = 3;
  string unique_flag_md5                = 4;
  string extra_info                     = 5;
  google.protobuf.Timestamp create_time = 6;
}

message CountOutreachCluesByTypeReq {}

message CountOutreachCluesByTypeResp {
  repeated OutreachTypeCount counts = 1;
}

message OutreachTypeCount {
  OutreachType type = 1;
  int64 count       = 2;
}

message ListClueTypeCountsReq {
  ClueType clue_type   = 1;
  TimeRange time_range = 2;
}

message ListClueTypeCountsResp {
  repeated ClueTypeCount counts = 1;
}

message ClueTypeCount {
  ClueType clue_type                           = 1;
  int32 clue_sub_type                          = 2;
  int64 count                                  = 3;
  google.protobuf.Timestamp latest_create_time = 4;
}

// ProcessInfo represents process-related information in an attack
message ProcessInfo {
  int32 pid                     = 1;   // Process ID
  int32 ppid                    = 2;   // Parent Process ID
  string proc_name              = 3;   // Process name
  string proc_path              = 4;   // Process path
  string dst_proc_path          = 5;   // Destination process path
  string parent_proc_path       = 6;   // Parent process path
  int64 parent_proc_create_time = 7;   // Parent process creation time
  string proc_user              = 8;   // Process user
  string proc_md5               = 9;   // Process MD5
  string proc_sha256            = 10;  // Process SHA256
  string proc_command           = 11;  // Process command line
  string proc_permission        = 12;  // Process permissions
  int64 proc_start_time         = 13;  // Process start time
  uint32 is_x86_process         = 14;  // Whether it's an x86 process
  string sub_proc_name          = 15;  // Sub-process name
  string sub_proc_path          = 16;  // Sub-process path
  string sub_proc_md5           = 17;  // Sub-process MD5
  string sub_proc_command       = 18;  // Sub-process command line
  int64 file_access_time        = 19;  // File access time
  int64 file_modify_time        = 20;  // File modification time
  int64 file_create_time        = 21;  // File creation time
  int64 file_size               = 22;  // File size
  string file_path              = 23;  // File path
  string file_company_name      = 24;  // Process file vendor
  string file_version           = 25;  // Process file version
  int64 euid                    = 26;  // Effective UID
  string cur_proc_info          = 27;  // Current process info
  string parent_proc_info       = 28;  // Parent process info
  string target_proc_info       = 29;  // Target process info
}

// FileInfo represents file-related information in an attack
message FileInfo {
  int32 file_type         = 1;   // File type
  string file_name        = 2;   // File name
  string file_path        = 3;   // File path
  string file_md5         = 4;   // File MD5
  string file_sha256      = 5;   // File SHA256
  string file_sha1        = 6;   // File SHA1
  int64 file_access_time  = 7;   // File access time
  int64 file_modify_time  = 8;   // File modify time
  int64 file_create_time  = 9;   // File creation time
  int64 file_size         = 10;  // File size
  string file_permission  = 11;  // File permissions
  string file_user        = 12;  // File owner
  uint32 file_clue_source = 13;  // File clue source: 1.agent 2.server
  string signature_info   = 14;  // Signature information
}

// DriverInfo represents driver-related information in an attack
message DriverInfo {
  string driver_name       = 1;  // Kernel driver name
  string driver_path       = 2;  // Kernel driver path
  int32 driver_risk_type   = 3;  // Kernel risk type
  string kernel_mod        = 4;  // Kernel module
  string remote_mod        = 5;  // Remote module
  string first_driver_info = 6;  // First driver process info, just used in def.MemKernelLeakDriverThreadType
}

// MaliciousInfo represents malicious code information
message MaliciousInfo {
  string malicious_code            = 1;   // Malicious code family
  string malicious_types           = 2;   // Suspicious types
  string malicious_dde             = 3;   // Suspicious DDE code
  string malicious_vba             = 4;   // Suspicious VBA code
  string malicious_lnk_target_path = 5;   // Suspicious LNK target path
  string malicious_lnk_working_dir = 6;   // Suspicious LNK working directory
  string malicious_lnk_cmd_line    = 7;   // Suspicious LNK command line
  string malicious_lnk_icon_path   = 8;   // Suspicious LNK icon path
  string malicious_url             = 9;   // Suspicious icon URL
  string sl_rule_name              = 10;  // SL rule name
  string sl_rule_detail            = 11;  // SL rule details
}

// OutreachInfo represents external connection information
message OutreachInfo {
  int64 euid              = 1;   // Effective UID
  string outreach_ip      = 2;   // External IP
  uint32 outreach_type    = 3;   // External connection type
  int32 outreach_port     = 4;   // External port
  string outreach_domain  = 5;   // External domain
  string outreach_address = 6;   // External address
  string ip_version       = 7;   // IP version
  string direction        = 8;   // Directional attack type
  int32 confidence        = 9;   // Confidence level
  int32 is_apt            = 10;  // Whether hit APT, 1: hit, 2: not hit
}

// AttackExtendInfo represents extended attack information
message AttackExtendInfo {
  int64 report_id       = 1;   // Report ID
  string proc_line_flag = 2;   // Process line flag
  string dump_flag      = 3;   // Dump flag
  string script_flag    = 4;   // Script flag
  string signature_info = 5;   // Signature information
  int64 script_type     = 6;   // Script type (only exists in NoFileAttack)
  string cve            = 7;   // CVE number
  string attack_ip      = 8;   // Attack IP
  string crontab        = 9;   // Crontab
  string local_ip       = 10;  // Local IP
  int32 local_port      = 11;  // Local port
  string remote_ip      = 12;  // Remote IP
  int32 remote_port     = 13;  // Remote port
  int32 hit_source      = 14;  // External hit source 1: public intel 2: private intel
  string protocol       = 15;  // Protocol
}

message Evidence {
  int64 evidence_size  = 1;
  string evidence_list = 2;
}

message AttackIndicatorContextValue {
  oneof value {
    string string_value = 1;
    int64 int64_value   = 2;
  }
}

// AttackDetail represents details about an attack event
message AttackDetail {
  ProcessInfo process_info                                      = 1;  // Process related information
  FileInfo file_info                                            = 2;  // File related information
  DriverInfo driver_info                                        = 3;  // Driver related information
  MaliciousInfo malicious_info                                  = 4;  // Malicious code information
  OutreachInfo outreach_info                                    = 5;  // External connection information
  Evidence evidence_info                                        = 6;  // Evidence information
  map<string, AttackIndicatorContextValue> attack_context       = 7;  // Dynamic attack context data
  AttackExtendInfo extend_info                                  = 8;  // Extended information
  map<string, AttackIndicatorContextValue> clue_type_extra_info = 9;  // Clue type extra info
}

message ClueDetail {
  oneof clue {
    MemoryAttackClueDetail memory_attack_clue_detail       = 1;
    FileThreatClueDetail file_threat_clue_detail           = 2;
    SystemAttackClueDetail system_attack_clue_detail       = 3;
    IllegalOutreachClueDetail illegal_outreach_clue_detail = 4;
  }
  ClueType clue_type = 5;  // The type of the clue
}

message MemoryAttackClueDetail {
  MemoryAttackClue clue = 1;
  AttackDetail detail   = 2;
}

message FileThreatClueDetail {
  FileThreatClue clue = 1;
  AttackDetail detail = 2;
}

message SystemAttackClueDetail {
  SystemAttackClue clue = 1;
  AttackDetail detail   = 2;
}

message IllegalOutreachClueDetail {
  IllegalOutreachClue clue = 1;
  AttackDetail detail      = 2;
}

message GetClueDetailReq {
  int64 id           = 1;
  ClueType clue_type = 2;
}

message LLMResult {
  string url                    = 1;
  string read_url               = 2;
  int64 rank                    = 3;
  string llm_answer             = 4;
  string llm_answer_description = 5;
  string screenshot             = 6;
}

message ListClueDetailsReq {
  // filters和ids至少提供一个， filter需要配合page使用
  ListAttackCluesFilter filter = 1;
  repeated int64 ids           = 2;
  ClueType clue_type           = 3;
  PageRequest page             = 4;
}

message ListClueDetailsResp {
  repeated ClueDetail clues = 1;
  PageResponse page         = 2;
}

message ListAffectedOutreachTerminalsReq {
  OutreachType type       = 1;
  string outreach_address = 2;
  string ip               = 3;
  PageRequest page        = 4;
  TimeRange range         = 5;
}

message ListAffectedOutreachTerminalsResp {
  repeated AffectedOutreachTerminal terminals = 1;
  PageResponse page                           = 2;
}

message AffectedOutreachTerminal {
  int64 id                              = 1;
  string machine_id                     = 2;
  string clue_key                       = 3;
  string unique_flag_md5                = 4;
  string extra_info                     = 5;
  google.protobuf.Timestamp create_time = 6;
}

message ListAffectedFileTerminalsReq {
  string file_sha256   = 1;
  TimeRange time_range = 2;
  PageRequest page     = 3;
}

message ListAffectedFileTerminalsResp {
  repeated AffectedFileTerminal terminals = 1;
  PageResponse page                       = 2;
}

message AffectedFileTerminal {
  int64 id                              = 1;
  string machine_id                     = 2;
  string clue_key                       = 3;
  int32 clue_status                     = 4;
  int32 clue_sub_type                   = 5;
  string file_name                      = 6;
  int32 file_type                       = 7;
  string unique_flag                    = 8;
  string extra_info                     = 9;
  int32 disposition                     = 10;
  google.protobuf.Timestamp create_time = 11;
}

message GetOutreachStatisticsReq {
  TimeRange time_range = 1;
}

message GetOutreachStatisticsResp {
  int64 total_count   = 1;  // 外联日志数量
  int64 illegal_count = 2;  // 非法外联数量
  int64 ip_count      = 3;  // IP数量
  int64 domain_count  = 4;  // 域名数量
}

message GetThreatenFileStatisticsReq {
  TimeRange time_range = 1;
}

message GetThreatenFileStatisticsResp {
  int64 total_count    = 1;  // 文件威胁告警数量
  int64 terminal_count = 2;  // 影响终端数量
  // key: mq.CluleDetectEngine
  map<int32, int64> check_engine_counts = 3;  // 检测引擎统计数量
}