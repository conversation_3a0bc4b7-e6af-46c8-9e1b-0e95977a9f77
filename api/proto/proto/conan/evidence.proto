syntax = "proto3";
package conan;
import "google/protobuf/empty.proto";
import "conan/common.proto";
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/conan";

// 证据管理服务
service EvidenceService {
  // 资源通知
  // 取证通道可用通知
  rpc ObtainEvidenceChannelAvailableNotify(LimitPassNotifyRequest)
      returns (ObtainEvidenceChannelAvailableNotifyResponse);
  // 取证完成通知
  rpc ObtainEvidenceFinishedNotify(ObtainEvidenceFinishedNotifyRequest) returns (google.protobuf.Empty);

  // illaoi 通知
  // 上报取证结果信息
  rpc ReportObtainEvidenceInfo(ObtainEvidenceResponse) returns (google.protobuf.Empty);

  // 添加取证任务
  // 添加线索取证任务
  rpc AddClueObtainEvidenceTask(ClueObtainEvidenceTaskRequest) returns (ClueObtainEvidenceTaskStatus);
  // 添加文件取证任务
  rpc AddFileObtainEvidenceTask(FileObtainEvidenceTaskInfo) returns (ObtainEvidenceTaskStatus);
  // 批量添加文件取证任务
  rpc BatchAddFileObtainEvidenceTask(BatchFileObtainEvidenceTaskRequest) returns (BatchFileObtainEvidenceResponse);
  // 添加网络取证任务
  rpc AddNetObtainEvidenceTask(NetObtainEvidenceTaskInfo) returns (ObtainEvidenceTaskStatus);
  // 添加日志取证任务
  rpc AddLogObtainEvidenceTask(LogObtainEvidenceTaskRequest) returns (ObtainEvidenceTaskStatus);
  // 重新提取证据
  rpc RenewObtainEvidence(RenewObtainEvidenceRequest) returns (ObtainEvidenceTaskStatus);

  // 查询取证任务
  // 根据线索获取证据信息
  rpc GetClueEvidenceInfo(ClueEvidenceQueryRequest) returns (EvidenceExtractionResultResponse);
  // 获取文件证据信息
  rpc GetFileEvidenceInfo(GetFileEvidenceRequest) returns (EvidenceExtractionResult);
  // 查询网络外联取证信息
  rpc QueryNetEvidenceInfo(NetEvidenceQueryRequest) returns (NetEvidenceInfoListResponse);
  // 查询文件取证信息
  rpc QueryFileEvidenceInfo(FileEvidenceQueryRequest) returns (FileEvidenceInfoListResponse);
  // 查询内存取证信息
  rpc QueryMemoryDumpEvidenceInfo(MemDumpEvidenceQueryRequest) returns (MemDumpEvidenceInfoListResponse);
  // 查询日志取证信息
  rpc QueryLogEvidenceInfo(LogEvidenceQueryRequest) returns (LogEvidenceInfoListResponse);
  // 获取取证结果信息
  rpc GetEvidenceResultInfo(GetEvidenceResultInfoRequest) returns (EvidenceResultInfo);
  // 批量获取取证结果信息
  rpc BatchGetEvidenceResultInfo(BatchGetEvidenceResultInfoRequest) returns (BatchEvidenceResultInfoResponse);
  // 获取取证存储信息
  rpc GetEvidenceStorageInfo(GetEvidenceStorageInfoRequest) returns (EvidenceStorage);
  // 获取已获取证据计数
  rpc GetObtainedEvidenceCount(GetObtainedEvidenceCountRequest) returns (ObtainEvidenceCountResponse);

  // 取证配置信息
  // 获取取证来源配置信息
  rpc GetEvidenceSourceConfig(GetEvidenceSourceConfigRequest) returns (EvidenceSourceConfigResponse);
  // 添加取证源配置
  rpc AddEvidenceSourceConfig(EvidenceSourceConfigRequest) returns (google.protobuf.Empty);
  // 删除取证源配置
  rpc DeleteEvidenceSourceConfig(DeleteEvidenceSourceConfigRequest) returns (google.protobuf.Empty);
  // 更新取证来源配置信息(自动取证线索子类型)
  rpc UpdateAutoObtainEvidenceClueSubType(UpdateAutoObtainEvidenceClueSubTypeRequest) returns (google.protobuf.Empty);
}

message ClueObtainEvidenceTaskStatus {
  EvidenceType evidence_type      = 1;
  ObtainEvidenceTaskStatus status = 2;
}

// 通道可用通知信息
message LimitPassNotifyRequest {
  int64 task_id     = 1;  // 取证任务ID
  int64 channel     = 2;  // 限流通道
  int32 category    = 3;  // 分类(非必要字段)
  string temp_token = 4;  // 临时Token
  string identifier = 5;  // 唯一串
  string content    = 6;  // 内容
}

message ObtainEvidenceChannelAvailableNotifyResponse {
  ResourceTaskStatus status = 1;
}

// 资源任务状态枚举
enum ResourceTaskStatus {
  RESOURCE_TASK_STATUS_NULL     = 0;  // 未知
  RESOURCE_TASK_STATUS_UNSENT   = 1;  // 未下发 (业务创建时的状态)
  RESOURCE_TASK_STATUS_PREPARE  = 2;  // 待下发 (生成临时Token, 并已通知业务服务)
  RESOURCE_TASK_STATUS_SENT     = 3;  // 已下发 (业务服务已通过临时Token换取会话Token)
  RESOURCE_TASK_STATUS_FINISHED = 4;  // 已完成
  RESOURCE_TASK_STATUS_TIMEOUT  = 5;  // 超时
  RESOURCE_TASK_STATUS_CANCEL   = 6;  // 用户取消
}

// 证据提取状态
enum EvidenceStatus {
  EVIDENCE_STATUS_UNSPECIFIED         = 0;   // 未提取
  EVIDENCE_STATUS_PREPARE             = 1;   // 待取证
  EVIDENCE_STATUS_EXTRACTING          = 2;   // 取证中
  EVIDENCE_STATUS_SUCCESS             = 3;   // 成功
  EVIDENCE_STATUS_IN_WHITELIST        = 4;   // 证据在白名单中,不提取
  EVIDENCE_STATUS_HOST_NOT_FOUND      = 5;   // 主机不存在
  EVIDENCE_STATUS_HOST_OFFLINE        = 6;   // 主机已离线
  EVIDENCE_STATUS_SEND_FAILED         = 7;   // 取证任务下发失败
  EVIDENCE_STATUS_TIMEOUT             = 8;   // 超时
  EVIDENCE_STATUS_FILE_SIZE_TOO_LARGE = 9;   // 文件太大
  EVIDENCE_STATUS_FILE_NOT_FOUND      = 10;  // 证据未找到
  EVIDENCE_STATUS_UPLOAD_FAILED       = 11;  // 证据上传失败
  EVIDENCE_STATUS_HAS_EXPIRED         = 12;  // 证据已过期
  EVIDENCE_STATUS_NOT_SUPPORT         = 13;  // 不支持取证
  EVIDENCE_STATUS_UPLOAD_UNKNOWN      = 99;  // 未知错误
}

// 证据来源类型
enum EvidenceSourceType {
  EVIDENCE_SOURCE_TYPE_UNDEFINED = 0;
  EVIDENCE_SOURCE_TYPE_AUTO      = 1;
  EVIDENCE_SOURCE_TYPE_MANUAL    = 2;
  EVIDENCE_SOURCE_TYPE_EXTERNAL  = 3;
}

// 证据类型
enum EvidenceType {
  EVIDENCE_TYPE_EMPTY            = 0;
  EVIDENCE_TYPE_PROCESS_CHAIN    = 1;
  EVIDENCE_TYPE_FILE             = 2;
  EVIDENCE_TYPE_SCRIPT_FILE      = 4;
  EVIDENCE_TYPE_MEMORY_DUMP      = 8;
  EVIDENCE_TYPE_LOG              = 16;
  EVIDENCE_TYPE_MINI_MEMORY_DUMP = 32;
  EVIDENCE_TYPE_MEMORY_SEGMENT   = 64;
  EVIDENCE_TYPE_ALL              = 95;  // exclude MINI_MEMORY_DUMP
  EVIDENCE_TYPE_AUTO =
      71;  // EvidenceTypeProcessChain | EvidenceTypeFile | EvidenceTypeScriptFile | EvidenceTypeMemorySegment
}

// 证据存储模式
enum EvidenceStorageMode {
  EVIDENCE_STORAGE_MODE_UNDEFINED = 0;  // 未定义
  EVIDENCE_STORAGE_MODE_FILE      = 1;  // 文件(minio: bucket + objectname)
  EVIDENCE_STORAGE_MODE_CONTENT   = 2;  // 内容(json)
}

// 取证结果返回方式
enum EvidenceReturnWay {
  EVIDENCE_RETURN_WAY_UNSPECIFIED = 0;
  EVIDENCE_RETURN_WAY_POLLING     = 1;  // 轮询
  EVIDENCE_RETURN_WAY_CALLBACK    = 2;  // 回调
}

// 取证任务状态
message ObtainEvidenceTaskStatus {
  int64 task_id         = 1;
  EvidenceStatus status = 2;
  bool existed          = 3;
}

// 上传信息
message UploadInfo {
  string bucket_name = 1;
  string object_name = 2;
  string md5         = 3;
  int64 size         = 4;
}

// 取证完成信息通知
message ObtainEvidenceFinishedNotifyRequest {
  UploadInfo upload_info          = 1;
  string raw_filename             = 2;
  int64 task_id                   = 3;
  string identifier               = 4;
  ResourceTaskStatus status       = 5;  // 修改为使用 ResourceTaskStatus 枚举
  map<string, string> form_params = 6;
}

// 取证结果响应
message ObtainEvidenceResponse {
  // 这里需要根据实际的 MemProtect.ObtainEvidenceResp 结构定义
  // 由于没有看到具体定义，这里只是一个占位符
  string data = 1;
}

// 证据来源信息
message EvidenceSourceInfo {
  int32 source_type = 1;  // 取证来源类型
  int64 source_id   = 2;  // 取证来源ID
  string identifier = 3;  // 唯一标识
}

// 主机线索信息
message HostClueInfo {
  string machine_id = 1;
  string clue_key   = 2;
}

// 线索取证任务请求
message ClueObtainEvidenceTaskRequest {
  EvidenceSourceInfo source_info = 1;
  string clue_id                 = 2;
  int32 clue_type                = 3;
  EvidenceType evidence_type     = 4;
}

// 文件信息
message EvicenceFileInfo {
  string file_name   = 1;  // 文件名
  string file_path   = 2;  // 文件路径
  int64 raw_filesize = 3;  // 原始文件size
  string file_md5    = 4;  // 文件md5
  string file_sha1   = 5;  // 文件sha1
  string file_sha256 = 6;  // 文件sha256
  int64 atime        = 7;  // 最后一次访问文件时间
  int64 mtime        = 8;  // 最后一次文件修改时间
  int64 ctime        = 9;  // 文件修改时间
}

// 证据信息
message EvidenceInfo {
  int32 evidence_type    = 1;
  string unique_flag     = 2;
  uint64 evidence_size   = 3;
  string filepath        = 4;
  string filename        = 5;
  FileDetail file_detail = 6;
}

message FileDetail {
  int64 raw_filesize = 1;
  string md5         = 2;
  string sha256      = 3;
  int64 atime        = 4;
  int64 mtime        = 5;
  int64 ctime        = 6;
}

// 文件取证任务请求
message FileObtainEvidenceTaskInfo {
  HostClueInfo host_clue_info    = 1;
  EvidenceSourceInfo source_info = 2;
  string search_key              = 3;
  int64 timestamp                = 4;
  bool existed_raise_error       = 5;
  EvidenceInfo evidence_info     = 6;
}

// 批量文件取证任务请求
message BatchFileObtainEvidenceTaskRequest {
  EvidenceSourceInfo source_info = 1;
  string file_object_name        = 2;
}

// 批量文件取证响应
message BatchFileObtainEvidenceResponse {}

// 网络连接信息
message NetConnectionInfo {
  string dest_ip  = 1;
  int32 dest_port = 2;
  string src_ip   = 3;
  int32 src_port  = 4;
  string protocol = 5;
}

// 外联信息
message EvidenceOutReachInfo {
  NetConnectionInfo connection_info = 1;
  int64 timestamp                   = 2;
}

// 网络取证任务请求
message NetObtainEvidenceTaskInfo {
  HostClueInfo host_clue_info        = 1;
  EvidenceSourceInfo source_info     = 2;
  EvidenceOutReachInfo outreach_info = 3;
  bool existed_raise_error           = 4;
}

// 日志取证任务请求
message LogObtainEvidenceTaskRequest {
  string machine_id              = 1;
  EvidenceSourceInfo source_info = 2;
  int32 log_bit                  = 3;
  int64 timestamp                = 4;
  bool existed_raise_error       = 5;
}

// 重新提取证据请求
message RenewObtainEvidenceRequest {
  int64 task_id = 1;
}

// 线索取证查询请求
message ClueEvidenceQueryRequest {
  ClueType clue_type         = 1;
  string clue_key            = 2;
  EvidenceType evidence_type = 3;
  bool is_url_zip            = 4;  // URL下载文件，是否需zip加密格式
}

// 获取文件证据请求
message GetFileEvidenceRequest {
  string machine_id  = 1;
  string search_data = 2;
  int64 timestamp    = 3;
  bool is_url_zip    = 4;  // URL下载文件，是否需zip加密格式
}

// 证据提取结果
message EvidenceExtractionResultResponse {
  repeated EvidenceExtractionResult results = 1;
}

message EvidenceExtractionResult {
  int64 task_id              = 1;
  EvidenceType evidence_type = 2;
  EvidenceStatus status      = 3;
  string url                 = 4;
  int64 created_at           = 5;
}

// 证据过滤条件
message EvidenceFilter {
  string search_data = 1;
  bool is_url_zip    = 2;  // URL下载文件，是否需zip加密格式
}

// 网络取证查询请求
message NetEvidenceQueryRequest {
  EvidenceFilter filter = 1;
  string order_by       = 2;
  PageRequest page      = 3;
}

// 证据来源项
message EvidenceSourceItem {
  int32 source_type  = 1;
  int64 source_id    = 2;
  string source_name = 3;
}

// 网络取证信息
message NetEvidenceInfo {
  int64 task_id                       = 1;
  string machine_id                   = 2;
  EvidenceStatus status               = 3;
  string remote_ip                    = 4;
  int32 remote_port                   = 5;
  string local_ip                     = 6;
  int32 local_port                    = 7;
  string protocol                     = 8;
  string domain                       = 9;
  int64 created_at                    = 10;
  int64 updated_at                    = 11;
  repeated EvidenceSourceItem sources = 12;
  string download_url                 = 13;
}

// 网络取证信息列表响应
message NetEvidenceInfoListResponse {
  repeated NetEvidenceInfo items = 1;
  PageResponse page              = 2;
}

// 文件取证查询请求
message FileEvidenceQueryRequest {
  EvidenceFilter filter = 1;
  string order_by       = 2;
  PageRequest page      = 3;
}

// 文件取证信息
message FileEvidenceInfo {
  int64 task_id                       = 1;
  int32 category                      = 2;
  string machine_id                   = 3;
  EvidenceStatus status               = 4;
  int32 isolate                       = 5;  // 文件隔離狀態
  EvicenceFileInfo file_info          = 6;
  int64 created_at                    = 7;
  int64 updated_at                    = 8;
  repeated EvidenceSourceItem sources = 9;
  string download_url                 = 10;
}

// 文件取证信息列表响应
message FileEvidenceInfoListResponse {
  repeated FileEvidenceInfo items = 1;
  PageResponse page               = 2;
}

// 内存dump取证查询请求
message MemDumpEvidenceQueryRequest {
  EvidenceFilter filter = 1;
  string order_by       = 2;
  PageRequest page      = 3;
}

// 内存dump取证信息
message MemDumpEvidenceInfo {
  int64 task_id                       = 1;
  int32 category                      = 2;
  string machine_id                   = 3;
  EvidenceStatus status               = 4;
  int32 pid                           = 5;   // 进程ID
  string name                         = 6;   // 进程名
  string filepath                     = 7;   // 进程文件路径
  string file_md5                     = 8;   // 文件md5
  string cmd                          = 9;   // 进程命令行
  string owner                        = 10;  // 进程创建用户
  int64 created_at                    = 11;
  int64 updated_at                    = 12;
  repeated EvidenceSourceItem sources = 13;
  string download_url                 = 14;
}

// 内存dump取证信息列表响应
message MemDumpEvidenceInfoListResponse {
  repeated MemDumpEvidenceInfo items = 1;
  PageResponse page                  = 2;
}

// 日志取证过滤条件
message LogEvidenceFilter {
  repeated string machine_id_slice = 1;
  int32 log_type                   = 2;  // 日志类型：1. 系统, 2. 安全，4. setup, 8. 应用
}

// 日志取证查询请求
message LogEvidenceQueryRequest {
  LogEvidenceFilter filter = 1;
  string order_by          = 2;
  PageRequest page         = 3;
}

// 日志取证信息
message LogEvidenceInfo {
  int64 task_id                       = 1;
  string machine_id                   = 2;
  EvidenceStatus status               = 3;
  int32 log_bit                       = 4;
  int64 created_at                    = 5;
  int64 updated_at                    = 6;
  repeated EvidenceSourceItem sources = 7;
  string download_url                 = 8;
}

// 日志取证信息列表响应
message LogEvidenceInfoListResponse {
  repeated LogEvidenceInfo items = 1;
  PageResponse page              = 2;
}

// 获取取证结果信息请求
message GetEvidenceResultInfoRequest {
  int64 task_id = 1;
}

// 终端信息
message TerminalInfo {
  string host_id  = 1;
  string hostname = 2;
  string ip       = 3;
  string os       = 4;
  string group    = 5;
}

// 进程信息
message EvidenceProcessInfo {
  int64 pid               = 1;   // 进程ID
  int64 start_time        = 2;   // 进程创建时间
  int64 ppid              = 3;   // 父进程ID
  int64 parent_start_time = 4;   // 进程创建时间
  string process_name     = 5;   // 进程名
  string cmd              = 6;   // 进程命令行
  string filepath         = 7;   // 进程文件路径
  string file_md5         = 8;   // 文件md5
  string file_sha256      = 9;   // 文件sha256
  int64 file_size         = 10;  // 进程文件大小
  int64 file_ctime        = 11;  // 进程文件创建时间
  int64 file_mtime        = 12;  // 进程文件修改时间
  int64 file_atime        = 13;  // 进程文件最后访问时间
  string owner            = 14;  // 进程创建用户
}

// 文件证据内容
message FileEvidenceContent {
  string filepath = 1;
  string url      = 2;
  string md5sum   = 3;
}

// 通用证据内容
message CommonEvidenceContent {
  string filename = 1;
  string url      = 2;
}

// 日志证据内容
message LogEvidenceContent {
  CommonEvidenceContent common = 1;
  int32 log_bit                = 2;
}

// 取证结果信息
message EvidenceResultInfo {
  int64 task_id                                   = 1;
  EvidenceStatus status                           = 2;
  int64 expired_at                                = 3;
  string clue_key                                 = 4;
  TerminalInfo terminal_info                      = 5;
  repeated EvidenceProcessInfo process_chain_info = 6;
  FileEvidenceContent file                        = 7;
  FileEvidenceContent script_file                 = 8;
  CommonEvidenceContent memory_dump               = 9;
  CommonEvidenceContent mini_memory_dump          = 10;
  CommonEvidenceContent memory_segment            = 11;
  LogEvidenceContent log_file                     = 12;
}

// 批量获取取证结果信息请求
message BatchGetEvidenceResultInfoRequest {
  repeated int64 task_ids = 1;
}

// 批量取证结果信息响应
message BatchEvidenceResultInfoResponse {
  repeated EvidenceResultInfo results = 1;
}

// 获取取证存储信息请求
message GetEvidenceStorageInfoRequest {
  int64 task_id = 1;
}

// 取证存储信息
message EvidenceStorage {
  EvidenceStorageMode mode = 1;
  string content           = 2;
  int64 size               = 3;
  string md5               = 4;
  int64 created_at         = 5;
}

// 获取已获取证据计数请求
message GetObtainedEvidenceCountRequest {
  repeated int64 date_range = 1;
}

// 已获取证据计数响应
message ObtainEvidenceCountResponse {
  int64 total               = 1;
  int64 evidence_net_count  = 2;
  int64 evidence_file_count = 3;
  int64 evidence_dump_count = 4;
}

// 获取取证来源配置信息请求
message GetEvidenceSourceConfigRequest {
  string platform = 1;
}

// 取证来源配置信息
message EvidenceSourceConfigResponse {
  string platform              = 1;
  EvidenceReturnWay return_way = 2;
  string callback_path         = 3;  // 取证结果回调路径,"/xxx/abc""
  string description           = 4;
}

// 添加取证源配置请求
message EvidenceSourceConfigRequest {
  string platform              = 1;
  EvidenceReturnWay return_way = 2;
  string callback_path         = 3;  // 取证结果回调路径,"/xxx/abc""
  string description           = 4;
}

// 删除取证源配置请求
message DeleteEvidenceSourceConfigRequest {
  string platform = 1;
}

// 更新取证来源配置信息(自动取证线索子类型)请求
message UpdateAutoObtainEvidenceClueSubTypeRequest {
  string platform               = 1;
  repeated int32 sub_type_slice = 2;
}
