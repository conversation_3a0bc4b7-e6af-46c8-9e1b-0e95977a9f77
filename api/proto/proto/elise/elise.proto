syntax = "proto3";
package elise;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/elise";

// 文件上传服务
service Elise {

  // 获取文件元数据信息
  rpc GetFile(GetFileReq) returns (GetFileResp);
  // 上传文件
  rpc UploadFile(UploadFileReq) returns (UploadFileResp);
  // 下载文件
  rpc DownloadFile(DownloadFileReq) returns (stream DownloadFileResp);
  // 删除文件
  rpc DeleteFile(DeleteFileReq) returns (DeleteFileResp);
}


message GetFileReq {
  string sha256 = 1; // 文件md5
}
message GetFileResp {
  string sha256 = 1; // 文件md5
  string name = 2; // 文件名
  int64 size = 3; // 文件大小
  int64 create_time = 4; // 文件创建时间
  int64 update_time = 5; // 文件更新时间
  string path = 6; // 文件路径
  string storage_path = 7; // 文件存储路径
  string storage_url =8; // 文件存储URL
}

message UploadFileReq {
  string sha256 = 1; // 文件sha256
  string name = 2; // 文件名
  int64 size = 3; // 文件大小
  string path = 4; // 文件路径
  bytes data = 5; // 文件数据
  int64 offset = 6; // 文件偏移
}
message UploadFileResp {
}

message DownloadFileReq {
  string sha256 = 1; // 文件sha256
  int64 size = 2; // 文件大小
  string path = 3; // 文件路径
  int64 offset = 4; // 文件偏移
}
message DownloadFileResp {
  bytes data = 1; // 文件数据
}

message DeleteFileReq {
  string sha256 = 1; // 文件sha256
}
message DeleteFileResp {
}
