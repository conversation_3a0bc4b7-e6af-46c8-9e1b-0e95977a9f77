syntax = "proto3";
package portal;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/portal";

// 网防云接入服务
service Portal {

  // 探测云沙箱
  rpc Probe(ProbeReq) returns (ProbeResp) {}
  // 文件检测
  rpc FileDetect(stream FileDetectReq) returns (stream FileDetectResp) {}
}

message ProbeReq {
  string auth_addr = 1; // 身份认证地址
  string auth_key = 2; // 身份认证key
  string cloud_addr = 3; // 沙箱服务地址
  string cloud_secret = 4; // 沙箱服务密钥
  string bucket_addr = 5; // 存储桶地址
  string bucket_secret = 6; // 存储桶密钥
}
message ProbeResp {
}

message FileDetectReq {
  string filename = 1; // 仅首个包携带
  string sha256 = 2; // 文件sha256
  bytes chunk_data = 3; // 数据块
  bool eof = 4; // 是否结束标志
}
message FileDetectResp {
  int64 task_id = 1; // 检测任务Id
}