syntax = "proto3";
package olaf;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/olaf";

// Olaf（ai检测服务）
service Olaf {

  // 文件检测
  rpc FileDetect(stream FileDetectReq) returns (stream FileDetectResp) {}
}

message FileDetectReq {
  string filename = 1; // 仅首个包携带
  string sha256 = 2; // 文件sha256
  bytes chunk_data = 3; // 数据块
  bool eof = 4; // 是否结束标志
  DetectType type = 5; // 检测类型 
}
message FileDetectResp {
  int32 score = 1; // 检测分值
  ModelType model_type = 2; // 使用的检测模型
}

enum DetectType {
  UNKNOWN = 0;
  AI = 1; // ai检测
  AIPE = 2; // aipe检测
}

enum ModelType {
  MODEL_UNKNOWN = 0; // 未知
  MODEL_PDF = 1;     // pdf模型
  MODEL_OFFICE = 2;  // office模型
  MODEL_PE = 3;      // pe模型
}
