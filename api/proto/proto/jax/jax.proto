syntax = "proto3";
package jax;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/jax";

import "google/api/annotations.proto";

// B端接入网关服务
service Jax {

  // 查询配置
  rpc GetConfig(GetConfigReq) returns (GetConfigResp) {
    option (google.api.http) = {
      get: "/config/get"
    };
  };
  // 更新配置
  rpc UpdateConfig(UpdateConfigReq) returns (UpdateConfigResp) {
    option (google.api.http) = {
      post: "/config/update"
      body: "*"
    };
  };
}

message GetConfigReq {
}
message GetConfigResp {
  string data = 1; // 文件内容
}

message UpdateConfigReq {
  string data = 1; // 文件内容
}
message UpdateConfigResp {
}