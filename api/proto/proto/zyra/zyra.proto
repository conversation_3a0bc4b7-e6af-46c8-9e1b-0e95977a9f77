syntax = "proto3";
package zyra;
option go_package = "git.anxin.com/v01-cluster/vapi/pkg/zyra";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "mq/detect.proto";


// 文件风险检测增强服务
service Zyra {

  // 添加检测任务（模拟yone推送MQ，测试专用）
  rpc AddTask(AddTaskReq) returns (AddTaskResp);

  // 自研沙箱检测（沙箱探活）
  rpc SandboxProbe(SandboxProbeReq) returns (SandboxProbeResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/probe",
      body: "*"
    };
  }
  // 自研沙箱检测（检测任务状态回调）
  rpc PushSandboxDetectStatus(PushSandboxDetectStatusReq) returns (PushSandboxDetectStatusResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/status",
      body: "*"
    };
  };
  // 自研沙箱检测（设备探活）
  rpc PushSandboxAlive(google.protobuf.Empty) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/keepalive",
      body: "*"
    };
  }
  // 自研沙箱任务状态查询
  rpc QuerySandboxTaskStatus(QuerySandboxTaskStatusReq) returns (QuerySandboxTaskStatusResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/task/status",
      body: "*"
    };
  }
  // 自研沙箱任务状态批量查询
  rpc QuerySandboxTaskBatchStatus(QuerySandboxTaskBatchStatusReq) returns (QuerySandboxTaskBatchStatusResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/task/batchStatus",
      body: "*"
    };
  }
  // 自研沙箱任务结果查询
  rpc QuerySandboxTaskResult(QuerySandboxTaskResultReq) returns (QuerySandboxTaskResultResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/sandbox/task/result",
      body: "*"
    };
  }
  // 云沙箱检测（沙箱探活）
  rpc CloudSandboxProbe(CloudSandboxProbeReq) returns (CloudSandboxProbeResp) {
    option (google.api.http) = {
      post: "/openapi/v1/detect/cloudsandbox/probe",
      body: "*"
    };
  }
}

// 添加检测任务
message AddTaskReq {
  mq.SampleDetectFile detect_info = 1; // 检测文件信息
}
message AddTaskResp {
}

// 沙箱检测任务创建
message SandboxCreateReq {
  string file = 1; // 样本文件（必填）
  string compress_password = 2; // 压缩密码（可选）
  string compress_password_deep = 3; // 嵌套压缩包密码（可选）
  string mail_sender = 4; // 邮件发送者（可选）
  string mail_receiver = 5; // 邮件接收者（可选）
  string mail_subject = 6; // 邮件主题（可选）
}
message SandboxCreateResp {
  int32 code = 1; // 状态码
  string msg = 2; // 错误信息

  message Data {
    int32 task_id = 1; // 检测任务Id
  }
  Data data = 3; // 数据
}

// 查询沙箱状态
message SandboxProbeReq {
  string address = 1; // 沙箱地址
  string auth_key = 2; // 沙箱认证key
}
message SandboxProbeResp {
}

// 沙箱检测结果回调接口
message PushSandboxDetectStatusReq {
  int32 task_id = 1; // 任务id
  int32 task_status = 2; // 任务状态 1:待检测，2:检测中，3:检测完成，4:检测失败，5:检测超时
  string filename = 3; // 样本文件名
  string sha256 = 4; // 样本sha256
  string md5 = 5; // 样本md5
  int32 risk_level = 6; // 风险级别 1 安全，2 疑似，3 风险
  int32 risk_value = 7; // 风险分值
  repeated int32 risk_types = 8; // 风险类型
}
message PushSandboxDetectStatusResp {
  int32 code = 1; // 错误码
  string msg = 2; // 错误信息
}

message SandboxTask {
  int32 task_id = 1; // 任务id
  int32 task_status = 2; // 任务状态
  string filename = 3; // 文件名
  string md5 = 4; // 文件md5
  string sha256 = 5; // 文件sha256
  int32 risk_level = 6; // 1 安全， 2 疑似， 3 风险
  int32 risk_value = 7; // 分值从 0 至 100，越高风险越大，可用于细粒度的风险值判断
  repeated int32 risk_types = 8; // 包含样本具体的风险类型
  string raw_result = 9; // 原始结果
}

// 自研沙箱任务状态查询
message QuerySandboxTaskStatusReq {
  int32 task_id = 1; // 任务id
  string sha256 = 2; // 文件sha256
  string md5 = 3; // 文件md5
}
message QuerySandboxTaskStatusResp {
  int32 code = 1; // 沙箱返回错误码
  string msg = 2; // 沙箱返回错误信息
  SandboxTask data = 3; // 沙箱返回结果
}

// 自研沙箱任务状态批量查询
message QuerySandboxTaskBatchStatusReq {
  repeated int32 task_ids = 1; // 任务id
  repeated string sha256s = 2; // 文件sha256
  repeated string md5s = 3; // 文件md5
}
message QuerySandboxTaskBatchStatusResp {
  int32 code = 1; // 沙箱返回错误码
  string msg = 2; // 沙箱返回错误信息
  repeated SandboxTask data = 3; // 沙箱返回结果
}

// 自研沙箱任务结果查询
message QuerySandboxTaskResultReq {
  int32 task_id = 1; // 任务id
  string sha256 = 2; // 文件sha256
  string md5 = 3; // 文件md5
}
message QuerySandboxTaskResultResp {
  int32 code = 1; // 沙箱返回错误码
  string msg = 2; // 沙箱返回错误信息
  SandboxTask data = 3; // 沙箱返回结果
}

// 查询云沙箱状态
message CloudSandboxProbeReq {
  string auth_addr = 1; // 身份认证地址
  string auth_key = 2; // 身份认证key
  string cloud_addr = 3; // 沙箱服务地址
  string cloud_secret = 4; // 沙箱服务密钥
  string bucket_addr = 5; // 存储桶地址
  string bucket_secret = 6; // 存储桶密钥
}
message CloudSandboxProbeResp {
}

// 文件检测条件
message DetectCond {
  int64 task_id = 1; // 检测任务Id
  string name = 2; // 文件名
  string type = 3; // 文件类型
  string bucket = 4; // 文件存储桶
  string path = 5; // 文件路径
  string sha256 = 6; // 文件sha256
  string md5 = 7; // 文件md5
  int32 retry = 8; // 重试次数
  int32 timeout = 9; // 超时时间
  mq.DetectEngine engine = 10; // 引擎类型
  int32 buf_chunk_size = 11; // 流式数据缓冲区大小，达到这个标准就需要发送数据
}

// 检测任务状态
enum DetectTaskStatus {
  UNKNOWN = 0;
  PENDING = 1; // 待检测
  RUNNING = 2; // 检测中
  SUCCESS = 3; // 检测成功
  FAIL = 4; // 检测失败
  FREEZE = 5; // 冻结中
  CANCEL = 6; // 取消
}