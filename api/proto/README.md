# vapi

vapi定义了v01集群版相关api，包含grpc,rpcx以及http等协议，后续新增的rpc,api等都应统一定义到该项目之下。

## 快速开始

* 执行Makefile中的install任务，安装特定版本的protoc及相关工具链，会将二进制文件下载至bin目录下。

```shell
make install
```

* 执行Makefile中的build任务编译proto文件，编译会只使用bin目录下的相关工具链，保持所有人编译后的文件一致。

```shell
make build
```

### 注意事项

* 使用grpc定义api时直接在proto目录下编写服务的proto文件即可，之后使用make build生成对应的go文件，会按照文件名在pkg目录下创建同名目录，
  用于管理该服务的rpc定义。

* 果使用rpcx或http定义api，无法使用工具生成，直接在pkg目录下手动创建服务同名目录，在该目录下编写服务的api定义，具体风格和grpc生成的尽量保持
  一致，如包含参数的结构体、一个能直接使用的client等。

* 不要在vapi中编写服务的具体实现逻辑，vapi中应该只包含服务rpc的签名信息。

* 只可以更改自己负责的服务的rpc定义，不得更改其他人的rpc，如果希望某服务提供新的rpc则需要对应的负责人自己添加。

* 一旦rpc被暴露出去则rpc签名及业务逻辑必须保持兼容。
