---
Language: Proto
BasedOnStyle: Google
ColumnLimit: 120 # 不限制列宽
IndentWidth: 2
UseTab: Never
AlignConsecutiveAssignments: true
AlignConsecutiveDeclarations: false
AllowShortBlocksOnASingleLine: Empty
AllowShortIfStatementsOnASingleLine: Never
AllowShortLoopsOnASingleLine: false
BreakBeforeBraces: Attach
DerivePointerAlignment: false
PointerAlignment: Left
SpacesBeforeTrailingComments: 2
ReflowComments: true
SortIncludes: true
AlignTrailingComments:
  Kind: Always
# 增加以下设置以控制换行行为
BreakBeforeInheritanceComma: false
BreakConstructorInitializersBeforeComma: false
BreakStringLiterals: false
