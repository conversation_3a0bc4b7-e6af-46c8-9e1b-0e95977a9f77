# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: olaf/olaf.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0folaf/olaf.proto\x12\x04olaf\"y\n\rFileDetectReq\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x0e\n\x06sha256\x18\x02 \x01(\t\x12\x12\n\nchunk_data\x18\x03 \x01(\x0c\x12\x0b\n\x03\x65of\x18\x04 \x01(\x08\x12%\n\x0b\x64\x65tect_type\x18\x05 \x01(\x0e\x32\x10.olaf.DetectType\"D\n\x0e\x46ileDetectResp\x12\r\n\x05score\x18\x01 \x01(\x05\x12#\n\nmodel_type\x18\x02 \x01(\x0e\x32\x0f.olaf.ModelType*@\n\nDetectType\x12\x12\n\x0e\x44\x45TECT_UNKNOWN\x10\x00\x12\r\n\tDETECT_AI\x10\x01\x12\x0f\n\x0b\x44\x45TECT_AIPE\x10\x02*M\n\tModelType\x12\x11\n\rMODEL_UNKNOWN\x10\x00\x12\r\n\tMODEL_PDF\x10\x01\x12\x10\n\x0cMODEL_OFFICE\x10\x02\x12\x0c\n\x08MODEL_PE\x10\x03\x32\x45\n\x04Olaf\x12=\n\nFileDetect\x12\x13.olaf.FileDetectReq\x1a\x14.olaf.FileDetectResp\"\x00(\x01\x30\x01\x42)Z\'git.anxin.com/v01-cluster/vapi/pkg/olafb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'olaf.olaf_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\'git.anxin.com/v01-cluster/vapi/pkg/olaf'
  _globals['_DETECTTYPE']._serialized_start=218
  _globals['_DETECTTYPE']._serialized_end=282
  _globals['_MODELTYPE']._serialized_start=284
  _globals['_MODELTYPE']._serialized_end=361
  _globals['_FILEDETECTREQ']._serialized_start=25
  _globals['_FILEDETECTREQ']._serialized_end=146
  _globals['_FILEDETECTRESP']._serialized_start=148
  _globals['_FILEDETECTRESP']._serialized_end=216
  _globals['_OLAF']._serialized_start=363
  _globals['_OLAF']._serialized_end=432
# @@protoc_insertion_point(module_scope)
