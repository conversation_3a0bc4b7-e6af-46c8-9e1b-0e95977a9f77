# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import olaf_pb2 as olaf_dot_olaf__pb2


class OlafStub(object):
    """<PERSON>（ai检测服务）
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.FileDetect = channel.stream_stream(
                '/olaf.<PERSON>/FileDetect',
                request_serializer=olaf_dot_olaf__pb2.FileDetectReq.SerializeToString,
                response_deserializer=olaf_dot_olaf__pb2.FileDetectResp.FromString,
                )


class OlafServicer(object):
    """<PERSON>（ai检测服务）
    """

    def FileDetect(self, request_iterator, context):
        """文件检测
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_OlafServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'FileDetect': grpc.stream_stream_rpc_method_handler(
                    servicer.FileDetect,
                    request_deserializer=olaf_dot_olaf__pb2.FileDetectReq.FromString,
                    response_serializer=olaf_dot_olaf__pb2.FileDetectResp.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'olaf.Olaf', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class Olaf(object):
    """Olaf（ai检测服务）
    """

    @staticmethod
    def FileDetect(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/olaf.Olaf/FileDetect',
            olaf_dot_olaf__pb2.FileDetectReq.SerializeToString,
            olaf_dot_olaf__pb2.FileDetectResp.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
