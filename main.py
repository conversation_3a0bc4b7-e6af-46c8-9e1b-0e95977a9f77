from concurrent import futures
from api.pb.olaf import olaf_pb2_grpc
from grpc_health.v1 import health, health_pb2, health_pb2_grpc

import grpc
import src.controller.ctr as ctr
import conf.olaf as olaf_conf


def run_server():
    olaf_conf.init_conf()

    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    health_servicer = health.HealthServicer()
    health_servicer.set(
        service='grpc.health.v1.Health',
        status=health_pb2.HealthCheckResponse.SERVING
    )
    health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)

    olaf_pb2_grpc.add_OlafServicer_to_server(ctr.OlafServicer(), server)

    server.add_insecure_port('[::]:50051')
    server.start()
    print("gRPC server started on port 50051 with reflection enabled")
    server.wait_for_termination()

if __name__ == '__main__':
    run_server()