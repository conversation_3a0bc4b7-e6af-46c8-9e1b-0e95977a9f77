# Olaf 服务配置

# 服务配置
server:
  mode: debug # server mode: release, debug, test，默认 release
  port: 50051
  healthz: true # 是否开启健康检查，如果开启会安装 /healthz 路由，默认 true

# Log 配置
log:
  name: olaf # Logger的名字
  development: true # 是否是开发模式。如果是开发模式，会对DPanicLevel进行堆栈跟踪。
  level: debug # 日志级别，优先级从低到高依次为：debug, info, warn, error, dpanic, panic, fatal。
  format: console # 支持的日志输出格式，目前支持console和json两种。console其实就是text格式。
  enable-color: true # 是否开启颜色输出，true:是，false:否
  disable-caller: false # 是否开启 caller，如果开启会在日志中显示调用日志所在的文件、函数和行号
  disable-stacktrace: false # 是否再panic及以上级别禁止打印堆栈信息
