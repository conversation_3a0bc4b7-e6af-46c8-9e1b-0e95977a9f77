import os
import yaml
import threading
from pydantic import BaseModel, Field
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler


class ServerConfig(BaseModel):
    mode: str = Field("release")
    port: int = Field(50051)
    healthz: bool = Field(True)


class LogConfig(BaseModel):
    name: str = Field("olaf")
    development: bool = Field(True)
    level: str = Field("debug")
    format: str = Field("console")
    enable_color: bool = Field(True)
    disable_caller: bool = Field(False)
    disable_stacktrace: bool = Field(False)


class OlafConfig(BaseModel):
    server: ServerConfig = ServerConfig()
    log: LogConfig = LogConfig()

    @classmethod
    def from_yaml(cls, path: str = "./conf/olaf.yaml") -> "OlafConfig":
        with open(path, "r", encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return cls(**data)


class ConfigReloader(FileSystemEventHandler):
    def __init__(self, config_file: str, config_cls: type, on_change=None):
        self.config_file = config_file
        self.config_cls = config_cls
        self.on_change = on_change
        self._load_config()

    def _load_config(self):
        global config
        try:
            config = self.config_cls.from_yaml(self.config_file)
            print(f"🔄 Config reloaded from {self.config_file}")
            if self.on_change:
                self.on_change(config)
        except Exception as e:
            print(f"⚠️ Failed to reload config: {e}")

    def on_modified(self, event):
        if event.src_path == os.path.abspath(self.config_file):
            self._load_config()


def init_conf():
    # 初始化配置
    config_file = os.getenv("OLAF_CONFIG", "./conf/olaf.yaml")
    config = OlafConfig.from_yaml(config_file)

    # 启动文件监听
    observer = Observer()
    reloader = ConfigReloader(config_file, OlafConfig)
    observer.schedule(reloader, path=os.path.dirname(config_file), recursive=False)
    observer.start()

    # 启动守护线程，确保程序退出时停止观察者
    threading.Thread(target=observer.join, daemon=True).start()