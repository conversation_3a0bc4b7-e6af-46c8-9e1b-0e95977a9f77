import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from api.pb.olaf import olaf_pb2
from api.pb.olaf import olaf_pb2_grpc

import grpc

def generate_requests(filepath):
    chunk_size = 1024 * 512  # 512 KB
    with open(filepath, "rb") as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            yield olaf_pb2.FileDetectReq(
                chunk_data=chunk,
            )
    yield olaf_pb2.FileDetectReq(
        filename=r"1bff2b01511a57d1f4403735e20afdd7(cb825670)",
        sha256="abc123",
        eof=True,
    )

def run():
    with grpc.insecure_channel("127.0.0.1:50051") as channel:
        stub = olaf_pb2_grpc.OlafStub(channel)
        responses = stub.FileDetect(generate_requests(r"./../requirements.txt"))
        for resp in responses:
            print(f"✅ Got response: score = {resp.score}")

if __name__ == "__main__":
    run()
