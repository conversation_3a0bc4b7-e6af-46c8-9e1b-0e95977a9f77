"""
通用日志组件
提供结构化的检测日志记录功能，支持单行聚合输出
"""

import logging
import time
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum


class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"


class DetectionContext:
    """
    检测上下文类，用于收集检测过程中的各种信息，最后统一输出日志
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.data = {}
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('detection')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s', 
                                        datefmt='%Y-%m-%d %H:%M:%S')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def set_file_info(self, filename: str, sha256: str, temp_path: str) -> 'DetectionContext':
        """设置文件信息"""
        self.data.update({
            'filename': filename,
            'sha256': sha256,
            'temp_path': temp_path
        })
        return self
    
    def set_detect_type(self, detect_type: str) -> 'DetectionContext':
        """设置检测类型"""
        self.data['detect_type'] = detect_type
        return self
    
    def set_file_type(self, file_type: str) -> 'DetectionContext':
        """设置文件类型"""
        self.data['file_type'] = file_type
        return self
    
    def set_result(self, score: int, model_type: str) -> 'DetectionContext':
        """设置检测结果"""
        self.data.update({
            'score': score,
            'model_type': model_type
        })
        return self
    
    def set_error(self, error_msg: str) -> 'DetectionContext':
        """设置错误信息"""
        self.data['error'] = error_msg
        return self
    
    def add_custom_field(self, key: str, value: Any) -> 'DetectionContext':
        """添加自定义字段"""
        self.data[key] = value
        return self
    
    def _calculate_duration(self) -> int:
        """计算处理耗时（毫秒）"""
        return int((time.time() - self.start_time) * 1000)
    
    def _format_log_message(self) -> str:
        """格式化日志消息为单行输出"""
        duration = self._calculate_duration()
        
        # 构建键值对字符串
        parts = []
        
        # 核心字段按固定顺序输出
        core_fields = ['filename', 'sha256', 'detect_type', 'file_type', 'score', 'model_type']
        for field in core_fields:
            if field in self.data:
                value = self.data[field]
                # 对于文件名，只显示前20个字符避免过长
                if field == 'filename' and len(str(value)) > 30:
                    value = str(value)[:30] + "..."
                # 对于临时路径，只显示文件名部分
                elif field == 'temp_path':
                    continue  # 临时路径不在核心输出中
                parts.append(f"{field}={value}")
        
        # 添加错误信息（如果有）
        if 'error' in self.data:
            parts.append(f"error={self.data['error']}")
        
        # 添加其他自定义字段
        for key, value in self.data.items():
            if key not in core_fields + ['error', 'temp_path']:
                parts.append(f"{key}={value}")
        
        # 添加耗时
        parts.append(f"duration={duration}ms")
        
        return "DETECTION_SUMMARY " + " ".join(parts)
    
    def log_summary(self, level: LogLevel = LogLevel.INFO) -> None:
        """输出聚合日志"""
        message = self._format_log_message()
        
        if level == LogLevel.DEBUG:
            self.logger.debug(message)
        elif level == LogLevel.INFO:
            self.logger.info(message)
        elif level == LogLevel.WARNING:
            self.logger.warning(message)
        elif level == LogLevel.ERROR:
            self.logger.error(message)
    
    def log_error_summary(self, error_msg: str) -> None:
        """输出错误日志"""
        self.set_error(error_msg)
        self.log_summary(LogLevel.ERROR)


def create_detection_context() -> DetectionContext:
    """创建检测上下文的工厂函数"""
    return DetectionContext()


# 便捷函数
def log_detection_info(filename: str, sha256: str, detect_type: str, 
                      file_type: str, score: int, model_type: str, 
                      temp_path: str = "", **kwargs) -> None:
    """
    便捷函数：直接记录检测信息日志
    """
    ctx = create_detection_context()
    ctx.set_file_info(filename, sha256, temp_path)
    ctx.set_detect_type(detect_type)
    ctx.set_file_type(file_type)
    ctx.set_result(score, model_type)
    
    # 添加额外字段
    for key, value in kwargs.items():
        ctx.add_custom_field(key, value)
    
    ctx.log_summary()


def log_detection_error(filename: str, error_msg: str, **kwargs) -> None:
    """
    便捷函数：直接记录检测错误日志
    """
    ctx = create_detection_context()
    ctx.set_file_info(filename, "", "")
    ctx.set_error(error_msg)
    
    # 添加额外字段
    for key, value in kwargs.items():
        ctx.add_custom_field(key, value)
    
    ctx.log_error_summary(error_msg)
