import time
import uuid
import shutil
import os
import requests
from fastapi import FastAPI, HTTPException, logger
import urllib3
from feature_src.predict import check_1pe_once

app = FastAPI()

def download_file(url, num_retries=4):
    """
    Download file from a given URL to a local path, or copy it if it is a local file
    """
    TEMP_DIR = "/home/<USER>/Desktop/share_files"  # 请根据实际情况设定临时目录路径

    if url.startswith('file://'):
        local_file_path = urllib3.parse.unquote_plus(url[7:])
        if os.path.exists(local_file_path):
            unique_filename = str(uuid.uuid4())
            local_filename = os.path.join(TEMP_DIR, unique_filename)
            shutil.copy2(local_file_path, local_filename)
            return local_filename
        else:
            raise HTTPException(status_code=400, detail=f"No such file: {url}")
    else:
        for i in range(num_retries + 1):
            unique_filename = str(uuid.uuid4())
            local_filename = os.path.join(TEMP_DIR, unique_filename)
            if os.path.exists(local_filename):
                os.remove(local_filename)

            try:
                response = requests.get(url, stream=True)
                response.raise_for_status()
            except requests.exceptions.HTTPError as err:
                logger.error(f"HTTP Error during file download: {str(err)}")
                if i < num_retries:
                    time.sleep(2**i)
                    continue
                else:
                    raise HTTPException(status_code=502, detail=str(err))

            try:
                with open(local_filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                return local_filename
            except Exception as err:
                raise HTTPException(status_code=500, detail=f"Error during file writing: {str(err)}")

        raise HTTPException(status_code=504, detail="File download timed out after retries.")

def get_predict(pe_path: str) -> bool:
    mal_score = check_1pe_once(pe_path)
    return bool(mal_score > 0.73)



# return jsonify({'task_id': task_id, 'sha256': sha256, 'error_code': error_code, 'mal_degree': ''}), 429  # 429 Too Many Requests



@app.post("/analyze")
async def analyze(request_data: dict):
    file_url = request_data.get('url')
    file_type = request_data.get('filetype')
    sha256 = request_data.get('sha256')
    task_id = request_data.get('task_id')
    
    if not all([file_url, sha256, task_id]):
        raise HTTPException(status_code=400, detail="Missing required parameters.")
    
    try:
        temp_pe_path = download_file(file_url)
    except HTTPException as e:
        return {"task_id": task_id, "sha256": sha256, "mal_degree": ""}, 444
    
    try:
        is_malicious = get_predict(temp_pe_path)
    except Exception as e:
        os.remove(temp_pe_path)
        return {"task_id": task_id, "sha256": sha256, "mal_degree": str(e)}, 540
    
    os.remove(temp_pe_path)  # 清理临时文件
    

    mal_degree =  is_malicious 
    return {"task_id": task_id, "sha256": sha256, "mal_degree": mal_degree}, 200