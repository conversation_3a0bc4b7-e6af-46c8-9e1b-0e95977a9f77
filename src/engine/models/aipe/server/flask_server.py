import multiprocessing
from queue import Empty
import sys
from flask import Flask, request, jsonify
import uuid
import os
import re
import requests
from gunicorn.app.base import BaseApplication
import yaml

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

if ROOT_DIR:
    from feature_src.predict import check_1pe_once,part_1_get_features,part_2_predict
    from utils.logger_config import setup_logger
    logfile = os.path.join(ROOT_DIR, 'logs', 'pe_scan.log')
    logger = setup_logger('flask_server', logfile)

    config_path = os.path.join(
        ROOT_DIR, 'server', 'config', 'server_conf.yml')
    with open(config_path, "r") as ymlfile:
        cfg = yaml.load(ymlfile, Loader=yaml.FullLoader)

app = Flask(__name__)

TEMP_DIR = f"{ROOT_DIR}/tmp"  
if not os.path.exists(TEMP_DIR):
    os.makedirs(TEMP_DIR)


def download_file_tset(url):
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
    except requests.exceptions.HTTPError as err:
        app.logger.error(f"HTTP Error during file download: {str(err)}")
        return jsonify({"error": str(err)}), 502

    unique_filename = str(uuid.uuid4())
    local_filename = os.path.join(TEMP_DIR, unique_filename)
    with open(local_filename, 'wb') as f:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                f.write(chunk)
    return local_filename
    
def download_file(url, num_retries=4):
    """
    Download file from a given URL to a local path, or copy it if it is a local file
    """
    # 存放到tmp下
    # TEMP_DIR = "/path/to/tmp"  # 临时目录路径

    if url.startswith('file://'):
        local_file_path = urllib.parse.unquote_plus(url[7:])
        if os.path.exists(local_file_path):
            # 生成唯一的本地文件名
            unique_filename = str(uuid.uuid4())
            local_filename = os.path.join(TEMP_DIR, unique_filename)

            shutil.copy2(local_file_path, local_filename)
        else:
            logger.error(f"No such file: {url}")
            return None
        return local_filename
    else:
        for i in range(num_retries + 1):  # Try to download the file num_retries times
            # 生成唯一的本地文件名
            unique_filename = str(uuid.uuid4())
            local_filename = os.path.join(TEMP_DIR, unique_filename)

            if os.path.exists(local_filename):  # Check if the file already exists
                os.remove(local_filename)  # If so, remove it

            try:
                response = requests.get(url, stream=True, verify=False)
                response.raise_for_status()  # Raise an HTTPError if the response was unsuccessful
            except requests.exceptions.HTTPError as err:
                # Retry if response was unsuccessful
                logger.error(f"HTTP Error during file download: {str(err)}")
                if i < num_retries:  # Only delay if we are going to retry
                    time.sleep(2**i)  # Exponential backoff
                continue  # Go to the next loop iteration to retry

            try:
                with open(local_filename, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:  # filter keep-alive new chunks
                            f.write(chunk)
                return local_filename
            except Exception as err:
                logger.error(f"Error during file writing : {str(err)}")

        # If the retries have all been used and the file is still not downloaded, return None
        return None


def get_predict(pe_path):
    mal_score = check_1pe_once(pe_path)
    # TODO:拆成两部分考虑，对提取特征进行多线程处理
    # features = part_1_get_features(pe_path)
    # mal_score = part_2_predict(features,pe_path)
    return mal_score > 0.73

def get_file_size(file_path, type='MB'):
    """
    Get the size of a file in megabytes (MB)
    """
    size_in_bytes = os.path.getsize(file_path)
    if type == 'MB':
        size_ = size_in_bytes / (1024 * 1024)
    elif type == 'KB':
        size_ = size_in_bytes / (1024)
    else:
        raise ValueError("Invalid type. Please use 'MB' or 'KB'.")
    return size_


def check_install_uninstall(filename):
    """
    检查文件名是否包含与安装或卸载相关的关键词，并尝试识别版本号。
    如果版本号的".数字"序列超过四次，则认为是版本号。
    """
    # 匹配与安装相关的关键词
    install_pattern = re.compile(
        r'(inst|setup|configure|deployment|update|patch|installer|windows|x64|x86|win32|win64|amd64)', re.IGNORECASE)

    # 匹配与卸载相关的关键词
    uninstall_pattern = re.compile(
        r'(unin|uninst|remove|deinstall|cleanup|cleaner|uninstaller)', re.IGNORECASE)

    # 使用search()来查看是否有匹配
    is_install = bool(install_pattern.search(filename))
    is_uninstall = bool(uninstall_pattern.search(filename))

    # 匹配版本号，这里使用非贪婪量词来匹配尽可能少的".数字"序列
    version_pattern = re.compile(r'\d+(?:\.\d+)*')
    version_num = version_pattern.search(filename)
    # print(f"get version {has_version}")
    has_version = False
    if version_num and version_num.group(0).count(".") > 2:
        has_version = True
        

    return is_install or is_uninstall or has_version



def get_file_extension(filename):
    """
    获取文件名的后缀名（扩展名）。

    :param filename: 文件名字符串
    :return: 文件的后缀名（不带点+大写），如果没有后缀则返回"NONE"
    """
    ext = os.path.splitext(filename)[1][1:]
    if ext:
        return ext.upper()
    else:
        return "NONE"

@app.route('/analyze', methods=['POST'])
def analyze():
    # data = {
    #     'url': file_url,
    #     'filetype': filetype,  
    #     'sha256': sha256,
    #     'task_id': task_id
    # }

    data = request.get_json()
    file_url = data.get('url')
    file_type = data.get('filetype')
    sha256 = data.get('sha256')
    task_id = data.get('task_id')
    filename = data.get('filename')
    
    # 返回数据格式
    ret_data = {'task_id': task_id, 'sha256': sha256}
    mal_degree =''
    error_code = cfg['errcode']['no_error']

    
    # TODO:增加一些文件名过滤
    # print(filename)
    logger.info(f"get file {filename}")
    ext = get_file_extension(filename)
    if ext != "NONE":
        if ext in ['SFX','MSI'] or check_install_uninstall(filename):
            ret_data.update({'error_code': error_code,'mal_degree': 0})
            logger.info(f"Filtered at the file name")
            requests.post(cfg['server']['post_url'], json=ret_data)
            return ret_data, 200


    # 限制流量
    temp_dir = TEMP_DIR
    file_count = len([name for name in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, name))])
    if file_count >= cfg['server']['max_file_count']:  
        error_code = cfg['errcode']['timeout_error']
        ret_data.update({'error_code': error_code, 'mal_degree':mal_degree })
        logger.info(f"Too many files are accepted, processing is blocked")
        requests.post(cfg['server']['post_url'], json=ret_data)
        return ret_data, 429  # 429 Too Many Requests
       

    try:
        temp_pe_path = download_file(file_url)
    except Exception as e:
        logger.info(f"Failed to download the file {filename}")
        error_code = cfg['errcode']['download_error']
        ret_data.update({'error_code': error_code, 'mal_degree':mal_degree })
        requests.post(cfg['server']['post_url'], json=ret_data)
        return ret_data, 500

 
    # 控制文件体积小于 50M 且最小文件不小于1k
    if not os.path.exists(temp_pe_path):
        logger.error('file not exists!')
        return
        
        
    file_size = get_file_size(temp_pe_path)
    if file_size > cfg['server']['max_size'] or (file_size*1024) < cfg['server']['min_size']:
        os.remove(temp_pe_path)
        error_code = cfg['errcode']['size_error']
        ret_data.update({'error_code': error_code, 'mal_degree': mal_degree})
        logger.info(f"The {filename} is too large or too small to be filtered ")
        requests.post(cfg['server']['post_url'], json=ret_data)
        return ret_data, 413



    try:
        is_malicious = get_predict(temp_pe_path)
    except Exception as e:
        os.remove(temp_pe_path)
        logger.error(f"{filename} get predict error!")
        error_code = cfg['errcode']['else_error']
        ret_data.update({'error_code': error_code, 'mal_degree': mal_degree })
        requests.post(cfg['server']['post_url'], json=ret_data)
        return ret_data, 500

    os.remove(temp_pe_path)
    mal_degree = int(is_malicious)
    logger.info(f"{filename} get predict {mal_degree}")
    ret_data.update({'error_code': error_code,'mal_degree': mal_degree})
    requests.post(cfg['server']['post_url'], json=ret_data)
    return ret_data, 200


class GunicornServer(BaseApplication):
    def __init__(self, app, options=None):
        self.options = options or {}
        self.application = app
        super().__init__()

    def load_config(self):
        for key, value in self.options.items():
            self.cfg.set(key, value)

        # 添加禁用日志记录的配置项
        # self.cfg.set('accesslog', None)
        # self.cfg.set('errorlog', None)
        self.cfg.set('loglevel', 'ERROR')
        self.cfg.set('capture_output', True)

    def load(self):
        return self.application


def start_server():
    port = cfg['server']['port']
    ip = cfg['server']['ip']
    options = {
        'bind': ip + f':{port}',  # 指定要绑定的地址和端口
        'workers': cfg['server']['workers'],  # 设置工作进程数量
    }

    server = GunicornServer(app, options)
    server.run()


if __name__ == '__main__':
    start_server()
