'''
定义必要的全局路径，或其他共享的配置

'''


import os
from pathlib import Path

current_path = Path(__file__).resolve()

# 获取项目的根目录 ai_pe/
project_root = current_path.parent.parent
# print(project_root)

# 全局路径和变量
TEST_DIR = os.path.join(project_root, 'test')
OUT_DIR = os.path.join(project_root, 'out')
MODELS_DIR = os.path.join(project_root, 'models')


MAL_BODAS_DIR = r'E:\BODAS\BODMAS_malwares_2\altered'
BEN_ANXIN_DIR = r'E:\anxin_pe\0c_ben_85k'


YARA_PATH = os.path.join(project_root, 'tools', 'packer.yara')


# 模型的阈值

base_et220_threshold = 0.73
plus4_rf230_threshold = 0.95


# 对文件大小的限制，单位MB

pe_size_threshold = 50
