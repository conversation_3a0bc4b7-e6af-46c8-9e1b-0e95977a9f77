'''
兼容原来的ember模型，但是这里选择的特征都是更有区分度的。
例如，使用某些特征选择手段从众多特征中筛选过的，或者积累了某些现有的规则知识。

'''

import hashlib
import json
import os
import numpy as np
import lief
import pandas as pd
from sklearn.feature_extraction import FeatureHasher

IS_TEST = False


class FeatureType(object):
    ''' Base class from which each feature type may inherit '''

    name = ''
    dim = 0

    def __repr__(self):
        """
        Return a string representation of the object.
        """
        return '{}({})'.format(self.name, self.dim)

    def raw_features(self, bytez, lief_binary):
        ''' Generate a JSON-able representation of the file '''
        raise (NotImplementedError)

    def process_raw_features(self, raw_obj):
        ''' Generate a feature vector from the raw features '''
        raise (NotImplementedError)

    def feature_vector(self, bytez, lief_binary):
        ''' Directly calculate the feature vector from the sample itself. This should only be implemented differently
        if there are significant speedups to be gained from combining the two functions. '''
        return self.process_raw_features(self.raw_features(bytez, lief_binary))


class HeaderFileInfo(FeatureType):
    ''' Machine, architecture, OS, linker, and other information extracted from the header '''

    name = 'header'
    # TODO: 这里的维度需要重新去计算
    dim = 81

    def __init__(self):
        super().__init__()

    def extract_coff_features(self, binary):
        return {
            'timestamp': binary.header.time_date_stamps,
            'machine': int(binary.header.machine),
            'characteristics': int(binary.header.characteristics),
            # [int(c) for c in binary.header.characteristics_list],
            # [str(c).split('.')[-1] for c in binary.header.characteristics_list],

            'number_of_sections': binary.header.numberof_sections,
            'number_of_symbols': binary.header.numberof_symbols
        }

    def extract_optional_features(self, binary):
        optional_header = binary.optional_header
        return {
            'subsystem': int(optional_header.subsystem),
            #   str(optional_header.subsystem).split('.')[-1],
            'dll_characteristics': optional_header.dll_characteristics,
            #   [str(c).split('.')[-1] for c in optional_header.dll_characteristics_lists],
            'magic': int(optional_header.magic),
            # str(optional_header.magic).split('.')[-1],
            'address_of_entry_point': optional_header.addressof_entrypoint,
            'image_base': optional_header.imagebase,
            'major_image_version': optional_header.major_image_version,
            'minor_image_version': optional_header.minor_image_version,
            'major_linker_version': optional_header.major_linker_version,
            'minor_linker_version': optional_header.minor_linker_version,
            'major_operating_system_version': optional_header.major_operating_system_version,
            'minor_operating_system_version': optional_header.minor_operating_system_version,
            'major_subsystem_version': optional_header.major_subsystem_version,
            'minor_subsystem_version': optional_header.minor_subsystem_version,
            'sizeof_code': optional_header.sizeof_code,
            'sizeof_headers': optional_header.sizeof_headers,
            'sizeof_heap_commit': optional_header.sizeof_heap_commit,
            'sizeof_heap_reserve': optional_header.sizeof_heap_reserve,


            'sizeof_initialized_data': optional_header.sizeof_initialized_data,
            'sizeof_uninitialized_data': optional_header.sizeof_uninitialized_data,
            'baseof_code': optional_header.baseof_code,
            'checksum': optional_header.checksum,
            'sizeof_image': optional_header.sizeof_image,
            'sizeof_stack_reserve': optional_header.sizeof_stack_reserve,
            'sizeof_stack_commit': optional_header.sizeof_stack_commit,

            'section_alignment': optional_header.section_alignment,
            'file_alignment': optional_header.file_alignment
        }

    def calculate_section_properties(self, binary):
        section_properties = {
            'max_char': 0,
            'min_virtual_size': float('inf'),
            'min_entropy': float('inf'),
            'min_rawsize': float('inf'),
            'max_physical_address': 0,
            'max_pointer_data': 0,
            'max_virtual_address': 0,

            'length': len(binary.sections),
        }

        for section in binary.sections:
            section_properties['max_char'] = max(
                section_properties['max_char'], section.characteristics)
            section_properties['min_virtual_size'] = min(
                section_properties['min_virtual_size'], section.virtual_size)
            section_properties['min_rawsize'] = min(
                section_properties['min_rawsize'], section.sizeof_raw_data)
            section_properties['max_physical_address'] = max(
                section_properties['max_physical_address'], section.virtual_address+section.sizeof_raw_data)
            section_properties['max_pointer_data'] = max(
                section_properties['max_pointer_data'], section.pointerto_raw_data)
            section_properties['max_virtual_address'] = max(
                section_properties['max_virtual_address'], section.virtual_address)

            # 更新最小熵值
            section_properties['min_entropy'] = min(
                section_properties['min_entropy'], section.entropy)

        return section_properties

    def extract_directory_features(self, binary):
        data_directories = binary.data_directories

        # print(type(data_directories))
        # print(dir(data_directories))

        # 用于debug时使用查看当前版本下支持的特征或属性名称
        # tmp = lief.PE
        # print(dir(tmp))

        # 0.14.0
        # types = lief.PE.DataDirectory.TYPES

        '''
        ARCHITECTURE = lief._lief.PE.TYPES.ARCHITECTURE
        BASE_RELOCATION_TABLE = lief._lief.PE.TYPES.BASE_RELOCATION_TABLE
        BOUND_IMPORT = lief._lief.PE.TYPES.BOUND_IMPORT
        CERTIFICATE_TABLE = lief._lief.PE.TYPES.CERTIFICATE_TABLE
        CLR_RUNTIME_HEADER = lief._lief.PE.TYPES.CLR_RUNTIME_HEADER
        DEBUG = lief._lief.PE.TYPES.DEBUG
        DELAY_IMPORT_DESCRIPTOR = lief._lief.PE.TYPES.DELAY_IMPORT_DESCRIPTOR
        EXCEPTION_TABLE = lief._lief.PE.TYPES.EXCEPTION_TABLE
        EXPORT_TABLE = lief._lief.PE.TYPES.EXPORT_TABLE
        GLOBAL_PTR = lief._lief.PE.TYPES.GLOBAL_PTR
        IAT = lief._lief.PE.TYPES.IAT
        IMPORT_TABLE = lief._lief.PE.TYPES.IMPORT_TABLE
        LOAD_CONFIG_TABLE = lief._lief.PE.TYPES.LOAD_CONFIG_TABLE
        RESERVED = lief._lief.PE.TYPES.RESERVED
        RESOURCE_TABLE = lief._lief.PE.TYPES.RESOURCE_TABLE
        TLS_TABLE = lief._lief.PE.TYPES.TLS_TABLE
        UNKNOWN = lief._lief.PE.TYPES.UNKNOWN
        '''

        # return {
        #     'ImageDirectoryEntryExport': (data_directories[types.EXPORT_TABLE].rva),
        #     'ImageDirectoryEntryImport': (data_directories[types.IMPORT_TABLE].rva),
        #     'ImageDirectoryEntrySecurity': (data_directories[types.CERTIFICATE_TABLE].rva),
        #     'ImageDirectoryEntryResource': (data_directories[types.RESOURCE_TABLE].rva),
        #     'DirectoryEntryImportSize': data_directories[types.IMPORT_TABLE].size,
        #     'DirectoryEntryExportSize': data_directories[types.EXPORT_TABLE].size
        # }

        # 0.12.3
        pe = lief.PE
        types = lief.PE.DATA_DIRECTORY
        return {
            'ImageDirectoryEntryExport': (data_directories[types.EXPORT_TABLE].rva),
            'ImageDirectoryEntryImport': (data_directories[types.IMPORT_TABLE].rva),
            'ImageDirectoryEntrySecurity': (data_directories[types.CERTIFICATE_TABLE].rva),
            'ImageDirectoryEntryResource': (data_directories[types.RESOURCE_TABLE].rva),
            'DirectoryEntryImportSize': data_directories[types.IMPORT_TABLE].size,
            'DirectoryEntryExportSize': data_directories[types.EXPORT_TABLE].size
        }

    def raw_to_df(self, raw_obj):
        #  raw_obj 您的原始字典数据

        # 扁平化字典
        flat_data = {}
        for key, value in raw_obj.items():
            if isinstance(value, dict):
                flat_data.update({f"{key}_{k}": v for k, v in value.items()})
            else:
                flat_data[key] = value

        # 转换为DataFrame
        df = pd.DataFrame(flat_data, index=[0])

        return df

    def raw_features(self, bytez, lief_binary):
        if lief_binary is None:
            return None

        if IS_TEST:
            # test 查看optional所有特征
            print('optional header like flowing:')
            print(lief_binary.optional_header)
            # end test

        raw_obj = {
            'coff': self.extract_coff_features(lief_binary),
            'optional': self.extract_optional_features(lief_binary),
            'sections': self.calculate_section_properties(lief_binary),
            'directories': self.extract_directory_features(lief_binary),
        }

        df_raw = self.raw_to_df(raw_obj)

        return df_raw

    def process_raw_features(self, raw_obj):

        return None


class PEFeatureExtractor(object):
    ''' Extract useful features from a PE file, and return as a vector of fixed size. '''

    def __init__(self):
        self.features = []
        features = {
            'HeaderFileInfo': HeaderFileInfo(),  # 4
        }

        self.features = list(features.values())

        # print(f"WARNING:   lief version {lief.__version__} >= 0.14.0")

        # TODO: 维度没有更新
        self.dim = sum([fe.dim for fe in self.features])

    def raw_features(self, bytez):
        lief_errors = (lief.bad_format, lief.bad_file, lief.pe_error,
                       lief.parser_error, lief.read_out_of_bound, RuntimeError)
        try:
            lief_binary = lief.PE.parse(list(bytez))
        except lief_errors as e:
            print("lief error: ", str(e))
            lief_binary = None
        # everything else (KeyboardInterrupt, SystemExit, ValueError):
        except Exception:
            raise

        features = {"sha256": hashlib.sha256(bytez).hexdigest()}
        features.update({fe.name: fe.raw_features(bytez, lief_binary)
                        for fe in self.features})
        return features

    def process_raw_features(self, raw_obj):
        feature_vectors = [fe.process_raw_features(
            raw_obj[fe.name]) for fe in self.features]
        return np.hstack(feature_vectors).astype(np.float32)

    def feature_vector(self, bytez):
        return self.process_raw_features(self.raw_features(bytez))

    # 实际使用的提取逻辑

    def set_bins(self, feature, labels, bins, df):
        # 使用pd.cut将数值映射到区间标签
        df[f'{feature}_'] = pd.cut(
            df[feature], bins=bins, labels=labels, right=False)

        # 将区间标签转换为虚拟/指示变量
        df = pd.get_dummies(df, columns=[f'{feature}_'])

        # 如果需要移除原始列
        df.drop(columns=feature, inplace=True)

        return df

    def set_bins_process(self, df):
        bins = [0, 0.5, 1.5, 2.5, float('inf')]
        labels = ['0', '1', '2', '3_inf']  #
        feature = 'optional_minor_operating_system_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 5.5, 6.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['4_5', '6', 'inf']
        feature = 'optional_major_operating_system_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 5.5, 6.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['4_5', '6', '7_inf']
        feature = 'optional_major_subsystem_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 0.5, 2.5, 3.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0', '1_2', '3', '4_inf']
        feature = 'optional_minor_subsystem_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 2.5, 3.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0_2', '3', '4_inf']
        feature = 'optional_subsystem'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 1e9+100, 3e9+100, 4e9+100, 7e9 +
                100,  float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['1e9', '2_3e9', '4e9', '7e9', '7e9_inf']
        feature = 'optional_image_base'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 5e5, 15e5, 3e6,  float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0_5e5', '15e5', '3e6', '3e6_inf']
        feature = 'optional_sizeof_stack_reserve'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 1e3, 1e4, 3e4, 4e4, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['1e3', '1e4', '3e4', '4e4', '4e4_inf']
        feature = 'coff_characteristics'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 0.5, 2.5, 3.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0', '1_2', '3', '4_inf']
        feature = 'optional_minor_image_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 10000, 20000, 40000, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['1w', '1_2w', '3_4w', '4_inf']
        feature = 'optional_dll_characteristics'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 50000, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['5w', '5w_inf']
        feature = 'optional_sizeof_initialized_data'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 2.5, 6.5, 9.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0_2', '2_6', '6_9', '9_inf']
        feature = 'optional_major_image_version'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 1.5, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['0_1', '2_inf']
        feature = 'directories_DirectoryEntryExportSize'
        df = self.set_bins(feature, labels, bins, df)

        bins = [0, 750, 1500, float('inf')]  # 分割点, 包括左边界但不包括右边界
        labels = ['500', '1000', '1k_inf']
        feature = 'optional_sizeof_headers'
        df = self.set_bins(feature, labels, bins, df)

        return df

    def get_features_numpy(self, bytez, feature_idx=0):
        # 初始化 lief_binary
        # 0.14.0
        # lief_errors = (lief._lief.lief_errors.file_error, lief._lief.lief_errors.read_error, lief._lief.lief_errors.parsing_error,
        #                lief._lief.lief_errors.not_found, lief._lief.lief_errors.file_format_error, lief._lief.lief_errors.read_out_of_bound,
        #                RuntimeError)
        # old version 0.12.3
        lief_errors = (lief.bad_format, lief.bad_file, lief.pe_error,
                       lief.parser_error, lief.read_out_of_bound, RuntimeError)
        try:
            lief_binary = lief.PE.parse(list(bytez))
        except lief_errors as e:
            print("lief error: ", str(e))
            lief_binary = None
        # everything else (KeyboardInterrupt, SystemExit, ValueError):
        except Exception as e:
            print(f'else error: {str(e.__doc__)}')
            lief_binary = None

        if lief_binary is None:
            # TODO: 如果类型识别错误，会导致lief_binary为None
            return None

        fe = self.features[feature_idx]  # 初始化解析器
        df_feature = fe.raw_features(bytez, lief_binary)

        if df_feature is None:
            # TODO: 中间出现未知的错误
            return None

        # 增加分箱处理
        df_feature = self.set_bins_process(df_feature)

        # print(df_feature.columns)

        np_features = df_feature.values.astype(np.float32)[0]
        return np_features


def test():
    pe_abs_path = r'/home/<USER>/Desktop/pe_ai/test/test_install/360zip_setup.exe'
    ben_data = open(pe_abs_path, "rb").read()
    extractor = PEFeatureExtractor()
    np_features = extractor.get_features_numpy(ben_data)
    print(np_features)


if __name__ == '__main__':
    test()
