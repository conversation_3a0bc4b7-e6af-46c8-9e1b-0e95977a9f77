
from .features import PEFeatureExtractor


def predict_sample(model, file_data):
    """
    Predict a PE file with an model
    """

    extractor = PEFeatureExtractor()
    features = extractor.get_features_numpy(file_data)

    # print('i am in loacl anxin  file.')

    if features is None:
        # TODO:遇到非PE文件，预先设定为良性
        return 0.0

    res = model.predict_proba([features])
    # print(res)

    return res[0][1]
