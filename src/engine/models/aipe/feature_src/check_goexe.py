import os
import lief

import re


def count_patterned_section_names(pe):
    """
    提取PE文件中所有节的名字，并统计符合 /数字 模式的节名数目。
    :param file_path: PE文件的路径。
    :return: 符合模式的节名数目。
    """
    try:
        if not hasattr(pe, "sections"):
            return None

        section_names = [section.name for section in pe.sections]
        pattern = re.compile(r"/\d+")
        count = len(
            [name for name in section_names if pattern.search(name) or name == ".symtab"])
        if ".symtab" in section_names:
            return count-1
        return None
    except Exception as e:
        print(f"文件加载错误： {e}")
        return None


# Unit 1: 定义判断Go编译PE文件的函数


def is_go_compiled_pe(pe_path):
    """
    判断一个PE文件是否是从Go语言编译所得。

    Args:
      pe_path: PE文件路径。

    Returns:
      bool: True if the PE file is compiled from Go, False otherwise.
    """
    # Unit 2: 解析PE文件
    try:
        with open(pe_path, 'rb') as f:
            data = f.read()

        pe = lief.PE.parse(list(data))
    except lief.bad_format as e:
        print(f"Error parsing PE file: {e}")
        return False

    # Unit 3: 检查PE文件特征
    # 特征1: 含有.symtab节名，且/数字模式节名数量大于6
    sect_num = count_patterned_section_names(pe)
    print(sect_num)
    go_specific_sections = sect_num is not None and sect_num > 6

    # 综合判断
    return go_specific_sections


# Unit 4: 主函数
if __name__ == "__main__":
    # 替换为实际PE文件路径
    dir_path = '/mnt/hgfs/data/go_pes'
    pe_path = os.path.join(dir_path, 'nm.exe')

    if os.path.exists(pe_path):
        print("PE file found")
    if is_go_compiled_pe(pe_path):
        print("PE file is compiled from Go")
    else:
        print("PE file is not compiled from Go")
