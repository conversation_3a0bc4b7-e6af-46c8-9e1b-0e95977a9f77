
import feature_src.disposition as disposition
import feature_src.cofuncs as cofuncs
import feature_src.anxin as anxin
import feature_src.ember as ember
import sys
from typing import Dict
import os
import time
# import lightgbm as lgb

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if ROOT_DIR not in sys.path:
    sys.path.append(ROOT_DIR)


prediction_threshold = disposition.plus4_rf230_threshold
pe_size_threshold = disposition.pe_size_threshold

global_base_path = os.path.join(disposition.MODELS_DIR, 'base_et220_.pkl')
global_plus_path = os.path.join(disposition.MODELS_DIR, 'plus4_rf230_.pkl')

global_rf_plus = None
global_et_base = None


def load_model_once(model_path, is_plus=False):
    """加载模型的通用函数，如果模型未加载则加载，否则直接返回已加载的模型"""
    global global_lgbm_model, global_rf_plus, global_et_base
    # if model_path.endswith('.txt'):  # 假设.lgb模型文件的处理方式
    #     if global_lgbm_model is None:
    #         global_lgbm_model = lgb.Booster(model_file=model_path)
    #     return global_lgbm_model

    if model_path.endswith('.pkl'):  # 假设.pkl模型文件的处理方式
        if is_plus:
            if global_rf_plus is None:
                global_rf_plus = cofuncs.load_pkl_model(model_path)
            return global_rf_plus
        else:  # 假设另一个.pkl是et_base
            if global_et_base is None:
                global_et_base = cofuncs.load_pkl_model(model_path)
            return global_et_base
    else:
        raise ValueError("Unsupported model file type.")


# TODO: 加载base模型，这里只保存一个
s0 = time.time()
load_model_once(global_base_path)
# load_model_once(global_plus_path,is_plus=True)
e1 = time.time()
print(f'load model time is {e1 - s0:.3}')


def part_1_get_features(pe_path):
    pe_size = cofuncs.get_file_size_in_mb(pe_path)
    # print(f"文件 {pe_path} 体积 {pe_size} MB")
    if pe_size > pe_size_threshold:
        # print(f"文件 {pe_path} 体积{pe_size} MB过大")
        return 0.0

    try:
        # 如果不可检查或类型错误
        if not cofuncs.is_pe32_lief(pe_path):
            return 0.0

        pe_data = open(pe_path, "rb").read()

        features = ember.get_df_feature(pe_data)
        return features
    except Exception as e:
        return None


def part_2_predict(features, pe_path):
    if features is None:
        return 0.0  # 出现错误默认为中性

    if hasattr(global_et_base, 'predict_proba'):
        res = global_et_base.predict_proba(features)
        mal_prob = res[0][1]
    else:
        res = global_et_base.predict(features)
        mal_prob = res[0]

    # TODO: 后过滤1，检查PE是否用go编译
    if cofuncs.is_go_compiled(pe_path):
        mal_prob -= 0.067

    # 后过滤1.1，检查PE是否用python编译
    if cofuncs.is_python_compiled(pe_path):
        mal_prob -= 0.067

    # TODO: 后过滤2，检查PE签名是否有效
    signature = cofuncs.verify_signature(pe_path)
    if signature:
        if signature == 'expired':
            mal_prob -= 0.129
        elif signature == 'ok':
            mal_prob -= 0.2

    return mal_prob


def check_1pe_once(pe_path, is_plus=False):

    pe_size = cofuncs.get_file_size_in_mb(pe_path)
    # print(f"文件 {pe_path} 体积 {pe_size} MB")
    if pe_size > pe_size_threshold:
        # print(f"文件 {pe_path} 体积{pe_size} MB过大")
        return 0.0

    try:
        # 如果不可检查或类型错误
        if not cofuncs.is_pe32_lief(pe_path):
            return 0.0

        pe_data = open(pe_path, "rb").read()
        if is_plus:
            # 速度更快
            mal_prob = anxin.predict_sample(global_rf_plus, pe_data)
        else:
            #
            mal_prob = ember.predict_sample(global_et_base, pe_data)

            # TODO: 后过滤1，检查PE是否用go编译
            if cofuncs.is_go_compiled(pe_path):
                mal_prob -= 0.067

            # 后过滤1.1，检查PE是否用python编译
            if cofuncs.is_python_compiled(pe_path):
                # print(mal_prob)
                mal_prob -= 0.134

        # TODO: 后过滤2，检查PE签名是否有效
        signature = cofuncs.verify_signature(pe_path)
        if signature:
            if signature == 'expired':
                mal_prob -= 0.129
            elif signature == 'ok':
                mal_prob -= 0.2

        return mal_prob
    except Exception as e:
        print(f'[error] {str(e)} in check_1pe() {pe_path}')
        return 0.0  # 出现错误默认为中性


def check_1pe(model, pe_path, is_plus=False):

    pe_size = cofuncs.get_file_size_in_mb(pe_path)
    # print(f"文件 {pe_path} 体积 {pe_size} MB")
    if pe_size > pe_size_threshold:
        # print(f"文件 {pe_path} 体积{pe_size} MB过大")
        return 0.0

    try:
        # 如果不可检查或类型错误
        if not cofuncs.is_pe32_lief(pe_path):
            return 0.0

        pe_data = open(pe_path, "rb").read()
        if is_plus:
            # 速度更快
            mal_prob = anxin.predict_sample(model, pe_data)
        else:
            #
            mal_prob = ember.predict_sample(model, pe_data)

            # TODO: 后过滤1，检查PE是否用go编译
            if cofuncs.is_go_compiled(pe_path):
                mal_prob -= 0.067

            # 后过滤1.1，检查PE是否用python编译
            if cofuncs.is_python_compiled(pe_path):
                mal_prob -= 0.067

        # TODO: 后过滤2，检查PE签名是否有效
        signature = cofuncs.verify_signature(pe_path)
        if signature:
            if signature == 'expired':
                mal_prob -= 0.129
            elif signature == 'ok':
                mal_prob -= 0.2

        return mal_prob
    except Exception as e:
        print(f'[error] {str(e)} in check_1pe() {pe_path}')
        return 0.0  # 出现错误默认为中性


'''
TODO:处理中间出现的错误信息：解析失败的概率在恶意文件中更高
'''


def check_pe_folder(lgbm_model, folder_path: str, is_plus=True) -> Dict[str, float]:
    malicious_scores = {}

    # 定义一个辅助函数来检查单个文件是否为PE文件并计算恶意分数
    def is_pe_and_check_score(file_path: str):
        if cofuncs.is_pebin(file_path):
            score = check_1pe(lgbm_model, file_path, is_plus=is_plus)
            malicious_scores[file_path] = score

    # 遍历目录及其子目录
    select = 0

    for root, dirs, files in os.walk(folder_path):
        for file_name in files:
            select += 1
            # 选取100分之一
            if select % 2 != 0:
                continue

            file_path = os.path.join(root, file_name)
            is_pe_and_check_score(file_path)

    return malicious_scores


def scan_pes(ml_model, target_folder, is_plus):

    scores = check_pe_folder(ml_model, target_folder, is_plus=is_plus)

    count_mals = 0
    for file_path, score in scores.items():
        if score > prediction_threshold:
            print(f'Score: {score:4.3f}, File: {os.path.basename(file_path)}')
            count_mals += 1
    print(
        f'Total Malicious Files: {count_mals/len(scores)}, benigns = {len(scores) - count_mals}')


def test():
    # 定义数据
    # model_path = os.path.join(disposition.MODELS_DIR, 'ember_model_2018.txt')
    # lgbm_model = lgb.Booster(model_file=model_path)

    ben_packed_1 = '/home/<USER>/Desktop/pe_ai/test/test_install/VSCodeUserSetup-x64-1.79.2.exe'
    pe_path = r'/home/<USER>/anqing/pe_ai/test/mal2'
    # pe_path = r'/home/<USER>/Desktop/pe_ai/test/benware_300_A/0cea5d831139873f50b39ed0f900c67d'

    # 测试单个文件
    s1 = time.time()
    # res = check_1pe(lgbm_model, pe_path)
    # res = check_1pe(rf_plus, pe_path, is_plus=True)
    # res2 = check_1pe(lgbm_model, pe_path)
    res3 = check_1pe_once(pe_path)
    # print(f'plus mal score = {res:5.3f}')
    # print(f'ember mal score = {res2:5.3f}')
    print(f'et mal score = {res3:5.3f}')

    e2 = time.time()
    print(f'predict time is {e2 - s1:.3}')

    # 测试文件夹
    # tmp_dir = r'/home/<USER>/Desktop/pe_ai/test/test_install'
    # tmp_dir = r'/home/<USER>/Desktop/pe_ai/test/benware_300_A'
    # tmp_dir = r'/mnt/hgfs/0mal_dataset_virus/20230328/samples'
    # tmp_dir = r'/mnt/hgfs/win11_bens'

    # tmp_dir = r'/home/<USER>/Desktop/pe_ai/test/benware_300_A'
    # scan_pes(global_et_base, tmp_dir, is_plus=False)
    # scan_pes(rf_plus, tmp_dir, is_plus=True)


if __name__ == '__main__':
    start = time.time()
    test()
    end = time.time()
    print(f'run time is {end - start:.3}')
