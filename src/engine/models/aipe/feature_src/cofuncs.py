'''
准备一部分可以复用的函数工具


'''

import os
import re
import joblib
import pefile
import yara
import lief

from magika import Magika

import feature_src.disposition as disposition


def get_file_size_in_mb(file_path):
    # 获取文件大小（以字节为单位）
    size_in_bytes = os.path.getsize(file_path)

    # 转换为兆字节(MB)
    size_in_mb = size_in_bytes / (1024 * 1024)

    return round(size_in_mb, 2)


def get_file_type(file_path):
    '''
    识别文件的类型，但对于PE不区分16,32,64位，统一的为pebin
    '''

    m = Magika()

    with open(file_path, "rb") as file:
        file_bytes = file.read()
    res = m.identify_bytes(file_bytes)

    return (res.output.ct_label)


def is_pebin(file_path):

    if not os.path.isfile(file_path):
        print("File does not exist.")
        return False

    try:
        type_res = get_file_type(file_path)
        if type_res == 'pebin':
            return True
        else:
            return False
    except Exception as e:
        print(f'{str(e.__doc__)} in is_pebin.')

    return False


def is_pe32_lief(pe_path):
    try:
        bytez = open(pe_path, 'rb').read()
        is_pe32 = lief.PE.is_pe(list(bytez))
        # type_pe = lief.PE.get_type(list(bytez))
        # print(f'is pe {is_pe32}')

        return is_pe32
    except Exception as e:
        print(f'{str(e.__doc__)} in is_pe32_lief.')

    return False


def is_valid_pe(file_path):
    """
    判断给定路径下的文件是否为有效的PE格式文件。

    参数：
    file_path (str): 待检测文件的路径

    返回值：
    bool：如果文件是有效的PE格式则返回True，否则返回False。
    """

    # 检查文件是否存在
    if not os.path.isfile(file_path):
        print(f"文件 {file_path} 不存在或不是普通文件")
        return False

    # 尝试打开并解析PE文件
    try:
        pe = pefile.PE(file_path)
    except pefile.PEFormatError as e:
        # 当PE文件格式错误时抛出异常
        print(f"文件 {file_path} 格式错误：{str(e)}")
        return False
    except Exception as e:
        # 其他未知错误
        print(f"在处理文件 {file_path} 时发生未知错误：{str(e)}")
        return False

    # 验证DOS MZ头部
    if pe.DOS_HEADER.e_magic != pefile.IMAGE_DOS_SIGNATURE:
        print(f"文件 {file_path} 缺少有效的MZ头部")
        return False

    # 检查PE签名
    if not hasattr(pe, 'NT_HEADERS') or pe.NT_HEADERS.Signature != pefile.IMAGE_NT_SIGNATURE:
        print(f"文件 {file_path} 缺少有效的PE签名")
        return False

    # 检查文件头（File Header），确保机器类型有效
    if pe.FILE_HEADER.Machine == 0:
        print(f"文件 {file_path} 的机器类型未知")
        return False

    # 检查可选头（Optional Header），确保其Magic值有效
    valid_magic_values = [0x10b, 0x20b, 0x107]  # 包括PE32, PE32+ 和 ROM映像
    if pe.OPTIONAL_HEADER.Magic not in valid_magic_values:
        print(f"文件 {file_path} 缺少有效的可选头Magic值")
        return False

    # 验证PE文件至少包含一个节（Section）
    if len(pe.sections) == 0:
        print(f"文件 {file_path} 不包含任何节")
        return False

    # 这里可以添加更多的完整性及安全性检查，如验证数字签名、检查节属性等，
    # 根据需求增强代码以支持更多复杂的PE文件校验规则

    # 若上述所有基本验证均通过，则该文件被认为是有效的PE文件
    return True


def count_patterned_section_names(pe):
    """
    提取PE文件中所有节的名字，并统计符合 /数字 模式的节名数目。
    :param file_path: PE文件的路径。
    :return: 符合模式的节名数目。
    """
    try:
        if not hasattr(pe, "sections"):
            return None

        section_names = [section.name for section in pe.sections]
        pattern = re.compile(r"/\d+")
        count = len(
            [name for name in section_names if pattern.search(name) or name == ".symtab"])
        if ".symtab" in section_names:
            return count-1
        return None
    except Exception as e:
        print(f"文件加载错误： {e}")
        return None


def is_go_compiled(pe_path):
    """
    判断一个PE文件是否是从Go语言编译所得。

    Args:
      pe_path: PE文件路径。

    Returns:
      bool: True if the PE file is compiled from Go, False otherwise.
    """
    # Unit 2: 解析PE文件
    try:
        with open(pe_path, 'rb') as f:
            data = f.read()

        pe = lief.PE.parse(list(data))
    except Exception as e:
        print(f"Error parsing PE file: {e}")
        return False

    # Unit 3: 检查PE文件特征
    # 特征1: 含有.symtab节名，且/数字模式节名数量大于6
    sect_num = count_patterned_section_names(pe)
    # print(sect_num)
    go_specific_sections = sect_num is not None and sect_num > 6

    # 特征2：

    # 综合判断
    return go_specific_sections


def verify_signature(file_path):
    """
    检查PE文件的签名和证书的有效性。
    参数:
    - file_path: 字符串，PE文件的完整路径。

    返回:
    - None
    """
    try:
        binary = lief.parse(file_path)

        if not hasattr(binary, "signatures"):
            return None

        if binary.has_signatures:
            for signature in binary.signatures:
                verification_result = signature.check(
                    lief.PE.Signature.VERIFICATION_CHECKS.DEFAULT)
                # print(verification_result)
                if verification_result == lief.PE.Signature.VERIFICATION_FLAGS.OK:
                    return "ok"
                elif verification_result == lief.PE.Signature.VERIFICATION_FLAGS.CERT_EXPIRED:
                    return "expired"
        return None
    except Exception as e:
        print(f"签名验证过程中发生错误: {e}")
        return None


def extract_visible_strings(pe_path):
    with open(pe_path, 'rb') as f:
        content = f.read()
        strings = re.findall(b'[\x20-\x7E]{4,}', content)
        visible_strings = [s.decode('ascii') for s in strings]
    return visible_strings


def is_python_compiled(pe_path):
    visible_strings = extract_visible_strings(pe_path)
    res_list = []
    cnt = 0
    for it in visible_strings:
        if len(it) > 9:
            res_list.append(it)
            if "Py_" in it:
                cnt += 1
                if cnt > 10:
                    return True

            if "python37.dll" in it or "zPYZ-00.pyz" in it:
                return True

    return False


def get_first_pack_name(s):
    if len(s) < 1:
        return ""

    pos = s.find('[')
    if pos != -1:
        s = s[:pos-1]

    first_index = s.index('_')
    if first_index == -1:
        return s

    second_index = s.find('_', first_index+1)
    if second_index == -1:
        return s

    return s[:second_index]


def get_packed_first_name(file_path):

    # 加载规则文件
    rules_path = disposition.YARA_PATH
    rules = yara.compile(filepath=rules_path)

    # 扫描单个文件
    matches = rules.match(filepath=file_path)

    # 输出匹配的结果
    if len(matches) < 1:
        return None

    # print(dir(matches[0])) # 查看所有的属性
    # TODO: 这里为方便处理选择第一个被识别到的规则名，实际这里可以从中选刻意的加壳名
    first_name = matches[0].rule
    # print(first_name)

    return get_first_pack_name(first_name)


# TODO: 识别某一类幻觉对象的函数，如安装器


def load_pkl_model(model_path):
    # 加载模型
    with open(model_path, 'rb') as f:
        model = joblib.load(f)

    return model


def test():
    # data def
    pe_path = '/home/<USER>/Desktop/pe_ai/test/packed.exe'

    # run
    # out = is_valid_pe(pe_path)
    # out = get_file_type(pe_path)
    # out = get_packed_first_name(pe_path)
    out = is_pe32_lief(pe_path)

    print(out)


if __name__ == '__main__':
    test()
