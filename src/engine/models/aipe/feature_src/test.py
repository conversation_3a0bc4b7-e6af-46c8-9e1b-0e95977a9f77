

import glob
import json
import os
import config
import cofuncs
import lightgbm as lgb
from predict import check_1pe


def yara():
    import yara

    # 加载规则文件
    rules_path = '/home/<USER>/Desktop/pe_ai/tools/packer.yara'
    rules = yara.compile(filepath=rules_path)

    # 扫描单个文件
    file_path = '/home/<USER>/Desktop/pe_ai/test/0f0c9cbe0b56920edfe2bc5044fd59ce(c2be2ebd)'
    matches = rules.match(filepath=file_path)

    # 输出匹配的结果
    for match in matches:
        print(f"Rule {match.rule}")


def packed_first_name(file_path):
    import yara

    # 加载规则文件
    rules_path = '/home/<USER>/Desktop/pe_ai/tools/packer.yara'
    rules = yara.compile(filepath=rules_path)

    # 扫描单个文件
    matches = rules.match(filepath=file_path)

    # 输出匹配的结果
    if len(matches) < 1:
        return None
    first_name = matches[0].rele


def test_dir():
    # 定义数据
    model_path = os.path.join(config.MODELS_DIR, 'ember_model_2018.txt')
    model_plus = os.path.join(config.MODELS_DIR, 'rf_500_plus_1.pkl')

    # 初始化模型
    lgbm_model = lgb.Booster(model_file=model_path)
    rf_plus = cofuncs.load_pkl_model(model_plus)

    ben_packed_1 = '/home/<USER>/Desktop/pe_ai/test/test_install/VSCodeUserSetup-x64-1.79.2.exe'
    mal_1 = r'/home/<USER>/Desktop/pe_ai/test/test_install/sogou_pinyin_guanwang_14.2a.exe'

    # 测试单个文件
    res = check_1pe(rf_plus, mal_1, is_plus=True)
    res2 = check_1pe(lgbm_model, mal_1)
    # res =  check_1pe(rf_model,ben_pe)
    print(f'plus mal score = {res:5.3f}')
    print(f'ember mal score = {res2:5.3f}')


def record_false_negatives(model, dir_path, threshold, output_file="false_negatives.json"):
    false_negatives = []

    # 获取指定目录下的所有文件（假设均为PE文件）
    all_files = os.listdir(dir_path)

    for file_name in all_files:
        # 假设文件名就是SHA256哈希值
        pe_file = os.path.join(dir_path, file_name)
        mal_prob = check_1pe(model, pe_file, is_plus=True)

        if mal_prob < threshold:
            false_negatives.append({"path": pe_file, "mal_prob": mal_prob})

    # 将结果写入JSON文件
    outfile = os.path.join(config.OUT_DIR, output_file)
    with open(output_file, "w") as outfile:
        json.dump(false_negatives, outfile, indent=4)


def record_false_positives(model, dir_path, threshold, output_file="false_positives.json"):
    false_positives = []

    # 获取指定目录下的所有文件（假设均为PE文件）
    all_files = os.listdir(dir_path)

    for file_name in all_files:
        # 假设文件名就是SHA256哈希值
        pe_file = os.path.join(dir_path, file_name)
        mal_prob = check_1pe(model, pe_file, is_plus=True)

        if mal_prob > threshold:
            false_positives.append({"path": pe_file, "mal_prob": mal_prob})

    # 将结果写入JSON文件
    outfile = os.path.join(config.OUT_DIR, output_file)
    with open(output_file, "w") as outfile:
        json.dump(false_positives, outfile, indent=4)


def try_die():
    import subprocess

    # 定义可执行文件路径和参数
    executable_path = r"/home/<USER>/Desktop/pe_ai/tools/die_centos/diec.exe"
    input_file_path = r"/home/<USER>/Desktop/pe_ai/test/mal"

    # 构建命令行参数列表
    arguments = ["-urd", input_file_path]
    # e entropy i info u heuristic b verbose d deepscan -r recursivescan

    print(arguments)

    # 使用subprocess.run()执行命令
    try:
        result = subprocess.run([executable_path, *arguments], stdout=subprocess.PIPE,
                                stderr=subprocess.PIPE, check=True, encoding='utf-8')
        # print(result.stdout)
        # print(result.stderr)
        out = result.stdout
        if "Compiler: Go" in out:
            print("Compiler: Go")
        else:
            print(out)

    except subprocess.CalledProcessError as e:
        print(f"Command execution failed with error: {e}")


def start():
    print("i am here!!")

    # 定义数据
    # model_path = os.path.join(config.MODELS_DIR, 'ember_model_2018.txt')
    # model_plus = os.path.join(config.MODELS_DIR, 'rf_500_plus_1.pkl')
    # mals_path = r'/home/<USER>/Desktop/pe_ai/test/benware_300_A'

    # 初始化模型
    # lgbm_model = lgb.Booster(model_file=model_path)
    # rf_plus = cofuncs.load_pkl_model(model_plus)
    # record_false_positives(rf_plus, mals_path, threshold=0.5)

    try_die()


if __name__ == '__main__':
    start()
    # yara()
    # test_dir()
