import os
import shutil
import time
import vt


def detect_mal_id(client, id):

    try:
        all_res = client.get_object(f"/files/{id}")
        # 检查上次提交的时间
        date = int(all_res.last_analysis_date.year)  # 2014-03-08 08:47:14

        if date < 2020:
            print("The result is not up to date.")
            # TODO: 不在最新结果范围内，有重新分析的api么？
            # with open(file_path, "rb") as f:
            #     all_res = client.scan_file(f, wait_for_completion=True)

        # 检查文件的类型
        type_tag = all_res.type_tag
        # TODO: 或需要更多的文件类型
        if type_tag not in ['peexe', 'pedll']:
            print("The file type is not supported.")

        # 1 优先考虑提取某些高可信的杀软结果
        last_analysis_result = all_res.last_analysis_results
        best_avs = ["ESET-NOD32", "ClamAV", "Kaspersky", "BitDefender",
                    "McAfee", "Avast", "AVG", "DrWeb", "Symantec",
                    "CrowdStrike", "Microsoft", "Sophos", "Fortinet", "Paloalto"
                    ]
        best_cnt = 0
        for av in best_avs:
            if av in last_analysis_result:
                # 恶意标签不是None说明投票了
                if last_analysis_result[av]['result']:
                    best_cnt += 1

        alpha = best_cnt/float(len(best_avs))
        if alpha > 0.2 or best_cnt > 3:
            print('Reliable antivirus software considers the software to be malicious.')
            return True

        # 2 统计整体的恶意情况
        out = all_res.last_analysis_stats

        cnt_all = out['malicious']+out['undetected'] + \
            out['suspicious']+out['harmless']
        cnt_mal = (out['malicious']+out['suspicious'])
        mal_alpha = cnt_mal/float(cnt_all)

        # 检查恶意标签的比例 TODO: 这里的阈值需要进一步调整
        if mal_alpha > 0.4:
            print("More than half of antiviruses consider the software to be malicious.")
            return True
        if cnt_mal < 10 and best_cnt < 1:
            print(
                'Less than 10 antiviruses were voted and did not contain authoritative results')
            return False

        # TODO: 可以进一步的细化中间 10~28的结果内容
        if best_cnt > 0 and cnt_mal > 14:
            print(
                "The number of antiviruses is between 15~29 and contains authoritative opinions")
            return True

        if cnt_mal > 23 and best_cnt < 1:
            print(
                "The number of antiviruses is between 10~29 and does not contain authoritative opinions")
            # TODO: 或者是可疑的情况

        return False

    except Exception as e:
        if "NotFoundError" in str(e):
            print("The file does not exist.")
            return None  # TODO: 暂时设置不存在的文件


'''
Kaspersky（卡巴斯基）
Bitdefender
Norton（诺顿）
McAfee
Microsoft Defender
Avast
AVG
ESET NOD32
Symantec（赛门铁克）
Trend Micro
Sophos
Fortinet
Palo Alto Networks (WildFire)
'''


def get_VT_Client(api_key):
    return vt.Client(api_key)


def get_all_filenames_md5(folder_path):
    filenames = []

    for root, dirs, files in os.walk(folder_path):
        for file in files:
            md5 = file[0:-10]
            filenames.append(md5)

    return filenames

def test():
    my_api_key = '4947469575ef8fcae5dc61f7598a68bafbf67d68e3ad1e7fecd24f5388f47711'
    client = vt.Client(my_api_key)

    # 检测单个
    # fb7ad0e2dfa019bd51e250d56effc9f5 不存在
    # md5_or_sha256 = "fb7ad0e2dfa019bd51e250d56effc9f5"
    # result = detect_mal_id(client, md5_or_sha256)

    # if result:
    #     print("The file is malicious.")
    # else:
    #     print("The file is not malicious.")

    # 检测多个
    folder_path = r'E:\anxin_pe_clean\89k_notsure_packed'

    real_benign = r'E:\anxin_pe_clean\89k_notsure_packed_ben'
    real_malicious = r'E:\anxin_pe_clean\89k_notsure_packed_mal'

    if not os.path.exists(real_benign):
        os.makedirs(real_benign)
    if not os.path.exists(real_malicious):
        os.makedirs(real_malicious)

    
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            md5 = file[0:-10]
            result = detect_mal_id(client, md5)
            if result is None :
                continue
            if result==True :
                shutil.move(os.path.join(root, file), real_malicious)
            else :
                shutil.move(os.path.join(root, file), real_benign)
            

    client.close()


if __name__ == "__main__":
    start = time.time()
    test()
    end = time.time()
    print(f"Total time: {end - start}")


'''
先查MD5和检测时间，没有符合再考虑上传文件
'''

# with open("/path/to/file", "rb") as f:
#     analysis = client.scan_file(f)


# while True:
#     analysis = client.get_object("/analyses/{}", analysis.id)
#     print(analysis.status)
#     if analysis.status == "completed":
#         break
#     time.sleep(30)


# with open("/path/to/file", "rb") as f:
#     analysis = client.scan_file(f, wait_for_completion=True)
