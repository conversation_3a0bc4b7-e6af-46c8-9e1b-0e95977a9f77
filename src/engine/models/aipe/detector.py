
from tqdm import tqdm
import os, sys

root_path = os.path.dirname(os.path.abspath(__file__))
sys.path.append(root_path)

# 暂时注释掉有问题的导入
# if root_path:
#     try:
#         from feature_src.predict import check_1pe_once
#     except ImportError as e:
#         print(f"Warning: Could not import check_1pe_once: {e}")
#         def check_1pe_once(pe_path):
#             print(f"Warning: check_1pe_once not available, returning dummy score for {pe_path}")
#             return 0.5  # 返回一个默认分数

# 临时的替代函数
def check_1pe_once(pe_path):
    print(f"Warning: check_1pe_once not available due to yara import issues, returning dummy score for {pe_path}")
    return 0.5  # 返回一个默认分数

def check_pe(pe_path):
    return check_1pe_once(pe_path)

def get_predict(pe_path):
    mal_score = check_1pe_once(pe_path)
    is_mal = False
    if mal_score > 0.73:
        is_mal = True
    return is_mal


def detect_pe_folder(folder_path):
    # 计数器初始化
    total_files = 0
    malicious_files = 0

    mal_paths = []

    # 获取文件夹中所有文件的数量，用于进度条的总数
    file_count = sum([len(files) for r, d, files in os.walk(folder_path)])

    # 遍历文件夹，使用tqdm包装files迭代器以创建进度条
    for root, dirs, files in os.walk(folder_path):
        for file in tqdm(files, desc=f'Processing {root}', total=file_count):
            if file:
                total_files += 1
                pe_path = os.path.join(root, file)

                # 调用get_predict函数进行检测
                if get_predict(pe_path):
                    malicious_files += 1
                    mal_paths.append(pe_path)
                    # print(f"恶意文件: {pe_path}")

    # 将恶意路径写入文件
    import datetime

    # 获取当前的日期和时间
    timestamp = datetime.datetime.now().timestamp() 

    with open(f'/home/<USER>/anqing/pe_test/mal_paths_{timestamp}.log', 'w') as f:
        for path in mal_paths:
            f.write(path + '\n')

    # 计算并返回恶意文件的比例
    if total_files > 0:
        malicious_ratio = malicious_files / total_files
    else:
        malicious_ratio = 0  # 防止除以零错误

    print(
        f"总PE文件数: {total_files}, 恶意PE数: {malicious_files}, 恶意比例: {malicious_ratio:.2%}")
    return malicious_ratio


def test():
    pe_path = "/usr/local/pe_ai/test/small_test_1/fccb13c00df25d074a78f1eeeb04a0e7_pebin"
    print(get_predict(pe_path))
    # folder_to_scan = "/home/<USER>/anqing/pe_test/sensitive_test_2k"  # 替换为你的PE文件夹路径
    # malicious_ratio = detect_pe_folder(folder_to_scan)
    # print(f'mal-ratio is {malicious_ratio}')


if __name__ == '__main__':
    test()


'''
白样本
总PE文件数: 1000, 恶意PE文件数: 1, 恶意文件比例: 0.10%
/home/<USER>/anqing/pe_test/white/3bfab9a02f112bc2065cd26f3ce56f31.vir （53/71 真黑）

goexe 无误报
pythonexe 已经调高阈值修复
恶意文件: /home/<USER>/anqing/pe_test/pythonexe/stat_360.exe （7/74）
恶意文件: /home/<USER>/anqing/pe_test/pythonexe/stat_ai.exe （7/74）
总PE文件数: 4, 恶意PE文件数: 2, 恶意文件比例: 50.00%

nodejsexe 无误报


可疑样本
恶意文件: /home/<USER>/anqing/pe_test/suspicious/245824502aefe21b01e42f61955aa7f4_pebin （3/71 UPX加壳误判+卸载器）
恶意文件: /home/<USER>/anqing/pe_test/suspicious/24609655b2ed01ce4c6a7c6f86b1ae94_pebin （53/71 真黑）
总PE文件数: 1144, 恶意PE文件数: 2, 恶意文件比例: 0.17%

suspicious2/
总PE文件数: 1553, 恶意PE文件数: 18, 恶意文件比例: 1.16%
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/ca1d9908f32ee5c0bdd9b4efec79108f_pebin 40/74
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/c716eb7cb270cd7d19bd30b99e65152e_pebin 56/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/c3d422c2065ec3d9063929a1d4955416_pebin 53/70
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/c3e32c38b8a8cf706a29eb9e1a9a97a6_pebin 36/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/bf1c8ff4541075312d7c2eef667510e4_pebin 48/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/bf27bbcf6f92c3935e0f8515e90a5da1_pebin 54/68
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/ba5da875c1b8c13a771623c2fd3388d2_pebin 39/70
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/b5bd55cffd97bc3b6ad65a0b10fdcd49_pebin 6/71 Zip.SFX (误报)
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/b495bec9996c0d0c50f27da552719e59_pebin 54/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/b301cd0e42803b0373438e9d4ca01421_pebin 50/68
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/b1e5a284b0064758d65c5fc0e201db35_pebin 56/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/adb84098afa86b11bbb6c9992cd271a5_pebin 44/69
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/a59c9f117399b15d17ddf82b338782f7_pebin 52/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/a378626a8a08573e8aadc480250358c8_pebin 53/71
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/a30f16bc22f9f13f913e86a5dfffd331_pebin 58/72
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/a02f2d1693683972260adc26d71af9fa_pebin 49/69
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/94394247c21a559c0e7324e79e02d242_pebin 54/70
恶意文件: /home/<USER>/anqing/pe_test/suspicious2/382132e601d7a4ae39a4e7d89457597f_pebin 53/73



恶意样本 
20240623/
总PE文件数: 999, 恶意PE文件数: 777, 恶意文件比例: 77.78%

old(2013)/
总PE文件数: 1000, 恶意PE文件数: 665, 恶意文件比例: 66.50%
mal-ratio is 0.665

异常
大附件 pass
加壳 总PE文件数: 5, 恶意PE文件数: 5, 恶意文件比例: 100.00%
超大文件 pass
头部损坏 pass 具体总PE文件数: 4, 恶意PE文件数: 0, 恶意文件比例: 0.00%
特殊工具 pass


第二次测试
总PE文件数: 8515, 恶意PE文件数: 70, 恶意文件比例: 0.82%
误报 三个

Zip32(6.21).SFX
./pe_test/sensitive_test_8k/3aff44f1f15b4321e1c4ac20c7bc044b_pebin 5/68 Default.SFX Google+clamAV
./anqing/pe_test/sensitive_test_8k/245824502aefe21b01e42f61955aa7f4_pebin 3/70 UninsHs.exe
./anqing/pe_test/sensitive_test_8k/b5bd55cffd97bc3b6ad65a0b10fdcd49_pebin 6/71 Zip32(6.21).SFX

'''
