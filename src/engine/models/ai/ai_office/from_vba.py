'''从vba代码中提取特征
分为几种情况：
1，没有vba代码
2，存在明确的恶意特征（如下载http 可执行文件），直接恶意判定
3, 存在明确的混淆特征，百分之百是混淆的特征，直接恶意判定
4, 没有明确的混淆特征，提取特征并返回 特征

'''
import os
import re
import sys

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools
    import ai_office.obfuscate_name as obf


def analyze_vba_code(file_path, train_mode=False):

    vba_code = tools.get_vb_code(file_path)
    if vba_code == None:
        return False, 'nothing'

    if vba_code == 'nothing':
        # print('vba nothing!')
        return False, 'nothing'

    # TODO: 过滤MATLAB的使用
    if "matlab" in vba_code or "MatlabStartup" in vba_code:
        return False, 'matlab'

    # print(vba_code)

    # # 提取属性名 过滤一类良性文件中含有大量的属性名
    # pattern = r'\.([A-Za-z_]+)'
    # attr_list = re.findall(pattern, vba_code)

    # # TODO: 恶意文件存在较多误伤，含有点.号的行数 超过一半
    # has_dot_lines = 0
    # num_lines = 0
    # for line in vba_code.split('\n'):
    #     line = line.strip()
    #     skip_words = ['Const', "'", 'Attribute', 'Dim', 'Set', 'For','On','Public',
    #                   'Next', 'If', 'Else', 'MsgBox', 'Sub', 'End', 'Private', 'Option']
    #     is_skip = False
    #     if 'Cells' in line or line.isupper() :
    #         is_skip = True
    #     for skip in skip_words:
    #         if line.startswith(skip) or is_skip:
    #             is_skip = True
    #             break
    #     if not is_skip and line:
    #         if '.' in line:
    #             has_dot_lines += 1
    #         # print(line)
    #         num_lines += 1
    # if num_lines and has_dot_lines / num_lines > 0.75:
    #     print(f'ratio of lines with dots: {has_dot_lines / num_lines:.2f}')
    #     return False, "benign_many_dots"
    # if num_lines and len(attr_list) / num_lines > 1.3:
    #     print(f'{len(attr_list)} / {num_lines}={len(attr_list) / num_lines:.2f}')
    #     return False, "benign_many_attributes"

    # print(f'{has_dot_lines} == {num_lines}')

    is_malicious = False
    # 1 不存在vba代码
    if vba_code is None:
        # 之后合并时统一处理
        return is_malicious, None  # 特征数据为空

    #  非训练模式下，检测恶意和短路退出
    if not train_mode:
        # 2 存在明显的恶意 或 混淆特征
        is_malicious, features_obf = obf.check_names(vba_code)
        if is_malicious:
            return is_malicious, features_obf  # 有恶意特征时，features_obf 是标签说明 字符类型

    # 3 没有明显的混淆特征，提取模糊特征
    features = {}
    # TODO: 暂时不加入name特征 自然语言费时间太大
    features = obf.get_varname_features(vba_code)
    features['file_name'] = file_path
    features['file_size'] = float(os.path.getsize(file_path) / 1000)
    else_ = extract_func_features(vba_code)
    if else_:
        features.update(else_)

    return is_malicious, features


def count_frequency(keys, dict_):
    total = 0
    for key, cnt in dict_.items():
        if key.lower() in keys:
            total += cnt
    return total


def extract_func_features(vb_code):
    # 可能的混淆特征也放到这里面
    key_cnt = tools.parse_vba_functions(vb_code)
    # print(key_cnt)
    # for key, cnt in key_cnt.items():
    #     print(f"{key}: {cnt}")

    if key_cnt is None:
        return None

    keys = ['autoopen', 'autoclose', 'auto_close', ]
    autoopen = count_frequency(keys, key_cnt)
    createobject = count_frequency(['createobject'], key_cnt)
    getobject = count_frequency(['getobject'], key_cnt)
    windows = count_frequency(['windows'], key_cnt)
    array = count_frequency(['array'], key_cnt)
    environ = count_frequency(['environ'], key_cnt)
    run = count_frequency(['run'], key_cnt)

    workbook_open = count_frequency(['workbook_open'], key_cnt)
    document_open = count_frequency(['document_open'], key_cnt)
    document_close = count_frequency(['document_close'], key_cnt)
    auto_open = count_frequency(['auto_open'], key_cnt)

    # 字符串变化
    Hex = count_frequency(['hex'], key_cnt)
    Chr = count_frequency(['chr'], key_cnt)
    Chrw = count_frequency(['chrw'], key_cnt)
    Chrb = count_frequency(['chrb'], key_cnt)
    Strreverse = count_frequency(['strreverse'], key_cnt)
    Xor = count_frequency(['xor'], key_cnt)
    Cdate = count_frequency(['cdate'], key_cnt)
    Cstr = count_frequency(['cstr'], key_cnt)

    click = 0
    for key, cnt in key_cnt.items():
        if 'click' in key.lower():
            click += cnt

    close = 0
    for key, cnt in key_cnt.items():
        if 'close' in key.lower():
            close += cnt

    Open = 0
    for key, cnt in key_cnt.items():
        if 'open' in key.lower():
            Open += cnt

    shell = 0
    for key, cnt in key_cnt.items():
        if 'shell' in key.lower():
            shell += cnt

    create = 0
    for key, cnt in key_cnt.items():
        if 'create' in key.lower():
            create += cnt

    files = 0
    for key, cnt in key_cnt.items():
        if 'file' in key.lower():
            files += cnt

    # 全局统计信息
    # OBFUSCATION_FUNCTIONS =
    # ['Mid', 'Left', 'Right', 'Trim', 'LTrim', 'RTrim', 'UCase', 'LCase', 'Len', 'Replace',
    # 'StrReverse', 'StrComp', 'Split',  'Format', 'CStr', 'Chr', 'ChrW',
    #  'ChrB', 'CDate', 'Ucase', 'Lcase', 'CByte', 'CInt', 'CLng', 'CDec', 'CDbl', 'CSng', 'CCur',
    # 'CBool', 'CVar', 'Str', 'Val', 'StrConv', 'Asc', 'AscB', 'AscW', 'Hex', 'Oct', 'InStr', 'InStrRev',
    # ]

    maths = ['sin', 'len', 'round', 'rnd', 'fix', 'log',
             'int', 'cos', 'sgn', 'atn', 'tan', 'sqr', 'val']
    math_ops = count_frequency(maths, key_cnt)
    func_num = 0
    for key in key_cnt.keys():
        if len(key) >= 8:
            func_num += 1

    types = ['csng', 'hex', 'cdate', 'cstr', 'oct', 'cbyte', 'cdbl', 'CDec', 'CCur', 'CVar', 'Str',
             'Asc', 'AscB', 'AscW', 'clng', 'cbool', 'cint', 'chr', 'ChrB', 'ChrW', 'Binary', 'vartype']
    type_ops = count_frequency(types, key_cnt)

    strs = ['ucase', 'lcase', 'ltrim', 'rtrim', 'midb', 'strreverse', 'mid', 'replace', 'StrComp',
            'StrConv', 'Format', 'right', 'left', 'trim', 'split', 'Space', 'instrrev', 'InStr',]
    str_ops = count_frequency(strs, key_cnt)

    cur_features = {
        'autoopen': autoopen,
        'createobject': createobject,
        'getobject': getobject,
        'windows': windows,
        'array': array,
        'environ': environ,

        'workbook_open': workbook_open,
        'document_open': document_open,
        'document_close': document_close,
        'auto_open': auto_open,
        'shell': shell,
        'create': create,  # 新增 10-12
        'files': files,
        'ops': key_cnt['ops'],
        'lines': key_cnt['lines'],
        'prints': key_cnt['prints'],

        'run': run,
        'click': click,
        'close': close,
        'open': Open,

        'hex': Hex,
        'chr': Chr,
        'chrw': Chrw,
        'chrb': Chrb,
        'strreverse': Strreverse,
        'xor': Xor,
        'cdate': Cdate,
        'cstr': Cstr,

        'math_ops': math_ops,
        'func_num': func_num,
        'type_ops': type_ops,
        'str_ops': str_ops,
    }

    return cur_features


# 测试函数的基本功能
def test():
    f = r'test\Mal_0002'
    ans = analyze_vba_code(f, True)
    print(ans)


if __name__ == '__main__':
    tools.calculate_run_time(test)
