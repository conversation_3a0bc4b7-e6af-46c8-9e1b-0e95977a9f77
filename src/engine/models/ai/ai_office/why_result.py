'''
import os
import sys
from lime import lime_tabular
import numpy as np
from sklearn.model_selection import train_test_split
import pandas as pd
from sklearn.impute import SimpleImputer

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools
    dir_path = os.path.dirname(os.path.abspath(__file__))
    RF_PATH = os.path.join(dir_path, 'models', 'RF_all_73.joblib')
    DATA_DIR = os.path.join(dir_path, 'data', 'train')


#  定义字段的数据类型
data_types = {
    # 部分1 ：vba代码特征
    # 文档信息
    'file_name': 'category',
    'file_size': 'float32',

    # 函数名统计
    'autoopen': 'float32',  # 区分度极强
    'createobject': 'float32',  # 较强
    'getobject': 'float32',  # 区分度极强
    'windows': 'float32',  # 正负样本的区分度有限
    'array': 'float32',  # 区分度极强
    'environ': 'float32',  # 区分度极强

    'run': 'float32',  # 区分度较强
    'click': 'float32',  # 在所有函数名中出现的频率
    'close': 'float32',  # 在所有函数名中出现的频率
    'open': 'float32',  # 在所有函数名中出现的频率

    # 保证冗余，先不合并
    'workbook_open': 'float32',  # 区分度一般
    'document_open': 'float32',  # 区分度较强
    'document_close': 'float32',  # 区分度一般
    'auto_open': 'float32',  # 区分度一般
    'shell': 'float32',  # 指在函数名出现的频率
    'create': 'float32',  # 新增 10-12
    'files': 'float32',
    'ops': 'float32',
    'lines': 'float32',
    'prints': 'float32',
    # # TODO: 10-13新增 vba 变量名相关
    # 'var_good_ratio': 'float32',  速度太慢
    'var_digit_ratio': 'float32',
    'var_case_det': 'float32',
    'var_Axx_ratio': 'float32',
    'var_bad_num': 'float32',
    'var_too_long': 'float32',
    'var_skip_vb_num': 'float32',
    'var_else': 'float32',

    # 字符串变换
    'hex': 'float32',  # 区分度极强
    'chr': 'float32',  # 区分度较强
    'chrw': 'float32',  # 区分度极强
    'chrb': 'float32',  # 区分度极强
    'strreverse': 'float32',  # 区分度极强
    'xor': 'float32',  # 极强
    'cdate': 'float32',  # 区分度较强
    'cstr': 'float32',  # 区分度较强

    # 全局统计信息
    'math_ops': 'float32',
    'func_num': 'float32',
    'type_ops': 'float32',
    'str_ops': 'float32',

    # 部分2：olevba 工具
    'AutoExec': 'float32',
    'Suspicious': 'float32',
    'Base64 String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64
    'Hex String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64

    'Environ': 'float32',
    'Create': 'float32',
    'GetObject': 'float32',
    'Binary': 'float32',
    'System': 'float32',
    'Kill': 'float32',
    'Active': 'float32',  # ActiveWorkbook.SaveA 注意到关键字可能和工具版本相关，会变化
    'WScript.Shell': 'float32',
    'Powershell': 'float32',  # 在suspicious里面 先变化为小写
    'Call': 'float32',  # 在suspicious里面 call
    'VBHide': 'float32',  # 在suspicious里面 可能执行可执行文件
    'Print': 'float32',  # 在suspicious里面 print
    'VBA Stomping': 'float32',  # 在suspicious里面
    'Shell': 'float32',  # 在suspicious里面 含有shell就算 ShellExecute
    'ExecuteExcel4Macro': 'float32',
    'XMLhttp': 'float32',  # 含有就算
    'ShowWindow': 'float32',  # 测得时候考虑小写'ShowWindow',
    'Windows': 'float32',
    'Lib': 'float32',
    'Write': 'float32',
    'Output': 'float32',
    'Callbyname': 'float32',
    'Open': 'float32',
    'Put': 'float32',
    'File': 'float32',  # 在suspicious里面 带有file的
    'XLM macro': 'float32',  #
    'Execute': 'float32',

    # 增加IOC恶意程度
    'IOC': 'float32',  # key里面找IOC 可能含有http .exe .pif .bat .cmd .vbs .jar
    'ExeOrbat': 'float32',  # .exe .pif .bat .cmd
    'DDElink': 'float32',  # dde

    # 'MayBenign': 'float32',
    # 'MayMalicious': 'float32',
}


def set_label(df, dep):
    df['Class'] = df['Class'].astype(str)
    df['Class'] = df['Class'].replace({'Benign': 0, 'Malicious': 1})
    df['Class'] = df['Class'].astype('int32')


# 拆分 剩余数据xs 和 targ列（一般是标签）
def xs_y(df_, targ):
    if not isinstance(targ, list):
        xs = df_[df_.columns.difference([targ])].copy()
    else:
        xs = df_[df_.columns.difference(targ)].copy()
    y = df_[targ].copy()
    return xs, y


def drop_file_name(df):
    # 为了保证训练之后还可以找回文件名
    name_df = df['file_name']
    df.drop(columns=['file_name'], inplace=True)
    return df, name_df

# 切分数据


def split_data_df(data_path, alpha=0.3):

    df = pd.read_parquet(data_path)  # 数据集
    # print(df.head())
    # 将字典中的值转换为相应的数据类型
    for key, value in data_types.items():
        df[key] = df[key].astype(value)

    # 文件名 列不用
    # if 'file_name' in df.columns:
    #     df.drop(columns=['file_name'], inplace=True)
    # drops = ['ddelink', 'saveas',
    #          'run', 'strreverse', 'kill', 'system', 'xor']
    # df.drop(columns=drops, inplace=True)
    # if 'file_name' in df.columns:
    #     df.drop(columns=['file_name'], inplace=True)
    # if 'Not' in df.columns:
    #     df.drop(columns=['Not'], inplace=True)

    # 尝试进行特征融合
    wait_merge_list = ['chrb', 'strreverse', 'hex', 'chrw', 'cdate']
    df['new_1'] = 0.0
    for key in wait_merge_list:
        df['new_1'] += df[key]
    df.drop(columns=wait_merge_list, inplace=True)

    # 尝试进行特征融合
    df['new_2'] = 0.0
    drop_list = []
    for key in df.columns:
        if 'var_' in key:
            df['new_2'] += df[key]
            drop_list.append(key)

    df.drop(columns=drop_list, inplace=True)

    # 标签列名
    dep = 'Class'
    set_label(df, dep)

    # 选择category类型的列
    cats = df.select_dtypes(include='category').columns

    # 所有列名中排除dep和cats剩下 数字类型
    conts = df.columns.difference([dep] + list(cats))

    # 训练 验证 测试 70 5 25
    trn_df, val_df = train_test_split(df, test_size=alpha, shuffle=True)
    if val_df is not None:
        val_df, test_df = train_test_split(
            val_df, test_size=0.83, shuffle=True)
        val_df[cats] = val_df[cats].apply(lambda x: x.cat.codes)
        test_df[cats] = test_df[cats].apply(lambda x: x.cat.codes)
        X_val, y_val = xs_y(val_df, dep)
        X_test, y_test = xs_y(test_df, dep)

    # 转换category为数值编码
    trn_df[cats] = trn_df[cats].apply(lambda x: x.cat.codes)

    # 抽取标签
    X_train, y_train = xs_y(trn_df, dep)

    return X_train, y_train, X_val, y_val, X_test, y_test


def get_explainer():
    # data
    data_path = os.path.join(DATA_DIR, 'all_2.parquet')
    X_train, y_train, X_val, y_val, X_test, y_test = split_data_df(
        data_path, 0.2)

    X_train, X_train_fname = drop_file_name(X_train)

    # 处理缺失的值
    imputer = SimpleImputer(strategy='median')
    X_train = pd.DataFrame(imputer.fit_transform(
        X_train), columns=X_train.columns)

    #
    explainer = lime_tabular.LimeTabularExplainer(X_train.values,
                                                  feature_names=X_train.columns,
                                                  class_names=['Ben', 'Mal'],
                                                  discretize_continuous=True)

    return explainer, X_train


def explain_doc(i):
    # 修正打包路径
    model = tools.load_model(RF_PATH)
    explainer, X_test = get_explainer()

    import warnings
    from sklearn.exceptions import DataConversionWarning

    flag = '恶意'
    with warnings.catch_warnings():
        warnings.filterwarnings(action='ignore', category=UserWarning)
        prediction = model.predict(X_test.iloc[i].values.reshape(1, -1))
        if prediction == 0:
            flag = '正常'
        print(X_test.iloc[i].values.shape)
        exp = explainer.explain_instance(
            X_test.iloc[i].values, model.predict_proba, num_features=5)

    list_of_explanations = exp.as_list()

    # Create a more detailed description based on feature names and weights
    detailed_descriptions = []
    for feature, weight in list_of_explanations:
        # Craft the description
        if weight > 0:
            description = f"预测为{flag}贡献{weight:.2f}是敏感 特征'{feature}'"
        else:
            description = f"预测为{flag}贡献{-weight:.2f}是常规 特征'{feature}'"
        detailed_descriptions.append(description)

    # Print the detailed descriptions
    for description in detailed_descriptions:
        print(description)


def explain_mal_doc(df_1, model, explainer):
    # 修正打包路径
    # model = tools.load_model(RF_PATH)
    # explainer,X_test = get_explainer()

    import warnings
    from sklearn.exceptions import DataConversionWarning
    with warnings.catch_warnings():
        warnings.filterwarnings(action='ignore', category=UserWarning)

        input_np = df_1.values.squeeze()
        exp = explainer.explain_instance(
            input_np, model.predict_proba, num_features=5)

    list_of_explanations = exp.as_list()

    # Create a more detailed description based on feature names and weights
    detailed_descriptions = []
    for feature, weight in list_of_explanations:
        # Craft the description
        if weight > 0:
            description = f"contribute {weight:.2f} suspicious feature: {feature} "
        else:
            description = f"contribute {-weight:.2f} regular feature: {feature} "
        detailed_descriptions.append(description)

    # Print the detailed descriptions
    # for description in detailed_descriptions:
    #     print(description)

    return detailed_descriptions


if __name__ == '__main__':
    explain_doc(12)
'''