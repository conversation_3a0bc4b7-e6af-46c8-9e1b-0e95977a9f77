'''检测vba代码是否存在恶意特征
1. 存在100%恶意特征，返回标签
2. 不存在100%恶意特征，返回模糊特征
'''

import ast
import nltk
from nltk.stem import WordNetLemmatizer
from nltk.corpus import wordnet, words
import os
from collections import Counter
import re
import sys
from typing import Dict, List


root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_office.office_tools as tools
    dir_path = os.path.dirname(os.path.abspath(__file__))
    NLTK_DATA = os.path.join(dir_path, 'nltk_data')
    nltk.data.path.append(NLTK_DATA)
    lemmatizer = WordNetLemmatizer()
    words_set = set(words.words())


def filter_caps(s):
    # 不包含小写字母
    if not any(c.islower() for c in s):
        return s.lower()

    start = 0
    while start < len(s) and (s[start].isupper() or s[start] == '_'):
        start += 1
    end = len(s) - 1
    while end >= 0 and (s[end].isupper() or s[end] == '_'):
        end -= 1
    if start == 0:  # 细节，如果开头没有大写字母或者下划线，不能-1
        return s[:end+1]
    if s[start-1] == '_':  # 细节，如果开头是下划线，不能-1
        return s[start:end+1]
    return s[start - 1:end + 1]


def get_first_and_last_word(s):
    if not s:
        return []

    s = filter_caps(s)
    if len(s) <= 3:
        return [s, '']

    # Convert first letter to lowercase
    s = s[0].lower() + s[1:]

    # Extract word until first uppercase character or underscore from the begining
    index_start = next((i for i, c in enumerate(
        s) if c.isupper() or c == '_'), len(s))
    first_word = s[:index_start]

    # Extract word after the last uppercase character or underscore from the end
    index_end = max(s.rfind('_'), max(i for i, c in enumerate(
        s) if c.isupper()) if any(x.isupper() for x in s) else -1)
    if index_end == -1:
        last_word = s
    elif s[index_end] == '_':
        last_word = s[index_end+1:]
    else:
        last_word = s[index_end:]

    return [first_word, last_word]


def is_good_word(word):
    word = re.sub(r'\d', '', word)  # 去除数字

    # 变量名中存在有意义的词
    words = get_first_and_last_word(word)

    words.extend([word[:5], word[-5:], word[:4],
                 word[-4:], word[-3:], word[:3]])

    words = list(set(words))
    # print(f'words = {words}')

    for word in words:
        word = word.lower()
        if word in ['show', 'strikethrough', 'app', 'config', 'attrib', 'atty', 'arg', 'tmp', 'proj']:
            return True
        skips = ['calc', 'vbproj', 'formula', 'val', 'share', 'obj', 'dict',
                 'addr', 'num', 'msg', 'rng', 'cnt', 'cel', 'sum', 'int', 'str', 'box']
        for skip in skips:
            if skip in word:
                return True
        if 'ing' in word:
            words.append(word.replace('ing', ''))
            words.append(word.replace('ing', '')+'e')

        if len(word) <= 2:
            if word in ['no', 'my']:
                return True
            continue

        lemmatized_word = lemmatizer.lemmatize(word)
        if lemmatized_word.lower() in words_set:
            return True

        singular_word = lemmatizer.lemmatize(word, pos='n')
        if bool(wordnet.synsets(singular_word)):
            return True

    return False


# TODO: 用于分析未使用的单词 暂时不可以用 解析代码语法错误太多


class VarAssignUsageChecker(ast.NodeVisitor):
    def __init__(self):
        self.assigns = set()
        self.usage = set()

    def visit_Assign(self, node):
        for target in node.targets:
            if isinstance(target, ast.Name):
                self.assigns.add(target.id)
        self.generic_visit(node)

    def visit_Name(self, node):
        if isinstance(node.ctx, ast.Load):
            self.usage.add(node.id)
        self.generic_visit(node)


def get_name_notused(vb_code):
    new_code = ""
    lines = vb_code.split("\n")
    skip_like_attribute = ['Attribute', 'Function',
                           'Sub', 'With', 'Case', 'End', 'Set', 'Call']
    for line in lines:
        jump = False
        for attr in skip_like_attribute:
            if attr in line:
                jump = True
                break
        if jump:
            continue
        new_code += line.strip() + "\n"
    try:
        checker = VarAssignUsageChecker()
        tree = ast.parse(new_code)
        checker.visit(tree)
    except SyntaxError as e:
        print(f'SyntaxError: {e}')
        return []
    only_assigned_not_used = checker.assigns - checker.usage
    return only_assigned_not_used


def check_Axx(s):
    # 首先检查字符串长度是否大于7
    if len(s) <= 7:
        return False

    # 然后检查第一个字符是不是大写
    if not s[0].isupper():
        return False

    # 接下来检查其余字符是不是小写
    for character in s[1:]:
        if not character.islower():
            return False

    # 所有检查都通过最后返回 True
    return True


def R1C1(str):
    # 这个正则表达式可以匹配 'R' 接一或多个数字, 然后接 'C'，最后再接一或多个数字
    pattern = re.compile(r'R\d+C\d+')
    match = pattern.fullmatch(str)
    if match:
        return True
    return False


def find_notused_vars(code):
    # Create an empty set for variable definitions and usages
    var_defs = set()
    var_usages = set()

    # Loop over code lines
    skip_like_attribute = ['Attribute', 'Function',
                           'Sub', 'With', 'Case', 'End', 'Set', 'Call']
    for line in code.splitlines():
        if any(attr in line for attr in skip_like_attribute):
            continue
        # Check if line can be split into two parts
        if line.count('=') == 1:
            # Split in two parts
            left, right = re.split(r'\s*=\s*', line)

            # Search for variable definition on the left
            match_def = re.search(r'\b(\w+)\b', left)
            if match_def:
                var_defs.add(match_def.group(1))

            # Search for variable usage on the right
            match_usage = re.findall(r'\b(\w+)\b', right)
            if match_usage:
                var_usages.update(set(match_usage))

    # Variables not in usages set are likely obfuscations
    likely_obfuscations = var_defs - var_usages
    return likely_obfuscations


def has_varname_obfuscate(script):

    # 提取变量名称
    pattern = re.compile(r'\b(?:Dim)\s+([a-zA-Z_]\w*)')
    matches = re.findall(pattern, script)

    # 用于识别Dim只是定义不使用的混淆
    num_of_Dim = len(matches)

    pattern_1 = re.compile(r'\b\w+\b(?=\s*=)')
    matches_1 = re.findall(pattern_1, script)
    _list = matches + matches_1

    if len(_list) == 0:
        # print("变量名列表为空")
        return False, {}

    # 清洗
    bad_vars = []
    good_words_num = 0
    skip_vb_num = 0
    for item in _list:
        if R1C1(item) or item.startswith('VB_') or (item.startswith('tb') and len(item) == 3) or len(item) < 4:
            skip_vb_num += 1
            continue

        if is_good_word(item):
            good_words_num += 1
            continue

        bad_vars.append(item)

    if len(bad_vars) == 0:
        # print("变量名列表为空")
        return False, {}

    good_ratio = good_words_num / (len(_list)-skip_vb_num)

    # 0 恶意 无意义变量名大于一半
    # TODO: 这里的阈值可以调整较大些
    # if good_ratio < 0.05:
    # print(f'path: {path}')
    # print(f'bad vars: {bad_vars}')
    # print(f"无意义变量名大于一半: {good_ratio}")
    # return True, "var_name_half_bad"

    up_letters = low_letters = digits = 0
    Axx_num = 0
    var_dict = {}
    for item in bad_vars:
        # 1 惡意 不含下划线的变量名长度不小于25
        if len(item) > 50 or (len(item) > 25 and '_' not in item):
            # print(f"不含下划线的变量名长度不小于25: {len(item)}")
            return True, "var_name_too_long"
        # 2 变量名包含连续三个数字
        pattern = re.compile(r"[a-zA-Z_]\d{3}[a-zA-Z_]")
        pattern2 = re.compile(r"[a-zA-Z_]\d{6}[a-zA-Z_]")
        if bool(pattern.match(item)) or bool(pattern2.match(item)):
            # print(f"变量名包含连续三个数字: {item}")
            return True, "var_name_contains_123"

        pattern = re.compile('^[a-zA-Z0-9_]*$')
        if not pattern.match(item):
            # print(f"变量名包含特殊字符: {item}")
            return True, "var_name_contains_special"

        if item in var_dict.keys():
            var_dict[item] += 1
        else:
            var_dict[item] = 1

        for char in item:
            if char.isupper():
                up_letters += 1
            elif char.islower():
                low_letters += 1
            elif char.isdigit():
                digits += 1

        if up_letters == low_letters and len(item) > 20 and digits == 0:
            return True, "var_20half_up_low"

        if len(item) > 20 and digits/(len(item)) > 0.5:
            return True, "var_20half_digit"

        if check_Axx(item):
            Axx_num += 1

    # 2.1 单个恶意变量名占比过高 用于混淆
    total = sum(var_dict.values())
    most_frequent_word = max(var_dict, key=var_dict.get)
    # 判断这个最大值是否超过总量的0.75
    if total > 40 and var_dict[most_frequent_word] / total >= 0.75:
        # print("单个词重复过度")
        return True, "var_single_freq"

    # 建立新的以频率为key，词的列表为值的字典
    freq_word_dict = {}
    for word, freq in var_dict.items():
        if freq in freq_word_dict:
            freq_word_dict[freq].append(word)
        else:
            freq_word_dict[freq] = [word]

    # 2.1 恶意 相同频率的词数大于一半
    for freq, words in freq_word_dict.items():
        if freq > 3 and len(words) / len(bad_vars) > 0.75:
            # print(f"相同频率的词数大于一半: {freq}")
            # print(f'{freq_word_dict}')
            return True, "var_same_frequency"
        if (freq in [1, 2]) and num_of_Dim > 0 and num_of_Dim < len(words) and num_of_Dim / len(words) >= 0.68:
            # 保证恶意单词数不少于 dim 个数
            # print(f"Dim只是定义不使用: {num_of_Dim}")
            # print(f'words: {len(words)}')
            # print(f'{freq_word_dict}')
            return True, "var_dim_onces"

    digit_ratio = 0
    if digits > 0:
        digit_ratio = digits / (digits+up_letters+low_letters)
        # 3 恶意 数字占比大于50%
        if digit_ratio > 0.5 and len(var_dict) > 30:
            # print(f'path: {path}')
            # print(f'bad vars: {bad_vars}')
            # print(f"数字占比大于50%: {digit_ratio}")
            return True, "var_name_half_digit"

    all_char_num = up_letters+low_letters
    case_det = 1.0
    if all_char_num > 0:
        case_det = abs(up_letters-low_letters) / all_char_num
        # 4 恶意 大小写比例几乎一致
        if case_det <= 0.05 and len(var_dict) > 30:
            # print(f"大小写比例几乎一致: {case_det}")
            return True, "var_name_close_Aa"

    # 5 恶意 Axxxx占比过高
    if len(bad_vars) > 9 and Axx_num/len(bad_vars) > 0.95:
        # print(f"Axxxx占比过高: {Axx_num/len(bad_vars)}")
        return True, "Axx_much_AA_much"

    # 6 恶意 随机单词比例高于600
    if len(var_dict) > 20 and len(bad_vars) > 100:
        print(f'{var_dict}')
        return True, "bad_var_too_many"

    # TODO: 获取赋值不使用的变量名
    # not_used_vars = get_name_notused(script)
    not_used_vars = find_notused_vars(script)
    not_used_var = 0
    for item in not_used_vars:
        if not is_good_word(item) and item in var_dict.keys():
            not_used_var += 1
    if not_used_var < len(var_dict.keys()) and not_used_var / len(var_dict.keys()) > 0.9:
        # print(f'无用变量名太多，{not_used_vars}')
        return True, "var_many_not_used"

    #  TODO: 暂时不加入混淆特征，满足条件的数据很少
    # features = {
    #     "var_good_ratio": good_ratio,
    #     "var_digit_ratio": digit_ratio,
    #     "var_case_det": case_det,
    #     "var_Axx_ratio": Axx_num/len(bad_vars),
    #     "var_bad_num": len(bad_vars),
    #     "var_good_num": good_words_num,
    #     "var_skip_vb_num": skip_vb_num,
    # }

    # print(f'path: {path}')
    # print(f'bad vars: {var_dict}')

    return False, "good_var_names"


def get_vb_funcs(vb_code: str):
    func_list = []
    lines: List[str] = vb_code.split("\n")

    # 部分混淆特征
    # 1 Debug.Print 所占行数大于一半 （不含Attribute行）
    debug_print = 0
    Attribute_num = 0  # Attribute
    # 'Attribute' in line or len(line) < 4 or 'Function' in line or 'Sub' in line or 'With' in line :
    skip_like_attribute = ['Attribute',
                           'Function', 'Sub', 'With', 'Case', 'End']

    # 2 winmgmts:Win32_Process 拆分混淆
    win32 = "winmgmts:Win32_Processstartup"

    # 3 CInt(Mid( 转换字符串

    cint_mid = 0

    # 3.1 恶意 while 循环极端多
    # 3.2 单行 拼接字符过多

    for line in lines:
        # 计算连接字符
        plus_num = 0
        if '+' in line and "++" not in line and line.count('.') < 1 and line.count('Chr') > 0:
            plus_num += line.count('+') + line.count('^') + line.count('&')
            if plus_num > 20:
                # print(f'plus_too_many: {line}')
                return None, "plus_too_many"
        if len(line) > 500 and line.count(' ') < 5:
            # print(f'line_too_long: {line}')
            return None, "line_too_long"

        # 计数 Debug.Print
        if 'Debug.Print' in line or 'MsgBox' in line or 'Month Format(':
            debug_print += 1

        # 计数 忽略的行数
        for item in skip_like_attribute:
            if item in line:
                Attribute_num += 1

        # 提取部分含有 32_的字符串
        if '32_' in line:
            s = repr(line)
            parts = re.findall(r'"(.*?)"', s)
            win_tmp = (''.join(parts)).lower()
            # print(f'win_tmp: {win_tmp}')
            # print(f"line: {line}")
            if '32_' in win_tmp:
                case1 = win_tmp in win32.lower()
                case2 = "winmgmts" in win_tmp or "win32_process" in win_tmp
                if case1 or case2:
                    return None, "win32_process"
        if 'Do While' in line:
            numbers = re.findall(r'\b\d+\b', line)
            if numbers:
                loops = max(map(int, numbers))
                # print(f'loops: {loops}')
                if loops >= 50000:  # 65536 550000
                    return None, "while_loops_too_many"

        # CInt(Mid( 转换字符串
        if 'CInt(Mid(' in line:
            cint_mid += 1

        # 提取函数名
        pattern: str = r'[^a-zA-Z_]\s*([a-zA-Z_]+)\s*\('  # Match function name
        matches: List[str] = re.findall(pattern, line)
        for func in matches:
            func_list.append(func)

    # print(f'{debug_print} {Attribute_num} {len(lines)}')

    # 恶意 Debug.Print 所占行数大于一半 （不含Attribute行）
    sum_line = (len(lines) - Attribute_num)
    if sum_line > 0 and debug_print < sum_line and debug_print / sum_line > 0.5:
        print(
            f'Debug.Print 所占行数大于一半: { debug_print / (len(lines) - Attribute_num) }')
        return None, "debug_print_too_many"

    # 恶意 CInt(Mid( 转换字符串
    if cint_mid > 2:
        return None, "cint_mid_cvar_createobject"

    return func_list, "good"  # 暂时未检出恶意


def has_vbafunc_obfuscate(vb_code: str):
    names_list, info = get_vb_funcs(vb_code)

    # 处理混淆
    if info != "good":
        return True, info

    func_list = []
    good_names = []
    str_funcs = []
    good_num = 0
    onelen_names_num = 0

    # 清洗
    list_str_func = ['join', 'mid', 'left', 'right', 'trim', 'ltrim', 'rtrim', 'ucase', 'lcase', 'len', 'replace', 'strreverse', 'strcomp', 'split', 'format', 'cstr', 'chr', 'chrw', 'chrb',
                     'cdate', 'ucase', 'lcase', 'cbyte', 'cint', 'clng', 'cdec', 'cdbl', 'csng', 'ccur', 'cbool', 'cvar', 'str', 'val', 'strconv', 'asc', 'ascb', 'ascw', 'hex', 'oct', 'instr', 'instrrev',]
    good_func_names = ['rgb', 'dir', 'eof', 'if',
                       'iif', 'to', 'ocx', 'or', 'chdir', 'xor', 'atn']
    one_char_names = ['a', 'b', 'c', 'x', 'f', 'v', 'p', 'm', 't', 'ln', 'to']
    for item in names_list:
        low_item = item.lower()
        if len(low_item) < 3 and low_item not in one_char_names:
            onelen_names_num += 1
            continue

        if low_item in list_str_func:
            str_funcs.append(low_item)
            good_num += 1
            continue

        if low_item in good_func_names or is_good_word(item):
            good_names.append(item)
            good_num += 1
            continue

        func_list.append(item)

    if len(func_list) == 0:
        # print("函数名为空")
        return False, {}

    # 0 恶意 包含模式 'Tan', 'Log', 'CreateObject',
    if good_names.count('Tan') > 5 and good_names.count('Log') > 5 and good_names.count('CreateObject') > 0:
        # print("包含模式 'Tan', 'Log', 'CreateObject'")
        return True, 'Tan_Log_CreateObject'

    # 1 恶意 CreateProcessA AutoOpen
    if good_names.count('CreateProcessA') > 0 and good_names.count('AutoOpen') > 0:
        # print("包含 CreateProcessA AutoOpen")
        return True, 'CreateProcessA_AutoOpen'

    # 1.1 恶意 ['AutoOpen', 'Shell', 'DownloadFile']
    if good_names.count('AutoOpen') > 0 and good_names.count('Shell') > 0 and good_names.count('DownloadFile') > 0:
        # print("包含 AutoOpen Shell DownloadFile")
        return True, 'AutoOpen_Shell_DownloadFile'

    # 1.2 恶意  Cdbl + CData 过多
    if str_funcs.count('Cdbl') + str_funcs.count('CData') >= 40:
        # print("Cdbl + CData 过多")
        return True, 'Cdbl_CData_too_many'

    for item in func_list:
        # 2 恶意 不含下划线的函数名长度不小于25
        if len(item) > 50 or (len(item) > 25 and '_' not in item):
            return True, 'func_name_too_long'

    # 合并到 extra_func_features
    # features = {
    #     "good_names": len(good_names),
    #     "bad_names": len(func_list),
    #     "str_funcs": len(str_funcs),
    #     "func_1_num": onelen_names_num,
    # }

    return False, "good_func_names"


def check_names(vab_code):
    is_malicious, mal_tag = has_varname_obfuscate(vab_code)

    if is_malicious:
        return is_malicious, mal_tag
    else:
        is_malicious, mal_tag = has_vbafunc_obfuscate(vab_code)
        if is_malicious:
            return is_malicious, mal_tag

    return False, "good_names"


def get_varname_features(script):

    # 提取变量名称
    pattern = re.compile(r'\b(?:Dim)\s+([a-zA-Z_]\w*)')
    matches = re.findall(pattern, script)

    pattern_1 = re.compile(r'\b\w+\b(?=\s*=)')
    matches_1 = re.findall(pattern_1, script)
    _list = matches + matches_1

    if len(_list) == 0:
        # print("变量名列表为空")
        return {}

    # 清洗
    bad_vars = []
    skip_vb_num = 0
    for item in _list:
        if R1C1(item) or item.startswith('VB_') or (item.startswith('tb') and len(item) == 3) or len(item) < 4:
            skip_vb_num += 1
            continue

        bad_vars.append(item)

    if len(bad_vars) == 0:
        # print("变量名列表为空")
        return {}

    up_letters = low_letters = digits = 0
    Axx_num = 0
    var_dict = {}

    # 特征计数器
    too_long_name = 0
    half_up_low = 0
    half_digits = 0

    for item in bad_vars:
        # 1 惡意 不含下划线的变量名长度不小于25
        if len(item) > 50 or (len(item) > 25 and '_' not in item):
            too_long_name += 1

        if item in var_dict.keys():
            var_dict[item] += 1
        else:
            var_dict[item] = 1

        for char in item:
            if char.isupper():
                up_letters += 1
            elif char.islower():
                low_letters += 1
            elif char.isdigit():
                digits += 1

        if up_letters == low_letters and len(item) > 20 and digits == 0:
            half_up_low += 1

        if len(item) > 20 and digits/(len(item)) > 0.5:
            half_digits += 1

        if check_Axx(item):
            Axx_num += 1

    digit_ratio = 0
    if digits > 0:
        digit_ratio = digits / (digits+up_letters+low_letters)

    all_char_num = up_letters+low_letters
    case_det = 1.0
    if all_char_num > 0:
        case_det = abs(up_letters-low_letters) / all_char_num

    sum_else = half_digits + half_up_low

    #  TODO: 暂时不加入混淆特征，满足条件的数据很少
    features = {
        "var_digit_ratio": digit_ratio,
        "var_case_det": case_det,
        "var_Axx_ratio": Axx_num/len(bad_vars),
        "var_bad_num": len(bad_vars),
        "var_too_long": too_long_name,
        "var_skip_vb_num": skip_vb_num,
        "var_else": sum_else
    }
    return features


def test():
    path = r'ai_office\test\tmp.vb'
    with open(path, 'r') as f:
        ss = f.read()
        # ans = has_varname_obfuscate(ss)
        # ans= check_names(ss)
        ans = get_varname_features(ss)
        print(ans)


if __name__ == "__main__":
    tools.calculate_run_time(test)
