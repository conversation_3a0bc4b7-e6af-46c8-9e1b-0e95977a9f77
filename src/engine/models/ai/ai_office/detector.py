import argparse
import os
import sys
from joblib import load
from collections import Counter


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if BASE_DIR not in sys.path:
    sys.path.insert(0, BASE_DIR)

#from utils.logger_config import setup_logger
import ai_office.get_features as getFeatures
import ai_office.office_tools as tools
import ai_office.why_result as explanation

dir_path = os.path.dirname(os.path.abspath(__file__))  # ai_office/
MODELS_PATH = os.path.join(dir_path, 'models')
RF_PATH = os.path.join(dir_path, 'models', 'RF_all_73.joblib')

# 日志文件路径写法（log文件夹要放在ai-master下）
#logfile = os.path.join(BASE_DIR, 'logs', 'ai_scan.log')
#logger = setup_logger('doc_detector', logfile)
_MODEL_CACHE = None
# 一些工具
#  定义字段的数据类型
data_types = {
    # 部分1 ：vba代码特征
    # 文档信息
    # 'file_name': 'category',
    'file_size': 'float32',

    # 函数名统计
    'autoopen': 'float32',  # 区分度极强
    'createobject': 'float32',  # 较强
    'getobject': 'float32',  # 区分度极强
    'windows': 'float32',  # 正负样本的区分度有限
    'array': 'float32',  # 区分度极强
    'environ': 'float32',  # 区分度极强

    'run': 'float32',  # 区分度较强
    'click': 'float32',  # 在所有函数名中出现的频率
    'close': 'float32',  # 在所有函数名中出现的频率
    'open': 'float32',  # 在所有函数名中出现的频率

    # 保证冗余，先不合并
    'workbook_open': 'float32',  # 区分度一般
    'document_open': 'float32',  # 区分度较强
    'document_close': 'float32',  # 区分度一般
    'auto_open': 'float32',  # 区分度一般
    'shell': 'float32',  # 指在函数名出现的频率
    'create': 'float32',  # 新增 10-12
    'files': 'float32',
    'ops': 'float32',
    'lines': 'float32',
    'prints': 'float32',
    # # TODO:新增 vba 变量名相关
    # 'var_good_ratio': 'float32',  速度太慢
    'var_digit_ratio': 'float32',
    'var_case_det': 'float32',
    'var_Axx_ratio': 'float32',
    'var_bad_num': 'float32',
    'var_too_long': 'float32',
    'var_skip_vb_num': 'float32',
    'var_else': 'float32',

    # 字符串变换
    'hex': 'float32',  # 区分度极强
    'chr': 'float32',  # 区分度较强
    'chrw': 'float32',  # 区分度极强
    'chrb': 'float32',  # 区分度极强
    'strreverse': 'float32',  # 区分度极强
    'xor': 'float32',  # 极强
    'cdate': 'float32',  # 区分度较强
    'cstr': 'float32',  # 区分度较强

    # 全局统计信息
    'math_ops': 'float32',
    'func_num': 'float32',
    'type_ops': 'float32',
    'str_ops': 'float32',

    # 部分2：olevba 工具
    'AutoExec': 'float32',
    'Suspicious': 'float32',
    'Base64 String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64
    'Hex String': 'float32',  # 先在key里面找，没有就在suspicious里面找Base64

    'Environ': 'float32',
    'Create': 'float32',
    'GetObject': 'float32',
    'Binary': 'float32',
    'System': 'float32',
    'Kill': 'float32',
    'Active': 'float32',  # ActiveWorkbook.SaveA 注意到关键字可能和工具版本相关，会变化
    'WScript.Shell': 'float32',
    'Powershell': 'float32',  # 在suspicious里面 先变化为小写
    'Call': 'float32',  # 在suspicious里面 call
    'VBHide': 'float32',  # 在suspicious里面 可能执行可执行文件
    'Print': 'float32',  # 在suspicious里面 print
    'VBA Stomping': 'float32',  # 在suspicious里面
    'Shell': 'float32',  # 在suspicious里面 含有shell就算 ShellExecute
    'ExecuteExcel4Macro': 'float32',
    'XMLhttp': 'float32',  # 含有就算
    'ShowWindow': 'float32',  # 测得时候考虑小写'ShowWindow',
    'Windows': 'float32',
    'Lib': 'float32',
    'Write': 'float32',
    'Output': 'float32',
    'Callbyname': 'float32',
    'Open': 'float32',
    'Put': 'float32',
    'File': 'float32',  # 在suspicious里面 带有file的
    'XLM macro': 'float32',  #
    'Execute': 'float32',

    # 增加IOC恶意程度
    'IOC': 'float32',  # key里面找IOC 可能含有http .exe .pif .bat .cmd .vbs .jar
    'ExeOrbat': 'float32',  # .exe .pif .bat .cmd
    'DDElink': 'float32',  # dde

    # 'MayBenign': 'float32',
    # 'MayMalicious': 'float32',
}


def adjust_features(df):
    # 将字典中的值转换为相应的数据类型
    # for key, value in data_types.items():
    #     df[key] = df[key].astype(value)

    # 文件名 列不用
    if 'file_name' in df.columns:
        df.drop(columns=['file_name'], inplace=True)

    # 尝试进行特征融合
    wait_merge_list = ['chrb', 'strreverse', 'hex', 'chrw', 'cdate']
    df['new_1'] = 0.0
    for key in wait_merge_list:
        df['new_1'] += df[key]
    df.drop(columns=wait_merge_list, inplace=True)

    # 尝试进行特征融合
    df['new_2'] = 0.0
    drop_list = []
    for key in df.columns:
        if 'var_' in key:
            df['new_2'] += df[key]
            drop_list.append(key)

    df.drop(columns=drop_list, inplace=True)

    if 'Class' in df.columns:
        df.drop(columns=['Class'], inplace=True)

    # 选择category类型的列
    cats = df.select_dtypes(include='category').columns

    # 转换category为数值编码
    df[cats] = df[cats].apply(lambda x: x.cat.codes)

    targ = 'Class'
    if not isinstance(targ, list):
        df = df[df.columns.difference([targ])].copy()
    else:
        df = df[df.columns.difference(targ)].copy()

    return df


class EnsembleClassifier:
    def __init__(self, model_paths):
        """
        初始化方法，传入模型路径列表，加载模型
        """
        self.models = [load(path) for path in model_paths]

    def parse_file(self, file, just_model=False, yara_all=False):
        # #TODO: 当features为str时，表明是明确的结果，无需解析文件，提取特征
        features = getFeatures.extract_doc_features(
            file, just_model, yara_all)
        if isinstance(features, str):
            return features  # 此时是恶意标签

        features = adjust_features(features)

        return features

    def predict(self, file, just_model=False, yara_all=False):
        """
        预测函数，使用加载的模型作出预测并采用投票机制返回结果
        """
        # logger.info(f'Only model: {just_model}, Yara all:{yara_all} in {file}')
        features = self.parse_file(file, just_model, yara_all)
        # print(features)

        if isinstance(features, str):
            if 'benign' in features:
                #logger.info(f'benign as {features} in {file} ')
                return 0, features
            else:
                #logger.info(f'malicious as {features} in {file} ')

                # TODO: 应该对恶意特征分级
                return 8, features

        # TODO: 加入前过滤策略，如果全部为0，或只有少量可疑信息
        sum_score = (features.sum(axis=1) - features['file_size'])
        # print(sum_score)
        # print(f'features sum = {sum.to_string(index=False)}')
        if sum_score.eq(0.0).any() or (sum_score.item()-features['Suspicious'].item()) < 4.0:
            return 0, features

        # TODO: 统计典型恶意特征组合模式，注意字段名称的大小写
        modes = [
                ['AutoExec'],
                ['AutoExec', 'click'], ['autoopen', 'click'],
                ['AutoExec', 'IOC'],
                ['AutoExec', 'Suspicious'],  # 自动执行+可疑标记
                ['AutoExec', 'Suspicious', 'click'],
                ['AutoExec', 'Suspicious', 'Hex String'],
                ['AutoExec', 'Suspicious', 'Base64 String'],
                ['AutoExec', 'Suspicious', 'chr', 'create', 'createobject'],
                ['AutoExec', 'Environ', 'Execute', 'ExecuteExcel4Macro', 'File'],
                ['AutoExec', 'Suspicious', 'Shell', 'createobject'],  # 执行外部命令+创建对象
                ['AutoExec', 'Suspicious', 'Shell', 'GetObject'],  # 执行外部命令+获取对象
                ['document_open', 'Suspicious', 'Hex String'],  # 可疑标记+编码+自动打开宏
                ['AutoExec', 'Create', 'Hex String',
                    'Environ'],  # 创建操作+编码数据+环境变量利用
                ['AutoExec', 'Base64 String', 'chr', 'math_ops'],  # 编码+字符操作+数学运算操作
                ['document_open', 'Hex String', 'math_ops',
                    'str_ops'],  # 同上，考虑不同编码和字符串操作
                ['Suspicious', 'autoopen', 'Shell'],  # 自动打开宏+Shell命令
                ['Suspicious', 'Shell', 'document_open'],
                ['AutoExec', 'Suspicious', 'ShowWindow',
                    'createobject'],  # 隐藏窗口+创建对象+可疑行为
                ['AutoExec', 'Suspicious', 'ShowWindow', 'getobject'],
                ['AutoExec', 'IOC', 'Suspicious'],  # 已知妥协指标+可疑操作
                ['AutoExec', 'Binary', 'Create', 'Shell'],  # 二进制数据+创建操作+Shell命令
                ['AutoExec', 'Binary', 'Call', 'Callbyname'],
                ['AutoExec', 'Shell', 'Environ', 'Suspicious'],
                ['Call', 'Create', 'Suspicious', 'document_open', 'getobject'],
                ['Hex String', 'auto_open', 'open'],
                ['Base64 String', 'autoopen', 'str_ops', 'type_ops'],
                ['Shell', 'AutoExec', 'math_ops'],
                ['Shell', 'AutoExec', 'str_ops'],
                ['Shell', 'AutoExec', 'type_ops'],
                ['AutoExec', 'type_ops', 'math_ops', 'chr'],
                ['AutoExec', 'Create', 'Shell', 'Suspicious'],
                ['AutoExec', 'Create', 'GetObject', 'Hex String', 'ShowWindow'],
                ['Suspicious', 'auto_open', 'open'],
                ['Base64 String', 'Hex String', 'Suspicious'],
                ['Base64 String', 'Call', 'Hex String', 'Shell', 'Suspicious'],
                ['Base64 String', 'Execute', 'Suspicious', 'XLM macro'],
                ['Suspicious', 'chr', 'new_1', 'new_2', 'str_ops', 'type_ops'],
                ['auto_open', 'chr', 'create', 'createobject',
                    'shell', 'str_ops', 'type_ops'],
                ['Call', 'ExeOrbat', 'Execute', 'IOC', 'Open', 'Shell'],
                ['Base64 String', 'Suspicious', 'chr'],
                ['VBHide', 'autoopen'],

                ['AutoExec', 'Base64 String', 'Call'],
                ['Base64 String', 'Call',  'Suspicious', 'create', 'createobject'],
                ['Shell', 'Suspicious', 'VBHide', 'document_open', 'environ'],
                ['Base64 String', 'Write', 'create',
                    'createobject', 'document_open'],
                ['Base64 String', 'Call', 'Execute', 'File',
                    'Shell', 'Suspicious', 'create', 'createobject'],
                ['Base64 String', 'Hex String', 'Suspicious', 'math_ops'],
                ['Suspicious', 'chr', 'close', 'create',
                    'document_close', 'document_open'],
                ['AutoExec', 'Suspicious', 'XLM macro'],
                ['Call', 'Shell', 'Suspicious', 'autoopen'],
                ['click', 'close', 'create', 'createobject', 'cstr', 'environ'],
                ['ShowWindow', 'Suspicious', 'autoopen'],
                ['chr', 'click', 'workbook_open'],
                ['Binary', 'Kill'], ['AutoExec', 'Kill'],
                ['array', 'autoopen'], ['array', 'document_open'],
                ['WScript.Shell', 'run'],
                ['run', 'click', 'environ'],
                ['Powershell', 'VBHide'],
                ['Powershell', 'ExeOrbat'],
                ['Powershell', 'IOC'],

            # TODO:慎重追加
            # ['Hex String', 'Suspicious'],
            # ['Base64 String', 'Suspicious'],
            ['Active', 'Hex String', 'Suspicious', 'Windows'],



        ]

        catch_mode_nums = 0
        hit_limit = 2  # TODO: 命中底线的阈值，越大检出率越低，此时2对应的检出是 95%

        can_pass = False
        for mode in modes:
            if all(features[key].item() > 0.0 for key in mode):
                catch_mode_nums += 1
                if catch_mode_nums > hit_limit:
                    can_pass = True
                    break

        if not can_pass:
            return 3, features

        # print(f"文档中匹配到的恶意模式总数: {catch_mode_nums}")

        # 收集恶意特征
        # from collections import Counter
        # string_freq = Counter()
        # first_row = features.iloc[0]
        # for i in range(len(first_row)):
        #     if first_row.iloc[i] > 0:
        #         string_freq[features.columns[i]] += 1

        # if catch_mode_nums < 3:
        #     # for it in string_freq.keys():
        #     #     print(it, end=" ")

        #     with open('mal_doc_less_3.log', "a+") as f:
        #         f.write(str(string_freq)+"\n")

        predictions = [model.predict(features).ravel()[0]
                       for model in self.models]

        # print(f'predict result: {predictions} in {file} ')
        #.info(f'predict result: {predictions} in {file} ')

        vote_mal_num = predictions.count(1)
        # TODO: 按权重计算 投票结果
        return vote_mal_num, features


def get_ensemble_model():
    # 缓存模型，避免重复加载
    global _MODEL_CACHE
    # 如果模型已加载，则直接返回缓存的模型
    if _MODEL_CACHE is not None:
        return _MODEL_CACHE
    # 检测一个doc文件是否为恶意doc
    model_dir = MODELS_PATH
    model_paths = [
        'RF_all_73.joblib',  # 1 识别恶意很强 表明良性 必然良性
        'RF_all_55.joblib',
        'RF_1030_65.joblib',
        # 'RF_1030_85.joblib', # 和1的结果一致
        # 'RF_1030_90.joblib', # 和1的结果一致

        'ADA_1030_75.joblib',
        # 'ADA_1030_85.joblib',
        'ADA_all_73.joblib',
        # 'ADA_all_46.joblib',
        # 'ADA_all_55.joblib',
        # 识别良性 具有一致性
        # 'MLP_all_82.joblib',
        'MLP_all_73.joblib',
        # 'MLP_all_37.joblib',
        'MLP_all_55.joblib',
        'MLP_1030_11.joblib',
        'MLP_1030_111.joblib',
        # 'MLP_1030_53.joblib',

    ]
    model_paths = [os.path.join(model_dir, path) for path in model_paths]
    model = EnsembleClassifier(model_paths)
    _MODEL_CACHE = model
    return model


def identify_doc_files(doc_dir, expected_tag):
    """
    主函数，遍历文件夹，调用预测函数
    """
    ensemble = get_ensemble_model()

    doc_names = sorted(os.listdir(doc_dir))
    predicts = []

    for doc_name in doc_names:
        file = os.path.join(doc_dir, doc_name)
        if os.path.isfile(file):  # and '.doc' in doc_name:
            print(f'{file} 开始检测...', end='')
            predict = ensemble.predict(file)
            predicts.append(predict)
            if predict == expected_tag:
                print(f'[OK]')
            else:
                print(f'[FAIL]')
            print('----------------------------------')
    return predicts


def check_docs(doc_dir, ensemble, just_model=False, yara_all=False):
    doc_names = sorted(os.listdir(doc_dir))

    for doc_name in doc_names:
        file = os.path.join(doc_dir, doc_name)
        if os.path.isfile(file):
            print(f'{file} 开始检测...', end='')
            predict, feature = ensemble.predict(file, just_model, yara_all)
            print(f'{predict}', end=" ")
            if predict < 7:
                print('PASS')
            else:
                print('Malicious')
            print('----------------------------------')


def check_doc(doc_path, model, just_model=False, yara_all=False):
    # Check the pdf files
    predict, feature = model.predict(doc_path, just_model, yara_all)
    '''
    print(f'{predict} in 10')
    if isinstance(feature, str):
        print(feature)
        return
    # 获取DataFrame的第一行数据
    first_row = feature.iloc[0]

    # 打印列名和对应的数据
    for i in range(len(first_row)):
        print(f'{feature.columns[i]:20}: {first_row.iloc[i]:7.1f}', end=" ")
        if i % 2 == 0:
            print()
    print()
    '''
    # explainer = explanation.get_explainer()
    # RF = tools.load_model(RF_PATH)
    # if predict == 0:
    #     print('PASS')
    # elif predict == 1:
    #     print('Malicious')
    #     if not isinstance(feature, str):
    #         explanation.explain_mal_doc(feature, RF, explainer)
    #     else:
    #         print(feature)
    return predict


def get_office_models():
    # 为服务器代码提供
    # ensemble = get_ensemble_model()
    # explainer, _ = explanation.get_explainer()
    modelRF = tools.load_model(RF_PATH)
    return modelRF


def get_predict(doc_path):
    ensemble = get_ensemble_model()
    res, predict = ensemble.predict(doc_path)
    return res, predict


def test():
    model = get_ensemble_model()
    # doc_dir = 'ai_office/test/Ben'
    # check_docs(doc_dir, model)

    doc_path = r"/Users/<USER>/workspace/olaf/src/engine/models/ai/ai_office/data/Mal_0009"
    #check_doc(doc_path, model)

    score = check_doc(doc_path, model)
    print(f"✅ 检测分数: {score}")
    # 集成模型预测
    # src_dir =r'F:\20240306\office'
    # mal_cnt = 0
    # all_cnt = 0
    # for root, dirs, files in os.walk(src_dir):
    #     for file in files:
    #         doc_path = os.path.join(root, file)
    #         all_cnt += 1
    #         res, predict = model.predict(doc_path)

    #         if res >= 7:
    #             mal_cnt += 1

    # print(f'{mal_cnt}/{all_cnt}')


def main():
    parser = argparse.ArgumentParser(
        description="Script to process office file and directory.")
    parser.add_argument("--dir", type=str,
                        help="office-directory to be processed")
    parser.add_argument("--file", type=str, help="office-file to be processed")

    # 添加新的参数
    parser.add_argument("--faster", action='store_true',
                        help="Process the file or directory faster.")
    parser.add_argument("--yara_all", action='store_true',
                        help="Use YARA to process all files and directories")

    args = parser.parse_args()
    # Load the model
    ensemble = get_ensemble_model()

    args.faster = True
    if args.dir:
        check_docs(args.dir, ensemble, args.faster, args.yara_all)

    if args.file:
        check_doc(args.file, ensemble, args.faster, args.yara_all)


if __name__ == "__main__":
    # main()
    test()
