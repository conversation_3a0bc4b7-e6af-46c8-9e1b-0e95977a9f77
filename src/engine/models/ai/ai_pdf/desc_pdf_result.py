'''
import os
import sys
# from lime import lime_tabular
import numpy as np
from sklearn.model_selection import train_test_split
import pandas as pd
from sklearn.impute import SimpleImputer

# 向上回转两级目录 来到anxinai
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_pdf.pdfBaseTools as tools
    ROOT_PATH = os.path.dirname(os.path.abspath(__file__))  # ai_pdf
    DATA_DIR = os.path.join(ROOT_PATH, 'data', 'train')
    RF_PATH = os.path.join(ROOT_PATH, 'models',
                           'pdf_models', 'RF_2w_2w_best.joblib')

#  定义字段的数据类型
data_types = {
    'FileName': 'category',
    'PdfSize': 'float32',
    'MetadataSize': 'float32',
    'Pages': 'float32',
    'XrefLength': 'float32',
    'TitleCharacters': 'float32',
    'isEncrypted': 'float32',
    'EmbeddedFiles': 'float32',
    'Images': 'category',
    'Text': 'category',
    'Header': 'category',
    'Obj': 'category',
    'Endobj': 'category',
    'Stream': 'float32',
    'Endstream': 'category',
    'Xref': 'category',
    'Trailer': 'float32',
    'StartXref': 'category',
    'PageNo': 'category',
    'Encrypt': 'float32',
    'ObjStm': 'float32',
    'JS': 'category',
    'Javascript': 'category',
    'AA': 'category',
    'OpenAction': 'category',
    'Acroform': 'category',
    'JBIG2Decode': 'category',
    'RichMedia': 'category',
    'Launch': 'category',
    'EmbeddedFile': 'category',
    'XFA': 'category',
    'Colors': 'float32',
    'Class': 'category',
    'MalTrail1': 'float32',
    'MalTrail2': 'float32',
    'MalTrail3': 'float32',
    'DiffObj': 'float32',
    'DiffStream': 'float32',
    'ElseDecode': 'float32',
    'Names': 'float32',
    'FontName': 'float32',
    'JsBadStream': 'float32',
    'SensitiveCode': 'float32',
    'KeywordObfuscation': 'float32',
    'JsObfuscation': 'float32',
    # 'ShellInject': 'float32',
    'MayBenign': 'float32',
    'MayMalicious': 'float32'

}


def set_label(df, dep):
    df['Class'] = df['Class'].astype(str)
    df['Class'] = df['Class'].replace({'Benign': 0, 'Malicious': 1})
    df['Class'] = df['Class'].astype('int32')


# 拆分 剩余数据xs 和 targ列（一般是标签）
def xs_y(df_, targ):
    if not isinstance(targ, list):
        xs = df_[df_.columns.difference([targ])].copy()
    else:
        xs = df_[df_.columns.difference(targ)].copy()
    y = df_[targ].copy()
    return xs, y


def drop_file_name(df):
    # 为了保证训练之后还可以找回文件名
    name_df = df['FileName']
    df.drop(columns=['FileName'], inplace=True)
    return df, name_df

# 切分数据


def split_data_df(data_path, alpha=0.3):

    df = pd.read_parquet(data_path)  # 数据集
    # print(df.head())
    # 将字典中的值转换为相应的数据类型
    for key, value in data_types.items():
        df[key] = df[key].astype(value)

    # 实验 放弃部分特征列
    drops = ['JsBadStream']
    # df.drop(columns=drops, inplace=True)

    # if 'file_name' in df.columns:
    #     df.drop(columns=['file_name'], inplace=True)

    # 标签列名
    dep = 'Class'
    set_label(df, dep)

    # 选择category类型的列
    cats = df.select_dtypes(include='category').columns

    cats = cats.drop('FileName')

    # 所有列名中排除dep和cats剩下 数字类型
    conts = df.columns.difference([dep] + list(cats))

    # 训练 验证 测试 70 5 25
    trn_df, val_df = train_test_split(df, test_size=alpha, shuffle=True)
    if val_df is not None:
        val_df, test_df = train_test_split(
            val_df, test_size=0.90, shuffle=True)
        val_df[cats] = val_df[cats].apply(lambda x: x.cat.codes)
        test_df[cats] = test_df[cats].apply(lambda x: x.cat.codes)
        X_val, y_val = xs_y(val_df, dep)
        X_test, y_test = xs_y(test_df, dep)

    # 转换category为数值编码
    trn_df[cats] = trn_df[cats].apply(lambda x: x.cat.codes)

    # 抽取标签
    X_train, y_train = xs_y(trn_df, dep)

    return X_train, y_train, X_val, y_val, X_test, y_test


def get_explainer():
    # data
    data_path = os.path.join(DATA_DIR, 'real_2w_2w.parquet')
    X_train, y_train, X_val, y_val, X_test, y_test = split_data_df(
        data_path, 0.2)

    X_train, X_train_fname = drop_file_name(X_train)

    # 处理缺失的值
    imputer = SimpleImputer(strategy='median')
    X_train = pd.DataFrame(imputer.fit_transform(
        X_train), columns=X_train.columns)

    #
    explainer = lime_tabular.LimeTabularExplainer(X_train.values,
                                                  feature_names=X_train.columns,
                                                  class_names=['Ben', 'Mal'],
                                                  discretize_continuous=True)

    return explainer, X_train


def explain_pdf(i):
    # 修正打包路径
    model = tools.load_model(RF_PATH)
    explainer, X_test = get_explainer()

    import warnings
    from sklearn.exceptions import DataConversionWarning

    flag = '恶意'
    with warnings.catch_warnings():
        warnings.filterwarnings(action='ignore', category=UserWarning)
        prediction = model.predict(X_test.iloc[i].values.reshape(1, -1))
        if prediction == 0:
            flag = '正常'
        print(X_test.iloc[i].values.shape)
        exp = explainer.explain_instance(
            X_test.iloc[i].values, model.predict_proba, num_features=5)

    list_of_explanations = exp.as_list()

    # Create a more detailed description based on feature names and weights
    detailed_descriptions = []
    for feature, weight in list_of_explanations:
        # Craft the description
        if weight > 0:
            description = f"预测为{flag}贡献{weight:.2f}是敏感 特征'{feature}'"
        else:
            description = f"预测为{flag}贡献{-weight:.2f}是常规 特征'{feature}'"
        detailed_descriptions.append(description)

    # Print the detailed descriptions
    for description in detailed_descriptions:
        print(description)


def explain_mal_pdf(df_1, model, explainer, n_featrues=5):
    # 修正打包路径
    # model = tools.load_model(RF_PATH)
    # explainer,X_test = get_explainer()

    import warnings
    from sklearn.exceptions import DataConversionWarning
    with warnings.catch_warnings():
        warnings.filterwarnings(action='ignore', category=UserWarning)

        input_np = df_1.values.squeeze()
        exp = explainer.explain_instance(
            input_np, model.predict_proba, n_featrues)

    list_of_explanations = exp.as_list()

    # Create a more detailed description based on feature names and weights
    detailed_descriptions = []
    for feature, weight in list_of_explanations:
        # Craft the description
        if weight > 0:
            description = f"contribute {weight:.2f} suspicious feature: {feature}"
        else:
            description = f"contribute {-weight:.2f} regular feature: {feature}"
        detailed_descriptions.append(description)

    # Print the detailed descriptions
    # for description in detailed_descriptions:
    #     print(description)
    return detailed_descriptions


if __name__ == '__main__':
    explain_pdf(343)
'''