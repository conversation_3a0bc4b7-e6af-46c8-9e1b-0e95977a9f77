
import os
import re
import sys
import warnings
warnings.filterwarnings(action='ignore', category=FutureWarning)

# 向上回转两级目录 来到anxinai
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    #from utils.logger_config import setup_logger
    import ai_pdf.extract_pdf_javascript as getJs
    import ai_pdf.pdfBaseTools as tools
    #logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    #logger = setup_logger('pdf_jsobfuse', logfile)


def special_symbols_times(javascript_code):
    javascript_code = javascript_code.replace('&&', '')  # 去除&&，只考虑作为运算符的&
    symbols = ['%', '^', '*', '&', '!', '|', '<<', '>>', '$']
    # 其他的特殊字符可能是加密字符的运算
    cnt = 0
    for sign in symbols:
        cnt += javascript_code.count(sign)
    return cnt

# 用于计算指定 区域（一般是函数参数）里面包含的加号个数


def plus_times(line_code):
    sign_list = ['+', '+=', '%u', 'substr', 'String', 'concat']  #
    cnt = 0
    for sign in sign_list:
        cnt += line_code.count(sign)
    cnt -= line_code.count('++') * 2
    return cnt

# 获取指定函数的参数字符串 中的特殊字符


def check_para_signs(javascript_code):
    functions_list = ['eval', 'unescape', 'this', 'String', 'fromCharCode']
    plus_cnt = 0
    special_cnt = 0
    for func in functions_list:
        pattern = re.escape(func) + r'\((.*?)\)'
        matches = re.findall(pattern, javascript_code)
        for match in matches:
            plus_cnt += plus_times(match)
            special_cnt += special_symbols_times(match)

    # TODO: 考察特定函数参数中的特殊字符对混淆的影响
    if plus_cnt > 0:
        logger.info(f'function para has {plus_cnt} + signs.')
    if special_cnt > 0:
        logger.info(f'function para has {special_cnt} special signs.')


# 不考虑函数本身，直接查看[] () =xxx;内包含符号数目
def interval_confusion_detection(javascript_code):
    javascript_code = tools.clean_comments(javascript_code)

    plus_cnt = 0
    special_cnt = 0

    pattern = r'\((.*?)\)'
    matches = re.findall(pattern, javascript_code)
    for match in matches:
        plus_cnt = max(plus_times(match), plus_cnt)
        special_cnt = max(special_symbols_times(match), special_cnt)

    pattern1 = r'\[(.*?)\]'
    matches = re.findall(pattern1, javascript_code)
    for match in matches:
        plus_cnt = max(plus_times(match), plus_cnt)
        special_cnt = max(special_symbols_times(match), special_cnt)

    logger.info(
        f'Number of plus signs  {plus_cnt} Number of special characters {special_cnt}')
    # TODO: 阈值是需要进一步考察的
    if plus_cnt > 2 and special_cnt > 3:
        return True
    return False


def has_comment_confusion(javascript_code):
    # 匹配注释 /* 注释 */
    pattern = r'\/\*(.*?)\*\/'
    matches = re.findall(pattern, javascript_code)
    if len(matches) == 0:
        # logger.info("不存在注释")
        return False

    all_comments = ""
    max_rep_times = 0  # 记录相同注释出现的最大次数

    cur_comment = matches[0]
    cnt = 0
    for comment in matches:
        # print(comment)
        all_comments += comment
        if comment == cur_comment:
            cnt += 1
        else:
            cur_comment = comment
            max_rep_times = max(max_rep_times, cnt)
            cnt = 1

    max_rep_times = max(max_rep_times, cnt)
    if len(matches) > 10 and max_rep_times / len(matches) > 0.75:  # 存在大量一模一样的注释
        logger.info('Comment confusion exists')
        return True

    num_ratio = tools.calculate_digit_ratio(all_comments)
    avg_comment_len = len(all_comments) / len(matches)  # 注释的平均长度

    # TODO: 需要进一步探索阈值的问题
    if avg_comment_len > 40 and len(matches) > 5:
        logger.info(f"The average length of comments is {avg_comment_len}")
        return True
    if num_ratio > 0.10:  # 可能是随机生成的字符串
        logger.info(
            f"The ratio of the numbers in the annotation is {num_ratio}")
    if len(matches) > 5 and all_comments.find(' ') == -1:  # 对英文注释有效，但中文注释确实没有空格
        logger.info('There are no spaces in the comments.')
        return True

    return False


def remove_splicing_confusion(obfuscated_code):
    if obfuscated_code.find('/*') != -1:
        obfuscated_code = re.sub('/\*.*?\*/', '', obfuscated_code)  # 去掉插入的注释

    obfuscated_code = obfuscated_code.replace("'", '"')  # 统一替换单引号为双引号
    # 目前只是针对字符串常量的拼接 简单的替换合并字符串
    if obfuscated_code.find('"+"') != -1:
        obfuscated_code = obfuscated_code.replace('"+"', "")

    return obfuscated_code


def has_variable_name_confusion(javascript_code):
    # 匹配变量名和函数名的正则表达式
    variable_pattern = r'\bvar\s+([a-zA-Z_]\w*)\b'
    function_pattern = r'\bfunction\s+([a-zA-Z_]\w*)\b'

    # 提取变量名和函数名
    variable_names = re.findall(variable_pattern, javascript_code)
    function_names = re.findall(function_pattern, javascript_code)

    # 1. 考虑数字的比率
    all_name_str = ""
    total_length = 0
    for name in variable_names + function_names:
        all_name_str += name
        total_length += len(name)
    num_ratio = tools.calculate_digit_ratio(all_name_str)
    # logger.info(num_ratio)
    if num_ratio > 0.15 and len(variable_names) > 12:
        logger.info("Variable names are confused by too many numbers")
        return True

    # 2. 考虑长度
    if len(function_names) > 0:
        long_name_cnt = 0
        for name in function_names+variable_names:
            if len(name) > 15:
                long_name_cnt += 1

        ratio_greater_than_15 = long_name_cnt * \
            100.0 / len(function_names+variable_names)
        logger.info(f'>15 var name {ratio_greater_than_15:.2f}%')
        if ratio_greater_than_15 > 12:
            logger.info("Function name confusion exists")
            return True

        # TODO: 计算平均长度， 阈值的大小需要进一步考察
        average_length = total_length / \
            (len(variable_names) + len(function_names))
        if average_length > 6:
            logger.info("Variable name too long confusion.")
        logger.info(
            f'The average length of variable names is {average_length}')


def has_loop_confusion(javascript_code):

    # 清理非&&的逻辑判断
    javascript_code = javascript_code.replace('&&', " ")

    regex = re.compile(r'(?:for|while)\s*\([^)]*\)\s*{([\s\S]*?)}')
    matches = regex.findall(javascript_code)
    loop_bodies = [match.strip() for match in matches]

    # 1 检查字符串拼接
    connect_keys = ['+=', 'String', 'substr',
                    'fromCharCode', 'charCodeAt', 'concat']
    special_keys = ['& ', '^ ', '%u', 'unescape',]

    for loop_body in loop_bodies:
        for key in connect_keys:
            if loop_body.find(key) != -1:
                logger.info("There is string concatenation confusion")
                return True
        for key in special_keys:
            if loop_body.find(key) != -1:
                logger.info("There is string decoding obfuscation")
                return True

    # 2 检查字符串命名的混淆
    for loop_body in loop_bodies:
        num_ratio = tools.calculate_digit_ratio(loop_body)
        # logger.info(num_ratio)
        if num_ratio > 0.25:
            logger.info("There is string naming confusion")
            return True

    return False


def split_or_replace_confusion(javascript_code):
    # 使用正则表达式找到所有的split(X).join(Y)函数，并提取X,Y
    pattern = r'.split\((.*?)\)'  # .join\((.*?)\)
    matches = re.findall(pattern, javascript_code)

    # 统计split函数的参数 X被当成分隔符
    obfuscated_string_cnt = 0
    for match in matches:
        delimiter = match.replace('"', '').replace("'", "")
        obfuscated_string_cnt += javascript_code.count(delimiter)

    if obfuscated_string_cnt > 20:  # 存在大量的分隔符
        logger.info("There is confusion about splitting very long strings")
        return True

    # replace(/Z/g,'%u').replace(/X/g,'0');
    # TODO:如果能找到第一个参数的在字符串中出现的次数也有参考意义

    # 使用正则表达式找到所有的replace函数
    cnt_replace = javascript_code.count('replace')
    if cnt_replace == 0:
        return False
    line_str = javascript_code.replace('"', "'")
    matches = re.findall(r'replace\(.+?,\s*(.+?)\)', line_str)
    cnt = 0
    keys = ["'%'", "'n'", "'%u'", "'0'", "'x'", "''"]
    for match in matches:
        # logger.info(match)
        if match in keys:
            cnt += 1
    if cnt > 1:
        logger.info('replace involves function name confusion')
        return True

    return False


def has_keyword_confusion(javascript_code):
    has_keyword_confusion = 0

    # 简单预处理
    old_line = javascript_code
    no_spaces_line = tools.convert_multiple_spaces(old_line, '')  # 去掉空格

    typical_samples = ['=app;', '=app.doc;', '=unescape;', '="ev";', '=eval;', '=this;', '="%";', '="%u";', '=this.info.title;'
                       'returneval(', 'returnunescape(', ]
    # 良性文件也可能存在 '=app.viewerVersion.toString();','=app.plugIns;'
    for typical_sample in typical_samples:
        if no_spaces_line.find(typical_sample) != -1:
            logger.info(
                f'variable name {typical_sample} replacement obfuscation')
            has_keyword_confusion += 1

    clear_str = remove_splicing_confusion(old_line)
    obfuse_keys = ['subject', 'eval', 'unescape', 'fromCharCode', 'this', 'mediaPlayer',
                   '0x', '%u', 'info', 'push', 'getPage', 'charCodeAt', 'viewerVersion',
                   'String', 'for', 'while', 'else', 'Array', 'media', 'newPlayer', "%u9090",
                   'replace'
                   ]

    # 判断 1：之前未检索到关键字，去掉拼接的混淆后，存在关键字 如 'ev'+'al'
    for key in obfuse_keys:
        if old_line.find(key) == -1 and clear_str.find(key) != -1:
            logger.info(f'string {key} concat confusation exists')
            has_keyword_confusion += 1

    # 判断 2：检测数组切分混淆 记录String(到)之间的 ']+'的频率
    # var adobe =[ "p", "a", "p", ""];
    # mail= new String(adobe[1]+ adobe[0] +adobe [0]+ adobe[3]);
    string_matchs = tools.extract_between(no_spaces_line, 'String(', ')')
    for match in string_matchs:
        if match.count(']+') > 2:
            logger.info('Array slicing confusion exists')
            has_keyword_confusion += 1

    # 判断 3：检测字符串 和 变量拼接的混淆

    if has_keyword_confusion > 0:
        return True
    return False


def parse_extremel_long_string(longest_string, key_frequency_dict):
    # 复合特征1: 超长字符串 且 四个关键字同时存在
    features = ['replace', 'eval', 'split', 'unescape']
    appear_feature_num = 0
    for feature in features:
        if key_frequency_dict[feature] > 0:
            appear_feature_num += 1
    if appear_feature_num == 4:
        return True

    # TODO: 阈值的选择需要根据实际情况进行调整
    threshold = 100

    # 复合特征2: 超长字符串 且 存在字符串拼接
    splicer_list = ['+', 'concat', 'substr', "("]
    splicer_num = 0
    for splicer in splicer_list:
        splicer_num += longest_string.count(splicer)
    if splicer_num > threshold:
        return True

    # 复合特征3: 超长字符串 且 存在转义字符
    escape_char_list = ['\\x', '\\u', '%u']
    escape_char_num = 0
    for escape_char in escape_char_list:
        escape_char_num += longest_string.count(escape_char)
    if escape_char_num > threshold:
        return True

    # 复合特征4: 超长字符串 且 大量的空格和逗号 超大数组
    separator_list = [',', ' ', '\\', '-', '$']
    separator_num = 0
    for separator in separator_list:
        separator_num += longest_string.count(separator)
    if separator_num > threshold:
        return True

    # 复合特征5: 超长字符串 且 存在大量的特殊运算符 用于解码加密字符的运算
    special_ops_list = ['%', '^', '*', '&', '-', '!', '|', '<<', '>>']
    special_ops_num = 0
    for special_ops in special_ops_list:
        special_ops_num += longest_string.count(special_ops)
    if special_ops_num > threshold:
        return True

    return False


def analyze_global_dictionary(key_frequency_dict):
    confusion_threshold_dictionary = {'%u': 30, 'null': 20, ',': 300, '+': 180, 'function': 30, 'var': 30,
                                      'for': 12, 'while': 12, 'substr': 12,  '\\x': 12, '\\u': 12, 'URI': 12,
                                      'charCodeAt': 12,  'app[': 12,
                                      }
    # TODO:初步采样的结果，最好对全局恶意样本提取平均值

    confusing_scores = 0
    for k, v in key_frequency_dict.items():
        if k in confusion_threshold_dictionary and v > confusion_threshold_dictionary[k]:
            confusing_scores += 1

    return confusing_scores


def identify_code_obfuscation(javascript_code):
    # logger.info(f'---obfuscating-----------')
    if javascript_code is None or javascript_code == '' or javascript_code == 'incorrect header':
        return 0

    if len(javascript_code) < 150:
        return 0

    key_feature_words = ['%u', 'null', ',', '+', '%u9090', 'replace', 'split', 'eval',
                         'unescape', 'this', 'String', 'fromCharCode', 'function', 'var',
                         'for', 'while', 'substr', 'x-javascript', 'getPage', "\\x", "\\u", 'URI',
                         'charCodeAt', 'mediaPlayer', 'viewerVersion', 'app[', 'Array', 'newPlayer',
                         '%n', '%s', '.exe'
                         ]
    # TODO: %20是空格字符 收集其他的特殊字符 this.exportDataObject
    # 1. 找到最长字符串
    longest_string = ""

    string_list = tools.extract_between(javascript_code, '=', ';')
    return_list = tools.extract_between(javascript_code, 'return', '}')
    for string in string_list+return_list:
        if len(string) > len(longest_string):
            longest_string = string.strip('\r\n')
    # print('longest_string',longest_string)

    # 1.1 最长字符串的末尾中隐藏着敏感函数的特征
    last_100 = longest_string[-100:]
    funtions = ['eval', 'fromChar', 'String.fromC']
    if any(f in last_100 for f in funtions):
        return 1

    # 2. 找到所有的key的频率
    key_frequency_dict = tools.count_keyword_frequency(
        javascript_code, key_feature_words)
    key_frequency_dict['longest_string'] = len(longest_string)
    # for k, v in key_frequency_dict.items():
    #     print(f'{k:18s}:{v:6.0f}')

    # 3. 初步判断是否存在恶意代码
    has_malicious_code = False
    if key_frequency_dict['%u9090'] > 100:  # NOP即空指令，常见于溢出攻击
        has_malicious_code = True
    elif key_frequency_dict['unescape'] > 14:  # 用于解码转义字符串
        has_malicious_code = True
    elif key_frequency_dict['.exe'] > 0:  # 用于启动外部程序
        has_malicious_code = True

    if has_malicious_code:
        logger.info('There is malicious code')
        return -1

    # 4. 初步判定考虑是否存在混淆
    confusing_scores = 0
    confusing_scores += analyze_global_dictionary(key_frequency_dict)
    # logger.info('confusing_scores', confusing_scores)
    if confusing_scores > 2:
        logger.info('There is global confusion')
        return 1

    # 5.考虑超长字符串的情况
    if key_frequency_dict['longest_string'] > 1300:
        has_malicious_codes = parse_extremel_long_string(
            longest_string, key_frequency_dict)
        if has_malicious_codes:
            logger.info('There is confusion over long strings')
            return 1

    # 6. JavaScript 无超长字符串的代码分析
    comment_confusion = has_comment_confusion(javascript_code)
    if comment_confusion:
        return 1
    keyword_confusion = has_keyword_confusion(javascript_code)
    if keyword_confusion:
        return 1
    replace_split = split_or_replace_confusion(javascript_code)
    if replace_split:
        return 1
    cycle_confusion = has_loop_confusion(javascript_code)
    if cycle_confusion:
        return 1
    var_confusion = has_variable_name_confusion(javascript_code)
    if var_confusion:
        return 1
    has_confusion = interval_confusion_detection(javascript_code)
    if has_confusion:
        return 1

    return 0


def deal_one_pdf(pdf_path):
    # print(pdf_path,end=" ")
    js, _, _ = getJs.get_pdf_containing_javascript(pdf_path)
    # print(js)
    ret = identify_code_obfuscation(js)
    return ret, pdf_path


def test():

    # ./test200\104.pdf  # 多个pdf拼接，且缺少endstream
    # ./test200\1054.pdf #  乱码
    # ./test200\1060.pdf # 1
    # ./test200\1070.pdf
    # ./test200\g100.pdf

    #  整体测试
    # pdf = r'./test200\100.pdf'
    # code, has_obfuse,_ = getJs.get_pdf_containing_javascript(pdf)
    # logging.info(code)
    # out = identify_code_obfuscation(code)
    # logging.info(out)

    # 函数测试
    obfuse_str = ''' '''
    # has_obfuse = has_keyword_confusion(obfuse_str)
    # has_obfuse = split_or_replace_confusion(obfuse_str)
    # has_loop_confusion = has_loop_confusion(obfuse_str)
    # has_obfuse = has_variable_name_confusion(obfuse_str)
    # has_obfuse = interval_confusion_detection(obfuse_str)

    # 测试200文件
    # pdf_dir = './test200'
    # out = tools.process_pdf_files(pdf_dir, deal_one_pdf, 0,100)
    # for it in out:
    #     if it[0]==0:
    #         print(it[1])
    # 结论：加密之后的pdf文件，在解压FlateDecode时总失败

    # 良性pdf中提取的JS
    js_code = ""
    out = identify_code_obfuscation(js_code)
    print(out)


if __name__ == "__main__":
    tools.calculate_runtime(test)
