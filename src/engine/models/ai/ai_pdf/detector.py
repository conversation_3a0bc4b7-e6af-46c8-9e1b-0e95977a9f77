import argparse
import os
import sys
from joblib import load
from collections import Counter
import warnings
warnings.filterwarnings("ignore", category=UserWarning, module='xgboost')



# 获取文件的绝对路径，向上回转两级目录，来到anxinai
# 然后append到sys.path中，这样就可以在其他地方导入ai_pdf模块
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    #from utils.logger_config import setup_logger
    #解释器，为什么pdf被判定为异常
    #import ai_pdf.desc_pdf_result as why
    #提取pdf特征
    import ai_pdf.extract_pdf_features as getFeatures
    #各种工具
    #import ai_pdf.pdfBaseTools as tools

    #提取文件的绝对路径
    ROOT_PATH = os.path.dirname(os.path.abspath(__file__))
    #模型路径
    MODELS_DIR = os.path.join(ROOT_PATH, 'models', 'pdf_models')
    #保存随机森林模型路径
    RF_PATH = os.path.join(MODELS_DIR, 'RF_2w_2w_best.joblib')
    #日志路径
    #logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    #日志配置
    #logger = setup_logger('pdf_detector', logfile)

_MODEL_CACHE = None
# 一些工具
#  定义字段的数据类型
data_types = {
    'FileName': 'category',  # 文件名
    'PdfSize': 'float32',  # PDF文件大小
    'MetadataSize': 'float32',  # 元数据大小
    'Pages': 'float32',  # 页数
    'XrefLength': 'float32',  # Xref表长度
    'TitleCharacters': 'float32',  # 标题字符数
    'isEncrypted': 'float32',  # 是否加密
    'EmbeddedFiles': 'float32',  # 嵌入文件数
    'Images': 'float32',  # 图像数
    'Text': 'category',  # 文本存在与否
    'Header': 'float32',  # 头部信息
    'Obj': 'float32',  # 对象数
    'Endobj': 'float32',  # 结束对象数
    'Stream': 'float32',  # 流数
    'Endstream': 'float32',  # 结束流数
    'Xref': 'float32',  # Xref表数
    'Trailer': 'float32',  # 尾部信息
    'StartXref': 'float32',  # 开始Xref位置
    'PageNo': 'float32',  # 页码
    'Encrypt': 'float32',  # 加密信息
    'ObjStm': 'float32',  # 对象流数
    'JS': 'float32',  # JavaScript代码数
    'Javascript': 'float32',  # JavaScript代码数
    'AA': 'float32',  # 自动动作数
    'OpenAction': 'float32',  # 打开动作数
    'Acroform': 'float32',  # 交互式表单数
    'JBIG2Decode': 'float32',  # JBIG2解码器数
    'RichMedia': 'float32',  # 丰富媒体数
    'Launch': 'float32',  # 启动动作数
    'EmbeddedFile': 'float32',  # 嵌入文件数
    'XFA': 'float32',  # XFA表单数
    'Colors': 'category',  # 颜色信息
    'Class': 'category',  # 类别信息
    'MalTrail1': 'float32',  # 恶意痕迹1
    'MalTrail2': 'float32',  # 恶意痕迹2
    'MalTrail3': 'float32',  # 恶意痕迹3
    'DiffObj': 'float32',  # 对象差异
    'DiffStream': 'float32',  # 流差异
    'ElseDecode': 'float32',  # 解码差异
    'Names': 'float32',  # 名称信息
    'FontName': 'float32',  # 字体名称
    'JsBadStream': 'float32',  # 坏流JavaScript
    'SensitiveCode': 'float32',  # 敏感代码
    'KeywordObfuscation': 'float32',  # 关键词混淆
    'JsObfuscation': 'float32',  # JavaScript混淆
    # 'ShellInject': 'float32',  # shell注入
    'MayBenign': 'float32',  # 可能良性
    'MayMalicious': 'float32'  # 可能恶意

}

#特征处理以及误报优化
def adjust_features(df):
    # print(df.iloc[0])

    # 将字典中的值转换为相应的数据类型
    for key, value in data_types.items():
        df[key] = df[key].astype(value)

    # 去除文件名、标签，不参与特征输入
    if 'FileName' in df.columns:
        df.drop(columns=['FileName'], inplace=True)
    if 'Class' in df.columns:
        df.drop(columns=['Class'], inplace=True)

    # Uri 缺失检查
    if 'Uri' not in df.columns:
        df['Uri'] = float(0.0)
        df['Uri'] = df['Uri'].astype('float32')
    #强制给jsbadstream赋值为0，规避某些异常误报
    if 'JsBadStream' in df.columns:
        df['JsBadStream'] = float(0.0)

    # category类型特征（例如yes or no）转数值
    cats = df.select_dtypes(include='category').columns
    # 转换category为数值编码
    df[cats] = df[cats].apply(lambda x: x.cat.codes)
    
    #再次删除class这个列 
    targ = 'Class'
    if not isinstance(targ, list):
        df = df[df.columns.difference([targ])].copy()
    else:
        df = df[df.columns.difference(targ)].copy()

    # TODO: 过滤某类识别错误【因为当下这种数据不足】
    auto_exe = df['OpenAction'].iloc[0]+df['AA'].iloc[0]

    # URI相关文档误报优化
    # 1 URI 有许多 配合少许的JS  但是没有自启动，多见于论文、出版书的链接跳转
    if df['Uri'].iloc[0] > 0.0:
        #加密、对象流、自启动、Acroform、嵌入文件、XFA
        uri = df['Encrypt'].iloc[0]+df['ObjStm'].iloc[0]+auto_exe + \
            df['Acroform'].iloc[0]+df['Launch'].iloc[0] + \
            df['EmbeddedFile'].iloc[0]+df['XFA'].iloc[0]

        print(f'URI = {uri}')
        #上述问题都没有，则赋值url、js、良性、恶意的值
        if uri < 1.0:
            df['Uri'] = 0.0
            df['Javascript'] = 0.0
            df['MayBenign'] = 10.0 #良性概率
            df['MayMalicious'] = 0.0 #恶意概率
            return df

    # 2 发票类型的文件只包含单个AcroForm和单个ObjStm 或单个JBIG2Decode
    if df['Pages'].iloc[0] < 2.0 and df['Trailer'].iloc[0] < 1.0 and df['TitleCharacters'].iloc[0] < 1.0:
        #加密、对象流、自启动、Acroform、嵌入文件、XFA
        invoice = df['Encrypt'].iloc[0]+auto_exe + \
            df['Acroform'].iloc[0]+df['Launch'].iloc[0] + \
            df['EmbeddedFile'].iloc[0]+df['XFA'].iloc[0]

        print(f'Invoice = {invoice}')

        if df['ObjStm'].iloc[0]+df['Acroform'].iloc[0]+df['JBIG2Decode'].iloc[0] < 4.0 and invoice < 1.0:
            df['ObjStm'] = 0.0
            df['Javascript'] = 0.0
            df['MayBenign'] = 10.0
            df['MayMalicious'] = 0.0
            df['Pages'] = 5.0  # 多是单页和恶意文件相似
            df['Trailer'] = 1.0  # 发票类型缺少必要尾部
            df['TitleCharacters'] = 26.0  # 发票类型缺少创建信息FileError

            return df

    # 3 简历类型的pdf误报 只有许多的流对象
    if df['Pages'].iloc[0] <= 2.0 and df['TitleCharacters'].iloc[0] < 1.0 \
            and df['ObjStm'].iloc[0] > 0.0:
        resume = df['Encrypt'].iloc[0]+auto_exe + \
            df['Acroform'].iloc[0]+df['Launch'].iloc[0] + \
            df['EmbeddedFile'].iloc[0]+df['XFA'].iloc[0]
        print(f'Resume = {resume}')

        if resume < 1.0:
            df['ObjStm'] = 0.0
            df['XrefLength'] = 120.0
            df['Javascript'] = 0.0
            df['MayBenign'] = 10.0
            df['MayMalicious'] = 0.0
            df['TitleCharacters'] = 26.0  # 发票类型缺少创建信息FileError
            df['Pages'] = 5.0  # 多是单页和恶意文件相似
            return df
    
    # 去除file_error的部分情况：频繁解析压缩失败导致单个FILE—ERROR
    # 部分“文件解析失败”的情况，模型可能会误报恶意。如果实际没有敏感特征，则修正为良性，避免因“解析失败”误判
    if df['FileError'].iloc[0] < 2.0 and df['MayMalicious'].iloc[0] < 0.3:
        main_flags = df['Encrypt'].iloc[0]+auto_exe + \
            df['Launch'].iloc[0] + \
            df['EmbeddedFile'].iloc[0]+df['XFA'].iloc[0] + df['JS'].iloc[0]

        # TODO: 应对PDF阅读器中stamps误报的情况，Acroform 存在并不直接指向恶意
        # df['Acroform'].iloc[0]

        print(f'main_flag = {main_flags}')
        if main_flags < 1.0:
            df['MayBenign'] = 10.0
            df['FileError'] = 0.0
            df['MayMalicious'] = 0.0

    # print(df.iloc[0])
    return df

#多模型集成分类
class EnsembleClassifier:
    def __init__(self, model_paths):
        """
        初始化方法，传入模型路径列表，加载模型
        """
        self.models = [load(path) for path in model_paths]

    def parse_file(self, file_or_bytes, see=False):
        """
        解析文件或字节数据，提取特征
        
        Args:
            file_or_bytes: 文件路径或字节数据
            see: 是否打印特征
            
        Returns:
            features: 提取的特征DataFrame
        """
        # 处理输入可能是文件路径或字节数据
        if isinstance(file_or_bytes, (bytes, bytearray)):
            # 如果是字节数据，使用内存处理函数
            features = adjust_features(getFeatures.deal_pdf_bytes(file_or_bytes))
        else:
            # 如果是文件路径，使用原有方法
            features = adjust_features(getFeatures.deal_one_pdf(file_or_bytes))
            
        if see:
            print(features.iloc[0])
        return features
    
    def predict(self, file_or_bytes):
        """
        预测函数，使用加载的模型作出预测并采用投票机制返回结果
        
        Args:
            file_or_bytes: 文件路径或字节数据
            
        Returns:
            mals_vote: 预测为恶意的模型数量
            features: 提取的特征
        """
        features = self.parse_file(file_or_bytes, see=False)

        # 直接处理PDF嵌入Office的情况
        if features['EmbeddedFile'].iloc[0] > 9999:
            return 9, features

        # 获得多个模型对这个pdf的分数
        predictions = [model.predict(features).ravel()[0]
                      for model in self.models]
        mals_vote = predictions.count(1)
        return mals_vote, features


def get_model():
    # 缓存模型，避免重复加载
    global _MODEL_CACHE
    # 如果模型已加载，则直接返回缓存的模型
    if _MODEL_CACHE is not None:
        return _MODEL_CACHE
    
    # 然后构建到你的模型文件夹 'models' 的全路径
    model_dir = MODELS_DIR
    model_paths = [
        # 'MLP_2w_1h_best.joblib',
        # 'MLP_2w_1k_best.joblib',
        # 'MLP_2w_5k_best.joblib',

        # 'SVM_2w_ik_best.joblib', # 良性严重过拟合
        'MLP_1031_2.joblib',  # 稍微过拟合良性
        'MLP_1031_3.joblib',  # 稍微过拟合良性
        'ADA_1031_1.joblib',  # 稍微过拟合良性
        'ADA_1031_2.joblib',  # 稍微过拟合良性

        'ADA_2w_2w_best.joblib',
        # 'DT_2w_2w_best.joblib', # 均衡 不如ADA稳定
        'MLP_1031_1.joblib',  # 均衡
        'XGB_best.joblib',
        'RF_1031_3.joblib',  # 均衡
        # 'ADA_best.joblib',
        # 识别恶意 前四个
        # 'RF_1031_1.joblib', # 过拟合倾向
        # 'RF_best.joblib', # 过拟合倾向重
        # 'RF_2w_2w_best.joblib', # 过拟合重
        # 'XGB_2w_2w_best.joblib', # 过拟合重

        # 稍微过拟合恶意文件
        # 'RF_1031_2.joblib',
        'RF_1031_4.joblib',
        'ADA_1031_3.joblib',
        # 'ADA_1031_4.joblib',
    ]
    model_paths = [os.path.join(model_dir, path) for path in model_paths]
    ensemble = EnsembleClassifier(model_paths)
    _MODEL_CACHE = ensemble
    return ensemble


def identify_pdf_files(pdf_dir, ensemble):
    """
    主函数，遍历文件夹，调用预测函数
    """
    pdf_names = sorted(os.listdir(pdf_dir))
    predicts = []
    for pdf_name in pdf_names:
        file = os.path.join(pdf_dir, pdf_name)
        if os.path.isfile(file) and '.pdf' in pdf_name:
            print(f'{file} 开始检测...', end='')
            predict, features = ensemble.predict(file)
            predicts.append(predict)
            if predict == 0:
                print(f'[OK]')
            else:
                print(f'[FAIL]')
            print('----------------------------------')

    return predicts


def check_one_pdf(pdf_path, model):
    # import time
    # print(1,time.time())
    # 检测一个PDF文件是否为恶意PDF
    # print(f'{pdf_path} 开始检测...', end='')
    predict, feature = model.predict(pdf_path)
    #print(f'预测为恶意的分数为 {predict}/100')
    #return feature
    return predict


def check_pdf_files(pdf_dir, ensemble_model):
    pdf_names = sorted(os.listdir(pdf_dir))
    predicts = []
    # 准备解释器
    model = tools.load_model(RF_PATH)
    explainer, _ = why.get_explainer()

    for pdf_name in pdf_names:
        file = os.path.join(pdf_dir, pdf_name)
        if os.path.isfile(file) and '.pdf' in pdf_name:
            print(f'{file} 开始检测...', end='')
            predict, features = ensemble_model.predict(file)

            predicts.append(predict)
            if predict == 0:
                print(f'[PASS]')
            elif predict == 2:
                print(f'[SUSPICIOUS]')
                why.explain_mal_pdf(features, model, explainer)
            else:
                print(f'[MALICIOUS]')
                why.explain_mal_pdf(features, model, explainer)
            print('----------------------------------')

    return predicts


def prepare_pdf_models():
    # 提供给服务器代码使用
    # ensemble = get_model()
    # explainer, _ = why.get_explainer()
    modelRF = tools.load_model(RF_PATH)
    # return explainer, modelRF
    return modelRF


def get_predict(pdf_path):
    ensemble = get_model()
    predict, feature = ensemble.predict(pdf_path)
    return predict, feature


def test_folder(folder_path):
    model = get_model()
    pdf_files = [file for file in os.listdir(folder_path)]  # 获取文件夹下所有的PDF文件
    for pdf_file in pdf_files:
        # print(pdf_file)
        pdf_path = os.path.join(folder_path, pdf_file)  # 获取PDF文件的完整路径
        mals, feature = model.predict(pdf_path)
        if mals >= 5:  # 打印误报的情况
            print(f'{mals} {pdf_file}')
            analyze_one_pdf(model, pdf_path, feature)
        else:
            print(f'{mals} {pdf_file}')
            pass


def analyze_one_pdf(model, pdf_path, feature=None):
    if feature is None:
        feature = check_one_pdf(pdf_path, model)  # 调用传入的函数
    # 获取DataFrame的第一行数据
    first_row = feature.iloc[0]
    # 打印列名和对应的数据
    for i in range(len(first_row)):
        print(f'{feature.columns[i]:20}: {first_row[i]:7.1f}', end=" ")
        if i % 2 == 0:
            print()
    print()


def test():
    model = get_model()
    pdf_dir = r"/Users/<USER>/Desktop/ai-master/ai_pdf/data/FPS"
    pdf_name = '/Users/<USER>/workspace/olaf/src/engine/models/ai/ai_pdf/data/Dynamic.pdf'
    # pdf_path = os.path.join(pdf_dir, pdf_name)
    pdf_path = r"/Users/<USER>/workspace/olaf/src/engine/models/ai/ai_pdf/data/g1664.pdf" # Hanko.pdf # 

    #test_folder(pdf_dir)
    #analyze_one_pdf(model, pdf_path)
    score = check_one_pdf(pdf_path, model)
    print(f"✅ 检测分数: {score}")
    


def main():
    parser = argparse.ArgumentParser(
        description="Script to process pdf file and directory.")
    parser.add_argument("--dir", type=str,
                        help="pdf-directory to be processed")
    parser.add_argument("--file", type=str, help="pdf-file to be processed")
    args = parser.parse_args()
    # Load the model
    ensemble = get_model()

    if args.dir:
        check_pdf_files(args.dir, ensemble)

    if args.file:
        check_one_pdf(args.file, ensemble)


if __name__ == "__main__":
    # main()
    test()

    # 查看文件内识别情况
    # folder_path =r"C:\Users\<USER>\Documents\malware_data\pdf_analyze\data\bad_data\pdf_virus"
    # test_folder(folder_path)
