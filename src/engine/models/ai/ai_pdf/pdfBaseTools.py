from typing import List
from joblib import dump, load
import glob
import hashlib
import os
import re
import shutil
import subprocess
import sys
import time
import pandas as pd

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_pdf.manage_exceptions as errors
    PDFID_PATH = os.path.join(root_path, 'ai_pdf', 'stv_tools', 'pdfid.py')


# CHAR_ENCODE 编码并不支持包括中文、日文、韩文等非拉丁字符。对于包含这些字符的文本，CHAR_ENCODE 编码将无法正确表示。
CHAR_ENCODE = 'latin-1'

# 准备一些工具


def check_bytes_string(input_string):
    if isinstance(input_string, bytes):
        encoded_string = input_string.decode(CHAR_ENCODE)
        return encoded_string

    return input_string


def has_escaped_characters(string):
    string = repr(string)
    pattern = r'\\x[0-9a-fA-F]{2}|\\u[0-9a-fA-F]{4}'
    # code = re.findall(pattern, string)
    matches = re.search(pattern, string)
    return matches is not None

# 去除那些类似#6c的混淆


def restore_ascii_code(obfuscated_code_str):
    # 将 ASCII 编码字符序列转换为对应的字符
    original_code = re.sub(
        r'#([0-9a-fA-F]{2})', lambda match: chr(int(match.group(1), 16)), obfuscated_code_str)

    # 使用 repr() 函数输出字符串的可打印形式
    # printable_code = repr(original_code)

    return original_code


def has_ascii_obfuscation(obfuscated_code_str):
    # 提取混淆代码中的 ASCII 编码字符序列
    ascii_codes = re.findall(r'#([0-9a-fA-F]{2})', obfuscated_code_str)
    # for code in ascii_codes:
    #     print(code, end=" ")
    if len(ascii_codes) > 0:
        return True
    return False


def decode_string(encoded_string):
    if re.search(r'\\x[0-9A-Fa-f]{2}', encoded_string):
        try:
            decoded_string = bytes(
                encoded_string, 'utf-8').decode('unicode_escape')
        except UnicodeEncodeError as e:
            errors.record_func_error("decode_string", str(e))
            return encoded_string
    else:
        decoded_string = encoded_string
    return decoded_string


def restore_mixed_escape(escaped_string):
    try:
        import html
        escaped_string = html.unescape(escaped_string)  # 类似这种转义 &#0110;

        # 匹配形如\xHH的转义字符
        escaped_string = decode_string(escaped_string)

        # 匹配形如\uXXXX的转义字符
        escaped_string = re.sub(
            r'\\u([0-9a-fA-F]{4})', lambda m: chr(int(m.group(1), 16)), escaped_string)
    except Exception as e:
        errors.record_func_error(
            "restore_mixed_escape",  str(e))

    return escaped_string


import ahocorasick

def has_javascript_code(code):
    # 字符清理
    code = code.replace(' (', '(').replace(' [', '[')

    javascript_keywords = ['.split(', '.getAnnots(', 'String.fromCharCode(', 'eval(', 'x-javascript', 
                       'app[', 'unescape(', '"0x"+', '.length', '.replace(', 'for(', 
                       'while(', 'substr(','.exportDataObject(']

    # 初始化 Aho-Corasick自动机
    automaton = ahocorasick.Automaton()
    for idx, keyword in enumerate(javascript_keywords):
        automaton.add_word(keyword, (idx, keyword))
    automaton.make_automaton()

    # 在code中查找所有的javascript关键字
    found = False
    for end_index, (_, found_keyword) in automaton.iter(code):
        # print(f"Found {found_keyword} at index {end_index - len(found_keyword) + 1}")
        found = True
        break

    return found


def is_hex_string(string):
    """
    判断是否是十六进制字符串
    :param string: 字符串
    :return: True or False
    """
    string = string.strip('-')
    pattern = r'^([0-9a-fA-F]{2}-)*[0-9a-fA-F]{2}$'
    match = re.fullmatch(pattern, string)
    return match is not None


def decompress_hex_string(hex_string):
    hex_pairs = hex_string.split("-")
    decoded_string = ''.join([chr(int(pair, 16)) for pair in hex_pairs])
    return decoded_string


def remove_null_characters(string):
    # 使用列表推导式过滤掉 NUL 字符
    filtered_string = ''.join(char for char in string if char != '\x00')
    return filtered_string


def check_and_fix_pdf(pdf_file_path):
    pdf_str = read_file_as_string(pdf_file_path)
    # print(pdf_str)  # 原来
    # TODO: 清理空字符过于耗时，先不做
    # pdf_str = remove_null_characters(pdf_str)

    if has_javascript_code(pdf_str):  # 已有JS代码，不关心关键字混淆
        return pdf_str, 2  # 2代表已经含有JS代码

    matchs = extract_between(pdf_str, ' obj', '>>')
    keywords_str = ''
    for match in matchs:
        # TODO:还是可能存在误判 #6c
        if '#area' in match or '/FlateDecode' in match or '/MediaBox' in match:
            continue

        pattern = r"#\b[0-9a-fA-F]{2}\b"
        res = re.search(pattern, match)

        if res:
            keywords_str += ("\n" + match)
        if "\\u" in match or "\\x" in match:
            keywords_str += ("\n" + match)

    if keywords_str != '':
        pdf_str = restore_ascii_code(pdf_str)
        pdf_str = restore_mixed_escape(pdf_str)
        return pdf_str, 1
    else:
        return pdf_str, 0


def replace_content_between_markers(file_string, start_marker, end_marker):
    start_indexes = [i for i in range(
        len(file_string)) if file_string.startswith(start_marker, i)]
    end_indexes = [i for i in range(
        len(file_string)) if file_string.startswith(end_marker, i)]

    for start_index, end_index in zip(start_indexes, end_indexes):
        replaced_string = file_string[start_index:end_index + len(end_marker)]
        file_string = file_string.replace(replaced_string, "")

    file_string = convert_multiple_spaces(file_string)
    return file_string


def calculate_sha256(file_path):
    sha256_hash = hashlib.sha256()
    # 以二进制模式打开文件
    with open(file_path, 'rb') as file:
        # 逐块读取文件内容并更新哈希对象
        for chunk in iter(lambda: file.read(4096), b''):
            sha256_hash.update(chunk)

    # 获取哈希值的十六进制表示
    sha256_hex = sha256_hash.hexdigest()

    return sha256_hex

# 计数指定关键字列表，在PDF结构代码中出现的总次数


def count_fiters_occurrences(fiters, special_compression_algorithms):
    # 把Filters展平
    filter_list = [element for sublist in fiters for element in sublist]

    count = 0
    for algorithm in special_compression_algorithms:
        count += filter_list.count(algorithm)
    return count


def is_document_text(now_str):
    # TODO: 含有文档的特征格式符号，暂时不可用，会误判恶意JavaScript中的文档特殊符号
    # pattern = r"[\u253C]"

    doc_keywords = ['Tw', 'Tc', 'TD', 'TJ', 'Tj']
    for keyword in doc_keywords:
        if now_str.find(keyword) != -1:
            return True  # 是正规文档格式
    return False


def calculate_runtime(func):
    start_time = time.time()
    func()  # 调用传入的函数
    end_time = time.time()
    runtime = end_time - start_time
    print("运行时间: %.2f 秒" % runtime)


def concat_parquet_file(file_1, file_2, new_file_path):
    good_df = pd.read_parquet(file_1)
    bad_df = pd.read_parquet(file_2)
    all_df = pd.concat([good_df, bad_df])
    all_df.to_parquet(new_file_path, index=False)  # 不包含索引


def extract_pdfid_output(pdf_path):#调用pdfid工具来提取pdf结构信息
    # 构建命令行参数
    # /home/<USER>/Documents/test_ai/file_ai/ai_pdf/stv_tools/pdfid.py
    command = [
        "python",
        PDFID_PATH,
        pdf_path
    ]

    try:
        # 执行命令行并捕获输出结果
        result = subprocess.run(command,
                                # shell=True,
                                capture_output=True, text=True)
        output = result.stdout
    except subprocess.CalledProcessError as e:
        errors.record_func_error("extract_pdfid_output", str(e))
        return None
    # 'PDFiD 0.2.8 train\\good2bad\\Ben_05912.pdf\n Not a PDF document\n\n'
    if 'Not a PDF' in output:
        return None
    return output


def extract_pdf_parser_output(pdf_file):
    # 构建命令行参数 # TODO: 存在延迟问题，暂时不用
    # command = [
    #     "python",
    #     "./stv_tools/pdf-parser.py",
    #     pdf_file
    # ]

    # 直接读取PDF字符串并匹配

    pdf_str = read_file_as_string(pdf_file)
    matchs = extract_between(pdf_str, ' obj', '>>')
    output = ' '.join(matchs)

    # TODO:过滤掉 PDF Comment '%\xe2\xe3\xcf\xd3\r\n'  obj 避免对良性文件识别转义

    # 去掉方括号之间字体信息
    pattern = r'\[.*?\]'
    result = re.sub(pattern, '', output)
    return result


def convert_multiple_spaces(text, replacement=' '):
    if text == None:
        return ""
    text = check_bytes_string(text)
    if '\\r\\n' in text:
        text = repr(text).replace('\\r\\n', '').replace(
            '\\t', '').replace('\\n', '')
    text = (text).replace('\\\\r\\\\n', ' ').replace(
        "\\n", '').replace('\\t', '').replace('\\', '')

    # 使用正则表达式将多个连续的空格替换为自定义字符
    converted_text = re.sub(r'\s+', replacement, text)

    return converted_text


def read_file_as_string(file_path):
    with open(file_path, 'rb') as file:
        file_data = file.read()

    # 将字节数据转换为字符串 CHAR_ENCODE
    file_string = check_bytes_string(file_data)
    file_string = convert_multiple_spaces(file_string)

    return file_string


def is_pdf_file(file_path):
    has_pdf_header = False
    with open(file_path, 'rb') as file:
        header = file.read(4)
        has_pdf_header = (header == b'%PDF')

    is_broken_pdf = False
    try:
        import fitz
        doc = fitz.open(file_path)
    except Exception as e:
        if 'broken document' in str(e):
            is_broken_pdf = True

    return has_pdf_header and not is_broken_pdf


def count_files_in_folder(folder_path):
    file_count = 0
    files = glob.glob(os.path.join(folder_path, "*"))
    # 遍历文件列表并计数
    for file in files:
        if os.path.isfile(file):
            file_count += 1

    return file_count


def process_pdf_files(pdf_files, process_func, start=0, num=100000):
    file_nth = 0  # 记录文件序号

    ans = []

    files_num = count_files_in_folder(pdf_files) - start
    if num != 100000:
        files_num = num  # 指定要处理的文件数
    interval = files_num/20  # 每处理二十，输出一次提示信息
    for filename in os.listdir(pdf_files):
        file_path = os.path.join(pdf_files, filename)  # 使用绝对路径
        # print(file_path)

        if file_nth >= start:
            try:
                # 执行传入的处理函数 记录对应文件的处理结果
                result = process_func(file_path)
                ans.append(result)
                now_num = file_nth - start + 1
                if now_num >= files_num:
                    break
                if now_num % interval == 0:
                    # 打印当前实际处理的进度
                    print(f"{now_num * 100.0 / files_num:3.0f}% ", end="")
                    sys.stdout.flush()
            except Exception as e:
                errors.record_func_error(
                    "process_pdf_files", filename + str(e))
                pass

        file_nth += 1

    return ans


def get_one_nonpdf(pdf_path):
    if is_pdf_file(pdf_path):
        return None
    return pdf_path


def filter_non_pdfs(pdfs_dir):
    out = process_pdf_files(pdfs_dir, get_one_nonpdf)
    non_pdfs = []
    for file in out:
        if file:
            non_pdfs.append(file)
    return non_pdfs


def move_files_to_folder(file_list, folder_path):
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)

    for file in file_list:
        # 构建目标文件的路径
        destination = os.path.join(folder_path, os.path.basename(file))
        # 如果目标文件已存在，则跳过移动操作
        if os.path.exists(destination):
            continue
        # 移动文件到目标文件夹
        shutil.move(file, destination)


def extract_between(code, start, end):
    code = check_bytes_string(code)
    # 编译正则表达式
    pattern = re.escape(start) + r'(.*?)' + re.escape(end)
    matches = re.findall(pattern, code, flags=re.DOTALL)
    return matches


def count_keyword_frequency(text, keywords):
    text = check_bytes_string(text)
    frequency = {}
    for keyword in keywords:
        count = text.count(keyword)
        frequency[keyword] = count
    return frequency


def count_keyword_occurrences(text, keywords):
    text = check_bytes_string(text)
    count = 0
    for keyword in keywords:
        count += text.count(keyword)
    return count


def clean_comments(text):
    pattern = '/\*.*?\*/'
    cleaned_text = re.sub(pattern, "", text, flags=re.DOTALL)
    return cleaned_text


def calculate_digit_ratio(string):
    digit_count = 0
    total_count = len(string)
    digit_ratio = 0.0

    for char in string:
        if char.isdigit():
            digit_count += 1

    if total_count > 0:
        digit_ratio = digit_count / total_count
    return digit_ratio


def rename_pdfs(folder_path, malicious_tag=True):
    files = os.listdir(folder_path)
    pdf_files = [file for file in files if file.endswith('.pdf')]

    for i, file in enumerate(pdf_files, start=1):
        file_name, file_ext = os.path.splitext(file)
        if malicious_tag:
            new_file_name = f"Mal_{str(i).zfill(5)}{file_ext}"  # malicious
        else:
            new_file_name = f"Ben_{str(i).zfill(5)}{file_ext}"  # benign label
        old_path = os.path.join(folder_path, file)
        new_path = os.path.join(folder_path, new_file_name)
        os.rename(old_path, new_path)


def is_pdf_encrypted(pdf_path):
    import fitz
    doc = fitz.open(pdf_path)
    is_encrypted = doc.is_encrypted
    doc.close()
    return is_encrypted


def save_model(model, save_path='./models/model.joblib'):
    dump(model, save_path)


def load_model(model_path='./models/model.joblib'):
    return load(model_path)


def filter_broken_pdf(pdf_dir, save_path):
    # 抽取非标准的PDF文件
    non_pdfs_path_list = filter_non_pdfs(pdf_dir)
    move_files_to_folder(non_pdfs_path_list, save_path)
    # TODO: 恶意文件故意破坏PDF结构，是恢复后继续分析还是直接过滤


def get_parquet_files(folder_path, distinguish_word):
    # 获取指定目录下，文件名含有distinguish_word的所有文件的路径列表
    file_list = []

    for file_name in os.listdir(folder_path):
        if distinguish_word in file_name:
            file_path = os.path.join(folder_path, file_name)
            file_list.append(file_path)

    return file_list


def merge_parquet_files(file_list, output_file):
    merged_df = pd.DataFrame()

    for file in file_list:
        df = pd.read_parquet(file)
        merged_df = pd.concat([merged_df, df])

    merged_df.to_parquet(output_file, index=False)


def summary_merge_parquet_files(file_dir, output_file, distinguish_word):
    # 把分步获得的子文件合并
    file_list = get_parquet_files(file_dir, distinguish_word)
    merge_parquet_files(file_list, output_file)
    # 检查是否合并成功
    df = pd.read_parquet(output_file)
    print(df.info())


def get_train_data():
    # TODO: 随后改为一个一步完成的pipeline
    pdf_dir = "./data_frame"
    output_file_1 = "./data_train/anxin_bad.parquet"
    distinguish_word = "anxin_bad"
    summary_merge_parquet_files(pdf_dir, output_file_1, distinguish_word)

    output_file_2 = "./data_train/anxin_good.parquet"
    distinguish_word = "anxin_good"
    summary_merge_parquet_files(pdf_dir, output_file_2, distinguish_word)

    output_file_3 = "./data_train/anxin_all.parquet"
    concat_parquet_file(output_file_1, output_file_2, output_file_3)


def copy_files_to_folder(files_path_list: List[str], dest_folder: str) -> None:
    """
    Copy files from a list of file paths to a destination folder.

    Args:
        files_path_list (List[str]): A list of file paths to copy.
        dest_folder (str): The destination folder to copy the files to.

    Returns:
        None
    """
    os.makedirs(dest_folder, exist_ok=True)

    for file_path in files_path_list:
        if os.path.isfile(file_path):
            shutil.copy(file_path, dest_folder)
        else:
            print(f"无法找到文件 {file_path}")


def sample_parquet(ben_num, mal_num, save_path):
    # 采样
    ben_df = pd.read_parquet("data_train\pdf22\pdf22_ben.parquet")
    ben_df_1 = pd.read_parquet("data_train\\anxin\\anxin_good.parquet")
    ben_df = pd.concat([ben_df, ben_df_1], ignore_index=True)

    mal_df = pd.read_parquet("data_train\pdf22\pdf22_mal.parquet")
    mal_df_1 = pd.read_parquet("data_train\\anxin\\anxin_bad.parquet")
    mal_df = pd.concat([mal_df, mal_df_1], ignore_index=True)

    # check num
    ben_num = min(ben_num, len(ben_df))
    mal_num = min(mal_num, len(mal_df))

    # 根据你的需求从每个数据集中抽样
    ben_sample_df = ben_df.sample(n=ben_num)   # 设置你想从良性文件中取得的数量
    mal_sample_df = mal_df.sample(n=mal_num)    # 设置你想从恶意文件中取得的数量

    # 合并两个抽样后的数据集
    real_data_df = pd.concat([ben_sample_df, mal_sample_df], ignore_index=True)

    # 将结果写入新的parquet文件
    real_data_df.to_parquet(save_path)


def test():

    # 抽样数据

    # dir_path = 'data_train'
    # file_name = 'real_2w_2w.parquet'
    # sample_parquet(20000, 20000, dir_path + '\\' + file_name)

    # 规范化重命名
    # pdf_dir = './pdf2022/Ben'
    # rename_pdfs(pdf_dir,True)

    # 抽取关键字内容
    # pdf_path = './anxin_data/bad\Mal_04890.pdf'
    # out = extract_pdf_parser_output(pdf_path)
    # print(out)

    # 测试去混淆的JS
    # out =' /*&#0042;/app.eval); s=u&#0110;e&#115;cape("#6c\x12\u1245 \U1236520230'
    # out = restore_mixed_escape(out)
    # print(out)

    # new = restore_ascii_code(out)
    # print(new)

    # has_ascii_obfuscation(out)
    # has_escaped_characters(out)

    # pdf_dir = "pdf2022/Ben"
    # save_path = "./pdf2022_non_pdfs"
    # filter_broken_pdf(pdf_dir,save_path)
    # 示例用法

    # pdf_dir = "./data_frame"
    # output_file = "./data/pdf22_mal.parquet"
    # distinguish_word = "mal"
    # summary_merge_parquet_files(pdf_dir,output_file,distinguish_word)

    # f1 = r'data_train\\pdf22\\pdf22_all.parquet'
    # f2 = r'data_train\\anxin\\anxin_all.parquet'
    # f3 = r'data_train\\all.parquet'

    # concat_parquet_file(f1,f2,f3)

    # get_train_data()

    pdf_path = '/home/<USER>/Documents/test_ai/file_ai/Mal_03362.pdf'
    out = extract_pdfid_output(pdf_path)
    print(out)


if __name__ == "__main__":
    calculate_runtime(test)
