
import os
import sys
#from src.engine.models.ai.ai_pdf.logger_config import setup_logger
'''
# 向上回转两级目录 来到anxinai
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    from utils.logger_config import setup_logger
    logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    logger = setup_logger('pdfunc_error', logfile)

'''
def record_file_error(file_path, error_message):
    log_entry = f" FILE_ERROR {file_path} : {error_message}\n"
    logger.error(log_entry)

    # with open(FILE_ERROR_PATH, "a") as log_file:
    #     log_file.write(log_entry)


def record_func_error(func_name, error_message):
    log_entry = f"FUNC_ERROR {func_name} : {error_message}\n"
    logger.error(log_entry)
    # with open(FUNC_ERROR_PATH, "a") as log_file:
    #     log_file.write(log_entry)


def check_return_value(return_value, info, is_func=False):
    if return_value is None:
        # 执行错误处理程序
        if is_func:
            record_func_error(info, "return None.")
        else:
            record_file_error(info, "return None.")
        return False
    else:
        # 返回值正常
        return True


# def clean_log_file(file_path=FILE_ERROR_PATH):
#     with open(file_path, 'w') as file:
#         file.truncate(0)


# if __name__ == "__main__":
#     # 示例用法
#     clean_log_file()
