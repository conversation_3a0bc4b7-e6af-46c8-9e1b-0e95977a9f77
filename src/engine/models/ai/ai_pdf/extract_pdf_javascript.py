import binascii
import os
import subprocess
import sys

import zlib
import re
import base64
import lz4.frame as lzw
from io import BytesIO
from PIL import Image

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    # 测试需要
    import ai_pdf.manage_exceptions as errors
    import ai_pdf.pdfBaseTools as tools


def preprocess_string(string):
    pattern = r'(?<=Type:)(?:(?!obj).)*?(?=\s*Contains stream)'
    cleaned_string = re.sub(pattern, '', string, flags=re.DOTALL)
    cleaned_string = cleaned_string.strip() + ' Type: Contains stream'
    return cleaned_string


def extract_obj_numbers(string):
    cleaned_string = preprocess_string(string)
    pattern = r'obj\s+(\d+)\s+0\s+Type:\s+Contains stream'
    matches = re.findall(pattern, cleaned_string)
    return matches


def extract_js_obj_numbers(string):
    pattern = r"/JS (\d+) 0 R"
    matches = re.findall(pattern, string)
    return list(set(matches))


def decompress_pdf_stream(pdf_str, pdf_file):
    # pdf_str = tools.read_file_as_string(pdf_file)
    matchs = tools.extract_between(pdf_str, ' obj', '>>')
    # TODO: 关键字stream 被混淆如何
    # keywords_str = ''
    # for match in matchs:
    #     if "#" in match or "\\x" in match:
    #         keywords_str += (" " + match)

    js_objs = []
    for match in matchs:
        if "/JS" in match:
            js_objs.append(match)

    output = ' '.join(js_objs)
    # TODO: JS不总是存在，使用正则表达式搜索对象编号
    obj_ids = extract_js_obj_numbers(output)
    # obj_ids = extract_js_obj_numbers(pdf_str)

    # TODO: 全部搜索对象编号，耗时太长，部分文件的流对象三十以上
    if len(obj_ids) > 20:
        obj_ids = obj_ids[:5]

    js_code = ""
    for num in obj_ids:
        stream = extract_nth_objstream(pdf_file, num)  # 解压 奇怪

        if stream is None or stream == '':
            continue
        outstr = tools.convert_multiple_spaces(stream)

        if len(outstr) > 0 and tools.is_hex_string(outstr):  # 判断是否是十六进制编码
            outstr = tools.decompress_hex_string(outstr)

        if len(outstr) > 0 and tools.has_javascript_code(outstr):
            js_code += outstr
            # return outstr  # TODO: 提前结束，节约时间

    if len(js_code) > 0:
        return js_code
    return None


def extract_nth_objstream(pdf_file, object_number_str):
    # 构建命令行参数
    parser_path = os.path.join(
        root_path, 'ai_pdf', "stv_tools", "pdf-parser.py")
    command = [
        "python",
        parser_path,
        "-o", object_number_str,  # 指定要解析的对象编号
        "-f",  # 解析整个PDF文件的压缩stream
        pdf_file
    ]

    # 执行命令行并捕获输出结果
    result = subprocess.run(command,  # shell=True,
                            capture_output=True, text=True)

    output = result.stdout
    if output == '':
        return ''
    output = tools.convert_multiple_spaces(result.stdout)
    signs = ['decompress failed', 'zlib.error', 'Unsupported',
             '®', '©', '§', 'Á', '¾', '°', '/CIDInit', 'ü', 'ý']
    for s in signs:
        if output.find(s) != -1:
            errors.record_file_error(pdf_file, output)
            return None

    output = tools.check_bytes_string(output)
    output = output[output.find('>>')+2:]
    output = tools.restore_mixed_escape(output)
    return output


def extract_filtered_streams(filename, fix_pdf_str, has_obfuscated=False):
    if has_obfuscated:
        content = fix_pdf_str
    else:
        with open(filename, 'rb') as file:
            content = file.read()

    content = tools.check_bytes_string(content)
    matches = re.findall(r'/Filter.*?stream(.*?)endstream', content, re.DOTALL)
    streams = []
    for match in matches:
        streams.append(bytes(match.strip("\r\n"), "utf-8"))
    return streams


def get_file_size(file_path):
    size_in_bytes = os.path.getsize(file_path)
    # 内存大小通常以2的倍数来计算，1MB=1024KB=1048576B。
    size_in_megabytes = size_in_bytes / 1048576
    return size_in_megabytes


def get_pdf_containing_javascript(file_path):
    file_size = get_file_size(file_path)
    pdf_fixed, has_obfuscated = tools.check_and_fix_pdf(file_path)

    # print("pdf_fixed",pdf_fixed)
    javascript_code = ""
    codes = []
    #这里它提取了从 x-javascript 到 endstream 之间的内容到codes中
    if "x-javascript" in pdf_fixed:
        codes = tools.extract_between(pdf_fixed, 'x-javascript', 'endstream')
    #如果存在混淆，则提取/JS 到 >> 之间的内容到codes中
    if has_obfuscated == 2 and "JS" in pdf_fixed:
        codes = tools.extract_between(pdf_fixed, "/JS", ">>")

    pdf_filters = get_filters_all(pdf_fixed)

    # TODO:过滤特定的数字图章PDF
    # <dc:format>application/pdf</dc:format>
    if "<dc:format>application/pdf</dc:format>" in pdf_fixed:
        return "stamps", has_obfuscated, pdf_filters

    if len(codes) > 0:
        for code in codes:
            if has_obfuscated == 2 or tools.has_javascript_code(code):
                javascript_code += code
        if javascript_code != '':
            return javascript_code, has_obfuscated, pdf_filters

    # print(f"pdf_filters: {pdf_filters}")
    # 可能在压缩后的流对象中
    if pdf_fixed.find('endstream') == -1:
        return None, has_obfuscated, pdf_filters  # 流对象结构被破坏，无法提取

    stream_list = extract_filtered_streams(
        file_path, pdf_fixed, has_obfuscated)
    # print(stream_list[0])

    # 补充上空对象
    if len(stream_list) < len(pdf_filters):
        for i in range(0, len(pdf_filters) - len(stream_list)):
            stream_list.append(b'')

    # print('streams')
    already_used_parser = False  # 只调用一次pdf-parser工具
    if len(stream_list) == len(pdf_filters):
        for i in range(0, len(pdf_filters)):
            data = decode_compressed_stream(stream_list[i], pdf_filters[i])

            if already_used_parser == True:
                break

            if data == "pass":
                continue

            if data == 'zlib_error' and already_used_parser == False:  # 调用pdf-parser工具
                if file_size < 1.5:
                    data = decompress_pdf_stream(pdf_fixed, file_path)
                    already_used_parser = True
                    if data is None:
                        javascript_code = "incorrect header"
                        return javascript_code, has_obfuscated, pdf_filters

            if data is not None and data != '':
                # and not tools.is_document_text(data):
                if tools.has_escaped_characters(data):
                    data = tools.restore_mixed_escape(data)
                if tools.has_javascript_code(data):
                    codes.append(data)
                    break  # TODO:只收集一份，可以提速

    for code in codes:
        javascript_code += code
    # print(javascript_code)
    return javascript_code, has_obfuscated, pdf_filters


def get_filters_all(pdf_str):
    matches = re.findall(r'/Filter\s*(?:\[([^]]+)\]|(/\w+))', pdf_str)
    filters = []
    for match in matches:
        if match[0]:
            filters.append(re.findall(r'/(\w+)', match[0]))
        else:
            filters.append([match[1].strip('/')])
    return filters


def decode_dctdecode_stream(compressed_stream):
    try:
        image_data = BytesIO(compressed_stream)
        image = Image.open(image_data)
        decoded_data = image.tobytes()
        return decoded_data
    except Exception as e:
        # 处理解压缩错误
        return None


def decode_runlengthdecode_stream(compressed_stream):
    try:
        decoded_data = b''
        i = 0
        while i < len(compressed_stream):
            length = compressed_stream[i]
            if length == 128:  # 特殊情况：长度为128表示结束
                break
            elif length < 128:  # 长度小于128表示直接复制
                decoded_data += compressed_stream[i + 1: i + 1 + length + 1]
                i += length + 2
            else:  # 长度大于128表示重复字符
                byte = compressed_stream[i + 1]
                decoded_data += bytes([byte]) * (257 - length)
                i += 2
        return decoded_data

    except Exception as e:
        # 处理解压缩错误
        print(f"decode_runlengthdecode_stream: {e}")
        return None


def fix_valid_ascii85(encoded_bytes):
    filtered_bytes = bytearray()
    is_incomplete_block = False

    for byte in encoded_bytes:
        if 33 <= byte <= 117:  # 字节在 ASCII85 编码范围内
            filtered_bytes.append(byte)
        else:
            filtered_bytes.append(ord(' '))  # 替换为空格字符
            is_incomplete_block = True

    # 检查完整性
    if len(filtered_bytes) % 5 != 0:
        # 数据长度不是5的倍数，可能存在不完整的编码块
        is_incomplete_block = True
        # logging.info("warning: ascii85 incomplete block")

    if is_incomplete_block:
        filtered_bytes = filtered_bytes[:-(len(filtered_bytes) % 5)]

    return bytes(filtered_bytes)


def fix_asciihex_decode_error(data):
    data = tools.check_bytes_string(data)

    # 检查是否存在非十六进制字符
    fixed_data = ''.join(c for c in data if c.isdigit()
                         or c.lower() in 'abcdef')

    # 检查字符串长度是否为奇数
    if len(fixed_data) % 2 != 0:
        fixed_data = fixed_data[:-1]  # 删除最后一个字符

    return fixed_data.encode()


def decode_compressed_stream(compressed_stream_str, filters):
    # 保证是字节类型
    decoded_data = ""
    if isinstance(compressed_stream_str, str):
        decoded_data = compressed_stream_str.encode()
    else:
        decoded_data = compressed_stream_str
    # print("start", decoded_data)

    try:
        for compression_algorithm in filters:
            # 'FlateDecode' 'Fl'
            if compression_algorithm[0] == 'F' or compression_algorithm == 'Standard':
                decoded_data = zlib.decompress(decoded_data)

            elif compression_algorithm == 'ASCIIHexDecode' or compression_algorithm == 'AHx':  # 'AHx'
                decoded_data = fix_asciihex_decode_error(decoded_data)
                # 解码 ASCIIHexDecode
                decoded_data = binascii.unhexlify(decoded_data)

            elif compression_algorithm == 'DCTDecode':
                decoded_data = decode_dctdecode_stream(decoded_data)

            elif compression_algorithm == 'ASCII85Decode' or compression_algorithm == 'A85' or compression_algorithm == 'ASC':  # 'A85'
                decoded_data = fix_valid_ascii85(decoded_data)
                decoded_data = base64.a85decode(decoded_data)

            elif compression_algorithm == 'RunLengthDecode':
                decoded_data = decode_runlengthdecode_stream(decoded_data)

            elif compression_algorithm == 'LZWDecode':
                decoded_data = lzw.decompress(decoded_data)

            elif compression_algorithm in ['Adobe', 'CCITTFaxDecode', 'JPXDecode', 'JBIG2Decode']:
                # TODO: 对图形压缩的常用算法 先忽略
                return "pass"
            else:
                # 处理其他压缩算法
                print(f"other algorithm: {compression_algorithm}")
                return None

    except Exception as e:
        if not ('while' in str(e)):  # 过滤FlateDecode
            errors.record_func_error(
                decode_compressed_stream, compression_algorithm + " " + str(e))
        return "zlib_error"   # 使用工具解析

    if decoded_data is None:
        return None

    decoded_data = tools.check_bytes_string(decoded_data)
    return decoded_data


def process_pdf_files(pdf_files, process_func, start=0):
    file_nth = 1  # 记录文件序号
    ans = []

    # 检查文件是否存在并清空内容
    with open('out.log', 'r+') as file:
        if file.read():
            file.seek(0)  # 将文件指针移回文件开头
            file.truncate()  # 清空文件内容

    files_num = tools.count_files_in_folder(pdf_files)
    pdf_sum = files_num - start
    for filename in os.listdir(pdf_files):
        file_path = os.path.join(pdf_files, filename)  # 使用绝对路径
        if file_nth > start:
            # 执行传入的处理函数 记录对应文件的处理结果
            str, has_obfuscation, filters = process_func(file_path)
            if str == '':
                content = f'{file_path} -> {has_obfuscation} {filters} \n'
                with open('out.log', 'a') as file:
                    file.write(content)
            if file_nth % 100 == 0:
                # 打印当前实际处理的数量
                print(f"{(file_nth - start) * 100 / pdf_sum:2.0f}% ", end="")

        file_nth += 1

    return ans


def test():
    # 大规模测试 导出失败的解析到 out.log
    # pdfs_dir = './test200'    # './pdf2022/Mal'
    # process_pdf_files(pdfs_dir, get_pdf_containing_javascript, start=190)

    # 正常的例子 b919.pdf
    # 测试失败的例子 定位错误
    # './anxin_data/bad\Mal_04890.pdf' 未压缩的JS
    # ./anxin_data/bad\Mal_05050.pdf 带有#关键字混淆的JS

    # pdf = r'C:\Users\<USER>\Documents\malware_data\test_ai\file_ai\test\Mal_03362.pdf'
    pdf = r"/Users/<USER>/Desktop/ai-master/ai_pdf/data/test/Mal_03362.pdf"
    js, _, fiters = get_pdf_containing_javascript(pdf)
    print(fiters)
    print(js)
    # print(tools.convert_multiple_spaces(js))

    # 换一种方式
    # out = decompress_pdf_stream(pdf)
    # print(out)

    # 压缩数据
    # data = b'''var hfosdho='ev';'''  # 将字符串转换为字节流
    # compressed_data = zlib.compress(data)
    # print(compressed_data)

    # # 提取到的流数据 取到经过 FlateDecode 压缩的流
    # stream =b'x\x9c+K,R\xc8H\xcb/N\xc9\xc8\xb7UO-S\xb7\x06\x0095\x05\xf6'
    # out = zlib.decompress(stream) # ,16+zlib.MAX_WBITS -zlib.MAX_WBITS
    # print(out)

    # # 解码 ASCIIHexDecode 编码数据
    # decoded_data = binascii.unhexlify(ori)
    # # 打印解压缩和解码后的数据
    # print(decoded_data.hex())


if __name__ == "__main__":
    tools.calculate_runtime(test)
