import logging
from logging.handlers import RotatingFileHandler
import os
import sys


def setup_logger(name, logfile, level=logging.INFO, console_level=logging.CRITICAL, max_bytes=200 * 1024 * 1024, backup_count=2):
    """设置日志配置的函数"""

    # 放到一個文件裏
    # logfile = os.path.join(os.path.dirname(os.path.dirname(
    #     os.path.abspath(__file__))), 'logs', 'ai_scan.log')
    # logfile='/tmp/ai.log'
    # with open(logfile, "w") as file:
    #     file.write(f'{logfile} \n')

    # 创建一个logger
    logger = logging.getLogger(name)
    logger.setLevel(level)  # 设置总体的日志级别

    # 清除现有的handler
    while logger.handlers:
        logger.handlers.pop()

    # 创建一个到控制台的handler，设置其日志级别
    console_handler = logging.StreamHandler()
    console_handler.setLevel(console_level)

    # 创建一个到文件的handler，设置其日志级别和最大文件大小以及备份数量
    file_handler = RotatingFileHandler(
        logfile, maxBytes=max_bytes, backupCount=backup_count)
    file_handler.setLevel(level)

    # 定义handler的输出格式
    formatter = logging.Formatter(
        '[%(process)d] [%(asctime)s] [%(levelname)s] [%(name)s] %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加handler到logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

# # 调用函数来设置logger
# logger = setup_logger('ai_pdf_detector', '../../logs/ai_pdf.log')

# # 使用logger记录日志
# logger.info('This is an info message.')  # Print only into the log file, not console
# logger.error("This is an error message.")  # Print both into the log file and console
