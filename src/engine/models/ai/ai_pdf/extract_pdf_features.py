import glob
import logging
import os
import re
import sys
import fitz
import pandas as pd
import argparse

# 向上回转两级目录 来到anxinai
root_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(root_path)

if root_path:
    # 测试时需要
    #from utils.logger_config import setup_logger
    import ai_pdf.pdfBaseTools as tools
    import ai_pdf.extract_pdf_javascript as getJs
    import ai_pdf.manage_exceptions as errors
    import ai_pdf.identify_js_obfuscation as obfuse
    import ai_pdf.extract_embeded_js as getEmbedJs

    #logfile = os.path.join(root_path, 'logs', 'ai_scan.log')
    #logger = setup_logger('pdf_processs', logfile)


#  定义字段的数据类型
data_types = {
    'FileName': 'category',  # 文件名
    'PdfSize': 'float32',  # PDF文件大小
    'MetadataSize': 'float32',  # 元数据大小
    'Pages': 'float32',  # 页数
    'XrefLength': 'float32',  # Xref表长度
    'TitleCharacters': 'float32',  # 标题字符数
    'isEncrypted': 'float32',  # 是否加密
    'EmbeddedFiles': 'float32',  # 嵌入文件数
    'Images': 'category',  # 图像数
    'Text': 'category',  # 文本存在与否
    'Header': 'category',  # 头部信息
    'Obj': 'category',  # 对象数
    'Endobj': 'category',  # 结束对象数
    'Stream': 'float32',  # 流数
    'Endstream': 'category',  # 结束流数
    'Xref': 'category',  # Xref表数
    'Trailer': 'float32',  # 尾部信息
    'StartXref': 'category',  # 开始Xref位置
    'PageNo': 'category',  # 页码
    'Encrypt': 'float32',  # 加密信息
    'ObjStm': 'float32',  # 对象流数
    'JS': 'category',  # JavaScript代码数
    'Javascript': 'category',  # JavaScript代码数
    'AA': 'category',  # 自动动作数
    'OpenAction': 'category',  # 打开动作数
    'Acroform': 'category',  # 交互式表单数
    'JBIG2Decode': 'category',  # JBIG2解码器数
    'RichMedia': 'category',  # 丰富媒体数
    'Launch': 'category',  # 启动动作数
    'EmbeddedFile': 'category',  # 嵌入文件数
    'XFA': 'category',  # XFA表单数
    'Colors': 'float32',  # 颜色信息
    'Class': 'category',  # 类别信息
    'MalTrail1': 'float32',  # 恶意痕迹1
    'MalTrail2': 'float32',  # 恶意痕迹2
    'MalTrail3': 'float32',  # 恶意痕迹3
    'DiffObj': 'float32',  # 对象差异
    'DiffStream': 'float32',  # 流差异
    'ElseDecode': 'float32',  # 解码差异
    'Names': 'float32',  # 名称信息
    'FontName': 'float32',  # 字体名称
    'JsBadStream': 'float32',  # 坏流JavaScript
    'SensitiveCode': 'float32',  # 敏感代码
    'KeywordObfuscation': 'float32',  # 关键词混淆
    'JsObfuscation': 'float32',  # JavaScript混淆
    # 'ShellInject': 'float32',  # shell注入
    'MayBenign': 'float32',  # 可能良性
    'MayMalicious': 'float32'  # 可能恶意

}

#获取pdf的基本特征、文件名、大小、页数、元数据
def extract_pdf_general_features(pdf_file_path):
    # file_name = tools.calculate_sha256(pdf_file_path)
    file_name = os.path.basename(pdf_file_path).split('.')[0] #获取文件名
    pdf_size = int(os.path.getsize(pdf_file_path) / 1000)#获取文件大小 KB

    # 设置一个默认恶意的特征字典，供无法打开文件时备用
    features = {
        'FileName': file_name,
        'PdfSize': pdf_size,
        'MetadataSize': 150.0,
        'Pages': 1.0,
        'XrefLength': 28.0,
        'TitleCharacters': 28.0,
        'isEncrypted': 0.0,
        'EmbeddedFiles': 0.0,
        'Images': 0.0,
        'Text': "No",
        'FileError': 0  # 增加一维标记异常文件
    }
    #异常处理
    try:
        doc = fitz.open(pdf_file_path)
    except Exception as e:
        errors.record_file_error(pdf_file_path, str(e))#记录文件错误
        features['FileError'] = 10  # 文件打不开 增加一维标记异常文件
        return features
    # 获取元数据
    metadata_size = 0.0  # 获取元数据大小（以字节为单位）
    title_characters = 0.0  # 获取标题字符数
    #doc.metadata是元数据字典、包含了pdf的各种信息
    if doc.metadata:
        metadata_size = len(str(doc.metadata).encode(
            'utf-8')) - 83.0  # 去掉固定关键字的长度 如title、author之类的
        title_characters = float(len(doc.metadata.get('title', '')))
    else:
        # 不存在元数据
        features['FileError'] += 3

    # 获取页数
    num_pages = float(doc.page_count)

    # 获取xref长度
    xref_length = float(doc.xref_length())

    # 是否加密
    is_encrypted = 1.0 if doc.is_encrypted else 0.0

    # 统计图像的数量
    num_images = 0.0
    has_text = 'No'
    emfiles_avg_size = 0.0
    if 0.0 == is_encrypted:
        try:
            txt = ""
            for page in doc.pages():
                txt = txt + page.get_text()
                # 判断是否存在文本
                if len(txt) > 100:
                    has_text = 'Yes'
                    break

            cnt = 0
            for pno in range(0, len(doc)):#每一页
                cnt = cnt + len(doc.get_page_images(pno))
            num_images = float(cnt)
        except Exception as e:
            errors.record_file_error(pdf_file_path, str(e))
            features['FileError'] += 5  # page tree error

        # 获取嵌入文件平均大小（以KB为单位）
        try:
            num = doc.embfile_count()#
            size = 0
            for idx in range(0, num):
                size = size + doc.embfile_info(idx)['size']
            emfiles_avg_size = size / (num + 1.0)
        except Exception as e:
            errors.record_file_error(pdf_file_path, str(e))
            features['FileError'] += 3  # 读取嵌入文件错误

    # 将属性存储到字典中
    features_cur = {'FileName': file_name, 'PdfSize': pdf_size, 'MetadataSize': metadata_size, 'Pages': num_pages,
                    'XrefLength': xref_length, 'TitleCharacters': title_characters, 'isEncrypted': is_encrypted,
                    'EmbeddedFiles': emfiles_avg_size, 'Images': num_images, 'Text': has_text,
                    'FileError': features['FileError']}
    return features_cur


def check_javascript_code(code_str):
    if code_str == '' or code_str == 'incorrect header' or code_str is None:
        return 0.0

    java_list = ["util[", "unescape(", "xfa:data", "fromCharCode(", 'xfa:contentType ="image/tif"', "eval(", ".replace(",
                 "x-javascript", ".push(", "app[", '0c0c', '9090', '.split('
                 ]

    func_sum = 0  # 记录典型的JavaScript函数
    for key in java_list:
        if code_str.find(key) != -1:
            func_sum += 1  # 按首次出现计算

    if func_sum < 2:  # 只有某一个特征不足以说明问题
        return 0.0

    return float(func_sum)

# 处理 2(3) 格式的关键字计数


def calculate_string(string):
    pattern = r'(\d+)\s*\((\d+)\)'  # 定义匹配模式

    match = re.search(pattern, string)  # 进行匹配
    if match:
        number1 = float(match.group(1))
        number2 = float(match.group(2))
        result = number1 + 0.5 * number2
        return result
    else:
        errors.record_func_error("calculate_string", string + "error in n(n)")
        return float(-1.0)

#pdf的结构信息
def extract_pdf_structural_features(data):
    # TODO: 默认值是一个正常的 pdf
    alternate_result = {'Header': 1.3, 'Obj': 9.0, 'Endobj': 9.0, 'Stream': 3.0, 'Endstream': 3.0, 'Xref': 1.0, 'Trailer': 1.0, 'StartXref': 1.0, 'PageNo': 5.0, 'Encrypt': 0.0, 'ObjStm': 0.0, 'JS': 0.0,
                        'Javascript': 0.0, 'AA': 0.0, 'OpenAction': 0.0, 'Acroform': 0.0, 'JBIG2Decode': 0.0, 'RichMedia': 0.0, 'Launch': 0.0, 'EmbeddedFile': 0.0, 'XFA': 0.0, 'Uri': 0.0, 'Colors': 0.0}
    if data is None or data == '':
        return alternate_result

    data = data.strip()# 去除字符串开头和结尾的空白字符
    lines = data.split('\n')# 换行

    # 创建一个空字典来存储解析后的数据
    result = {}

    # 解析每一行数据并存储到字典中
    for line in lines[1:]:#从第二行开始遍历
        line = line.replace("/", "").replace("> 2^24 ", "")#清除这些字段

        if ':' in line:
            key, value = line.split(':', 1)
            value_str = value.split('-', 1)[1].strip()
            if "x0" in value:
                # 恶意文件存在乱码 \\x07.3 \\x07.4 \\x00.4
                value_str = value_str.replace("\\x0", "")
            try:
                value = float(value_str)
            except ValueError as e:
                errors.record_func_error(
                    "extract_pdf_structural_features", str(e) + f" in {value_str}")
                value = -1.0

            result['Header'] = value
        else:
            words = line.split()
            key = words[0].capitalize()
            # 不同版本的关键字形式有变化
            if key == 'Startxref':
                key = 'StartXref'
            elif key == 'Page':
                key = 'PageNo'
            elif key == 'Aa':
                key = 'AA'
            elif key == 'Objstm':
                key = 'ObjStm'
            elif key == 'Xfa':
                key = 'XFA'
            elif key == 'Embeddedfile':
                key = 'EmbeddedFile'
            elif key == 'Richmedia':
                key = 'RichMedia'
            elif key == 'Js':
                key = 'JS'
            elif key == 'Openaction':
                key = 'OpenAction'
            elif key == 'Jbig2decode':
                key = 'JBIG2Decode'

            if '(' in words[1] and ')' in words[1]:
                words[1] = calculate_string(words[1])
            try:
                if key == 'Not':
                    result['OpenAction'] = 10.0
                else:
                    result[key] = int(words[1]) * 1.0
            except ValueError as e:
                kv_str = "kay = " + key + " -> val = " + words[1]
                errors.record_func_error(
                    "extract_pdf_structural_features", f"{str(e)} in {kv_str}.")
                result[key] = float(-1.0)

    return result


def extract_pdf_features(pdf_path):

    #pdf特征信息
    dict_pre = extract_pdf_general_features(pdf_path)

    #logger.info('extract pdf general features done')
    # print(type(dict_pre))
    # import time
    # print(3,time.time())

    # 运行 pdfid.py 命令并获取输出结果 并解析为dic
    output = tools.extract_pdfid_output(pdf_path)
    # if not errors.check_return_value(output, "extract_pdf_output", True):
    if output == '':
        dict_pre['FileError'] += 1
        output = None  # 没有提取到pdfid信息，则返回None
    # print(4,time.time())
    #结构信息
    dict_nxt = extract_pdf_structural_features(output)
    #logger.info('extract pdf structural features done')
    # print(type(dict_nxt))

    #合并基础特征和结构特征
    merged_dict = {**dict_pre, **dict_nxt}

    # 检查关键字是否存在，不在就赋值-1确保完整性
    for key, _ in data_types.items():
        if key in merged_dict.keys():
            continue
        else:
            merged_dict[key] = float(-1.0)

    # 加入新的复合特征
    # MalTrail1 : Xref Trailer StartXref 均只出现一次 并且 还有JavaScript
    # MalTrail2 : Xref Trailer StartXref 均只出现一次 并且 还有嵌入文件
    # MalTrail3 : 嵌入文件 和 JavaScript 同时出现
    # DiffObj : Obj 和 Endobj 的个数差，有问题的PDF通常缺少关闭的关键字即Endobj
    # DiffStream : stream 和 Endstream 的个数差

    merged_dict['MalTrail1'] = 0.0
    merged_dict['MalTrail2'] = 0.0
    merged_dict['MalTrail3'] = 0.0
    merged_dict['DiffObj'] = 0.0
    merged_dict['DiffStream'] = 0.0

    try:
        if merged_dict['Xref'] == 1 and merged_dict['Trailer'] == 1 and merged_dict['StartXref'] == 1:
            if merged_dict['Javascript'] > 0:
                merged_dict['MalTrail1'] = 1.0
            if merged_dict['EmbeddedFiles'] > 0:
                merged_dict['MalTrail2'] = 1.0

        if merged_dict['Javascript'] > 0 and merged_dict['EmbeddedFiles'] > 0:
            merged_dict['MalTrail3'] = 1.0

        merged_dict['DiffObj'] = merged_dict['Obj'] - merged_dict['Endobj']
        merged_dict['DiffStream'] = merged_dict['Stream'] - \
            merged_dict['Endstream']
    except KeyError as e:
        errors.record_file_error(pdf_path, str(e))

    #  添加新的特征
    # ['Names'] 用于存储命名资源的索引，恶意文件会增多该字段
    # ['FontName'] 记录嵌入字体的个数
    # ['SensitiveCode'] 记录敏感JavaScript代码段的出现情况
    # ['KeywordObfuscation'] 是否存在 #6c等对形如关键字 FlateDecode 的混淆
    # TODO: ['ShellInject'] 记录shell注入出现的情况
    # ['ElseDecode'] 记录其他压缩字段的使用次数

    """
    get_pdf_containing_javascript这个函数做了什么
    1. 获取JavaScript代码
    2. 判断是否是图章文件
    3. 判断是否是嵌入文件
    4. 判断是否是敏感代码
    5. 判断是否是关键字混淆
    6. 判断是否是其他压缩字段的使用次数
    7. 判断是否是其他压缩字段的使用次数
    """
    
    code_str, has_keyword_obfuscation, filters = getJs.get_pdf_containing_javascript(
        pdf_path)  # 获取JavaScript代码
    #logger.info('extract pdf JS done.')
    # print(time.time())
    #如何判断它是图章文件的？
    # TODO: 处理PDF的图章文件
    if code_str == 'stamps':
        merged_dict['JS'] = 0.0
        merged_dict['Javascript'] = 0.0
        merged_dict['AA'] = 0.0
        merged_dict['Acroform'] = 0.0

    # 当js压缩流提取失败或为空时，考虑对文件嵌入流打开
    if merged_dict['EmbeddedFile'] > 0 and len(code_str) < 300:
        code_embed = getEmbedJs.get_embed_js(pdf_path)
        # print(code_embed) #
        if code_embed == 'Office':
            # TODO: 先用一个特殊值来表示PDF嵌入了Office文档
            merged_dict['EmbeddedFile'] = 10000  # 以下，该特征代码值禁止修改
        elif code_embed is not None:
            code_str += code_embed

    if code_str != "":  # tools.has_javascript_code(code_str):
        out_string = ""
    else:
        out_string = tools.extract_pdf_parser_output(pdf_path)

    merged_dict['Names'] = out_string.count('Names')
    merged_dict['FontName'] = out_string.count('FontName')

    merged_dict['JsBadStream'] = 0.0
    if code_str and 'incorrect header' in code_str:
        merged_dict['FileError'] += 1.0

    merged_dict['JsObfuscation'] = obfuse.identify_code_obfuscation(code_str)
    #logger.info('pdf JS code obfuscation done')
    if has_keyword_obfuscation > 0:
        merged_dict['KeywordObfuscation'] = 1.0
    else:
        merged_dict['KeywordObfuscation'] = 0.0
    merged_dict['SensitiveCode'] = check_javascript_code(code_str)
    other_algo_list = ['DCTDecode', 'RunLengthDecode',
                       'CCITTFaxDecode', 'JPXDecode']  # 图像压缩
    merged_dict['ElseDecode'] = tools.count_fiters_occurrences(
        filters, other_algo_list)

    # 添加新特征
    # ['MayBenign'] 当前典型特征全为0，且pages >1 ，用于过滤误判
    # ['MayMalicious'] 将当前典型特征按权重聚合

    merged_dict['MayMalicious'] = 0.0
    key_dict = {'JS': 0.75, 'OpenAction': 0.50,  'ObjStm': 0.25, "MalTrail1": 0.50, 'DiffObj': 0.12,
                'KeywordObfuscation': 0.50, 'SensitiveCode': 0.15, 'JsObfuscation': 0.81
                }
    # 区分度并不大 'JBIG2Decode' 'JsBadStream': 2, 'AA': 0.05，'Acroform': 8,
    for k, v in key_dict.items():
        if merged_dict[k] > 0.0:
            merged_dict['MayMalicious'] += merged_dict[k] * v

    # 对文档加密会导致解压缩是出现不正确的头部
    # if merged_dict['JsBadStream'] > 0.0 and merged_dict['JS'] > 0.0 and merged_dict['Encrypt'] > 0.0:
    #     merged_dict['MayMalicious'] += 0.50

    merged_dict['MayBenign'] = 1.0
    ben_keys = ['JS', 'OpenAction', 'MalTrail1', 'KeywordObfuscation', 'EmbeddedFiles',
                'SensitiveCode', 'JsObfuscation', 'FileError', 'DiffObj']
    for key in ben_keys:
        if merged_dict[key] > 0.0:
            merged_dict['MayBenign'] = 0.0
            break

    # 加入标签值，良性的文件名 以 Ben 开头
    filename = os.path.basename(pdf_path).split('.')[0]
    if 'Ben' in filename or 'g' in filename:
        merged_dict['Class'] = 'Benign'
    else:
        merged_dict['Class'] = 'Malicious'

    return dict(merged_dict)


def deal_one_pdf(pdf_file_path):
    # print(pdf_file_path)
    features = extract_pdf_features(pdf_file_path)
    # print(features.keys())
    df = pd.DataFrame(features, index=[0])
    # print(df.columns)

    # 将字典中的值转换为相应的数据类型
    for key, value in data_types.items():
        df[key] = df[key].astype(value)

    # logging.info('get one pdf df')

    return df


def extract_pdfs_features2data_frame(pdfs_dir, save_path, start=0, n=100000):
    pdf_feature_list = tools.process_pdf_files(
        pdfs_dir, deal_one_pdf, start, n)
    if len(pdf_feature_list) > 0:
        df = pd.concat(pdf_feature_list, ignore_index=True)

        # 存为 CSV 会失去category类型
        # df.to_csv(save_path, index=False)

        # 存为 parquet 能保存category类型
        df.to_parquet(save_path, index=False)
    else:
        errors.record_func_error(
            "extract_pdfs_features2data_frame", "pdf features is None.")


def extract_thousands_pdfs_features2data_frame(pdfs_dir, file_tag):
    start = 0
    dalta = 400  # 每dalta个保存一次
    total_amount = tools.count_files_in_folder(pdfs_dir)
    loop_num = int(total_amount/dalta) + 1

    for nth_loop in range(loop_num):
        # 处理第八组 第十六组的卡顿 这个之前的跳过 之后的退出
        # bad_id = 16
        # if nth_loop < bad_id:
        #     start += dalta
        #     continue
        # if nth_loop > bad_id:
        #     break

        save_path = "data_frame/" + f"{file_tag}_{nth_loop:03d}.parquet"
        pd_list = tools.process_pdf_files(pdfs_dir, deal_one_pdf, start, dalta)
        if len(pd_list) > 0:
            df = pd.concat(pd_list, ignore_index=True)
            df.to_parquet(save_path, index=False)
            print(f"loop {nth_loop} done.")

        start += dalta


def args_deal_pdfs():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(
        description="提取指定目录下的pdf文件特征并保存到parquet文件中")

    parser.add_argument("pdf_dir", type=str, help="PDF Folder path")
    parser.add_argument("save_path", type=str, help="Save parquet file path")
    parser.add_argument("start_num", type=int, help="Start nth file number")

    # 解析命令行参数
    args = parser.parse_args()

    # 调用函数，传入命令行参数
    extract_pdfs_features2data_frame(
        args.pdf_dir, args.save_path, args.start_num)


def test_func():
    # 处理PDF的文件夹
    # args_deal_pdfs()  # 命令行的方式

    # 测试单个的文件 0.5秒
    # r'.\test200\1084.pdf'   #r'./test200\104.pdf'
    # pdf_file_path = './anxin_data/bad\Mal_02489.pdf'  # r'./test200\1090.pdf'
    # mydf = deal_one_pdf(pdf_file_path)
    # print(mydf.iloc[0])

    # 测试200个文件
    # data_dir = r'.\test200'
    # save_path = r'.\data_frame\test200.parquet'
    # errors.clean_log_file()
    # extract_pdfs_features2data_frame(data_dir, save_path, 52, 1)

    # df = pd.read_parquet(save_path)
    # print(df['MayMalicious'])  # 'MayMalicious''MayBenign'
    # print(df.iloc[0])

    # 合并数据
    # tools.concat_parquet_file(r'.\data_frame\*.parquet')

    #  测试抽取工具返回为空的情况
    # pdf_path = './b905.pdf'
    # output = tools.extract_pdfid_output(pdf_path)
    # out = extract_pdf_structural_features(output)
    # print(out)

    # 抽取安芯的PDF
    # pdf_dir = "./anxin_data/good"
    # tag = 'anxin_good'
    # extract_thousands_pdfs_features2data_frame(pdf_dir,tag)

    # 抽取PDF2022
    pdf_dir = "pdf2022\Mal"
    tag = 'pdf22_mal'
    extract_thousands_pdfs_features2data_frame(pdf_dir, tag)


# if __name__ == "__main__":
#     tools.calculate_runtime(test_func)


# 临时提取PDF中的JS脚本

# 导入所需模块

# Unit 1: 定义遍历文件夹并查找PDF文件的函数

def list_pdf_files_in_directory(root_dir):
    """
    Unit 1: 列出指定目录及其子目录下的所有PDF文件路径
    """
    pdf_files = []
    for dirpath, _, filenames in os.walk(root_dir):
        pdf_files.extend(glob.glob(os.path.join(dirpath, '*.pdf')))
    return pdf_files

# Unit 2: 提取并保存PDF中的JavaScript代码


def extract_and_save_js_from_pdfs(pdf_files, output_dir):
    """
    Unit 2: 从PDF文件中提取JavaScript代码并保存到指定目录
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    for pdf_path in pdf_files:
        # 提取包含的JavaScript代码
        js_code, _, _ = getJs.get_pdf_containing_javascript(pdf_path)

        # 提取嵌入式JavaScript代码
        embed_js_code = getEmbedJs.get_embed_js(pdf_path)

        # 构建输出文件名
        base_name = os.path.splitext(os.path.basename(pdf_path))[0]
        output_file = os.path.join(output_dir, f"{base_name}_decoded.js")
        output_embed_file = os.path.join(
            output_dir, f"{base_name}_embedded.js")
        try:
            if js_code and len(js_code) > 50:
                # 保存提取的JavaScript代码
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(js_code)
            if embed_js_code and len(embed_js_code) > 50:
                # 保存嵌入式JavaScript代码
                with open(output_embed_file, 'w', encoding='utf-8') as ef:
                    ef.write(embed_js_code)
        except Exception as e:
            print(f"Error processing {pdf_path}: {e}")
            print(js_code+embed_js_code)


# 主流程
if __name__ == "__main__":
    # 指定根目录
    root_dir = r"C:\Users\<USER>\Documents\malware_data\pdf_analyze\ExtractPdfFeatures\anxin_data\bad"

    # 目标输出目录
    output_dir = r"D:\Js_analyze\JStap-master\data\pdf_js_2"

    # 获取PDF文件列表
    pdf_files = list_pdf_files_in_directory(root_dir)
    # print(len(pdf_files))
    # print(pdf_files[0:3])

    # 提取并保存JavaScript代码
    extract_and_save_js_from_pdfs(pdf_files, output_dir)
