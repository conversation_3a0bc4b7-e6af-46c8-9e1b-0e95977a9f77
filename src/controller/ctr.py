import os
import sys
import tempfile
import grpc
from api.pb.olaf import olaf_pb2, olaf_pb2_grpc
import filetype

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.engine.models.ai.ai_pdf.detector import check_one_pdf, get_model
from src.engine.models.ai.ai_office.detector import check_doc, get_ensemble_model
from src.engine.models.aipe.detector import check_pe
from src.utils.logging_utils import create_detection_context

class OlafServicer(olaf_pb2_grpc.OlafServicer):

    def FileDetect(self, request_iterator, context):
        filename, sha256 = "", ""
        fbuf = bytearray()
        tmp_file_path = ""
        detect_type = olaf_pb2.DetectType.DETECT_UNKNOWN

        # 创建检测上下文用于日志聚合
        detection_ctx = create_detection_context()

        try:
            # 接收数据流
            for request in request_iterator:
                if request.filename:
                    filename = request.filename
                if request.sha256:
                    sha256 = request.sha256
                if request.chunk_data:
                    fbuf.extend(request.chunk_data)
                if request.detect_type:
                    detect_type = request.detect_type
                if request.eof:
                    break

            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                tmp_file.write(fbuf)
                tmp_file_path = tmp_file.name

            # 设置文件信息到日志上下文
            detection_ctx.set_file_info(filename, sha256, tmp_file_path)
            detection_ctx.set_detect_type(olaf_pb2.DetectType.Name(detect_type))

            kind = filetype.guess(tmp_file_path)
            file_ext = kind.extension if kind else "unknown"
            detection_ctx.set_file_type(file_ext)

            resp = self._run_detection(detect_type, file_ext, tmp_file_path, detection_ctx)
            yield resp

        except Exception as e:
            # 记录错误日志
            detection_ctx.log_error_summary(str(e))
            raise

        finally:
            if tmp_file_path and os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)

    def _run_detection(self, detect_type, file_ext, path, detection_ctx):
        """
        根据检测类型和文件扩展名执行具体检测逻辑
        """
        if detect_type == olaf_pb2.DetectType.DETECT_AIPE:
            if file_ext == "exe":
                score = check_pe(path)
                return self._make_resp(score * 100, olaf_pb2.ModelType.MODEL_PE, detection_ctx)
            else:
                detection_ctx.add_custom_field("warning", "AIPE只支持EXE文件")
                return self._make_resp(0, olaf_pb2.ModelType.MODEL_UNKNOWN, detection_ctx)

        elif detect_type == olaf_pb2.DetectType.DETECT_AI:
            return self._detect_ai(file_ext, path, detection_ctx)

        else:
            # detect_type == DETECT_UNKNOWN，尝试自动判断
            return self._detect_ai(file_ext, path, detection_ctx)

    def _detect_ai(self, file_ext, path, detection_ctx):
        """
        针对 AI 模型的自动检测逻辑
        """
        if file_ext == "pdf":
            score = check_one_pdf(path, get_model())
            return self._make_resp(score * 10, olaf_pb2.ModelType.MODEL_PDF, detection_ctx)
        elif file_ext in ["doc", "docx"]:
            score = check_doc(path, get_ensemble_model())
            return self._make_resp(score * 10, olaf_pb2.ModelType.MODEL_OFFICE, detection_ctx)
        else:
            detection_ctx.add_custom_field("warning", f"不支持的AI文件类型: {file_ext}")
            return self._make_resp(0, olaf_pb2.ModelType.MODEL_UNKNOWN, detection_ctx)

    def _make_resp(self, score, model_type, detection_ctx):
        """
        构造统一响应体
        """
        score = min(max(int(score), 0), 100)

        # 设置检测结果到日志上下文
        detection_ctx.set_result(score, olaf_pb2.ModelType.Name(model_type))

        # 输出聚合日志
        detection_ctx.log_summary()

        return olaf_pb2.FileDetectResp(score=score, model_type=model_type)
