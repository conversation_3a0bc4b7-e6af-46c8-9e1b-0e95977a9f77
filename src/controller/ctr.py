import os
import sys
import tempfile
import grpc
from api.pb.olaf import olaf_pb2, olaf_pb2_grpc
import filetype

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.engine.models.ai.ai_pdf.detector import check_one_pdf, get_model
from src.engine.models.ai.ai_office.detector import check_doc, get_ensemble_model
from src.engine.models.aipe.detector import check_pe

class OlafServicer(olaf_pb2_grpc.OlafServicer):

    def FileDetect(self, request_iterator, context):
        filename, sha256 = "", ""
        fbuf = bytearray()
        tmp_file_path = ""
        detect_type = olaf_pb2.DetectType.DETECT_UNKNOWN

        try:
            # 接收数据流
            for request in request_iterator:
                if request.filename:
                    filename = request.filename
                if request.sha256:
                    sha256 = request.sha256
                if request.chunk_data:
                    fbuf.extend(request.chunk_data)
                if request.detect_type:
                    detect_type = request.detect_type
                if request.eof:
                    break

            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                tmp_file.write(fbuf)
                tmp_file_path = tmp_file.name

            print(f"✅ Received file: {filename}")
            print(f"✅ SHA256: {sha256}")
            print(f"✅ Temp file path: {tmp_file_path}")
            print(f"✅ Detect type: {olaf_pb2.DetectType.Name(detect_type)}")

            kind = filetype.guess(tmp_file_path)
            file_ext = kind.extension if kind else "unknown"
            print(f"✅ Detected file type: {file_ext}")

            resp = self._run_detection(detect_type, file_ext, tmp_file_path)
            yield resp

        except Exception as e:
            print(f"❌ Exception in FileDetect: {e}")
            raise

        finally:
            if tmp_file_path and os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)

    def _run_detection(self, detect_type, file_ext, path):
        """
        根据检测类型和文件扩展名执行具体检测逻辑
        """
        if detect_type == olaf_pb2.DetectType.DETECT_AIPE:
            if file_ext == "exe":
                score = check_pe(path)
                return self._make_resp(score * 100, olaf_pb2.ModelType.MODEL_PE)
            else:
                print("⚠️ AIPE 只支持 EXE 文件")
                return self._make_resp(0, olaf_pb2.ModelType.MODEL_UNKNOWN)

        elif detect_type == olaf_pb2.DetectType.DETECT_AI:
            return self._detect_ai(file_ext, path)

        else:
            # detect_type == DETECT_UNKNOWN，尝试自动判断
            return self._detect_ai(file_ext, path)

    def _detect_ai(self, file_ext, path):
        """
        针对 AI 模型的自动检测逻辑
        """
        if file_ext == "pdf":
            score = check_one_pdf(path, get_model())
            return self._make_resp(score * 10, olaf_pb2.ModelType.MODEL_PDF)
        elif file_ext in ["doc", "docx"]:
            score = check_doc(path, get_ensemble_model())
            return self._make_resp(score * 10, olaf_pb2.ModelType.MODEL_OFFICE)
        else:
            print(f"⚠️ 不支持的 AI 文件类型: {file_ext}")
            return self._make_resp(0, olaf_pb2.ModelType.MODEL_UNKNOWN)

    def _make_resp(self, score, model_type):
        """
        构造统一响应体
        """
        score = min(max(int(score), 0), 100)
        print(f"✅ 检测分数: {score}, 模型类型: {olaf_pb2.ModelType.Name(model_type)}")
        return olaf_pb2.FileDetectResp(score=score, model_type=model_type)
