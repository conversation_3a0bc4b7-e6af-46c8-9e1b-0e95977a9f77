# Olaf

```bash
# 添加子模块
git submodule add http://git.anxin.com/v01-cluster/vapi.git ./api/proto

# 更新子模块
git submodule update --init

# 删除子模块
git submodule deinit -f ./api/proto

# 安装依赖
python -m pip install -r requirements.txt

# 运行
export PYTHONPATH=./api/pb:$PYTHONPATH
export PYTHONPYCACHEPREFIX=/root/anxinsec/olaf/.pycache
python main.py

# 清理
find . -name "__pycache__" -type d -exec rm -rf {} +

# 镜像构建
docker build -t registry.harbor.cn/base/ai-base-run:latest .
docker push registry.harbor.cn/base/ai-base-run:latest

# 忽略pyc
# 把 .pyc 文件从 Git 追踪中移除
git rm --cached -r '*.pyc'
git rm --cached -r __pycache__/
# 添加 .gitignore 文件并提交
echo -e "*.pyc\n__pycache__/" >> .gitignore
git add .gitignore
git commit -m "chore: ignore pyc files"
```
