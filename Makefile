.DEFAULT_GOAL := all

.PHONY: all
all: build pack

# ==============================================================================
# Includes
include scripts/make-rules/common.mk # make sure include common.mk at the first include line
include scripts/make-rules/pack.mk
include scripts/make-rules/proto.mk

# ==============================================================================
# Targets

.PHONY: proto
proto:
	@$(MAKE) proto.build

.PHONY: image
image:
	docker build -t registry.harbor.cn/v01/olaf:latest .
	docker push registry.harbor.cn/v01/olaf:latest